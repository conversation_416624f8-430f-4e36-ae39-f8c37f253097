/**
 * 构建执行计划对象
 *
 * @param apply   申请单对象
 * @param charges 费用列表
 * @return 执行计划对象
 */
private CisOrderExecPlanNto buildOrderExecPlanNto(CisBaseApply apply, List<CisOrderExecPlanChargeNto> charges) {
    // 参数校验
    BusinessAssert.notNull(apply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单对象不能为空");
    BusinessAssert.notEmpty(charges, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "费用列表不能为空");

    CisOrderExecPlanNto nto = new CisOrderExecPlanNto();

    // 设置申请单基本信息
    nto.setOrderClass(apply.getSystemType());
    nto.setVisitCode(apply.getVisitCode());
    nto.setPatMiCode(apply.getPatMiCode());
    nto.setOrgCode(apply.getVisitOrgCode());
    nto.setOrgName(apply.getVisitOrgName());
    nto.setDeptNurseCode(apply.getDeptNurseCode());
    nto.setOrderId(apply.getOrderID());
    nto.setSortNo(apply.getSortNo());
    nto.setServiceItemCode(apply.getServiceItemCode());
    nto.setServiceItemName(apply.getServiceItemName());
    nto.setNum(apply.getNum());

    // 设置执行机构信息
    CisOrderExecPlanChargeNto firstCharge = charges.get(0);
    if (firstCharge != null) {
        nto.setExecOrgCode(firstCharge.getExecuteOrgCode());
        nto.setExecOrgName(firstCharge.getExecuteOrgName());
    }

    // 设置其他信息
    nto.setOrgName(apply.getVisitOrgName());
    nto.setHeldStaff(apply.getCreatedStaff());
    nto.setHeldStaffName(apply.getCreatedStaffName());

    return nto;
}
