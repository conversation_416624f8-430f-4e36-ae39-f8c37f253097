<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
        <groupId>com.bjgoodwill.hip</groupId>
        <artifactId>hip-parent</artifactId>
        <version>5.0-SNAPSHOT</version>
    </parent>

    <artifactId>hip-cis-adv-api</artifactId>
	<version>5.0-SNAPSHOT</version>
	<name>hip-cis-adv-api</name>

    <properties>
        <hip-business-util.version>5.0-SNAPSHOT</hip-business-util.version>
    </properties>

	<dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-business-util</artifactId>
            <version>${hip-business-util.version}</version>
            <scope>compile</scope>
        </dependency>
	</dependencies>

</project>
