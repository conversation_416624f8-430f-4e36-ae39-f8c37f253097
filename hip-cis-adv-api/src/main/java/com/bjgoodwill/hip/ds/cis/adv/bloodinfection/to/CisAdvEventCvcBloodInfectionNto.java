package com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.time.LocalDateTime;

@Schema(description = "CVC相关血流感染相关信息收集表")
public class CisAdvEventCvcBloodInfectionNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -961468133328649934L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "年龄范围: 新生儿、1-6月、7-12月、1-6岁、7-12岁、13-18岁、19-64岁、65岁及以上、无法确定")
    private String ageRange;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区科室名称")
    private String areaName;
    @Schema(description = "入院时间")
    private LocalDateTime inDate;
    @Schema(description = "留置导管的主要原因：输入高渗液体hypertonicfluid，输入化疗药物chemotherapydrugs，长期输液longterminfusion，抢救和监测需要rescueandmnitorin，其他other")
    private String indwellReason;
    @Schema(description = "留置导管的主要原因：输入高渗液体hypertonicfluid，输入化疗药物chemotherapydrugs，长期输液longterminfusion，抢救和监测需要rescueandmnitorin，其他other")
    private String indwellReasonName;
    @Schema(description = "cvc置管位置：锁骨下静脉subclavian，颈内静脉internaljugular，股静脉femoral")
    private String cvcLocation;
    @Schema(description = "cvc置管位置：锁骨下静脉subclavian，颈内静脉internaljugular，股静脉femoral")
    private String cvcLocationName;
    @Schema(description = "导管类型：单腔导管endotrachealintubation；双腔导管doublelumen；三腔导管threelumen")
    private String extubationType;
    @Schema(description = "导管类型名称：单腔导管endotrachealintubation；双腔导管doublelumen；三腔导管threelumen")
    private String extubationTypeName;
    @Schema(description = "是否为抗菌导管：1是，0否")
    private boolean antibacterialFlag;
    @Schema(description = "发生clabsi时cvc留置时长：天")
    private Integer cvcTime;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @Size(max = 50, message = "不良事件id长度不能超过50个字符！")
    public String getEventReportId() {
        return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = StringUtils.trimToNull(eventReportId);
    }

    @Size(max = 2, message = "患者类型长度不能超过2个字符！")
    public String getPatType() {
        return patType;
    }

    public void setPatType(String patType) {
        this.patType = StringUtils.trimToNull(patType);
    }

    @Size(max = 16, message = "住院号(门诊就诊卡号)长度不能超过16个字符！")
    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = StringUtils.trimToNull(inpatientCode);
    }

    @Size(max = 16, message = "就诊流水号长度不能超过16个字符！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @Size(max = 64, message = "患者姓名长度不能超过64个字符！")
    public String getPatName() {
        return patName;
    }

    public void setPatName(String patName) {
        this.patName = StringUtils.trimToNull(patName);
    }

    @Size(max = 16, message = "性别长度不能超过16个字符！")
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = StringUtils.trimToNull(sex);
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    @Size(max = 64, message = "年龄范围: 新生儿、1-6月、7-12月、1-6岁、7-12岁、13-18岁、19-64岁、65岁及以上、无法确定长度不能超过64个字符！")
    public String getAgeRange() {
        return ageRange;
    }

    public void setAgeRange(String ageRange) {
        this.ageRange = StringUtils.trimToNull(ageRange);
    }

    @Size(max = 16, message = "病区科室长度不能超过16个字符！")
    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = StringUtils.trimToNull(areaCode);
    }

    @Size(max = 64, message = "病区科室名称长度不能超过64个字符！")
    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = StringUtils.trimToNull(areaName);
    }

    public LocalDateTime getInDate() {
        return inDate;
    }

    public void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    public String getIndwellReason() {
        return indwellReason;
    }

    public void setIndwellReason(String indwellReason) {
        this.indwellReason = StringUtils.trimToNull(indwellReason);
    }

    public String getIndwellReasonName() {
        return indwellReasonName;
    }

    public void setIndwellReasonName(String indwellReasonName) {
        this.indwellReasonName = StringUtils.trimToNull(indwellReasonName);
    }

    @Size(max = 64, message = "cvc置管位置：锁骨下静脉subclavian，颈内静脉internaljugular，股静脉femoral长度不能超过64个字符！")
    public String getCvcLocation() {
        return cvcLocation;
    }

    public void setCvcLocation(String cvcLocation) {
        this.cvcLocation = StringUtils.trimToNull(cvcLocation);
    }

    public String getCvcLocationName() {
        return cvcLocationName;
    }

    public void setCvcLocationName(String cvcLocationName) {
        this.cvcLocationName = StringUtils.trimToNull(cvcLocationName);
    }

    public String getExtubationType() {
        return extubationType;
    }

    public void setExtubationType(String extubationType) {
        this.extubationType = StringUtils.trimToNull(extubationType);
    }

    public String getExtubationTypeName() {
        return extubationTypeName;
    }

    public void setExtubationTypeName(String extubationTypeName) {
        this.extubationTypeName = StringUtils.trimToNull(extubationTypeName);
    }

    public boolean isAntibacterialFlag() {
        return antibacterialFlag;
    }

    public void setAntibacterialFlag(boolean antibacterialFlag) {
        this.antibacterialFlag = antibacterialFlag;
    }

    public Integer getCvcTime() {
        return cvcTime;
    }

    public void setCvcTime(Integer cvcTime) {
        this.cvcTime = cvcTime;
    }
}