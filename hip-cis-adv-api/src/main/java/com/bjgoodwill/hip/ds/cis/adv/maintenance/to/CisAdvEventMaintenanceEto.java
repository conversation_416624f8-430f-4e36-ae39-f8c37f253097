package com.bjgoodwill.hip.ds.cis.adv.maintenance.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "不良事件维护")
public class CisAdvEventMaintenanceEto  extends BaseEto implements Serializable {

	@Serial
    private static final long serialVersionUID = -6418962678303505999L;

    @Schema(description = "上级目录编码")
    private String parentCode;
    @Schema(description = "不良事件编码")
    private String eventCode;
    @Schema(description = "不良事件名称")
    private String eventName;
    @Schema(description = "不良事件名称")
    private Integer eventLevel;
    @Schema(description = "已启用")
    private boolean enabled;

    @Size(max = 16, message = "上级目录编码长度不能超过16个字符！")
    public String getParentCode() {
    	return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = StringUtils.trimToNull(parentCode);
    }

    @Size(max = 16, message = "不良事件编码长度不能超过16个字符！")
    public String getEventCode() {
    	return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = StringUtils.trimToNull(eventCode);
    }

    @Size(max = 128, message = "不良事件名称长度不能超过128个字符！")
    public String getEventName() {
    	return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = StringUtils.trimToNull(eventName);
    }

    public Integer getEventLevel() {
    	return eventLevel;
    }

    public void setEventLevel(Integer eventLevel) {
        this.eventLevel = eventLevel;
    }

    public boolean isEnabled() {
    	return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}