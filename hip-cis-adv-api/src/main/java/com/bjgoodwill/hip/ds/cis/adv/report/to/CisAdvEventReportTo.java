package com.bjgoodwill.hip.ds.cis.adv.report.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.AdvEventsStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "不良事件报告")
public class CisAdvEventReportTo implements Serializable {

	@Serial
    private static final long serialVersionUID = -6809655941005593529L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "患者职业")
    private String work;
    @Schema(description = "患者所在科室(门诊挂号科室)")
    private String areaCode;
    @Schema(description = "患者所在科室名称(门诊挂号科室)")
    private String areaName;
    @Schema(description = "床号")
    private String bedName;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "临床诊断")
    private String clinicalDiagnosis;
    @Schema(description = "事件发生场所")
    private String eventPlace;
    @Schema(description = "其它事件发生地")
    private String otherEventPlace;
    @Schema(description = "不良后果标识")
    private String advConsequencesFlag;
    @Schema(description = "不良后果内容")
    private String advConsequences;
    @Schema(description = "事件经过")
    private String eventAfter;
    @Schema(description = "不良事件类别")
    private String eventType;
    @Schema(description = "附卡")
    private String eventCard;
    @Schema(description = "附卡标识")
    private boolean additionFlag;
    @Schema(description = "事件处理情况")
    private String eventHandle;
    @Schema(description = "不良事件等级")
    private String eventLevel;
    @Schema(description = "不良事件等级名称")
    private String eventLevelName;
    @Schema(description = "导致事件的可能原因")
    private String eventWhy;
    @Schema(description = "不良事件评价人")
    private String evaluationUser;
    @Schema(description = "不良事件评价时间")
    private LocalDateTime evaluationDate;
    @Schema(description = "持续改进措施")
    private String improvementMeasures;
    @Schema(description = "持续改进措施填写人")
    private String improvementUser;
    @Schema(description = "持续改进措施填写人名称")
    private String improvementUserName;
    @Schema(description = "持续改进措施填写时间")
    private LocalDateTime improvementDate;
    @Schema(description = "转发接收科室")
    private String receiveOrgCode;
    @Schema(description = "转发接收科室名称")
    private String receiveOrgName;
    @Schema(description = "主管部门意见陈述")
    private String opinionState;
    @Schema(description = "主管部门意见陈述人")
    private String opinionStateUser;
    @Schema(description = "主管部门意见陈述人名称")
    private String opinionStateUserName;
    @Schema(description = "主管部门意见陈述时间")
    private LocalDateTime opinionStateDate;
    @Schema(description = "报告人类别")
    private String reportUserType;
    @Schema(description = "报告人类别名称")
    private String reportUserTypeName;
    @Schema(description = "当事人的类别")
    private String litigantType;
    @Schema(description = "职称")
    private String title;
    @Schema(description = "职称名称")
    private String titleName;
    @Schema(description = "责任者姓名")
    private String dutyUser;
    @Schema(description = "责任者职称")
    private String dutyTitle;
    @Schema(description = "责任者职称名称")
    private String dutyTitleName;
    @Schema(description = "责任者工作年限")
    private String workingLife;
    @Schema(description = "责任者工作年限名称")
    private String workingLifeName;
    @Schema(description = "护士级别")
    private String dutyNurseLevel;
    @Schema(description = "责任者姓名")
    private String dutyUser2;
    @Schema(description = "责任者职称")
    private String dutyTitle2;
    @Schema(description = "责任者职称名称")
    private String dutyTitle2Name;
    @Schema(description = "责任者工作年限")
    private String workingLife2;
    @Schema(description = "责任者工作年限名称")
    private String workingLife2Name;
    @Schema(description = "护士级别")
    private String dutyNurseLevel2;
    @Schema(description = "报告人")
    private String reportUser;
    @Schema(description = "报告科室")
    private String reportOrgCode;
    @Schema(description = "报告人联系电话")
    private String reportTel;
    @Schema(description = "报告时间")
    private LocalDateTime reportDate;
    @Schema(description = "打回人")
    private String backUser;
    @Schema(description = "打回原因")
    private String backRemarks;
    @Schema(description = "打回时间")
    private LocalDateTime backDate;
    @Schema(description = "发回人")
    private String cancelUser;
    @Schema(description = "发回原因")
    private String cancelRemarks;
    @Schema(description = "发回时间")
    private LocalDateTime cancelDate;
    @Schema(description = "医院编码")
    private String hospitalCode;
    @Schema(description = "状态")
    private AdvEventsStatusEnum statusCode;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;

    public String getId() {
    	return id;
    }

    public void setId(String id) {
    	this.id = id;
    }

    public String getPatMiCode() {
    	return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
    	this.patMiCode = patMiCode;
    }

    public String getPatType() {
    	return patType;
    }

    public void setPatType(String patType) {
    	this.patType = patType;
    }

    public String getInpatientCode() {
    	return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
    	this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    public void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public String getPatName() {
    	return patName;
    }

    public void setPatName(String patName) {
    	this.patName = patName;
    }

    public String getSex() {
    	return sex;
    }

    public void setSex(String sex) {
    	this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
    	this.birthDate = birthDate;
    }

    public String getWork() {
    	return work;
    }

    public void setWork(String work) {
    	this.work = work;
    }

    public String getAreaCode() {
    	return areaCode;
    }

    public void setAreaCode(String areaCode) {
    	this.areaCode = areaCode;
    }

    public String getBedName() {
    	return bedName;
    }

    public void setBedName(String bedName) {
    	this.bedName = bedName;
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
    	this.eventDate = eventDate;
    }

    public String getClinicalDiagnosis() {
    	return clinicalDiagnosis;
    }

    public void setClinicalDiagnosis(String clinicalDiagnosis) {
    	this.clinicalDiagnosis = clinicalDiagnosis;
    }

    public String getEventPlace() {
    	return eventPlace;
    }

    public void setEventPlace(String eventPlace) {
    	this.eventPlace = eventPlace;
    }

    public String getOtherEventPlace() {
    	return otherEventPlace;
    }

    public void setOtherEventPlace(String otherEventPlace) {
    	this.otherEventPlace = otherEventPlace;
    }

    public String getAdvConsequencesFlag() {
    	return advConsequencesFlag;
    }

    public void setAdvConsequencesFlag(String advConsequencesFlag) {
    	this.advConsequencesFlag = advConsequencesFlag;
    }

    public String getAdvConsequences() {
    	return advConsequences;
    }

    public void setAdvConsequences(String advConsequences) {
    	this.advConsequences = advConsequences;
    }

    public String getEventAfter() {
    	return eventAfter;
    }

    public void setEventAfter(String eventAfter) {
    	this.eventAfter = eventAfter;
    }

    public String getEventType() {
    	return eventType;
    }

    public void setEventType(String eventType) {
    	this.eventType = eventType;
    }

    public String getEventCard() {
    	return eventCard;
    }

    public void setEventCard(String eventCard) {
    	this.eventCard = eventCard;
    }

    public boolean isAdditionFlag() {
    	return additionFlag;
    }

    public void setAdditionFlag(boolean additionFlag) {
    	this.additionFlag = additionFlag;
    }

    public String getEventHandle() {
    	return eventHandle;
    }

    public void setEventHandle(String eventHandle) {
    	this.eventHandle = eventHandle;
    }

    public String getEventLevel() {
    	return eventLevel;
    }

    public void setEventLevel(String eventLevel) {
    	this.eventLevel = eventLevel;
    }

    public String getEventWhy() {
    	return eventWhy;
    }

    public void setEventWhy(String eventWhy) {
    	this.eventWhy = eventWhy;
    }

    public String getEvaluationUser() {
    	return evaluationUser;
    }

    public void setEvaluationUser(String evaluationUser) {
    	this.evaluationUser = evaluationUser;
    }

    public LocalDateTime getEvaluationDate() {
    	return evaluationDate;
    }

    public void setEvaluationDate(LocalDateTime evaluationDate) {
    	this.evaluationDate = evaluationDate;
    }

    public String getImprovementMeasures() {
    	return improvementMeasures;
    }

    public void setImprovementMeasures(String improvementMeasures) {
    	this.improvementMeasures = improvementMeasures;
    }

    public String getImprovementUser() {
    	return improvementUser;
    }

    public void setImprovementUser(String improvementUser) {
    	this.improvementUser = improvementUser;
    }

    public LocalDateTime getImprovementDate() {
    	return improvementDate;
    }

    public void setImprovementDate(LocalDateTime improvementDate) {
    	this.improvementDate = improvementDate;
    }

    public String getReceiveOrgCode() {
    	return receiveOrgCode;
    }

    public void setReceiveOrgCode(String receiveOrgCode) {
    	this.receiveOrgCode = receiveOrgCode;
    }

    public String getOpinionState() {
    	return opinionState;
    }

    public void setOpinionState(String opinionState) {
    	this.opinionState = opinionState;
    }

    public String getOpinionStateUser() {
    	return opinionStateUser;
    }

    public void setOpinionStateUser(String opinionStateUser) {
    	this.opinionStateUser = opinionStateUser;
    }

    public LocalDateTime getOpinionStateDate() {
    	return opinionStateDate;
    }

    public void setOpinionStateDate(LocalDateTime opinionStateDate) {
    	this.opinionStateDate = opinionStateDate;
    }

    public String getReportUserType() {
    	return reportUserType;
    }

    public void setReportUserType(String reportUserType) {
    	this.reportUserType = reportUserType;
    }

    public String getLitigantType() {
    	return litigantType;
    }

    public void setLitigantType(String litigantType) {
    	this.litigantType = litigantType;
    }

    public String getTitle() {
    	return title;
    }

    public void setTitle(String title) {
    	this.title = title;
    }

    public String getDutyUser() {
    	return dutyUser;
    }

    public void setDutyUser(String dutyUser) {
    	this.dutyUser = dutyUser;
    }

    public String getDutyTitle() {
    	return dutyTitle;
    }

    public void setDutyTitle(String dutyTitle) {
    	this.dutyTitle = dutyTitle;
    }

    public String getWorkingLife() {
    	return workingLife;
    }

    public void setWorkingLife(String workingLife) {
    	this.workingLife = workingLife;
    }

    public String getDutyNurseLevel() {
    	return dutyNurseLevel;
    }

    public void setDutyNurseLevel(String dutyNurseLevel) {
    	this.dutyNurseLevel = dutyNurseLevel;
    }

    public String getDutyUser2() {
    	return dutyUser2;
    }

    public void setDutyUser2(String dutyUser2) {
    	this.dutyUser2 = dutyUser2;
    }

    public String getDutyTitle2() {
    	return dutyTitle2;
    }

    public void setDutyTitle2(String dutyTitle2) {
    	this.dutyTitle2 = dutyTitle2;
    }

    public String getWorkingLife2() {
    	return workingLife2;
    }

    public void setWorkingLife2(String workingLife2) {
    	this.workingLife2 = workingLife2;
    }

    public String getDutyNurseLevel2() {
    	return dutyNurseLevel2;
    }

    public void setDutyNurseLevel2(String dutyNurseLevel2) {
    	this.dutyNurseLevel2 = dutyNurseLevel2;
    }

    public String getReportUser() {
    	return reportUser;
    }

    public void setReportUser(String reportUser) {
    	this.reportUser = reportUser;
    }

    public String getReportOrgCode() {
    	return reportOrgCode;
    }

    public void setReportOrgCode(String reportOrgCode) {
    	this.reportOrgCode = reportOrgCode;
    }

    public String getReportTel() {
    	return reportTel;
    }

    public void setReportTel(String reportTel) {
    	this.reportTel = reportTel;
    }

    public LocalDateTime getReportDate() {
    	return reportDate;
    }

    public void setReportDate(LocalDateTime reportDate) {
    	this.reportDate = reportDate;
    }

    public String getBackUser() {
    	return backUser;
    }

    public void setBackUser(String backUser) {
    	this.backUser = backUser;
    }

    public String getBackRemarks() {
    	return backRemarks;
    }

    public void setBackRemarks(String backRemarks) {
    	this.backRemarks = backRemarks;
    }

    public LocalDateTime getBackDate() {
    	return backDate;
    }

    public void setBackDate(LocalDateTime backDate) {
    	this.backDate = backDate;
    }

    public String getCancelUser() {
    	return cancelUser;
    }

    public void setCancelUser(String cancelUser) {
    	this.cancelUser = cancelUser;
    }

    public String getCancelRemarks() {
    	return cancelRemarks;
    }

    public void setCancelRemarks(String cancelRemarks) {
    	this.cancelRemarks = cancelRemarks;
    }

    public LocalDateTime getCancelDate() {
    	return cancelDate;
    }

    public void setCancelDate(LocalDateTime cancelDate) {
    	this.cancelDate = cancelDate;
    }

    public String getHospitalCode() {
    	return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
    	this.hospitalCode = hospitalCode;
    }

    public AdvEventsStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(AdvEventsStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getEventLevelName() {
        return eventLevelName;
    }

    public void setEventLevelName(String eventLevelName) {
        this.eventLevelName = eventLevelName;
    }

    public String getImprovementUserName() {
        return improvementUserName;
    }

    public void setImprovementUserName(String improvementUserName) {
        this.improvementUserName = improvementUserName;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public String getOpinionStateUserName() {
        return opinionStateUserName;
    }

    public void setOpinionStateUserName(String opinionStateUserName) {
        this.opinionStateUserName = opinionStateUserName;
    }

    public String getTitleName() {
        return titleName;
    }

    public void setTitleName(String titleName) {
        this.titleName = titleName;
    }

    public String getDutyTitleName() {
        return dutyTitleName;
    }

    public void setDutyTitleName(String dutyTitleName) {
        this.dutyTitleName = dutyTitleName;
    }

    public String getWorkingLifeName() {
        return workingLifeName;
    }

    public void setWorkingLifeName(String workingLifeName) {
        this.workingLifeName = workingLifeName;
    }

    public String getDutyTitle2Name() {
        return dutyTitle2Name;
    }

    public void setDutyTitle2Name(String dutyTitle2Name) {
        this.dutyTitle2Name = dutyTitle2Name;
    }

    public String getWorkingLife2Name() {
        return workingLife2Name;
    }

    public void setWorkingLife2Name(String workingLife2Name) {
        this.workingLife2Name = workingLife2Name;
    }

    public String getReportUserTypeName() {
        return reportUserTypeName;
    }

    public void setReportUserTypeName(String reportUserTypeName) {
        this.reportUserTypeName = reportUserTypeName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventReportTo other = (CisAdvEventReportTo) obj;
		return Objects.equals(id, other.id);
	}
}