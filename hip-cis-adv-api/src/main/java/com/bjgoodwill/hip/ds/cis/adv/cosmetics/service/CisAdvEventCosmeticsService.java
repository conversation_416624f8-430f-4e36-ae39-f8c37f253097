package com.bjgoodwill.hip.ds.cis.adv.cosmetics.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.CisAdvEventCosmeticsEto;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.CisAdvEventCosmeticsNto;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.CisAdvEventCosmeticsQto;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.CisAdvEventCosmeticsTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "化妆品不良反应事件报告表领域服务", description = "化妆品不良反应事件报告表领域服务")
public interface CisAdvEventCosmeticsService {

    @Operation(summary = "根据查询条件对化妆品不良反应事件报告表进行查询。")
    @GetMapping("/cisAdvEventCosmeticses")
    List<CisAdvEventCosmeticsTo> getCisAdvEventCosmeticses(@ParameterObject @SpringQueryMap CisAdvEventCosmeticsQto cisAdvEventCosmeticsQto);

    @Operation(summary = "根据查询条件对化妆品不良反应事件报告表进行分页查询。")
    @GetMapping("/cisAdvEventCosmeticses/pages")
    GridResultSet<CisAdvEventCosmeticsTo> getCisAdvEventCosmeticsPage(@ParameterObject @SpringQueryMap CisAdvEventCosmeticsQto cisAdvEventCosmeticsQto);

    @Operation(summary = "根据唯一标识返回化妆品不良反应事件报告表。")
    @GetMapping("/cisAdvEventCosmeticses/{id:.+}")
    CisAdvEventCosmeticsTo getCisAdvEventCosmeticsById(@PathVariable("id") String id);

    @Operation(summary = "创建化妆品不良反应事件报告表。")
    @PostMapping("/cisAdvEventCosmeticses")
    CisAdvEventCosmeticsTo createCisAdvEventCosmetics(@RequestBody @Valid CisAdvEventCosmeticsNto cisAdvEventCosmeticsNto);

    @Operation(summary = "根据唯一标识修改化妆品不良反应事件报告表。")
    @PutMapping("/cisAdvEventCosmeticses/{id:.+}")
    void updateCisAdvEventCosmetics(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventCosmeticsEto cisAdvEventCosmeticsEto);

    @Operation(summary = "根据唯一标识删除化妆品不良反应事件报告表。")
    @DeleteMapping("/cisAdvEventCosmeticses/{id:.+}")
    void deleteCisAdvEventCosmetics(@PathVariable("id") String id);

}