package com.bjgoodwill.hip.ds.cis.adv.vap.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "呼吸机相关肺炎（VAP）相关信息收集")
public class CisAdvEventVapEto extends BaseEto implements Serializable {

	@Serial
    private static final long serialVersionUID = -8969404951951576051L;

    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "年龄范围: 新生儿、1-6月、7-12月、1-6岁、7-12岁、13-18岁、19-64岁、65岁及以上、无法确定")
    private String ageRange;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区名称")
    private String areaName;
    @Schema(description = "入院时间")
    private LocalDateTime inDate;
    @Schema(description = "人工气道类型：气管插管endotrachealintubation、气管切开tracheotomy")
    private String artificialType;
    @Schema(description = "人工气道类型名称：气管插管endotrachealintubation、气管切开tracheotomy")
    private String artificialTypeName;
    @Schema(description = "导管类型：普通general、声门下吸引型导subglottic")
    private String extubationType;
    @Schema(description = "导管类型名称：普通general、声门下吸引型导subglottic")
    private String extubationTypeName;
    @Schema(description = "湿化装置：呼吸机加温加湿ventilator、人工鼻湿化artificial、生理盐水滴注salineinfusion、其他other")
    private String humidifyDevice;
    @Schema(description = "湿化装置名称：呼吸机加温加湿ventilator、人工鼻湿化artificial、生理盐水滴注salineinfusion、其他other")
    private String humidifyDeviceName;
    @Schema(description = "吸痰方式：密闭式吸痰closed、开放式吸痰open")
    private String sputumType;
    @Schema(description = "吸痰方式名称：密闭式吸痰closed、开放式吸痰open")
    private String sputumTypeName;
    @Schema(description = "口腔护理方式：擦拭wipe、擦拭+冲洗wipeandrinse 、刷牙brushteeth")
    private String oralCareType;
    @Schema(description = "口腔护理方式名称：擦拭wipe、擦拭+冲洗wipeandrinse 、刷牙brushteeth")
    private String oralCareTypeName;
    @Schema(description = "每天口腔护理次数：次")
    private Integer oralCareTimes;
    @Schema(description = "口腔护理液选择：生理盐水normalsaline、含洗必泰includingchlorhexidine、腔护理液cavitynursing、牙膏toothpaste、其他other")
    private String oralNursing;
    @Schema(description = "口腔护理液选择名称：生理盐水normalsaline、含洗必泰includingchlorhexidine、腔护理液cavitynursing、牙膏toothpaste、其他other")
    private String oralNursingName;
    @Schema(description = "其他选择")
    private String otherNursing;
    @Schema(description = "经人工气道通气的同时，是否有经鼻胃管肠内营养： 0否、1是")
    private boolean nasogasticFlag;
    @Schema(description = "发生vap时，经人工气道机械通气时长:天")
    private Integer vapTime;

    @Size(max = 50, message = "不良事件id长度不能超过50个字符！")
    public String getEventReportId() {
    	return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = StringUtils.trimToNull(eventReportId);
    }

    @Size(max = 2, message = "患者类型长度不能超过2个字符！")
    public String getPatType() {
    	return patType;
    }

    public void setPatType(String patType) {
        this.patType = StringUtils.trimToNull(patType);
    }

    @Size(max = 16, message = "住院号(门诊就诊卡号)长度不能超过16个字符！")
    public String getInpatientCode() {
    	return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = StringUtils.trimToNull(inpatientCode);
    }

    @Size(max = 16, message = "就诊流水号长度不能超过16个字符！")
    public String getVisitCode() {
    	return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @Size(max = 64, message = "患者姓名长度不能超过64个字符！")
    public String getPatName() {
    	return patName;
    }

    public void setPatName(String patName) {
        this.patName = StringUtils.trimToNull(patName);
    }

    @Size(max = 16, message = "性别长度不能超过16个字符！")
    public String getSex() {
    	return sex;
    }

    public void setSex(String sex) {
        this.sex = StringUtils.trimToNull(sex);
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    @Size(max = 64, message = "年龄范围: 新生儿、1-6月、7-12月、1-6岁、7-12岁、13-18岁、19-64岁、65岁及以上、无法确定长度不能超过64个字符！")
    public String getAgeRange() {
    	return ageRange;
    }

    public void setAgeRange(String ageRange) {
        this.ageRange = StringUtils.trimToNull(ageRange);
    }

    @Size(max = 16, message = "病区科室长度不能超过16个字符！")
    public String getAreaCode() {
    	return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = StringUtils.trimToNull(areaCode);
    }

    @Size(max = 64, message = "病区名称长度不能超过64个字符！")
    public String getAreaName() {
    	return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = StringUtils.trimToNull(areaName);
    }

    public LocalDateTime getInDate() {
    	return inDate;
    }

    public void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    @Size(max = 128, message = "人工气道类型：气管插管endotrachealintubation、气管切开tracheotomy长度不能超过128个字符！")
    public String getArtificialType() {
    	return artificialType;
    }

    public void setArtificialType(String artificialType) {
        this.artificialType = StringUtils.trimToNull(artificialType);
    }

    @Size(max = 128, message = "人工气道类型名称：气管插管endotrachealintubation、气管切开tracheotomy长度不能超过128个字符！")
    public String getArtificialTypeName() {
    	return artificialTypeName;
    }

    public void setArtificialTypeName(String artificialTypeName) {
        this.artificialTypeName = StringUtils.trimToNull(artificialTypeName);
    }

    @Size(max = 64, message = "导管类型：普通general、声门下吸引型导subglottic长度不能超过64个字符！")
    public String getExtubationType() {
    	return extubationType;
    }

    public void setExtubationType(String extubationType) {
        this.extubationType = StringUtils.trimToNull(extubationType);
    }

    @Size(max = 128, message = "导管类型名称：普通general、声门下吸引型导subglottic长度不能超过128个字符！")
    public String getExtubationTypeName() {
    	return extubationTypeName;
    }

    public void setExtubationTypeName(String extubationTypeName) {
        this.extubationTypeName = StringUtils.trimToNull(extubationTypeName);
    }

    @Size(max = 128, message = "湿化装置：呼吸机加温加湿ventilator、人工鼻湿化artificial、生理盐水滴注salineinfusion、其他other长度不能超过128个字符！")
    public String getHumidifyDevice() {
    	return humidifyDevice;
    }

    public void setHumidifyDevice(String humidifyDevice) {
        this.humidifyDevice = StringUtils.trimToNull(humidifyDevice);
    }

    @Size(max = 128, message = "湿化装置名称：呼吸机加温加湿ventilator、人工鼻湿化artificial、生理盐水滴注salineinfusion、其他other长度不能超过128个字符！")
    public String getHumidifyDeviceName() {
    	return humidifyDeviceName;
    }

    public void setHumidifyDeviceName(String humidifyDeviceName) {
        this.humidifyDeviceName = StringUtils.trimToNull(humidifyDeviceName);
    }

    @Size(max = 64, message = "吸痰方式：密闭式吸痰closed、开放式吸痰open长度不能超过64个字符！")
    public String getSputumType() {
    	return sputumType;
    }

    public void setSputumType(String sputumType) {
        this.sputumType = StringUtils.trimToNull(sputumType);
    }

    @Size(max = 64, message = "吸痰方式名称：密闭式吸痰closed、开放式吸痰open长度不能超过64个字符！")
    public String getSputumTypeName() {
    	return sputumTypeName;
    }

    public void setSputumTypeName(String sputumTypeName) {
        this.sputumTypeName = StringUtils.trimToNull(sputumTypeName);
    }

    @Size(max = 64, message = "口腔护理方式：擦拭wipe、擦拭+冲洗wipeandrinse 、刷牙brushteeth长度不能超过64个字符！")
    public String getOralCareType() {
    	return oralCareType;
    }

    public void setOralCareType(String oralCareType) {
        this.oralCareType = StringUtils.trimToNull(oralCareType);
    }

    @Size(max = 64, message = "口腔护理方式名称：擦拭wipe、擦拭+冲洗wipeandrinse 、刷牙brushteeth长度不能超过64个字符！")
    public String getOralCareTypeName() {
    	return oralCareTypeName;
    }

    public void setOralCareTypeName(String oralCareTypeName) {
        this.oralCareTypeName = StringUtils.trimToNull(oralCareTypeName);
    }

    public Integer getOralCareTimes() {
    	return oralCareTimes;
    }

    public void setOralCareTimes(Integer oralCareTimes) {
        this.oralCareTimes = oralCareTimes;
    }

    @Size(max = 128, message = "口腔护理液选择：生理盐水normalsaline、含洗必泰includingchlorhexidine、腔护理液cavitynursing、牙膏toothpaste、其他other长度不能超过128个字符！")
    public String getOralNursing() {
    	return oralNursing;
    }

    public void setOralNursing(String oralNursing) {
        this.oralNursing = StringUtils.trimToNull(oralNursing);
    }

    @Size(max = 128, message = "口腔护理液选择名称：生理盐水normalsaline、含洗必泰includingchlorhexidine、腔护理液cavitynursing、牙膏toothpaste、其他other长度不能超过128个字符！")
    public String getOralNursingName() {
    	return oralNursingName;
    }

    public void setOralNursingName(String oralNursingName) {
        this.oralNursingName = StringUtils.trimToNull(oralNursingName);
    }

    public String getOtherNursing() {
    	return otherNursing;
    }

    public void setOtherNursing(String otherNursing) {
        this.otherNursing = StringUtils.trimToNull(otherNursing);
    }

    public boolean isNasogasticFlag() {
    	return nasogasticFlag;
    }

    public void setNasogasticFlag(boolean nasogasticFlag) {
        this.nasogasticFlag = nasogasticFlag;
    }

    public Integer getVapTime() {
    	return vapTime;
    }

    public void setVapTime(Integer vapTime) {
        this.vapTime = vapTime;
    }
}