package com.bjgoodwill.hip.ds.cis.adv.nursing.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.nursing.to.CisAdvEventNursingEto;
import com.bjgoodwill.hip.ds.cis.adv.nursing.to.CisAdvEventNursingNto;
import com.bjgoodwill.hip.ds.cis.adv.nursing.to.CisAdvEventNursingQto;
import com.bjgoodwill.hip.ds.cis.adv.nursing.to.CisAdvEventNursingTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "护理不良事件报告单领域服务", description = "护理不良事件报告单领域服务")
public interface CisAdvEventNursingService {

    @Operation(summary = "根据查询条件对护理不良事件报告单进行查询。")
    @GetMapping("/cisAdvEventNursings")
    List<CisAdvEventNursingTo> getCisAdvEventNursings(@ParameterObject @SpringQueryMap CisAdvEventNursingQto cisAdvEventNursingQto);

    @Operation(summary = "根据查询条件对护理不良事件报告单进行分页查询。")
    @GetMapping("/cisAdvEventNursings/pages")
    GridResultSet<CisAdvEventNursingTo> getCisAdvEventNursingPage(@ParameterObject @SpringQueryMap CisAdvEventNursingQto cisAdvEventNursingQto);

    @Operation(summary = "根据唯一标识返回护理不良事件报告单。")
    @GetMapping("/cisAdvEventNursings/{id:.+}")
    CisAdvEventNursingTo getCisAdvEventNursingById(@PathVariable("id") String id);

    @Operation(summary = "创建护理不良事件报告单。")
    @PostMapping("/cisAdvEventNursings")
    CisAdvEventNursingTo createCisAdvEventNursing(@RequestBody @Valid CisAdvEventNursingNto cisAdvEventNursingNto);

    @Operation(summary = "根据唯一标识修改护理不良事件报告单。")
    @PutMapping("/cisAdvEventNursings/{id:.+}")
    void updateCisAdvEventNursing(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventNursingEto cisAdvEventNursingEto);

    @Operation(summary = "根据唯一标识删除护理不良事件报告单。")
    @DeleteMapping("/cisAdvEventNursings/{id:.+}")
    void deleteCisAdvEventNursing(@PathVariable("id") String id);

}