package com.bjgoodwill.hip.ds.cis.adv.pressure.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "压力性损伤上报表")
public class CisAdvEventPressureInjuryTo implements Serializable {

	@Serial
    private static final long serialVersionUID = -7356414017694497887L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "入院日期")
    private LocalDateTime inDate;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区科室名称")
    private String areaName;
    @Schema(description = "诊断")
    private String clinicalDiagnosis;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "风险评估工具: braden评分表，norton评分表，waterlow评分表，braden-q评分表，其他，未评估")
    private String assessmentTool;
    @Schema(description = "入病区时是否进行压力性损伤风险评估：0 无；1 有；")
    private boolean assessmentFlag;
    @Schema(description = "入院时压力性损伤风险评估分数")
    private Integer assessmentScore;
    @Schema(description = "入院时压力性损伤风险评估级别:lowrisk低危；middlerisk中危；highrisk高危；extremelrisk极危险；")
    private String assessmentLevel;
    @Schema(description = "最近1次压力性损伤风险评估分数")
    private Integer latelyAssessmentScore;
    @Schema(description = "最近1次压力性损伤风险评估距离发现时间:a小于24小时；b 1天；d 2天；e 3天；f 4天；g 5天；h 6天；i 1周；j 1周前；k 未评估；")
    private String latelyTime;
    @Schema(description = "最近1次压力性损伤评估级别:lowrisk低危；middlerisk中危；highrisk高危；extremelrisk极危险；")
    private String latelyAssessmentLevel;
    @Schema(description = "院外带入压力性损伤的部位数量")
    private Integer outOspitalNum;
    @Schema(description = "院外带入来源home自家庭入住; beadhouse自养老院入住；otherhospital自其他医院转入；other入住;")
    private String outOspitalSource;
    @Schema(description = "医院获得性压力性损伤的部位数量")
    private Integer inHospitalNum;
    @Schema(description = "other院内其他病区带入压力性损伤的标识；newdiscovery入本病区24小时后新发压力性损伤的标识")
    private String inOspitalSource;
    @Schema(description = "不可避免压力性损伤的原因：severemalnutrition严重营养不良；severehypoproteinemia严重低蛋白血症；advancedcancer癌症晚期恶液质 ；bloodcirculation血液循环差；needdisease医嘱或病情需要不能变换体位；oneself患者和家属拒绝配合；other其他；")
    private String inevitablePressureInjury;
    @Schema(description = "不可避免压力性损伤的原因：severemalnutrition严重营养不良；severehypoproteinemia严重低蛋白血症；advancedcancer癌症晚期恶液质 ；bloodcirculation血液循环差；needdisease医嘱或病情需要不能变换体位；oneself患者和家属拒绝配合；other其他；")
    private String inevitablePressureInjuryName;
    @Schema(description = "护理措施")
    private String improvementMeasures;
    @Schema(description = "护士长签字")
    private String headSignature;
    @Schema(description = "护士长签字名称")
    private String headSignatureName;
    @Schema(description = "护士长填写时间")
    private LocalDateTime headSignatureDate;
    @Schema(description = "护理部确认及指导意见")
    private String nursDeptOpinion;
    @Schema(description = "护理部签字")
    private String nursDeptSignature;
    @Schema(description = "护理部签字名称")
    private String nursDeptSignatureName;
    @Schema(description = "护理部签字时间")
    private LocalDateTime deptSignatureDate;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;

    public String getId() {
    	return id;
    }

    public void setId(String id) {
    	this.id = id;
    }

    public String getEventReportId() {
    	return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
    	this.eventReportId = eventReportId;
    }

    public String getPatType() {
    	return patType;
    }

    public void setPatType(String patType) {
    	this.patType = patType;
    }

    public String getInpatientCode() {
    	return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
    	this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    public void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public String getPatName() {
    	return patName;
    }

    public void setPatName(String patName) {
    	this.patName = patName;
    }

    public String getSex() {
    	return sex;
    }

    public void setSex(String sex) {
    	this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
    	this.birthDate = birthDate;
    }

    public LocalDateTime getInDate() {
    	return inDate;
    }

    public void setInDate(LocalDateTime inDate) {
    	this.inDate = inDate;
    }

    public String getAreaCode() {
    	return areaCode;
    }

    public void setAreaCode(String areaCode) {
    	this.areaCode = areaCode;
    }

    public String getAreaName() {
    	return areaName;
    }

    public void setAreaName(String areaName) {
    	this.areaName = areaName;
    }

    public String getClinicalDiagnosis() {
    	return clinicalDiagnosis;
    }

    public void setClinicalDiagnosis(String clinicalDiagnosis) {
    	this.clinicalDiagnosis = clinicalDiagnosis;
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
    	this.eventDate = eventDate;
    }

    public String getAssessmentTool() {
    	return assessmentTool;
    }

    public void setAssessmentTool(String assessmentTool) {
    	this.assessmentTool = assessmentTool;
    }

    public boolean isAssessmentFlag() {
    	return assessmentFlag;
    }

    public void setAssessmentFlag(boolean assessmentFlag) {
    	this.assessmentFlag = assessmentFlag;
    }

    public Integer getAssessmentScore() {
    	return assessmentScore;
    }

    public void setAssessmentScore(Integer assessmentScore) {
    	this.assessmentScore = assessmentScore;
    }

    public String getAssessmentLevel() {
    	return assessmentLevel;
    }

    public void setAssessmentLevel(String assessmentLevel) {
    	this.assessmentLevel = assessmentLevel;
    }

    public Integer getLatelyAssessmentScore() {
    	return latelyAssessmentScore;
    }

    public void setLatelyAssessmentScore(Integer latelyAssessmentScore) {
    	this.latelyAssessmentScore = latelyAssessmentScore;
    }

    public String getLatelyTime() {
    	return latelyTime;
    }

    public void setLatelyTime(String latelyTime) {
    	this.latelyTime = latelyTime;
    }

    public String getLatelyAssessmentLevel() {
    	return latelyAssessmentLevel;
    }

    public void setLatelyAssessmentLevel(String latelyAssessmentLevel) {
    	this.latelyAssessmentLevel = latelyAssessmentLevel;
    }

    public Integer getOutOspitalNum() {
    	return outOspitalNum;
    }

    public void setOutOspitalNum(Integer outOspitalNum) {
    	this.outOspitalNum = outOspitalNum;
    }

    public String getOutOspitalSource() {
    	return outOspitalSource;
    }

    public void setOutOspitalSource(String outOspitalSource) {
    	this.outOspitalSource = outOspitalSource;
    }

    public Integer getInHospitalNum() {
    	return inHospitalNum;
    }

    public void setInHospitalNum(Integer inHospitalNum) {
    	this.inHospitalNum = inHospitalNum;
    }

    public String getInOspitalSource() {
    	return inOspitalSource;
    }

    public void setInOspitalSource(String inOspitalSource) {
    	this.inOspitalSource = inOspitalSource;
    }

    public String getInevitablePressureInjury() {
    	return inevitablePressureInjury;
    }

    public void setInevitablePressureInjury(String inevitablePressureInjury) {
    	this.inevitablePressureInjury = inevitablePressureInjury;
    }

    public String getInevitablePressureInjuryName() {
    	return inevitablePressureInjuryName;
    }

    public void setInevitablePressureInjuryName(String inevitablePressureInjuryName) {
    	this.inevitablePressureInjuryName = inevitablePressureInjuryName;
    }

    public String getImprovementMeasures() {
    	return improvementMeasures;
    }

    public void setImprovementMeasures(String improvementMeasures) {
    	this.improvementMeasures = improvementMeasures;
    }

    public String getHeadSignature() {
    	return headSignature;
    }

    public void setHeadSignature(String headSignature) {
    	this.headSignature = headSignature;
    }

    public String getHeadSignatureName() {
    	return headSignatureName;
    }

    public void setHeadSignatureName(String headSignatureName) {
    	this.headSignatureName = headSignatureName;
    }

    public LocalDateTime getHeadSignatureDate() {
    	return headSignatureDate;
    }

    public void setHeadSignatureDate(LocalDateTime headSignatureDate) {
    	this.headSignatureDate = headSignatureDate;
    }

    public String getNursDeptOpinion() {
    	return nursDeptOpinion;
    }

    public void setNursDeptOpinion(String nursDeptOpinion) {
    	this.nursDeptOpinion = nursDeptOpinion;
    }

    public String getNursDeptSignature() {
    	return nursDeptSignature;
    }

    public void setNursDeptSignature(String nursDeptSignature) {
    	this.nursDeptSignature = nursDeptSignature;
    }

    public String getNursDeptSignatureName() {
    	return nursDeptSignatureName;
    }

    public void setNursDeptSignatureName(String nursDeptSignatureName) {
    	this.nursDeptSignatureName = nursDeptSignatureName;
    }

    public LocalDateTime getDeptSignatureDate() {
    	return deptSignatureDate;
    }

    public void setDeptSignatureDate(LocalDateTime deptSignatureDate) {
    	this.deptSignatureDate = deptSignatureDate;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventPressureInjuryTo other = (CisAdvEventPressureInjuryTo) obj;
		return Objects.equals(id, other.id);
	}
}