package com.bjgoodwill.hip.ds.cis.adv.extubation.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.extubation.to.CisAdvEventExtubationEto;
import com.bjgoodwill.hip.ds.cis.adv.extubation.to.CisAdvEventExtubationNto;
import com.bjgoodwill.hip.ds.cis.adv.extubation.to.CisAdvEventExtubationQto;
import com.bjgoodwill.hip.ds.cis.adv.extubation.to.CisAdvEventExtubationTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "非计划拔管事件上报表领域服务", description = "非计划拔管事件上报表领域服务")
public interface CisAdvEventExtubationService {

    @Operation(summary = "根据查询条件对非计划拔管事件上报表进行查询。")
    @GetMapping("/cisAdvEventExtubations")
    List<CisAdvEventExtubationTo> getCisAdvEventExtubations(@ParameterObject @SpringQueryMap CisAdvEventExtubationQto cisAdvEventExtubationQto);

    @Operation(summary = "根据查询条件对非计划拔管事件上报表进行分页查询。")
    @GetMapping("/cisAdvEventExtubations/pages")
    GridResultSet<CisAdvEventExtubationTo> getCisAdvEventExtubationPage(@ParameterObject @SpringQueryMap CisAdvEventExtubationQto cisAdvEventExtubationQto);

    @Operation(summary = "根据唯一标识返回非计划拔管事件上报表。")
    @GetMapping("/cisAdvEventExtubations/{id:.+}")
    CisAdvEventExtubationTo getCisAdvEventExtubationById(@PathVariable("id") String id);

    @Operation(summary = "创建非计划拔管事件上报表。")
    @PostMapping("/cisAdvEventExtubations")
    CisAdvEventExtubationTo createCisAdvEventExtubation(@RequestBody @Valid CisAdvEventExtubationNto cisAdvEventExtubationNto);

    @Operation(summary = "根据唯一标识修改非计划拔管事件上报表。")
    @PutMapping("/cisAdvEventExtubations/{id:.+}")
    void updateCisAdvEventExtubation(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventExtubationEto cisAdvEventExtubationEto);

    @Operation(summary = "根据唯一标识删除非计划拔管事件上报表。")
    @DeleteMapping("/cisAdvEventExtubations/{id:.+}")
    void deleteCisAdvEventExtubation(@PathVariable("id") String id);

}