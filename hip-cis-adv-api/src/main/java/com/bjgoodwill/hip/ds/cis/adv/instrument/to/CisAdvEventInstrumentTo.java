package com.bjgoodwill.hip.ds.cis.adv.instrument.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "医疗器械不良事件报告")
public class CisAdvEventInstrumentTo implements Serializable {

	@Serial
    private static final long serialVersionUID = -6224234945038484086L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区科室名称")
    private String areaName;
    @Schema(description = "既往病史")
    private String pastValue;
    @Schema(description = "产品名称")
    private String instrumentName;
    @Schema(description = "注册证编号")
    private String instrumentTrademark;
    @Schema(description = "型号规格")
    private String instrumentModel;
    @Schema(description = "产品批号")
    private String instrumentBatchNo;
    @Schema(description = "产品编号")
    private String instrumentNo;
    @Schema(description = "udi器械识别码")
    private String instrumentUdi;
    @Schema(description = "生产日期")
    private LocalDateTime manufactureDate;
    @Schema(description = "有效期至")
    private LocalDateTime validUntil;
    @Schema(description = "生产企业")
    private String sourceInformation;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "发现或获知日期")
    private LocalDateTime knowledgeDate;
    @Schema(description = "伤害程度:death死亡；serious严重伤害；other其他；")
    private String injuryDegree;
    @Schema(description = "伤害程度名称")
    private String injuryDegreeName;
    @Schema(description = "伤害表现")
    private String injuryPerformance;
    @Schema(description = "器械故障表现")
    private String faultPerformance;
    @Schema(description = "预期治疗疾病或作用")
    private String treatmentEffect;
    @Schema(description = "器械使用日期")
    private LocalDateTime useDate;
    @Schema(description = "使用场所:medical医疗机构；family家庭；other其他")
    private String usePlace;
    @Schema(description = "使用场所名称")
    private String usePlaceName;
    @Schema(description = "使用过程")
    private String useProcess;
    @Schema(description = "合并用药器械说明")
    private String useCombined;
    @Schema(description = "事件原因分析:product产品原因；operation操作原因；oneself患者自身原因；undetermined无法确定；")
    private String eventReasonsType;
    @Schema(description = "事件原因分析名称")
    private String eventReasonsTypeName;
    @Schema(description = "事件原因分析描述")
    private String eventReasonsDescribe;
    @Schema(description = "初步处置情况")
    private String disposalSituation;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;

    public String getId() {
    	return id;
    }

    public void setId(String id) {
    	this.id = id;
    }

    public String getEventReportId() {
    	return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
    	this.eventReportId = eventReportId;
    }

    public String getPatType() {
    	return patType;
    }

    public void setPatType(String patType) {
    	this.patType = patType;
    }

    public String getInpatientCode() {
    	return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
    	this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    public void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public String getPatName() {
    	return patName;
    }

    public void setPatName(String patName) {
    	this.patName = patName;
    }

    public String getSex() {
    	return sex;
    }

    public void setSex(String sex) {
    	this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
    	this.birthDate = birthDate;
    }

    public String getAreaCode() {
    	return areaCode;
    }

    public void setAreaCode(String areaCode) {
    	this.areaCode = areaCode;
    }

    public String getAreaName() {
    	return areaName;
    }

    public void setAreaName(String areaName) {
    	this.areaName = areaName;
    }

    public String getPastValue() {
    	return pastValue;
    }

    public void setPastValue(String pastValue) {
    	this.pastValue = pastValue;
    }

    public String getInstrumentName() {
    	return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
    	this.instrumentName = instrumentName;
    }

    public String getInstrumentTrademark() {
    	return instrumentTrademark;
    }

    public void setInstrumentTrademark(String instrumentTrademark) {
    	this.instrumentTrademark = instrumentTrademark;
    }

    public String getInstrumentModel() {
    	return instrumentModel;
    }

    public void setInstrumentModel(String instrumentModel) {
    	this.instrumentModel = instrumentModel;
    }

    public String getInstrumentBatchNo() {
    	return instrumentBatchNo;
    }

    public void setInstrumentBatchNo(String instrumentBatchNo) {
    	this.instrumentBatchNo = instrumentBatchNo;
    }

    public String getInstrumentNo() {
    	return instrumentNo;
    }

    public void setInstrumentNo(String instrumentNo) {
    	this.instrumentNo = instrumentNo;
    }

    public String getInstrumentUdi() {
    	return instrumentUdi;
    }

    public void setInstrumentUdi(String instrumentUdi) {
    	this.instrumentUdi = instrumentUdi;
    }

    public LocalDateTime getManufactureDate() {
    	return manufactureDate;
    }

    public void setManufactureDate(LocalDateTime manufactureDate) {
    	this.manufactureDate = manufactureDate;
    }

    public LocalDateTime getValidUntil() {
    	return validUntil;
    }

    public void setValidUntil(LocalDateTime validUntil) {
    	this.validUntil = validUntil;
    }

    public String getSourceInformation() {
    	return sourceInformation;
    }

    public void setSourceInformation(String sourceInformation) {
    	this.sourceInformation = sourceInformation;
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
    	this.eventDate = eventDate;
    }

    public LocalDateTime getKnowledgeDate() {
    	return knowledgeDate;
    }

    public void setKnowledgeDate(LocalDateTime knowledgeDate) {
    	this.knowledgeDate = knowledgeDate;
    }

    public String getInjuryDegree() {
    	return injuryDegree;
    }

    public void setInjuryDegree(String injuryDegree) {
    	this.injuryDegree = injuryDegree;
    }

    public String getInjuryDegreeName() {
    	return injuryDegreeName;
    }

    public void setInjuryDegreeName(String injuryDegreeName) {
    	this.injuryDegreeName = injuryDegreeName;
    }

    public String getInjuryPerformance() {
    	return injuryPerformance;
    }

    public void setInjuryPerformance(String injuryPerformance) {
    	this.injuryPerformance = injuryPerformance;
    }

    public String getFaultPerformance() {
    	return faultPerformance;
    }

    public void setFaultPerformance(String faultPerformance) {
    	this.faultPerformance = faultPerformance;
    }

    public String getTreatmentEffect() {
    	return treatmentEffect;
    }

    public void setTreatmentEffect(String treatmentEffect) {
    	this.treatmentEffect = treatmentEffect;
    }

    public LocalDateTime getUseDate() {
    	return useDate;
    }

    public void setUseDate(LocalDateTime useDate) {
    	this.useDate = useDate;
    }

    public String getUsePlace() {
    	return usePlace;
    }

    public void setUsePlace(String usePlace) {
    	this.usePlace = usePlace;
    }

    public String getUsePlaceName() {
    	return usePlaceName;
    }

    public void setUsePlaceName(String usePlaceName) {
    	this.usePlaceName = usePlaceName;
    }

    public String getUseProcess() {
    	return useProcess;
    }

    public void setUseProcess(String useProcess) {
    	this.useProcess = useProcess;
    }

    public String getUseCombined() {
    	return useCombined;
    }

    public void setUseCombined(String useCombined) {
    	this.useCombined = useCombined;
    }

    public String getEventReasonsType() {
    	return eventReasonsType;
    }

    public void setEventReasonsType(String eventReasonsType) {
    	this.eventReasonsType = eventReasonsType;
    }

    public String getEventReasonsTypeName() {
    	return eventReasonsTypeName;
    }

    public void setEventReasonsTypeName(String eventReasonsTypeName) {
    	this.eventReasonsTypeName = eventReasonsTypeName;
    }

    public String getEventReasonsDescribe() {
    	return eventReasonsDescribe;
    }

    public void setEventReasonsDescribe(String eventReasonsDescribe) {
    	this.eventReasonsDescribe = eventReasonsDescribe;
    }

    public String getDisposalSituation() {
    	return disposalSituation;
    }

    public void setDisposalSituation(String disposalSituation) {
    	this.disposalSituation = disposalSituation;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventInstrumentTo other = (CisAdvEventInstrumentTo) obj;
		return Objects.equals(id, other.id);
	}
}