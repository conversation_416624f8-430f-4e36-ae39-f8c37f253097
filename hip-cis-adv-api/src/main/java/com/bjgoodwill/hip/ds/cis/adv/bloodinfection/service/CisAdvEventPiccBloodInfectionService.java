package com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionEto;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionNto;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionQto;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "PICC相关血流感染相关信息收集表领域服务", description = "PICC相关血流感染相关信息收集表领域服务")
public interface CisAdvEventPiccBloodInfectionService {

    @Operation(summary = "根据查询条件对PICC相关血流感染相关信息收集表进行查询。")
    @GetMapping("/cisAdvEventPiccBloodInfections")
    List<CisAdvEventPiccBloodInfectionTo> getCisAdvEventPiccBloodInfections(@ParameterObject @SpringQueryMap CisAdvEventPiccBloodInfectionQto cisAdvEventPiccBloodInfectionQto);

    @Operation(summary = "根据查询条件对PICC相关血流感染相关信息收集表进行分页查询。")
    @GetMapping("/cisAdvEventPiccBloodInfections/pages")
    GridResultSet<CisAdvEventPiccBloodInfectionTo> getCisAdvEventPiccBloodInfectionPage(@ParameterObject @SpringQueryMap CisAdvEventPiccBloodInfectionQto cisAdvEventPiccBloodInfectionQto);

    @Operation(summary = "根据唯一标识返回PICC相关血流感染相关信息收集表。")
    @GetMapping("/cisAdvEventPiccBloodInfections/{id:.+}")
    CisAdvEventPiccBloodInfectionTo getCisAdvEventPiccBloodInfectionById(@PathVariable("id") String id);

    @Operation(summary = "创建PICC相关血流感染相关信息收集表。")
    @PostMapping("/cisAdvEventPiccBloodInfections")
    CisAdvEventPiccBloodInfectionTo createCisAdvEventPiccBloodInfection(@RequestBody @Valid CisAdvEventPiccBloodInfectionNto cisAdvEventPiccBloodInfectionNto);

    @Operation(summary = "根据唯一标识修改PICC相关血流感染相关信息收集表。")
    @PutMapping("/cisAdvEventPiccBloodInfections/{id:.+}")
    void updateCisAdvEventPiccBloodInfection(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventPiccBloodInfectionEto cisAdvEventPiccBloodInfectionEto);

    @Operation(summary = "根据唯一标识删除PICC相关血流感染相关信息收集表。")
    @DeleteMapping("/cisAdvEventPiccBloodInfections/{id:.+}")
    void deleteCisAdvEventPiccBloodInfection(@PathVariable("id") String id);

}