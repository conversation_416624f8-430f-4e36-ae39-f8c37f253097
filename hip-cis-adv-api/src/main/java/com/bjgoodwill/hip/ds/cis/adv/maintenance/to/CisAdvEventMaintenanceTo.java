package com.bjgoodwill.hip.ds.cis.adv.maintenance.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "不良事件维护")
public class CisAdvEventMaintenanceTo implements Serializable {

	@Serial
    private static final long serialVersionUID = -4519434061079294963L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "上级目录编码")
    private String parentCode;
    @Schema(description = "不良事件编码")
    private String eventCode;
    @Schema(description = "不良事件名称")
    private String eventName;
    @Schema(description = "不良事件名称")
    private Integer eventLevel;
    @Schema(description = "已启用")
    private boolean enabled;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;

    public String getId() {
    	return id;
    }

    public void setId(String id) {
    	this.id = id;
    }

    public String getParentCode() {
    	return parentCode;
    }

    public void setParentCode(String parentCode) {
    	this.parentCode = parentCode;
    }

    public String getEventCode() {
    	return eventCode;
    }

    public void setEventCode(String eventCode) {
    	this.eventCode = eventCode;
    }

    public String getEventName() {
    	return eventName;
    }

    public void setEventName(String eventName) {
    	this.eventName = eventName;
    }

    public Integer getEventLevel() {
    	return eventLevel;
    }

    public void setEventLevel(Integer eventLevel) {
    	this.eventLevel = eventLevel;
    }

    public boolean isEnabled() {
    	return enabled;
    }

    public void setEnabled(boolean enabled) {
    	this.enabled = enabled;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventMaintenanceTo other = (CisAdvEventMaintenanceTo) obj;
		return Objects.equals(id, other.id);
	}
}