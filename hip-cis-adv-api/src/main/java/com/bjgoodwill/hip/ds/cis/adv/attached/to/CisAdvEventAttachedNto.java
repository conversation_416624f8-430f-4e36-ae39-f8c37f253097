package com.bjgoodwill.hip.ds.cis.adv.attached.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "不良事件与附卡对照")
public class CisAdvEventAttachedNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -7141211768806025277L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "不良事件类别")
    private String eventCode;
    @Schema(description = "附卡")
    private String attachedCard;
    @Schema(description = "医院编码")
    private String hospitalCode;
    @Schema(description = "医院名称")
    private String hospitalName;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @Size(max = 32, message = "不良事件类别长度不能超过32个字符！")
    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = StringUtils.trimToNull(eventCode);
    }

    @Size(max = 32, message = "附卡长度不能超过32个字符！")
    public String getAttachedCard() {
        return attachedCard;
    }

    public void setAttachedCard(String attachedCard) {
        this.attachedCard = StringUtils.trimToNull(attachedCard);
    }

    @Size(max = 32, message = "医院编码长度不能超过32个字符！")
    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = StringUtils.trimToNull(hospitalCode);
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }
}