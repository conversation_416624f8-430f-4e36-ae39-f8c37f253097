package com.bjgoodwill.hip.ds.cis.adv.pressure.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureInjuryEto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureInjuryNto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureInjuryQto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureInjuryTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "压力性损伤上报表领域服务", description = "压力性损伤上报表领域服务")
public interface CisAdvEventPressureInjuryService {

    @Operation(summary = "根据查询条件对压力性损伤上报表进行查询。")
    @GetMapping("/cisAdvEventPressureInjuries")
    List<CisAdvEventPressureInjuryTo> getCisAdvEventPressureInjuries(@ParameterObject @SpringQueryMap CisAdvEventPressureInjuryQto cisAdvEventPressureInjuryQto);

    @Operation(summary = "根据查询条件对压力性损伤上报表进行分页查询。")
    @GetMapping("/cisAdvEventPressureInjuries/pages")
    GridResultSet<CisAdvEventPressureInjuryTo> getCisAdvEventPressureInjuryPage(@ParameterObject @SpringQueryMap CisAdvEventPressureInjuryQto cisAdvEventPressureInjuryQto);

    @Operation(summary = "根据唯一标识返回压力性损伤上报表。")
    @GetMapping("/cisAdvEventPressureInjuries/{id:.+}")
    CisAdvEventPressureInjuryTo getCisAdvEventPressureInjuryById(@PathVariable("id") String id);

    @Operation(summary = "创建压力性损伤上报表。")
    @PostMapping("/cisAdvEventPressureInjuries")
    CisAdvEventPressureInjuryTo createCisAdvEventPressureInjury(@RequestBody @Valid CisAdvEventPressureInjuryNto cisAdvEventPressureInjuryNto);

    @Operation(summary = "根据唯一标识修改压力性损伤上报表。")
    @PutMapping("/cisAdvEventPressureInjuries/{id:.+}")
    void updateCisAdvEventPressureInjury(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventPressureInjuryEto cisAdvEventPressureInjuryEto);

    @Operation(summary = "根据唯一标识删除压力性损伤上报表。")
    @DeleteMapping("/cisAdvEventPressureInjuries/{id:.+}")
    void deleteCisAdvEventPressureInjury(@PathVariable("id") String id);

}