package com.bjgoodwill.hip.ds.cis.adv.pressure.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegEto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegNto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegQto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "医院获得性压力性损伤情况登记领域服务", description = "医院获得性压力性损伤情况登记领域服务")
public interface CisAdvEventPressureRegService {

    @Operation(summary = "根据查询条件对医院获得性压力性损伤情况登记进行查询。")
    @GetMapping("/cisAdvEventPressureRegs")
    List<CisAdvEventPressureRegTo> getCisAdvEventPressureRegs(@ParameterObject @SpringQueryMap CisAdvEventPressureRegQto cisAdvEventPressureRegQto);

    @Operation(summary = "根据查询条件对医院获得性压力性损伤情况登记进行分页查询。")
    @GetMapping("/cisAdvEventPressureRegs/pages")
    GridResultSet<CisAdvEventPressureRegTo> getCisAdvEventPressureRegPage(@ParameterObject @SpringQueryMap CisAdvEventPressureRegQto cisAdvEventPressureRegQto);

    @Operation(summary = "根据唯一标识返回医院获得性压力性损伤情况登记。")
    @GetMapping("/cisAdvEventPressureRegs/{id:.+}")
    CisAdvEventPressureRegTo getCisAdvEventPressureRegById(@PathVariable("id") String id);

    @Operation(summary = "创建医院获得性压力性损伤情况登记。")
    @PostMapping("/cisAdvEventPressureRegs")
    CisAdvEventPressureRegTo createCisAdvEventPressureReg(@RequestBody @Valid CisAdvEventPressureRegNto cisAdvEventPressureRegNto);

    @Operation(summary = "根据唯一标识修改医院获得性压力性损伤情况登记。")
    @PutMapping("/cisAdvEventPressureRegs/{id:.+}")
    void updateCisAdvEventPressureReg(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventPressureRegEto cisAdvEventPressureRegEto);

    @Operation(summary = "根据唯一标识删除医院获得性压力性损伤情况登记。")
    @DeleteMapping("/cisAdvEventPressureRegs/{id:.+}")
    void deleteCisAdvEventPressureReg(@PathVariable("id") String id);

}