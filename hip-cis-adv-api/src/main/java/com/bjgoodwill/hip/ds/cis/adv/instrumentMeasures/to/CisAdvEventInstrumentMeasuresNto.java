package com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "医疗器械不良事件整改措施")
public class CisAdvEventInstrumentMeasuresNto extends BaseNto implements Serializable {

	@Serial
    private static final long serialVersionUID = -3131967322703215853L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "事件发生科室")
    private String eventDeptCode;
    @Schema(description = "事件发生科室名称")
    private String eventDeptName;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "事件经过")
    private String eventProcess;
    @Schema(description = "改进措施")
    private String improvementMeasures;
    @Schema(description = "处理结果")
    private String processingResult;
    @Schema(description = "负责人签名")
    private String leaderSign;
    @Schema(description = "负责人签名名称")
    private String leaderSignName;
    @Schema(description = "签名日期")
    private LocalDateTime signDate;
    @Schema(description = "生产批号")
    private String batchNo;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
    	return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @Size(max = 50, message = "不良事件id长度不能超过50个字符！")
    public String getEventReportId() {
    	return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = StringUtils.trimToNull(eventReportId);
    }

    @Size(max = 16, message = "事件发生科室长度不能超过16个字符！")
    public String getEventDeptCode() {
    	return eventDeptCode;
    }

    public void setEventDeptCode(String eventDeptCode) {
        this.eventDeptCode = StringUtils.trimToNull(eventDeptCode);
    }

    @Size(max = 32, message = "事件发生科室名称长度不能超过32个字符！")
    public String getEventDeptName() {
    	return eventDeptName;
    }

    public void setEventDeptName(String eventDeptName) {
        this.eventDeptName = StringUtils.trimToNull(eventDeptName);
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
        this.eventDate = eventDate;
    }

    public String getEventProcess() {
    	return eventProcess;
    }

    public void setEventProcess(String eventProcess) {
        this.eventProcess = StringUtils.trimToNull(eventProcess);
    }

    public String getImprovementMeasures() {
    	return improvementMeasures;
    }

    public void setImprovementMeasures(String improvementMeasures) {
        this.improvementMeasures = StringUtils.trimToNull(improvementMeasures);
    }

    public String getProcessingResult() {
    	return processingResult;
    }

    public void setProcessingResult(String processingResult) {
        this.processingResult = StringUtils.trimToNull(processingResult);
    }

    @Size(max = 16, message = "负责人签名长度不能超过16个字符！")
    public String getLeaderSign() {
    	return leaderSign;
    }

    public void setLeaderSign(String leaderSign) {
        this.leaderSign = StringUtils.trimToNull(leaderSign);
    }

    @Size(max = 32, message = "负责人签名名称长度不能超过32个字符！")
    public String getLeaderSignName() {
    	return leaderSignName;
    }

    public void setLeaderSignName(String leaderSignName) {
        this.leaderSignName = StringUtils.trimToNull(leaderSignName);
    }

    public LocalDateTime getSignDate() {
    	return signDate;
    }

    public void setSignDate(LocalDateTime signDate) {
        this.signDate = signDate;
    }

    @Size(max = 16, message = "生产批号长度不能超过16个字符！")
    public String getBatchNo() {
    	return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = StringUtils.trimToNull(batchNo);
    }
}