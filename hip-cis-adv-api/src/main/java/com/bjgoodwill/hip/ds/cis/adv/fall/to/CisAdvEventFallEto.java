package com.bjgoodwill.hip.ds.cis.adv.fall.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.time.LocalDateTime;

@Schema(description = "跌倒坠床事件上报表")
public class CisAdvEventFallEto extends BaseEto {

    @Serial
    private static final long serialVersionUID = -8643416240158810224L;

    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区科室名称")
    private String areaName;
    @Schema(description = "入院日期")
    private LocalDateTime inDate;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "事件发生场所：in病区内，out病区外（院内）")
    private String eventPlace;
    @Schema(description = "事件发生场所名称：in病区内，out病区外（院内）")
    private String eventPlaceName;
    @Schema(description = "本次跌倒(坠床)次数:1第一次；2第二次；3第三次；4大于3次")
    private Integer thisFallNum;
    @Schema(description = "跌倒(坠床)前患者活动能力:freemovement活动自如；bedridden卧床不起；walkingstick手杖；wheelchair轮椅；walkeraids助行器；prosthetic假肢")
    private String fallActivityAbility;
    @Schema(description = "跌倒(坠床)发生于何项活动过程:a躺卧病床；b上下病床；c坐床旁椅；d如厕；e沐浴时；站立；f行走时；g上下平车；h从事康复活动时")
    private String fallActivity;
    @Schema(description = "有无跌倒(坠床)伤害:0 无；1 有；")
    private boolean hurtFlag;
    @Schema(description = "跌倒(坠床)伤害级别:0跌倒无伤害 (0级)；1轻度伤害(1级)；2中度伤害(2级)；3重度伤害(3级)；4 死亡")
    private String hurtLevel;
    @Schema(description = "跌倒(坠床)伤害级别名称:0跌倒无伤害 (0级)；1轻度伤害(1级)；2中度伤害(2级)；3重度伤害(3级)；4 死亡")
    private String hurtLevelName;
    @Schema(description = "跌倒(坠床)原因类型:oneself患者因素；drugtreatment药物和(或)治疗因素; environment环境因素; other其他")
    private String fallReasonsType;
    @Schema(description = "跌倒(坠床)原因类型名称:oneself患者因素；drugtreatment药物和(或)治疗因素; environment环境因素; other其他")
    private String fallReasonsTypeName;
    @Schema(description = "跌倒(坠床)风险评估工具：morse跌倒（坠床）风险评估量表;约翰霍普金斯跌倒（坠床）风险评估量表;改良版humpty dumpty 儿童跌倒（坠床）风险量表;托马斯跌倒（坠床）风险评估工具;hendrich跌倒（坠床）风险评估表;其他")
    private String assessmentInstrument;
    @Schema(description = "跌倒(坠床)风险评估工具：morse跌倒（坠床）风险评估量表;约翰霍普金斯跌倒（坠床）风险评估量表;改良版humpty dumpty 儿童跌倒（坠床）风险量表;托马斯跌倒（坠床）风险评估工具;hendrich跌倒（坠床）风险评估表;其他")
    private String assessmentInstrumentName;
    @Schema(description = "跌倒(坠床)前有无跌倒(坠床)风险评估:0 无；1 有；")
    private boolean assessmentFlag;
    @Schema(description = "跌倒(坠床)前跌倒(坠床)风险评估分数（分）")
    private Integer assessmentScore;
    @Schema(description = "跌倒(坠床)前是否评估为跌倒(坠床)高危人群：0 否；1 是；")
    private boolean fallHighGroup;
    @Schema(description = "最近一天跌倒(坠床)风险评估距跌倒(坠床)发生时间：a小于24小时；b 1天；c 2天；d 3天；e 4天；f 5天；g 6天；h 1周；i 1周前；uncertain不确定；")
    private String lastTime;
    @Schema(description = "最近一天跌倒(坠床)风险评估距跌倒(坠床)发生时间：a小于24小时；b 1天；c 2天；d 3天；e 4天；f 5天；g 6天；h 1周；i 1周前；uncertain不确定；")
    private String lastTimeName;
    @Schema(description = "跌倒(坠床)时有无约束:0 否；1 是；")
    private boolean constraintFlag;
    @Schema(description = "跌倒(坠床)发生时当班护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    private String workingLife;
    @Schema(description = "跌倒(坠床)发生时当班护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    private String workingLifeName;
    @Schema(description = "跌倒(坠床)发生时在岗责任护士人数")
    private Integer dutyNum;
    @Schema(description = "跌倒(坠床)发生时病区在院患者数")
    private Integer areaNum;
    @Schema(description = "跌倒(坠床)发生经过")
    private String eventProcess;
    @Schema(description = "护士签字")
    private String nursSignature;
    @Schema(description = "护士签字名称")
    private String nursSignatureName;
    @Schema(description = "护士填写时间")
    private LocalDateTime nursSignatureDate;
    @Schema(description = "护理措施")
    private String nursingMeasure;
    @Schema(description = "护士长签字")
    private String headSignature;
    @Schema(description = "护士长签字名称")
    private String headSignatureName;
    @Schema(description = "护士长签字时间")
    private LocalDateTime headSignatureDate;
    @Schema(description = "护理部确认及指导意见")
    private String nursDeptOpinion;
    @Schema(description = "护理部签字")
    private String nursDeptSignature;
    @Schema(description = "护理部签字名称")
    private String nursDeptSignatureName;
    @Schema(description = "护理部签字时间")
    private LocalDateTime deptSignatureDate;

    @Size(max = 50, message = "不良事件id长度不能超过50个字符！")
    public String getEventReportId() {
        return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = StringUtils.trimToNull(eventReportId);
    }

    @Size(max = 16, message = "患者类型长度不能超过16个字符！")
    public String getPatType() {
        return patType;
    }

    public void setPatType(String patType) {
        this.patType = StringUtils.trimToNull(patType);
    }

    @Size(max = 16, message = "住院号(门诊就诊卡号)长度不能超过16个字符！")
    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = StringUtils.trimToNull(inpatientCode);
    }

    @Size(max = 16, message = "就诊流水号长度不能超过16个字符！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @Size(max = 64, message = "患者姓名长度不能超过64个字符！")
    public String getPatName() {
        return patName;
    }

    public void setPatName(String patName) {
        this.patName = StringUtils.trimToNull(patName);
    }

    @Size(max = 16, message = "性别长度不能超过16个字符！")
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = StringUtils.trimToNull(sex);
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    @Size(max = 16, message = "病区科室长度不能超过16个字符！")
    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = StringUtils.trimToNull(areaCode);
    }

    @Size(max = 32, message = "病区科室名称长度不能超过32个字符！")
    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = StringUtils.trimToNull(areaName);
    }

    public LocalDateTime getInDate() {
        return inDate;
    }

    public void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    public LocalDateTime getEventDate() {
        return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
        this.eventDate = eventDate;
    }

    @Size(max = 16, message = "事件发生场所：in病区内，out病区外（院内）长度不能超过16个字符！")
    public String getEventPlace() {
        return eventPlace;
    }

    public void setEventPlace(String eventPlace) {
        this.eventPlace = StringUtils.trimToNull(eventPlace);
    }

    @Size(max = 32, message = "事件发生场所名称：in病区内，out病区外（院内）长度不能超过32个字符！")
    public String getEventPlaceName() {
        return eventPlaceName;
    }

    public void setEventPlaceName(String eventPlaceName) {
        this.eventPlaceName = StringUtils.trimToNull(eventPlaceName);
    }

    public Integer getThisFallNum() {
        return thisFallNum;
    }

    public void setThisFallNum(Integer thisFallNum) {
        this.thisFallNum = thisFallNum;
    }

    @Size(max = 16, message = "跌倒(坠床)前患者活动能力:freemovement活动自如；bedridden卧床不起；walkingstick手杖；wheelchair轮椅；walkeraids助行器；prosthetic假肢长度不能超过16个字符！")
    public String getFallActivityAbility() {
        return fallActivityAbility;
    }

    public void setFallActivityAbility(String fallActivityAbility) {
        this.fallActivityAbility = StringUtils.trimToNull(fallActivityAbility);
    }

    @Size(max = 16, message = "跌倒(坠床)发生于何项活动过程:a躺卧病床；b上下病床；c坐床旁椅；d如厕；e沐浴时；站立；f行走时；g上下平车；h从事康复活动时长度不能超过16个字符！")
    public String getFallActivity() {
        return fallActivity;
    }

    public void setFallActivity(String fallActivity) {
        this.fallActivity = StringUtils.trimToNull(fallActivity);
    }

    public boolean isHurtFlag() {
        return hurtFlag;
    }

    public void setHurtFlag(boolean hurtFlag) {
        this.hurtFlag = hurtFlag;
    }

    @Size(max = 16, message = "跌倒(坠床)伤害级别:0跌倒无伤害 (0级)；1轻度伤害(1级)；2中度伤害(2级)；3重度伤害(3级)；4 死亡长度不能超过16个字符！")
    public String getHurtLevel() {
        return hurtLevel;
    }

    public void setHurtLevel(String hurtLevel) {
        this.hurtLevel = StringUtils.trimToNull(hurtLevel);
    }

    @Size(max = 32, message = "跌倒(坠床)伤害级别名称:0跌倒无伤害 (0级)；1轻度伤害(1级)；2中度伤害(2级)；3重度伤害(3级)；4 死亡长度不能超过32个字符！")
    public String getHurtLevelName() {
        return hurtLevelName;
    }

    public void setHurtLevelName(String hurtLevelName) {
        this.hurtLevelName = StringUtils.trimToNull(hurtLevelName);
    }

    @Size(max = 16, message = "跌倒(坠床)原因类型:oneself患者因素；drugtreatment药物和(或)治疗因素; environment环境因素; other其他长度不能超过16个字符！")
    public String getFallReasonsType() {
        return fallReasonsType;
    }

    public void setFallReasonsType(String fallReasonsType) {
        this.fallReasonsType = StringUtils.trimToNull(fallReasonsType);
    }

    @Size(max = 32, message = "跌倒(坠床)原因类型名称:oneself患者因素；drugtreatment药物和(或)治疗因素; environment环境因素; other其他长度不能超过32个字符！")
    public String getFallReasonsTypeName() {
        return fallReasonsTypeName;
    }

    public void setFallReasonsTypeName(String fallReasonsTypeName) {
        this.fallReasonsTypeName = StringUtils.trimToNull(fallReasonsTypeName);
    }

    @Size(max = 128, message = "跌倒(坠床)风险评估工具：morse跌倒（坠床）风险评估量表;约翰霍普金斯跌倒（坠床）风险评估量表;改良版humpty dumpty 儿童跌倒（坠床）风险量表;托马斯跌倒（坠床）风险评估工具;hendrich跌倒（坠床）风险评估表;其他长度不能超过128个字符！")
    public String getAssessmentInstrument() {
        return assessmentInstrument;
    }

    public void setAssessmentInstrument(String assessmentInstrument) {
        this.assessmentInstrument = StringUtils.trimToNull(assessmentInstrument);
    }

    @Size(max = 128, message = "跌倒(坠床)风险评估工具：morse跌倒（坠床）风险评估量表;约翰霍普金斯跌倒（坠床）风险评估量表;改良版humpty dumpty 儿童跌倒（坠床）风险量表;托马斯跌倒（坠床）风险评估工具;hendrich跌倒（坠床）风险评估表;其他长度不能超过128个字符！")
    public String getAssessmentInstrumentName() {
        return assessmentInstrumentName;
    }

    public void setAssessmentInstrumentName(String assessmentInstrumentName) {
        this.assessmentInstrumentName = StringUtils.trimToNull(assessmentInstrumentName);
    }

    public boolean isAssessmentFlag() {
        return assessmentFlag;
    }

    public void setAssessmentFlag(boolean assessmentFlag) {
        this.assessmentFlag = assessmentFlag;
    }

    public Integer getAssessmentScore() {
        return assessmentScore;
    }

    public void setAssessmentScore(Integer assessmentScore) {
        this.assessmentScore = assessmentScore;
    }

    public boolean isFallHighGroup() {
        return fallHighGroup;
    }

    public void setFallHighGroup(boolean fallHighGroup) {
        this.fallHighGroup = fallHighGroup;
    }

    @Size(max = 16, message = "最近一天跌倒(坠床)风险评估距跌倒(坠床)发生时间：a小于24小时；b 1天；c 2天；d 3天；e 4天；f 5天；g 6天；h 1周；i 1周前；uncertain不确定；长度不能超过16个字符！")
    public String getLastTime() {
        return lastTime;
    }

    public void setLastTime(String lastTime) {
        this.lastTime = StringUtils.trimToNull(lastTime);
    }

    @Size(max = 32, message = "最近一天跌倒(坠床)风险评估距跌倒(坠床)发生时间：a小于24小时；b 1天；c 2天；d 3天；e 4天；f 5天；g 6天；h 1周；i 1周前；uncertain不确定；长度不能超过32个字符！")
    public String getLastTimeName() {
        return lastTimeName;
    }

    public void setLastTimeName(String lastTimeName) {
        this.lastTimeName = StringUtils.trimToNull(lastTimeName);
    }

    public boolean isConstraintFlag() {
        return constraintFlag;
    }

    public void setConstraintFlag(boolean constraintFlag) {
        this.constraintFlag = constraintFlag;
    }

    @Size(max = 16, message = "跌倒(坠床)发生时当班护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年长度不能超过16个字符！")
    public String getWorkingLife() {
        return workingLife;
    }

    public void setWorkingLife(String workingLife) {
        this.workingLife = StringUtils.trimToNull(workingLife);
    }

    @Size(max = 32, message = "跌倒(坠床)发生时当班护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年长度不能超过32个字符！")
    public String getWorkingLifeName() {
        return workingLifeName;
    }

    public void setWorkingLifeName(String workingLifeName) {
        this.workingLifeName = StringUtils.trimToNull(workingLifeName);
    }

    public Integer getDutyNum() {
        return dutyNum;
    }

    public void setDutyNum(Integer dutyNum) {
        this.dutyNum = dutyNum;
    }

    public Integer getAreaNum() {
        return areaNum;
    }

    public void setAreaNum(Integer areaNum) {
        this.areaNum = areaNum;
    }

    public String getEventProcess() {
        return eventProcess;
    }

    public void setEventProcess(String eventProcess) {
        this.eventProcess = StringUtils.trimToNull(eventProcess);
    }

    @Size(max = 16, message = "护士签字长度不能超过16个字符！")
    public String getNursSignature() {
        return nursSignature;
    }

    public void setNursSignature(String nursSignature) {
        this.nursSignature = StringUtils.trimToNull(nursSignature);
    }

    @Size(max = 32, message = "护士签字名称长度不能超过32个字符！")
    public String getNursSignatureName() {
        return nursSignatureName;
    }

    public void setNursSignatureName(String nursSignatureName) {
        this.nursSignatureName = StringUtils.trimToNull(nursSignatureName);
    }

    public LocalDateTime getNursSignatureDate() {
        return nursSignatureDate;
    }

    public void setNursSignatureDate(LocalDateTime nursSignatureDate) {
        this.nursSignatureDate = nursSignatureDate;
    }

    public String getNursingMeasure() {
        return nursingMeasure;
    }

    public void setNursingMeasure(String nursingMeasure) {
        this.nursingMeasure = StringUtils.trimToNull(nursingMeasure);
    }

    @Size(max = 16, message = "护士长签字长度不能超过16个字符！")
    public String getHeadSignature() {
        return headSignature;
    }

    public void setHeadSignature(String headSignature) {
        this.headSignature = StringUtils.trimToNull(headSignature);
    }

    @Size(max = 32, message = "护士长签字名称长度不能超过32个字符！")
    public String getHeadSignatureName() {
        return headSignatureName;
    }

    public void setHeadSignatureName(String headSignatureName) {
        this.headSignatureName = StringUtils.trimToNull(headSignatureName);
    }

    public LocalDateTime getHeadSignatureDate() {
        return headSignatureDate;
    }

    public void setHeadSignatureDate(LocalDateTime headSignatureDate) {
        this.headSignatureDate = headSignatureDate;
    }

    public String getNursDeptOpinion() {
        return nursDeptOpinion;
    }

    public void setNursDeptOpinion(String nursDeptOpinion) {
        this.nursDeptOpinion = StringUtils.trimToNull(nursDeptOpinion);
    }

    @Size(max = 16, message = "护理部签字长度不能超过16个字符！")
    public String getNursDeptSignature() {
        return nursDeptSignature;
    }

    public void setNursDeptSignature(String nursDeptSignature) {
        this.nursDeptSignature = StringUtils.trimToNull(nursDeptSignature);
    }

    @Size(max = 32, message = "护理部签字名称长度不能超过32个字符！")
    public String getNursDeptSignatureName() {
        return nursDeptSignatureName;
    }

    public void setNursDeptSignatureName(String nursDeptSignatureName) {
        this.nursDeptSignatureName = StringUtils.trimToNull(nursDeptSignatureName);
    }

    public LocalDateTime getDeptSignatureDate() {
        return deptSignatureDate;
    }

    public void setDeptSignatureDate(LocalDateTime deptSignatureDate) {
        this.deptSignatureDate = deptSignatureDate;
    }
}