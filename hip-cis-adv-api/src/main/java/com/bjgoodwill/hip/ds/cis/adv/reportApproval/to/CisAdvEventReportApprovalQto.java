package com.bjgoodwill.hip.ds.cis.adv.reportApproval.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.AdvEventsStatusEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.time.LocalDateTime;

@Schema(description = "不良事件报告审批")
public class CisAdvEventReportApprovalQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -4974544558817794315L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "主管部门")
    private String opinionOrgCode;
    @Schema(description = "主管部门意见陈述")
    private String opinionState;
    @Schema(description = "主管部门意见陈述人")
    private String opinionStateUser;
    @Schema(description = "主管部门意见陈述时间")
    private LocalDateTime opinionStateDate;
    @Schema(description = "事件总结(转发人填写)")
    private String eventConclusion;
    @Schema(description = "状态")
    private AdvEventsStatusEnum statusCode;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getEventReportId() {
        return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = eventReportId;
    }

    public String getOpinionOrgCode() {
        return opinionOrgCode;
    }

    public void setOpinionOrgCode(String opinionOrgCode) {
        this.opinionOrgCode = opinionOrgCode;
    }

    public String getOpinionState() {
        return opinionState;
    }

    public void setOpinionState(String opinionState) {
        this.opinionState = opinionState;
    }

    public String getOpinionStateUser() {
        return opinionStateUser;
    }

    public void setOpinionStateUser(String opinionStateUser) {
        this.opinionStateUser = opinionStateUser;
    }

    public LocalDateTime getOpinionStateDate() {
        return opinionStateDate;
    }

    public void setOpinionStateDate(LocalDateTime opinionStateDate) {
        this.opinionStateDate = opinionStateDate;
    }

    public String getEventConclusion() {
        return eventConclusion;
    }

    public void setEventConclusion(String eventConclusion) {
        this.eventConclusion = eventConclusion;
    }

    public AdvEventsStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(AdvEventsStatusEnum statusCode) {
        this.statusCode = statusCode;
    }
}