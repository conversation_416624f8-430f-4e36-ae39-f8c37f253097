package com.bjgoodwill.hip.ds.cis.adv.cosmetics.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.time.LocalDateTime;

@Schema(description = "化妆品不良反应事件报告表")
public class CisAdvEventCosmeticsNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -6610244185107004018L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "报告类型：serious严重；commonly一般")
    private String reportType;
    @Schema(description = "报告类型名称：serious严重；commonly一般")
    private String reportTypeName;
    @Schema(description = "报告单位类别：medical医疗机构；management经营企业；produce生产企业；personal个人；other其它")
    private String reportUnitCategory;
    @Schema(description = "报告单位类别：medical医疗机构；management经营企业名称；produce生产企业；personal个人；other其它")
    private String reportUnitCategoryName;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "民族")
    private String nation;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "联系电话")
    private String contactTel;
    @Schema(description = "通讯地址-居住省")
    private String czProvince;
    @Schema(description = "通讯地址-居住省")
    private String czProvinceName;
    @Schema(description = "通讯地址-居住市")
    private String czCity;
    @Schema(description = "通讯地址-居住市")
    private String czCityName;
    @Schema(description = "通讯地址-居住县（区）")
    private String czCounty;
    @Schema(description = "通讯地址-居住县（区）")
    private String czCountyName;
    @Schema(description = "通讯地址-居住乡（镇、街道）")
    private String czHomeJd;
    @Schema(description = "通讯地址-居住乡（镇、街道）")
    private String czHomeJdName;
    @Schema(description = "通讯地址-居住村")
    private String czVillage;
    @Schema(description = "通讯地址-居住村")
    private String czVillageName;
    @Schema(description = "有无化妆品过敏史1有2无3不详")
    private String isCosmeticsAllergy;
    @Schema(description = "化妆品过敏史具体")
    private String cosmeticsAllergyRemark;
    @Schema(description = "有无药品过敏史1有2无3不详")
    private String isDrugAllergy;
    @Schema(description = "药品过敏史具体")
    private String drugAllergyRemark;
    @Schema(description = "有无食物过敏史1有2无3不详")
    private String isFoodAllergy;
    @Schema(description = "食物过敏史具体")
    private String foodAllergyRemark;
    @Schema(description = "有无其他接触物过敏史1有2无3不详")
    private String isOtherAllergy;
    @Schema(description = "其他接触物过敏史具体")
    private String otherAllergyRemark;
    @Schema(description = "开始使用日期")
    private LocalDateTime beginDate;
    @Schema(description = "不良反应发生日期")
    private LocalDateTime eventDate;
    @Schema(description = "停用日期")
    private LocalDateTime endDate;
    @Schema(description = "潜伏期可疑化妆品开始/停止时间,1开始，0停止")
    private String eventProcess1;
    @Schema(description = "出现临床表现的时间差")
    private Integer eventProcess11;
    @Schema(description = "可疑化妆品开始/停止时间单位，hours小时，day 天，month月")
    private String eventProcess12;
    @Schema(description = "自觉症状,1瘙痒，2灼热感，3疼痛，4干燥，5紧绷感，6其他")
    private String eventProcess2;
    @Schema(description = "自觉症状其他")
    private String eventProcess21;
    @Schema(description = "皮损部位:1面部2头皮 3外耳廓 4颈部 5全身6胸部 7腹部 8背部9腋窝 10腹股沟11上肢 12下肢 13手部 14甲周 15甲板  16无   17其他")
    private String eventProcess3;
    @Schema(description = "皮损部位其他")
    private String eventProcess31;
    @Schema(description = "皮损形态: 1红斑  2丘疹 3斑块  4丘疱疹  5水肿6水疱  7粉刺8风团  9毛囊炎样  10毛细血管扩张 11色素沉着  12色素减退  13色素脱失 14毛发脱色  15毛发变脆  16毛发分叉  17毛发断裂  18毛发脱落19甲板变形  20甲板软化  21甲板剥离 22甲板脆裂  23甲周皮炎24伴糜烂   25渗出   26痂   27鳞屑   28苔藓样变   29萎缩   30抓痕   31无   32其他")
    private String eventProcess4;
    @Schema(description = "皮损形态其他")
    private String eventProcess41;
    @Schema(description = "其他损害：1神经系统  2全身性  3肾损害  4精神障碍  5无   6其他")
    private String eventProcess5;
    @Schema(description = "其他损害其他")
    private String eventProcess51;
    @Schema(description = "过程描述补充说明")
    private String eventProcessRemark;
    @Schema(description = "初步判断1化妆品接触性皮炎   2化妆品光感性皮炎  3化妆品皮肤色素异常   4化妆品痤疮 5化妆品唇炎5化妆品毛发损害6化妆品甲损害7化妆品荨麻疹 8激素依赖性皮炎  9其他")
    private String preliminaryDiag;
    @Schema(description = "初步判断其他")
    private String preliminaryDiagOther;
    @Schema(description = "事件结果:1痊愈；2好转；3未好转 4并发症，5其他")
    private String eventResult;
    @Schema(description = "事件结果:1痊愈；2好转；3未好转 4并发症，5其他")
    private String eventResultName;
    @Schema(description = "事件结果并发症")
    private String resultComplication;
    @Schema(description = "事件结果其他")
    private String resultOther;
    @Schema(description = "化妆品：1怀疑 2并用")
    private String useCosmetics;
    @Schema(description = "批准文号（备案号）")
    private String gbCode;
    @Schema(description = "化妆品名称")
    private String cosmeticsName;
    @Schema(description = "商标名")
    private String brandName;
    @Schema(description = "通用名")
    private String commonName;
    @Schema(description = "属性名")
    private String propertyName;
    @Schema(description = "类别：1特殊2普通 3育发类4染发类5烫发类6脱毛类7美乳类8健美类9除臭类10祛斑类11防晒类12发用类13护肤类14美容修饰类15香水类16洗发17护发18养发19固发20美发21膏22霜23乳液24化妆用油25面膜26化妆水类27胭脂香粉28唇膏（护唇膏、唇彩、口红）29洁肤类（沐浴液、洗手液）30眼部用彩妆（眉笔、眼线笔、睫毛膏）31指（趾）甲用化妆品32香水类")
    private String cosmeticsClass;
    @Schema(description = "类别：1特殊2普通 3育发类4染发类5烫发类6脱毛类7美乳类8健美类9除臭类10祛斑类11防晒类12发用类13护肤类14美容修饰类15香水类16洗发17护发18养发19固发20美发21膏22霜23乳液24化妆用油25面膜26化妆水类27胭脂香粉28唇膏（护唇膏、唇彩、口红）29洁肤类（沐浴液、洗手液）30眼部用彩妆（眉笔、眼线笔、睫毛膏）31指（趾）甲用化妆品32香水类")
    private String cosmeticsClassName;
    @Schema(description = "生产厂家")
    private String manufactureFirm;
    @Schema(description = "生产批号")
    private String batchNo;
    @Schema(description = "产品来源：1商场（超市、专柜） 2网购   3美容美发机构   4其他")
    private String productSource;
    @Schema(description = "产品来源：1商场（超市、专柜） 2网购   3美容美发机构   4其他")
    private String productSourceName;
    @Schema(description = "购买地点")
    private String buySite;
    @Schema(description = "化妆品有关斑贴试验0未做，1己做")
    private String patchTest1;
    @Schema(description = "化妆品斑贴试验己做：1原物斑贴实验2光斑贴试验")
    private String patchTest2;
    @Schema(description = "实验结果1阳性0阴性")
    private boolean patchTest3;
    @Schema(description = "阳性实验结果")
    private String patchTest4;
    @Schema(description = "欧标、澳标变应原系列斑贴试验：0未做，1己做")
    private boolean allergenPatchTest1;
    @Schema(description = "欧标、澳标变应原系列斑贴试验己做结果：1有呈阳性受试，0无呈阳性受试物质")
    private boolean allergenPatchTest2;
    @Schema(description = "1有呈阳性受试内容")
    private String allergenPatchTest3;
    @Schema(description = "其他辅助检查：1有2无3不详")
    private String otherAuxExam1;
    @Schema(description = "其他辅助检查（1有）名称")
    private String otherAuxExam2;
    @Schema(description = "其他辅助检查（1有）结果")
    private String otherAuxExam3;
    @Schema(description = "化妆品使用与不良反应出现有无合理的时间关系1有0无")
    private boolean relevanceEval1;
    @Schema(description = "停止使用化妆品后不良反应是否消失或减轻1是0否2不明")
    private String relevanceEval2;
    @Schema(description = "再次使用可疑化妆品是否再次出现同样反应1是0否2未再使用")
    private String relevanceEval3;
    @Schema(description = "不良反应是否可用其他接触物的作用，患者/消费者的病情进展解释1是0否")
    private String relevanceEval4;
    @Schema(description = "	斑贴试验结果是否可以说明化妆品使用与不良反应出现有明显的相关性1是0否2不明3未做")
    private String relevanceEval5;
    @Schema(description = "	报告人评价结果: 1肯定   2很可能  3可能  4可能无关  5待评价  6无法评价")
    private String reportRemark;
    @Schema(description = "报告人")
    private String reportUser;
    @Schema(description = "报告人名称")
    private String reportUserName;
    @Schema(description = "报告人联系电话")
    private String reportTel;
    @Schema(description = "报告时间")
    private LocalDateTime reportDate;
    @Schema(description = "报告人职业: 1医护人员  2美容美发师  3销售人员  4其他")
    private String reportUserWork;
    @Schema(description = "报告人职业名称: 1医护人员  2美容美发师  3销售人员 4其他")
    private String reportUserWorkName;
    @Schema(description = "报告单位名称")
    private String reportUnit;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "附件（汉字说明）")
    private String attachmentName;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @Size(max = 50, message = "不良事件id长度不能超过50个字符！")
    public String getEventReportId() {
        return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = StringUtils.trimToNull(eventReportId);
    }

    @Size(max = 16, message = "报告类型：serious严重；commonly一般长度不能超过16个字符！")
    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = StringUtils.trimToNull(reportType);
    }

    @Size(max = 16, message = "报告类型名称：serious严重；commonly一般长度不能超过16个字符！")
    public String getReportTypeName() {
        return reportTypeName;
    }

    public void setReportTypeName(String reportTypeName) {
        this.reportTypeName = StringUtils.trimToNull(reportTypeName);
    }

    @Size(max = 32, message = "报告单位类别：medical医疗机构；management经营企业；produce生产企业；personal个人；other其它长度不能超过32个字符！")
    public String getReportUnitCategory() {
        return reportUnitCategory;
    }

    public void setReportUnitCategory(String reportUnitCategory) {
        this.reportUnitCategory = StringUtils.trimToNull(reportUnitCategory);
    }

    @Size(max = 32, message = "报告单位类别：medical医疗机构；management经营企业名称；produce生产企业；personal个人；other其它长度不能超过32个字符！")
    public String getReportUnitCategoryName() {
        return reportUnitCategoryName;
    }

    public void setReportUnitCategoryName(String reportUnitCategoryName) {
        this.reportUnitCategoryName = StringUtils.trimToNull(reportUnitCategoryName);
    }

    @Size(max = 2, message = "患者类型长度不能超过2个字符！")
    public String getPatType() {
        return patType;
    }

    public void setPatType(String patType) {
        this.patType = StringUtils.trimToNull(patType);
    }

    @Size(max = 16, message = "住院号(门诊就诊卡号)长度不能超过16个字符！")
    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = StringUtils.trimToNull(inpatientCode);
    }

    @Size(max = 16, message = "就诊流水号长度不能超过16个字符！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @Size(max = 64, message = "患者姓名长度不能超过64个字符！")
    public String getPatName() {
        return patName;
    }

    public void setPatName(String patName) {
        this.patName = StringUtils.trimToNull(patName);
    }

    @Size(max = 16, message = "性别长度不能超过16个字符！")
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = StringUtils.trimToNull(sex);
    }

    @Size(max = 16, message = "民族长度不能超过16个字符！")
    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = StringUtils.trimToNull(nation);
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    @Size(max = 24, message = "联系电话长度不能超过24个字符！")
    public String getContactTel() {
        return contactTel;
    }

    public void setContactTel(String contactTel) {
        this.contactTel = StringUtils.trimToNull(contactTel);
    }

    @Size(max = 16, message = "通讯地址-居住省长度不能超过16个字符！")
    public String getCzProvince() {
        return czProvince;
    }

    public void setCzProvince(String czProvince) {
        this.czProvince = StringUtils.trimToNull(czProvince);
    }

    @Size(max = 32, message = "通讯地址-居住省长度不能超过32个字符！")
    public String getCzProvinceName() {
        return czProvinceName;
    }

    public void setCzProvinceName(String czProvinceName) {
        this.czProvinceName = StringUtils.trimToNull(czProvinceName);
    }

    @Size(max = 16, message = "通讯地址-居住市长度不能超过16个字符！")
    public String getCzCity() {
        return czCity;
    }

    public void setCzCity(String czCity) {
        this.czCity = StringUtils.trimToNull(czCity);
    }

    @Size(max = 32, message = "通讯地址-居住市长度不能超过32个字符！")
    public String getCzCityName() {
        return czCityName;
    }

    public void setCzCityName(String czCityName) {
        this.czCityName = StringUtils.trimToNull(czCityName);
    }

    @Size(max = 16, message = "通讯地址-居住县（区）长度不能超过16个字符！")
    public String getCzCounty() {
        return czCounty;
    }

    public void setCzCounty(String czCounty) {
        this.czCounty = StringUtils.trimToNull(czCounty);
    }

    @Size(max = 32, message = "通讯地址-居住县（区）长度不能超过32个字符！")
    public String getCzCountyName() {
        return czCountyName;
    }

    public void setCzCountyName(String czCountyName) {
        this.czCountyName = StringUtils.trimToNull(czCountyName);
    }

    @Size(max = 32, message = "通讯地址-居住乡（镇、街道）长度不能超过32个字符！")
    public String getCzHomeJd() {
        return czHomeJd;
    }

    public void setCzHomeJd(String czHomeJd) {
        this.czHomeJd = StringUtils.trimToNull(czHomeJd);
    }

    public String getCzHomeJdName() {
        return czHomeJdName;
    }

    public void setCzHomeJdName(String czHomeJdName) {
        this.czHomeJdName = StringUtils.trimToNull(czHomeJdName);
    }

    @Size(max = 32, message = "通讯地址-居住村长度不能超过32个字符！")
    public String getCzVillage() {
        return czVillage;
    }

    public void setCzVillage(String czVillage) {
        this.czVillage = StringUtils.trimToNull(czVillage);
    }

    public String getCzVillageName() {
        return czVillageName;
    }

    public void setCzVillageName(String czVillageName) {
        this.czVillageName = StringUtils.trimToNull(czVillageName);
    }

    @Size(max = 2, message = "有无化妆品过敏史1有2无3不详长度不能超过2个字符！")
    public String getIsCosmeticsAllergy() {
        return isCosmeticsAllergy;
    }

    public void setIsCosmeticsAllergy(String isCosmeticsAllergy) {
        this.isCosmeticsAllergy = StringUtils.trimToNull(isCosmeticsAllergy);
    }

    public String getCosmeticsAllergyRemark() {
        return cosmeticsAllergyRemark;
    }

    public void setCosmeticsAllergyRemark(String cosmeticsAllergyRemark) {
        this.cosmeticsAllergyRemark = StringUtils.trimToNull(cosmeticsAllergyRemark);
    }

    @Size(max = 2, message = "有无药品过敏史1有2无3不详长度不能超过2个字符！")
    public String getIsDrugAllergy() {
        return isDrugAllergy;
    }

    public void setIsDrugAllergy(String isDrugAllergy) {
        this.isDrugAllergy = StringUtils.trimToNull(isDrugAllergy);
    }

    public String getDrugAllergyRemark() {
        return drugAllergyRemark;
    }

    public void setDrugAllergyRemark(String drugAllergyRemark) {
        this.drugAllergyRemark = StringUtils.trimToNull(drugAllergyRemark);
    }

    @Size(max = 2, message = "有无食物过敏史1有2无3不详长度不能超过2个字符！")
    public String getIsFoodAllergy() {
        return isFoodAllergy;
    }

    public void setIsFoodAllergy(String isFoodAllergy) {
        this.isFoodAllergy = StringUtils.trimToNull(isFoodAllergy);
    }

    public String getFoodAllergyRemark() {
        return foodAllergyRemark;
    }

    public void setFoodAllergyRemark(String foodAllergyRemark) {
        this.foodAllergyRemark = StringUtils.trimToNull(foodAllergyRemark);
    }

    @Size(max = 2, message = "有无其他接触物过敏史1有2无3不详长度不能超过2个字符！")
    public String getIsOtherAllergy() {
        return isOtherAllergy;
    }

    public void setIsOtherAllergy(String isOtherAllergy) {
        this.isOtherAllergy = StringUtils.trimToNull(isOtherAllergy);
    }

    public String getOtherAllergyRemark() {
        return otherAllergyRemark;
    }

    public void setOtherAllergyRemark(String otherAllergyRemark) {
        this.otherAllergyRemark = StringUtils.trimToNull(otherAllergyRemark);
    }

    public LocalDateTime getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDateTime beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDateTime getEventDate() {
        return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
        this.eventDate = eventDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }

    public String getEventProcess1() {
        return eventProcess1;
    }

    public void setEventProcess1(String eventProcess1) {
        this.eventProcess1 = StringUtils.trimToNull(eventProcess1);
    }

    public Integer getEventProcess11() {
        return eventProcess11;
    }

    public void setEventProcess11(Integer eventProcess11) {
        this.eventProcess11 = eventProcess11;
    }

    @Size(max = 16, message = "可疑化妆品开始/停止时间单位，hours小时，day 天，month月长度不能超过16个字符！")
    public String getEventProcess12() {
        return eventProcess12;
    }

    public void setEventProcess12(String eventProcess12) {
        this.eventProcess12 = StringUtils.trimToNull(eventProcess12);
    }

    public String getEventProcess2() {
        return eventProcess2;
    }

    public void setEventProcess2(String eventProcess2) {
        this.eventProcess2 = StringUtils.trimToNull(eventProcess2);
    }

    public String getEventProcess21() {
        return eventProcess21;
    }

    public void setEventProcess21(String eventProcess21) {
        this.eventProcess21 = StringUtils.trimToNull(eventProcess21);
    }

    public String getEventProcess3() {
        return eventProcess3;
    }

    public void setEventProcess3(String eventProcess3) {
        this.eventProcess3 = StringUtils.trimToNull(eventProcess3);
    }

    public String getEventProcess31() {
        return eventProcess31;
    }

    public void setEventProcess31(String eventProcess31) {
        this.eventProcess31 = StringUtils.trimToNull(eventProcess31);
    }

    public String getEventProcess4() {
        return eventProcess4;
    }

    public void setEventProcess4(String eventProcess4) {
        this.eventProcess4 = StringUtils.trimToNull(eventProcess4);
    }

    public String getEventProcess41() {
        return eventProcess41;
    }

    public void setEventProcess41(String eventProcess41) {
        this.eventProcess41 = StringUtils.trimToNull(eventProcess41);
    }

    public String getEventProcess5() {
        return eventProcess5;
    }

    public void setEventProcess5(String eventProcess5) {
        this.eventProcess5 = StringUtils.trimToNull(eventProcess5);
    }

    public String getEventProcess51() {
        return eventProcess51;
    }

    public void setEventProcess51(String eventProcess51) {
        this.eventProcess51 = StringUtils.trimToNull(eventProcess51);
    }

    public String getEventProcessRemark() {
        return eventProcessRemark;
    }

    public void setEventProcessRemark(String eventProcessRemark) {
        this.eventProcessRemark = StringUtils.trimToNull(eventProcessRemark);
    }

    public String getPreliminaryDiag() {
        return preliminaryDiag;
    }

    public void setPreliminaryDiag(String preliminaryDiag) {
        this.preliminaryDiag = StringUtils.trimToNull(preliminaryDiag);
    }

    public String getPreliminaryDiagOther() {
        return preliminaryDiagOther;
    }

    public void setPreliminaryDiagOther(String preliminaryDiagOther) {
        this.preliminaryDiagOther = StringUtils.trimToNull(preliminaryDiagOther);
    }

    @Size(max = 1, message = "事件结果:1痊愈；2好转；3未好转 4并发症，5其他长度不能超过1个字符！")
    public String getEventResult() {
        return eventResult;
    }

    public void setEventResult(String eventResult) {
        this.eventResult = StringUtils.trimToNull(eventResult);
    }

    @Size(max = 16, message = "事件结果:1痊愈；2好转；3未好转 4并发症，5其他长度不能超过16个字符！")
    public String getEventResultName() {
        return eventResultName;
    }

    public void setEventResultName(String eventResultName) {
        this.eventResultName = StringUtils.trimToNull(eventResultName);
    }

    public String getResultComplication() {
        return resultComplication;
    }

    public void setResultComplication(String resultComplication) {
        this.resultComplication = StringUtils.trimToNull(resultComplication);
    }

    public String getResultOther() {
        return resultOther;
    }

    public void setResultOther(String resultOther) {
        this.resultOther = StringUtils.trimToNull(resultOther);
    }

    @Size(max = 2, message = "化妆品：1怀疑 2并用长度不能超过2个字符！")
    public String getUseCosmetics() {
        return useCosmetics;
    }

    public void setUseCosmetics(String useCosmetics) {
        this.useCosmetics = StringUtils.trimToNull(useCosmetics);
    }

    @Size(max = 128, message = "批准文号（备案号）长度不能超过128个字符！")
    public String getGbCode() {
        return gbCode;
    }

    public void setGbCode(String gbCode) {
        this.gbCode = StringUtils.trimToNull(gbCode);
    }

    public String getCosmeticsName() {
        return cosmeticsName;
    }

    public void setCosmeticsName(String cosmeticsName) {
        this.cosmeticsName = StringUtils.trimToNull(cosmeticsName);
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = StringUtils.trimToNull(brandName);
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = StringUtils.trimToNull(commonName);
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = StringUtils.trimToNull(propertyName);
    }

    public String getCosmeticsClass() {
        return cosmeticsClass;
    }

    public void setCosmeticsClass(String cosmeticsClass) {
        this.cosmeticsClass = StringUtils.trimToNull(cosmeticsClass);
    }

    public String getCosmeticsClassName() {
        return cosmeticsClassName;
    }

    public void setCosmeticsClassName(String cosmeticsClassName) {
        this.cosmeticsClassName = StringUtils.trimToNull(cosmeticsClassName);
    }

    public String getManufactureFirm() {
        return manufactureFirm;
    }

    public void setManufactureFirm(String manufactureFirm) {
        this.manufactureFirm = StringUtils.trimToNull(manufactureFirm);
    }

    @Size(max = 16, message = "生产批号长度不能超过16个字符！")
    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = StringUtils.trimToNull(batchNo);
    }

    @Size(max = 16, message = "产品来源：1商场（超市、专柜） 2网购   3美容美发机构   4其他长度不能超过16个字符！")
    public String getProductSource() {
        return productSource;
    }

    public void setProductSource(String productSource) {
        this.productSource = StringUtils.trimToNull(productSource);
    }

    @Size(max = 32, message = "产品来源：1商场（超市、专柜） 2网购   3美容美发机构   4其他长度不能超过32个字符！")
    public String getProductSourceName() {
        return productSourceName;
    }

    public void setProductSourceName(String productSourceName) {
        this.productSourceName = StringUtils.trimToNull(productSourceName);
    }

    public String getBuySite() {
        return buySite;
    }

    public void setBuySite(String buySite) {
        this.buySite = StringUtils.trimToNull(buySite);
    }

    @Size(max = 2, message = "化妆品有关斑贴试验0未做，1己做长度不能超过2个字符！")
    public String getPatchTest1() {
        return patchTest1;
    }

    public void setPatchTest1(String patchTest1) {
        this.patchTest1 = StringUtils.trimToNull(patchTest1);
    }

    @Size(max = 2, message = "化妆品斑贴试验己做：1原物斑贴实验2光斑贴试验长度不能超过2个字符！")
    public String getPatchTest2() {
        return patchTest2;
    }

    public void setPatchTest2(String patchTest2) {
        this.patchTest2 = StringUtils.trimToNull(patchTest2);
    }

    public boolean isPatchTest3() {
        return patchTest3;
    }

    public void setPatchTest3(boolean patchTest3) {
        this.patchTest3 = patchTest3;
    }

    public String getPatchTest4() {
        return patchTest4;
    }

    public void setPatchTest4(String patchTest4) {
        this.patchTest4 = StringUtils.trimToNull(patchTest4);
    }

    public boolean isAllergenPatchTest1() {
        return allergenPatchTest1;
    }

    public void setAllergenPatchTest1(boolean allergenPatchTest1) {
        this.allergenPatchTest1 = allergenPatchTest1;
    }

    public boolean isAllergenPatchTest2() {
        return allergenPatchTest2;
    }

    public void setAllergenPatchTest2(boolean allergenPatchTest2) {
        this.allergenPatchTest2 = allergenPatchTest2;
    }

    public String getAllergenPatchTest3() {
        return allergenPatchTest3;
    }

    public void setAllergenPatchTest3(String allergenPatchTest3) {
        this.allergenPatchTest3 = StringUtils.trimToNull(allergenPatchTest3);
    }

    @Size(max = 2, message = "其他辅助检查：1有2无3不详长度不能超过2个字符！")
    public String getOtherAuxExam1() {
        return otherAuxExam1;
    }

    public void setOtherAuxExam1(String otherAuxExam1) {
        this.otherAuxExam1 = StringUtils.trimToNull(otherAuxExam1);
    }

    public String getOtherAuxExam2() {
        return otherAuxExam2;
    }

    public void setOtherAuxExam2(String otherAuxExam2) {
        this.otherAuxExam2 = StringUtils.trimToNull(otherAuxExam2);
    }

    public String getOtherAuxExam3() {
        return otherAuxExam3;
    }

    public void setOtherAuxExam3(String otherAuxExam3) {
        this.otherAuxExam3 = StringUtils.trimToNull(otherAuxExam3);
    }

    public boolean isRelevanceEval1() {
        return relevanceEval1;
    }

    public void setRelevanceEval1(boolean relevanceEval1) {
        this.relevanceEval1 = relevanceEval1;
    }

    @Size(max = 2, message = "停止使用化妆品后不良反应是否消失或减轻1是0否2不明长度不能超过2个字符！")
    public String getRelevanceEval2() {
        return relevanceEval2;
    }

    public void setRelevanceEval2(String relevanceEval2) {
        this.relevanceEval2 = StringUtils.trimToNull(relevanceEval2);
    }

    @Size(max = 16, message = "再次使用可疑化妆品是否再次出现同样反应1是0否2未再使用长度不能超过16个字符！")
    public String getRelevanceEval3() {
        return relevanceEval3;
    }

    public void setRelevanceEval3(String relevanceEval3) {
        this.relevanceEval3 = StringUtils.trimToNull(relevanceEval3);
    }

    @Size(max = 16, message = "不良反应是否可用其他接触物的作用，患者/消费者的病情进展解释1是0否长度不能超过16个字符！")
    public String getRelevanceEval4() {
        return relevanceEval4;
    }

    public void setRelevanceEval4(String relevanceEval4) {
        this.relevanceEval4 = StringUtils.trimToNull(relevanceEval4);
    }

    @Size(max = 16, message = "	斑贴试验结果是否可以说明化妆品使用与不良反应出现有明显的相关性1是0否2不明3未做长度不能超过16个字符！")
    public String getRelevanceEval5() {
        return relevanceEval5;
    }

    public void setRelevanceEval5(String relevanceEval5) {
        this.relevanceEval5 = StringUtils.trimToNull(relevanceEval5);
    }

    @Size(max = 16, message = "	报告人评价结果: 1肯定   2很可能  3可能  4可能无关  5待评价  6无法评价长度不能超过16个字符！")
    public String getReportRemark() {
        return reportRemark;
    }

    public void setReportRemark(String reportRemark) {
        this.reportRemark = StringUtils.trimToNull(reportRemark);
    }

    @Size(max = 16, message = "报告人长度不能超过16个字符！")
    public String getReportUser() {
        return reportUser;
    }

    public void setReportUser(String reportUser) {
        this.reportUser = StringUtils.trimToNull(reportUser);
    }

    @Size(max = 32, message = "报告人名称长度不能超过32个字符！")
    public String getReportUserName() {
        return reportUserName;
    }

    public void setReportUserName(String reportUserName) {
        this.reportUserName = StringUtils.trimToNull(reportUserName);
    }

    @Size(max = 24, message = "报告人联系电话长度不能超过24个字符！")
    public String getReportTel() {
        return reportTel;
    }

    public void setReportTel(String reportTel) {
        this.reportTel = StringUtils.trimToNull(reportTel);
    }

    public LocalDateTime getReportDate() {
        return reportDate;
    }

    public void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    @Size(max = 16, message = "报告人职业: 1医护人员  2美容美发师  3销售人员  4其他长度不能超过16个字符！")
    public String getReportUserWork() {
        return reportUserWork;
    }

    public void setReportUserWork(String reportUserWork) {
        this.reportUserWork = StringUtils.trimToNull(reportUserWork);
    }

    @Size(max = 32, message = "报告人职业名称: 1医护人员  2美容美发师  3销售人员 4其他长度不能超过32个字符！")
    public String getReportUserWorkName() {
        return reportUserWorkName;
    }

    public void setReportUserWorkName(String reportUserWorkName) {
        this.reportUserWorkName = StringUtils.trimToNull(reportUserWorkName);
    }

    @Size(max = 64, message = "报告单位名称长度不能超过64个字符！")
    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = StringUtils.trimToNull(reportUnit);
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = StringUtils.trimToNull(remark);
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = StringUtils.trimToNull(attachmentName);
    }
}