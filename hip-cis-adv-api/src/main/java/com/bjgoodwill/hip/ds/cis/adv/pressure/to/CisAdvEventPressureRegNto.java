package com.bjgoodwill.hip.ds.cis.adv.pressure.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "医院获得性压力性损伤情况登记")
public class CisAdvEventPressureRegNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -1050884991916671520L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "压力性损伤id")
    private String pressureId;
    @Schema(description = "分期类型:a ⅰ期;bⅱ期；c ⅲ期；d ⅳ期；e不可分期；f可疑深部组织损伤；g粘膜压力性损伤；")
    private String stagesType;
    @Schema(description = "分期类型名称:a ⅰ期;bⅱ期；c ⅲ期；d ⅳ期；e不可分期；f可疑深部组织损伤；g粘膜压力性损伤；")
    private String stagesTypeName;
    @Schema(description = "其他病区带入压力性损伤")
    private boolean otherArea;
    @Schema(description = "带入损伤医疗器械相关压力性损伤")
    private boolean otherInjury;
    @Schema(description = "是否入本病区24小时")
    private boolean newArea;
    @Schema(description = "医疗器械相关压力性损伤1是；0否")
    private boolean newInjury;
    @Schema(description = "损伤部位骶尾椎骨 1是；0否")
    private boolean caudalVertebrae;
    @Schema(description = "损伤部位坐骨处	1是；0否")
    private boolean sciaticBone;
    @Schema(description = "损伤部位股骨粗隆处 1是；0否")
    private boolean femur;
    @Schema(description = "损伤部位跟骨处1是；0否")
    private boolean calcaneus;
    @Schema(description = "损伤部位足踝处1是；0否")
    private boolean ankle;
    @Schema(description = "损伤部位肩胛骨处	1是；0否")
    private boolean scapula;
    @Schema(description = "损伤部位枕骨处1是；0否")
    private boolean occipitalBone;
    @Schema(description = "损伤部位其他部位1是；0否")
    private boolean otherParts;
    @Schema(description = "损伤部位多处压力性	1是；0否")
    private boolean multiple;
    @Schema(description = "入本病区24小时后新发2期及以上院内压力性损伤部位数")
    private Integer newAreaNum;
    @Schema(description = "其中，医疗器械相关压力性损伤部位数")
    private Integer newInjuryNum;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @Size(max = 50, message = "压力性损伤id长度不能超过50个字符！")
    public String getPressureId() {
        return pressureId;
    }

    public void setPressureId(String pressureId) {
        this.pressureId = StringUtils.trimToNull(pressureId);
    }

    public String getStagesType() {
        return stagesType;
    }

    public void setStagesType(String stagesType) {
        this.stagesType = StringUtils.trimToNull(stagesType);
    }

    public String getStagesTypeName() {
        return stagesTypeName;
    }

    public void setStagesTypeName(String stagesTypeName) {
        this.stagesTypeName = StringUtils.trimToNull(stagesTypeName);
    }

    public boolean isOtherArea() {
        return otherArea;
    }

    public void setOtherArea(boolean otherArea) {
        this.otherArea = otherArea;
    }

    public boolean isOtherInjury() {
        return otherInjury;
    }

    public void setOtherInjury(boolean otherInjury) {
        this.otherInjury = otherInjury;
    }

    public boolean isNewArea() {
        return newArea;
    }

    public void setNewArea(boolean newArea) {
        this.newArea = newArea;
    }

    public boolean isNewInjury() {
        return newInjury;
    }

    public void setNewInjury(boolean newInjury) {
        this.newInjury = newInjury;
    }

    public boolean isCaudalVertebrae() {
        return caudalVertebrae;
    }

    public void setCaudalVertebrae(boolean caudalVertebrae) {
        this.caudalVertebrae = caudalVertebrae;
    }

    public boolean isSciaticBone() {
        return sciaticBone;
    }

    public void setSciaticBone(boolean sciaticBone) {
        this.sciaticBone = sciaticBone;
    }

    public boolean isFemur() {
        return femur;
    }

    public void setFemur(boolean femur) {
        this.femur = femur;
    }

    public boolean isCalcaneus() {
        return calcaneus;
    }

    public void setCalcaneus(boolean calcaneus) {
        this.calcaneus = calcaneus;
    }

    public boolean isAnkle() {
        return ankle;
    }

    public void setAnkle(boolean ankle) {
        this.ankle = ankle;
    }

    public boolean isScapula() {
        return scapula;
    }

    public void setScapula(boolean scapula) {
        this.scapula = scapula;
    }

    public boolean isOccipitalBone() {
        return occipitalBone;
    }

    public void setOccipitalBone(boolean occipitalBone) {
        this.occipitalBone = occipitalBone;
    }

    public boolean isOtherParts() {
        return otherParts;
    }

    public void setOtherParts(boolean otherParts) {
        this.otherParts = otherParts;
    }

    public boolean isMultiple() {
        return multiple;
    }

    public void setMultiple(boolean multiple) {
        this.multiple = multiple;
    }

    public Integer getNewAreaNum() {
        return newAreaNum;
    }

    public void setNewAreaNum(Integer newAreaNum) {
        this.newAreaNum = newAreaNum;
    }

    public Integer getNewInjuryNum() {
        return newInjuryNum;
    }

    public void setNewInjuryNum(Integer newInjuryNum) {
        this.newInjuryNum = newInjuryNum;
    }
}