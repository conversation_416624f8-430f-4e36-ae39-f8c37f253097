package com.bjgoodwill.hip.ds.cis.adv.pressure.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医院获得性压力性损伤情况登记")
public class CisAdvEventPressureRegQto extends BaseQto implements Serializable {

	@Serial
    private static final long serialVersionUID = -7059761948240421922L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "压力性损伤id")
    private String pressureId;
    @Schema(description = "分期类型:a ⅰ期;bⅱ期；c ⅲ期；d ⅳ期；e不可分期；f可疑深部组织损伤；g粘膜压力性损伤；")
    private String stagesType;
    @Schema(description = "分期类型名称:a ⅰ期;bⅱ期；c ⅲ期；d ⅳ期；e不可分期；f可疑深部组织损伤；g粘膜压力性损伤；")
    private String stagesTypeName;
    @Schema(description = "其他病区带入压力性损伤")
    private Boolean otherArea;
    @Schema(description = "带入损伤医疗器械相关压力性损伤")
    private Boolean otherInjury;
    @Schema(description = "是否入本病区24小时")
    private Boolean newArea;
    @Schema(description = "医疗器械相关压力性损伤1是；0否")
    private Boolean newInjury;
    @Schema(description = "损伤部位骶尾椎骨 1是；0否")
    private Boolean caudalVertebrae;
    @Schema(description = "损伤部位坐骨处	1是；0否")
    private Boolean sciaticBone;
    @Schema(description = "损伤部位股骨粗隆处 1是；0否")
    private Boolean femur;
    @Schema(description = "损伤部位跟骨处1是；0否")
    private Boolean calcaneus;
    @Schema(description = "损伤部位足踝处1是；0否")
    private Boolean ankle;
    @Schema(description = "损伤部位肩胛骨处	1是；0否")
    private Boolean scapula;
    @Schema(description = "损伤部位枕骨处1是；0否")
    private Boolean occipitalBone;
    @Schema(description = "损伤部位其他部位1是；0否")
    private Boolean otherParts;
    @Schema(description = "损伤部位多处压力性	1是；0否")
    private Boolean multiple;
    @Schema(description = "入本病区24小时后新发2期及以上院内压力性损伤部位数")
    private Integer newAreaNum;
    @Schema(description = "其中，医疗器械相关压力性损伤部位数")
    private Integer newInjuryNum;

    public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

    public String getPressureId() {
    	return pressureId;
    }

    public void setPressureId(String pressureId) {
    	this.pressureId = pressureId;
    }

    public String getStagesType() {
    	return stagesType;
    }

    public void setStagesType(String stagesType) {
    	this.stagesType = stagesType;
    }

    public String getStagesTypeName() {
    	return stagesTypeName;
    }

    public void setStagesTypeName(String stagesTypeName) {
    	this.stagesTypeName = stagesTypeName;
    }

    public Boolean getOtherArea() {
    	return otherArea;
    }

    public void setOtherArea(Boolean otherArea) {
    	this.otherArea = otherArea;
    }

    public Boolean getOtherInjury() {
    	return otherInjury;
    }

    public void setOtherInjury(Boolean otherInjury) {
    	this.otherInjury = otherInjury;
    }

    public Boolean getNewArea() {
    	return newArea;
    }

    public void setNewArea(Boolean newArea) {
    	this.newArea = newArea;
    }

    public Boolean getNewInjury() {
    	return newInjury;
    }

    public void setNewInjury(Boolean newInjury) {
    	this.newInjury = newInjury;
    }

    public Boolean getCaudalVertebrae() {
    	return caudalVertebrae;
    }

    public void setCaudalVertebrae(Boolean caudalVertebrae) {
    	this.caudalVertebrae = caudalVertebrae;
    }

    public Boolean getSciaticBone() {
    	return sciaticBone;
    }

    public void setSciaticBone(Boolean sciaticBone) {
    	this.sciaticBone = sciaticBone;
    }

    public Boolean getFemur() {
    	return femur;
    }

    public void setFemur(Boolean femur) {
    	this.femur = femur;
    }

    public Boolean getCalcaneus() {
    	return calcaneus;
    }

    public void setCalcaneus(Boolean calcaneus) {
    	this.calcaneus = calcaneus;
    }

    public Boolean getAnkle() {
    	return ankle;
    }

    public void setAnkle(Boolean ankle) {
    	this.ankle = ankle;
    }

    public Boolean getScapula() {
    	return scapula;
    }

    public void setScapula(Boolean scapula) {
    	this.scapula = scapula;
    }

    public Boolean getOccipitalBone() {
    	return occipitalBone;
    }

    public void setOccipitalBone(Boolean occipitalBone) {
    	this.occipitalBone = occipitalBone;
    }

    public Boolean getOtherParts() {
    	return otherParts;
    }

    public void setOtherParts(Boolean otherParts) {
    	this.otherParts = otherParts;
    }

    public Boolean getMultiple() {
    	return multiple;
    }

    public void setMultiple(Boolean multiple) {
    	this.multiple = multiple;
    }

    public Integer getNewAreaNum() {
    	return newAreaNum;
    }

    public void setNewAreaNum(Integer newAreaNum) {
    	this.newAreaNum = newAreaNum;
    }

    public Integer getNewInjuryNum() {
    	return newInjuryNum;
    }

    public void setNewInjuryNum(Integer newInjuryNum) {
    	this.newInjuryNum = newInjuryNum;
    }
}