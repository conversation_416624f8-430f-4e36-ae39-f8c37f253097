package com.bjgoodwill.hip.ds.cis.adv.vap.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapEto;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapNto;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapQto;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "呼吸机相关肺炎（VAP）相关信息收集领域服务", description = "呼吸机相关肺炎（VAP）相关信息收集领域服务")
public interface CisAdvEventVapService {

    @Operation(summary = "根据查询条件对呼吸机相关肺炎（VAP）相关信息收集进行查询。")
    @GetMapping("/cisAdvEventVaps")
    List<CisAdvEventVapTo> getCisAdvEventVaps(@ParameterObject @SpringQueryMap CisAdvEventVapQto cisAdvEventVapQto);

    @Operation(summary = "根据查询条件对呼吸机相关肺炎（VAP）相关信息收集进行分页查询。")
    @GetMapping("/cisAdvEventVaps/pages")
    GridResultSet<CisAdvEventVapTo> getCisAdvEventVapPage(@ParameterObject @SpringQueryMap CisAdvEventVapQto cisAdvEventVapQto);

    @Operation(summary = "根据唯一标识返回呼吸机相关肺炎（VAP）相关信息收集。")
    @GetMapping("/cisAdvEventVaps/{id:.+}")
    CisAdvEventVapTo getCisAdvEventVapById(@PathVariable("id") String id);

    @Operation(summary = "创建呼吸机相关肺炎（VAP）相关信息收集。")
    @PostMapping("/cisAdvEventVaps")
    CisAdvEventVapTo createCisAdvEventVap(@RequestBody @Valid CisAdvEventVapNto cisAdvEventVapNto);

    @Operation(summary = "根据唯一标识修改呼吸机相关肺炎（VAP）相关信息收集。")
    @PutMapping("/cisAdvEventVaps/{id:.+}")
    void updateCisAdvEventVap(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventVapEto cisAdvEventVapEto);

    @Operation(summary = "根据唯一标识删除呼吸机相关肺炎（VAP）相关信息收集。")
    @DeleteMapping("/cisAdvEventVaps/{id:.+}")
    void deleteCisAdvEventVap(@PathVariable("id") String id);

}