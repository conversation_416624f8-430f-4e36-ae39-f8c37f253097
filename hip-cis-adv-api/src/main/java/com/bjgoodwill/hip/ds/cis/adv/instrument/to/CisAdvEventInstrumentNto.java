package com.bjgoodwill.hip.ds.cis.adv.instrument.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "医疗器械不良事件报告")
public class CisAdvEventInstrumentNto  extends BaseNto implements Serializable {

	@Serial
    private static final long serialVersionUID = -851997703089439540L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区科室名称")
    private String areaName;
    @Schema(description = "既往病史")
    private String pastValue;
    @Schema(description = "产品名称")
    private String instrumentName;
    @Schema(description = "注册证编号")
    private String instrumentTrademark;
    @Schema(description = "型号规格")
    private String instrumentModel;
    @Schema(description = "产品批号")
    private String instrumentBatchNo;
    @Schema(description = "产品编号")
    private String instrumentNo;
    @Schema(description = "udi器械识别码")
    private String instrumentUdi;
    @Schema(description = "生产日期")
    private LocalDateTime manufactureDate;
    @Schema(description = "有效期至")
    private LocalDateTime validUntil;
    @Schema(description = "生产企业")
    private String sourceInformation;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "发现或获知日期")
    private LocalDateTime knowledgeDate;
    @Schema(description = "伤害程度:death死亡；serious严重伤害；other其他；")
    private String injuryDegree;
    @Schema(description = "伤害程度名称")
    private String injuryDegreeName;
    @Schema(description = "伤害表现")
    private String injuryPerformance;
    @Schema(description = "器械故障表现")
    private String faultPerformance;
    @Schema(description = "预期治疗疾病或作用")
    private String treatmentEffect;
    @Schema(description = "器械使用日期")
    private LocalDateTime useDate;
    @Schema(description = "使用场所:medical医疗机构；family家庭；other其他")
    private String usePlace;
    @Schema(description = "使用场所名称")
    private String usePlaceName;
    @Schema(description = "使用过程")
    private String useProcess;
    @Schema(description = "合并用药器械说明")
    private String useCombined;
    @Schema(description = "事件原因分析:product产品原因；operation操作原因；oneself患者自身原因；undetermined无法确定；")
    private String eventReasonsType;
    @Schema(description = "事件原因分析名称")
    private String eventReasonsTypeName;
    @Schema(description = "事件原因分析描述")
    private String eventReasonsDescribe;
    @Schema(description = "初步处置情况")
    private String disposalSituation;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
    	return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @Size(max = 50, message = "不良事件id长度不能超过50个字符！")
    public String getEventReportId() {
    	return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = StringUtils.trimToNull(eventReportId);
    }

    @Size(max = 2, message = "患者类型长度不能超过2个字符！")
    public String getPatType() {
    	return patType;
    }

    public void setPatType(String patType) {
        this.patType = StringUtils.trimToNull(patType);
    }

    @Size(max = 16, message = "住院号(门诊就诊卡号)长度不能超过16个字符！")
    public String getInpatientCode() {
    	return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = StringUtils.trimToNull(inpatientCode);
    }

    @Size(max = 16, message = "就诊流水号长度不能超过16个字符！")
    public String getVisitCode() {
    	return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @Size(max = 64, message = "患者姓名长度不能超过64个字符！")
    public String getPatName() {
    	return patName;
    }

    public void setPatName(String patName) {
        this.patName = StringUtils.trimToNull(patName);
    }

    @Size(max = 16, message = "性别长度不能超过16个字符！")
    public String getSex() {
    	return sex;
    }

    public void setSex(String sex) {
        this.sex = StringUtils.trimToNull(sex);
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    @Size(max = 16, message = "病区科室长度不能超过16个字符！")
    public String getAreaCode() {
    	return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = StringUtils.trimToNull(areaCode);
    }

    @Size(max = 32, message = "病区科室名称长度不能超过32个字符！")
    public String getAreaName() {
    	return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = StringUtils.trimToNull(areaName);
    }

    public String getPastValue() {
    	return pastValue;
    }

    public void setPastValue(String pastValue) {
        this.pastValue = StringUtils.trimToNull(pastValue);
    }

    public String getInstrumentName() {
    	return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
        this.instrumentName = StringUtils.trimToNull(instrumentName);
    }

    @Size(max = 64, message = "注册证编号长度不能超过64个字符！")
    public String getInstrumentTrademark() {
    	return instrumentTrademark;
    }

    public void setInstrumentTrademark(String instrumentTrademark) {
        this.instrumentTrademark = StringUtils.trimToNull(instrumentTrademark);
    }

    @Size(max = 64, message = "型号规格长度不能超过64个字符！")
    public String getInstrumentModel() {
    	return instrumentModel;
    }

    public void setInstrumentModel(String instrumentModel) {
        this.instrumentModel = StringUtils.trimToNull(instrumentModel);
    }

    @Size(max = 64, message = "产品批号长度不能超过64个字符！")
    public String getInstrumentBatchNo() {
    	return instrumentBatchNo;
    }

    public void setInstrumentBatchNo(String instrumentBatchNo) {
        this.instrumentBatchNo = StringUtils.trimToNull(instrumentBatchNo);
    }

    @Size(max = 64, message = "产品编号长度不能超过64个字符！")
    public String getInstrumentNo() {
    	return instrumentNo;
    }

    public void setInstrumentNo(String instrumentNo) {
        this.instrumentNo = StringUtils.trimToNull(instrumentNo);
    }

    @Size(max = 64, message = "udi器械识别码长度不能超过64个字符！")
    public String getInstrumentUdi() {
    	return instrumentUdi;
    }

    public void setInstrumentUdi(String instrumentUdi) {
        this.instrumentUdi = StringUtils.trimToNull(instrumentUdi);
    }

    public LocalDateTime getManufactureDate() {
    	return manufactureDate;
    }

    public void setManufactureDate(LocalDateTime manufactureDate) {
        this.manufactureDate = manufactureDate;
    }

    public LocalDateTime getValidUntil() {
    	return validUntil;
    }

    public void setValidUntil(LocalDateTime validUntil) {
        this.validUntil = validUntil;
    }

    public String getSourceInformation() {
    	return sourceInformation;
    }

    public void setSourceInformation(String sourceInformation) {
        this.sourceInformation = StringUtils.trimToNull(sourceInformation);
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
        this.eventDate = eventDate;
    }

    public LocalDateTime getKnowledgeDate() {
    	return knowledgeDate;
    }

    public void setKnowledgeDate(LocalDateTime knowledgeDate) {
        this.knowledgeDate = knowledgeDate;
    }

    @Size(max = 16, message = "伤害程度:death死亡；serious严重伤害；other其他；长度不能超过16个字符！")
    public String getInjuryDegree() {
    	return injuryDegree;
    }

    public void setInjuryDegree(String injuryDegree) {
        this.injuryDegree = StringUtils.trimToNull(injuryDegree);
    }

    @Size(max = 32, message = "伤害程度名称长度不能超过32个字符！")
    public String getInjuryDegreeName() {
    	return injuryDegreeName;
    }

    public void setInjuryDegreeName(String injuryDegreeName) {
        this.injuryDegreeName = StringUtils.trimToNull(injuryDegreeName);
    }

    public String getInjuryPerformance() {
    	return injuryPerformance;
    }

    public void setInjuryPerformance(String injuryPerformance) {
        this.injuryPerformance = StringUtils.trimToNull(injuryPerformance);
    }

    public String getFaultPerformance() {
    	return faultPerformance;
    }

    public void setFaultPerformance(String faultPerformance) {
        this.faultPerformance = StringUtils.trimToNull(faultPerformance);
    }

    public String getTreatmentEffect() {
    	return treatmentEffect;
    }

    public void setTreatmentEffect(String treatmentEffect) {
        this.treatmentEffect = StringUtils.trimToNull(treatmentEffect);
    }

    public LocalDateTime getUseDate() {
    	return useDate;
    }

    public void setUseDate(LocalDateTime useDate) {
        this.useDate = useDate;
    }

    @Size(max = 16, message = "使用场所:medical医疗机构；family家庭；other其他长度不能超过16个字符！")
    public String getUsePlace() {
    	return usePlace;
    }

    public void setUsePlace(String usePlace) {
        this.usePlace = StringUtils.trimToNull(usePlace);
    }

    @Size(max = 32, message = "使用场所名称长度不能超过32个字符！")
    public String getUsePlaceName() {
    	return usePlaceName;
    }

    public void setUsePlaceName(String usePlaceName) {
        this.usePlaceName = StringUtils.trimToNull(usePlaceName);
    }

    public String getUseProcess() {
    	return useProcess;
    }

    public void setUseProcess(String useProcess) {
        this.useProcess = StringUtils.trimToNull(useProcess);
    }

    public String getUseCombined() {
    	return useCombined;
    }

    public void setUseCombined(String useCombined) {
        this.useCombined = StringUtils.trimToNull(useCombined);
    }

    @Size(max = 16, message = "事件原因分析:product产品原因；operation操作原因；oneself患者自身原因；undetermined无法确定；长度不能超过16个字符！")
    public String getEventReasonsType() {
    	return eventReasonsType;
    }

    public void setEventReasonsType(String eventReasonsType) {
        this.eventReasonsType = StringUtils.trimToNull(eventReasonsType);
    }

    @Size(max = 32, message = "事件原因分析名称长度不能超过32个字符！")
    public String getEventReasonsTypeName() {
    	return eventReasonsTypeName;
    }

    public void setEventReasonsTypeName(String eventReasonsTypeName) {
        this.eventReasonsTypeName = StringUtils.trimToNull(eventReasonsTypeName);
    }

    public String getEventReasonsDescribe() {
    	return eventReasonsDescribe;
    }

    public void setEventReasonsDescribe(String eventReasonsDescribe) {
        this.eventReasonsDescribe = StringUtils.trimToNull(eventReasonsDescribe);
    }

    public String getDisposalSituation() {
    	return disposalSituation;
    }

    public void setDisposalSituation(String disposalSituation) {
        this.disposalSituation = StringUtils.trimToNull(disposalSituation);
    }
}