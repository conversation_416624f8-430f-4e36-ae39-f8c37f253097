package com.bjgoodwill.hip.ds.cis.adv.nursing.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.time.LocalDateTime;

@Schema(description = "护理不良事件报告单")
public class CisAdvEventNursingQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -5668555827800965216L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区科室名称")
    private String areaName;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "不良事件分级:0级：事件已发生，但在执行前被制止；1级：事件已发生，但未造成伤害；2级：轻微伤害，生命体征无变化，需进行临床观察及一般处理；3级：中度伤害，部分生命体征有改变，需进一步临床观察及对症处理；4级：重度伤害，生命体征改变明显，需提升护理级别及紧急处理；5级：永久性功能丧失；6级：死亡")
    private String eventLevel;
    @Schema(description = "不良事件分级:0级：事件已发生，但在执行前被制止；1级：事件已发生，但未造成伤害；2级：轻微伤害，生命体征无变化，需进行临床观察及一般处理；3级：中度伤害，部分生命体征有改变，需进一步临床观察及对症处理；4级：重度伤害，生命体征改变明显，需提升护理级别及紧急处理；5级：永久性功能丧失；6级：死亡")
    private String eventLevelName;
    @Schema(description = "给药缺陷事件：time给药时间错误；usage给药途径错误；speed输液速度错误；leakage漏给药；date给药日期错误；dosage计量错误；dosage form剂型错误；drug药物错误；uncomply未遵医嘱给药；other其他")
    private String eventDrugDefect;
    @Schema(description = "给药缺陷事件：time给药时间错误；usage给药途径错误；speed输液速度错误；leakage漏给药；date给药日期错误；dosage计量错误；dosage form剂型错误；drug药物错误；uncomply未遵医嘱给药；other其他")
    private String eventDrugDefectName;
    @Schema(description = "其它缺陷")
    private String defectOther;
    @Schema(description = "制度执行：handover交接班制度；check查对制度；drug给药制度；other其他")
    private String execSystem;
    @Schema(description = "制度执行名称：handover交接班制度；check查对制度；drug给药制度；other其他")
    private String execSystemName;
    @Schema(description = "其它制度执行")
    private String systemOther;
    @Schema(description = "压疮事件:unpredictable不可预知的院内压疮; other其他")
    private String pressureSoreType;
    @Schema(description = "压疮事件名称:unpredictable不可预知的院内压疮; other其他")
    private String pressureSoreTypeName;
    @Schema(description = "其他压疮事件")
    private String pressureSoreOther;
    @Schema(description = "跌倒事件：bed床旁跌倒；corridor走廊跌倒；toilet卫生间跌倒；other其他")
    private String eventFall;
    @Schema(description = "跌倒事件：bed床旁跌倒；corridor走廊跌倒；toilet卫生间跌倒；other其他")
    private String eventFallName;
    @Schema(description = "其他跌倒事件")
    private String eventFallOther;
    @Schema(description = "坠床事件:shelter使用床档；unshelter未使用床档；sober清醒状态；unsober不清醒状态；other其他")
    private String fallingBed;
    @Schema(description = "其他坠床事件")
    private String fallingBedOther;
    @Schema(description = "管路滑脱事件:oneself患者自拔；slippage与操作无关的滑脱；nurseslippage医护人员操作时滑脱；familyslippage家属协助患者时滑脱；other其他")
    private String eventSlippage;
    @Schema(description = "其他管路滑脱事件")
    private String eventSlippageOther;
    @Schema(description = "意外伤害事件:scald烧烫伤；aspiration误吸；suicide自杀或自伤；other其他")
    private String eventHurt;
    @Schema(description = "其他意外伤害事件")
    private String eventHurtOther;
    @Schema(description = "输血事件: commonlyreaction一般输血反应；seriousreaction严重输血反应；other其他；")
    private String eventBlood;
    @Schema(description = "其他输血事件")
    private String eventBloodOther;
    @Schema(description = "身份识别错误事：order医嘱相关；operation操作相关;other其他；")
    private String eventIdentityErr;
    @Schema(description = "其他身份识别错误事")
    private String eventIdentityOther;
    @Schema(description = "其他：suicide自杀倾向；operation操作失误；other其他")
    private String eventOther;
    @Schema(description = "其他内容")
    private String otherRemark;
    @Schema(description = "责任者姓名")
    private String dutyUser;
    @Schema(description = "责任者职称")
    private String dutyTitle;
    @Schema(description = "责任者年龄")
    private String dutyAge;
    @Schema(description = "责任者工作年限：a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    private String workingLife;
    @Schema(description = "事件经过及补救措施")
    private String eventProcess;
    @Schema(description = "责任者工作年限名称：a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    private String workingLifeName;
    @Schema(description = "科室讨论分析")
    private String deptAnalysis;
    @Schema(description = "科室处理意见")
    private String deptOpinion;
    @Schema(description = "整改措施")
    private String improvementMeasures;
    @Schema(description = "护士长签名")
    private String headSignature;
    @Schema(description = "护士长签名名称")
    private String headSignatureName;
    @Schema(description = "护士长填写时间")
    private LocalDateTime headSignatureDate;
    @Schema(description = "护理部意见")
    private String nursDeptOpinion;
    @Schema(description = "护理部签名")
    private String nursDeptSignature;
    @Schema(description = "护理部签名名称")
    private String nursDeptSignatureName;
    @Schema(description = "护理部填写时间")
    private LocalDateTime deptSignatureDate;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getEventReportId() {
        return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = eventReportId;
    }

    public String getPatType() {
        return patType;
    }

    public void setPatType(String patType) {
        this.patType = patType;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatName() {
        return patName;
    }

    public void setPatName(String patName) {
        this.patName = patName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public LocalDateTime getEventDate() {
        return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
        this.eventDate = eventDate;
    }

    public String getEventLevel() {
        return eventLevel;
    }

    public void setEventLevel(String eventLevel) {
        this.eventLevel = eventLevel;
    }

    public String getEventLevelName() {
        return eventLevelName;
    }

    public void setEventLevelName(String eventLevelName) {
        this.eventLevelName = eventLevelName;
    }

    public String getEventDrugDefect() {
        return eventDrugDefect;
    }

    public void setEventDrugDefect(String eventDrugDefect) {
        this.eventDrugDefect = eventDrugDefect;
    }

    public String getEventDrugDefectName() {
        return eventDrugDefectName;
    }

    public void setEventDrugDefectName(String eventDrugDefectName) {
        this.eventDrugDefectName = eventDrugDefectName;
    }

    public String getDefectOther() {
        return defectOther;
    }

    public void setDefectOther(String defectOther) {
        this.defectOther = defectOther;
    }

    public String getExecSystem() {
        return execSystem;
    }

    public void setExecSystem(String execSystem) {
        this.execSystem = execSystem;
    }

    public String getExecSystemName() {
        return execSystemName;
    }

    public void setExecSystemName(String execSystemName) {
        this.execSystemName = execSystemName;
    }

    public String getSystemOther() {
        return systemOther;
    }

    public void setSystemOther(String systemOther) {
        this.systemOther = systemOther;
    }

    public String getPressureSoreType() {
        return pressureSoreType;
    }

    public void setPressureSoreType(String pressureSoreType) {
        this.pressureSoreType = pressureSoreType;
    }

    public String getPressureSoreTypeName() {
        return pressureSoreTypeName;
    }

    public void setPressureSoreTypeName(String pressureSoreTypeName) {
        this.pressureSoreTypeName = pressureSoreTypeName;
    }

    public String getPressureSoreOther() {
        return pressureSoreOther;
    }

    public void setPressureSoreOther(String pressureSoreOther) {
        this.pressureSoreOther = pressureSoreOther;
    }

    public String getEventFall() {
        return eventFall;
    }

    public void setEventFall(String eventFall) {
        this.eventFall = eventFall;
    }

    public String getEventFallName() {
        return eventFallName;
    }

    public void setEventFallName(String eventFallName) {
        this.eventFallName = eventFallName;
    }

    public String getEventFallOther() {
        return eventFallOther;
    }

    public void setEventFallOther(String eventFallOther) {
        this.eventFallOther = eventFallOther;
    }

    public String getFallingBed() {
        return fallingBed;
    }

    public void setFallingBed(String fallingBed) {
        this.fallingBed = fallingBed;
    }

    public String getFallingBedOther() {
        return fallingBedOther;
    }

    public void setFallingBedOther(String fallingBedOther) {
        this.fallingBedOther = fallingBedOther;
    }

    public String getEventSlippage() {
        return eventSlippage;
    }

    public void setEventSlippage(String eventSlippage) {
        this.eventSlippage = eventSlippage;
    }

    public String getEventSlippageOther() {
        return eventSlippageOther;
    }

    public void setEventSlippageOther(String eventSlippageOther) {
        this.eventSlippageOther = eventSlippageOther;
    }

    public String getEventHurt() {
        return eventHurt;
    }

    public void setEventHurt(String eventHurt) {
        this.eventHurt = eventHurt;
    }

    public String getEventHurtOther() {
        return eventHurtOther;
    }

    public void setEventHurtOther(String eventHurtOther) {
        this.eventHurtOther = eventHurtOther;
    }

    public String getEventBlood() {
        return eventBlood;
    }

    public void setEventBlood(String eventBlood) {
        this.eventBlood = eventBlood;
    }

    public String getEventBloodOther() {
        return eventBloodOther;
    }

    public void setEventBloodOther(String eventBloodOther) {
        this.eventBloodOther = eventBloodOther;
    }

    public String getEventIdentityErr() {
        return eventIdentityErr;
    }

    public void setEventIdentityErr(String eventIdentityErr) {
        this.eventIdentityErr = eventIdentityErr;
    }

    public String getEventIdentityOther() {
        return eventIdentityOther;
    }

    public void setEventIdentityOther(String eventIdentityOther) {
        this.eventIdentityOther = eventIdentityOther;
    }

    public String getEventOther() {
        return eventOther;
    }

    public void setEventOther(String eventOther) {
        this.eventOther = eventOther;
    }

    public String getOtherRemark() {
        return otherRemark;
    }

    public void setOtherRemark(String otherRemark) {
        this.otherRemark = otherRemark;
    }

    public String getDutyUser() {
        return dutyUser;
    }

    public void setDutyUser(String dutyUser) {
        this.dutyUser = dutyUser;
    }

    public String getDutyTitle() {
        return dutyTitle;
    }

    public void setDutyTitle(String dutyTitle) {
        this.dutyTitle = dutyTitle;
    }

    public String getDutyAge() {
        return dutyAge;
    }

    public void setDutyAge(String dutyAge) {
        this.dutyAge = dutyAge;
    }

    public String getWorkingLife() {
        return workingLife;
    }

    public void setWorkingLife(String workingLife) {
        this.workingLife = workingLife;
    }

    public String getEventProcess() {
        return eventProcess;
    }

    public void setEventProcess(String eventProcess) {
        this.eventProcess = eventProcess;
    }

    public String getWorkingLifeName() {
        return workingLifeName;
    }

    public void setWorkingLifeName(String workingLifeName) {
        this.workingLifeName = workingLifeName;
    }

    public String getDeptAnalysis() {
        return deptAnalysis;
    }

    public void setDeptAnalysis(String deptAnalysis) {
        this.deptAnalysis = deptAnalysis;
    }

    public String getDeptOpinion() {
        return deptOpinion;
    }

    public void setDeptOpinion(String deptOpinion) {
        this.deptOpinion = deptOpinion;
    }

    public String getImprovementMeasures() {
        return improvementMeasures;
    }

    public void setImprovementMeasures(String improvementMeasures) {
        this.improvementMeasures = improvementMeasures;
    }

    public String getHeadSignature() {
        return headSignature;
    }

    public void setHeadSignature(String headSignature) {
        this.headSignature = headSignature;
    }

    public String getHeadSignatureName() {
        return headSignatureName;
    }

    public void setHeadSignatureName(String headSignatureName) {
        this.headSignatureName = headSignatureName;
    }

    public LocalDateTime getHeadSignatureDate() {
        return headSignatureDate;
    }

    public void setHeadSignatureDate(LocalDateTime headSignatureDate) {
        this.headSignatureDate = headSignatureDate;
    }

    public String getNursDeptOpinion() {
        return nursDeptOpinion;
    }

    public void setNursDeptOpinion(String nursDeptOpinion) {
        this.nursDeptOpinion = nursDeptOpinion;
    }

    public String getNursDeptSignature() {
        return nursDeptSignature;
    }

    public void setNursDeptSignature(String nursDeptSignature) {
        this.nursDeptSignature = nursDeptSignature;
    }

    public String getNursDeptSignatureName() {
        return nursDeptSignatureName;
    }

    public void setNursDeptSignatureName(String nursDeptSignatureName) {
        this.nursDeptSignatureName = nursDeptSignatureName;
    }

    public LocalDateTime getDeptSignatureDate() {
        return deptSignatureDate;
    }

    public void setDeptSignatureDate(LocalDateTime deptSignatureDate) {
        this.deptSignatureDate = deptSignatureDate;
    }
}