package com.bjgoodwill.hip.ds.cis.adv.drugExplain.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "不良事件药品说明")
public class CisAdvEventDrugExplainTo implements Serializable {

	@Serial
    private static final long serialVersionUID = -4034187051811855592L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "药品不良事件id")
    private String eventDrugId;
    @Schema(description = "类型：doubt怀疑；together并用")
    private String useDrugType;
    @Schema(description = "批准文号（国药准字）")
    private String approvalDoc;
    @Schema(description = "商品编码")
    private String drugGoodsCode;
    @Schema(description = "商品名称")
    private String drugGoodsName;
    @Schema(description = "通用名")
    private String commonName;
    @Schema(description = "剂型")
    private String dosageform;
    @Schema(description = "生产厂家")
    private String manufactureFirm;
    @Schema(description = "生产批号")
    private String batchNo;
    @Schema(description = "用法用量（每次量 途径 频次）")
    private String usageDosage;
    @Schema(description = "开始时间")
    private LocalDateTime effectiveTimeLow;
    @Schema(description = "终止时间")
    private LocalDateTime effectiveTimeHigh;
    @Schema(description = "用药原因")
    private String reasonsMedication;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;

    public String getId() {
    	return id;
    }

    public void setId(String id) {
    	this.id = id;
    }

    public String getEventDrugId() {
    	return eventDrugId;
    }

    public void setEventDrugId(String eventDrugId) {
    	this.eventDrugId = eventDrugId;
    }

    public String getUseDrugType() {
    	return useDrugType;
    }

    public void setUseDrugType(String useDrugType) {
    	this.useDrugType = useDrugType;
    }

    public String getApprovalDoc() {
    	return approvalDoc;
    }

    public void setApprovalDoc(String approvalDoc) {
    	this.approvalDoc = approvalDoc;
    }

    public String getDrugGoodsCode() {
    	return drugGoodsCode;
    }

    public void setDrugGoodsCode(String drugGoodsCode) {
    	this.drugGoodsCode = drugGoodsCode;
    }

    public String getDrugGoodsName() {
    	return drugGoodsName;
    }

    public void setDrugGoodsName(String drugGoodsName) {
    	this.drugGoodsName = drugGoodsName;
    }

    public String getCommonName() {
    	return commonName;
    }

    public void setCommonName(String commonName) {
    	this.commonName = commonName;
    }

    public String getDosageform() {
    	return dosageform;
    }

    public void setDosageform(String dosageform) {
    	this.dosageform = dosageform;
    }

    public String getManufactureFirm() {
    	return manufactureFirm;
    }

    public void setManufactureFirm(String manufactureFirm) {
    	this.manufactureFirm = manufactureFirm;
    }

    public String getBatchNo() {
    	return batchNo;
    }

    public void setBatchNo(String batchNo) {
    	this.batchNo = batchNo;
    }

    public String getUsageDosage() {
    	return usageDosage;
    }

    public void setUsageDosage(String usageDosage) {
    	this.usageDosage = usageDosage;
    }

    public LocalDateTime getEffectiveTimeLow() {
    	return effectiveTimeLow;
    }

    public void setEffectiveTimeLow(LocalDateTime effectiveTimeLow) {
    	this.effectiveTimeLow = effectiveTimeLow;
    }

    public LocalDateTime getEffectiveTimeHigh() {
    	return effectiveTimeHigh;
    }

    public void setEffectiveTimeHigh(LocalDateTime effectiveTimeHigh) {
    	this.effectiveTimeHigh = effectiveTimeHigh;
    }

    public String getReasonsMedication() {
    	return reasonsMedication;
    }

    public void setReasonsMedication(String reasonsMedication) {
    	this.reasonsMedication = reasonsMedication;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventDrugExplainTo other = (CisAdvEventDrugExplainTo) obj;
		return Objects.equals(id, other.id);
	}
}