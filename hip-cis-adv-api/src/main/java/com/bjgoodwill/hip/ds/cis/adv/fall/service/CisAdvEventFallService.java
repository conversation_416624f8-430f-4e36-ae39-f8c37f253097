package com.bjgoodwill.hip.ds.cis.adv.fall.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallEto;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallNto;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallQto;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "跌倒坠床事件上报表领域服务", description = "跌倒坠床事件上报表领域服务")
public interface CisAdvEventFallService {

    @Operation(summary = "根据查询条件对跌倒坠床事件上报表进行查询。")
    @GetMapping("/cisAdvEventFalls")
    List<CisAdvEventFallTo> getCisAdvEventFalls(@ParameterObject @SpringQueryMap CisAdvEventFallQto cisAdvEventFallQto);

    @Operation(summary = "根据查询条件对跌倒坠床事件上报表进行分页查询。")
    @GetMapping("/cisAdvEventFalls/pages")
    GridResultSet<CisAdvEventFallTo> getCisAdvEventFallPage(@ParameterObject @SpringQueryMap CisAdvEventFallQto cisAdvEventFallQto);

    @Operation(summary = "根据唯一标识返回跌倒坠床事件上报表。")
    @GetMapping("/cisAdvEventFalls/{id:.+}")
    CisAdvEventFallTo getCisAdvEventFallById(@PathVariable("id") String id);

    @Operation(summary = "创建跌倒坠床事件上报表。")
    @PostMapping("/cisAdvEventFalls")
    CisAdvEventFallTo createCisAdvEventFall(@RequestBody @Valid CisAdvEventFallNto cisAdvEventFallNto);

    @Operation(summary = "根据唯一标识修改跌倒坠床事件上报表。")
    @PutMapping("/cisAdvEventFalls/{id:.+}")
    void updateCisAdvEventFall(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventFallEto cisAdvEventFallEto);

    @Operation(summary = "根据唯一标识删除跌倒坠床事件上报表。")
    @DeleteMapping("/cisAdvEventFalls/{id:.+}")
    void deleteCisAdvEventFall(@PathVariable("id") String id);

}