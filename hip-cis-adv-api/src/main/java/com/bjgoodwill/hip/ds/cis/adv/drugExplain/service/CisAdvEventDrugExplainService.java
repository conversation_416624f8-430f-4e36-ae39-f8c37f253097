package com.bjgoodwill.hip.ds.cis.adv.drugExplain.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.to.CisAdvEventDrugExplainEto;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.to.CisAdvEventDrugExplainNto;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.to.CisAdvEventDrugExplainQto;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.to.CisAdvEventDrugExplainTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "不良事件药品说明领域服务", description = "不良事件药品说明领域服务")
public interface CisAdvEventDrugExplainService {

    @Operation(summary = "根据查询条件对不良事件药品说明进行查询。")
    @GetMapping("/cisAdvEventDrugExplains")
    List<CisAdvEventDrugExplainTo> getCisAdvEventDrugExplains(@ParameterObject @SpringQueryMap CisAdvEventDrugExplainQto cisAdvEventDrugExplainQto);

    @Operation(summary = "根据查询条件对不良事件药品说明进行分页查询。")
    @GetMapping("/cisAdvEventDrugExplains/pages")
    GridResultSet<CisAdvEventDrugExplainTo> getCisAdvEventDrugExplainPage(@ParameterObject @SpringQueryMap CisAdvEventDrugExplainQto cisAdvEventDrugExplainQto);

    @Operation(summary = "根据唯一标识返回不良事件药品说明。")
    @GetMapping("/cisAdvEventDrugExplains/{id:.+}")
    CisAdvEventDrugExplainTo getCisAdvEventDrugExplainById(@PathVariable("id") String id);

    @Operation(summary = "创建不良事件药品说明。")
    @PostMapping("/cisAdvEventDrugExplains")
    CisAdvEventDrugExplainTo createCisAdvEventDrugExplain(@RequestBody @Valid CisAdvEventDrugExplainNto cisAdvEventDrugExplainNto);

    @Operation(summary = "根据唯一标识修改不良事件药品说明。")
    @PutMapping("/cisAdvEventDrugExplains/{id:.+}")
    void updateCisAdvEventDrugExplain(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventDrugExplainEto cisAdvEventDrugExplainEto);

    @Operation(summary = "根据唯一标识删除不良事件药品说明。")
    @DeleteMapping("/cisAdvEventDrugExplains/{id:.+}")
    void deleteCisAdvEventDrugExplain(@PathVariable("id") String id);

}