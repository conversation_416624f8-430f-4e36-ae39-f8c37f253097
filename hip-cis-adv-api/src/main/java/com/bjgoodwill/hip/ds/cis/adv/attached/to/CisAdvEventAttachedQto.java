package com.bjgoodwill.hip.ds.cis.adv.attached.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "不良事件与附卡对照")
public class CisAdvEventAttachedQto extends BaseQto implements Serializable {

	@Serial
    private static final long serialVersionUID = -6778543927225325689L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "不良事件类别")
    private String eventCode;
    @Schema(description = "附卡")
    private String attachedCard;
    @Schema(description = "医院编码")
    private String hospitalCode;

    public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

    public String getEventCode() {
    	return eventCode;
    }

    public void setEventCode(String eventCode) {
    	this.eventCode = eventCode;
    }

    public String getAttachedCard() {
    	return attachedCard;
    }

    public void setAttachedCard(String attachedCard) {
    	this.attachedCard = attachedCard;
    }

    public String getHospitalCode() {
    	return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
    	this.hospitalCode = hospitalCode;
    }
}