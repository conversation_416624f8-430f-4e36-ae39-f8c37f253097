package com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.to.CisAdvEventSharpInjuriesEto;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.to.CisAdvEventSharpInjuriesNto;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.to.CisAdvEventSharpInjuriesQto;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.to.CisAdvEventSharpInjuriesTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "护士锐器伤相关信息收集表领域服务", description = "护士锐器伤相关信息收集表领域服务")
public interface CisAdvEventSharpInjuriesService {

    @Operation(summary = "根据查询条件对护士锐器伤相关信息收集表进行查询。")
    @GetMapping("/cisAdvEventSharpInjurieses")
    List<CisAdvEventSharpInjuriesTo> getCisAdvEventSharpInjurieses(@ParameterObject @SpringQueryMap CisAdvEventSharpInjuriesQto cisAdvEventSharpInjuriesQto);

    @Operation(summary = "根据查询条件对护士锐器伤相关信息收集表进行分页查询。")
    @GetMapping("/cisAdvEventSharpInjurieses/pages")
    GridResultSet<CisAdvEventSharpInjuriesTo> getCisAdvEventSharpInjuriesPage(@ParameterObject @SpringQueryMap CisAdvEventSharpInjuriesQto cisAdvEventSharpInjuriesQto);

    @Operation(summary = "根据唯一标识返回护士锐器伤相关信息收集表。")
    @GetMapping("/cisAdvEventSharpInjurieses/{id:.+}")
    CisAdvEventSharpInjuriesTo getCisAdvEventSharpInjuriesById(@PathVariable("id") String id);

    @Operation(summary = "创建护士锐器伤相关信息收集表。")
    @PostMapping("/cisAdvEventSharpInjurieses")
    CisAdvEventSharpInjuriesTo createCisAdvEventSharpInjuries(@RequestBody @Valid CisAdvEventSharpInjuriesNto cisAdvEventSharpInjuriesNto);

    @Operation(summary = "根据唯一标识修改护士锐器伤相关信息收集表。")
    @PutMapping("/cisAdvEventSharpInjurieses/{id:.+}")
    void updateCisAdvEventSharpInjuries(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventSharpInjuriesEto cisAdvEventSharpInjuriesEto);

    @Operation(summary = "根据唯一标识删除护士锐器伤相关信息收集表。")
    @DeleteMapping("/cisAdvEventSharpInjurieses/{id:.+}")
    void deleteCisAdvEventSharpInjuries(@PathVariable("id") String id);

}