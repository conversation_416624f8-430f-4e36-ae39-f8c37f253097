package com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.to.CisAdvEventInstrumentMeasuresEto;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.to.CisAdvEventInstrumentMeasuresNto;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.to.CisAdvEventInstrumentMeasuresQto;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.to.CisAdvEventInstrumentMeasuresTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "医疗器械不良事件整改措施领域服务", description = "医疗器械不良事件整改措施领域服务")
public interface CisAdvEventInstrumentMeasuresService {

    @Operation(summary = "根据查询条件对医疗器械不良事件整改措施进行查询。")
    @GetMapping("/cisAdvEventInstrumentMeasureses")
    List<CisAdvEventInstrumentMeasuresTo> getCisAdvEventInstrumentMeasureses(@ParameterObject @SpringQueryMap CisAdvEventInstrumentMeasuresQto cisAdvEventInstrumentMeasuresQto);

    @Operation(summary = "根据查询条件对医疗器械不良事件整改措施进行分页查询。")
    @GetMapping("/cisAdvEventInstrumentMeasureses/pages")
    GridResultSet<CisAdvEventInstrumentMeasuresTo> getCisAdvEventInstrumentMeasuresPage(@ParameterObject @SpringQueryMap CisAdvEventInstrumentMeasuresQto cisAdvEventInstrumentMeasuresQto);

    @Operation(summary = "根据唯一标识返回医疗器械不良事件整改措施。")
    @GetMapping("/cisAdvEventInstrumentMeasureses/{id:.+}")
    CisAdvEventInstrumentMeasuresTo getCisAdvEventInstrumentMeasuresById(@PathVariable("id") String id);

    @Operation(summary = "创建医疗器械不良事件整改措施。")
    @PostMapping("/cisAdvEventInstrumentMeasureses")
    CisAdvEventInstrumentMeasuresTo createCisAdvEventInstrumentMeasures(@RequestBody @Valid CisAdvEventInstrumentMeasuresNto cisAdvEventInstrumentMeasuresNto);

    @Operation(summary = "根据唯一标识修改医疗器械不良事件整改措施。")
    @PutMapping("/cisAdvEventInstrumentMeasureses/{id:.+}")
    void updateCisAdvEventInstrumentMeasures(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventInstrumentMeasuresEto cisAdvEventInstrumentMeasuresEto);

    @Operation(summary = "根据唯一标识删除医疗器械不良事件整改措施。")
    @DeleteMapping("/cisAdvEventInstrumentMeasureses/{id:.+}")
    void deleteCisAdvEventInstrumentMeasures(@PathVariable("id") String id);

}