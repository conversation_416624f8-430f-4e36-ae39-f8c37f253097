package com.bjgoodwill.hip.ds.cis.adv.cauti.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.cauti.to.CisAdvEventCautiEto;
import com.bjgoodwill.hip.ds.cis.adv.cauti.to.CisAdvEventCautiNto;
import com.bjgoodwill.hip.ds.cis.adv.cauti.to.CisAdvEventCautiQto;
import com.bjgoodwill.hip.ds.cis.adv.cauti.to.CisAdvEventCautiTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "导尿管相关尿路感染（CAUTI）相关信息收集表领域服务", description = "导尿管相关尿路感染（CAUTI）相关信息收集表领域服务")
public interface CisAdvEventCautiService {

    @Operation(summary = "根据查询条件对导尿管相关尿路感染（CAUTI）相关信息收集表进行查询。")
    @GetMapping("/cisAdvEventCautis")
    List<CisAdvEventCautiTo> getCisAdvEventCautis(@ParameterObject @SpringQueryMap CisAdvEventCautiQto cisAdvEventCautiQto);

    @Operation(summary = "根据查询条件对导尿管相关尿路感染（CAUTI）相关信息收集表进行分页查询。")
    @GetMapping("/cisAdvEventCautis/pages")
    GridResultSet<CisAdvEventCautiTo> getCisAdvEventCautiPage(@ParameterObject @SpringQueryMap CisAdvEventCautiQto cisAdvEventCautiQto);

    @Operation(summary = "根据唯一标识返回导尿管相关尿路感染（CAUTI）相关信息收集表。")
    @GetMapping("/cisAdvEventCautis/{id:.+}")
    CisAdvEventCautiTo getCisAdvEventCautiById(@PathVariable("id") String id);

    @Operation(summary = "创建导尿管相关尿路感染（CAUTI）相关信息收集表。")
    @PostMapping("/cisAdvEventCautis")
    CisAdvEventCautiTo createCisAdvEventCauti(@RequestBody @Valid CisAdvEventCautiNto cisAdvEventCautiNto);

    @Operation(summary = "根据唯一标识修改导尿管相关尿路感染（CAUTI）相关信息收集表。")
    @PutMapping("/cisAdvEventCautis/{id:.+}")
    void updateCisAdvEventCauti(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventCautiEto cisAdvEventCautiEto);

    @Operation(summary = "根据唯一标识删除导尿管相关尿路感染（CAUTI）相关信息收集表。")
    @DeleteMapping("/cisAdvEventCautis/{id:.+}")
    void deleteCisAdvEventCauti(@PathVariable("id") String id);

}