package com.bjgoodwill.hip.ds.cis.adv.drug.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "药品不良反应事件报告表")
public class CisAdvEventDrugTo implements Serializable {

	@Serial
    private static final long serialVersionUID = -7682832510523849597L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "报告类别：first首次；track跟踪")
    private String reportCategory;
    @Schema(description = "报告类别名称：first首次；track跟踪")
    private String reportCategoryName;
    @Schema(description = "报告类型：new新的；serious严重；commonly一般")
    private String reportType;
    @Schema(description = "报告类型名称：new新的；serious严重；commonly一般")
    private String reportTypeName;
    @Schema(description = "报告单位类别：medical医疗机构；management经营企业；produce生产企业；personal个人；other其它")
    private String reportUnitCategory;
    @Schema(description = "报告单位类别名称：medical医疗机构；management经营企业；produce生产企业；personal个人；other其它")
    private String reportUnitCategoryName;
    @Schema(description = "其它报告单位")
    private String reportUnitOther;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "民族")
    private String nation;
    @Schema(description = "体重")
    private String weight;
    @Schema(description = "卧床标识")
    private boolean bedriddenFlag;
    @Schema(description = "联系电话")
    private String contactTel;
    @Schema(description = "原患疾病")
    private String primaryDiseases;
    @Schema(description = "既往药品不良反应标识")
    private boolean pastEventFlag;
    @Schema(description = "既往药品不良反应")
    private String pastEventValue;
    @Schema(description = "家族药品不良反应标识")
    private boolean familyEventFlag;
    @Schema(description = "家族药品不良反应")
    private String familyEventValue;
    @Schema(description = "相关重要信息：smoke吸烟史；drinkawine饮酒史；pregnancy妊娠期；hepatopathy肝病史；nephropathy肾病史；allergy过敏史；other其他；")
    private String importantInfo;
    @Schema(description = "相关重要信息名称：smoke吸烟史；drinkawine饮酒史；pregnancy妊娠期；hepatopathy肝病史；nephropathy肾病史；allergy过敏史；other其他；")
    private String importantInfoName;
    @Schema(description = "过敏史")
    private String allergyValue;
    @Schema(description = "相关其他")
    private String importantInfoOther;
    @Schema(description = "怀疑药品标识")
    private boolean doubtDrugFlag;
    @Schema(description = "并用药品标识")
    private boolean togetherDrugFlag;
    @Schema(description = "事件名称")
    private String eventName;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "不良反应事件过程描述：患者因；使用；时间；出现；停药或减慢滴速；采取；时间；症状；其他补充；")
    private String eventProcess;
    @Schema(description = "事件结果：recovery痊愈；becomebetter好转；noimprovement未好转；unknown不详；sequela有后遗症；death死亡")
    private String remark;
    @Schema(description = "后遗症表现")
    private String eventResultsSequelae;
    @Schema(description = "直接死因")
    private String causeDeath;
    @Schema(description = "死亡时间")
    private LocalDateTime deathDate;
    @Schema(description = "停药或减量后症状表现：yes是；no否；unknown不明；notstop未停药或减量")
    private String stopSymptoms;
    @Schema(description = "再次使用：yes是；no否；unknown不明；notusedagain未再使用；")
    private String againUsing;
    @Schema(description = "对疾病影响：unobvious不明显；extension病程延长；aggravation病情加重；sequela导致后遗症；death导致死亡；")
    private String impactDisease;
    @Schema(description = "对疾病影响名称：unobvious不明显；extension病程延长；aggravation病情加重；sequela导致后遗症；death导致死亡；")
    private String impactDiseaseName;
    @Schema(description = "报告人评价：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    private String reportUserEvaluation;
    @Schema(description = "报告人评价名称：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    private String reportUserEvaluationName;
    @Schema(description = "报告人评价签字")
    private String reportUserSignature;
    @Schema(description = "报告人评价签字名称")
    private String reportUserSignatureName;
    @Schema(description = "报告单位评价：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    private String unitEvaluation;
    @Schema(description = "报告单位评价名称：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    private String unitEvaluationName;
    @Schema(description = "报告单位评价签字")
    private String unitUserSignature;
    @Schema(description = "报告人联系电话")
    private String reportUserTel;
    @Schema(description = "报告人职业：doctor医生；pharmacist药师；nurse护士；other其它；")
    private String reportUserWork;
    @Schema(description = "报告人职业名称：doctor医生；pharmacist药师；nurse护士；other其它；")
    private String reportUserWorkName;
    @Schema(description = "其他职业")
    private String otherWork;
    @Schema(description = "报告人电子邮箱")
    private String reportUserEmail;
    @Schema(description = "报告单位")
    private String reportUnit;
    @Schema(description = "报告单位联系人")
    private String unitContactUser;
    @Schema(description = "报告单位电话")
    private String unitTel;
    @Schema(description = "生产企业请填写信息来源")
    private String sourceInformation;
    @Schema(description = "其它来源")
    private String otherSource;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;

    public String getId() {
    	return id;
    }

    public void setId(String id) {
    	this.id = id;
    }

    public String getEventReportId() {
    	return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
    	this.eventReportId = eventReportId;
    }

    public String getReportCategory() {
    	return reportCategory;
    }

    public void setReportCategory(String reportCategory) {
    	this.reportCategory = reportCategory;
    }

    public String getReportType() {
    	return reportType;
    }

    public void setReportType(String reportType) {
    	this.reportType = reportType;
    }

    public String getReportUnitCategory() {
    	return reportUnitCategory;
    }

    public void setReportUnitCategory(String reportUnitCategory) {
    	this.reportUnitCategory = reportUnitCategory;
    }

    public String getReportUnitOther() {
    	return reportUnitOther;
    }

    public void setReportUnitOther(String reportUnitOther) {
    	this.reportUnitOther = reportUnitOther;
    }

    public String getPatType() {
    	return patType;
    }

    public void setPatType(String patType) {
    	this.patType = patType;
    }

    public String getInpatientCode() {
    	return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
    	this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    public void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public String getPatName() {
    	return patName;
    }

    public void setPatName(String patName) {
    	this.patName = patName;
    }

    public String getSex() {
    	return sex;
    }

    public void setSex(String sex) {
    	this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
    	this.birthDate = birthDate;
    }

    public String getNation() {
    	return nation;
    }

    public void setNation(String nation) {
    	this.nation = nation;
    }

    public String getWeight() {
    	return weight;
    }

    public void setWeight(String weight) {
    	this.weight = weight;
    }

    public boolean isBedriddenFlag() {
    	return bedriddenFlag;
    }

    public void setBedriddenFlag(boolean bedriddenFlag) {
    	this.bedriddenFlag = bedriddenFlag;
    }

    public String getContactTel() {
    	return contactTel;
    }

    public void setContactTel(String contactTel) {
    	this.contactTel = contactTel;
    }

    public String getPrimaryDiseases() {
    	return primaryDiseases;
    }

    public void setPrimaryDiseases(String primaryDiseases) {
    	this.primaryDiseases = primaryDiseases;
    }

    public boolean isPastEventFlag() {
    	return pastEventFlag;
    }

    public void setPastEventFlag(boolean pastEventFlag) {
    	this.pastEventFlag = pastEventFlag;
    }

    public String getPastEventValue() {
    	return pastEventValue;
    }

    public void setPastEventValue(String pastEventValue) {
    	this.pastEventValue = pastEventValue;
    }

    public boolean isFamilyEventFlag() {
    	return familyEventFlag;
    }

    public void setFamilyEventFlag(boolean familyEventFlag) {
    	this.familyEventFlag = familyEventFlag;
    }

    public String getFamilyEventValue() {
    	return familyEventValue;
    }

    public void setFamilyEventValue(String familyEventValue) {
    	this.familyEventValue = familyEventValue;
    }

    public String getImportantInfo() {
    	return importantInfo;
    }

    public void setImportantInfo(String importantInfo) {
    	this.importantInfo = importantInfo;
    }

    public String getAllergyValue() {
    	return allergyValue;
    }

    public void setAllergyValue(String allergyValue) {
    	this.allergyValue = allergyValue;
    }

    public String getImportantInfoOther() {
    	return importantInfoOther;
    }

    public void setImportantInfoOther(String importantInfoOther) {
    	this.importantInfoOther = importantInfoOther;
    }

    public boolean isDoubtDrugFlag() {
    	return doubtDrugFlag;
    }

    public void setDoubtDrugFlag(boolean doubtDrugFlag) {
    	this.doubtDrugFlag = doubtDrugFlag;
    }

    public boolean isTogetherDrugFlag() {
    	return togetherDrugFlag;
    }

    public void setTogetherDrugFlag(boolean togetherDrugFlag) {
    	this.togetherDrugFlag = togetherDrugFlag;
    }

    public String getEventName() {
    	return eventName;
    }

    public void setEventName(String eventName) {
    	this.eventName = eventName;
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
    	this.eventDate = eventDate;
    }

    public String getEventProcess() {
    	return eventProcess;
    }

    public void setEventProcess(String eventProcess) {
    	this.eventProcess = eventProcess;
    }

    public String getRemark() {
    	return remark;
    }

    public void setRemark(String remark) {
    	this.remark = remark;
    }

    public String getEventResultsSequelae() {
    	return eventResultsSequelae;
    }

    public void setEventResultsSequelae(String eventResultsSequelae) {
    	this.eventResultsSequelae = eventResultsSequelae;
    }

    public String getCauseDeath() {
    	return causeDeath;
    }

    public void setCauseDeath(String causeDeath) {
    	this.causeDeath = causeDeath;
    }

    public LocalDateTime getDeathDate() {
    	return deathDate;
    }

    public void setDeathDate(LocalDateTime deathDate) {
    	this.deathDate = deathDate;
    }

    public String getStopSymptoms() {
    	return stopSymptoms;
    }

    public void setStopSymptoms(String stopSymptoms) {
    	this.stopSymptoms = stopSymptoms;
    }

    public String getAgainUsing() {
    	return againUsing;
    }

    public void setAgainUsing(String againUsing) {
    	this.againUsing = againUsing;
    }

    public String getImpactDisease() {
    	return impactDisease;
    }

    public void setImpactDisease(String impactDisease) {
    	this.impactDisease = impactDisease;
    }

    public String getReportUserEvaluation() {
    	return reportUserEvaluation;
    }

    public void setReportUserEvaluation(String reportUserEvaluation) {
    	this.reportUserEvaluation = reportUserEvaluation;
    }

    public String getReportUserSignature() {
    	return reportUserSignature;
    }

    public void setReportUserSignature(String reportUserSignature) {
    	this.reportUserSignature = reportUserSignature;
    }

    public String getUnitEvaluation() {
    	return unitEvaluation;
    }

    public void setUnitEvaluation(String unitEvaluation) {
    	this.unitEvaluation = unitEvaluation;
    }

    public String getUnitUserSignature() {
    	return unitUserSignature;
    }

    public void setUnitUserSignature(String unitUserSignature) {
    	this.unitUserSignature = unitUserSignature;
    }

    public String getReportUserTel() {
    	return reportUserTel;
    }

    public void setReportUserTel(String reportUserTel) {
    	this.reportUserTel = reportUserTel;
    }

    public String getReportUserWork() {
    	return reportUserWork;
    }

    public void setReportUserWork(String reportUserWork) {
    	this.reportUserWork = reportUserWork;
    }

    public String getOtherWork() {
    	return otherWork;
    }

    public void setOtherWork(String otherWork) {
    	this.otherWork = otherWork;
    }

    public String getReportUserEmail() {
    	return reportUserEmail;
    }

    public void setReportUserEmail(String reportUserEmail) {
    	this.reportUserEmail = reportUserEmail;
    }

    public String getReportUnit() {
    	return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
    	this.reportUnit = reportUnit;
    }

    public String getUnitContactUser() {
    	return unitContactUser;
    }

    public void setUnitContactUser(String unitContactUser) {
    	this.unitContactUser = unitContactUser;
    }

    public String getUnitTel() {
    	return unitTel;
    }

    public void setUnitTel(String unitTel) {
    	this.unitTel = unitTel;
    }

    public String getSourceInformation() {
    	return sourceInformation;
    }

    public void setSourceInformation(String sourceInformation) {
    	this.sourceInformation = sourceInformation;
    }

    public String getOtherSource() {
    	return otherSource;
    }

    public void setOtherSource(String otherSource) {
    	this.otherSource = otherSource;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    public String getReportCategoryName() {
        return reportCategoryName;
    }

    public void setReportCategoryName(String reportCategoryName) {
        this.reportCategoryName = reportCategoryName;
    }

    public String getReportTypeName() {
        return reportTypeName;
    }

    public void setReportTypeName(String reportTypeName) {
        this.reportTypeName = reportTypeName;
    }

    public String getReportUnitCategoryName() {
        return reportUnitCategoryName;
    }

    public void setReportUnitCategoryName(String reportUnitCategoryName) {
        this.reportUnitCategoryName = reportUnitCategoryName;
    }

    public String getImportantInfoName() {
        return importantInfoName;
    }

    public void setImportantInfoName(String importantInfoName) {
        this.importantInfoName = importantInfoName;
    }

    public String getImpactDiseaseName() {
        return impactDiseaseName;
    }

    public void setImpactDiseaseName(String impactDiseaseName) {
        this.impactDiseaseName = impactDiseaseName;
    }

    public String getReportUserEvaluationName() {
        return reportUserEvaluationName;
    }

    public void setReportUserEvaluationName(String reportUserEvaluationName) {
        this.reportUserEvaluationName = reportUserEvaluationName;
    }

    public String getReportUserSignatureName() {
        return reportUserSignatureName;
    }

    public void setReportUserSignatureName(String reportUserSignatureName) {
        this.reportUserSignatureName = reportUserSignatureName;
    }

    public String getUnitEvaluationName() {
        return unitEvaluationName;
    }

    public void setUnitEvaluationName(String unitEvaluationName) {
        this.unitEvaluationName = unitEvaluationName;
    }

    public String getReportUserWorkName() {
        return reportUserWorkName;
    }

    public void setReportUserWorkName(String reportUserWorkName) {
        this.reportUserWorkName = reportUserWorkName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventDrugTo other = (CisAdvEventDrugTo) obj;
		return Objects.equals(id, other.id);
	}
}