package com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "护士锐器伤相关信息收集表")
public class CisAdvEventSharpInjuriesQto extends BaseQto implements Serializable {

	@Serial
    private static final long serialVersionUID = -8639318373804827965L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区名称")
    private String areaName;
    @Schema(description = "发生地点：imp住院病区（跳转至1a）；unimp非住院病区(跳转至1b)")
    private String eventPlace;
    @Schema(description = "发生地点名称：imp住院病区（跳转至1a）；unimp非住院病区(跳转至1b)")
    private String eventPlaceName;
    @Schema(description = "非住院病区部门（或科室）名称（待定）（单选）：急诊emer；门诊amb ；手术室operationroom；血液净化中心bloodpurificationcentre；内镜中心endoscopiccenter；口腔科dentaldepartment；放射科radiologydepartment；体检中心medicalexaminationcenter；其他other")
    private String unimpOrgCode;
    @Schema(description = "非住院病区部门名称（或科室）名称（待定）（单选）：急诊emer；门诊amb ；手术室operationroom；血液净化中心bloodpurificationcentre；内镜中心endoscopiccenter；口腔科dentaldepartment；放射科radiologydepartment；体检中心medicalexaminationcenter；其他other")
    private String unimpOrgName;
    @Schema(description = "人员类别（单选）：本院执业护士（不包含新入职护士） licensed；新入职护士new；进修护士refresher；实习护士internship")
    private String staffType;
    @Schema(description = "人员类别名称（单选）：本院执业护士（不包含新入职护士） licensed；新入职护士new；进修护士refresher；实习护士internship")
    private String staffTypeName;
    @Schema(description = "工作（或实习）年限（单选）（备注：本院执业护士、新入职护士、进修护士选择从事护理工作年限。应届实习护士选择＜1年，非应届实习护士选择实际从事护理工作年限）： y＜1年； 1≤y＜2年； 2≤y＜5年；5≤y＜10年；10≤y＜20年； y≥20年 ")
    private String workingLife;
    @Schema(description = "工作（或实习）年限名称（单选）（备注：本院执业护士、新入职护士、进修护士选择从事护理工作年限。应届实习护士选择＜1年，非应届实习护士选择实际从事护理工作年限）： y＜1年； 1≤y＜2年； 2≤y＜5年；5≤y＜10年；10≤y＜20年； y≥20年 ")
    private String workingLifeName;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "锐器伤发生方式（单选）：自伤self；他人误伤accidental；其他other")
    private String eventType;
    @Schema(description = "锐器伤发生方式名称（单选）：自伤self；他人误伤accidental；其他other")
    private String eventTypeName;
    @Schema(description = "锐器伤所涉及的具体器具（备注：安全型器具：锐器通过安全性设计变为使用后屏蔽锐器或者没有锐器的装置即为安全型器具）（单选）：头皮钢针scalpneedle ；安全型静脉留置针safetyindwellingneedle；非安全型静脉留置针unsafetyindwellingneedle；安全型一次性注射器针头safetysyringeneedle；非安全型一次性注射器针头unsafetysyringeneedle；安全型静脉采血针safetysamplingneedle；非安全型静脉采血针unsafetysamplingneedle；安全型输液港针safetyinfusionneedle；非安全型输液港针unsafetyinfusionneedle；中心静脉导管穿刺针centralcatheterpunctureneedle；安全型动脉采血器safetyarterialbloodsampler；非安全型动脉采血器unsafetyarterialbloodsampler；末梢采血针peripheralcollectbloodneedle；安全型胰岛素注射笔safetyinsulininjectionpen；非安全型胰岛素注射笔safetyinsulininjectionpen；手术缝针或手术刀surgicalneedleorscalpel；剪刀scissors；安瓿瓶ampoulebottle；其他other")
    private String sharpDevice;
    @Schema(description = "锐器伤所涉及的具体器具（备注：安全型器具：锐器通过安全性设计变为使用后屏蔽锐器或者没有锐器的装置即为安全型器具）（单选）：头皮钢针scalpneedle ；安全型静脉留置针safetyindwellingneedle；非安全型静脉留置针unsafetyindwellingneedle；安全型一次性注射器针头safetysyringeneedle；非安全型一次性注射器针头unsafetysyringeneedle；安全型静脉采血针safetysamplingneedle；非安全型静脉采血针unsafetysamplingneedle；安全型输液港针safetyinfusionneedle；非安全型输液港针unsafetyinfusionneedle；中心静脉导管穿刺针centralcatheterpunctureneedle；安全型动脉采血器safetyarterialbloodsampler；非安全型动脉采血器unsafetyarterialbloodsampler；末梢采血针peripheralcollectbloodneedle；安全型胰岛素注射笔safetyinsulininjectionpen；非安全型胰岛素注射笔safetyinsulininjectionpen；手术缝针或手术刀surgicalneedleorscalpel；剪刀scissors；安瓿瓶ampoulebottle；其他other")
    private String sharpDeviceName;
    @Schema(description = "发生锐器伤时的具体操作或环节（单选）: 准备输液器/输血器infusion/transfusion；静脉穿刺venipuncture；采集血标本collectblood；注射给药injection；药液配置drugconfiguration；换输液瓶（袋）changeinfusion bottle；茂菲氏管给药murphy'stubedrug；置入导管insertcatheter；冲管或封管flushingorsealingpipe；回套针帽；分离针头separateneedle；拔针pulloutneedle ；将针头放入锐器盒putintosharpsbox；传递锐器transfersharps ；整理手术器械arrangesurgicalinstruments ；清洗器械cleaningequipment ； 清理废物cleanupwaste；其他other")
    private String sharpOperation;
    @Schema(description = "发生锐器伤时的具体操作或环节（单选）: 准备输液器/输血器infusion/transfusion；静脉穿刺venipuncture；采集血标本collectblood；注射给药injection；药液配置drugconfiguration；换输液瓶（袋）changeinfusion bottle；茂菲氏管给药murphy'stubedrug；置入导管insertcatheter；冲管或封管flushingorsealingpipe；回套针帽；分离针头separateneedle；拔针pulloutneedle ；将针头放入锐器盒putintosharpsbox；传递锐器transfersharps ；整理手术器械arrangesurgicalinstruments ；清洗器械cleaningequipment ； 清理废物cleanupwaste；其他other")
    private String sharpOperationName;
    @Schema(description = "锐器是否被污染（单选）：1是；0否（直接跳转至13题）；uncertain不确定（直接跳转至13题）")
    private String pollutionFlag;
    @Schema(description = "锐器是否被污染名称（单选）：1是；0否（直接跳转至13题）；uncertain不确定（直接跳转至13题）")
    private String pollutionFlagName;
    @Schema(description = "污染源类型（单选）：血液；体液；其他")
    private String pollutionType;
    @Schema(description = "污染源类型名称（单选）：血液；体液；其他")
    private String pollutionTypeName;
    @Schema(description = "该污染源是否含有血源性传播疾病（单选）：1是；0否（直接跳转至13题） ；  uncertain不确定（直接跳转至13题）")
    private String bloodTransmittFlag;
    @Schema(description = "该污染源是否含有血源性传播疾病名称（单选）：1是；0否（直接跳转至13题） ；  uncertain不确定（直接跳转至13题）")
    private String bloodTransmittFlagName;
    @Schema(description = "血源性传播疾病类型（单选）：hiv；乙肝hepatitisc；丙肝hcv；梅毒syphilis； 其他other；两种或两种以上类型more")
    private String bloodTransmittType;
    @Schema(description = "血源性传播疾病类型名称（单选）：hiv；乙肝hepatitisc；丙肝hcv；梅毒syphilis； 其他other；两种或两种以上类型more")
    private String bloodTransmittTypeName;
    @Schema(description = "锐器伤后是否进行了定期追踪和检测（单选）：1是（直接跳转至15题）；0否")
    private Boolean trackFlag;
    @Schema(description = "未进行追踪检测的原因（单选）：自行判断后果不严重notserious  ；无相关制度和流程uncorrelated；其他原因other（选择任何一个选项后填报结束）")
    private String untrackReason;
    @Schema(description = "未进行追踪检测的原因名称（单选）：自行判断后果不严重notserious  ；无相关制度和流程uncorrelated；其他原因other（选择任何一个选项后填报结束）")
    private String untrackReasonName;
    @Schema(description = "截止到表单上报时，该事件是否导致锐器伤者确诊感染（单选）：1是；0否（填报结束）  ；waiting尚在等待检测结果（检测结果确定后请返回系统修改选项）（暂时填报结束，同时，系统预留一个口，等确定后再修改该题选项，每次登录提醒。）")
    private String confirmedFlag;
    @Schema(description = "截止到表单上报时，该事件是否导致锐器伤者确诊感染（单选）：1是；0否（填报结束）  ；waiting尚在等待检测结果（检测结果确定后请返回系统修改选项）（暂时填报结束，同时，系统预留一个口，等确定后再修改该题选项，每次登录提醒。）")
    private String confirmedFlagName;
    @Schema(description = "感染疾病类型（单选）： hiv ；乙肝hepatitisc ；丙肝hcv；梅毒syphilis  ；其他other ；两种或两种以上类型more")
    private String diseaseType;
    @Schema(description = "感染疾病类型名称（单选）： hiv ；乙肝hepatitisc ；丙肝hcv；梅毒syphilis  ；其他other ；两种或两种以上类型more")
    private String diseaseTypeName;

    public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

    public String getEventReportId() {
    	return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
    	this.eventReportId = eventReportId;
    }

    public String getPatType() {
    	return patType;
    }

    public void setPatType(String patType) {
    	this.patType = patType;
    }

    public String getInpatientCode() {
    	return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
    	this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    public void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public String getPatName() {
    	return patName;
    }

    public void setPatName(String patName) {
    	this.patName = patName;
    }

    public String getSex() {
    	return sex;
    }

    public void setSex(String sex) {
    	this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
    	this.birthDate = birthDate;
    }

    public String getAreaCode() {
    	return areaCode;
    }

    public void setAreaCode(String areaCode) {
    	this.areaCode = areaCode;
    }

    public String getAreaName() {
    	return areaName;
    }

    public void setAreaName(String areaName) {
    	this.areaName = areaName;
    }

    public String getEventPlace() {
    	return eventPlace;
    }

    public void setEventPlace(String eventPlace) {
    	this.eventPlace = eventPlace;
    }

    public String getEventPlaceName() {
    	return eventPlaceName;
    }

    public void setEventPlaceName(String eventPlaceName) {
    	this.eventPlaceName = eventPlaceName;
    }

    public String getUnimpOrgCode() {
    	return unimpOrgCode;
    }

    public void setUnimpOrgCode(String unimpOrgCode) {
    	this.unimpOrgCode = unimpOrgCode;
    }

    public String getUnimpOrgName() {
    	return unimpOrgName;
    }

    public void setUnimpOrgName(String unimpOrgName) {
    	this.unimpOrgName = unimpOrgName;
    }

    public String getStaffType() {
    	return staffType;
    }

    public void setStaffType(String staffType) {
    	this.staffType = staffType;
    }

    public String getStaffTypeName() {
    	return staffTypeName;
    }

    public void setStaffTypeName(String staffTypeName) {
    	this.staffTypeName = staffTypeName;
    }

    public String getWorkingLife() {
    	return workingLife;
    }

    public void setWorkingLife(String workingLife) {
    	this.workingLife = workingLife;
    }

    public String getWorkingLifeName() {
    	return workingLifeName;
    }

    public void setWorkingLifeName(String workingLifeName) {
    	this.workingLifeName = workingLifeName;
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
    	this.eventDate = eventDate;
    }

    public String getEventType() {
    	return eventType;
    }

    public void setEventType(String eventType) {
    	this.eventType = eventType;
    }

    public String getEventTypeName() {
    	return eventTypeName;
    }

    public void setEventTypeName(String eventTypeName) {
    	this.eventTypeName = eventTypeName;
    }

    public String getSharpDevice() {
    	return sharpDevice;
    }

    public void setSharpDevice(String sharpDevice) {
    	this.sharpDevice = sharpDevice;
    }

    public String getSharpDeviceName() {
    	return sharpDeviceName;
    }

    public void setSharpDeviceName(String sharpDeviceName) {
    	this.sharpDeviceName = sharpDeviceName;
    }

    public String getSharpOperation() {
    	return sharpOperation;
    }

    public void setSharpOperation(String sharpOperation) {
    	this.sharpOperation = sharpOperation;
    }

    public String getSharpOperationName() {
    	return sharpOperationName;
    }

    public void setSharpOperationName(String sharpOperationName) {
    	this.sharpOperationName = sharpOperationName;
    }

    public String getPollutionFlag() {
    	return pollutionFlag;
    }

    public void setPollutionFlag(String pollutionFlag) {
    	this.pollutionFlag = pollutionFlag;
    }

    public String getPollutionFlagName() {
    	return pollutionFlagName;
    }

    public void setPollutionFlagName(String pollutionFlagName) {
    	this.pollutionFlagName = pollutionFlagName;
    }

    public String getPollutionType() {
    	return pollutionType;
    }

    public void setPollutionType(String pollutionType) {
    	this.pollutionType = pollutionType;
    }

    public String getPollutionTypeName() {
    	return pollutionTypeName;
    }

    public void setPollutionTypeName(String pollutionTypeName) {
    	this.pollutionTypeName = pollutionTypeName;
    }

    public String getBloodTransmittFlag() {
    	return bloodTransmittFlag;
    }

    public void setBloodTransmittFlag(String bloodTransmittFlag) {
    	this.bloodTransmittFlag = bloodTransmittFlag;
    }

    public String getBloodTransmittFlagName() {
    	return bloodTransmittFlagName;
    }

    public void setBloodTransmittFlagName(String bloodTransmittFlagName) {
    	this.bloodTransmittFlagName = bloodTransmittFlagName;
    }

    public String getBloodTransmittType() {
    	return bloodTransmittType;
    }

    public void setBloodTransmittType(String bloodTransmittType) {
    	this.bloodTransmittType = bloodTransmittType;
    }

    public String getBloodTransmittTypeName() {
    	return bloodTransmittTypeName;
    }

    public void setBloodTransmittTypeName(String bloodTransmittTypeName) {
    	this.bloodTransmittTypeName = bloodTransmittTypeName;
    }

    public Boolean getTrackFlag() {
    	return trackFlag;
    }

    public void setTrackFlag(Boolean trackFlag) {
    	this.trackFlag = trackFlag;
    }

    public String getUntrackReason() {
    	return untrackReason;
    }

    public void setUntrackReason(String untrackReason) {
    	this.untrackReason = untrackReason;
    }

    public String getUntrackReasonName() {
    	return untrackReasonName;
    }

    public void setUntrackReasonName(String untrackReasonName) {
    	this.untrackReasonName = untrackReasonName;
    }

    public String getConfirmedFlag() {
    	return confirmedFlag;
    }

    public void setConfirmedFlag(String confirmedFlag) {
    	this.confirmedFlag = confirmedFlag;
    }

    public String getConfirmedFlagName() {
    	return confirmedFlagName;
    }

    public void setConfirmedFlagName(String confirmedFlagName) {
    	this.confirmedFlagName = confirmedFlagName;
    }

    public String getDiseaseType() {
    	return diseaseType;
    }

    public void setDiseaseType(String diseaseType) {
    	this.diseaseType = diseaseType;
    }

    public String getDiseaseTypeName() {
    	return diseaseTypeName;
    }

    public void setDiseaseTypeName(String diseaseTypeName) {
    	this.diseaseTypeName = diseaseTypeName;
    }
}