package com.bjgoodwill.hip.ds.cis.adv.drug.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "药品不良反应事件报告表")
public class CisAdvEventDrugEto  extends BaseEto implements Serializable {

	@Serial
    private static final long serialVersionUID = -7082654485897035117L;

    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "报告类别：first首次；track跟踪")
    private String reportCategory;
    @Schema(description = "报告类别名称：first首次；track跟踪")
    private String reportCategoryName;
    @Schema(description = "报告类型：new新的；serious严重；commonly一般")
    private String reportType;
    @Schema(description = "报告类型名称：new新的；serious严重；commonly一般")
    private String reportTypeName;
    @Schema(description = "报告单位类别：medical医疗机构；management经营企业；produce生产企业；personal个人；other其它")
    private String reportUnitCategory;
    @Schema(description = "报告单位类别名称：medical医疗机构；management经营企业；produce生产企业；personal个人；other其它")
    private String reportUnitCategoryName;
    @Schema(description = "其它报告单位")
    private String reportUnitOther;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "民族")
    private String nation;
    @Schema(description = "体重")
    private String weight;
    @Schema(description = "卧床标识")
    private boolean bedriddenFlag;
    @Schema(description = "联系电话")
    private String contactTel;
    @Schema(description = "原患疾病")
    private String primaryDiseases;
    @Schema(description = "既往药品不良反应标识")
    private boolean pastEventFlag;
    @Schema(description = "既往药品不良反应")
    private String pastEventValue;
    @Schema(description = "家族药品不良反应标识")
    private boolean familyEventFlag;
    @Schema(description = "家族药品不良反应")
    private String familyEventValue;
    @Schema(description = "相关重要信息：smoke吸烟史；drinkawine饮酒史；pregnancy妊娠期；hepatopathy肝病史；nephropathy肾病史；allergy过敏史；other其他；")
    private String importantInfo;
    @Schema(description = "相关重要信息名称：smoke吸烟史；drinkawine饮酒史；pregnancy妊娠期；hepatopathy肝病史；nephropathy肾病史；allergy过敏史；other其他；")
    private String importantInfoName;
    @Schema(description = "过敏史")
    private String allergyValue;
    @Schema(description = "相关其他")
    private String importantInfoOther;
    @Schema(description = "怀疑药品标识")
    private boolean doubtDrugFlag;
    @Schema(description = "并用药品标识")
    private boolean togetherDrugFlag;
    @Schema(description = "事件名称")
    private String eventName;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "不良反应事件过程描述：患者因；使用；时间；出现；停药或减慢滴速；采取；时间；症状；其他补充；")
    private String eventProcess;
    @Schema(description = "事件结果：recovery痊愈；becomebetter好转；noimprovement未好转；unknown不详；sequela有后遗症；death死亡")
    private String remark;
    @Schema(description = "后遗症表现")
    private String eventResultsSequelae;
    @Schema(description = "直接死因")
    private String causeDeath;
    @Schema(description = "死亡时间")
    private LocalDateTime deathDate;
    @Schema(description = "停药或减量后症状表现：yes是；no否；unknown不明；notstop未停药或减量")
    private String stopSymptoms;
    @Schema(description = "再次使用：yes是；no否；unknown不明；notusedagain未再使用；")
    private String againUsing;
    @Schema(description = "对疾病影响：unobvious不明显；extension病程延长；aggravation病情加重；sequela导致后遗症；death导致死亡；")
    private String impactDisease;
    @Schema(description = "对疾病影响名称：unobvious不明显；extension病程延长；aggravation病情加重；sequela导致后遗症；death导致死亡；")
    private String impactDiseaseName;
    @Schema(description = "报告人评价：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    private String reportUserEvaluation;
    @Schema(description = "报告人评价名称：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    private String reportUserEvaluationName;
    @Schema(description = "报告人评价签字")
    private String reportUserSignature;
    @Schema(description = "报告人评价签字名称")
    private String reportUserSignatureName;
    @Schema(description = "报告单位评价：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    private String unitEvaluation;
    @Schema(description = "报告单位评价名称：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    private String unitEvaluationName;
    @Schema(description = "报告单位评价签字")
    private String unitUserSignature;
    @Schema(description = "报告人联系电话")
    private String reportUserTel;
    @Schema(description = "报告人职业：doctor医生；pharmacist药师；nurse护士；other其它；")
    private String reportUserWork;
    @Schema(description = "报告人职业名称：doctor医生；pharmacist药师；nurse护士；other其它；")
    private String reportUserWorkName;
    @Schema(description = "其他职业")
    private String otherWork;
    @Schema(description = "报告人电子邮箱")
    private String reportUserEmail;
    @Schema(description = "报告单位")
    private String reportUnit;
    @Schema(description = "报告单位联系人")
    private String unitContactUser;
    @Schema(description = "报告单位电话")
    private String unitTel;
    @Schema(description = "生产企业请填写信息来源")
    private String sourceInformation;
    @Schema(description = "其它来源")
    private String otherSource;

    @Size(max = 50, message = "不良事件id长度不能超过50个字符！")
    public String getEventReportId() {
    	return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = StringUtils.trimToNull(eventReportId);
    }

    @Size(max = 16, message = "报告类别：first首次；track跟踪长度不能超过16个字符！")
    public String getReportCategory() {
    	return reportCategory;
    }

    public void setReportCategory(String reportCategory) {
        this.reportCategory = StringUtils.trimToNull(reportCategory);
    }

    @Size(max = 16, message = "报告类型：new新的；serious严重；commonly一般长度不能超过16个字符！")
    public String getReportType() {
    	return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = StringUtils.trimToNull(reportType);
    }

    @Size(max = 20, message = "报告单位类别：medical医疗机构；management经营企业；produce生产企业；personal个人；other其它长度不能超过20个字符！")
    public String getReportUnitCategory() {
    	return reportUnitCategory;
    }

    public void setReportUnitCategory(String reportUnitCategory) {
        this.reportUnitCategory = StringUtils.trimToNull(reportUnitCategory);
    }

    @Size(max = 128, message = "其它报告单位长度不能超过128个字符！")
    public String getReportUnitOther() {
    	return reportUnitOther;
    }

    public void setReportUnitOther(String reportUnitOther) {
        this.reportUnitOther = StringUtils.trimToNull(reportUnitOther);
    }

    @Size(max = 2, message = "患者类型长度不能超过2个字符！")
    public String getPatType() {
    	return patType;
    }

    public void setPatType(String patType) {
        this.patType = StringUtils.trimToNull(patType);
    }

    @Size(max = 16, message = "住院号(门诊就诊卡号)长度不能超过16个字符！")
    public String getInpatientCode() {
    	return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = StringUtils.trimToNull(inpatientCode);
    }

    @Size(max = 16, message = "就诊流水号长度不能超过16个字符！")
    public String getVisitCode() {
    	return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @Size(max = 64, message = "患者姓名长度不能超过64个字符！")
    public String getPatName() {
    	return patName;
    }

    public void setPatName(String patName) {
        this.patName = StringUtils.trimToNull(patName);
    }

    @Size(max = 16, message = "性别长度不能超过16个字符！")
    public String getSex() {
    	return sex;
    }

    public void setSex(String sex) {
        this.sex = StringUtils.trimToNull(sex);
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    @Size(max = 16, message = "民族长度不能超过16个字符！")
    public String getNation() {
    	return nation;
    }

    public void setNation(String nation) {
        this.nation = StringUtils.trimToNull(nation);
    }

    @Size(max = 16, message = "体重长度不能超过16个字符！")
    public String getWeight() {
    	return weight;
    }

    public void setWeight(String weight) {
        this.weight = StringUtils.trimToNull(weight);
    }

    public boolean isBedriddenFlag() {
    	return bedriddenFlag;
    }

    public void setBedriddenFlag(boolean bedriddenFlag) {
        this.bedriddenFlag = bedriddenFlag;
    }

    @Size(max = 24, message = "联系电话长度不能超过24个字符！")
    public String getContactTel() {
    	return contactTel;
    }

    public void setContactTel(String contactTel) {
        this.contactTel = StringUtils.trimToNull(contactTel);
    }

    @Size(max = 128, message = "原患疾病长度不能超过128个字符！")
    public String getPrimaryDiseases() {
    	return primaryDiseases;
    }

    public void setPrimaryDiseases(String primaryDiseases) {
        this.primaryDiseases = StringUtils.trimToNull(primaryDiseases);
    }

    public boolean isPastEventFlag() {
    	return pastEventFlag;
    }

    public void setPastEventFlag(boolean pastEventFlag) {
        this.pastEventFlag = pastEventFlag;
    }

    @Size(max = 128, message = "既往药品不良反应长度不能超过128个字符！")
    public String getPastEventValue() {
    	return pastEventValue;
    }

    public void setPastEventValue(String pastEventValue) {
        this.pastEventValue = StringUtils.trimToNull(pastEventValue);
    }

    public boolean isFamilyEventFlag() {
    	return familyEventFlag;
    }

    public void setFamilyEventFlag(boolean familyEventFlag) {
        this.familyEventFlag = familyEventFlag;
    }

    @Size(max = 128, message = "家族药品不良反应长度不能超过128个字符！")
    public String getFamilyEventValue() {
    	return familyEventValue;
    }

    public void setFamilyEventValue(String familyEventValue) {
        this.familyEventValue = StringUtils.trimToNull(familyEventValue);
    }

    @Size(max = 16, message = "相关重要信息：smoke吸烟史；drinkawine饮酒史；pregnancy妊娠期；hepatopathy肝病史；nephropathy肾病史；allergy过敏史；other其他；长度不能超过16个字符！")
    public String getImportantInfo() {
    	return importantInfo;
    }

    public void setImportantInfo(String importantInfo) {
        this.importantInfo = StringUtils.trimToNull(importantInfo);
    }

    public String getAllergyValue() {
    	return allergyValue;
    }

    public void setAllergyValue(String allergyValue) {
        this.allergyValue = StringUtils.trimToNull(allergyValue);
    }

    public String getImportantInfoOther() {
    	return importantInfoOther;
    }

    public void setImportantInfoOther(String importantInfoOther) {
        this.importantInfoOther = StringUtils.trimToNull(importantInfoOther);
    }

    public boolean isDoubtDrugFlag() {
    	return doubtDrugFlag;
    }

    public void setDoubtDrugFlag(boolean doubtDrugFlag) {
        this.doubtDrugFlag = doubtDrugFlag;
    }

    public boolean isTogetherDrugFlag() {
    	return togetherDrugFlag;
    }

    public void setTogetherDrugFlag(boolean togetherDrugFlag) {
        this.togetherDrugFlag = togetherDrugFlag;
    }

    public String getEventName() {
    	return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = StringUtils.trimToNull(eventName);
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
        this.eventDate = eventDate;
    }

    public String getEventProcess() {
    	return eventProcess;
    }

    public void setEventProcess(String eventProcess) {
        this.eventProcess = StringUtils.trimToNull(eventProcess);
    }

    @Size(max = 128, message = "事件结果：recovery痊愈；becomebetter好转；noimprovement未好转；unknown不详；sequela有后遗症；death死亡长度不能超过128个字符！")
    public String getRemark() {
    	return remark;
    }

    public void setRemark(String remark) {
        this.remark = StringUtils.trimToNull(remark);
    }

    @Size(max = 128, message = "后遗症表现长度不能超过128个字符！")
    public String getEventResultsSequelae() {
    	return eventResultsSequelae;
    }

    public void setEventResultsSequelae(String eventResultsSequelae) {
        this.eventResultsSequelae = StringUtils.trimToNull(eventResultsSequelae);
    }

    @Size(max = 128, message = "直接死因长度不能超过128个字符！")
    public String getCauseDeath() {
    	return causeDeath;
    }

    public void setCauseDeath(String causeDeath) {
        this.causeDeath = StringUtils.trimToNull(causeDeath);
    }

    public LocalDateTime getDeathDate() {
    	return deathDate;
    }

    public void setDeathDate(LocalDateTime deathDate) {
        this.deathDate = deathDate;
    }

    @Size(max = 16, message = "停药或减量后症状表现：yes是；no否；unknown不明；notstop未停药或减量长度不能超过16个字符！")
    public String getStopSymptoms() {
    	return stopSymptoms;
    }

    public void setStopSymptoms(String stopSymptoms) {
        this.stopSymptoms = StringUtils.trimToNull(stopSymptoms);
    }

    @Size(max = 16, message = "再次使用：yes是；no否；unknown不明；notusedagain未再使用；长度不能超过16个字符！")
    public String getAgainUsing() {
    	return againUsing;
    }

    public void setAgainUsing(String againUsing) {
        this.againUsing = StringUtils.trimToNull(againUsing);
    }

    @Size(max = 16, message = "对疾病影响：unobvious不明显；extension病程延长；aggravation病情加重；sequela导致后遗症；death导致死亡；长度不能超过16个字符！")
    public String getImpactDisease() {
    	return impactDisease;
    }

    public void setImpactDisease(String impactDisease) {
        this.impactDisease = StringUtils.trimToNull(impactDisease);
    }

    @Size(max = 16, message = "报告人评价：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；长度不能超过16个字符！")
    public String getReportUserEvaluation() {
    	return reportUserEvaluation;
    }

    public void setReportUserEvaluation(String reportUserEvaluation) {
        this.reportUserEvaluation = StringUtils.trimToNull(reportUserEvaluation);
    }

    @Size(max = 16, message = "报告人评价签字长度不能超过16个字符！")
    public String getReportUserSignature() {
    	return reportUserSignature;
    }

    public void setReportUserSignature(String reportUserSignature) {
        this.reportUserSignature = StringUtils.trimToNull(reportUserSignature);
    }

    @Size(max = 16, message = "报告单位评价：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；长度不能超过16个字符！")
    public String getUnitEvaluation() {
    	return unitEvaluation;
    }

    public void setUnitEvaluation(String unitEvaluation) {
        this.unitEvaluation = StringUtils.trimToNull(unitEvaluation);
    }

    @Size(max = 16, message = "报告单位评价签字长度不能超过16个字符！")
    public String getUnitUserSignature() {
    	return unitUserSignature;
    }

    public void setUnitUserSignature(String unitUserSignature) {
        this.unitUserSignature = StringUtils.trimToNull(unitUserSignature);
    }

    @Size(max = 24, message = "报告人联系电话长度不能超过24个字符！")
    public String getReportUserTel() {
    	return reportUserTel;
    }

    public void setReportUserTel(String reportUserTel) {
        this.reportUserTel = StringUtils.trimToNull(reportUserTel);
    }

    @Size(max = 16, message = "报告人职业：doctor医生；pharmacist药师；nurse护士；other其它；长度不能超过16个字符！")
    public String getReportUserWork() {
    	return reportUserWork;
    }

    public void setReportUserWork(String reportUserWork) {
        this.reportUserWork = StringUtils.trimToNull(reportUserWork);
    }

    @Size(max = 128, message = "其他职业长度不能超过128个字符！")
    public String getOtherWork() {
    	return otherWork;
    }

    public void setOtherWork(String otherWork) {
        this.otherWork = StringUtils.trimToNull(otherWork);
    }

    @Size(max = 64, message = "报告人电子邮箱长度不能超过64个字符！")
    public String getReportUserEmail() {
    	return reportUserEmail;
    }

    public void setReportUserEmail(String reportUserEmail) {
        this.reportUserEmail = StringUtils.trimToNull(reportUserEmail);
    }

    @Size(max = 128, message = "报告单位长度不能超过128个字符！")
    public String getReportUnit() {
    	return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = StringUtils.trimToNull(reportUnit);
    }

    @Size(max = 16, message = "报告单位联系人长度不能超过16个字符！")
    public String getUnitContactUser() {
    	return unitContactUser;
    }

    public void setUnitContactUser(String unitContactUser) {
        this.unitContactUser = StringUtils.trimToNull(unitContactUser);
    }

    @Size(max = 24, message = "报告单位电话长度不能超过24个字符！")
    public String getUnitTel() {
    	return unitTel;
    }

    public void setUnitTel(String unitTel) {
        this.unitTel = StringUtils.trimToNull(unitTel);
    }

    @Size(max = 16, message = "生产企业请填写信息来源长度不能超过16个字符！")
    public String getSourceInformation() {
    	return sourceInformation;
    }

    public void setSourceInformation(String sourceInformation) {
        this.sourceInformation = StringUtils.trimToNull(sourceInformation);
    }

    @Size(max = 28, message = "其它来源长度不能超过28个字符！")
    public String getOtherSource() {
    	return otherSource;
    }

    public void setOtherSource(String otherSource) {
        this.otherSource = StringUtils.trimToNull(otherSource);
    }

    public String getReportCategoryName() {
        return reportCategoryName;
    }

    public void setReportCategoryName(String reportCategoryName) {
        this.reportCategoryName = reportCategoryName;
    }

    public String getReportTypeName() {
        return reportTypeName;
    }

    public void setReportTypeName(String reportTypeName) {
        this.reportTypeName = reportTypeName;
    }

    public String getReportUnitCategoryName() {
        return reportUnitCategoryName;
    }

    public void setReportUnitCategoryName(String reportUnitCategoryName) {
        this.reportUnitCategoryName = reportUnitCategoryName;
    }

    public String getImportantInfoName() {
        return importantInfoName;
    }

    public void setImportantInfoName(String importantInfoName) {
        this.importantInfoName = importantInfoName;
    }

    public String getImpactDiseaseName() {
        return impactDiseaseName;
    }

    public void setImpactDiseaseName(String impactDiseaseName) {
        this.impactDiseaseName = impactDiseaseName;
    }

    public String getReportUserEvaluationName() {
        return reportUserEvaluationName;
    }

    public void setReportUserEvaluationName(String reportUserEvaluationName) {
        this.reportUserEvaluationName = reportUserEvaluationName;
    }

    public String getReportUserSignatureName() {
        return reportUserSignatureName;
    }

    public void setReportUserSignatureName(String reportUserSignatureName) {
        this.reportUserSignatureName = reportUserSignatureName;
    }

    public String getUnitEvaluationName() {
        return unitEvaluationName;
    }

    public void setUnitEvaluationName(String unitEvaluationName) {
        this.unitEvaluationName = unitEvaluationName;
    }

    public String getReportUserWorkName() {
        return reportUserWorkName;
    }

    public void setReportUserWorkName(String reportUserWorkName) {
        this.reportUserWorkName = reportUserWorkName;
    }
}