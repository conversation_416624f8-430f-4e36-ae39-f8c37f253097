package com.bjgoodwill.hip.ds.cis.adv.reportApproval.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.AdvEventsStatusEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "不良事件报告审批")
public class CisAdvEventReportApprovalEto  extends BaseEto implements Serializable {

	@Serial
    private static final long serialVersionUID = -5325980688820894651L;

    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "主管部门")
    private String opinionOrgCode;
    @Schema(description = "主管部门名称")
    private String opinionOrgName;
    @Schema(description = "主管部门意见陈述")
    private String opinionState;
    @Schema(description = "主管部门意见陈述人")
    private String opinionStateUser;
    @Schema(description = "主管部门意见陈述人名称")
    private String opinionStateUserName;
    @Schema(description = "主管部门意见陈述时间")
    private LocalDateTime opinionStateDate;
    @Schema(description = "事件总结(转发人填写)")
    private String eventConclusion;
    @Schema(description = "状态")
    private AdvEventsStatusEnum statusCode;

    @Size(max = 50, message = "不良事件id长度不能超过50个字符！")
    public String getEventReportId() {
    	return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = StringUtils.trimToNull(eventReportId);
    }

    @Size(max = 16, message = "主管部门长度不能超过16个字符！")
    public String getOpinionOrgCode() {
    	return opinionOrgCode;
    }

    public void setOpinionOrgCode(String opinionOrgCode) {
        this.opinionOrgCode = StringUtils.trimToNull(opinionOrgCode);
    }

    public String getOpinionState() {
    	return opinionState;
    }

    public void setOpinionState(String opinionState) {
        this.opinionState = StringUtils.trimToNull(opinionState);
    }

    @Size(max = 16, message = "主管部门意见陈述人长度不能超过16个字符！")
    public String getOpinionStateUser() {
    	return opinionStateUser;
    }

    public void setOpinionStateUser(String opinionStateUser) {
        this.opinionStateUser = StringUtils.trimToNull(opinionStateUser);
    }

    public LocalDateTime getOpinionStateDate() {
    	return opinionStateDate;
    }

    public void setOpinionStateDate(LocalDateTime opinionStateDate) {
        this.opinionStateDate = opinionStateDate;
    }

    public String getEventConclusion() {
    	return eventConclusion;
    }

    public void setEventConclusion(String eventConclusion) {
        this.eventConclusion = StringUtils.trimToNull(eventConclusion);
    }

    public AdvEventsStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(AdvEventsStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public String getOpinionOrgName() {
        return opinionOrgName;
    }

    public void setOpinionOrgName(String opinionOrgName) {
        this.opinionOrgName = opinionOrgName;
    }

    public String getOpinionStateUserName() {
        return opinionStateUserName;
    }

    public void setOpinionStateUserName(String opinionStateUserName) {
        this.opinionStateUserName = opinionStateUserName;
    }
}