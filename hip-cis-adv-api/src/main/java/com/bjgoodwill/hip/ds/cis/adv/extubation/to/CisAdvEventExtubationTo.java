package com.bjgoodwill.hip.ds.cis.adv.extubation.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "非计划拔管事件上报表")
public class CisAdvEventExtubationTo implements Serializable {

	@Serial
    private static final long serialVersionUID = -5012493414097700792L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "非计划拔管类型：导尿管；气管导管；picc；cvc；胃肠管（经口鼻）")
    private String unplannedType;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "床号")
    private String bedName;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区科室名称")
    private String areaName;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "事件发生场所：in病区内，out病区外（院内）")
    private String eventPlace;
    @Schema(description = "事件发生场所：in病区内，out病区外（院内）")
    private String eventPlaceName;
    @Schema(description = "该患者本次住院非计划拔管次数：1第一次；2第二次；3第三次；4大于3次")
    private Integer extubationNum;
    @Schema(description = "非计划拔管原因: oneself 患者自拔；slipping管路滑落；block阻塞；infected感染；material材质问题；other其他；")
    private String extubationReasons;
    @Schema(description = "非计划拔管原因名称: oneself 患者自拔；slipping管路滑落；block阻塞；infected感染；material材质问题；other其他；")
    private String extubationReasonsName;
    @Schema(description = "是否重置:0否；1是")
    private boolean resetFlag;
    @Schema(description = "重置时间：1非计划拔管后24小时内（含24小时）；2非计划拔管24小时后")
    private String reset24timeFlag;
    @Schema(description = "非计划拔管时有无约束：0否；1是；")
    private boolean constraintFlag;
    @Schema(description = "非计划拔管时患者状态：bedridden卧床时；turnover翻身时；passingbed过床时；transport转运时；inspect检查时；other其他；")
    private String patientState;
    @Schema(description = "非计划拔管时患者状态名称：bedridden卧床时；turnover翻身时；passingbed过床时；transport转运时；inspect检查时；other其他；")
    private String patientStateName;
    @Schema(description = "非计划拔管时患者神志：0不清醒；1清醒；")
    private boolean mindFlag;
    @Schema(description = "非计划拔管时患者是否镇静：0否；1是；2不知道")
    private String calmFlag;
    @Schema(description = "风险评估工具:rass(richmond 躁动-镇静评分)，sas（镇静-躁动评分），其他量表，未评估")
    private String assessmentTool;
    @Schema(description = "非计划拔管时患者pass评分（richmond躁动-镇静量表）：4，3，2，1，0，-1，-2，-3，-4，-5，其他量表，未评估")
    private String gaugeScore;
    @Schema(description = "其他量表名称")
    private String otherGauge;
    @Schema(description = "其他量表分值")
    private Integer otherGaugeScore;
    @Schema(description = "非计划拔管发生时当班责任护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    private String workingLife;
    @Schema(description = "非计划拔管发生时当班责任护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    private String workingLifeName;
    @Schema(description = "非计划拔管发生时在岗责任护士人数")
    private Integer dutyNum;
    @Schema(description = "非计划拔管发生时病区在院患者数")
    private Integer areaNum;
    @Schema(description = "整改措施")
    private String improvementMeasures;
    @Schema(description = "护士长签名")
    private String headSignature;
    @Schema(description = "护士长签名名称")
    private String headSignatureName;
    @Schema(description = "护士长填写时间")
    private LocalDateTime headSignatureDate;
    @Schema(description = "护理部意见")
    private String nursDeptOpinion;
    @Schema(description = "护理部签名")
    private String nursDeptSignature;
    @Schema(description = "护理部签名名称")
    private String nursDeptSignatureName;
    @Schema(description = "护理部填写时间")
    private LocalDateTime deptSignatureDate;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;

    public String getId() {
    	return id;
    }

    public void setId(String id) {
    	this.id = id;
    }

    public String getEventReportId() {
    	return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
    	this.eventReportId = eventReportId;
    }

    public String getUnplannedType() {
    	return unplannedType;
    }

    public void setUnplannedType(String unplannedType) {
    	this.unplannedType = unplannedType;
    }

    public String getPatType() {
    	return patType;
    }

    public void setPatType(String patType) {
    	this.patType = patType;
    }

    public String getInpatientCode() {
    	return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
    	this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    public void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public String getPatName() {
    	return patName;
    }

    public void setPatName(String patName) {
    	this.patName = patName;
    }

    public String getSex() {
    	return sex;
    }

    public void setSex(String sex) {
    	this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
    	this.birthDate = birthDate;
    }

    public String getBedName() {
    	return bedName;
    }

    public void setBedName(String bedName) {
    	this.bedName = bedName;
    }

    public String getAreaCode() {
    	return areaCode;
    }

    public void setAreaCode(String areaCode) {
    	this.areaCode = areaCode;
    }

    public String getAreaName() {
    	return areaName;
    }

    public void setAreaName(String areaName) {
    	this.areaName = areaName;
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
    	this.eventDate = eventDate;
    }

    public String getEventPlace() {
    	return eventPlace;
    }

    public void setEventPlace(String eventPlace) {
    	this.eventPlace = eventPlace;
    }

    public String getEventPlaceName() {
    	return eventPlaceName;
    }

    public void setEventPlaceName(String eventPlaceName) {
    	this.eventPlaceName = eventPlaceName;
    }

    public Integer getExtubationNum() {
    	return extubationNum;
    }

    public void setExtubationNum(Integer extubationNum) {
    	this.extubationNum = extubationNum;
    }

    public String getExtubationReasons() {
    	return extubationReasons;
    }

    public void setExtubationReasons(String extubationReasons) {
    	this.extubationReasons = extubationReasons;
    }

    public String getExtubationReasonsName() {
    	return extubationReasonsName;
    }

    public void setExtubationReasonsName(String extubationReasonsName) {
    	this.extubationReasonsName = extubationReasonsName;
    }

    public boolean isResetFlag() {
    	return resetFlag;
    }

    public void setResetFlag(boolean resetFlag) {
    	this.resetFlag = resetFlag;
    }

    public String getReset24timeFlag() {
    	return reset24timeFlag;
    }

    public void setReset24timeFlag(String reset24timeFlag) {
    	this.reset24timeFlag = reset24timeFlag;
    }

    public boolean isConstraintFlag() {
    	return constraintFlag;
    }

    public void setConstraintFlag(boolean constraintFlag) {
    	this.constraintFlag = constraintFlag;
    }

    public String getPatientState() {
    	return patientState;
    }

    public void setPatientState(String patientState) {
    	this.patientState = patientState;
    }

    public String getPatientStateName() {
    	return patientStateName;
    }

    public void setPatientStateName(String patientStateName) {
    	this.patientStateName = patientStateName;
    }

    public boolean isMindFlag() {
    	return mindFlag;
    }

    public void setMindFlag(boolean mindFlag) {
    	this.mindFlag = mindFlag;
    }

    public String getCalmFlag() {
    	return calmFlag;
    }

    public void setCalmFlag(String calmFlag) {
    	this.calmFlag = calmFlag;
    }

    public String getAssessmentTool() {
    	return assessmentTool;
    }

    public void setAssessmentTool(String assessmentTool) {
    	this.assessmentTool = assessmentTool;
    }

    public String getGaugeScore() {
    	return gaugeScore;
    }

    public void setGaugeScore(String gaugeScore) {
    	this.gaugeScore = gaugeScore;
    }

    public String getOtherGauge() {
    	return otherGauge;
    }

    public void setOtherGauge(String otherGauge) {
    	this.otherGauge = otherGauge;
    }

    public Integer getOtherGaugeScore() {
    	return otherGaugeScore;
    }

    public void setOtherGaugeScore(Integer otherGaugeScore) {
    	this.otherGaugeScore = otherGaugeScore;
    }

    public String getWorkingLife() {
    	return workingLife;
    }

    public void setWorkingLife(String workingLife) {
    	this.workingLife = workingLife;
    }

    public String getWorkingLifeName() {
    	return workingLifeName;
    }

    public void setWorkingLifeName(String workingLifeName) {
    	this.workingLifeName = workingLifeName;
    }

    public Integer getDutyNum() {
    	return dutyNum;
    }

    public void setDutyNum(Integer dutyNum) {
    	this.dutyNum = dutyNum;
    }

    public Integer getAreaNum() {
    	return areaNum;
    }

    public void setAreaNum(Integer areaNum) {
    	this.areaNum = areaNum;
    }

    public String getImprovementMeasures() {
    	return improvementMeasures;
    }

    public void setImprovementMeasures(String improvementMeasures) {
    	this.improvementMeasures = improvementMeasures;
    }

    public String getHeadSignature() {
    	return headSignature;
    }

    public void setHeadSignature(String headSignature) {
    	this.headSignature = headSignature;
    }

    public String getHeadSignatureName() {
    	return headSignatureName;
    }

    public void setHeadSignatureName(String headSignatureName) {
    	this.headSignatureName = headSignatureName;
    }

    public LocalDateTime getHeadSignatureDate() {
    	return headSignatureDate;
    }

    public void setHeadSignatureDate(LocalDateTime headSignatureDate) {
    	this.headSignatureDate = headSignatureDate;
    }

    public String getNursDeptOpinion() {
    	return nursDeptOpinion;
    }

    public void setNursDeptOpinion(String nursDeptOpinion) {
    	this.nursDeptOpinion = nursDeptOpinion;
    }

    public String getNursDeptSignature() {
    	return nursDeptSignature;
    }

    public void setNursDeptSignature(String nursDeptSignature) {
    	this.nursDeptSignature = nursDeptSignature;
    }

    public String getNursDeptSignatureName() {
    	return nursDeptSignatureName;
    }

    public void setNursDeptSignatureName(String nursDeptSignatureName) {
    	this.nursDeptSignatureName = nursDeptSignatureName;
    }

    public LocalDateTime getDeptSignatureDate() {
    	return deptSignatureDate;
    }

    public void setDeptSignatureDate(LocalDateTime deptSignatureDate) {
    	this.deptSignatureDate = deptSignatureDate;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventExtubationTo other = (CisAdvEventExtubationTo) obj;
		return Objects.equals(id, other.id);
	}
}