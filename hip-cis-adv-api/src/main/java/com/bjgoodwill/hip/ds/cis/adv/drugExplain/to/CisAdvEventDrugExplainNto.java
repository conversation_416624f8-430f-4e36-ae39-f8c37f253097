package com.bjgoodwill.hip.ds.cis.adv.drugExplain.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "不良事件药品说明")
public class CisAdvEventDrugExplainNto  extends BaseNto implements Serializable {

	@Serial
    private static final long serialVersionUID = -5765727957692642378L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "药品不良事件id")
    private String eventDrugId;
    @Schema(description = "类型：doubt怀疑；together并用")
    private String useDrugType;
    @Schema(description = "批准文号（国药准字）")
    private String approvalDoc;
    @Schema(description = "商品编码")
    private String drugGoodsCode;
    @Schema(description = "商品名称")
    private String drugGoodsName;
    @Schema(description = "通用名")
    private String commonName;
    @Schema(description = "剂型")
    private String dosageform;
    @Schema(description = "生产厂家")
    private String manufactureFirm;
    @Schema(description = "生产批号")
    private String batchNo;
    @Schema(description = "用法用量（每次量 途径 频次）")
    private String usageDosage;
    @Schema(description = "开始时间")
    private LocalDateTime effectiveTimeLow;
    @Schema(description = "终止时间")
    private LocalDateTime effectiveTimeHigh;
    @Schema(description = "用药原因")
    private String reasonsMedication;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
    	return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @Size(max = 50, message = "药品不良事件id长度不能超过50个字符！")
    public String getEventDrugId() {
    	return eventDrugId;
    }

    public void setEventDrugId(String eventDrugId) {
        this.eventDrugId = StringUtils.trimToNull(eventDrugId);
    }

    @Size(max = 16, message = "类型：doubt怀疑；together并用长度不能超过16个字符！")
    public String getUseDrugType() {
    	return useDrugType;
    }

    public void setUseDrugType(String useDrugType) {
        this.useDrugType = StringUtils.trimToNull(useDrugType);
    }

    public String getApprovalDoc() {
    	return approvalDoc;
    }

    public void setApprovalDoc(String approvalDoc) {
        this.approvalDoc = StringUtils.trimToNull(approvalDoc);
    }

    @Size(max = 50, message = "商品编码长度不能超过50个字符！")
    public String getDrugGoodsCode() {
    	return drugGoodsCode;
    }

    public void setDrugGoodsCode(String drugGoodsCode) {
        this.drugGoodsCode = StringUtils.trimToNull(drugGoodsCode);
    }

    @Size(max = 50, message = "商品名称长度不能超过50个字符！")
    public String getDrugGoodsName() {
    	return drugGoodsName;
    }

    public void setDrugGoodsName(String drugGoodsName) {
        this.drugGoodsName = StringUtils.trimToNull(drugGoodsName);
    }

    public String getCommonName() {
    	return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = StringUtils.trimToNull(commonName);
    }

    @Size(max = 50, message = "剂型长度不能超过50个字符！")
    public String getDosageform() {
    	return dosageform;
    }

    public void setDosageform(String dosageform) {
        this.dosageform = StringUtils.trimToNull(dosageform);
    }

    public String getManufactureFirm() {
    	return manufactureFirm;
    }

    public void setManufactureFirm(String manufactureFirm) {
        this.manufactureFirm = StringUtils.trimToNull(manufactureFirm);
    }

    @Size(max = 16, message = "生产批号长度不能超过16个字符！")
    public String getBatchNo() {
    	return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = StringUtils.trimToNull(batchNo);
    }

    @Size(max = 128, message = "用法用量（每次量 途径 频次）长度不能超过128个字符！")
    public String getUsageDosage() {
    	return usageDosage;
    }

    public void setUsageDosage(String usageDosage) {
        this.usageDosage = StringUtils.trimToNull(usageDosage);
    }

    public LocalDateTime getEffectiveTimeLow() {
    	return effectiveTimeLow;
    }

    public void setEffectiveTimeLow(LocalDateTime effectiveTimeLow) {
        this.effectiveTimeLow = effectiveTimeLow;
    }

    public LocalDateTime getEffectiveTimeHigh() {
    	return effectiveTimeHigh;
    }

    public void setEffectiveTimeHigh(LocalDateTime effectiveTimeHigh) {
        this.effectiveTimeHigh = effectiveTimeHigh;
    }

    public String getReasonsMedication() {
    	return reasonsMedication;
    }

    public void setReasonsMedication(String reasonsMedication) {
        this.reasonsMedication = StringUtils.trimToNull(reasonsMedication);
    }
}