package com.bjgoodwill.hip.ds.cis.adv.cauti.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.time.LocalDateTime;

@Schema(description = "导尿管相关尿路感染（CAUTI）相关信息收集表")
public class CisAdvEventCautiEto extends BaseEto {

    @Serial
    private static final long serialVersionUID = -4454250382932410704L;

    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "年龄范围: 新生儿、1-6月、7-12月、1-6岁、7-12岁、13-18岁、19-64岁、65岁及以上、无法确定")
    private String ageRange;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区名称")
    private String areaName;
    @Schema(description = "入院时间")
    private LocalDateTime inDate;
    @Schema(description = "留置导尿管的主要原因： 昏迷或精神异常无法自行排尿unabletourinate；尿潴留urinaryretention；尿失禁urinaryincontinence；监测尿量monitorurinevolume；近期有手术operation；骶尾部或会阴部有开放性伤口openwounds；other其他")
    private String indwellReason;
    @Schema(description = "留置导尿管的主要原因名称： 昏迷或精神异常无法自行排尿unabletourinate；尿潴留urinaryretention；尿失禁urinaryincontinence；监测尿量monitorurinevolume；近期有手术operation；骶尾部或会阴部有开放性伤口openwounds；other其他")
    private String indwellReasonName;
    @Schema(description = "导尿管型号：6f  ；8f  ；10f  ；12f  ；14f  ；16f  ；18f  ；20f ；22f  ；24f")
    private String captheterModel;
    @Schema(description = "导尿管型号名称：6f  ；8f  ；10f  ；12f  ；14f  ；16f  ；18f  ；20f ；22f  ；24f")
    private String captheterModelName;
    @Schema(description = "导尿管类型：普通导尿管general；双腔气囊导尿管doublelumenballoon；三腔气囊导尿管threechamberballoon")
    private String captheterType;
    @Schema(description = "导尿管类型名称：普通导尿管general；双腔气囊导尿管doublelumenballoon；三腔气囊导尿管threechamberballoon")
    private String captheterTypeName;
    @Schema(description = "导管材质：乳胶latex ；硅胶silicagel；其他other")
    private String extubationQuality;
    @Schema(description = "导管材质名称：乳胶latex ；硅胶silicagel；其他other")
    private String extubationQualityName;
    @Schema(description = "是否使用抗返流集尿装置： 1是 ；0否")
    private boolean useDeviceFlag;
    @Schema(description = "发生cauti前是否有膀胱冲洗：1是；0否")
    private boolean bladderIrrigationFlag;
    @Schema(description = "发生cauti时导尿管留置时长：天")
    private Integer cautiTime;

    @Size(max = 50, message = "不良事件id长度不能超过50个字符！")
    public String getEventReportId() {
        return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = StringUtils.trimToNull(eventReportId);
    }

    @Size(max = 2, message = "患者类型长度不能超过2个字符！")
    public String getPatType() {
        return patType;
    }

    public void setPatType(String patType) {
        this.patType = StringUtils.trimToNull(patType);
    }

    @Size(max = 16, message = "住院号(门诊就诊卡号)长度不能超过16个字符！")
    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = StringUtils.trimToNull(inpatientCode);
    }

    @Size(max = 16, message = "就诊流水号长度不能超过16个字符！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @Size(max = 64, message = "患者姓名长度不能超过64个字符！")
    public String getPatName() {
        return patName;
    }

    public void setPatName(String patName) {
        this.patName = StringUtils.trimToNull(patName);
    }

    @Size(max = 16, message = "性别长度不能超过16个字符！")
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = StringUtils.trimToNull(sex);
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    @Size(max = 64, message = "年龄范围: 新生儿、1-6月、7-12月、1-6岁、7-12岁、13-18岁、19-64岁、65岁及以上、无法确定长度不能超过64个字符！")
    public String getAgeRange() {
        return ageRange;
    }

    public void setAgeRange(String ageRange) {
        this.ageRange = StringUtils.trimToNull(ageRange);
    }

    @Size(max = 16, message = "病区科室长度不能超过16个字符！")
    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = StringUtils.trimToNull(areaCode);
    }

    @Size(max = 64, message = "病区名称长度不能超过64个字符！")
    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = StringUtils.trimToNull(areaName);
    }

    public LocalDateTime getInDate() {
        return inDate;
    }

    public void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    public String getIndwellReason() {
        return indwellReason;
    }

    public void setIndwellReason(String indwellReason) {
        this.indwellReason = StringUtils.trimToNull(indwellReason);
    }

    public String getIndwellReasonName() {
        return indwellReasonName;
    }

    public void setIndwellReasonName(String indwellReasonName) {
        this.indwellReasonName = StringUtils.trimToNull(indwellReasonName);
    }

    @Size(max = 16, message = "导尿管型号：6f  ；8f  ；10f  ；12f  ；14f  ；16f  ；18f  ；20f ；22f  ；24f长度不能超过16个字符！")
    public String getCaptheterModel() {
        return captheterModel;
    }

    public void setCaptheterModel(String captheterModel) {
        this.captheterModel = StringUtils.trimToNull(captheterModel);
    }

    @Size(max = 32, message = "导尿管型号名称：6f  ；8f  ；10f  ；12f  ；14f  ；16f  ；18f  ；20f ；22f  ；24f长度不能超过32个字符！")
    public String getCaptheterModelName() {
        return captheterModelName;
    }

    public void setCaptheterModelName(String captheterModelName) {
        this.captheterModelName = StringUtils.trimToNull(captheterModelName);
    }

    @Size(max = 64, message = "导尿管类型：普通导尿管general；双腔气囊导尿管doublelumenballoon；三腔气囊导尿管threechamberballoon长度不能超过64个字符！")
    public String getCaptheterType() {
        return captheterType;
    }

    public void setCaptheterType(String captheterType) {
        this.captheterType = StringUtils.trimToNull(captheterType);
    }

    @Size(max = 128, message = "导尿管类型名称：普通导尿管general；双腔气囊导尿管doublelumenballoon；三腔气囊导尿管threechamberballoon长度不能超过128个字符！")
    public String getCaptheterTypeName() {
        return captheterTypeName;
    }

    public void setCaptheterTypeName(String captheterTypeName) {
        this.captheterTypeName = StringUtils.trimToNull(captheterTypeName);
    }

    @Size(max = 64, message = "导管材质：乳胶latex ；硅胶silicagel；其他other长度不能超过64个字符！")
    public String getExtubationQuality() {
        return extubationQuality;
    }

    public void setExtubationQuality(String extubationQuality) {
        this.extubationQuality = StringUtils.trimToNull(extubationQuality);
    }

    @Size(max = 64, message = "导管材质名称：乳胶latex ；硅胶silicagel；其他other长度不能超过64个字符！")
    public String getExtubationQualityName() {
        return extubationQualityName;
    }

    public void setExtubationQualityName(String extubationQualityName) {
        this.extubationQualityName = StringUtils.trimToNull(extubationQualityName);
    }

    public boolean isUseDeviceFlag() {
        return useDeviceFlag;
    }

    public void setUseDeviceFlag(boolean useDeviceFlag) {
        this.useDeviceFlag = useDeviceFlag;
    }

    public boolean isBladderIrrigationFlag() {
        return bladderIrrigationFlag;
    }

    public void setBladderIrrigationFlag(boolean bladderIrrigationFlag) {
        this.bladderIrrigationFlag = bladderIrrigationFlag;
    }

    public Integer getCautiTime() {
        return cautiTime;
    }

    public void setCautiTime(Integer cautiTime) {
        this.cautiTime = cautiTime;
    }
}