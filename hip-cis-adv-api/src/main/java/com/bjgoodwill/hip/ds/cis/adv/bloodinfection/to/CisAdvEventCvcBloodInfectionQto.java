package com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.time.LocalDateTime;

@Schema(description = "CVC相关血流感染相关信息收集表")
public class CisAdvEventCvcBloodInfectionQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -5047959050071174242L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "年龄范围: 新生儿、1-6月、7-12月、1-6岁、7-12岁、13-18岁、19-64岁、65岁及以上、无法确定")
    private String ageRange;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区科室名称")
    private String areaName;
    @Schema(description = "入院时间")
    private LocalDateTime inDate;
    @Schema(description = "留置导管的主要原因：输入高渗液体hypertonicfluid，输入化疗药物chemotherapydrugs，长期输液longterminfusion，抢救和监测需要rescueandmnitorin，其他other")
    private String indwellReason;
    @Schema(description = "留置导管的主要原因：输入高渗液体hypertonicfluid，输入化疗药物chemotherapydrugs，长期输液longterminfusion，抢救和监测需要rescueandmnitorin，其他other")
    private String indwellReasonName;
    @Schema(description = "cvc置管位置：锁骨下静脉subclavian，颈内静脉internaljugular，股静脉femoral")
    private String cvcLocation;
    @Schema(description = "cvc置管位置：锁骨下静脉subclavian，颈内静脉internaljugular，股静脉femoral")
    private String cvcLocationName;
    @Schema(description = "导管类型：单腔导管endotrachealintubation；双腔导管doublelumen；三腔导管threelumen")
    private String extubationType;
    @Schema(description = "导管类型名称：单腔导管endotrachealintubation；双腔导管doublelumen；三腔导管threelumen")
    private String extubationTypeName;
    @Schema(description = "是否为抗菌导管：1是，0否")
    private Boolean antibacterialFlag;
    @Schema(description = "发生clabsi时cvc留置时长：天")
    private Integer cvcTime;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getEventReportId() {
        return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = eventReportId;
    }

    public String getPatType() {
        return patType;
    }

    public void setPatType(String patType) {
        this.patType = patType;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatName() {
        return patName;
    }

    public void setPatName(String patName) {
        this.patName = patName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getAgeRange() {
        return ageRange;
    }

    public void setAgeRange(String ageRange) {
        this.ageRange = ageRange;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public LocalDateTime getInDate() {
        return inDate;
    }

    public void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    public String getIndwellReason() {
        return indwellReason;
    }

    public void setIndwellReason(String indwellReason) {
        this.indwellReason = indwellReason;
    }

    public String getIndwellReasonName() {
        return indwellReasonName;
    }

    public void setIndwellReasonName(String indwellReasonName) {
        this.indwellReasonName = indwellReasonName;
    }

    public String getCvcLocation() {
        return cvcLocation;
    }

    public void setCvcLocation(String cvcLocation) {
        this.cvcLocation = cvcLocation;
    }

    public String getCvcLocationName() {
        return cvcLocationName;
    }

    public void setCvcLocationName(String cvcLocationName) {
        this.cvcLocationName = cvcLocationName;
    }

    public String getExtubationType() {
        return extubationType;
    }

    public void setExtubationType(String extubationType) {
        this.extubationType = extubationType;
    }

    public String getExtubationTypeName() {
        return extubationTypeName;
    }

    public void setExtubationTypeName(String extubationTypeName) {
        this.extubationTypeName = extubationTypeName;
    }

    public Boolean getAntibacterialFlag() {
        return antibacterialFlag;
    }

    public void setAntibacterialFlag(Boolean antibacterialFlag) {
        this.antibacterialFlag = antibacterialFlag;
    }

    public Integer getCvcTime() {
        return cvcTime;
    }

    public void setCvcTime(Integer cvcTime) {
        this.cvcTime = cvcTime;
    }
}