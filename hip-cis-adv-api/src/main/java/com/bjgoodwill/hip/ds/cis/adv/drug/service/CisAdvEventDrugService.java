package com.bjgoodwill.hip.ds.cis.adv.drug.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.drug.to.CisAdvEventDrugEto;
import com.bjgoodwill.hip.ds.cis.adv.drug.to.CisAdvEventDrugNto;
import com.bjgoodwill.hip.ds.cis.adv.drug.to.CisAdvEventDrugQto;
import com.bjgoodwill.hip.ds.cis.adv.drug.to.CisAdvEventDrugTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "药品不良反应事件报告表领域服务", description = "药品不良反应事件报告表领域服务")
public interface CisAdvEventDrugService {

    @Operation(summary = "根据查询条件对药品不良反应事件报告表进行查询。")
    @GetMapping("/cisAdvEventDrugs")
    List<CisAdvEventDrugTo> getCisAdvEventDrugs(@ParameterObject @SpringQueryMap CisAdvEventDrugQto cisAdvEventDrugQto);

    @Operation(summary = "根据查询条件对药品不良反应事件报告表进行分页查询。")
    @GetMapping("/cisAdvEventDrugs/pages")
    GridResultSet<CisAdvEventDrugTo> getCisAdvEventDrugPage(@ParameterObject @SpringQueryMap CisAdvEventDrugQto cisAdvEventDrugQto);

    @Operation(summary = "根据唯一标识返回药品不良反应事件报告表。")
    @GetMapping("/cisAdvEventDrugs/{id:.+}")
    CisAdvEventDrugTo getCisAdvEventDrugById(@PathVariable("id") String id);

    @Operation(summary = "创建药品不良反应事件报告表。")
    @PostMapping("/cisAdvEventDrugs")
    CisAdvEventDrugTo createCisAdvEventDrug(@RequestBody @Valid CisAdvEventDrugNto cisAdvEventDrugNto);

    @Operation(summary = "根据唯一标识修改药品不良反应事件报告表。")
    @PutMapping("/cisAdvEventDrugs/{id:.+}")
    void updateCisAdvEventDrug(@PathVariable("id") String id, @RequestBody @Valid CisAdvEventDrugEto cisAdvEventDrugEto);

    @Operation(summary = "根据唯一标识删除药品不良反应事件报告表。")
    @DeleteMapping("/cisAdvEventDrugs/{id:.+}")
    void deleteCisAdvEventDrug(@PathVariable("id") String id);

}