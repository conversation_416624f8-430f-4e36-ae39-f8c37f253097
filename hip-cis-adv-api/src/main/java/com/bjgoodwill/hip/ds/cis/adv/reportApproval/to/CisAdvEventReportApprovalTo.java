package com.bjgoodwill.hip.ds.cis.adv.reportApproval.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.AdvEventsStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "不良事件报告审批")
public class CisAdvEventReportApprovalTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1826580655318295111L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "主管部门")
    private String opinionOrgCode;
    @Schema(description = "主管部门名称")
    private String opinionOrgName;
    @Schema(description = "主管部门意见陈述")
    private String opinionState;
    @Schema(description = "主管部门意见陈述人")
    private String opinionStateUser;
    @Schema(description = "主管部门意见陈述人名称")
    private String opinionStateUserName;
    @Schema(description = "主管部门意见陈述时间")
    private LocalDateTime opinionStateDate;
    @Schema(description = "事件总结(转发人填写)")
    private String eventConclusion;
    @Schema(description = "状态")
    private AdvEventsStatusEnum statusCode;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEventReportId() {
        return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = eventReportId;
    }

    public String getOpinionOrgCode() {
        return opinionOrgCode;
    }

    public void setOpinionOrgCode(String opinionOrgCode) {
        this.opinionOrgCode = opinionOrgCode;
    }

    public String getOpinionState() {
        return opinionState;
    }

    public void setOpinionState(String opinionState) {
        this.opinionState = opinionState;
    }

    public String getOpinionStateUser() {
        return opinionStateUser;
    }

    public void setOpinionStateUser(String opinionStateUser) {
        this.opinionStateUser = opinionStateUser;
    }

    public LocalDateTime getOpinionStateDate() {
        return opinionStateDate;
    }

    public void setOpinionStateDate(LocalDateTime opinionStateDate) {
        this.opinionStateDate = opinionStateDate;
    }

    public String getEventConclusion() {
        return eventConclusion;
    }

    public void setEventConclusion(String eventConclusion) {
        this.eventConclusion = eventConclusion;
    }

    public AdvEventsStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(AdvEventsStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public String getOpinionOrgName() {
        return opinionOrgName;
    }

    public void setOpinionOrgName(String opinionOrgName) {
        this.opinionOrgName = opinionOrgName;
    }

    public String getOpinionStateUserName() {
        return opinionStateUserName;
    }

    public void setOpinionStateUserName(String opinionStateUserName) {
        this.opinionStateUserName = opinionStateUserName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisAdvEventReportApprovalTo other = (CisAdvEventReportApprovalTo) obj;
        return Objects.equals(id, other.id);
    }
}