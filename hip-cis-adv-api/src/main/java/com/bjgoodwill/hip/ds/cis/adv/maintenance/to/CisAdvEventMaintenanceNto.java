package com.bjgoodwill.hip.ds.cis.adv.maintenance.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "不良事件维护")
public class CisAdvEventMaintenanceNto  extends BaseNto implements Serializable {

	@Serial
    private static final long serialVersionUID = -5187672678191970568L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "上级目录编码")
    private String parentCode;
    @Schema(description = "不良事件编码")
    private String eventCode;
    @Schema(description = "不良事件名称")
    private String eventName;
    @Schema(description = "不良事件名称")
    private Integer eventLevel;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
    	return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @Size(max = 16, message = "上级目录编码长度不能超过16个字符！")
    public String getParentCode() {
    	return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = StringUtils.trimToNull(parentCode);
    }

    @Size(max = 16, message = "不良事件编码长度不能超过16个字符！")
    public String getEventCode() {
    	return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = StringUtils.trimToNull(eventCode);
    }

    @Size(max = 128, message = "不良事件名称长度不能超过128个字符！")
    public String getEventName() {
    	return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = StringUtils.trimToNull(eventName);
    }

    public Integer getEventLevel() {
    	return eventLevel;
    }

    public void setEventLevel(Integer eventLevel) {
        this.eventLevel = eventLevel;
    }
}