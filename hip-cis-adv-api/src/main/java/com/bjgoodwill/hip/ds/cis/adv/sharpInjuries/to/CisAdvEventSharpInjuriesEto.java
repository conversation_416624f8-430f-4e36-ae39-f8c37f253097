package com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.time.LocalDateTime;

@Schema(description = "护士锐器伤相关信息收集表")
public class CisAdvEventSharpInjuriesEto extends BaseEto {

    @Serial
    private static final long serialVersionUID = -5728247791402992543L;

    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区名称")
    private String areaName;
    @Schema(description = "发生地点：imp住院病区（跳转至1a）；unimp非住院病区(跳转至1b)")
    private String eventPlace;
    @Schema(description = "发生地点名称：imp住院病区（跳转至1a）；unimp非住院病区(跳转至1b)")
    private String eventPlaceName;
    @Schema(description = "非住院病区部门（或科室）名称（待定）（单选）：急诊emer；门诊amb ；手术室operationroom；血液净化中心bloodpurificationcentre；内镜中心endoscopiccenter；口腔科dentaldepartment；放射科radiologydepartment；体检中心medicalexaminationcenter；其他other")
    private String unimpOrgCode;
    @Schema(description = "非住院病区部门名称（或科室）名称（待定）（单选）：急诊emer；门诊amb ；手术室operationroom；血液净化中心bloodpurificationcentre；内镜中心endoscopiccenter；口腔科dentaldepartment；放射科radiologydepartment；体检中心medicalexaminationcenter；其他other")
    private String unimpOrgName;
    @Schema(description = "人员类别（单选）：本院执业护士（不包含新入职护士） licensed；新入职护士new；进修护士refresher；实习护士internship")
    private String staffType;
    @Schema(description = "人员类别名称（单选）：本院执业护士（不包含新入职护士） licensed；新入职护士new；进修护士refresher；实习护士internship")
    private String staffTypeName;
    @Schema(description = "工作（或实习）年限（单选）（备注：本院执业护士、新入职护士、进修护士选择从事护理工作年限。应届实习护士选择＜1年，非应届实习护士选择实际从事护理工作年限）： y＜1年； 1≤y＜2年； 2≤y＜5年；5≤y＜10年；10≤y＜20年； y≥20年 ")
    private String workingLife;
    @Schema(description = "工作（或实习）年限名称（单选）（备注：本院执业护士、新入职护士、进修护士选择从事护理工作年限。应届实习护士选择＜1年，非应届实习护士选择实际从事护理工作年限）： y＜1年； 1≤y＜2年； 2≤y＜5年；5≤y＜10年；10≤y＜20年； y≥20年 ")
    private String workingLifeName;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "锐器伤发生方式（单选）：自伤self；他人误伤accidental；其他other")
    private String eventType;
    @Schema(description = "锐器伤发生方式名称（单选）：自伤self；他人误伤accidental；其他other")
    private String eventTypeName;
    @Schema(description = "锐器伤所涉及的具体器具（备注：安全型器具：锐器通过安全性设计变为使用后屏蔽锐器或者没有锐器的装置即为安全型器具）（单选）：头皮钢针scalpneedle ；安全型静脉留置针safetyindwellingneedle；非安全型静脉留置针unsafetyindwellingneedle；安全型一次性注射器针头safetysyringeneedle；非安全型一次性注射器针头unsafetysyringeneedle；安全型静脉采血针safetysamplingneedle；非安全型静脉采血针unsafetysamplingneedle；安全型输液港针safetyinfusionneedle；非安全型输液港针unsafetyinfusionneedle；中心静脉导管穿刺针centralcatheterpunctureneedle；安全型动脉采血器safetyarterialbloodsampler；非安全型动脉采血器unsafetyarterialbloodsampler；末梢采血针peripheralcollectbloodneedle；安全型胰岛素注射笔safetyinsulininjectionpen；非安全型胰岛素注射笔safetyinsulininjectionpen；手术缝针或手术刀surgicalneedleorscalpel；剪刀scissors；安瓿瓶ampoulebottle；其他other")
    private String sharpDevice;
    @Schema(description = "锐器伤所涉及的具体器具（备注：安全型器具：锐器通过安全性设计变为使用后屏蔽锐器或者没有锐器的装置即为安全型器具）（单选）：头皮钢针scalpneedle ；安全型静脉留置针safetyindwellingneedle；非安全型静脉留置针unsafetyindwellingneedle；安全型一次性注射器针头safetysyringeneedle；非安全型一次性注射器针头unsafetysyringeneedle；安全型静脉采血针safetysamplingneedle；非安全型静脉采血针unsafetysamplingneedle；安全型输液港针safetyinfusionneedle；非安全型输液港针unsafetyinfusionneedle；中心静脉导管穿刺针centralcatheterpunctureneedle；安全型动脉采血器safetyarterialbloodsampler；非安全型动脉采血器unsafetyarterialbloodsampler；末梢采血针peripheralcollectbloodneedle；安全型胰岛素注射笔safetyinsulininjectionpen；非安全型胰岛素注射笔safetyinsulininjectionpen；手术缝针或手术刀surgicalneedleorscalpel；剪刀scissors；安瓿瓶ampoulebottle；其他other")
    private String sharpDeviceName;
    @Schema(description = "发生锐器伤时的具体操作或环节（单选）: 准备输液器/输血器infusion/transfusion；静脉穿刺venipuncture；采集血标本collectblood；注射给药injection；药液配置drugconfiguration；换输液瓶（袋）changeinfusion bottle；茂菲氏管给药murphy'stubedrug；置入导管insertcatheter；冲管或封管flushingorsealingpipe；回套针帽；分离针头separateneedle；拔针pulloutneedle ；将针头放入锐器盒putintosharpsbox；传递锐器transfersharps ；整理手术器械arrangesurgicalinstruments ；清洗器械cleaningequipment ； 清理废物cleanupwaste；其他other")
    private String sharpOperation;
    @Schema(description = "发生锐器伤时的具体操作或环节（单选）: 准备输液器/输血器infusion/transfusion；静脉穿刺venipuncture；采集血标本collectblood；注射给药injection；药液配置drugconfiguration；换输液瓶（袋）changeinfusion bottle；茂菲氏管给药murphy'stubedrug；置入导管insertcatheter；冲管或封管flushingorsealingpipe；回套针帽；分离针头separateneedle；拔针pulloutneedle ；将针头放入锐器盒putintosharpsbox；传递锐器transfersharps ；整理手术器械arrangesurgicalinstruments ；清洗器械cleaningequipment ； 清理废物cleanupwaste；其他other")
    private String sharpOperationName;
    @Schema(description = "锐器是否被污染（单选）：1是；0否（直接跳转至13题）；uncertain不确定（直接跳转至13题）")
    private String pollutionFlag;
    @Schema(description = "锐器是否被污染名称（单选）：1是；0否（直接跳转至13题）；uncertain不确定（直接跳转至13题）")
    private String pollutionFlagName;
    @Schema(description = "污染源类型（单选）：血液；体液；其他")
    private String pollutionType;
    @Schema(description = "污染源类型名称（单选）：血液；体液；其他")
    private String pollutionTypeName;
    @Schema(description = "该污染源是否含有血源性传播疾病（单选）：1是；0否（直接跳转至13题） ；  uncertain不确定（直接跳转至13题）")
    private String bloodTransmittFlag;
    @Schema(description = "该污染源是否含有血源性传播疾病名称（单选）：1是；0否（直接跳转至13题） ；  uncertain不确定（直接跳转至13题）")
    private String bloodTransmittFlagName;
    @Schema(description = "血源性传播疾病类型（单选）：hiv；乙肝hepatitisc；丙肝hcv；梅毒syphilis； 其他other；两种或两种以上类型more")
    private String bloodTransmittType;
    @Schema(description = "血源性传播疾病类型名称（单选）：hiv；乙肝hepatitisc；丙肝hcv；梅毒syphilis； 其他other；两种或两种以上类型more")
    private String bloodTransmittTypeName;
    @Schema(description = "锐器伤后是否进行了定期追踪和检测（单选）：1是（直接跳转至15题）；0否")
    private boolean trackFlag;
    @Schema(description = "未进行追踪检测的原因（单选）：自行判断后果不严重notserious  ；无相关制度和流程uncorrelated；其他原因other（选择任何一个选项后填报结束）")
    private String untrackReason;
    @Schema(description = "未进行追踪检测的原因名称（单选）：自行判断后果不严重notserious  ；无相关制度和流程uncorrelated；其他原因other（选择任何一个选项后填报结束）")
    private String untrackReasonName;
    @Schema(description = "截止到表单上报时，该事件是否导致锐器伤者确诊感染（单选）：1是；0否（填报结束）  ；waiting尚在等待检测结果（检测结果确定后请返回系统修改选项）（暂时填报结束，同时，系统预留一个口，等确定后再修改该题选项，每次登录提醒。）")
    private String confirmedFlag;
    @Schema(description = "截止到表单上报时，该事件是否导致锐器伤者确诊感染（单选）：1是；0否（填报结束）  ；waiting尚在等待检测结果（检测结果确定后请返回系统修改选项）（暂时填报结束，同时，系统预留一个口，等确定后再修改该题选项，每次登录提醒。）")
    private String confirmedFlagName;
    @Schema(description = "感染疾病类型（单选）： hiv ；乙肝hepatitisc ；丙肝hcv；梅毒syphilis  ；其他other ；两种或两种以上类型more")
    private String diseaseType;
    @Schema(description = "感染疾病类型名称（单选）： hiv ；乙肝hepatitisc ；丙肝hcv；梅毒syphilis  ；其他other ；两种或两种以上类型more")
    private String diseaseTypeName;

    @Size(max = 50, message = "不良事件id长度不能超过50个字符！")
    public String getEventReportId() {
        return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = StringUtils.trimToNull(eventReportId);
    }

    @Size(max = 2, message = "患者类型长度不能超过2个字符！")
    public String getPatType() {
        return patType;
    }

    public void setPatType(String patType) {
        this.patType = StringUtils.trimToNull(patType);
    }

    @Size(max = 16, message = "住院号(门诊就诊卡号)长度不能超过16个字符！")
    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = StringUtils.trimToNull(inpatientCode);
    }

    @Size(max = 16, message = "就诊流水号长度不能超过16个字符！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @Size(max = 64, message = "患者姓名长度不能超过64个字符！")
    public String getPatName() {
        return patName;
    }

    public void setPatName(String patName) {
        this.patName = StringUtils.trimToNull(patName);
    }

    @Size(max = 16, message = "性别长度不能超过16个字符！")
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = StringUtils.trimToNull(sex);
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    @Size(max = 16, message = "病区科室长度不能超过16个字符！")
    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = StringUtils.trimToNull(areaCode);
    }

    @Size(max = 64, message = "病区名称长度不能超过64个字符！")
    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = StringUtils.trimToNull(areaName);
    }

    @Size(max = 16, message = "发生地点：imp住院病区（跳转至1a）；unimp非住院病区(跳转至1b)长度不能超过16个字符！")
    public String getEventPlace() {
        return eventPlace;
    }

    public void setEventPlace(String eventPlace) {
        this.eventPlace = StringUtils.trimToNull(eventPlace);
    }

    @Size(max = 64, message = "发生地点名称：imp住院病区（跳转至1a）；unimp非住院病区(跳转至1b)长度不能超过64个字符！")
    public String getEventPlaceName() {
        return eventPlaceName;
    }

    public void setEventPlaceName(String eventPlaceName) {
        this.eventPlaceName = StringUtils.trimToNull(eventPlaceName);
    }

    public String getUnimpOrgCode() {
        return unimpOrgCode;
    }

    public void setUnimpOrgCode(String unimpOrgCode) {
        this.unimpOrgCode = StringUtils.trimToNull(unimpOrgCode);
    }

    public String getUnimpOrgName() {
        return unimpOrgName;
    }

    public void setUnimpOrgName(String unimpOrgName) {
        this.unimpOrgName = StringUtils.trimToNull(unimpOrgName);
    }

    @Size(max = 64, message = "人员类别（单选）：本院执业护士（不包含新入职护士） licensed；新入职护士new；进修护士refresher；实习护士internship长度不能超过64个字符！")
    public String getStaffType() {
        return staffType;
    }

    public void setStaffType(String staffType) {
        this.staffType = StringUtils.trimToNull(staffType);
    }

    @Size(max = 64, message = "人员类别名称（单选）：本院执业护士（不包含新入职护士） licensed；新入职护士new；进修护士refresher；实习护士internship长度不能超过64个字符！")
    public String getStaffTypeName() {
        return staffTypeName;
    }

    public void setStaffTypeName(String staffTypeName) {
        this.staffTypeName = StringUtils.trimToNull(staffTypeName);
    }

    @Size(max = 64, message = "工作（或实习）年限（单选）（备注：本院执业护士、新入职护士、进修护士选择从事护理工作年限。应届实习护士选择＜1年，非应届实习护士选择实际从事护理工作年限）： y＜1年； 1≤y＜2年； 2≤y＜5年；5≤y＜10年；10≤y＜20年； y≥20年 长度不能超过64个字符！")
    public String getWorkingLife() {
        return workingLife;
    }

    public void setWorkingLife(String workingLife) {
        this.workingLife = StringUtils.trimToNull(workingLife);
    }

    @Size(max = 64, message = "工作（或实习）年限名称（单选）（备注：本院执业护士、新入职护士、进修护士选择从事护理工作年限。应届实习护士选择＜1年，非应届实习护士选择实际从事护理工作年限）： y＜1年； 1≤y＜2年； 2≤y＜5年；5≤y＜10年；10≤y＜20年； y≥20年 长度不能超过64个字符！")
    public String getWorkingLifeName() {
        return workingLifeName;
    }

    public void setWorkingLifeName(String workingLifeName) {
        this.workingLifeName = StringUtils.trimToNull(workingLifeName);
    }

    public LocalDateTime getEventDate() {
        return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
        this.eventDate = eventDate;
    }

    @Size(max = 64, message = "锐器伤发生方式（单选）：自伤self；他人误伤accidental；其他other长度不能超过64个字符！")
    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = StringUtils.trimToNull(eventType);
    }

    @Size(max = 64, message = "锐器伤发生方式名称（单选）：自伤self；他人误伤accidental；其他other长度不能超过64个字符！")
    public String getEventTypeName() {
        return eventTypeName;
    }

    public void setEventTypeName(String eventTypeName) {
        this.eventTypeName = StringUtils.trimToNull(eventTypeName);
    }

    public String getSharpDevice() {
        return sharpDevice;
    }

    public void setSharpDevice(String sharpDevice) {
        this.sharpDevice = StringUtils.trimToNull(sharpDevice);
    }

    public String getSharpDeviceName() {
        return sharpDeviceName;
    }

    public void setSharpDeviceName(String sharpDeviceName) {
        this.sharpDeviceName = StringUtils.trimToNull(sharpDeviceName);
    }

    public String getSharpOperation() {
        return sharpOperation;
    }

    public void setSharpOperation(String sharpOperation) {
        this.sharpOperation = StringUtils.trimToNull(sharpOperation);
    }

    public String getSharpOperationName() {
        return sharpOperationName;
    }

    public void setSharpOperationName(String sharpOperationName) {
        this.sharpOperationName = StringUtils.trimToNull(sharpOperationName);
    }

    @Size(max = 64, message = "锐器是否被污染（单选）：1是；0否（直接跳转至13题）；uncertain不确定（直接跳转至13题）长度不能超过64个字符！")
    public String getPollutionFlag() {
        return pollutionFlag;
    }

    public void setPollutionFlag(String pollutionFlag) {
        this.pollutionFlag = StringUtils.trimToNull(pollutionFlag);
    }

    @Size(max = 64, message = "锐器是否被污染名称（单选）：1是；0否（直接跳转至13题）；uncertain不确定（直接跳转至13题）长度不能超过64个字符！")
    public String getPollutionFlagName() {
        return pollutionFlagName;
    }

    public void setPollutionFlagName(String pollutionFlagName) {
        this.pollutionFlagName = StringUtils.trimToNull(pollutionFlagName);
    }

    @Size(max = 64, message = "污染源类型（单选）：血液；体液；其他长度不能超过64个字符！")
    public String getPollutionType() {
        return pollutionType;
    }

    public void setPollutionType(String pollutionType) {
        this.pollutionType = StringUtils.trimToNull(pollutionType);
    }

    @Size(max = 64, message = "污染源类型名称（单选）：血液；体液；其他长度不能超过64个字符！")
    public String getPollutionTypeName() {
        return pollutionTypeName;
    }

    public void setPollutionTypeName(String pollutionTypeName) {
        this.pollutionTypeName = StringUtils.trimToNull(pollutionTypeName);
    }

    @Size(max = 16, message = "该污染源是否含有血源性传播疾病（单选）：1是；0否（直接跳转至13题） ；  uncertain不确定（直接跳转至13题）长度不能超过16个字符！")
    public String getBloodTransmittFlag() {
        return bloodTransmittFlag;
    }

    public void setBloodTransmittFlag(String bloodTransmittFlag) {
        this.bloodTransmittFlag = StringUtils.trimToNull(bloodTransmittFlag);
    }

    @Size(max = 64, message = "该污染源是否含有血源性传播疾病名称（单选）：1是；0否（直接跳转至13题） ；  uncertain不确定（直接跳转至13题）长度不能超过64个字符！")
    public String getBloodTransmittFlagName() {
        return bloodTransmittFlagName;
    }

    public void setBloodTransmittFlagName(String bloodTransmittFlagName) {
        this.bloodTransmittFlagName = StringUtils.trimToNull(bloodTransmittFlagName);
    }

    @Size(max = 16, message = "血源性传播疾病类型（单选）：hiv；乙肝hepatitisc；丙肝hcv；梅毒syphilis； 其他other；两种或两种以上类型more长度不能超过16个字符！")
    public String getBloodTransmittType() {
        return bloodTransmittType;
    }

    public void setBloodTransmittType(String bloodTransmittType) {
        this.bloodTransmittType = StringUtils.trimToNull(bloodTransmittType);
    }

    @Size(max = 64, message = "血源性传播疾病类型名称（单选）：hiv；乙肝hepatitisc；丙肝hcv；梅毒syphilis； 其他other；两种或两种以上类型more长度不能超过64个字符！")
    public String getBloodTransmittTypeName() {
        return bloodTransmittTypeName;
    }

    public void setBloodTransmittTypeName(String bloodTransmittTypeName) {
        this.bloodTransmittTypeName = StringUtils.trimToNull(bloodTransmittTypeName);
    }

    public boolean isTrackFlag() {
        return trackFlag;
    }

    public void setTrackFlag(boolean trackFlag) {
        this.trackFlag = trackFlag;
    }

    public String getUntrackReason() {
        return untrackReason;
    }

    public void setUntrackReason(String untrackReason) {
        this.untrackReason = StringUtils.trimToNull(untrackReason);
    }

    public String getUntrackReasonName() {
        return untrackReasonName;
    }

    public void setUntrackReasonName(String untrackReasonName) {
        this.untrackReasonName = StringUtils.trimToNull(untrackReasonName);
    }

    @Size(max = 16, message = "截止到表单上报时，该事件是否导致锐器伤者确诊感染（单选）：1是；0否（填报结束）  ；waiting尚在等待检测结果（检测结果确定后请返回系统修改选项）（暂时填报结束，同时，系统预留一个口，等确定后再修改该题选项，每次登录提醒。）长度不能超过16个字符！")
    public String getConfirmedFlag() {
        return confirmedFlag;
    }

    public void setConfirmedFlag(String confirmedFlag) {
        this.confirmedFlag = StringUtils.trimToNull(confirmedFlag);
    }

    public String getConfirmedFlagName() {
        return confirmedFlagName;
    }

    public void setConfirmedFlagName(String confirmedFlagName) {
        this.confirmedFlagName = StringUtils.trimToNull(confirmedFlagName);
    }

    @Size(max = 64, message = "感染疾病类型（单选）： hiv ；乙肝hepatitisc ；丙肝hcv；梅毒syphilis  ；其他other ；两种或两种以上类型more长度不能超过64个字符！")
    public String getDiseaseType() {
        return diseaseType;
    }

    public void setDiseaseType(String diseaseType) {
        this.diseaseType = StringUtils.trimToNull(diseaseType);
    }

    @Size(max = 64, message = "感染疾病类型名称（单选）： hiv ；乙肝hepatitisc ；丙肝hcv；梅毒syphilis  ；其他other ；两种或两种以上类型more长度不能超过64个字符！")
    public String getDiseaseTypeName() {
        return diseaseTypeName;
    }

    public void setDiseaseTypeName(String diseaseTypeName) {
        this.diseaseTypeName = StringUtils.trimToNull(diseaseTypeName);
    }
}