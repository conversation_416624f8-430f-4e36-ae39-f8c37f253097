package com.bjgoodwill.hip.ds.cis.adv.extubation.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "非计划拔管事件上报表")
public class CisAdvEventExtubationEto  extends BaseEto implements Serializable {

	@Serial
    private static final long serialVersionUID = -1606001329124731610L;

    @Schema(description = "不良事件id")
    private String eventReportId;
    @Schema(description = "非计划拔管类型：导尿管；气管导管；picc；cvc；胃肠管（经口鼻）")
    private String unplannedType;
    @Schema(description = "患者类型")
    private String patType;
    @Schema(description = "住院号(门诊就诊卡号)")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "床号")
    private String bedName;
    @Schema(description = "病区科室")
    private String areaCode;
    @Schema(description = "病区科室名称")
    private String areaName;
    @Schema(description = "事件发生时间")
    private LocalDateTime eventDate;
    @Schema(description = "事件发生场所：in病区内，out病区外（院内）")
    private String eventPlace;
    @Schema(description = "事件发生场所：in病区内，out病区外（院内）")
    private String eventPlaceName;
    @Schema(description = "该患者本次住院非计划拔管次数：1第一次；2第二次；3第三次；4大于3次")
    private Integer extubationNum;
    @Schema(description = "非计划拔管原因: oneself 患者自拔；slipping管路滑落；block阻塞；infected感染；material材质问题；other其他；")
    private String extubationReasons;
    @Schema(description = "非计划拔管原因名称: oneself 患者自拔；slipping管路滑落；block阻塞；infected感染；material材质问题；other其他；")
    private String extubationReasonsName;
    @Schema(description = "是否重置:0否；1是")
    private boolean resetFlag;
    @Schema(description = "重置时间：1非计划拔管后24小时内（含24小时）；2非计划拔管24小时后")
    private String reset24timeFlag;
    @Schema(description = "非计划拔管时有无约束：0否；1是；")
    private boolean constraintFlag;
    @Schema(description = "非计划拔管时患者状态：bedridden卧床时；turnover翻身时；passingbed过床时；transport转运时；inspect检查时；other其他；")
    private String patientState;
    @Schema(description = "非计划拔管时患者状态名称：bedridden卧床时；turnover翻身时；passingbed过床时；transport转运时；inspect检查时；other其他；")
    private String patientStateName;
    @Schema(description = "非计划拔管时患者神志：0不清醒；1清醒；")
    private boolean mindFlag;
    @Schema(description = "非计划拔管时患者是否镇静：0否；1是；2不知道")
    private String calmFlag;
    @Schema(description = "风险评估工具:rass(richmond 躁动-镇静评分)，sas（镇静-躁动评分），其他量表，未评估")
    private String assessmentTool;
    @Schema(description = "非计划拔管时患者pass评分（richmond躁动-镇静量表）：4，3，2，1，0，-1，-2，-3，-4，-5，其他量表，未评估")
    private String gaugeScore;
    @Schema(description = "其他量表名称")
    private String otherGauge;
    @Schema(description = "其他量表分值")
    private Integer otherGaugeScore;
    @Schema(description = "非计划拔管发生时当班责任护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    private String workingLife;
    @Schema(description = "非计划拔管发生时当班责任护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    private String workingLifeName;
    @Schema(description = "非计划拔管发生时在岗责任护士人数")
    private Integer dutyNum;
    @Schema(description = "非计划拔管发生时病区在院患者数")
    private Integer areaNum;
    @Schema(description = "整改措施")
    private String improvementMeasures;
    @Schema(description = "护士长签名")
    private String headSignature;
    @Schema(description = "护士长签名名称")
    private String headSignatureName;
    @Schema(description = "护士长填写时间")
    private LocalDateTime headSignatureDate;
    @Schema(description = "护理部意见")
    private String nursDeptOpinion;
    @Schema(description = "护理部签名")
    private String nursDeptSignature;
    @Schema(description = "护理部签名名称")
    private String nursDeptSignatureName;
    @Schema(description = "护理部填写时间")
    private LocalDateTime deptSignatureDate;

    @Size(max = 50, message = "不良事件id长度不能超过50个字符！")
    public String getEventReportId() {
    	return eventReportId;
    }

    public void setEventReportId(String eventReportId) {
        this.eventReportId = StringUtils.trimToNull(eventReportId);
    }

    @Size(max = 32, message = "非计划拔管类型：导尿管；气管导管；picc；cvc；胃肠管（经口鼻）长度不能超过32个字符！")
    public String getUnplannedType() {
    	return unplannedType;
    }

    public void setUnplannedType(String unplannedType) {
        this.unplannedType = StringUtils.trimToNull(unplannedType);
    }

    @Size(max = 2, message = "患者类型长度不能超过2个字符！")
    public String getPatType() {
    	return patType;
    }

    public void setPatType(String patType) {
        this.patType = StringUtils.trimToNull(patType);
    }

    @Size(max = 16, message = "住院号(门诊就诊卡号)长度不能超过16个字符！")
    public String getInpatientCode() {
    	return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = StringUtils.trimToNull(inpatientCode);
    }

    @Size(max = 16, message = "就诊流水号长度不能超过16个字符！")
    public String getVisitCode() {
    	return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @Size(max = 64, message = "患者姓名长度不能超过64个字符！")
    public String getPatName() {
    	return patName;
    }

    public void setPatName(String patName) {
        this.patName = StringUtils.trimToNull(patName);
    }

    @Size(max = 16, message = "性别长度不能超过16个字符！")
    public String getSex() {
    	return sex;
    }

    public void setSex(String sex) {
        this.sex = StringUtils.trimToNull(sex);
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    @Size(max = 64, message = "床号长度不能超过64个字符！")
    public String getBedName() {
    	return bedName;
    }

    public void setBedName(String bedName) {
        this.bedName = StringUtils.trimToNull(bedName);
    }

    @Size(max = 16, message = "病区科室长度不能超过16个字符！")
    public String getAreaCode() {
    	return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = StringUtils.trimToNull(areaCode);
    }

    @Size(max = 16, message = "病区科室名称长度不能超过16个字符！")
    public String getAreaName() {
    	return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = StringUtils.trimToNull(areaName);
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    public void setEventDate(LocalDateTime eventDate) {
        this.eventDate = eventDate;
    }

    @Size(max = 16, message = "事件发生场所：in病区内，out病区外（院内）长度不能超过16个字符！")
    public String getEventPlace() {
    	return eventPlace;
    }

    public void setEventPlace(String eventPlace) {
        this.eventPlace = StringUtils.trimToNull(eventPlace);
    }

    @Size(max = 32, message = "事件发生场所：in病区内，out病区外（院内）长度不能超过32个字符！")
    public String getEventPlaceName() {
    	return eventPlaceName;
    }

    public void setEventPlaceName(String eventPlaceName) {
        this.eventPlaceName = StringUtils.trimToNull(eventPlaceName);
    }

    public Integer getExtubationNum() {
    	return extubationNum;
    }

    public void setExtubationNum(Integer extubationNum) {
        this.extubationNum = extubationNum;
    }

    @Size(max = 16, message = "非计划拔管原因: oneself 患者自拔；slipping管路滑落；block阻塞；infected感染；material材质问题；other其他；长度不能超过16个字符！")
    public String getExtubationReasons() {
    	return extubationReasons;
    }

    public void setExtubationReasons(String extubationReasons) {
        this.extubationReasons = StringUtils.trimToNull(extubationReasons);
    }

    @Size(max = 32, message = "非计划拔管原因名称: oneself 患者自拔；slipping管路滑落；block阻塞；infected感染；material材质问题；other其他；长度不能超过32个字符！")
    public String getExtubationReasonsName() {
    	return extubationReasonsName;
    }

    public void setExtubationReasonsName(String extubationReasonsName) {
        this.extubationReasonsName = StringUtils.trimToNull(extubationReasonsName);
    }

    public boolean isResetFlag() {
    	return resetFlag;
    }

    public void setResetFlag(boolean resetFlag) {
        this.resetFlag = resetFlag;
    }

    @Size(max = 32, message = "重置时间：1非计划拔管后24小时内（含24小时）；2非计划拔管24小时后长度不能超过32个字符！")
    public String getReset24timeFlag() {
    	return reset24timeFlag;
    }

    public void setReset24timeFlag(String reset24timeFlag) {
        this.reset24timeFlag = StringUtils.trimToNull(reset24timeFlag);
    }

    public boolean isConstraintFlag() {
    	return constraintFlag;
    }

    public void setConstraintFlag(boolean constraintFlag) {
        this.constraintFlag = constraintFlag;
    }

    @Size(max = 16, message = "非计划拔管时患者状态：bedridden卧床时；turnover翻身时；passingbed过床时；transport转运时；inspect检查时；other其他；长度不能超过16个字符！")
    public String getPatientState() {
    	return patientState;
    }

    public void setPatientState(String patientState) {
        this.patientState = StringUtils.trimToNull(patientState);
    }

    @Size(max = 32, message = "非计划拔管时患者状态名称：bedridden卧床时；turnover翻身时；passingbed过床时；transport转运时；inspect检查时；other其他；长度不能超过32个字符！")
    public String getPatientStateName() {
    	return patientStateName;
    }

    public void setPatientStateName(String patientStateName) {
        this.patientStateName = StringUtils.trimToNull(patientStateName);
    }

    public boolean isMindFlag() {
    	return mindFlag;
    }

    public void setMindFlag(boolean mindFlag) {
        this.mindFlag = mindFlag;
    }

    @Size(max = 16, message = "非计划拔管时患者是否镇静：0否；1是；2不知道长度不能超过16个字符！")
    public String getCalmFlag() {
    	return calmFlag;
    }

    public void setCalmFlag(String calmFlag) {
        this.calmFlag = StringUtils.trimToNull(calmFlag);
    }

    @Size(max = 128, message = "风险评估工具:rass(richmond 躁动-镇静评分)，sas（镇静-躁动评分），其他量表，未评估长度不能超过128个字符！")
    public String getAssessmentTool() {
    	return assessmentTool;
    }

    public void setAssessmentTool(String assessmentTool) {
        this.assessmentTool = StringUtils.trimToNull(assessmentTool);
    }

    @Size(max = 16, message = "非计划拔管时患者pass评分（richmond躁动-镇静量表）：4，3，2，1，0，-1，-2，-3，-4，-5，其他量表，未评估长度不能超过16个字符！")
    public String getGaugeScore() {
    	return gaugeScore;
    }

    public void setGaugeScore(String gaugeScore) {
        this.gaugeScore = StringUtils.trimToNull(gaugeScore);
    }

    @Size(max = 128, message = "其他量表名称长度不能超过128个字符！")
    public String getOtherGauge() {
    	return otherGauge;
    }

    public void setOtherGauge(String otherGauge) {
        this.otherGauge = StringUtils.trimToNull(otherGauge);
    }

    public Integer getOtherGaugeScore() {
    	return otherGaugeScore;
    }

    public void setOtherGaugeScore(Integer otherGaugeScore) {
        this.otherGaugeScore = otherGaugeScore;
    }

    @Size(max = 16, message = "非计划拔管发生时当班责任护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年长度不能超过16个字符！")
    public String getWorkingLife() {
    	return workingLife;
    }

    public void setWorkingLife(String workingLife) {
        this.workingLife = StringUtils.trimToNull(workingLife);
    }

    public String getWorkingLifeName() {
    	return workingLifeName;
    }

    public void setWorkingLifeName(String workingLifeName) {
        this.workingLifeName = StringUtils.trimToNull(workingLifeName);
    }

    public Integer getDutyNum() {
    	return dutyNum;
    }

    public void setDutyNum(Integer dutyNum) {
        this.dutyNum = dutyNum;
    }

    public Integer getAreaNum() {
    	return areaNum;
    }

    public void setAreaNum(Integer areaNum) {
        this.areaNum = areaNum;
    }

    public String getImprovementMeasures() {
    	return improvementMeasures;
    }

    public void setImprovementMeasures(String improvementMeasures) {
        this.improvementMeasures = StringUtils.trimToNull(improvementMeasures);
    }

    @Size(max = 16, message = "护士长签名长度不能超过16个字符！")
    public String getHeadSignature() {
    	return headSignature;
    }

    public void setHeadSignature(String headSignature) {
        this.headSignature = StringUtils.trimToNull(headSignature);
    }

    @Size(max = 32, message = "护士长签名名称长度不能超过32个字符！")
    public String getHeadSignatureName() {
    	return headSignatureName;
    }

    public void setHeadSignatureName(String headSignatureName) {
        this.headSignatureName = StringUtils.trimToNull(headSignatureName);
    }

    public LocalDateTime getHeadSignatureDate() {
    	return headSignatureDate;
    }

    public void setHeadSignatureDate(LocalDateTime headSignatureDate) {
        this.headSignatureDate = headSignatureDate;
    }

    public String getNursDeptOpinion() {
    	return nursDeptOpinion;
    }

    public void setNursDeptOpinion(String nursDeptOpinion) {
        this.nursDeptOpinion = StringUtils.trimToNull(nursDeptOpinion);
    }

    @Size(max = 16, message = "护理部签名长度不能超过16个字符！")
    public String getNursDeptSignature() {
    	return nursDeptSignature;
    }

    public void setNursDeptSignature(String nursDeptSignature) {
        this.nursDeptSignature = StringUtils.trimToNull(nursDeptSignature);
    }

    @Size(max = 32, message = "护理部签名名称长度不能超过32个字符！")
    public String getNursDeptSignatureName() {
    	return nursDeptSignatureName;
    }

    public void setNursDeptSignatureName(String nursDeptSignatureName) {
        this.nursDeptSignatureName = StringUtils.trimToNull(nursDeptSignatureName);
    }

    public LocalDateTime getDeptSignatureDate() {
    	return deptSignatureDate;
    }

    public void setDeptSignatureDate(LocalDateTime deptSignatureDate) {
        this.deptSignatureDate = deptSignatureDate;
    }
}