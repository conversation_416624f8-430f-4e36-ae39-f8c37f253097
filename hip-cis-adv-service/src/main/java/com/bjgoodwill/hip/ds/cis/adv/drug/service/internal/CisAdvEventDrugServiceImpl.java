package com.bjgoodwill.hip.ds.cis.adv.drug.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.drug.entity.CisAdvEventDrug;
import com.bjgoodwill.hip.ds.cis.adv.drug.service.CisAdvEventDrugService;
import com.bjgoodwill.hip.ds.cis.adv.drug.service.internal.assembler.CisAdvEventDrugAssembler;
import com.bjgoodwill.hip.ds.cis.adv.drug.to.CisAdvEventDrugEto;
import com.bjgoodwill.hip.ds.cis.adv.drug.to.CisAdvEventDrugNto;
import com.bjgoodwill.hip.ds.cis.adv.drug.to.CisAdvEventDrugQto;
import com.bjgoodwill.hip.ds.cis.adv.drug.to.CisAdvEventDrugTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.drug.service.CisAdvEventDrugService")
@RequestMapping(value = "/api/cisadv/drug/cisAdvEventDrug", produces = "application/json; charset=utf-8")
public class CisAdvEventDrugServiceImpl implements CisAdvEventDrugService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventDrugTo> getCisAdvEventDrugs(CisAdvEventDrugQto cisAdvEventDrugQto) {
        return CisAdvEventDrugAssembler.toTos(CisAdvEventDrug.getCisAdvEventDrugs(cisAdvEventDrugQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventDrugTo> getCisAdvEventDrugPage(CisAdvEventDrugQto cisAdvEventDrugQto) {
        Page<CisAdvEventDrug> page = CisAdvEventDrug.getCisAdvEventDrugPage(cisAdvEventDrugQto);
        Page<CisAdvEventDrugTo> result = page.map(CisAdvEventDrugAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventDrugTo getCisAdvEventDrugById(String id) {
        return CisAdvEventDrugAssembler.toTo(CisAdvEventDrug.getCisAdvEventDrugById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventDrugTo createCisAdvEventDrug(CisAdvEventDrugNto cisAdvEventDrugNto) {
        CisAdvEventDrug cisAdvEventDrug = new CisAdvEventDrug();
		cisAdvEventDrug = cisAdvEventDrug.create(cisAdvEventDrugNto);
		return CisAdvEventDrugAssembler.toTo(cisAdvEventDrug);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventDrug(String id, CisAdvEventDrugEto cisAdvEventDrugEto) {
        Optional<CisAdvEventDrug> cisAdvEventDrugOptional = CisAdvEventDrug.getCisAdvEventDrugById(id);
		cisAdvEventDrugOptional.ifPresent(cisAdvEventDrug -> cisAdvEventDrug.update(cisAdvEventDrugEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventDrug(String id) {
        Optional<CisAdvEventDrug> cisAdvEventDrugOptional = CisAdvEventDrug.getCisAdvEventDrugById(id);
		cisAdvEventDrugOptional.ifPresent(cisAdvEventDrug -> cisAdvEventDrug.delete());
    }

    @InitBinder
	public void initBinder(WebDataBinder binder) {
	}
}