package com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.entity.CisAdvEventPiccBloodInfection;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventPiccBloodInfectionAssembler {

    public static List<CisAdvEventPiccBloodInfectionTo> toTos(List<CisAdvEventPiccBloodInfection> cisAdvEventPiccBloodInfections) {
		return toTos(cisAdvEventPiccBloodInfections, false);
	}

	public static List<CisAdvEventPiccBloodInfectionTo> toTos(List<CisAdvEventPiccBloodInfection> cisAdvEventPiccBloodInfections, boolean withAllParts) {
		Assert.notNull(cisAdvEventPiccBloodInfections, "参数cisAdvEventPiccBloodInfections不能为空！");

		List<CisAdvEventPiccBloodInfectionTo> tos = new ArrayList<>();
		for (CisAdvEventPiccBloodInfection cisAdvEventPiccBloodInfection : cisAdvEventPiccBloodInfections)
			tos.add(toTo(cisAdvEventPiccBloodInfection, withAllParts));
		return tos;
	}

	public static CisAdvEventPiccBloodInfectionTo toTo(CisAdvEventPiccBloodInfection cisAdvEventPiccBloodInfection) {
		return toTo(cisAdvEventPiccBloodInfection, false);
	}

	/**
	 * @generated
	 */
	public static CisAdvEventPiccBloodInfectionTo toTo(CisAdvEventPiccBloodInfection cisAdvEventPiccBloodInfection, boolean withAllParts) {
		if (cisAdvEventPiccBloodInfection == null)
			return null;
		CisAdvEventPiccBloodInfectionTo to = new CisAdvEventPiccBloodInfectionTo();
        to.setId(cisAdvEventPiccBloodInfection.getId());
        to.setEventReportId(cisAdvEventPiccBloodInfection.getEventReportId());
        to.setPatType(cisAdvEventPiccBloodInfection.getPatType());
        to.setInpatientCode(cisAdvEventPiccBloodInfection.getInpatientCode());
        to.setVisitCode(cisAdvEventPiccBloodInfection.getVisitCode());
        to.setPatName(cisAdvEventPiccBloodInfection.getPatName());
        to.setSex(cisAdvEventPiccBloodInfection.getSex());
        to.setBirthDate(cisAdvEventPiccBloodInfection.getBirthDate());
        to.setAgeRange(cisAdvEventPiccBloodInfection.getAgeRange());
        to.setAreaCode(cisAdvEventPiccBloodInfection.getAreaCode());
        to.setAreaName(cisAdvEventPiccBloodInfection.getAreaName());
        to.setInDate(cisAdvEventPiccBloodInfection.getInDate());
        to.setIndwellReason(cisAdvEventPiccBloodInfection.getIndwellReason());
        to.setIndwellReasonName(cisAdvEventPiccBloodInfection.getIndwellReasonName());
        to.setPiccLocation(cisAdvEventPiccBloodInfection.getPiccLocation());
        to.setPiccLocationName(cisAdvEventPiccBloodInfection.getPiccLocationName());
        to.setPiccType(cisAdvEventPiccBloodInfection.getPiccType());
        to.setPiccTypeName(cisAdvEventPiccBloodInfection.getPiccTypeName());
        to.setExtubationType(cisAdvEventPiccBloodInfection.getExtubationType());
        to.setExtubationTypeName(cisAdvEventPiccBloodInfection.getExtubationTypeName());
        to.setAntibacterialFlag(cisAdvEventPiccBloodInfection.isAntibacterialFlag());
        to.setPiccTime(cisAdvEventPiccBloodInfection.getPiccTime());
        to.setCreatedDate(cisAdvEventPiccBloodInfection.getCreatedDate());
        to.setCreatedStaff(cisAdvEventPiccBloodInfection.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventPiccBloodInfection.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventPiccBloodInfection.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventPiccBloodInfection.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventPiccBloodInfection.getUpdatedStaffName());

		if (withAllParts) {
		}
		return to;
	}

}