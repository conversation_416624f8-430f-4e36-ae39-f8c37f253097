package com.bjgoodwill.hip.ds.cis.adv.vap.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.vap.entity.CisAdvEventVap;
import com.bjgoodwill.hip.ds.cis.adv.vap.service.CisAdvEventVapService;
import com.bjgoodwill.hip.ds.cis.adv.vap.service.internal.assembler.CisAdvEventVapAssembler;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapEto;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapNto;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapQto;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.vap.service.CisAdvEventVapService")
@RequestMapping(value = "/api/cisadv/vap/cisAdvEventVap", produces = "application/json; charset=utf-8")
public class CisAdvEventVapServiceImpl implements CisAdvEventVapService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventVapTo> getCisAdvEventVaps(CisAdvEventVapQto cisAdvEventVapQto) {
        return CisAdvEventVapAssembler.toTos(CisAdvEventVap.getCisAdvEventVaps(cisAdvEventVapQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventVapTo> getCisAdvEventVapPage(CisAdvEventVapQto cisAdvEventVapQto) {
        Page<CisAdvEventVap> page = CisAdvEventVap.getCisAdvEventVapPage(cisAdvEventVapQto);
        Page<CisAdvEventVapTo> result = page.map(CisAdvEventVapAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventVapTo getCisAdvEventVapById(String id) {
        return CisAdvEventVapAssembler.toTo(CisAdvEventVap.getCisAdvEventVapById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventVapTo createCisAdvEventVap(CisAdvEventVapNto cisAdvEventVapNto) {
        CisAdvEventVap cisAdvEventVap = new CisAdvEventVap();
        cisAdvEventVap = cisAdvEventVap.create(cisAdvEventVapNto);
        return CisAdvEventVapAssembler.toTo(cisAdvEventVap);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventVap(String id, CisAdvEventVapEto cisAdvEventVapEto) {
        Optional<CisAdvEventVap> cisAdvEventVapOptional = CisAdvEventVap.getCisAdvEventVapById(id);
        cisAdvEventVapOptional.ifPresent(cisAdvEventVap -> cisAdvEventVap.update(cisAdvEventVapEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventVap(String id) {
        Optional<CisAdvEventVap> cisAdvEventVapOptional = CisAdvEventVap.getCisAdvEventVapById(id);
        cisAdvEventVapOptional.ifPresent(cisAdvEventVap -> cisAdvEventVap.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}