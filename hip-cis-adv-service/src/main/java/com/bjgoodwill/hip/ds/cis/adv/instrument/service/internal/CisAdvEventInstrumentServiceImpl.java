package com.bjgoodwill.hip.ds.cis.adv.instrument.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.instrument.entity.CisAdvEventInstrument;
import com.bjgoodwill.hip.ds.cis.adv.instrument.service.CisAdvEventInstrumentService;
import com.bjgoodwill.hip.ds.cis.adv.instrument.service.internal.assembler.CisAdvEventInstrumentAssembler;
import com.bjgoodwill.hip.ds.cis.adv.instrument.to.CisAdvEventInstrumentEto;
import com.bjgoodwill.hip.ds.cis.adv.instrument.to.CisAdvEventInstrumentNto;
import com.bjgoodwill.hip.ds.cis.adv.instrument.to.CisAdvEventInstrumentQto;
import com.bjgoodwill.hip.ds.cis.adv.instrument.to.CisAdvEventInstrumentTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.instrument.service.CisAdvEventInstrumentService")
@RequestMapping(value = "/api/cisadv/instrument/cisAdvEventInstrument", produces = "application/json; charset=utf-8")
public class CisAdvEventInstrumentServiceImpl implements CisAdvEventInstrumentService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventInstrumentTo> getCisAdvEventInstruments(CisAdvEventInstrumentQto cisAdvEventInstrumentQto) {
        return CisAdvEventInstrumentAssembler.toTos(CisAdvEventInstrument.getCisAdvEventInstruments(cisAdvEventInstrumentQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventInstrumentTo> getCisAdvEventInstrumentPage(CisAdvEventInstrumentQto cisAdvEventInstrumentQto) {
        Page<CisAdvEventInstrument> page = CisAdvEventInstrument.getCisAdvEventInstrumentPage(cisAdvEventInstrumentQto);
        Page<CisAdvEventInstrumentTo> result = page.map(CisAdvEventInstrumentAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventInstrumentTo getCisAdvEventInstrumentById(String id) {
        return CisAdvEventInstrumentAssembler.toTo(CisAdvEventInstrument.getCisAdvEventInstrumentById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventInstrumentTo createCisAdvEventInstrument(CisAdvEventInstrumentNto cisAdvEventInstrumentNto) {
        CisAdvEventInstrument cisAdvEventInstrument = new CisAdvEventInstrument();
        cisAdvEventInstrument = cisAdvEventInstrument.create(cisAdvEventInstrumentNto);
        return CisAdvEventInstrumentAssembler.toTo(cisAdvEventInstrument);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventInstrument(String id, CisAdvEventInstrumentEto cisAdvEventInstrumentEto) {
        Optional<CisAdvEventInstrument> cisAdvEventInstrumentOptional = CisAdvEventInstrument.getCisAdvEventInstrumentById(id);
        cisAdvEventInstrumentOptional.ifPresent(cisAdvEventInstrument -> cisAdvEventInstrument.update(cisAdvEventInstrumentEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventInstrument(String id) {
        Optional<CisAdvEventInstrument> cisAdvEventInstrumentOptional = CisAdvEventInstrument.getCisAdvEventInstrumentById(id);
        cisAdvEventInstrumentOptional.ifPresent(cisAdvEventInstrument -> cisAdvEventInstrument.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}