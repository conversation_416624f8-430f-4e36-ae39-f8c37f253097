package com.bjgoodwill.hip.ds.cis.adv.extubation.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.extubation.entity.CisAdvEventExtubation;
import com.bjgoodwill.hip.ds.cis.adv.extubation.to.CisAdvEventExtubationTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventExtubationAssembler {

    public static List<CisAdvEventExtubationTo> toTos(List<CisAdvEventExtubation> cisAdvEventExtubations) {
		return toTos(cisAdvEventExtubations, false);
	}

	public static List<CisAdvEventExtubationTo> toTos(List<CisAdvEventExtubation> cisAdvEventExtubations, boolean withAllParts) {
		Assert.notNull(cisAdvEventExtubations, "参数cisAdvEventExtubations不能为空！");

		List<CisAdvEventExtubationTo> tos = new ArrayList<>();
		for (CisAdvEventExtubation cisAdvEventExtubation : cisAdvEventExtubations)
			tos.add(toTo(cisAdvEventExtubation, withAllParts));
		return tos;
	}

	public static CisAdvEventExtubationTo toTo(CisAdvEventExtubation cisAdvEventExtubation) {
		return toTo(cisAdvEventExtubation, false);
	}

	/**
	 * @generated
	 */
	public static CisAdvEventExtubationTo toTo(CisAdvEventExtubation cisAdvEventExtubation, boolean withAllParts) {
		if (cisAdvEventExtubation == null)
			return null;
		CisAdvEventExtubationTo to = new CisAdvEventExtubationTo();
        to.setId(cisAdvEventExtubation.getId());
        to.setEventReportId(cisAdvEventExtubation.getEventReportId());
        to.setUnplannedType(cisAdvEventExtubation.getUnplannedType());
        to.setPatType(cisAdvEventExtubation.getPatType());
        to.setInpatientCode(cisAdvEventExtubation.getInpatientCode());
        to.setVisitCode(cisAdvEventExtubation.getVisitCode());
        to.setPatName(cisAdvEventExtubation.getPatName());
        to.setSex(cisAdvEventExtubation.getSex());
        to.setBirthDate(cisAdvEventExtubation.getBirthDate());
        to.setBedName(cisAdvEventExtubation.getBedName());
        to.setAreaCode(cisAdvEventExtubation.getAreaCode());
        to.setAreaName(cisAdvEventExtubation.getAreaName());
        to.setEventDate(cisAdvEventExtubation.getEventDate());
        to.setEventPlace(cisAdvEventExtubation.getEventPlace());
        to.setEventPlaceName(cisAdvEventExtubation.getEventPlaceName());
        to.setExtubationNum(cisAdvEventExtubation.getExtubationNum());
        to.setExtubationReasons(cisAdvEventExtubation.getExtubationReasons());
        to.setExtubationReasonsName(cisAdvEventExtubation.getExtubationReasonsName());
        to.setResetFlag(cisAdvEventExtubation.isResetFlag());
        to.setReset24timeFlag(cisAdvEventExtubation.getReset24timeFlag());
        to.setConstraintFlag(cisAdvEventExtubation.isConstraintFlag());
        to.setPatientState(cisAdvEventExtubation.getPatientState());
        to.setPatientStateName(cisAdvEventExtubation.getPatientStateName());
        to.setMindFlag(cisAdvEventExtubation.isMindFlag());
        to.setCalmFlag(cisAdvEventExtubation.getCalmFlag());
        to.setAssessmentTool(cisAdvEventExtubation.getAssessmentTool());
        to.setGaugeScore(cisAdvEventExtubation.getGaugeScore());
        to.setOtherGauge(cisAdvEventExtubation.getOtherGauge());
        to.setOtherGaugeScore(cisAdvEventExtubation.getOtherGaugeScore());
        to.setWorkingLife(cisAdvEventExtubation.getWorkingLife());
        to.setWorkingLifeName(cisAdvEventExtubation.getWorkingLifeName());
        to.setDutyNum(cisAdvEventExtubation.getDutyNum());
        to.setAreaNum(cisAdvEventExtubation.getAreaNum());
        to.setImprovementMeasures(cisAdvEventExtubation.getImprovementMeasures());
        to.setHeadSignature(cisAdvEventExtubation.getHeadSignature());
        to.setHeadSignatureName(cisAdvEventExtubation.getHeadSignatureName());
        to.setHeadSignatureDate(cisAdvEventExtubation.getHeadSignatureDate());
        to.setNursDeptOpinion(cisAdvEventExtubation.getNursDeptOpinion());
        to.setNursDeptSignature(cisAdvEventExtubation.getNursDeptSignature());
        to.setNursDeptSignatureName(cisAdvEventExtubation.getNursDeptSignatureName());
        to.setDeptSignatureDate(cisAdvEventExtubation.getDeptSignatureDate());
        to.setCreatedDate(cisAdvEventExtubation.getCreatedDate());
        to.setCreatedStaff(cisAdvEventExtubation.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventExtubation.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventExtubation.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventExtubation.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventExtubation.getUpdatedStaffName());

		if (withAllParts) {
		}
		return to;
	}

}