package com.bjgoodwill.hip.ds.cis.adv.drug.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.drug.repository.CisAdvEventDrugRepository;
import com.bjgoodwill.hip.ds.cis.adv.drug.to.CisAdvEventDrugEto;
import com.bjgoodwill.hip.ds.cis.adv.drug.to.CisAdvEventDrugNto;
import com.bjgoodwill.hip.ds.cis.adv.drug.to.CisAdvEventDrugQto;
import com.bjgoodwill.hip.ds.cis.adv.enmus.CisAdvBusinessErrorEnum;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "药品不良反应事件报告表")
@Table(name = "cis_adv_event_drug", indexes = {}, uniqueConstraints = {})
public class CisAdvEventDrug {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件id")
    @Column(name = "event_report_id", nullable = true, length = 50)
    private String eventReportId;


    @Comment("报告类别：first首次；track跟踪")
    @Column(name = "report_category", nullable = true, length = 16)
    private String reportCategory;

    @Comment("报告类别名称：first首次；track跟踪")
    @Column(name = "report_category_name", nullable = true, length = 16)
    private String reportCategoryName;


    @Comment("报告类型：new新的；serious严重；commonly一般")
    @Column(name = "report_type", nullable = true, length = 16)
    private String reportType;

    @Comment("报告类型名称：new新的；serious严重；commonly一般")
    @Column(name = "report_type_name", nullable = true, length = 16)
    private String reportTypeName;


    @Comment("报告单位类别：medical医疗机构；management经营企业；produce生产企业；personal个人；other其它")
    @Column(name = "report_unit_category", nullable = true, length = 20)
    private String reportUnitCategory;

    @Comment("报告单位类别名称：medical医疗机构；management经营企业；produce生产企业；personal个人；other其它")
    @Column(name = "report_unit_category_name", nullable = true, length = 20)
    private String reportUnitCategoryName;


    @Comment("其它报告单位")
    @Column(name = "report_unit_other", nullable = true, length = 128)
    private String reportUnitOther;


    @Comment("患者类型")
    @Column(name = "pat_type", nullable = true, length = 2)
    private String patType;


    @Comment("住院号(门诊就诊卡号)")
    @Column(name = "inpatient_code", nullable = true, length = 16)
    private String inpatientCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true, length = 16)
    private String visitCode;


    @Comment("患者姓名")
    @Column(name = "pat_name", nullable = true, length = 64)
    private String patName;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("民族")
    @Column(name = "nation", nullable = true, length = 16)
    private String nation;


    @Comment("体重")
    @Column(name = "weight", nullable = true, length = 16)
    private String weight;


    @Comment("卧床标识")
    @Column(name = "bedridden_flag", nullable = false)
    private boolean bedriddenFlag;


    @Comment("联系电话")
    @Column(name = "contact_tel", nullable = true, length = 24)
    private String contactTel;


    @Comment("原患疾病")
    @Column(name = "primary_diseases", nullable = true, length = 128)
    private String primaryDiseases;


    @Comment("既往药品不良反应标识")
    @Column(name = "past_event_flag", nullable = false)
    private boolean pastEventFlag;


    @Comment("既往药品不良反应")
    @Column(name = "past_event_value", nullable = true, length = 128)
    private String pastEventValue;


    @Comment("家族药品不良反应标识")
    @Column(name = "family_event_flag", nullable = false)
    private boolean familyEventFlag;


    @Comment("家族药品不良反应")
    @Column(name = "family_event_value", nullable = true, length = 128)
    private String familyEventValue;


    @Comment("相关重要信息：smoke吸烟史；drinkawine饮酒史；pregnancy妊娠期；hepatopathy肝病史；nephropathy肾病史；allergy过敏史；other其他；")
    @Column(name = "important_info", nullable = true, length = 16)
    private String importantInfo;

    @Comment("相关重要信息名称：smoke吸烟史；drinkawine饮酒史；pregnancy妊娠期；hepatopathy肝病史；nephropathy肾病史；allergy过敏史；other其他；")
    @Column(name = "important_info_name", nullable = true, length = 16)
    private String importantInfoName;


    @Comment("过敏史")
    @Column(name = "allergy_value", nullable = true)
    private String allergyValue;


    @Comment("相关其他")
    @Column(name = "important_info_other", nullable = true)
    private String importantInfoOther;


    @Comment("怀疑药品标识")
    @Column(name = "doubt_drug_flag", nullable = false)
    private boolean doubtDrugFlag;


    @Comment("并用药品标识")
    @Column(name = "together_drug_flag", nullable = false)
    private boolean togetherDrugFlag;


    @Comment("事件名称")
    @Column(name = "event_name", nullable = true)
    private String eventName;


    @Comment("事件发生时间")
    @Column(name = "event_date", nullable = true)
    private LocalDateTime eventDate;


    @Comment("不良反应事件过程描述：患者因；使用；时间；出现；停药或减慢滴速；采取；时间；症状；其他补充；")
    @Column(name = "event_process", nullable = true)
    private String eventProcess;


    @Comment("事件结果：recovery痊愈；becomebetter好转；noimprovement未好转；unknown不详；sequela有后遗症；death死亡")
    @Column(name = "remark", nullable = true, length = 128)
    private String remark;


    @Comment("后遗症表现")
    @Column(name = "event_results_sequelae", nullable = true, length = 128)
    private String eventResultsSequelae;


    @Comment("直接死因")
    @Column(name = "cause_death", nullable = true, length = 128)
    private String causeDeath;


    @Comment("死亡时间")
    @Column(name = "death_date", nullable = true)
    private LocalDateTime deathDate;


    @Comment("停药或减量后症状表现：yes是；no否；unknown不明；notstop未停药或减量")
    @Column(name = "stop_symptoms", nullable = true, length = 16)
    private String stopSymptoms;


    @Comment("再次使用：yes是；no否；unknown不明；notusedagain未再使用；")
    @Column(name = "again_using", nullable = true, length = 16)
    private String againUsing;


    @Comment("对疾病影响：unobvious不明显；extension病程延长；aggravation病情加重；sequela导致后遗症；death导致死亡；")
    @Column(name = "impact_disease", nullable = true, length = 16)
    private String impactDisease;

    @Comment("对疾病影响名称：unobvious不明显；extension病程延长；aggravation病情加重；sequela导致后遗症；death导致死亡；")
    @Column(name = "impact_disease_name", nullable = true, length = 32)
    private String impactDiseaseName;


    @Comment("报告人评价：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    @Column(name = "report_user_evaluation", nullable = true, length = 16)
    private String reportUserEvaluation;

    @Comment("报告人评价名称：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    @Column(name = "report_user_evaluation_name", nullable = true, length = 16)
    private String reportUserEvaluationName;


    @Comment("报告人评价签字")
    @Column(name = "report_user_signature", nullable = true, length = 16)
    private String reportUserSignature;

    @Comment("报告人评价签字名称")
    @Column(name = "report_user_signature_name", nullable = true, length = 16)
    private String reportUserSignatureName;


    @Comment("报告单位评价：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    @Column(name = "unit_evaluation", nullable = true, length = 16)
    private String unitEvaluation;

    @Comment("报告单位评价名称：sure肯定；like很可能；may可能；unmay可能无关；evaluate待评价；un evaluate无法评价；")
    @Column(name = "unit_evaluation_name", nullable = true, length = 16)
    private String unitEvaluationName;


    @Comment("报告单位评价签字")
    @Column(name = "unit_user_signature", nullable = true, length = 16)
    private String unitUserSignature;


    @Comment("报告人联系电话")
    @Column(name = "report_user_tel", nullable = true, length = 24)
    private String reportUserTel;


    @Comment("报告人职业：doctor医生；pharmacist药师；nurse护士；other其它；")
    @Column(name = "report_user_work", nullable = true, length = 16)
    private String reportUserWork;

    @Comment("报告人职业名称：doctor医生；pharmacist药师；nurse护士；other其它；")
    @Column(name = "report_user_work_name", nullable = true, length = 16)
    private String reportUserWorkName;


    @Comment("其他职业")
    @Column(name = "other_work", nullable = true, length = 128)
    private String otherWork;


    @Comment("报告人电子邮箱")
    @Column(name = "report_user_email", nullable = true, length = 64)
    private String reportUserEmail;


    @Comment("报告单位")
    @Column(name = "report_unit", nullable = true, length = 128)
    private String reportUnit;


    @Comment("报告单位联系人")
    @Column(name = "unit_contact_user", nullable = true, length = 16)
    private String unitContactUser;


    @Comment("报告单位电话")
    @Column(name = "unit_tel", nullable = true, length = 24)
    private String unitTel;


    @Comment("生产企业请填写信息来源")
    @Column(name = "source_information", nullable = true, length = 16)
    private String sourceInformation;


    @Comment("其它来源")
    @Column(name = "other_source", nullable = true, length = 28)
    private String otherSource;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    public String getId() {
    	return id;
    }

    protected void setId(String id) {
    	this.id = id;
    }

    public String getEventReportId() {
    	return eventReportId;
    }

    protected void setEventReportId(String eventReportId) {
    	this.eventReportId = eventReportId;
    }

    public String getReportCategory() {
    	return reportCategory;
    }

    protected void setReportCategory(String reportCategory) {
    	this.reportCategory = reportCategory;
    }

    public String getReportType() {
    	return reportType;
    }

    protected void setReportType(String reportType) {
    	this.reportType = reportType;
    }

    public String getReportUnitCategory() {
    	return reportUnitCategory;
    }

    protected void setReportUnitCategory(String reportUnitCategory) {
    	this.reportUnitCategory = reportUnitCategory;
    }

    public String getReportUnitOther() {
    	return reportUnitOther;
    }

    protected void setReportUnitOther(String reportUnitOther) {
    	this.reportUnitOther = reportUnitOther;
    }

    public String getPatType() {
    	return patType;
    }

    protected void setPatType(String patType) {
    	this.patType = patType;
    }

    public String getInpatientCode() {
    	return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
    	this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    protected void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public String getPatName() {
    	return patName;
    }

    protected void setPatName(String patName) {
    	this.patName = patName;
    }

    public String getSex() {
    	return sex;
    }

    protected void setSex(String sex) {
    	this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
    	this.birthDate = birthDate;
    }

    public String getNation() {
    	return nation;
    }

    protected void setNation(String nation) {
    	this.nation = nation;
    }

    public String getWeight() {
    	return weight;
    }

    protected void setWeight(String weight) {
    	this.weight = weight;
    }

    public boolean isBedriddenFlag() {
    	return bedriddenFlag;
    }

    protected void setBedriddenFlag(boolean bedriddenFlag) {
    	this.bedriddenFlag = bedriddenFlag;
    }

    public String getContactTel() {
    	return contactTel;
    }

    protected void setContactTel(String contactTel) {
    	this.contactTel = contactTel;
    }

    public String getPrimaryDiseases() {
    	return primaryDiseases;
    }

    protected void setPrimaryDiseases(String primaryDiseases) {
    	this.primaryDiseases = primaryDiseases;
    }

    public boolean isPastEventFlag() {
    	return pastEventFlag;
    }

    protected void setPastEventFlag(boolean pastEventFlag) {
    	this.pastEventFlag = pastEventFlag;
    }

    public String getPastEventValue() {
    	return pastEventValue;
    }

    protected void setPastEventValue(String pastEventValue) {
    	this.pastEventValue = pastEventValue;
    }

    public boolean isFamilyEventFlag() {
    	return familyEventFlag;
    }

    protected void setFamilyEventFlag(boolean familyEventFlag) {
    	this.familyEventFlag = familyEventFlag;
    }

    public String getFamilyEventValue() {
    	return familyEventValue;
    }

    protected void setFamilyEventValue(String familyEventValue) {
    	this.familyEventValue = familyEventValue;
    }

    public String getImportantInfo() {
    	return importantInfo;
    }

    protected void setImportantInfo(String importantInfo) {
    	this.importantInfo = importantInfo;
    }

    public String getAllergyValue() {
    	return allergyValue;
    }

    protected void setAllergyValue(String allergyValue) {
    	this.allergyValue = allergyValue;
    }

    public String getImportantInfoOther() {
    	return importantInfoOther;
    }

    protected void setImportantInfoOther(String importantInfoOther) {
    	this.importantInfoOther = importantInfoOther;
    }

    public boolean isDoubtDrugFlag() {
    	return doubtDrugFlag;
    }

    protected void setDoubtDrugFlag(boolean doubtDrugFlag) {
    	this.doubtDrugFlag = doubtDrugFlag;
    }

    public boolean isTogetherDrugFlag() {
    	return togetherDrugFlag;
    }

    protected void setTogetherDrugFlag(boolean togetherDrugFlag) {
    	this.togetherDrugFlag = togetherDrugFlag;
    }

    public String getEventName() {
    	return eventName;
    }

    protected void setEventName(String eventName) {
    	this.eventName = eventName;
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    protected void setEventDate(LocalDateTime eventDate) {
    	this.eventDate = eventDate;
    }

    public String getEventProcess() {
    	return eventProcess;
    }

    protected void setEventProcess(String eventProcess) {
    	this.eventProcess = eventProcess;
    }

    public String getRemark() {
    	return remark;
    }

    protected void setRemark(String remark) {
    	this.remark = remark;
    }

    public String getEventResultsSequelae() {
    	return eventResultsSequelae;
    }

    protected void setEventResultsSequelae(String eventResultsSequelae) {
    	this.eventResultsSequelae = eventResultsSequelae;
    }

    public String getCauseDeath() {
    	return causeDeath;
    }

    protected void setCauseDeath(String causeDeath) {
    	this.causeDeath = causeDeath;
    }

    public LocalDateTime getDeathDate() {
    	return deathDate;
    }

    protected void setDeathDate(LocalDateTime deathDate) {
    	this.deathDate = deathDate;
    }

    public String getStopSymptoms() {
    	return stopSymptoms;
    }

    protected void setStopSymptoms(String stopSymptoms) {
    	this.stopSymptoms = stopSymptoms;
    }

    public String getAgainUsing() {
    	return againUsing;
    }

    protected void setAgainUsing(String againUsing) {
    	this.againUsing = againUsing;
    }

    public String getImpactDisease() {
    	return impactDisease;
    }

    protected void setImpactDisease(String impactDisease) {
    	this.impactDisease = impactDisease;
    }

    public String getReportUserEvaluation() {
    	return reportUserEvaluation;
    }

    protected void setReportUserEvaluation(String reportUserEvaluation) {
    	this.reportUserEvaluation = reportUserEvaluation;
    }

    public String getReportUserSignature() {
    	return reportUserSignature;
    }

    protected void setReportUserSignature(String reportUserSignature) {
    	this.reportUserSignature = reportUserSignature;
    }

    public String getUnitEvaluation() {
    	return unitEvaluation;
    }

    protected void setUnitEvaluation(String unitEvaluation) {
    	this.unitEvaluation = unitEvaluation;
    }

    public String getUnitUserSignature() {
    	return unitUserSignature;
    }

    protected void setUnitUserSignature(String unitUserSignature) {
    	this.unitUserSignature = unitUserSignature;
    }

    public String getReportUserTel() {
    	return reportUserTel;
    }

    protected void setReportUserTel(String reportUserTel) {
    	this.reportUserTel = reportUserTel;
    }

    public String getReportUserWork() {
    	return reportUserWork;
    }

    protected void setReportUserWork(String reportUserWork) {
    	this.reportUserWork = reportUserWork;
    }

    public String getOtherWork() {
    	return otherWork;
    }

    protected void setOtherWork(String otherWork) {
    	this.otherWork = otherWork;
    }

    public String getReportUserEmail() {
    	return reportUserEmail;
    }

    protected void setReportUserEmail(String reportUserEmail) {
    	this.reportUserEmail = reportUserEmail;
    }

    public String getReportUnit() {
    	return reportUnit;
    }

    protected void setReportUnit(String reportUnit) {
    	this.reportUnit = reportUnit;
    }

    public String getUnitContactUser() {
    	return unitContactUser;
    }

    protected void setUnitContactUser(String unitContactUser) {
    	this.unitContactUser = unitContactUser;
    }

    public String getUnitTel() {
    	return unitTel;
    }

    protected void setUnitTel(String unitTel) {
    	this.unitTel = unitTel;
    }

    public String getSourceInformation() {
    	return sourceInformation;
    }

    protected void setSourceInformation(String sourceInformation) {
    	this.sourceInformation = sourceInformation;
    }

    public String getOtherSource() {
    	return otherSource;
    }

    protected void setOtherSource(String otherSource) {
    	this.otherSource = otherSource;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    public String getReportCategoryName() {
        return reportCategoryName;
    }

    public void setReportCategoryName(String reportCategoryName) {
        this.reportCategoryName = reportCategoryName;
    }

    public String getReportTypeName() {
        return reportTypeName;
    }

    public void setReportTypeName(String reportTypeName) {
        this.reportTypeName = reportTypeName;
    }

    public String getReportUnitCategoryName() {
        return reportUnitCategoryName;
    }

    public void setReportUnitCategoryName(String reportUnitCategoryName) {
        this.reportUnitCategoryName = reportUnitCategoryName;
    }

    public String getImportantInfoName() {
        return importantInfoName;
    }

    public void setImportantInfoName(String importantInfoName) {
        this.importantInfoName = importantInfoName;
    }

    public String getImpactDiseaseName() {
        return impactDiseaseName;
    }

    public void setImpactDiseaseName(String impactDiseaseName) {
        this.impactDiseaseName = impactDiseaseName;
    }

    public String getReportUserEvaluationName() {
        return reportUserEvaluationName;
    }

    public void setReportUserEvaluationName(String reportUserEvaluationName) {
        this.reportUserEvaluationName = reportUserEvaluationName;
    }

    public String getReportUserSignatureName() {
        return reportUserSignatureName;
    }

    public void setReportUserSignatureName(String reportUserSignatureName) {
        this.reportUserSignatureName = reportUserSignatureName;
    }

    public String getUnitEvaluationName() {
        return unitEvaluationName;
    }

    public void setUnitEvaluationName(String unitEvaluationName) {
        this.unitEvaluationName = unitEvaluationName;
    }

    public String getReportUserWorkName() {
        return reportUserWorkName;
    }

    public void setReportUserWorkName(String reportUserWorkName) {
        this.reportUserWorkName = reportUserWorkName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventDrug other = (CisAdvEventDrug) obj;
		return Objects.equals(id, other.id);
	}

    public CisAdvEventDrug create(CisAdvEventDrugNto cisAdvEventDrugNto) {
        BusinessAssert.notNull(cisAdvEventDrugNto, CisAdvBusinessErrorEnum.BUS_CIS_ADV_0001,"参数cisAdvEventDrugNto");

        setId(cisAdvEventDrugNto.getId());
        setEventReportId(cisAdvEventDrugNto.getEventReportId());
        setReportCategory(cisAdvEventDrugNto.getReportCategory());
        setReportCategoryName(cisAdvEventDrugNto.getReportCategoryName());
        setReportType(cisAdvEventDrugNto.getReportType());
        setReportTypeName(cisAdvEventDrugNto.getReportTypeName());
        setReportUnitCategory(cisAdvEventDrugNto.getReportUnitCategory());
        setReportUnitCategoryName(cisAdvEventDrugNto.getReportUnitCategoryName());
        setReportUnitOther(cisAdvEventDrugNto.getReportUnitOther());
        setPatType(cisAdvEventDrugNto.getPatType());
        setInpatientCode(cisAdvEventDrugNto.getInpatientCode());
        setVisitCode(cisAdvEventDrugNto.getVisitCode());
        setPatName(cisAdvEventDrugNto.getPatName());
        setSex(cisAdvEventDrugNto.getSex());
        setBirthDate(cisAdvEventDrugNto.getBirthDate());
        setNation(cisAdvEventDrugNto.getNation());
        setWeight(cisAdvEventDrugNto.getWeight());
        setBedriddenFlag(cisAdvEventDrugNto.isBedriddenFlag());
        setContactTel(cisAdvEventDrugNto.getContactTel());
        setPrimaryDiseases(cisAdvEventDrugNto.getPrimaryDiseases());
        setPastEventFlag(cisAdvEventDrugNto.isPastEventFlag());
        setPastEventValue(cisAdvEventDrugNto.getPastEventValue());
        setFamilyEventFlag(cisAdvEventDrugNto.isFamilyEventFlag());
        setFamilyEventValue(cisAdvEventDrugNto.getFamilyEventValue());
        setImportantInfo(cisAdvEventDrugNto.getImportantInfo());
        setImportantInfoName(cisAdvEventDrugNto.getImportantInfoName());
        setAllergyValue(cisAdvEventDrugNto.getAllergyValue());
        setImportantInfoOther(cisAdvEventDrugNto.getImportantInfoOther());
        setDoubtDrugFlag(cisAdvEventDrugNto.isDoubtDrugFlag());
        setTogetherDrugFlag(cisAdvEventDrugNto.isTogetherDrugFlag());
        setEventName(cisAdvEventDrugNto.getEventName());
        setEventDate(cisAdvEventDrugNto.getEventDate());
        setEventProcess(cisAdvEventDrugNto.getEventProcess());
        setRemark(cisAdvEventDrugNto.getRemark());
        setEventResultsSequelae(cisAdvEventDrugNto.getEventResultsSequelae());
        setCauseDeath(cisAdvEventDrugNto.getCauseDeath());
        setDeathDate(cisAdvEventDrugNto.getDeathDate());
        setStopSymptoms(cisAdvEventDrugNto.getStopSymptoms());
        setAgainUsing(cisAdvEventDrugNto.getAgainUsing());
        setImpactDisease(cisAdvEventDrugNto.getImpactDisease());
        setImpactDiseaseName(cisAdvEventDrugNto.getImpactDiseaseName());
        setReportUserEvaluation(cisAdvEventDrugNto.getReportUserEvaluation());
        setReportUserEvaluationName(cisAdvEventDrugNto.getReportUserEvaluationName());
        setReportUserSignature(cisAdvEventDrugNto.getReportUserSignature());
        setReportUserSignatureName(cisAdvEventDrugNto.getReportUserSignatureName());
        setUnitEvaluation(cisAdvEventDrugNto.getUnitEvaluation());
        setUnitEvaluationName(cisAdvEventDrugNto.getUnitEvaluationName());
        setUnitUserSignature(cisAdvEventDrugNto.getUnitUserSignature());
        setReportUserTel(cisAdvEventDrugNto.getReportUserTel());
        setReportUserWork(cisAdvEventDrugNto.getReportUserWork());
        setReportUserWorkName(cisAdvEventDrugNto.getReportUserWorkName());
        setOtherWork(cisAdvEventDrugNto.getOtherWork());
        setReportUserEmail(cisAdvEventDrugNto.getReportUserEmail());
        setReportUnit(cisAdvEventDrugNto.getReportUnit());
        setUnitContactUser(cisAdvEventDrugNto.getUnitContactUser());
        setUnitTel(cisAdvEventDrugNto.getUnitTel());
        setSourceInformation(cisAdvEventDrugNto.getSourceInformation());
        setOtherSource(cisAdvEventDrugNto.getOtherSource());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventDrugEto cisAdvEventDrugEto) {
        setEventReportId(cisAdvEventDrugEto.getEventReportId());
        setReportCategory(cisAdvEventDrugEto.getReportCategory());
        setReportCategoryName(cisAdvEventDrugEto.getReportCategoryName());
        setReportType(cisAdvEventDrugEto.getReportType());
        setReportTypeName(cisAdvEventDrugEto.getReportTypeName());
        setReportUnitCategory(cisAdvEventDrugEto.getReportUnitCategory());
        setReportUnitCategoryName(cisAdvEventDrugEto.getReportUnitCategoryName());
        setReportUnitOther(cisAdvEventDrugEto.getReportUnitOther());
        setPatType(cisAdvEventDrugEto.getPatType());
        setInpatientCode(cisAdvEventDrugEto.getInpatientCode());
        setVisitCode(cisAdvEventDrugEto.getVisitCode());
        setPatName(cisAdvEventDrugEto.getPatName());
        setSex(cisAdvEventDrugEto.getSex());
        setBirthDate(cisAdvEventDrugEto.getBirthDate());
        setNation(cisAdvEventDrugEto.getNation());
        setWeight(cisAdvEventDrugEto.getWeight());
        setBedriddenFlag(cisAdvEventDrugEto.isBedriddenFlag());
        setContactTel(cisAdvEventDrugEto.getContactTel());
        setPrimaryDiseases(cisAdvEventDrugEto.getPrimaryDiseases());
        setPastEventFlag(cisAdvEventDrugEto.isPastEventFlag());
        setPastEventValue(cisAdvEventDrugEto.getPastEventValue());
        setFamilyEventFlag(cisAdvEventDrugEto.isFamilyEventFlag());
        setFamilyEventValue(cisAdvEventDrugEto.getFamilyEventValue());
        setImportantInfo(cisAdvEventDrugEto.getImportantInfo());
        setImportantInfoName(cisAdvEventDrugEto.getImportantInfoName());
        setAllergyValue(cisAdvEventDrugEto.getAllergyValue());
        setImportantInfoOther(cisAdvEventDrugEto.getImportantInfoOther());
        setDoubtDrugFlag(cisAdvEventDrugEto.isDoubtDrugFlag());
        setTogetherDrugFlag(cisAdvEventDrugEto.isTogetherDrugFlag());
        setEventName(cisAdvEventDrugEto.getEventName());
        setEventDate(cisAdvEventDrugEto.getEventDate());
        setEventProcess(cisAdvEventDrugEto.getEventProcess());
        setRemark(cisAdvEventDrugEto.getRemark());
        setEventResultsSequelae(cisAdvEventDrugEto.getEventResultsSequelae());
        setCauseDeath(cisAdvEventDrugEto.getCauseDeath());
        setDeathDate(cisAdvEventDrugEto.getDeathDate());
        setStopSymptoms(cisAdvEventDrugEto.getStopSymptoms());
        setAgainUsing(cisAdvEventDrugEto.getAgainUsing());
        setImpactDisease(cisAdvEventDrugEto.getImpactDisease());
        setImpactDiseaseName(cisAdvEventDrugEto.getImpactDiseaseName());
        setReportUserEvaluation(cisAdvEventDrugEto.getReportUserEvaluation());
        setReportUserEvaluationName(cisAdvEventDrugEto.getReportUserEvaluationName());
        setReportUserSignature(cisAdvEventDrugEto.getReportUserSignature());
        setReportUserSignatureName(cisAdvEventDrugEto.getReportUserSignatureName());
        setUnitEvaluation(cisAdvEventDrugEto.getUnitEvaluation());
        setUnitEvaluationName(cisAdvEventDrugEto.getUnitEvaluationName());
        setUnitUserSignature(cisAdvEventDrugEto.getUnitUserSignature());
        setReportUserTel(cisAdvEventDrugEto.getReportUserTel());
        setReportUserWork(cisAdvEventDrugEto.getReportUserWork());
        setReportUserWorkName(cisAdvEventDrugEto.getReportUserWorkName());
        setOtherWork(cisAdvEventDrugEto.getOtherWork());
        setReportUserEmail(cisAdvEventDrugEto.getReportUserEmail());
        setReportUnit(cisAdvEventDrugEto.getReportUnit());
        setUnitContactUser(cisAdvEventDrugEto.getUnitContactUser());
        setUnitTel(cisAdvEventDrugEto.getUnitTel());
        setSourceInformation(cisAdvEventDrugEto.getSourceInformation());
        setOtherSource(cisAdvEventDrugEto.getOtherSource());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

    public static Optional<CisAdvEventDrug> getCisAdvEventDrugById(String id) {
		return dao().findById(id);
	}

	public static List<CisAdvEventDrug> getCisAdvEventDrugs(CisAdvEventDrugQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
	}

	public static Page<CisAdvEventDrug> getCisAdvEventDrugPage(CisAdvEventDrugQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
	}


	/**
	 * @generated
	 */
    private static Specification<CisAdvEventDrug> getSpecification(CisAdvEventDrugQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
        	if(StringUtils.isNotBlank(qto.getEventReportId())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReportId"), qto.getEventReportId()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportCategory())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportCategory"), qto.getReportCategory()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportType"), qto.getReportType()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUnitCategory())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUnitCategory"), qto.getReportUnitCategory()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUnitOther())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUnitOther"), qto.getReportUnitOther()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patType"), qto.getPatType()));
        	}
        	if(StringUtils.isNotBlank(qto.getInpatientCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getVisitCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patName"), qto.getPatName()));
        	}
        	if(StringUtils.isNotBlank(qto.getSex())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
        	}
    		if(qto.getBirthDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getNation())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nation"), qto.getNation()));
        	}
        	if(StringUtils.isNotBlank(qto.getWeight())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("weight"), qto.getWeight()));
        	}
    		if(qto.getBedriddenFlag() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("bedriddenFlag"), qto.getBedriddenFlag()));
        	}
        	if(StringUtils.isNotBlank(qto.getContactTel())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("contactTel"), qto.getContactTel()));
        	}
        	if(StringUtils.isNotBlank(qto.getPrimaryDiseases())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("primaryDiseases"), qto.getPrimaryDiseases()));
        	}
    		if(qto.getPastEventFlag() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("pastEventFlag"), qto.getPastEventFlag()));
        	}
        	if(StringUtils.isNotBlank(qto.getPastEventValue())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("pastEventValue"), qto.getPastEventValue()));
        	}
    		if(qto.getFamilyEventFlag() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("familyEventFlag"), qto.getFamilyEventFlag()));
        	}
        	if(StringUtils.isNotBlank(qto.getFamilyEventValue())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("familyEventValue"), qto.getFamilyEventValue()));
        	}
        	if(StringUtils.isNotBlank(qto.getImportantInfo())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("importantInfo"), qto.getImportantInfo()));
        	}
        	if(StringUtils.isNotBlank(qto.getAllergyValue())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("allergyValue"), qto.getAllergyValue()));
        	}
        	if(StringUtils.isNotBlank(qto.getImportantInfoOther())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("importantInfoOther"), qto.getImportantInfoOther()));
        	}
    		if(qto.getDoubtDrugFlag() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("doubtDrugFlag"), qto.getDoubtDrugFlag()));
        	}
    		if(qto.getTogetherDrugFlag() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("togetherDrugFlag"), qto.getTogetherDrugFlag()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventName"), qto.getEventName()));
        	}
    		if(qto.getEventDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("eventDate"), LocalDateUtil.beginOfDay(qto.getEventDate()), LocalDateUtil.endOfDay(qto.getEventDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcess())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess"), qto.getEventProcess()));
        	}
        	if(StringUtils.isNotBlank(qto.getRemark())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("remark"), qto.getRemark()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventResultsSequelae())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventResultsSequelae"), qto.getEventResultsSequelae()));
        	}
        	if(StringUtils.isNotBlank(qto.getCauseDeath())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("causeDeath"), qto.getCauseDeath()));
        	}
    		if(qto.getDeathDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("deathDate"), LocalDateUtil.beginOfDay(qto.getDeathDate()), LocalDateUtil.endOfDay(qto.getDeathDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getStopSymptoms())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("stopSymptoms"), qto.getStopSymptoms()));
        	}
        	if(StringUtils.isNotBlank(qto.getAgainUsing())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("againUsing"), qto.getAgainUsing()));
        	}
        	if(StringUtils.isNotBlank(qto.getImpactDisease())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("impactDisease"), qto.getImpactDisease()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUserEvaluation())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUserEvaluation"), qto.getReportUserEvaluation()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUserSignature())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUserSignature"), qto.getReportUserSignature()));
        	}
        	if(StringUtils.isNotBlank(qto.getUnitEvaluation())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("unitEvaluation"), qto.getUnitEvaluation()));
        	}
        	if(StringUtils.isNotBlank(qto.getUnitUserSignature())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("unitUserSignature"), qto.getUnitUserSignature()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUserTel())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUserTel"), qto.getReportUserTel()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUserWork())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUserWork"), qto.getReportUserWork()));
        	}
        	if(StringUtils.isNotBlank(qto.getOtherWork())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherWork"), qto.getOtherWork()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUserEmail())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUserEmail"), qto.getReportUserEmail()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUnit())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUnit"), qto.getReportUnit()));
        	}
        	if(StringUtils.isNotBlank(qto.getUnitContactUser())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("unitContactUser"), qto.getUnitContactUser()));
        	}
        	if(StringUtils.isNotBlank(qto.getUnitTel())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("unitTel"), qto.getUnitTel()));
        	}
        	if(StringUtils.isNotBlank(qto.getSourceInformation())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sourceInformation"), qto.getSourceInformation()));
        	}
        	if(StringUtils.isNotBlank(qto.getOtherSource())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherSource"), qto.getOtherSource()));
        	}
            return predicate;
        };
    }

    private static CisAdvEventDrugRepository dao() {
		return SpringUtil.getBean(CisAdvEventDrugRepository.class);
	}

}
