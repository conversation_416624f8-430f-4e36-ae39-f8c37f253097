package com.bjgoodwill.hip.ds.cis.adv.pressure.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.pressure.entity.CisAdvEventPressureInjury;
import com.bjgoodwill.hip.ds.cis.adv.pressure.service.CisAdvEventPressureInjuryService;
import com.bjgoodwill.hip.ds.cis.adv.pressure.service.internal.assembler.CisAdvEventPressureInjuryAssembler;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureInjuryEto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureInjuryNto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureInjuryQto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureInjuryTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.pressure.service.CisAdvEventPressureInjuryService")
@RequestMapping(value = "/api/cisadv/pressure/cisAdvEventPressureInjury", produces = "application/json; charset=utf-8")
public class CisAdvEventPressureInjuryServiceImpl implements CisAdvEventPressureInjuryService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventPressureInjuryTo> getCisAdvEventPressureInjuries(CisAdvEventPressureInjuryQto cisAdvEventPressureInjuryQto) {
        return CisAdvEventPressureInjuryAssembler.toTos(CisAdvEventPressureInjury.getCisAdvEventPressureInjuries(cisAdvEventPressureInjuryQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventPressureInjuryTo> getCisAdvEventPressureInjuryPage(CisAdvEventPressureInjuryQto cisAdvEventPressureInjuryQto) {
        Page<CisAdvEventPressureInjury> page = CisAdvEventPressureInjury.getCisAdvEventPressureInjuryPage(cisAdvEventPressureInjuryQto);
        Page<CisAdvEventPressureInjuryTo> result = page.map(CisAdvEventPressureInjuryAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventPressureInjuryTo getCisAdvEventPressureInjuryById(String id) {
        return CisAdvEventPressureInjuryAssembler.toTo(CisAdvEventPressureInjury.getCisAdvEventPressureInjuryById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventPressureInjuryTo createCisAdvEventPressureInjury(CisAdvEventPressureInjuryNto cisAdvEventPressureInjuryNto) {
        CisAdvEventPressureInjury cisAdvEventPressureInjury = new CisAdvEventPressureInjury();
		cisAdvEventPressureInjury = cisAdvEventPressureInjury.create(cisAdvEventPressureInjuryNto);
		return CisAdvEventPressureInjuryAssembler.toTo(cisAdvEventPressureInjury);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventPressureInjury(String id, CisAdvEventPressureInjuryEto cisAdvEventPressureInjuryEto) {
        Optional<CisAdvEventPressureInjury> cisAdvEventPressureInjuryOptional = CisAdvEventPressureInjury.getCisAdvEventPressureInjuryById(id);
		cisAdvEventPressureInjuryOptional.ifPresent(cisAdvEventPressureInjury -> cisAdvEventPressureInjury.update(cisAdvEventPressureInjuryEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventPressureInjury(String id) {
        Optional<CisAdvEventPressureInjury> cisAdvEventPressureInjuryOptional = CisAdvEventPressureInjury.getCisAdvEventPressureInjuryById(id);
		cisAdvEventPressureInjuryOptional.ifPresent(cisAdvEventPressureInjury -> cisAdvEventPressureInjury.delete());
    }

    @InitBinder
	public void initBinder(WebDataBinder binder) {
	}
}