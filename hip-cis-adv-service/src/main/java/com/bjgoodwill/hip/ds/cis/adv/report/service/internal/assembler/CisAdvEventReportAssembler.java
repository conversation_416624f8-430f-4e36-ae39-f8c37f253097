package com.bjgoodwill.hip.ds.cis.adv.report.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.adv.report.entity.CisAdvEventReport;
import com.bjgoodwill.hip.ds.cis.adv.report.to.CisAdvEventReportTo;
import com.bjgoodwill.hip.ds.cis.adv.enmus.CisAdvBusinessErrorEnum;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventReportAssembler {

    public static List<CisAdvEventReportTo> toTos(List<CisAdvEventReport> cisAdvEventReports) {
		return toTos(cisAdvEventReports, false);
	}

	public static List<CisAdvEventReportTo> toTos(List<CisAdvEventReport> cisAdvEventReports, boolean withAllParts) {
		BusinessAssert.notEmpty(cisAdvEventReports, CisAdvBusinessErrorEnum.BUS_CIS_ADV_0001,"参数cisAdvEventReports");

		List<CisAdvEventReportTo> tos = new ArrayList<>();
		for (CisAdvEventReport cisAdvEventReport : cisAdvEventReports)
			tos.add(toTo(cisAdvEventReport, withAllParts));
		return tos;
	}

	public static CisAdvEventReportTo toTo(CisAdvEventReport cisAdvEventReport) {
		return toTo(cisAdvEventReport, false);
	}

	/**
	 * @generated
	 */
	public static CisAdvEventReportTo toTo(CisAdvEventReport cisAdvEventReport, boolean withAllParts) {
		if (cisAdvEventReport == null)
			return null;
		CisAdvEventReportTo to = new CisAdvEventReportTo();
        to.setId(cisAdvEventReport.getId());
        to.setPatMiCode(cisAdvEventReport.getPatMiCode());
        to.setPatType(cisAdvEventReport.getPatType());
        to.setInpatientCode(cisAdvEventReport.getInpatientCode());
        to.setVisitCode(cisAdvEventReport.getVisitCode());
        to.setPatName(cisAdvEventReport.getPatName());
        to.setSex(cisAdvEventReport.getSex());
        to.setBirthDate(cisAdvEventReport.getBirthDate());
        to.setWork(cisAdvEventReport.getWork());
        to.setAreaCode(cisAdvEventReport.getAreaCode());
        to.setAreaName(cisAdvEventReport.getAreaName());
        to.setBedName(cisAdvEventReport.getBedName());
        to.setEventDate(cisAdvEventReport.getEventDate());
        to.setClinicalDiagnosis(cisAdvEventReport.getClinicalDiagnosis());
        to.setEventPlace(cisAdvEventReport.getEventPlace());
        to.setOtherEventPlace(cisAdvEventReport.getOtherEventPlace());
        to.setAdvConsequencesFlag(cisAdvEventReport.getAdvConsequencesFlag());
        to.setAdvConsequences(cisAdvEventReport.getAdvConsequences());
        to.setEventAfter(cisAdvEventReport.getEventAfter());
        to.setEventType(cisAdvEventReport.getEventType());
        to.setEventCard(cisAdvEventReport.getEventCard());
        to.setAdditionFlag(cisAdvEventReport.isAdditionFlag());
        to.setEventHandle(cisAdvEventReport.getEventHandle());
        to.setEventLevel(cisAdvEventReport.getEventLevel());
        to.setEventWhy(cisAdvEventReport.getEventWhy());
        to.setEvaluationUser(cisAdvEventReport.getEvaluationUser());
        to.setEvaluationDate(cisAdvEventReport.getEvaluationDate());
        to.setImprovementMeasures(cisAdvEventReport.getImprovementMeasures());
        to.setImprovementUser(cisAdvEventReport.getImprovementUser());
        to.setImprovementUserName(cisAdvEventReport.getImprovementUserName());
        to.setImprovementDate(cisAdvEventReport.getImprovementDate());
        to.setReceiveOrgCode(cisAdvEventReport.getReceiveOrgCode());
        to.setReceiveOrgName(cisAdvEventReport.getReceiveOrgName());
        to.setOpinionState(cisAdvEventReport.getOpinionState());
        to.setOpinionStateUser(cisAdvEventReport.getOpinionStateUser());
        to.setOpinionStateUserName(cisAdvEventReport.getOpinionStateUserName());
        to.setOpinionStateDate(cisAdvEventReport.getOpinionStateDate());
        to.setReportUserType(cisAdvEventReport.getReportUserType());
        to.setReportUserTypeName(cisAdvEventReport.getReportUserTypeName());
        to.setLitigantType(cisAdvEventReport.getLitigantType());
        to.setTitle(cisAdvEventReport.getTitle());
        to.setTitleName(cisAdvEventReport.getTitleName());
        to.setDutyUser(cisAdvEventReport.getDutyUser());
        to.setDutyTitle(cisAdvEventReport.getDutyTitle());
        to.setDutyTitleName(cisAdvEventReport.getDutyTitleName());
        to.setWorkingLife(cisAdvEventReport.getWorkingLife());
        to.setWorkingLifeName(cisAdvEventReport.getWorkingLifeName());
        to.setDutyNurseLevel(cisAdvEventReport.getDutyNurseLevel());
        to.setDutyUser2(cisAdvEventReport.getDutyUser2());
        to.setDutyTitle2(cisAdvEventReport.getDutyTitle2());
        to.setDutyTitle2Name(cisAdvEventReport.getDutyTitle2Name());
        to.setWorkingLife2(cisAdvEventReport.getWorkingLife2());
        to.setWorkingLife2Name(cisAdvEventReport.getWorkingLife2Name());
        to.setDutyNurseLevel2(cisAdvEventReport.getDutyNurseLevel2());
        to.setReportUser(cisAdvEventReport.getReportUser());
        to.setReportOrgCode(cisAdvEventReport.getReportOrgCode());
        to.setReportTel(cisAdvEventReport.getReportTel());
        to.setReportDate(cisAdvEventReport.getReportDate());
        to.setBackUser(cisAdvEventReport.getBackUser());
        to.setBackRemarks(cisAdvEventReport.getBackRemarks());
        to.setBackDate(cisAdvEventReport.getBackDate());
        to.setCancelUser(cisAdvEventReport.getCancelUser());
        to.setCancelRemarks(cisAdvEventReport.getCancelRemarks());
        to.setCancelDate(cisAdvEventReport.getCancelDate());
        to.setHospitalCode(cisAdvEventReport.getHospitalCode());
        to.setStatusCode(cisAdvEventReport.getStatusCode());
        to.setCreatedDate(cisAdvEventReport.getCreatedDate());
        to.setCreatedStaff(cisAdvEventReport.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventReport.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventReport.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventReport.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventReport.getUpdatedStaffName());

		if (withAllParts) {
		}
		return to;
	}

}