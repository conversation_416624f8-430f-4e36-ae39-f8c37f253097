package com.bjgoodwill.hip.ds.cis.adv.nursing.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.nursing.entity.CisAdvEventNursing;
import com.bjgoodwill.hip.ds.cis.adv.nursing.to.CisAdvEventNursingTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventNursingAssembler {

    public static List<CisAdvEventNursingTo> toTos(List<CisAdvEventNursing> cisAdvEventNursings) {
        return toTos(cisAdvEventNursings, false);
    }

    public static List<CisAdvEventNursingTo> toTos(List<CisAdvEventNursing> cisAdvEventNursings, boolean withAllParts) {
        Assert.notNull(cisAdvEventNursings, "参数cisAdvEventNursings不能为空！");

        List<CisAdvEventNursingTo> tos = new ArrayList<>();
        for (CisAdvEventNursing cisAdvEventNursing : cisAdvEventNursings)
            tos.add(toTo(cisAdvEventNursing, withAllParts));
        return tos;
    }

    public static CisAdvEventNursingTo toTo(CisAdvEventNursing cisAdvEventNursing) {
        return toTo(cisAdvEventNursing, false);
    }

    /**
     * @generated
     */
    public static CisAdvEventNursingTo toTo(CisAdvEventNursing cisAdvEventNursing, boolean withAllParts) {
        if (cisAdvEventNursing == null)
            return null;
        CisAdvEventNursingTo to = new CisAdvEventNursingTo();
        to.setId(cisAdvEventNursing.getId());
        to.setEventReportId(cisAdvEventNursing.getEventReportId());
        to.setPatType(cisAdvEventNursing.getPatType());
        to.setInpatientCode(cisAdvEventNursing.getInpatientCode());
        to.setVisitCode(cisAdvEventNursing.getVisitCode());
        to.setPatName(cisAdvEventNursing.getPatName());
        to.setSex(cisAdvEventNursing.getSex());
        to.setBirthDate(cisAdvEventNursing.getBirthDate());
        to.setAreaCode(cisAdvEventNursing.getAreaCode());
        to.setAreaName(cisAdvEventNursing.getAreaName());
        to.setEventDate(cisAdvEventNursing.getEventDate());
        to.setEventLevel(cisAdvEventNursing.getEventLevel());
        to.setEventLevelName(cisAdvEventNursing.getEventLevelName());
        to.setEventDrugDefect(cisAdvEventNursing.getEventDrugDefect());
        to.setEventDrugDefectName(cisAdvEventNursing.getEventDrugDefectName());
        to.setDefectOther(cisAdvEventNursing.getDefectOther());
        to.setExecSystem(cisAdvEventNursing.getExecSystem());
        to.setExecSystemName(cisAdvEventNursing.getExecSystemName());
        to.setSystemOther(cisAdvEventNursing.getSystemOther());
        to.setPressureSoreType(cisAdvEventNursing.getPressureSoreType());
        to.setPressureSoreTypeName(cisAdvEventNursing.getPressureSoreTypeName());
        to.setPressureSoreOther(cisAdvEventNursing.getPressureSoreOther());
        to.setEventFall(cisAdvEventNursing.getEventFall());
        to.setEventFallName(cisAdvEventNursing.getEventFallName());
        to.setEventFallOther(cisAdvEventNursing.getEventFallOther());
        to.setFallingBed(cisAdvEventNursing.getFallingBed());
        to.setFallingBedOther(cisAdvEventNursing.getFallingBedOther());
        to.setEventSlippage(cisAdvEventNursing.getEventSlippage());
        to.setEventSlippageOther(cisAdvEventNursing.getEventSlippageOther());
        to.setEventHurt(cisAdvEventNursing.getEventHurt());
        to.setEventHurtOther(cisAdvEventNursing.getEventHurtOther());
        to.setEventBlood(cisAdvEventNursing.getEventBlood());
        to.setEventBloodOther(cisAdvEventNursing.getEventBloodOther());
        to.setEventIdentityErr(cisAdvEventNursing.getEventIdentityErr());
        to.setEventIdentityOther(cisAdvEventNursing.getEventIdentityOther());
        to.setEventOther(cisAdvEventNursing.getEventOther());
        to.setOtherRemark(cisAdvEventNursing.getOtherRemark());
        to.setDutyUser(cisAdvEventNursing.getDutyUser());
        to.setDutyTitle(cisAdvEventNursing.getDutyTitle());
        to.setDutyAge(cisAdvEventNursing.getDutyAge());
        to.setWorkingLife(cisAdvEventNursing.getWorkingLife());
        to.setEventProcess(cisAdvEventNursing.getEventProcess());
        to.setWorkingLifeName(cisAdvEventNursing.getWorkingLifeName());
        to.setDeptAnalysis(cisAdvEventNursing.getDeptAnalysis());
        to.setDeptOpinion(cisAdvEventNursing.getDeptOpinion());
        to.setImprovementMeasures(cisAdvEventNursing.getImprovementMeasures());
        to.setHeadSignature(cisAdvEventNursing.getHeadSignature());
        to.setHeadSignatureName(cisAdvEventNursing.getHeadSignatureName());
        to.setHeadSignatureDate(cisAdvEventNursing.getHeadSignatureDate());
        to.setNursDeptOpinion(cisAdvEventNursing.getNursDeptOpinion());
        to.setNursDeptSignature(cisAdvEventNursing.getNursDeptSignature());
        to.setNursDeptSignatureName(cisAdvEventNursing.getNursDeptSignatureName());
        to.setDeptSignatureDate(cisAdvEventNursing.getDeptSignatureDate());
        to.setCreatedDate(cisAdvEventNursing.getCreatedDate());
        to.setCreatedStaff(cisAdvEventNursing.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventNursing.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventNursing.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventNursing.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventNursing.getUpdatedStaffName());

        if (withAllParts) {
        }
        return to;
    }

}