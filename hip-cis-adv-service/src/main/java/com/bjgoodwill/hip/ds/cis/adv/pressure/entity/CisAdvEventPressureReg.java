package com.bjgoodwill.hip.ds.cis.adv.pressure.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.pressure.repository.CisAdvEventPressureRegRepository;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegEto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegNto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "医院获得性压力性损伤情况登记")
@Table(name = "cis_adv_event_pressure_reg", indexes = {}, uniqueConstraints = {})
public class CisAdvEventPressureReg {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("压力性损伤id")
    @Column(name = "pressure_id", nullable = true, length = 50)
    private String pressureId;


    @Comment("分期类型:a ⅰ期;bⅱ期；c ⅲ期；d ⅳ期；e不可分期；f可疑深部组织损伤；g粘膜压力性损伤；")
    @Column(name = "stages_type", nullable = true, length = 1)
    private String stagesType;


    @Comment("分期类型名称:a ⅰ期;bⅱ期；c ⅲ期；d ⅳ期；e不可分期；f可疑深部组织损伤；g粘膜压力性损伤；")
    @Column(name = "stages_type_name", nullable = true, length = 64)
    private String stagesTypeName;


    @Comment("其他病区带入压力性损伤")
    @Column(name = "other_area", nullable = false)
    private boolean otherArea;


    @Comment("带入损伤医疗器械相关压力性损伤")
    @Column(name = "other_injury", nullable = false)
    private boolean otherInjury;


    @Comment("是否入本病区24小时")
    @Column(name = "new_area", nullable = false)
    private boolean newArea;


    @Comment("医疗器械相关压力性损伤1是；0否")
    @Column(name = "new_injury", nullable = false)
    private boolean newInjury;


    @Comment("损伤部位骶尾椎骨 1是；0否")
    @Column(name = "caudal_vertebrae", nullable = false)
    private boolean caudalVertebrae;


    @Comment("损伤部位坐骨处	1是；0否")
    @Column(name = "sciatic_bone", nullable = false)
    private boolean sciaticBone;


    @Comment("损伤部位股骨粗隆处1是；0否")
    @Column(name = "femur", nullable = false)
    private boolean femur;


    @Comment("损伤部位跟骨处1是；0否")
    @Column(name = "calcaneus", nullable = false)
    private boolean calcaneus;


    @Comment("损伤部位足踝处1是；0否")
    @Column(name = "ankle", nullable = false)
    private boolean ankle;


    @Comment("损伤部位肩胛骨处	1是；0否")
    @Column(name = "scapula", nullable = false)
    private boolean scapula;


    @Comment("损伤部位枕骨处1是；0否")
    @Column(name = "occipital_bone", nullable = false)
    private boolean occipitalBone;


    @Comment("损伤部位其他部位1是；0否")
    @Column(name = "other_parts", nullable = false)
    private boolean otherParts;


    @Comment("损伤部位多处压力性	1是；0否")
    @Column(name = "multiple", nullable = false)
    private boolean multiple;


    @Comment("入本病区24小时后新发2期及以上院内压力性损伤部位数")
    @Column(name = "new_area_num", nullable = true)
    private Integer newAreaNum;


    @Comment("其中，医疗器械相关压力性损伤部位数")
    @Column(name = "new_injury_num", nullable = true)
    private Integer newInjuryNum;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    public String getId() {
    	return id;
    }

    protected void setId(String id) {
    	this.id = id;
    }

    public String getPressureId() {
    	return pressureId;
    }

    protected void setPressureId(String pressureId) {
    	this.pressureId = pressureId;
    }

    public String getStagesType() {
    	return stagesType;
    }

    protected void setStagesType(String stagesType) {
    	this.stagesType = stagesType;
    }

    public String getStagesTypeName() {
    	return stagesTypeName;
    }

    protected void setStagesTypeName(String stagesTypeName) {
    	this.stagesTypeName = stagesTypeName;
    }

    public boolean isOtherArea() {
    	return otherArea;
    }

    protected void setOtherArea(boolean otherArea) {
    	this.otherArea = otherArea;
    }

    public boolean isOtherInjury() {
    	return otherInjury;
    }

    protected void setOtherInjury(boolean otherInjury) {
    	this.otherInjury = otherInjury;
    }

    public boolean isNewArea() {
    	return newArea;
    }

    protected void setNewArea(boolean newArea) {
    	this.newArea = newArea;
    }

    public boolean isNewInjury() {
    	return newInjury;
    }

    protected void setNewInjury(boolean newInjury) {
    	this.newInjury = newInjury;
    }

    public boolean isCaudalVertebrae() {
    	return caudalVertebrae;
    }

    protected void setCaudalVertebrae(boolean caudalVertebrae) {
    	this.caudalVertebrae = caudalVertebrae;
    }

    public boolean isSciaticBone() {
    	return sciaticBone;
    }

    protected void setSciaticBone(boolean sciaticBone) {
    	this.sciaticBone = sciaticBone;
    }

    public boolean isFemur() {
    	return femur;
    }

    protected void setFemur(boolean femur) {
    	this.femur = femur;
    }

    public boolean isCalcaneus() {
    	return calcaneus;
    }

    protected void setCalcaneus(boolean calcaneus) {
    	this.calcaneus = calcaneus;
    }

    public boolean isAnkle() {
    	return ankle;
    }

    protected void setAnkle(boolean ankle) {
    	this.ankle = ankle;
    }

    public boolean isScapula() {
    	return scapula;
    }

    protected void setScapula(boolean scapula) {
    	this.scapula = scapula;
    }

    public boolean isOccipitalBone() {
    	return occipitalBone;
    }

    protected void setOccipitalBone(boolean occipitalBone) {
    	this.occipitalBone = occipitalBone;
    }

    public boolean isOtherParts() {
    	return otherParts;
    }

    protected void setOtherParts(boolean otherParts) {
    	this.otherParts = otherParts;
    }

    public boolean isMultiple() {
    	return multiple;
    }

    protected void setMultiple(boolean multiple) {
    	this.multiple = multiple;
    }

    public Integer getNewAreaNum() {
    	return newAreaNum;
    }

    protected void setNewAreaNum(Integer newAreaNum) {
    	this.newAreaNum = newAreaNum;
    }

    public Integer getNewInjuryNum() {
    	return newInjuryNum;
    }

    protected void setNewInjuryNum(Integer newInjuryNum) {
    	this.newInjuryNum = newInjuryNum;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventPressureReg other = (CisAdvEventPressureReg) obj;
		return Objects.equals(id, other.id);
	}

    public CisAdvEventPressureReg create(CisAdvEventPressureRegNto cisAdvEventPressureRegNto) {
        Assert.notNull(cisAdvEventPressureRegNto, "参数cisAdvEventPressureRegNto不能为空！");

        setId(cisAdvEventPressureRegNto.getId());
        setPressureId(cisAdvEventPressureRegNto.getPressureId());
        setStagesType(cisAdvEventPressureRegNto.getStagesType());
        setStagesTypeName(cisAdvEventPressureRegNto.getStagesTypeName());
        setOtherArea(cisAdvEventPressureRegNto.isOtherArea());
        setOtherInjury(cisAdvEventPressureRegNto.isOtherInjury());
        setNewArea(cisAdvEventPressureRegNto.isNewArea());
        setNewInjury(cisAdvEventPressureRegNto.isNewInjury());
        setCaudalVertebrae(cisAdvEventPressureRegNto.isCaudalVertebrae());
        setSciaticBone(cisAdvEventPressureRegNto.isSciaticBone());
        setFemur(cisAdvEventPressureRegNto.isFemur());
        setCalcaneus(cisAdvEventPressureRegNto.isCalcaneus());
        setAnkle(cisAdvEventPressureRegNto.isAnkle());
        setScapula(cisAdvEventPressureRegNto.isScapula());
        setOccipitalBone(cisAdvEventPressureRegNto.isOccipitalBone());
        setOtherParts(cisAdvEventPressureRegNto.isOtherParts());
        setMultiple(cisAdvEventPressureRegNto.isMultiple());
        setNewAreaNum(cisAdvEventPressureRegNto.getNewAreaNum());
        setNewInjuryNum(cisAdvEventPressureRegNto.getNewInjuryNum());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventPressureRegEto cisAdvEventPressureRegEto) {
        setPressureId(cisAdvEventPressureRegEto.getPressureId());
        setStagesType(cisAdvEventPressureRegEto.getStagesType());
        setStagesTypeName(cisAdvEventPressureRegEto.getStagesTypeName());
        setOtherArea(cisAdvEventPressureRegEto.isOtherArea());
        setOtherInjury(cisAdvEventPressureRegEto.isOtherInjury());
        setNewArea(cisAdvEventPressureRegEto.isNewArea());
        setNewInjury(cisAdvEventPressureRegEto.isNewInjury());
        setCaudalVertebrae(cisAdvEventPressureRegEto.isCaudalVertebrae());
        setSciaticBone(cisAdvEventPressureRegEto.isSciaticBone());
        setFemur(cisAdvEventPressureRegEto.isFemur());
        setCalcaneus(cisAdvEventPressureRegEto.isCalcaneus());
        setAnkle(cisAdvEventPressureRegEto.isAnkle());
        setScapula(cisAdvEventPressureRegEto.isScapula());
        setOccipitalBone(cisAdvEventPressureRegEto.isOccipitalBone());
        setOtherParts(cisAdvEventPressureRegEto.isOtherParts());
        setMultiple(cisAdvEventPressureRegEto.isMultiple());
        setNewAreaNum(cisAdvEventPressureRegEto.getNewAreaNum());
        setNewInjuryNum(cisAdvEventPressureRegEto.getNewInjuryNum());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

    public static Optional<CisAdvEventPressureReg> getCisAdvEventPressureRegById(String id) {
		return dao().findById(id);
	}

	public static List<CisAdvEventPressureReg> getCisAdvEventPressureRegs(CisAdvEventPressureRegQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
	}

	public static Page<CisAdvEventPressureReg> getCisAdvEventPressureRegPage(CisAdvEventPressureRegQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
	}


	/**
	 * @generated
	 */
    private static Specification<CisAdvEventPressureReg> getSpecification(CisAdvEventPressureRegQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
        	if(StringUtils.isNotBlank(qto.getPressureId())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("pressureId"), qto.getPressureId()));
        	}
        	if(StringUtils.isNotBlank(qto.getStagesType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("stagesType"), qto.getStagesType()));
        	}
        	if(StringUtils.isNotBlank(qto.getStagesTypeName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("stagesTypeName"), qto.getStagesTypeName()));
        	}
    		if(qto.getOtherArea() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherArea"), qto.getOtherArea()));
        	}
    		if(qto.getOtherInjury() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherInjury"), qto.getOtherInjury()));
        	}
    		if(qto.getNewArea() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("newArea"), qto.getNewArea()));
        	}
    		if(qto.getNewInjury() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("newInjury"), qto.getNewInjury()));
        	}
    		if(qto.getCaudalVertebrae() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("caudalVertebrae"), qto.getCaudalVertebrae()));
        	}
    		if(qto.getSciaticBone() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sciaticBone"), qto.getSciaticBone()));
        	}
    		if(qto.getFemur() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("femur"), qto.getFemur()));
        	}
    		if(qto.getCalcaneus() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("calcaneus"), qto.getCalcaneus()));
        	}
    		if(qto.getAnkle() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("ankle"), qto.getAnkle()));
        	}
    		if(qto.getScapula() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("scapula"), qto.getScapula()));
        	}
    		if(qto.getOccipitalBone() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("occipitalBone"), qto.getOccipitalBone()));
        	}
    		if(qto.getOtherParts() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherParts"), qto.getOtherParts()));
        	}
    		if(qto.getMultiple() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("multiple"), qto.getMultiple()));
        	}
    		if(qto.getNewAreaNum() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("newAreaNum"), qto.getNewAreaNum()));
        	}
    		if(qto.getNewInjuryNum() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("newInjuryNum"), qto.getNewInjuryNum()));
        	}
            return predicate;
        };
    }

    private static CisAdvEventPressureRegRepository dao() {
		return SpringUtil.getBean(CisAdvEventPressureRegRepository.class);
	}

}
