package com.bjgoodwill.hip.ds.cis.adv.cosmetics.service.internal.assembler;

import java.util.ArrayList;
import java.util.List;

import org.springframework.util.Assert;

import com.bjgoodwill.hip.ds.cis.adv.cosmetics.entity.*;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.*;

public abstract class CisAdvEventCosmeticsAssembler {

    public static List<CisAdvEventCosmeticsTo> toTos(List<CisAdvEventCosmetics> cisAdvEventCosmeticss) {
		return toTos(cisAdvEventCosmeticss, false);
	}

	public static List<CisAdvEventCosmeticsTo> toTos(List<CisAdvEventCosmetics> cisAdvEventCosmeticss, boolean withAllParts) {
		Assert.notNull(cisAdvEventCosmeticss, "参数cisAdvEventCosmeticss不能为空！");

		List<CisAdvEventCosmeticsTo> tos = new ArrayList<>();
		for (CisAdvEventCosmetics cisAdvEventCosmetics : cisAdvEventCosmeticss)
			tos.add(toTo(cisAdvEventCosmetics, withAllParts));
		return tos;
	}

	public static CisAdvEventCosmeticsTo toTo(CisAdvEventCosmetics cisAdvEventCosmetics) {
		return toTo(cisAdvEventCosmetics, false);
	}

	/**
	 * @generated
	 */
	public static CisAdvEventCosmeticsTo toTo(CisAdvEventCosmetics cisAdvEventCosmetics, boolean withAllParts) {
		if (cisAdvEventCosmetics == null)
			return null;
		CisAdvEventCosmeticsTo to = new CisAdvEventCosmeticsTo();
        to.setId(cisAdvEventCosmetics.getId());
        to.setEventReportId(cisAdvEventCosmetics.getEventReportId());
        to.setReportType(cisAdvEventCosmetics.getReportType());
        to.setReportTypeName(cisAdvEventCosmetics.getReportTypeName());
        to.setReportUnitCategory(cisAdvEventCosmetics.getReportUnitCategory());
        to.setReportUnitCategoryName(cisAdvEventCosmetics.getReportUnitCategoryName());
        to.setPatType(cisAdvEventCosmetics.getPatType());
        to.setInpatientCode(cisAdvEventCosmetics.getInpatientCode());
        to.setVisitCode(cisAdvEventCosmetics.getVisitCode());
        to.setPatName(cisAdvEventCosmetics.getPatName());
        to.setSex(cisAdvEventCosmetics.getSex());
        to.setNation(cisAdvEventCosmetics.getNation());
        to.setBirthDate(cisAdvEventCosmetics.getBirthDate());
        to.setContactTel(cisAdvEventCosmetics.getContactTel());
        to.setCzProvince(cisAdvEventCosmetics.getCzProvince());
        to.setCzProvinceName(cisAdvEventCosmetics.getCzProvinceName());
        to.setCzCity(cisAdvEventCosmetics.getCzCity());
        to.setCzCityName(cisAdvEventCosmetics.getCzCityName());
        to.setCzCounty(cisAdvEventCosmetics.getCzCounty());
        to.setCzCountyName(cisAdvEventCosmetics.getCzCountyName());
        to.setCzHomeJd(cisAdvEventCosmetics.getCzHomeJd());
        to.setCzHomeJdName(cisAdvEventCosmetics.getCzHomeJdName());
        to.setCzVillage(cisAdvEventCosmetics.getCzVillage());
        to.setCzVillageName(cisAdvEventCosmetics.getCzVillageName());
        to.setIsCosmeticsAllergy(cisAdvEventCosmetics.getIsCosmeticsAllergy());
        to.setCosmeticsAllergyRemark(cisAdvEventCosmetics.getCosmeticsAllergyRemark());
        to.setIsDrugAllergy(cisAdvEventCosmetics.getIsDrugAllergy());
        to.setDrugAllergyRemark(cisAdvEventCosmetics.getDrugAllergyRemark());
        to.setIsFoodAllergy(cisAdvEventCosmetics.getIsFoodAllergy());
        to.setFoodAllergyRemark(cisAdvEventCosmetics.getFoodAllergyRemark());
        to.setIsOtherAllergy(cisAdvEventCosmetics.getIsOtherAllergy());
        to.setOtherAllergyRemark(cisAdvEventCosmetics.getOtherAllergyRemark());
        to.setBeginDate(cisAdvEventCosmetics.getBeginDate());
        to.setEventDate(cisAdvEventCosmetics.getEventDate());
        to.setEndDate(cisAdvEventCosmetics.getEndDate());
        to.setEventProcess1(cisAdvEventCosmetics.getEventProcess1());
        to.setEventProcess11(cisAdvEventCosmetics.getEventProcess11());
        to.setEventProcess12(cisAdvEventCosmetics.getEventProcess12());
        to.setEventProcess2(cisAdvEventCosmetics.getEventProcess2());
        to.setEventProcess21(cisAdvEventCosmetics.getEventProcess21());
        to.setEventProcess3(cisAdvEventCosmetics.getEventProcess3());
        to.setEventProcess31(cisAdvEventCosmetics.getEventProcess31());
        to.setEventProcess4(cisAdvEventCosmetics.getEventProcess4());
        to.setEventProcess41(cisAdvEventCosmetics.getEventProcess41());
        to.setEventProcess5(cisAdvEventCosmetics.getEventProcess5());
        to.setEventProcess51(cisAdvEventCosmetics.getEventProcess51());
        to.setEventProcessRemark(cisAdvEventCosmetics.getEventProcessRemark());
        to.setPreliminaryDiag(cisAdvEventCosmetics.getPreliminaryDiag());
        to.setPreliminaryDiagOther(cisAdvEventCosmetics.getPreliminaryDiagOther());
        to.setEventResult(cisAdvEventCosmetics.getEventResult());
        to.setEventResultName(cisAdvEventCosmetics.getEventResultName());
        to.setResultComplication(cisAdvEventCosmetics.getResultComplication());
        to.setResultOther(cisAdvEventCosmetics.getResultOther());
        to.setUseCosmetics(cisAdvEventCosmetics.getUseCosmetics());
        to.setGbCode(cisAdvEventCosmetics.getGbCode());
        to.setCosmeticsName(cisAdvEventCosmetics.getCosmeticsName());
        to.setBrandName(cisAdvEventCosmetics.getBrandName());
        to.setCommonName(cisAdvEventCosmetics.getCommonName());
        to.setPropertyName(cisAdvEventCosmetics.getPropertyName());
        to.setCosmeticsClass(cisAdvEventCosmetics.getCosmeticsClass());
        to.setCosmeticsClassName(cisAdvEventCosmetics.getCosmeticsClassName());
        to.setManufactureFirm(cisAdvEventCosmetics.getManufactureFirm());
        to.setBatchNo(cisAdvEventCosmetics.getBatchNo());
        to.setProductSource(cisAdvEventCosmetics.getProductSource());
        to.setProductSourceName(cisAdvEventCosmetics.getProductSourceName());
        to.setBuySite(cisAdvEventCosmetics.getBuySite());
        to.setPatchTest1(cisAdvEventCosmetics.getPatchTest1());
        to.setPatchTest2(cisAdvEventCosmetics.getPatchTest2());
        to.setPatchTest3(cisAdvEventCosmetics.isPatchTest3());
        to.setPatchTest4(cisAdvEventCosmetics.getPatchTest4());
        to.setAllergenPatchTest1(cisAdvEventCosmetics.isAllergenPatchTest1());
        to.setAllergenPatchTest2(cisAdvEventCosmetics.isAllergenPatchTest2());
        to.setAllergenPatchTest3(cisAdvEventCosmetics.getAllergenPatchTest3());
        to.setOtherAuxExam1(cisAdvEventCosmetics.getOtherAuxExam1());
        to.setOtherAuxExam2(cisAdvEventCosmetics.getOtherAuxExam2());
        to.setOtherAuxExam3(cisAdvEventCosmetics.getOtherAuxExam3());
        to.setRelevanceEval1(cisAdvEventCosmetics.isRelevanceEval1());
        to.setRelevanceEval2(cisAdvEventCosmetics.getRelevanceEval2());
        to.setRelevanceEval3(cisAdvEventCosmetics.getRelevanceEval3());
        to.setRelevanceEval4(cisAdvEventCosmetics.getRelevanceEval4());
        to.setRelevanceEval5(cisAdvEventCosmetics.getRelevanceEval5());
        to.setReportRemark(cisAdvEventCosmetics.getReportRemark());
        to.setReportUser(cisAdvEventCosmetics.getReportUser());
        to.setReportUserName(cisAdvEventCosmetics.getReportUserName());
        to.setReportTel(cisAdvEventCosmetics.getReportTel());
        to.setReportDate(cisAdvEventCosmetics.getReportDate());
        to.setReportUserWork(cisAdvEventCosmetics.getReportUserWork());
        to.setReportUserWorkName(cisAdvEventCosmetics.getReportUserWorkName());
        to.setReportUnit(cisAdvEventCosmetics.getReportUnit());
        to.setRemark(cisAdvEventCosmetics.getRemark());
        to.setAttachmentName(cisAdvEventCosmetics.getAttachmentName());
        to.setCreatedDate(cisAdvEventCosmetics.getCreatedDate());
        to.setCreatedStaff(cisAdvEventCosmetics.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventCosmetics.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventCosmetics.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventCosmetics.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventCosmetics.getUpdatedStaffName());

		if (withAllParts) {
		}
		return to;
	}

}