package com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.entity.CisAdvEventPiccBloodInfection;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service.CisAdvEventPiccBloodInfectionService;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service.internal.assembler.CisAdvEventPiccBloodInfectionAssembler;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionEto;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionNto;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionQto;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service.CisAdvEventPiccBloodInfectionService")
@RequestMapping(value = "/api/cisadv/bloodinfection/cisAdvEventPiccBloodInfection", produces = "application/json; charset=utf-8")
public class CisAdvEventPiccBloodInfectionServiceImpl implements CisAdvEventPiccBloodInfectionService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventPiccBloodInfectionTo> getCisAdvEventPiccBloodInfections(CisAdvEventPiccBloodInfectionQto cisAdvEventPiccBloodInfectionQto) {
        return CisAdvEventPiccBloodInfectionAssembler.toTos(CisAdvEventPiccBloodInfection.getCisAdvEventPiccBloodInfections(cisAdvEventPiccBloodInfectionQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventPiccBloodInfectionTo> getCisAdvEventPiccBloodInfectionPage(CisAdvEventPiccBloodInfectionQto cisAdvEventPiccBloodInfectionQto) {
        Page<CisAdvEventPiccBloodInfection> page = CisAdvEventPiccBloodInfection.getCisAdvEventPiccBloodInfectionPage(cisAdvEventPiccBloodInfectionQto);
        Page<CisAdvEventPiccBloodInfectionTo> result = page.map(CisAdvEventPiccBloodInfectionAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventPiccBloodInfectionTo getCisAdvEventPiccBloodInfectionById(String id) {
        return CisAdvEventPiccBloodInfectionAssembler.toTo(CisAdvEventPiccBloodInfection.getCisAdvEventPiccBloodInfectionById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventPiccBloodInfectionTo createCisAdvEventPiccBloodInfection(CisAdvEventPiccBloodInfectionNto cisAdvEventPiccBloodInfectionNto) {
        CisAdvEventPiccBloodInfection cisAdvEventPiccBloodInfection = new CisAdvEventPiccBloodInfection();
        cisAdvEventPiccBloodInfection = cisAdvEventPiccBloodInfection.create(cisAdvEventPiccBloodInfectionNto);
        return CisAdvEventPiccBloodInfectionAssembler.toTo(cisAdvEventPiccBloodInfection);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventPiccBloodInfection(String id, CisAdvEventPiccBloodInfectionEto cisAdvEventPiccBloodInfectionEto) {
        Optional<CisAdvEventPiccBloodInfection> cisAdvEventPiccBloodInfectionOptional = CisAdvEventPiccBloodInfection.getCisAdvEventPiccBloodInfectionById(id);
        cisAdvEventPiccBloodInfectionOptional.ifPresent(cisAdvEventPiccBloodInfection -> cisAdvEventPiccBloodInfection.update(cisAdvEventPiccBloodInfectionEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventPiccBloodInfection(String id) {
        Optional<CisAdvEventPiccBloodInfection> cisAdvEventPiccBloodInfectionOptional = CisAdvEventPiccBloodInfection.getCisAdvEventPiccBloodInfectionById(id);
        cisAdvEventPiccBloodInfectionOptional.ifPresent(cisAdvEventPiccBloodInfection -> cisAdvEventPiccBloodInfection.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}