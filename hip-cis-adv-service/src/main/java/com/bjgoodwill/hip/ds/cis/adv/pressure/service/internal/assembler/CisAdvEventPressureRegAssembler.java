package com.bjgoodwill.hip.ds.cis.adv.pressure.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.pressure.entity.CisAdvEventPressureReg;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventPressureRegAssembler {

    public static List<CisAdvEventPressureRegTo> toTos(List<CisAdvEventPressureReg> cisAdvEventPressureRegs) {
        return toTos(cisAdvEventPressureRegs, false);
    }

    public static List<CisAdvEventPressureRegTo> toTos(List<CisAdvEventPressureReg> cisAdvEventPressureRegs, boolean withAllParts) {
        Assert.notNull(cisAdvEventPressureRegs, "参数cisAdvEventPressureRegs不能为空！");

        List<CisAdvEventPressureRegTo> tos = new ArrayList<>();
        for (CisAdvEventPressureReg cisAdvEventPressureReg : cisAdvEventPressureRegs)
            tos.add(toTo(cisAdvEventPressureReg, withAllParts));
        return tos;
    }

    public static CisAdvEventPressureRegTo toTo(CisAdvEventPressureReg cisAdvEventPressureReg) {
        return toTo(cisAdvEventPressureReg, false);
    }

    /**
     * @generated
     */
    public static CisAdvEventPressureRegTo toTo(CisAdvEventPressureReg cisAdvEventPressureReg, boolean withAllParts) {
        if (cisAdvEventPressureReg == null)
            return null;
        CisAdvEventPressureRegTo to = new CisAdvEventPressureRegTo();
        to.setId(cisAdvEventPressureReg.getId());
        to.setPressureId(cisAdvEventPressureReg.getPressureId());
        to.setStagesType(cisAdvEventPressureReg.getStagesType());
        to.setStagesTypeName(cisAdvEventPressureReg.getStagesTypeName());
        to.setOtherArea(cisAdvEventPressureReg.isOtherArea());
        to.setOtherInjury(cisAdvEventPressureReg.isOtherInjury());
        to.setNewArea(cisAdvEventPressureReg.isNewArea());
        to.setNewInjury(cisAdvEventPressureReg.isNewInjury());
        to.setCaudalVertebrae(cisAdvEventPressureReg.isCaudalVertebrae());
        to.setSciaticBone(cisAdvEventPressureReg.isSciaticBone());
        to.setFemur(cisAdvEventPressureReg.isFemur());
        to.setCalcaneus(cisAdvEventPressureReg.isCalcaneus());
        to.setAnkle(cisAdvEventPressureReg.isAnkle());
        to.setScapula(cisAdvEventPressureReg.isScapula());
        to.setOccipitalBone(cisAdvEventPressureReg.isOccipitalBone());
        to.setOtherParts(cisAdvEventPressureReg.isOtherParts());
        to.setMultiple(cisAdvEventPressureReg.isMultiple());
        to.setNewAreaNum(cisAdvEventPressureReg.getNewAreaNum());
        to.setNewInjuryNum(cisAdvEventPressureReg.getNewInjuryNum());
        to.setCreatedDate(cisAdvEventPressureReg.getCreatedDate());
        to.setCreatedStaff(cisAdvEventPressureReg.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventPressureReg.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventPressureReg.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventPressureReg.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventPressureReg.getUpdatedStaffName());

        if (withAllParts) {
        }
        return to;
    }

}