package com.bjgoodwill.hip.ds.cis.adv.instrument.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.instrument.repository.CisAdvEventInstrumentRepository;
import com.bjgoodwill.hip.ds.cis.adv.instrument.to.CisAdvEventInstrumentEto;
import com.bjgoodwill.hip.ds.cis.adv.instrument.to.CisAdvEventInstrumentNto;
import com.bjgoodwill.hip.ds.cis.adv.instrument.to.CisAdvEventInstrumentQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "医疗器械不良事件报告")
@Table(name = "cis_adv_event_instrument", indexes = {}, uniqueConstraints = {})
public class CisAdvEventInstrument {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件id")
    @Column(name = "event_report_id", nullable = true, length = 50)
    private String eventReportId;


    @Comment("患者类型")
    @Column(name = "pat_type", nullable = true, length = 2)
    private String patType;


    @Comment("住院号(门诊就诊卡号)")
    @Column(name = "inpatient_code", nullable = true, length = 16)
    private String inpatientCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true, length = 16)
    private String visitCode;


    @Comment("患者姓名")
    @Column(name = "pat_name", nullable = true, length = 64)
    private String patName;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("病区科室")
    @Column(name = "area_code", nullable = true, length = 16)
    private String areaCode;


    @Comment("病区科室名称")
    @Column(name = "area_name", nullable = true, length = 32)
    private String areaName;


    @Comment("既往病史")
    @Column(name = "past_value", nullable = true)
    private String pastValue;


    @Comment("产品名称")
    @Column(name = "instrument_name", nullable = true)
    private String instrumentName;


    @Comment("注册证编号")
    @Column(name = "instrument_trademark", nullable = true, length = 64)
    private String instrumentTrademark;


    @Comment("型号规格")
    @Column(name = "instrument_model", nullable = true, length = 64)
    private String instrumentModel;


    @Comment("产品批号")
    @Column(name = "instrument_batch_no", nullable = true, length = 64)
    private String instrumentBatchNo;


    @Comment("产品编号")
    @Column(name = "instrument_no", nullable = true, length = 64)
    private String instrumentNo;


    @Comment("udi器械识别码")
    @Column(name = "instrument_udi", nullable = true, length = 64)
    private String instrumentUdi;


    @Comment("生产日期")
    @Column(name = "manufacture_date", nullable = true)
    private LocalDateTime manufactureDate;


    @Comment("有效期至")
    @Column(name = "valid_until", nullable = true)
    private LocalDateTime validUntil;


    @Comment("生产企业")
    @Column(name = "source_information", nullable = true)
    private String sourceInformation;


    @Comment("事件发生时间")
    @Column(name = "event_date", nullable = true)
    private LocalDateTime eventDate;


    @Comment("发现或获知日期")
    @Column(name = "knowledge_date", nullable = true)
    private LocalDateTime knowledgeDate;


    @Comment("伤害程度:death死亡；serious严重伤害；other其他；")
    @Column(name = "injury_degree", nullable = true, length = 16)
    private String injuryDegree;


    @Comment("伤害程度名称")
    @Column(name = "injury_degree_name", nullable = true, length = 32)
    private String injuryDegreeName;


    @Comment("伤害表现")
    @Column(name = "injury_performance", nullable = true)
    private String injuryPerformance;


    @Comment("器械故障表现")
    @Column(name = "fault_performance", nullable = true)
    private String faultPerformance;


    @Comment("预期治疗疾病或作用")
    @Column(name = "treatment_effect", nullable = true)
    private String treatmentEffect;


    @Comment("器械使用日期")
    @Column(name = "use_date", nullable = true)
    private LocalDateTime useDate;


    @Comment("使用场所:medical医疗机构；family家庭；other其他")
    @Column(name = "use_place", nullable = true, length = 16)
    private String usePlace;


    @Comment("使用场所名称")
    @Column(name = "use_place_name", nullable = true, length = 32)
    private String usePlaceName;


    @Comment("使用过程")
    @Column(name = "use_process", nullable = true)
    private String useProcess;


    @Comment("合并用药器械说明")
    @Column(name = "use_combined", nullable = true)
    private String useCombined;


    @Comment("事件原因分析:product产品原因；operation操作原因；oneself患者自身原因；undetermined无法确定；")
    @Column(name = "event_reasons_type", nullable = true, length = 16)
    private String eventReasonsType;


    @Comment("事件原因分析名称")
    @Column(name = "event_reasons_type_name", nullable = true, length = 32)
    private String eventReasonsTypeName;


    @Comment("事件原因分析描述")
    @Column(name = "event_reasons_describe", nullable = true)
    private String eventReasonsDescribe;


    @Comment("初步处置情况")
    @Column(name = "disposal_situation", nullable = true)
    private String disposalSituation;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    public String getId() {
    	return id;
    }

    protected void setId(String id) {
    	this.id = id;
    }

    public String getEventReportId() {
    	return eventReportId;
    }

    protected void setEventReportId(String eventReportId) {
    	this.eventReportId = eventReportId;
    }

    public String getPatType() {
    	return patType;
    }

    protected void setPatType(String patType) {
    	this.patType = patType;
    }

    public String getInpatientCode() {
    	return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
    	this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    protected void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public String getPatName() {
    	return patName;
    }

    protected void setPatName(String patName) {
    	this.patName = patName;
    }

    public String getSex() {
    	return sex;
    }

    protected void setSex(String sex) {
    	this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
    	this.birthDate = birthDate;
    }

    public String getAreaCode() {
    	return areaCode;
    }

    protected void setAreaCode(String areaCode) {
    	this.areaCode = areaCode;
    }

    public String getAreaName() {
    	return areaName;
    }

    protected void setAreaName(String areaName) {
    	this.areaName = areaName;
    }

    public String getPastValue() {
    	return pastValue;
    }

    protected void setPastValue(String pastValue) {
    	this.pastValue = pastValue;
    }

    public String getInstrumentName() {
    	return instrumentName;
    }

    protected void setInstrumentName(String instrumentName) {
    	this.instrumentName = instrumentName;
    }

    public String getInstrumentTrademark() {
    	return instrumentTrademark;
    }

    protected void setInstrumentTrademark(String instrumentTrademark) {
    	this.instrumentTrademark = instrumentTrademark;
    }

    public String getInstrumentModel() {
    	return instrumentModel;
    }

    protected void setInstrumentModel(String instrumentModel) {
    	this.instrumentModel = instrumentModel;
    }

    public String getInstrumentBatchNo() {
    	return instrumentBatchNo;
    }

    protected void setInstrumentBatchNo(String instrumentBatchNo) {
    	this.instrumentBatchNo = instrumentBatchNo;
    }

    public String getInstrumentNo() {
    	return instrumentNo;
    }

    protected void setInstrumentNo(String instrumentNo) {
    	this.instrumentNo = instrumentNo;
    }

    public String getInstrumentUdi() {
    	return instrumentUdi;
    }

    protected void setInstrumentUdi(String instrumentUdi) {
    	this.instrumentUdi = instrumentUdi;
    }

    public LocalDateTime getManufactureDate() {
    	return manufactureDate;
    }

    protected void setManufactureDate(LocalDateTime manufactureDate) {
    	this.manufactureDate = manufactureDate;
    }

    public LocalDateTime getValidUntil() {
    	return validUntil;
    }

    protected void setValidUntil(LocalDateTime validUntil) {
    	this.validUntil = validUntil;
    }

    public String getSourceInformation() {
    	return sourceInformation;
    }

    protected void setSourceInformation(String sourceInformation) {
    	this.sourceInformation = sourceInformation;
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    protected void setEventDate(LocalDateTime eventDate) {
    	this.eventDate = eventDate;
    }

    public LocalDateTime getKnowledgeDate() {
    	return knowledgeDate;
    }

    protected void setKnowledgeDate(LocalDateTime knowledgeDate) {
    	this.knowledgeDate = knowledgeDate;
    }

    public String getInjuryDegree() {
    	return injuryDegree;
    }

    protected void setInjuryDegree(String injuryDegree) {
    	this.injuryDegree = injuryDegree;
    }

    public String getInjuryDegreeName() {
    	return injuryDegreeName;
    }

    protected void setInjuryDegreeName(String injuryDegreeName) {
    	this.injuryDegreeName = injuryDegreeName;
    }

    public String getInjuryPerformance() {
    	return injuryPerformance;
    }

    protected void setInjuryPerformance(String injuryPerformance) {
    	this.injuryPerformance = injuryPerformance;
    }

    public String getFaultPerformance() {
    	return faultPerformance;
    }

    protected void setFaultPerformance(String faultPerformance) {
    	this.faultPerformance = faultPerformance;
    }

    public String getTreatmentEffect() {
    	return treatmentEffect;
    }

    protected void setTreatmentEffect(String treatmentEffect) {
    	this.treatmentEffect = treatmentEffect;
    }

    public LocalDateTime getUseDate() {
    	return useDate;
    }

    protected void setUseDate(LocalDateTime useDate) {
    	this.useDate = useDate;
    }

    public String getUsePlace() {
    	return usePlace;
    }

    protected void setUsePlace(String usePlace) {
    	this.usePlace = usePlace;
    }

    public String getUsePlaceName() {
    	return usePlaceName;
    }

    protected void setUsePlaceName(String usePlaceName) {
    	this.usePlaceName = usePlaceName;
    }

    public String getUseProcess() {
    	return useProcess;
    }

    protected void setUseProcess(String useProcess) {
    	this.useProcess = useProcess;
    }

    public String getUseCombined() {
    	return useCombined;
    }

    protected void setUseCombined(String useCombined) {
    	this.useCombined = useCombined;
    }

    public String getEventReasonsType() {
    	return eventReasonsType;
    }

    protected void setEventReasonsType(String eventReasonsType) {
    	this.eventReasonsType = eventReasonsType;
    }

    public String getEventReasonsTypeName() {
    	return eventReasonsTypeName;
    }

    protected void setEventReasonsTypeName(String eventReasonsTypeName) {
    	this.eventReasonsTypeName = eventReasonsTypeName;
    }

    public String getEventReasonsDescribe() {
    	return eventReasonsDescribe;
    }

    protected void setEventReasonsDescribe(String eventReasonsDescribe) {
    	this.eventReasonsDescribe = eventReasonsDescribe;
    }

    public String getDisposalSituation() {
    	return disposalSituation;
    }

    protected void setDisposalSituation(String disposalSituation) {
    	this.disposalSituation = disposalSituation;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventInstrument other = (CisAdvEventInstrument) obj;
		return Objects.equals(id, other.id);
	}

    public CisAdvEventInstrument create(CisAdvEventInstrumentNto cisAdvEventInstrumentNto) {
        Assert.notNull(cisAdvEventInstrumentNto, "参数cisAdvEventInstrumentNto不能为空！");

        setId(cisAdvEventInstrumentNto.getId());
        setEventReportId(cisAdvEventInstrumentNto.getEventReportId());
        setPatType(cisAdvEventInstrumentNto.getPatType());
        setInpatientCode(cisAdvEventInstrumentNto.getInpatientCode());
        setVisitCode(cisAdvEventInstrumentNto.getVisitCode());
        setPatName(cisAdvEventInstrumentNto.getPatName());
        setSex(cisAdvEventInstrumentNto.getSex());
        setBirthDate(cisAdvEventInstrumentNto.getBirthDate());
        setAreaCode(cisAdvEventInstrumentNto.getAreaCode());
        setAreaName(cisAdvEventInstrumentNto.getAreaName());
        setPastValue(cisAdvEventInstrumentNto.getPastValue());
        setInstrumentName(cisAdvEventInstrumentNto.getInstrumentName());
        setInstrumentTrademark(cisAdvEventInstrumentNto.getInstrumentTrademark());
        setInstrumentModel(cisAdvEventInstrumentNto.getInstrumentModel());
        setInstrumentBatchNo(cisAdvEventInstrumentNto.getInstrumentBatchNo());
        setInstrumentNo(cisAdvEventInstrumentNto.getInstrumentNo());
        setInstrumentUdi(cisAdvEventInstrumentNto.getInstrumentUdi());
        setManufactureDate(cisAdvEventInstrumentNto.getManufactureDate());
        setValidUntil(cisAdvEventInstrumentNto.getValidUntil());
        setSourceInformation(cisAdvEventInstrumentNto.getSourceInformation());
        setEventDate(cisAdvEventInstrumentNto.getEventDate());
        setKnowledgeDate(cisAdvEventInstrumentNto.getKnowledgeDate());
        setInjuryDegree(cisAdvEventInstrumentNto.getInjuryDegree());
        setInjuryDegreeName(cisAdvEventInstrumentNto.getInjuryDegreeName());
        setInjuryPerformance(cisAdvEventInstrumentNto.getInjuryPerformance());
        setFaultPerformance(cisAdvEventInstrumentNto.getFaultPerformance());
        setTreatmentEffect(cisAdvEventInstrumentNto.getTreatmentEffect());
        setUseDate(cisAdvEventInstrumentNto.getUseDate());
        setUsePlace(cisAdvEventInstrumentNto.getUsePlace());
        setUsePlaceName(cisAdvEventInstrumentNto.getUsePlaceName());
        setUseProcess(cisAdvEventInstrumentNto.getUseProcess());
        setUseCombined(cisAdvEventInstrumentNto.getUseCombined());
        setEventReasonsType(cisAdvEventInstrumentNto.getEventReasonsType());
        setEventReasonsTypeName(cisAdvEventInstrumentNto.getEventReasonsTypeName());
        setEventReasonsDescribe(cisAdvEventInstrumentNto.getEventReasonsDescribe());
        setDisposalSituation(cisAdvEventInstrumentNto.getDisposalSituation());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventInstrumentEto cisAdvEventInstrumentEto) {
        setEventReportId(cisAdvEventInstrumentEto.getEventReportId());
        setPatType(cisAdvEventInstrumentEto.getPatType());
        setInpatientCode(cisAdvEventInstrumentEto.getInpatientCode());
        setVisitCode(cisAdvEventInstrumentEto.getVisitCode());
        setPatName(cisAdvEventInstrumentEto.getPatName());
        setSex(cisAdvEventInstrumentEto.getSex());
        setBirthDate(cisAdvEventInstrumentEto.getBirthDate());
        setAreaCode(cisAdvEventInstrumentEto.getAreaCode());
        setAreaName(cisAdvEventInstrumentEto.getAreaName());
        setPastValue(cisAdvEventInstrumentEto.getPastValue());
        setInstrumentName(cisAdvEventInstrumentEto.getInstrumentName());
        setInstrumentTrademark(cisAdvEventInstrumentEto.getInstrumentTrademark());
        setInstrumentModel(cisAdvEventInstrumentEto.getInstrumentModel());
        setInstrumentBatchNo(cisAdvEventInstrumentEto.getInstrumentBatchNo());
        setInstrumentNo(cisAdvEventInstrumentEto.getInstrumentNo());
        setInstrumentUdi(cisAdvEventInstrumentEto.getInstrumentUdi());
        setManufactureDate(cisAdvEventInstrumentEto.getManufactureDate());
        setValidUntil(cisAdvEventInstrumentEto.getValidUntil());
        setSourceInformation(cisAdvEventInstrumentEto.getSourceInformation());
        setEventDate(cisAdvEventInstrumentEto.getEventDate());
        setKnowledgeDate(cisAdvEventInstrumentEto.getKnowledgeDate());
        setInjuryDegree(cisAdvEventInstrumentEto.getInjuryDegree());
        setInjuryDegreeName(cisAdvEventInstrumentEto.getInjuryDegreeName());
        setInjuryPerformance(cisAdvEventInstrumentEto.getInjuryPerformance());
        setFaultPerformance(cisAdvEventInstrumentEto.getFaultPerformance());
        setTreatmentEffect(cisAdvEventInstrumentEto.getTreatmentEffect());
        setUseDate(cisAdvEventInstrumentEto.getUseDate());
        setUsePlace(cisAdvEventInstrumentEto.getUsePlace());
        setUsePlaceName(cisAdvEventInstrumentEto.getUsePlaceName());
        setUseProcess(cisAdvEventInstrumentEto.getUseProcess());
        setUseCombined(cisAdvEventInstrumentEto.getUseCombined());
        setEventReasonsType(cisAdvEventInstrumentEto.getEventReasonsType());
        setEventReasonsTypeName(cisAdvEventInstrumentEto.getEventReasonsTypeName());
        setEventReasonsDescribe(cisAdvEventInstrumentEto.getEventReasonsDescribe());
        setDisposalSituation(cisAdvEventInstrumentEto.getDisposalSituation());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

    public static Optional<CisAdvEventInstrument> getCisAdvEventInstrumentById(String id) {
		return dao().findById(id);
	}

	public static List<CisAdvEventInstrument> getCisAdvEventInstruments(CisAdvEventInstrumentQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
	}

	public static Page<CisAdvEventInstrument> getCisAdvEventInstrumentPage(CisAdvEventInstrumentQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
	}


	/**
	 * @generated
	 */
    private static Specification<CisAdvEventInstrument> getSpecification(CisAdvEventInstrumentQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
        	if(StringUtils.isNotBlank(qto.getEventReportId())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReportId"), qto.getEventReportId()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patType"), qto.getPatType()));
        	}
        	if(StringUtils.isNotBlank(qto.getInpatientCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getVisitCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patName"), qto.getPatName()));
        	}
        	if(StringUtils.isNotBlank(qto.getSex())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
        	}
    		if(qto.getBirthDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getAreaCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaCode"), qto.getAreaCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getAreaName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaName"), qto.getAreaName()));
        	}
        	if(StringUtils.isNotBlank(qto.getPastValue())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("pastValue"), qto.getPastValue()));
        	}
        	if(StringUtils.isNotBlank(qto.getInstrumentName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("instrumentName"), qto.getInstrumentName()));
        	}
        	if(StringUtils.isNotBlank(qto.getInstrumentTrademark())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("instrumentTrademark"), qto.getInstrumentTrademark()));
        	}
        	if(StringUtils.isNotBlank(qto.getInstrumentModel())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("instrumentModel"), qto.getInstrumentModel()));
        	}
        	if(StringUtils.isNotBlank(qto.getInstrumentBatchNo())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("instrumentBatchNo"), qto.getInstrumentBatchNo()));
        	}
        	if(StringUtils.isNotBlank(qto.getInstrumentNo())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("instrumentNo"), qto.getInstrumentNo()));
        	}
        	if(StringUtils.isNotBlank(qto.getInstrumentUdi())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("instrumentUdi"), qto.getInstrumentUdi()));
        	}
    		if(qto.getManufactureDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("manufactureDate"), LocalDateUtil.beginOfDay(qto.getManufactureDate()), LocalDateUtil.endOfDay(qto.getManufactureDate())));
        	}
    		if(qto.getValidUntil() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("validUntil"), LocalDateUtil.beginOfDay(qto.getValidUntil()), LocalDateUtil.endOfDay(qto.getValidUntil())));
        	}
        	if(StringUtils.isNotBlank(qto.getSourceInformation())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sourceInformation"), qto.getSourceInformation()));
        	}
    		if(qto.getEventDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("eventDate"), LocalDateUtil.beginOfDay(qto.getEventDate()), LocalDateUtil.endOfDay(qto.getEventDate())));
        	}
    		if(qto.getKnowledgeDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("knowledgeDate"), LocalDateUtil.beginOfDay(qto.getKnowledgeDate()), LocalDateUtil.endOfDay(qto.getKnowledgeDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getInjuryDegree())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("injuryDegree"), qto.getInjuryDegree()));
        	}
        	if(StringUtils.isNotBlank(qto.getInjuryDegreeName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("injuryDegreeName"), qto.getInjuryDegreeName()));
        	}
        	if(StringUtils.isNotBlank(qto.getInjuryPerformance())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("injuryPerformance"), qto.getInjuryPerformance()));
        	}
        	if(StringUtils.isNotBlank(qto.getFaultPerformance())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("faultPerformance"), qto.getFaultPerformance()));
        	}
        	if(StringUtils.isNotBlank(qto.getTreatmentEffect())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("treatmentEffect"), qto.getTreatmentEffect()));
        	}
    		if(qto.getUseDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("useDate"), LocalDateUtil.beginOfDay(qto.getUseDate()), LocalDateUtil.endOfDay(qto.getUseDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getUsePlace())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("usePlace"), qto.getUsePlace()));
        	}
        	if(StringUtils.isNotBlank(qto.getUsePlaceName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("usePlaceName"), qto.getUsePlaceName()));
        	}
        	if(StringUtils.isNotBlank(qto.getUseProcess())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("useProcess"), qto.getUseProcess()));
        	}
        	if(StringUtils.isNotBlank(qto.getUseCombined())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("useCombined"), qto.getUseCombined()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventReasonsType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReasonsType"), qto.getEventReasonsType()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventReasonsTypeName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReasonsTypeName"), qto.getEventReasonsTypeName()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventReasonsDescribe())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReasonsDescribe"), qto.getEventReasonsDescribe()));
        	}
        	if(StringUtils.isNotBlank(qto.getDisposalSituation())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("disposalSituation"), qto.getDisposalSituation()));
        	}
            return predicate;
        };
    }

    private static CisAdvEventInstrumentRepository dao() {
		return SpringUtil.getBean(CisAdvEventInstrumentRepository.class);
	}

}
