package com.bjgoodwill.hip.ds.cis.adv.report.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.report.entity.CisAdvEventReport;
import com.bjgoodwill.hip.ds.cis.adv.report.service.CisAdvEventReportService;
import com.bjgoodwill.hip.ds.cis.adv.report.service.internal.assembler.CisAdvEventReportAssembler;
import com.bjgoodwill.hip.ds.cis.adv.report.to.CisAdvEventReportEto;
import com.bjgoodwill.hip.ds.cis.adv.report.to.CisAdvEventReportNto;
import com.bjgoodwill.hip.ds.cis.adv.report.to.CisAdvEventReportQto;
import com.bjgoodwill.hip.ds.cis.adv.report.to.CisAdvEventReportTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.report.service.CisAdvEventReportService")
@RequestMapping(value = "/api/cisadv/report/cisAdvEventReport", produces = "application/json; charset=utf-8")
public class CisAdvEventReportServiceImpl implements CisAdvEventReportService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventReportTo> getCisAdvEventReports(CisAdvEventReportQto cisAdvEventReportQto) {
        return CisAdvEventReportAssembler.toTos(CisAdvEventReport.getCisAdvEventReports(cisAdvEventReportQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventReportTo> getCisAdvEventReportPage(CisAdvEventReportQto cisAdvEventReportQto) {
        Page<CisAdvEventReport> page = CisAdvEventReport.getCisAdvEventReportPage(cisAdvEventReportQto);
        Page<CisAdvEventReportTo> result = page.map(CisAdvEventReportAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventReportTo getCisAdvEventReportById(String id) {
        return CisAdvEventReportAssembler.toTo(CisAdvEventReport.getCisAdvEventReportById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventReportTo createCisAdvEventReport(CisAdvEventReportNto cisAdvEventReportNto) {
        CisAdvEventReport cisAdvEventReport = new CisAdvEventReport();
		cisAdvEventReport = cisAdvEventReport.create(cisAdvEventReportNto);
		return CisAdvEventReportAssembler.toTo(cisAdvEventReport);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventReport(String id, CisAdvEventReportEto cisAdvEventReportEto) {
        Optional<CisAdvEventReport> cisAdvEventReportOptional = CisAdvEventReport.getCisAdvEventReportById(id);
		cisAdvEventReportOptional.ifPresent(cisAdvEventReport -> cisAdvEventReport.update(cisAdvEventReportEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventReport(String id) {
        Optional<CisAdvEventReport> cisAdvEventReportOptional = CisAdvEventReport.getCisAdvEventReportById(id);
		cisAdvEventReportOptional.ifPresent(cisAdvEventReport -> cisAdvEventReport.delete());
    }

    @InitBinder
	public void initBinder(WebDataBinder binder) {
	}
}