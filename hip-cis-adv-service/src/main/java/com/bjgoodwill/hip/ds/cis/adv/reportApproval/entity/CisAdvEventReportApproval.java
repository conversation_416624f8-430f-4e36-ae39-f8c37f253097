package com.bjgoodwill.hip.ds.cis.adv.reportApproval.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.AdvEventsStatusEnum;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.repository.CisAdvEventReportApprovalRepository;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.to.CisAdvEventReportApprovalEto;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.to.CisAdvEventReportApprovalNto;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.to.CisAdvEventReportApprovalQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "不良事件报告审批")
@Table(name = "cis_adv_event_report_approval", indexes = {}, uniqueConstraints = {})
public class CisAdvEventReportApproval {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件id")
    @Column(name = "event_report_id", nullable = true, length = 50)
    private String eventReportId;


    @Comment("主管部门")
    @Column(name = "opinion_org_code", nullable = true, length = 16)
    private String opinionOrgCode;

    @Comment("主管部门名称")
    @Column(name = "opinion_org_name", nullable = true, length = 16)
    private String opinionOrgName;


    @Comment("主管部门意见陈述")
    @Column(name = "opinion_state", nullable = true)
    private String opinionState;


    @Comment("主管部门意见陈述人")
    @Column(name = "opinion_state_user", nullable = true, length = 16)
    private String opinionStateUser;

    @Comment("主管部门意见陈述人名称")
    @Column(name = "opinion_state_user_name", nullable = true, length = 16)
    private String opinionStateUserName;


    @Comment("主管部门意见陈述时间")
    @Column(name = "opinion_state_date", nullable = true)
    private LocalDateTime opinionStateDate;


    @Comment("事件总结(转发人填写)")
    @Column(name = "event_conclusion", nullable = true)
    private String eventConclusion;


    @Enumerated(EnumType.STRING)
    @Comment("状态:AdvEventsStatusEnum")
    @Column(name = "status_code", nullable = true, length = 10)
    private AdvEventsStatusEnum statusCode;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    public static Optional<CisAdvEventReportApproval> getCisAdvEventReportApprovalById(String id) {
        return dao().findById(id);
    }

    public static List<CisAdvEventReportApproval> getCisAdvEventReportApprovals(CisAdvEventReportApprovalQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisAdvEventReportApproval> getCisAdvEventReportApprovalPage(CisAdvEventReportApprovalQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisAdvEventReportApproval> getSpecification(CisAdvEventReportApprovalQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getEventReportId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReportId"), qto.getEventReportId()));
            }
            if (StringUtils.isNotBlank(qto.getOpinionOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("opinionOrgCode"), qto.getOpinionOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getOpinionState())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("opinionState"), qto.getOpinionState()));
            }
            if (StringUtils.isNotBlank(qto.getOpinionStateUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("opinionStateUser"), qto.getOpinionStateUser()));
            }
            if (qto.getOpinionStateDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("opinionStateDate"), LocalDateUtil.beginOfDay(qto.getOpinionStateDate()), LocalDateUtil.endOfDay(qto.getOpinionStateDate())));
            }
            if (StringUtils.isNotBlank(qto.getEventConclusion())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventConclusion"), qto.getEventConclusion()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            return predicate;
        };
    }

    private static CisAdvEventReportApprovalRepository dao() {
        return SpringUtil.getBean(CisAdvEventReportApprovalRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getEventReportId() {
        return eventReportId;
    }

    protected void setEventReportId(String eventReportId) {
        this.eventReportId = eventReportId;
    }

    public String getOpinionOrgCode() {
        return opinionOrgCode;
    }

    protected void setOpinionOrgCode(String opinionOrgCode) {
        this.opinionOrgCode = opinionOrgCode;
    }

    public String getOpinionState() {
        return opinionState;
    }

    protected void setOpinionState(String opinionState) {
        this.opinionState = opinionState;
    }

    public String getOpinionStateUser() {
        return opinionStateUser;
    }

    protected void setOpinionStateUser(String opinionStateUser) {
        this.opinionStateUser = opinionStateUser;
    }

    public LocalDateTime getOpinionStateDate() {
        return opinionStateDate;
    }

    protected void setOpinionStateDate(LocalDateTime opinionStateDate) {
        this.opinionStateDate = opinionStateDate;
    }

    public String getEventConclusion() {
        return eventConclusion;
    }

    protected void setEventConclusion(String eventConclusion) {
        this.eventConclusion = eventConclusion;
    }

    public AdvEventsStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(AdvEventsStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public String getOpinionOrgName() {
        return opinionOrgName;
    }

    public void setOpinionOrgName(String opinionOrgName) {
        this.opinionOrgName = opinionOrgName;
    }

    public String getOpinionStateUserName() {
        return opinionStateUserName;
    }

    public void setOpinionStateUserName(String opinionStateUserName) {
        this.opinionStateUserName = opinionStateUserName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisAdvEventReportApproval other = (CisAdvEventReportApproval) obj;
        return Objects.equals(id, other.id);
    }

    public CisAdvEventReportApproval create(CisAdvEventReportApprovalNto cisAdvEventReportApprovalNto) {
        Assert.notNull(cisAdvEventReportApprovalNto, "参数cisAdvEventReportApprovalNto不能为空！");

        setId(cisAdvEventReportApprovalNto.getId());
        setEventReportId(cisAdvEventReportApprovalNto.getEventReportId());
        setOpinionOrgCode(cisAdvEventReportApprovalNto.getOpinionOrgCode());
        setOpinionOrgName(cisAdvEventReportApprovalNto.getOpinionOrgName());
        setOpinionState(cisAdvEventReportApprovalNto.getOpinionState());
        setOpinionStateUser(cisAdvEventReportApprovalNto.getOpinionStateUser());
        setOpinionStateUserName(cisAdvEventReportApprovalNto.getOpinionStateUserName());
        setOpinionStateDate(cisAdvEventReportApprovalNto.getOpinionStateDate());
        setEventConclusion(cisAdvEventReportApprovalNto.getEventConclusion());
        setStatusCode(cisAdvEventReportApprovalNto.getStatusCode());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventReportApprovalEto cisAdvEventReportApprovalEto) {
        setEventReportId(cisAdvEventReportApprovalEto.getEventReportId());
        setOpinionOrgCode(cisAdvEventReportApprovalEto.getOpinionOrgCode());
        setOpinionOrgName(cisAdvEventReportApprovalEto.getOpinionOrgName());
        setOpinionState(cisAdvEventReportApprovalEto.getOpinionState());
        setOpinionStateUser(cisAdvEventReportApprovalEto.getOpinionStateUser());
        setOpinionStateUserName(cisAdvEventReportApprovalEto.getOpinionStateUserName());
        setOpinionStateDate(cisAdvEventReportApprovalEto.getOpinionStateDate());
        setEventConclusion(cisAdvEventReportApprovalEto.getEventConclusion());
        setStatusCode(cisAdvEventReportApprovalEto.getStatusCode());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

}
