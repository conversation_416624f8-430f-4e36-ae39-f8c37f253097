package com.bjgoodwill.hip.ds.cis.adv.fall.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.fall.repository.CisAdvEventFallRepository;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallEto;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallNto;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "跌倒坠床事件上报表")
@Table(name = "cis_adv_event_fall", indexes = {}, uniqueConstraints = {})
public class CisAdvEventFall {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件id")
    @Column(name = "event_report_id", nullable = true, length = 50)
    private String eventReportId;


    @Comment("患者类型")
    @Column(name = "pat_type", nullable = true, length = 16)
    private String patType;


    @Comment("住院号(门诊就诊卡号)")
    @Column(name = "inpatient_code", nullable = true, length = 16)
    private String inpatientCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true, length = 16)
    private String visitCode;


    @Comment("患者姓名")
    @Column(name = "pat_name", nullable = true, length = 64)
    private String patName;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("病区科室")
    @Column(name = "area_code", nullable = true, length = 16)
    private String areaCode;


    @Comment("病区科室名称")
    @Column(name = "area_name", nullable = true, length = 32)
    private String areaName;


    @Comment("入院日期")
    @Column(name = "in_date", nullable = true)
    private LocalDateTime inDate;


    @Comment("事件发生时间")
    @Column(name = "event_date", nullable = true)
    private LocalDateTime eventDate;


    @Comment("事件发生场所：in病区内，out病区外（院内）")
    @Column(name = "event_place", nullable = true, length = 16)
    private String eventPlace;


    @Comment("事件发生场所名称：in病区内，out病区外（院内）")
    @Column(name = "event_place_name", nullable = true, length = 32)
    private String eventPlaceName;


    @Comment("本次跌倒(坠床)次数:1第一次；2第二次；3第三次；4大于3次")
    @Column(name = "this_fall_num", nullable = true)
    private Integer thisFallNum;


    @Comment("跌倒(坠床)前患者活动能力:freemovement活动自如；bedridden卧床不起；walkingstick手杖；wheelchair轮椅；walkeraids助行器；prosthetic假肢")
    @Column(name = "fall_activity_ability", nullable = true, length = 16)
    private String fallActivityAbility;


    @Comment("跌倒(坠床)发生于何项活动过程:a躺卧病床；b上下病床；c坐床旁椅；d如厕；e沐浴时；站立；f行走时；g上下平车；h从事康复活动时")
    @Column(name = "fall_activity", nullable = true, length = 16)
    private String fallActivity;


    @Comment("有无跌倒(坠床)伤害:0 无；1 有；")
    @Column(name = "hurt_flag", nullable = false)
    private boolean hurtFlag;


    @Comment("跌倒(坠床)伤害级别:0跌倒无伤害 (0级)；1轻度伤害(1级)；2中度伤害(2级)；3重度伤害(3级)；4 死亡")
    @Column(name = "hurt_level", nullable = true, length = 16)
    private String hurtLevel;


    @Comment("跌倒(坠床)伤害级别名称:0跌倒无伤害 (0级)；1轻度伤害(1级)；2中度伤害(2级)；3重度伤害(3级)；4 死亡")
    @Column(name = "hurt_level_name", nullable = true, length = 32)
    private String hurtLevelName;


    @Comment("跌倒(坠床)原因类型:oneself患者因素；drugtreatment药物和(或)治疗因素; environment环境因素; other其他")
    @Column(name = "fall_reasons_type", nullable = true, length = 16)
    private String fallReasonsType;


    @Comment("跌倒(坠床)原因类型名称:oneself患者因素；drugtreatment药物和(或)治疗因素; environment环境因素; other其他")
    @Column(name = "fall_reasons_type_name", nullable = true, length = 32)
    private String fallReasonsTypeName;


    @Comment("跌倒(坠床)风险评估工具：morse跌倒（坠床）风险评估量表;约翰霍普金斯跌倒（坠床）风险评估量表;改良版humpty dumpty 儿童跌倒（坠床）风险量表;托马斯跌倒（坠床）风险评估工具;hendrich跌倒（坠床）风险评估表;其他")
    @Column(name = "assessment_instrument", nullable = true, length = 128)
    private String assessmentInstrument;


    @Comment("跌倒(坠床)风险评估工具：morse跌倒（坠床）风险评估量表;约翰霍普金斯跌倒（坠床）风险评估量表;改良版humpty dumpty 儿童跌倒（坠床）风险量表;托马斯跌倒（坠床）风险评估工具;hendrich跌倒（坠床）风险评估表;其他")
    @Column(name = "assessment_instrument_name", nullable = true, length = 128)
    private String assessmentInstrumentName;


    @Comment("跌倒(坠床)前有无跌倒(坠床)风险评估:0 无；1 有；")
    @Column(name = "assessment_flag", nullable = false)
    private boolean assessmentFlag;


    @Comment("跌倒(坠床)前跌倒(坠床)风险评估分数（分）")
    @Column(name = "assessment_score", nullable = true)
    private Integer assessmentScore;


    @Comment("跌倒(坠床)前是否评估为跌倒(坠床)高危人群：0 否；1 是；")
    @Column(name = "fall_high_group", nullable = false)
    private boolean fallHighGroup;


    @Comment("最近一天跌倒(坠床)风险评估距跌倒(坠床)发生时间：a小于24小时；b 1天；c 2天；d 3天；e 4天；f 5天；g 6天；h 1周；i 1周前；uncertain不确定；")
    @Column(name = "last_time", nullable = true, length = 16)
    private String lastTime;


    @Comment("最近一天跌倒(坠床)风险评估距跌倒(坠床)发生时间：a小于24小时；b 1天；c 2天；d 3天；e 4天；f 5天；g 6天；h 1周；i 1周前；uncertain不确定；")
    @Column(name = "last_time_name", nullable = true, length = 32)
    private String lastTimeName;


    @Comment("跌倒(坠床)时有无约束:0 否；1 是；")
    @Column(name = "constraint_flag", nullable = false)
    private boolean constraintFlag;


    @Comment("跌倒(坠床)发生时当班护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    @Column(name = "working_life", nullable = true, length = 16)
    private String workingLife;


    @Comment("跌倒(坠床)发生时当班护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    @Column(name = "working_life_name", nullable = true, length = 32)
    private String workingLifeName;


    @Comment("跌倒(坠床)发生时在岗责任护士人数")
    @Column(name = "duty_num", nullable = true)
    private Integer dutyNum;


    @Comment("跌倒(坠床)发生时病区在院患者数")
    @Column(name = "area_num", nullable = true)
    private Integer areaNum;


    @Comment("跌倒(坠床)发生经过")
    @Column(name = "event_process", nullable = true)
    private String eventProcess;


    @Comment("护士签字")
    @Column(name = "nurs_signature", nullable = true, length = 16)
    private String nursSignature;


    @Comment("护士签字名称")
    @Column(name = "nurs_signature_name", nullable = true, length = 32)
    private String nursSignatureName;


    @Comment("护士填写时间")
    @Column(name = "nurs_signature_date", nullable = true)
    private LocalDateTime nursSignatureDate;


    @Comment("护理措施")
    @Column(name = "nursing_measure", nullable = true)
    private String nursingMeasure;


    @Comment("护士长签字")
    @Column(name = "head_signature", nullable = true, length = 16)
    private String headSignature;


    @Comment("护士长签字名称")
    @Column(name = "head_signature_name", nullable = true, length = 32)
    private String headSignatureName;


    @Comment("护士长签字时间")
    @Column(name = "head_signature_date", nullable = true)
    private LocalDateTime headSignatureDate;


    @Comment("护理部确认及指导意见")
    @Column(name = "nurs_dept_opinion", nullable = true)
    private String nursDeptOpinion;


    @Comment("护理部签字")
    @Column(name = "nurs_dept_signature", nullable = true, length = 16)
    private String nursDeptSignature;


    @Comment("护理部签字名称")
    @Column(name = "nurs_dept_signature_name", nullable = true, length = 32)
    private String nursDeptSignatureName;


    @Comment("护理部签字时间")
    @Column(name = "dept_signature_date", nullable = true)
    private LocalDateTime deptSignatureDate;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    public static Optional<CisAdvEventFall> getCisAdvEventFallById(String id) {
        return dao().findById(id);
    }

    public static List<CisAdvEventFall> getCisAdvEventFalls(CisAdvEventFallQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisAdvEventFall> getCisAdvEventFallPage(CisAdvEventFallQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisAdvEventFall> getSpecification(CisAdvEventFallQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getEventReportId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReportId"), qto.getEventReportId()));
            }
            if (StringUtils.isNotBlank(qto.getPatType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patType"), qto.getPatType()));
            }
            if (StringUtils.isNotBlank(qto.getInpatientCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getPatName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patName"), qto.getPatName()));
            }
            if (StringUtils.isNotBlank(qto.getSex())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
            }
            if (qto.getBirthDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
            }
            if (StringUtils.isNotBlank(qto.getAreaCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaCode"), qto.getAreaCode()));
            }
            if (StringUtils.isNotBlank(qto.getAreaName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaName"), qto.getAreaName()));
            }
            if (qto.getInDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("inDate"), LocalDateUtil.beginOfDay(qto.getInDate()), LocalDateUtil.endOfDay(qto.getInDate())));
            }
            if (qto.getEventDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("eventDate"), LocalDateUtil.beginOfDay(qto.getEventDate()), LocalDateUtil.endOfDay(qto.getEventDate())));
            }
            if (StringUtils.isNotBlank(qto.getEventPlace())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventPlace"), qto.getEventPlace()));
            }
            if (StringUtils.isNotBlank(qto.getEventPlaceName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventPlaceName"), qto.getEventPlaceName()));
            }
            if (qto.getThisFallNum() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("thisFallNum"), qto.getThisFallNum()));
            }
            if (StringUtils.isNotBlank(qto.getFallActivityAbility())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("fallActivityAbility"), qto.getFallActivityAbility()));
            }
            if (StringUtils.isNotBlank(qto.getFallActivity())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("fallActivity"), qto.getFallActivity()));
            }
            if (qto.getHurtFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hurtFlag"), qto.getHurtFlag()));
            }
            if (StringUtils.isNotBlank(qto.getHurtLevel())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hurtLevel"), qto.getHurtLevel()));
            }
            if (StringUtils.isNotBlank(qto.getHurtLevelName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hurtLevelName"), qto.getHurtLevelName()));
            }
            if (StringUtils.isNotBlank(qto.getFallReasonsType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("fallReasonsType"), qto.getFallReasonsType()));
            }
            if (StringUtils.isNotBlank(qto.getFallReasonsTypeName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("fallReasonsTypeName"), qto.getFallReasonsTypeName()));
            }
            if (StringUtils.isNotBlank(qto.getAssessmentInstrument())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("assessmentInstrument"), qto.getAssessmentInstrument()));
            }
            if (StringUtils.isNotBlank(qto.getAssessmentInstrumentName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("assessmentInstrumentName"), qto.getAssessmentInstrumentName()));
            }
            if (qto.getAssessmentFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("assessmentFlag"), qto.getAssessmentFlag()));
            }
            if (qto.getAssessmentScore() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("assessmentScore"), qto.getAssessmentScore()));
            }
            if (qto.getFallHighGroup() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("fallHighGroup"), qto.getFallHighGroup()));
            }
            if (StringUtils.isNotBlank(qto.getLastTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("lastTime"), qto.getLastTime()));
            }
            if (StringUtils.isNotBlank(qto.getLastTimeName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("lastTimeName"), qto.getLastTimeName()));
            }
            if (qto.getConstraintFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("constraintFlag"), qto.getConstraintFlag()));
            }
            if (StringUtils.isNotBlank(qto.getWorkingLife())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("workingLife"), qto.getWorkingLife()));
            }
            if (StringUtils.isNotBlank(qto.getWorkingLifeName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("workingLifeName"), qto.getWorkingLifeName()));
            }
            if (qto.getDutyNum() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("dutyNum"), qto.getDutyNum()));
            }
            if (qto.getAreaNum() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaNum"), qto.getAreaNum()));
            }
            if (StringUtils.isNotBlank(qto.getEventProcess())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess"), qto.getEventProcess()));
            }
            if (StringUtils.isNotBlank(qto.getNursSignature())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursSignature"), qto.getNursSignature()));
            }
            if (StringUtils.isNotBlank(qto.getNursSignatureName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursSignatureName"), qto.getNursSignatureName()));
            }
            if (qto.getNursSignatureDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("nursSignatureDate"), LocalDateUtil.beginOfDay(qto.getNursSignatureDate()), LocalDateUtil.endOfDay(qto.getNursSignatureDate())));
            }
            if (StringUtils.isNotBlank(qto.getNursingMeasure())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursingMeasure"), qto.getNursingMeasure()));
            }
            if (StringUtils.isNotBlank(qto.getHeadSignature())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("headSignature"), qto.getHeadSignature()));
            }
            if (StringUtils.isNotBlank(qto.getHeadSignatureName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("headSignatureName"), qto.getHeadSignatureName()));
            }
            if (qto.getHeadSignatureDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("headSignatureDate"), LocalDateUtil.beginOfDay(qto.getHeadSignatureDate()), LocalDateUtil.endOfDay(qto.getHeadSignatureDate())));
            }
            if (StringUtils.isNotBlank(qto.getNursDeptOpinion())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursDeptOpinion"), qto.getNursDeptOpinion()));
            }
            if (StringUtils.isNotBlank(qto.getNursDeptSignature())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursDeptSignature"), qto.getNursDeptSignature()));
            }
            if (StringUtils.isNotBlank(qto.getNursDeptSignatureName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursDeptSignatureName"), qto.getNursDeptSignatureName()));
            }
            if (qto.getDeptSignatureDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("deptSignatureDate"), LocalDateUtil.beginOfDay(qto.getDeptSignatureDate()), LocalDateUtil.endOfDay(qto.getDeptSignatureDate())));
            }
            return predicate;
        };
    }

    private static CisAdvEventFallRepository dao() {
        return SpringUtil.getBean(CisAdvEventFallRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getEventReportId() {
        return eventReportId;
    }

    protected void setEventReportId(String eventReportId) {
        this.eventReportId = eventReportId;
    }

    public String getPatType() {
        return patType;
    }

    protected void setPatType(String patType) {
        this.patType = patType;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatName() {
        return patName;
    }

    protected void setPatName(String patName) {
        this.patName = patName;
    }

    public String getSex() {
        return sex;
    }

    protected void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getAreaCode() {
        return areaCode;
    }

    protected void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    protected void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public LocalDateTime getInDate() {
        return inDate;
    }

    protected void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    public LocalDateTime getEventDate() {
        return eventDate;
    }

    protected void setEventDate(LocalDateTime eventDate) {
        this.eventDate = eventDate;
    }

    public String getEventPlace() {
        return eventPlace;
    }

    protected void setEventPlace(String eventPlace) {
        this.eventPlace = eventPlace;
    }

    public String getEventPlaceName() {
        return eventPlaceName;
    }

    protected void setEventPlaceName(String eventPlaceName) {
        this.eventPlaceName = eventPlaceName;
    }

    public Integer getThisFallNum() {
        return thisFallNum;
    }

    protected void setThisFallNum(Integer thisFallNum) {
        this.thisFallNum = thisFallNum;
    }

    public String getFallActivityAbility() {
        return fallActivityAbility;
    }

    protected void setFallActivityAbility(String fallActivityAbility) {
        this.fallActivityAbility = fallActivityAbility;
    }

    public String getFallActivity() {
        return fallActivity;
    }

    protected void setFallActivity(String fallActivity) {
        this.fallActivity = fallActivity;
    }

    public boolean isHurtFlag() {
        return hurtFlag;
    }

    protected void setHurtFlag(boolean hurtFlag) {
        this.hurtFlag = hurtFlag;
    }

    public String getHurtLevel() {
        return hurtLevel;
    }

    protected void setHurtLevel(String hurtLevel) {
        this.hurtLevel = hurtLevel;
    }

    public String getHurtLevelName() {
        return hurtLevelName;
    }

    protected void setHurtLevelName(String hurtLevelName) {
        this.hurtLevelName = hurtLevelName;
    }

    public String getFallReasonsType() {
        return fallReasonsType;
    }

    protected void setFallReasonsType(String fallReasonsType) {
        this.fallReasonsType = fallReasonsType;
    }

    public String getFallReasonsTypeName() {
        return fallReasonsTypeName;
    }

    protected void setFallReasonsTypeName(String fallReasonsTypeName) {
        this.fallReasonsTypeName = fallReasonsTypeName;
    }

    public String getAssessmentInstrument() {
        return assessmentInstrument;
    }

    protected void setAssessmentInstrument(String assessmentInstrument) {
        this.assessmentInstrument = assessmentInstrument;
    }

    public String getAssessmentInstrumentName() {
        return assessmentInstrumentName;
    }

    protected void setAssessmentInstrumentName(String assessmentInstrumentName) {
        this.assessmentInstrumentName = assessmentInstrumentName;
    }

    public boolean isAssessmentFlag() {
        return assessmentFlag;
    }

    protected void setAssessmentFlag(boolean assessmentFlag) {
        this.assessmentFlag = assessmentFlag;
    }

    public Integer getAssessmentScore() {
        return assessmentScore;
    }

    protected void setAssessmentScore(Integer assessmentScore) {
        this.assessmentScore = assessmentScore;
    }

    public boolean isFallHighGroup() {
        return fallHighGroup;
    }

    protected void setFallHighGroup(boolean fallHighGroup) {
        this.fallHighGroup = fallHighGroup;
    }

    public String getLastTime() {
        return lastTime;
    }

    protected void setLastTime(String lastTime) {
        this.lastTime = lastTime;
    }

    public String getLastTimeName() {
        return lastTimeName;
    }

    protected void setLastTimeName(String lastTimeName) {
        this.lastTimeName = lastTimeName;
    }

    public boolean isConstraintFlag() {
        return constraintFlag;
    }

    protected void setConstraintFlag(boolean constraintFlag) {
        this.constraintFlag = constraintFlag;
    }

    public String getWorkingLife() {
        return workingLife;
    }

    protected void setWorkingLife(String workingLife) {
        this.workingLife = workingLife;
    }

    public String getWorkingLifeName() {
        return workingLifeName;
    }

    protected void setWorkingLifeName(String workingLifeName) {
        this.workingLifeName = workingLifeName;
    }

    public Integer getDutyNum() {
        return dutyNum;
    }

    protected void setDutyNum(Integer dutyNum) {
        this.dutyNum = dutyNum;
    }

    public Integer getAreaNum() {
        return areaNum;
    }

    protected void setAreaNum(Integer areaNum) {
        this.areaNum = areaNum;
    }

    public String getEventProcess() {
        return eventProcess;
    }

    protected void setEventProcess(String eventProcess) {
        this.eventProcess = eventProcess;
    }

    public String getNursSignature() {
        return nursSignature;
    }

    protected void setNursSignature(String nursSignature) {
        this.nursSignature = nursSignature;
    }

    public String getNursSignatureName() {
        return nursSignatureName;
    }

    protected void setNursSignatureName(String nursSignatureName) {
        this.nursSignatureName = nursSignatureName;
    }

    public LocalDateTime getNursSignatureDate() {
        return nursSignatureDate;
    }

    protected void setNursSignatureDate(LocalDateTime nursSignatureDate) {
        this.nursSignatureDate = nursSignatureDate;
    }

    public String getNursingMeasure() {
        return nursingMeasure;
    }

    protected void setNursingMeasure(String nursingMeasure) {
        this.nursingMeasure = nursingMeasure;
    }

    public String getHeadSignature() {
        return headSignature;
    }

    protected void setHeadSignature(String headSignature) {
        this.headSignature = headSignature;
    }

    public String getHeadSignatureName() {
        return headSignatureName;
    }

    protected void setHeadSignatureName(String headSignatureName) {
        this.headSignatureName = headSignatureName;
    }

    public LocalDateTime getHeadSignatureDate() {
        return headSignatureDate;
    }

    protected void setHeadSignatureDate(LocalDateTime headSignatureDate) {
        this.headSignatureDate = headSignatureDate;
    }

    public String getNursDeptOpinion() {
        return nursDeptOpinion;
    }

    protected void setNursDeptOpinion(String nursDeptOpinion) {
        this.nursDeptOpinion = nursDeptOpinion;
    }

    public String getNursDeptSignature() {
        return nursDeptSignature;
    }

    protected void setNursDeptSignature(String nursDeptSignature) {
        this.nursDeptSignature = nursDeptSignature;
    }

    public String getNursDeptSignatureName() {
        return nursDeptSignatureName;
    }

    protected void setNursDeptSignatureName(String nursDeptSignatureName) {
        this.nursDeptSignatureName = nursDeptSignatureName;
    }

    public LocalDateTime getDeptSignatureDate() {
        return deptSignatureDate;
    }

    protected void setDeptSignatureDate(LocalDateTime deptSignatureDate) {
        this.deptSignatureDate = deptSignatureDate;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisAdvEventFall other = (CisAdvEventFall) obj;
        return Objects.equals(id, other.id);
    }

    public CisAdvEventFall create(CisAdvEventFallNto cisAdvEventFallNto) {
        Assert.notNull(cisAdvEventFallNto, "参数cisAdvEventFallNto不能为空！");

        setId(cisAdvEventFallNto.getId());
        setEventReportId(cisAdvEventFallNto.getEventReportId());
        setPatType(cisAdvEventFallNto.getPatType());
        setInpatientCode(cisAdvEventFallNto.getInpatientCode());
        setVisitCode(cisAdvEventFallNto.getVisitCode());
        setPatName(cisAdvEventFallNto.getPatName());
        setSex(cisAdvEventFallNto.getSex());
        setBirthDate(cisAdvEventFallNto.getBirthDate());
        setAreaCode(cisAdvEventFallNto.getAreaCode());
        setAreaName(cisAdvEventFallNto.getAreaName());
        setInDate(cisAdvEventFallNto.getInDate());
        setEventDate(cisAdvEventFallNto.getEventDate());
        setEventPlace(cisAdvEventFallNto.getEventPlace());
        setEventPlaceName(cisAdvEventFallNto.getEventPlaceName());
        setThisFallNum(cisAdvEventFallNto.getThisFallNum());
        setFallActivityAbility(cisAdvEventFallNto.getFallActivityAbility());
        setFallActivity(cisAdvEventFallNto.getFallActivity());
        setHurtFlag(cisAdvEventFallNto.isHurtFlag());
        setHurtLevel(cisAdvEventFallNto.getHurtLevel());
        setHurtLevelName(cisAdvEventFallNto.getHurtLevelName());
        setFallReasonsType(cisAdvEventFallNto.getFallReasonsType());
        setFallReasonsTypeName(cisAdvEventFallNto.getFallReasonsTypeName());
        setAssessmentInstrument(cisAdvEventFallNto.getAssessmentInstrument());
        setAssessmentInstrumentName(cisAdvEventFallNto.getAssessmentInstrumentName());
        setAssessmentFlag(cisAdvEventFallNto.isAssessmentFlag());
        setAssessmentScore(cisAdvEventFallNto.getAssessmentScore());
        setFallHighGroup(cisAdvEventFallNto.isFallHighGroup());
        setLastTime(cisAdvEventFallNto.getLastTime());
        setLastTimeName(cisAdvEventFallNto.getLastTimeName());
        setConstraintFlag(cisAdvEventFallNto.isConstraintFlag());
        setWorkingLife(cisAdvEventFallNto.getWorkingLife());
        setWorkingLifeName(cisAdvEventFallNto.getWorkingLifeName());
        setDutyNum(cisAdvEventFallNto.getDutyNum());
        setAreaNum(cisAdvEventFallNto.getAreaNum());
        setEventProcess(cisAdvEventFallNto.getEventProcess());
        setNursSignature(cisAdvEventFallNto.getNursSignature());
        setNursSignatureName(cisAdvEventFallNto.getNursSignatureName());
        setNursSignatureDate(cisAdvEventFallNto.getNursSignatureDate());
        setNursingMeasure(cisAdvEventFallNto.getNursingMeasure());
        setHeadSignature(cisAdvEventFallNto.getHeadSignature());
        setHeadSignatureName(cisAdvEventFallNto.getHeadSignatureName());
        setHeadSignatureDate(cisAdvEventFallNto.getHeadSignatureDate());
        setNursDeptOpinion(cisAdvEventFallNto.getNursDeptOpinion());
        setNursDeptSignature(cisAdvEventFallNto.getNursDeptSignature());
        setNursDeptSignatureName(cisAdvEventFallNto.getNursDeptSignatureName());
        setDeptSignatureDate(cisAdvEventFallNto.getDeptSignatureDate());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventFallEto cisAdvEventFallEto) {
        setEventReportId(cisAdvEventFallEto.getEventReportId());
        setPatType(cisAdvEventFallEto.getPatType());
        setInpatientCode(cisAdvEventFallEto.getInpatientCode());
        setVisitCode(cisAdvEventFallEto.getVisitCode());
        setPatName(cisAdvEventFallEto.getPatName());
        setSex(cisAdvEventFallEto.getSex());
        setBirthDate(cisAdvEventFallEto.getBirthDate());
        setAreaCode(cisAdvEventFallEto.getAreaCode());
        setAreaName(cisAdvEventFallEto.getAreaName());
        setInDate(cisAdvEventFallEto.getInDate());
        setEventDate(cisAdvEventFallEto.getEventDate());
        setEventPlace(cisAdvEventFallEto.getEventPlace());
        setEventPlaceName(cisAdvEventFallEto.getEventPlaceName());
        setThisFallNum(cisAdvEventFallEto.getThisFallNum());
        setFallActivityAbility(cisAdvEventFallEto.getFallActivityAbility());
        setFallActivity(cisAdvEventFallEto.getFallActivity());
        setHurtFlag(cisAdvEventFallEto.isHurtFlag());
        setHurtLevel(cisAdvEventFallEto.getHurtLevel());
        setHurtLevelName(cisAdvEventFallEto.getHurtLevelName());
        setFallReasonsType(cisAdvEventFallEto.getFallReasonsType());
        setFallReasonsTypeName(cisAdvEventFallEto.getFallReasonsTypeName());
        setAssessmentInstrument(cisAdvEventFallEto.getAssessmentInstrument());
        setAssessmentInstrumentName(cisAdvEventFallEto.getAssessmentInstrumentName());
        setAssessmentFlag(cisAdvEventFallEto.isAssessmentFlag());
        setAssessmentScore(cisAdvEventFallEto.getAssessmentScore());
        setFallHighGroup(cisAdvEventFallEto.isFallHighGroup());
        setLastTime(cisAdvEventFallEto.getLastTime());
        setLastTimeName(cisAdvEventFallEto.getLastTimeName());
        setConstraintFlag(cisAdvEventFallEto.isConstraintFlag());
        setWorkingLife(cisAdvEventFallEto.getWorkingLife());
        setWorkingLifeName(cisAdvEventFallEto.getWorkingLifeName());
        setDutyNum(cisAdvEventFallEto.getDutyNum());
        setAreaNum(cisAdvEventFallEto.getAreaNum());
        setEventProcess(cisAdvEventFallEto.getEventProcess());
        setNursSignature(cisAdvEventFallEto.getNursSignature());
        setNursSignatureName(cisAdvEventFallEto.getNursSignatureName());
        setNursSignatureDate(cisAdvEventFallEto.getNursSignatureDate());
        setNursingMeasure(cisAdvEventFallEto.getNursingMeasure());
        setHeadSignature(cisAdvEventFallEto.getHeadSignature());
        setHeadSignatureName(cisAdvEventFallEto.getHeadSignatureName());
        setHeadSignatureDate(cisAdvEventFallEto.getHeadSignatureDate());
        setNursDeptOpinion(cisAdvEventFallEto.getNursDeptOpinion());
        setNursDeptSignature(cisAdvEventFallEto.getNursDeptSignature());
        setNursDeptSignatureName(cisAdvEventFallEto.getNursDeptSignatureName());
        setDeptSignatureDate(cisAdvEventFallEto.getDeptSignatureDate());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

}
