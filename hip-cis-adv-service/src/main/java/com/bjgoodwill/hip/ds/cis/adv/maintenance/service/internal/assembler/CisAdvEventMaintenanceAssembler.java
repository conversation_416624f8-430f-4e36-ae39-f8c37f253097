package com.bjgoodwill.hip.ds.cis.adv.maintenance.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.adv.enmus.CisAdvBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.entity.CisAdvEventMaintenance;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.to.CisAdvEventMaintenanceTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventMaintenanceAssembler {

    public static List<CisAdvEventMaintenanceTo> toTos(List<CisAdvEventMaintenance> cisAdvEventMaintenances) {
        return toTos(cisAdvEventMaintenances, false);
    }

    public static List<CisAdvEventMaintenanceTo> toTos(List<CisAdvEventMaintenance> cisAdvEventMaintenances, boolean withAllParts) {
        BusinessAssert.notEmpty(cisAdvEventMaintenances, CisAdvBusinessErrorEnum.BUS_CIS_ADV_0001, "参数cisAdvEventMaintenances");

        List<CisAdvEventMaintenanceTo> tos = new ArrayList<>();
        for (CisAdvEventMaintenance cisAdvEventMaintenance : cisAdvEventMaintenances)
            tos.add(toTo(cisAdvEventMaintenance, withAllParts));
        return tos;
    }

    public static CisAdvEventMaintenanceTo toTo(CisAdvEventMaintenance cisAdvEventMaintenance) {
        return toTo(cisAdvEventMaintenance, false);
    }

    /**
     * @generated
     */
    public static CisAdvEventMaintenanceTo toTo(CisAdvEventMaintenance cisAdvEventMaintenance, boolean withAllParts) {
        if (cisAdvEventMaintenance == null)
            return null;
        CisAdvEventMaintenanceTo to = new CisAdvEventMaintenanceTo();
        to.setId(cisAdvEventMaintenance.getId());
        to.setParentCode(cisAdvEventMaintenance.getParentCode());
        to.setEventCode(cisAdvEventMaintenance.getEventCode());
        to.setEventName(cisAdvEventMaintenance.getEventName());
        to.setEventLevel(cisAdvEventMaintenance.getEventLevel());
        to.setEnabled(cisAdvEventMaintenance.isEnabled());
        to.setCreatedDate(cisAdvEventMaintenance.getCreatedDate());
        to.setCreatedStaff(cisAdvEventMaintenance.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventMaintenance.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventMaintenance.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventMaintenance.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventMaintenance.getUpdatedStaffName());

        if (withAllParts) {
        }
        return to;
    }

}