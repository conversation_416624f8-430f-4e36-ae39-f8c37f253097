package com.bjgoodwill.hip.ds.cis.adv.pressure.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.pressure.repository.CisAdvEventPressureInjuryRepository;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureInjuryEto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureInjuryNto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureInjuryQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "压力性损伤上报表")
@Table(name = "cis_adv_event_pressure_injury", indexes = {}, uniqueConstraints = {})
public class CisAdvEventPressureInjury {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件id")
    @Column(name = "event_report_id", nullable = true, length = 50)
    private String eventReportId;


    @Comment("患者类型")
    @Column(name = "pat_type", nullable = true, length = 2)
    private String patType;


    @Comment("住院号(门诊就诊卡号)")
    @Column(name = "inpatient_code", nullable = true, length = 16)
    private String inpatientCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true, length = 16)
    private String visitCode;


    @Comment("患者姓名")
    @Column(name = "pat_name", nullable = true, length = 64)
    private String patName;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("入院日期")
    @Column(name = "in_date", nullable = true)
    private LocalDateTime inDate;


    @Comment("病区科室")
    @Column(name = "area_code", nullable = true, length = 16)
    private String areaCode;


    @Comment("病区科室名称")
    @Column(name = "area_name", nullable = true, length = 32)
    private String areaName;


    @Comment("诊断")
    @Column(name = "clinical_diagnosis", nullable = true)
    private String clinicalDiagnosis;


    @Comment("事件发生时间")
    @Column(name = "event_date", nullable = true)
    private LocalDateTime eventDate;


    @Comment("风险评估工具: braden评分表，norton评分表，waterlow评分表，braden-q评分表，其他，未评估")
    @Column(name = "assessment_tool", nullable = true)
    private String assessmentTool;


    @Comment("入病区时是否进行压力性损伤风险评估：0 无；1 有；")
    @Column(name = "assessment_flag", nullable = false)
    private boolean assessmentFlag;


    @Comment("入院时压力性损伤风险评估分数")
    @Column(name = "assessment_score", nullable = true)
    private Integer assessmentScore;


    @Comment("入院时压力性损伤风险评估级别:lowrisk低危；middlerisk中危；highrisk高危；extremelrisk极危险；")
    @Column(name = "assessment_level", nullable = true, length = 16)
    private String assessmentLevel;


    @Comment("最近1次压力性损伤风险评估分数")
    @Column(name = "lately_assessment_score", nullable = true)
    private Integer latelyAssessmentScore;


    @Comment("最近1次压力性损伤风险评估距离发现时间:a小于24小时；b 1天；d 2天；e 3天；f 4天；g 5天；h 6天；i 1周；j 1周前；k 未评估；")
    @Column(name = "lately_time", nullable = true, length = 16)
    private String latelyTime;


    @Comment("最近1次压力性损伤评估级别:lowrisk低危；middlerisk中危；highrisk高危；extremelrisk极危险；")
    @Column(name = "lately_assessment_level", nullable = true, length = 16)
    private String latelyAssessmentLevel;


    @Comment("院外带入压力性损伤的部位数量")
    @Column(name = "out_ospital_num", nullable = true)
    private Integer outOspitalNum;


    @Comment("院外带入来源home自家庭入住; beadhouse自养老院入住；otherhospital自其他医院转入；other入住;")
    @Column(name = "out_ospital_source", nullable = true, length = 16)
    private String outOspitalSource;


    @Comment("医院获得性压力性损伤的部位数量")
    @Column(name = "in_hospital_num", nullable = true)
    private Integer inHospitalNum;


    @Comment("other院内其他病区带入压力性损伤的标识；newdiscovery入本病区24小时后新发压力性损伤的标识")
    @Column(name = "in_ospital_source", nullable = true, length = 64)
    private String inOspitalSource;


    @Comment("不可避免压力性损伤的原因：severemalnutrition严重营养不良；severehypoproteinemia严重低蛋白血症；advancedcancer癌症晚期恶液质 ；bloodcirculation血液循环差；needdisease医嘱或病情需要不能变换体位；oneself患者和家属拒绝配合；other其他；")
    @Column(name = "inevitable_pressure_injury", nullable = true, length = 32)
    private String inevitablePressureInjury;


    @Comment("不可避免压力性损伤的原因：severemalnutrition严重营养不良；severehypoproteinemia严重低蛋白血症；advancedcancer癌症晚期恶液质 ；bloodcirculation血液循环差；needdisease医嘱或病情需要不能变换体位；oneself患者和家属拒绝配合；other其他；")
    @Column(name = "inevitable_pressure_injury_name", nullable = true, length = 64)
    private String inevitablePressureInjuryName;


    @Comment("护理措施")
    @Column(name = "improvement_measures", nullable = true)
    private String improvementMeasures;


    @Comment("护士长签字")
    @Column(name = "head_signature", nullable = true, length = 16)
    private String headSignature;


    @Comment("护士长签字名称")
    @Column(name = "head_signature_name", nullable = true, length = 32)
    private String headSignatureName;


    @Comment("护士长填写时间")
    @Column(name = "head_signature_date", nullable = true)
    private LocalDateTime headSignatureDate;


    @Comment("护理部确认及指导意见")
    @Column(name = "nurs_dept_opinion", nullable = true)
    private String nursDeptOpinion;


    @Comment("护理部签字")
    @Column(name = "nurs_dept_signature", nullable = true, length = 16)
    private String nursDeptSignature;


    @Comment("护理部签字名称")
    @Column(name = "nurs_dept_signature_name", nullable = true, length = 32)
    private String nursDeptSignatureName;


    @Comment("护理部签字时间")
    @Column(name = "dept_signature_date", nullable = true)
    private LocalDateTime deptSignatureDate;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    public static Optional<CisAdvEventPressureInjury> getCisAdvEventPressureInjuryById(String id) {
        return dao().findById(id);
    }

    public static List<CisAdvEventPressureInjury> getCisAdvEventPressureInjuries(CisAdvEventPressureInjuryQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisAdvEventPressureInjury> getCisAdvEventPressureInjuryPage(CisAdvEventPressureInjuryQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisAdvEventPressureInjury> getSpecification(CisAdvEventPressureInjuryQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getEventReportId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReportId"), qto.getEventReportId()));
            }
            if (StringUtils.isNotBlank(qto.getPatType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patType"), qto.getPatType()));
            }
            if (StringUtils.isNotBlank(qto.getInpatientCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getPatName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patName"), qto.getPatName()));
            }
            if (StringUtils.isNotBlank(qto.getSex())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
            }
            if (qto.getBirthDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
            }
            if (qto.getInDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("inDate"), LocalDateUtil.beginOfDay(qto.getInDate()), LocalDateUtil.endOfDay(qto.getInDate())));
            }
            if (StringUtils.isNotBlank(qto.getAreaCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaCode"), qto.getAreaCode()));
            }
            if (StringUtils.isNotBlank(qto.getAreaName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaName"), qto.getAreaName()));
            }
            if (StringUtils.isNotBlank(qto.getClinicalDiagnosis())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("clinicalDiagnosis"), qto.getClinicalDiagnosis()));
            }
            if (qto.getEventDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("eventDate"), LocalDateUtil.beginOfDay(qto.getEventDate()), LocalDateUtil.endOfDay(qto.getEventDate())));
            }
            if (StringUtils.isNotBlank(qto.getAssessmentTool())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("assessmentTool"), qto.getAssessmentTool()));
            }
            if (qto.getAssessmentFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("assessmentFlag"), qto.getAssessmentFlag()));
            }
            if (qto.getAssessmentScore() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("assessmentScore"), qto.getAssessmentScore()));
            }
            if (StringUtils.isNotBlank(qto.getAssessmentLevel())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("assessmentLevel"), qto.getAssessmentLevel()));
            }
            if (qto.getLatelyAssessmentScore() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("latelyAssessmentScore"), qto.getLatelyAssessmentScore()));
            }
            if (StringUtils.isNotBlank(qto.getLatelyTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("latelyTime"), qto.getLatelyTime()));
            }
            if (StringUtils.isNotBlank(qto.getLatelyAssessmentLevel())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("latelyAssessmentLevel"), qto.getLatelyAssessmentLevel()));
            }
            if (qto.getOutOspitalNum() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("outOspitalNum"), qto.getOutOspitalNum()));
            }
            if (StringUtils.isNotBlank(qto.getOutOspitalSource())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("outOspitalSource"), qto.getOutOspitalSource()));
            }
            if (qto.getInHospitalNum() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inHospitalNum"), qto.getInHospitalNum()));
            }
            if (StringUtils.isNotBlank(qto.getInOspitalSource())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inOspitalSource"), qto.getInOspitalSource()));
            }
            if (StringUtils.isNotBlank(qto.getInevitablePressureInjury())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inevitablePressureInjury"), qto.getInevitablePressureInjury()));
            }
            if (StringUtils.isNotBlank(qto.getInevitablePressureInjuryName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inevitablePressureInjuryName"), qto.getInevitablePressureInjuryName()));
            }
            if (StringUtils.isNotBlank(qto.getImprovementMeasures())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("improvementMeasures"), qto.getImprovementMeasures()));
            }
            if (StringUtils.isNotBlank(qto.getHeadSignature())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("headSignature"), qto.getHeadSignature()));
            }
            if (StringUtils.isNotBlank(qto.getHeadSignatureName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("headSignatureName"), qto.getHeadSignatureName()));
            }
            if (qto.getHeadSignatureDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("headSignatureDate"), LocalDateUtil.beginOfDay(qto.getHeadSignatureDate()), LocalDateUtil.endOfDay(qto.getHeadSignatureDate())));
            }
            if (StringUtils.isNotBlank(qto.getNursDeptOpinion())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursDeptOpinion"), qto.getNursDeptOpinion()));
            }
            if (StringUtils.isNotBlank(qto.getNursDeptSignature())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursDeptSignature"), qto.getNursDeptSignature()));
            }
            if (StringUtils.isNotBlank(qto.getNursDeptSignatureName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursDeptSignatureName"), qto.getNursDeptSignatureName()));
            }
            if (qto.getDeptSignatureDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("deptSignatureDate"), LocalDateUtil.beginOfDay(qto.getDeptSignatureDate()), LocalDateUtil.endOfDay(qto.getDeptSignatureDate())));
            }
            return predicate;
        };
    }

    private static CisAdvEventPressureInjuryRepository dao() {
        return SpringUtil.getBean(CisAdvEventPressureInjuryRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getEventReportId() {
        return eventReportId;
    }

    protected void setEventReportId(String eventReportId) {
        this.eventReportId = eventReportId;
    }

    public String getPatType() {
        return patType;
    }

    protected void setPatType(String patType) {
        this.patType = patType;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatName() {
        return patName;
    }

    protected void setPatName(String patName) {
        this.patName = patName;
    }

    public String getSex() {
        return sex;
    }

    protected void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public LocalDateTime getInDate() {
        return inDate;
    }

    protected void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    public String getAreaCode() {
        return areaCode;
    }

    protected void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    protected void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getClinicalDiagnosis() {
        return clinicalDiagnosis;
    }

    protected void setClinicalDiagnosis(String clinicalDiagnosis) {
        this.clinicalDiagnosis = clinicalDiagnosis;
    }

    public LocalDateTime getEventDate() {
        return eventDate;
    }

    protected void setEventDate(LocalDateTime eventDate) {
        this.eventDate = eventDate;
    }

    public String getAssessmentTool() {
        return assessmentTool;
    }

    protected void setAssessmentTool(String assessmentTool) {
        this.assessmentTool = assessmentTool;
    }

    public boolean isAssessmentFlag() {
        return assessmentFlag;
    }

    protected void setAssessmentFlag(boolean assessmentFlag) {
        this.assessmentFlag = assessmentFlag;
    }

    public Integer getAssessmentScore() {
        return assessmentScore;
    }

    protected void setAssessmentScore(Integer assessmentScore) {
        this.assessmentScore = assessmentScore;
    }

    public String getAssessmentLevel() {
        return assessmentLevel;
    }

    protected void setAssessmentLevel(String assessmentLevel) {
        this.assessmentLevel = assessmentLevel;
    }

    public Integer getLatelyAssessmentScore() {
        return latelyAssessmentScore;
    }

    protected void setLatelyAssessmentScore(Integer latelyAssessmentScore) {
        this.latelyAssessmentScore = latelyAssessmentScore;
    }

    public String getLatelyTime() {
        return latelyTime;
    }

    protected void setLatelyTime(String latelyTime) {
        this.latelyTime = latelyTime;
    }

    public String getLatelyAssessmentLevel() {
        return latelyAssessmentLevel;
    }

    protected void setLatelyAssessmentLevel(String latelyAssessmentLevel) {
        this.latelyAssessmentLevel = latelyAssessmentLevel;
    }

    public Integer getOutOspitalNum() {
        return outOspitalNum;
    }

    protected void setOutOspitalNum(Integer outOspitalNum) {
        this.outOspitalNum = outOspitalNum;
    }

    public String getOutOspitalSource() {
        return outOspitalSource;
    }

    protected void setOutOspitalSource(String outOspitalSource) {
        this.outOspitalSource = outOspitalSource;
    }

    public Integer getInHospitalNum() {
        return inHospitalNum;
    }

    protected void setInHospitalNum(Integer inHospitalNum) {
        this.inHospitalNum = inHospitalNum;
    }

    public String getInOspitalSource() {
        return inOspitalSource;
    }

    protected void setInOspitalSource(String inOspitalSource) {
        this.inOspitalSource = inOspitalSource;
    }

    public String getInevitablePressureInjury() {
        return inevitablePressureInjury;
    }

    protected void setInevitablePressureInjury(String inevitablePressureInjury) {
        this.inevitablePressureInjury = inevitablePressureInjury;
    }

    public String getInevitablePressureInjuryName() {
        return inevitablePressureInjuryName;
    }

    protected void setInevitablePressureInjuryName(String inevitablePressureInjuryName) {
        this.inevitablePressureInjuryName = inevitablePressureInjuryName;
    }

    public String getImprovementMeasures() {
        return improvementMeasures;
    }

    protected void setImprovementMeasures(String improvementMeasures) {
        this.improvementMeasures = improvementMeasures;
    }

    public String getHeadSignature() {
        return headSignature;
    }

    protected void setHeadSignature(String headSignature) {
        this.headSignature = headSignature;
    }

    public String getHeadSignatureName() {
        return headSignatureName;
    }

    protected void setHeadSignatureName(String headSignatureName) {
        this.headSignatureName = headSignatureName;
    }

    public LocalDateTime getHeadSignatureDate() {
        return headSignatureDate;
    }

    protected void setHeadSignatureDate(LocalDateTime headSignatureDate) {
        this.headSignatureDate = headSignatureDate;
    }

    public String getNursDeptOpinion() {
        return nursDeptOpinion;
    }

    protected void setNursDeptOpinion(String nursDeptOpinion) {
        this.nursDeptOpinion = nursDeptOpinion;
    }

    public String getNursDeptSignature() {
        return nursDeptSignature;
    }

    protected void setNursDeptSignature(String nursDeptSignature) {
        this.nursDeptSignature = nursDeptSignature;
    }

    public String getNursDeptSignatureName() {
        return nursDeptSignatureName;
    }

    protected void setNursDeptSignatureName(String nursDeptSignatureName) {
        this.nursDeptSignatureName = nursDeptSignatureName;
    }

    public LocalDateTime getDeptSignatureDate() {
        return deptSignatureDate;
    }

    protected void setDeptSignatureDate(LocalDateTime deptSignatureDate) {
        this.deptSignatureDate = deptSignatureDate;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisAdvEventPressureInjury other = (CisAdvEventPressureInjury) obj;
        return Objects.equals(id, other.id);
    }

    public CisAdvEventPressureInjury create(CisAdvEventPressureInjuryNto cisAdvEventPressureInjuryNto) {
        Assert.notNull(cisAdvEventPressureInjuryNto, "参数cisAdvEventPressureInjuryNto不能为空！");

        setId(cisAdvEventPressureInjuryNto.getId());
        setEventReportId(cisAdvEventPressureInjuryNto.getEventReportId());
        setPatType(cisAdvEventPressureInjuryNto.getPatType());
        setInpatientCode(cisAdvEventPressureInjuryNto.getInpatientCode());
        setVisitCode(cisAdvEventPressureInjuryNto.getVisitCode());
        setPatName(cisAdvEventPressureInjuryNto.getPatName());
        setSex(cisAdvEventPressureInjuryNto.getSex());
        setBirthDate(cisAdvEventPressureInjuryNto.getBirthDate());
        setInDate(cisAdvEventPressureInjuryNto.getInDate());
        setAreaCode(cisAdvEventPressureInjuryNto.getAreaCode());
        setAreaName(cisAdvEventPressureInjuryNto.getAreaName());
        setClinicalDiagnosis(cisAdvEventPressureInjuryNto.getClinicalDiagnosis());
        setEventDate(cisAdvEventPressureInjuryNto.getEventDate());
        setAssessmentTool(cisAdvEventPressureInjuryNto.getAssessmentTool());
        setAssessmentFlag(cisAdvEventPressureInjuryNto.isAssessmentFlag());
        setAssessmentScore(cisAdvEventPressureInjuryNto.getAssessmentScore());
        setAssessmentLevel(cisAdvEventPressureInjuryNto.getAssessmentLevel());
        setLatelyAssessmentScore(cisAdvEventPressureInjuryNto.getLatelyAssessmentScore());
        setLatelyTime(cisAdvEventPressureInjuryNto.getLatelyTime());
        setLatelyAssessmentLevel(cisAdvEventPressureInjuryNto.getLatelyAssessmentLevel());
        setOutOspitalNum(cisAdvEventPressureInjuryNto.getOutOspitalNum());
        setOutOspitalSource(cisAdvEventPressureInjuryNto.getOutOspitalSource());
        setInHospitalNum(cisAdvEventPressureInjuryNto.getInHospitalNum());
        setInOspitalSource(cisAdvEventPressureInjuryNto.getInOspitalSource());
        setInevitablePressureInjury(cisAdvEventPressureInjuryNto.getInevitablePressureInjury());
        setInevitablePressureInjuryName(cisAdvEventPressureInjuryNto.getInevitablePressureInjuryName());
        setImprovementMeasures(cisAdvEventPressureInjuryNto.getImprovementMeasures());
        setHeadSignature(cisAdvEventPressureInjuryNto.getHeadSignature());
        setHeadSignatureName(cisAdvEventPressureInjuryNto.getHeadSignatureName());
        setHeadSignatureDate(cisAdvEventPressureInjuryNto.getHeadSignatureDate());
        setNursDeptOpinion(cisAdvEventPressureInjuryNto.getNursDeptOpinion());
        setNursDeptSignature(cisAdvEventPressureInjuryNto.getNursDeptSignature());
        setNursDeptSignatureName(cisAdvEventPressureInjuryNto.getNursDeptSignatureName());
        setDeptSignatureDate(cisAdvEventPressureInjuryNto.getDeptSignatureDate());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventPressureInjuryEto cisAdvEventPressureInjuryEto) {
        setEventReportId(cisAdvEventPressureInjuryEto.getEventReportId());
        setPatType(cisAdvEventPressureInjuryEto.getPatType());
        setInpatientCode(cisAdvEventPressureInjuryEto.getInpatientCode());
        setVisitCode(cisAdvEventPressureInjuryEto.getVisitCode());
        setPatName(cisAdvEventPressureInjuryEto.getPatName());
        setSex(cisAdvEventPressureInjuryEto.getSex());
        setBirthDate(cisAdvEventPressureInjuryEto.getBirthDate());
        setInDate(cisAdvEventPressureInjuryEto.getInDate());
        setAreaCode(cisAdvEventPressureInjuryEto.getAreaCode());
        setAreaName(cisAdvEventPressureInjuryEto.getAreaName());
        setClinicalDiagnosis(cisAdvEventPressureInjuryEto.getClinicalDiagnosis());
        setEventDate(cisAdvEventPressureInjuryEto.getEventDate());
        setAssessmentTool(cisAdvEventPressureInjuryEto.getAssessmentTool());
        setAssessmentFlag(cisAdvEventPressureInjuryEto.isAssessmentFlag());
        setAssessmentScore(cisAdvEventPressureInjuryEto.getAssessmentScore());
        setAssessmentLevel(cisAdvEventPressureInjuryEto.getAssessmentLevel());
        setLatelyAssessmentScore(cisAdvEventPressureInjuryEto.getLatelyAssessmentScore());
        setLatelyTime(cisAdvEventPressureInjuryEto.getLatelyTime());
        setLatelyAssessmentLevel(cisAdvEventPressureInjuryEto.getLatelyAssessmentLevel());
        setOutOspitalNum(cisAdvEventPressureInjuryEto.getOutOspitalNum());
        setOutOspitalSource(cisAdvEventPressureInjuryEto.getOutOspitalSource());
        setInHospitalNum(cisAdvEventPressureInjuryEto.getInHospitalNum());
        setInOspitalSource(cisAdvEventPressureInjuryEto.getInOspitalSource());
        setInevitablePressureInjury(cisAdvEventPressureInjuryEto.getInevitablePressureInjury());
        setInevitablePressureInjuryName(cisAdvEventPressureInjuryEto.getInevitablePressureInjuryName());
        setImprovementMeasures(cisAdvEventPressureInjuryEto.getImprovementMeasures());
        setHeadSignature(cisAdvEventPressureInjuryEto.getHeadSignature());
        setHeadSignatureName(cisAdvEventPressureInjuryEto.getHeadSignatureName());
        setHeadSignatureDate(cisAdvEventPressureInjuryEto.getHeadSignatureDate());
        setNursDeptOpinion(cisAdvEventPressureInjuryEto.getNursDeptOpinion());
        setNursDeptSignature(cisAdvEventPressureInjuryEto.getNursDeptSignature());
        setNursDeptSignatureName(cisAdvEventPressureInjuryEto.getNursDeptSignatureName());
        setDeptSignatureDate(cisAdvEventPressureInjuryEto.getDeptSignatureDate());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

}
