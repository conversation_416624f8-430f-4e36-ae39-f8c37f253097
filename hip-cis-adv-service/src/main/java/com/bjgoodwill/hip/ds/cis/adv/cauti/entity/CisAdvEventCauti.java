package com.bjgoodwill.hip.ds.cis.adv.cauti.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.cauti.repository.CisAdvEventCautiRepository;
import com.bjgoodwill.hip.ds.cis.adv.cauti.to.CisAdvEventCautiEto;
import com.bjgoodwill.hip.ds.cis.adv.cauti.to.CisAdvEventCautiNto;
import com.bjgoodwill.hip.ds.cis.adv.cauti.to.CisAdvEventCautiQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "导尿管相关尿路感染（CAUTI）相关信息收集表")
@Table(name = "cis_adv_event_cauti", indexes = {}, uniqueConstraints = {})
public class CisAdvEventCauti {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件id")
    @Column(name = "event_report_id", nullable = true, length = 50)
    private String eventReportId;


    @Comment("患者类型")
    @Column(name = "pat_type", nullable = true, length = 2)
    private String patType;


    @Comment("住院号(门诊就诊卡号)")
    @Column(name = "inpatient_code", nullable = true, length = 16)
    private String inpatientCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true, length = 16)
    private String visitCode;


    @Comment("患者姓名")
    @Column(name = "pat_name", nullable = true, length = 64)
    private String patName;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("年龄范围: 新生儿、1-6月、7-12月、1-6岁、7-12岁、13-18岁、19-64岁、65岁及以上、无法确定")
    @Column(name = "age_range", nullable = true, length = 64)
    private String ageRange;


    @Comment("病区科室")
    @Column(name = "area_code", nullable = true, length = 16)
    private String areaCode;


    @Comment("病区名称")
    @Column(name = "area_name", nullable = true, length = 64)
    private String areaName;


    @Comment("入院时间")
    @Column(name = "in_date", nullable = true)
    private LocalDateTime inDate;


    @Comment("留置导尿管的主要原因： 昏迷或精神异常无法自行排尿unabletourinate；尿潴留urinaryretention；尿失禁urinaryincontinence；监测尿量monitorurinevolume；近期有手术operation；骶尾部或会阴部有开放性伤口openwounds；other其他")
    @Column(name = "indwell_reason", nullable = true)
    private String indwellReason;


    @Comment("留置导尿管的主要原因名称： 昏迷或精神异常无法自行排尿unabletourinate；尿潴留urinaryretention；尿失禁urinaryincontinence；监测尿量monitorurinevolume；近期有手术operation；骶尾部或会阴部有开放性伤口openwounds；other其他")
    @Column(name = "indwell_reason_name", nullable = true)
    private String indwellReasonName;


    @Comment("导尿管型号：6f  ；8f  ；10f  ；12f  ；14f  ；16f  ；18f  ；20f ；22f  ；24f")
    @Column(name = "captheter_model", nullable = true, length = 16)
    private String captheterModel;


    @Comment("导尿管型号名称：6f  ；8f  ；10f  ；12f  ；14f  ；16f  ；18f  ；20f ；22f  ；24f")
    @Column(name = "captheter_model_name", nullable = true, length = 32)
    private String captheterModelName;


    @Comment("导尿管类型：普通导尿管general；双腔气囊导尿管doublelumenballoon；三腔气囊导尿管threechamberballoon")
    @Column(name = "captheter_type", nullable = true, length = 64)
    private String captheterType;


    @Comment("导尿管类型名称：普通导尿管general；双腔气囊导尿管doublelumenballoon；三腔气囊导尿管threechamberballoon")
    @Column(name = "captheter_type_name", nullable = true, length = 128)
    private String captheterTypeName;


    @Comment("导管材质：乳胶latex ；硅胶silicagel；其他other")
    @Column(name = "extubation_quality", nullable = true, length = 64)
    private String extubationQuality;


    @Comment("导管材质名称：乳胶latex ；硅胶silicagel；其他other")
    @Column(name = "extubation_quality_name", nullable = true, length = 64)
    private String extubationQualityName;


    @Comment("是否使用抗返流集尿装置： 1是 ；0否")
    @Column(name = "use_device_flag", nullable = false)
    private boolean useDeviceFlag;


    @Comment("发生cauti前是否有膀胱冲洗：1是；0否")
    @Column(name = "bladder_irrigation_flag", nullable = false)
    private boolean bladderIrrigationFlag;


    @Comment("发生cauti时导尿管留置时长：天")
    @Column(name = "cauti_time", nullable = true)
    private Integer cautiTime;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    public String getId() {
    	return id;
    }

    protected void setId(String id) {
    	this.id = id;
    }

    public String getEventReportId() {
    	return eventReportId;
    }

    protected void setEventReportId(String eventReportId) {
    	this.eventReportId = eventReportId;
    }

    public String getPatType() {
    	return patType;
    }

    protected void setPatType(String patType) {
    	this.patType = patType;
    }

    public String getInpatientCode() {
    	return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
    	this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    protected void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public String getPatName() {
    	return patName;
    }

    protected void setPatName(String patName) {
    	this.patName = patName;
    }

    public String getSex() {
    	return sex;
    }

    protected void setSex(String sex) {
    	this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
    	this.birthDate = birthDate;
    }

    public String getAgeRange() {
    	return ageRange;
    }

    protected void setAgeRange(String ageRange) {
    	this.ageRange = ageRange;
    }

    public String getAreaCode() {
    	return areaCode;
    }

    protected void setAreaCode(String areaCode) {
    	this.areaCode = areaCode;
    }

    public String getAreaName() {
    	return areaName;
    }

    protected void setAreaName(String areaName) {
    	this.areaName = areaName;
    }

    public LocalDateTime getInDate() {
    	return inDate;
    }

    protected void setInDate(LocalDateTime inDate) {
    	this.inDate = inDate;
    }

    public String getIndwellReason() {
    	return indwellReason;
    }

    protected void setIndwellReason(String indwellReason) {
    	this.indwellReason = indwellReason;
    }

    public String getIndwellReasonName() {
    	return indwellReasonName;
    }

    protected void setIndwellReasonName(String indwellReasonName) {
    	this.indwellReasonName = indwellReasonName;
    }

    public String getCaptheterModel() {
    	return captheterModel;
    }

    protected void setCaptheterModel(String captheterModel) {
    	this.captheterModel = captheterModel;
    }

    public String getCaptheterModelName() {
    	return captheterModelName;
    }

    protected void setCaptheterModelName(String captheterModelName) {
    	this.captheterModelName = captheterModelName;
    }

    public String getCaptheterType() {
    	return captheterType;
    }

    protected void setCaptheterType(String captheterType) {
    	this.captheterType = captheterType;
    }

    public String getCaptheterTypeName() {
    	return captheterTypeName;
    }

    protected void setCaptheterTypeName(String captheterTypeName) {
    	this.captheterTypeName = captheterTypeName;
    }

    public String getExtubationQuality() {
    	return extubationQuality;
    }

    protected void setExtubationQuality(String extubationQuality) {
    	this.extubationQuality = extubationQuality;
    }

    public String getExtubationQualityName() {
    	return extubationQualityName;
    }

    protected void setExtubationQualityName(String extubationQualityName) {
    	this.extubationQualityName = extubationQualityName;
    }

    public boolean isUseDeviceFlag() {
    	return useDeviceFlag;
    }

    protected void setUseDeviceFlag(boolean useDeviceFlag) {
    	this.useDeviceFlag = useDeviceFlag;
    }

    public boolean isBladderIrrigationFlag() {
    	return bladderIrrigationFlag;
    }

    protected void setBladderIrrigationFlag(boolean bladderIrrigationFlag) {
    	this.bladderIrrigationFlag = bladderIrrigationFlag;
    }

    public Integer getCautiTime() {
    	return cautiTime;
    }

    protected void setCautiTime(Integer cautiTime) {
    	this.cautiTime = cautiTime;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventCauti other = (CisAdvEventCauti) obj;
		return Objects.equals(id, other.id);
	}

    public CisAdvEventCauti create(CisAdvEventCautiNto cisAdvEventCautiNto) {
        Assert.notNull(cisAdvEventCautiNto, "参数cisAdvEventCautiNto不能为空！");

        setId(cisAdvEventCautiNto.getId());
        setEventReportId(cisAdvEventCautiNto.getEventReportId());
        setPatType(cisAdvEventCautiNto.getPatType());
        setInpatientCode(cisAdvEventCautiNto.getInpatientCode());
        setVisitCode(cisAdvEventCautiNto.getVisitCode());
        setPatName(cisAdvEventCautiNto.getPatName());
        setSex(cisAdvEventCautiNto.getSex());
        setBirthDate(cisAdvEventCautiNto.getBirthDate());
        setAgeRange(cisAdvEventCautiNto.getAgeRange());
        setAreaCode(cisAdvEventCautiNto.getAreaCode());
        setAreaName(cisAdvEventCautiNto.getAreaName());
        setInDate(cisAdvEventCautiNto.getInDate());
        setIndwellReason(cisAdvEventCautiNto.getIndwellReason());
        setIndwellReasonName(cisAdvEventCautiNto.getIndwellReasonName());
        setCaptheterModel(cisAdvEventCautiNto.getCaptheterModel());
        setCaptheterModelName(cisAdvEventCautiNto.getCaptheterModelName());
        setCaptheterType(cisAdvEventCautiNto.getCaptheterType());
        setCaptheterTypeName(cisAdvEventCautiNto.getCaptheterTypeName());
        setExtubationQuality(cisAdvEventCautiNto.getExtubationQuality());
        setExtubationQualityName(cisAdvEventCautiNto.getExtubationQualityName());
        setUseDeviceFlag(cisAdvEventCautiNto.isUseDeviceFlag());
        setBladderIrrigationFlag(cisAdvEventCautiNto.isBladderIrrigationFlag());
        setCautiTime(cisAdvEventCautiNto.getCautiTime());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventCautiEto cisAdvEventCautiEto) {
        setEventReportId(cisAdvEventCautiEto.getEventReportId());
        setPatType(cisAdvEventCautiEto.getPatType());
        setInpatientCode(cisAdvEventCautiEto.getInpatientCode());
        setVisitCode(cisAdvEventCautiEto.getVisitCode());
        setPatName(cisAdvEventCautiEto.getPatName());
        setSex(cisAdvEventCautiEto.getSex());
        setBirthDate(cisAdvEventCautiEto.getBirthDate());
        setAgeRange(cisAdvEventCautiEto.getAgeRange());
        setAreaCode(cisAdvEventCautiEto.getAreaCode());
        setAreaName(cisAdvEventCautiEto.getAreaName());
        setInDate(cisAdvEventCautiEto.getInDate());
        setIndwellReason(cisAdvEventCautiEto.getIndwellReason());
        setIndwellReasonName(cisAdvEventCautiEto.getIndwellReasonName());
        setCaptheterModel(cisAdvEventCautiEto.getCaptheterModel());
        setCaptheterModelName(cisAdvEventCautiEto.getCaptheterModelName());
        setCaptheterType(cisAdvEventCautiEto.getCaptheterType());
        setCaptheterTypeName(cisAdvEventCautiEto.getCaptheterTypeName());
        setExtubationQuality(cisAdvEventCautiEto.getExtubationQuality());
        setExtubationQualityName(cisAdvEventCautiEto.getExtubationQualityName());
        setUseDeviceFlag(cisAdvEventCautiEto.isUseDeviceFlag());
        setBladderIrrigationFlag(cisAdvEventCautiEto.isBladderIrrigationFlag());
        setCautiTime(cisAdvEventCautiEto.getCautiTime());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

    public static Optional<CisAdvEventCauti> getCisAdvEventCautiById(String id) {
		return dao().findById(id);
	}

	public static List<CisAdvEventCauti> getCisAdvEventCautis(CisAdvEventCautiQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
	}

	public static Page<CisAdvEventCauti> getCisAdvEventCautiPage(CisAdvEventCautiQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
	}


	/**
	 * @generated
	 */
    private static Specification<CisAdvEventCauti> getSpecification(CisAdvEventCautiQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
        	if(StringUtils.isNotBlank(qto.getEventReportId())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReportId"), qto.getEventReportId()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patType"), qto.getPatType()));
        	}
        	if(StringUtils.isNotBlank(qto.getInpatientCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getVisitCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patName"), qto.getPatName()));
        	}
        	if(StringUtils.isNotBlank(qto.getSex())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
        	}
    		if(qto.getBirthDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getAgeRange())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("ageRange"), qto.getAgeRange()));
        	}
        	if(StringUtils.isNotBlank(qto.getAreaCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaCode"), qto.getAreaCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getAreaName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaName"), qto.getAreaName()));
        	}
    		if(qto.getInDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("inDate"), LocalDateUtil.beginOfDay(qto.getInDate()), LocalDateUtil.endOfDay(qto.getInDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getIndwellReason())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("indwellReason"), qto.getIndwellReason()));
        	}
        	if(StringUtils.isNotBlank(qto.getIndwellReasonName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("indwellReasonName"), qto.getIndwellReasonName()));
        	}
        	if(StringUtils.isNotBlank(qto.getCaptheterModel())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("captheterModel"), qto.getCaptheterModel()));
        	}
        	if(StringUtils.isNotBlank(qto.getCaptheterModelName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("captheterModelName"), qto.getCaptheterModelName()));
        	}
        	if(StringUtils.isNotBlank(qto.getCaptheterType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("captheterType"), qto.getCaptheterType()));
        	}
        	if(StringUtils.isNotBlank(qto.getCaptheterTypeName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("captheterTypeName"), qto.getCaptheterTypeName()));
        	}
        	if(StringUtils.isNotBlank(qto.getExtubationQuality())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("extubationQuality"), qto.getExtubationQuality()));
        	}
        	if(StringUtils.isNotBlank(qto.getExtubationQualityName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("extubationQualityName"), qto.getExtubationQualityName()));
        	}
    		if(qto.getUseDeviceFlag() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("useDeviceFlag"), qto.getUseDeviceFlag()));
        	}
    		if(qto.getBladderIrrigationFlag() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("bladderIrrigationFlag"), qto.getBladderIrrigationFlag()));
        	}
    		if(qto.getCautiTime() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cautiTime"), qto.getCautiTime()));
        	}
            return predicate;
        };
    }

    private static CisAdvEventCautiRepository dao() {
		return SpringUtil.getBean(CisAdvEventCautiRepository.class);
	}

}
