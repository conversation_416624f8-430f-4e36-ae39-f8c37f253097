package com.bjgoodwill.hip.ds.cis.adv.cauti.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.cauti.entity.CisAdvEventCauti;
import com.bjgoodwill.hip.ds.cis.adv.cauti.to.CisAdvEventCautiTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventCautiAssembler {

    public static List<CisAdvEventCautiTo> toTos(List<CisAdvEventCauti> cisAdvEventCautis) {
        return toTos(cisAdvEventCautis, false);
    }

    public static List<CisAdvEventCautiTo> toTos(List<CisAdvEventCauti> cisAdvEventCautis, boolean withAllParts) {
        Assert.notNull(cisAdvEventCautis, "参数cisAdvEventCautis不能为空！");

        List<CisAdvEventCautiTo> tos = new ArrayList<>();
        for (CisAdvEventCauti cisAdvEventCauti : cisAdvEventCautis)
            tos.add(toTo(cisAdvEventCauti, withAllParts));
        return tos;
    }

    public static CisAdvEventCautiTo toTo(CisAdvEventCauti cisAdvEventCauti) {
        return toTo(cisAdvEventCauti, false);
    }

    /**
     * @generated
     */
    public static CisAdvEventCautiTo toTo(CisAdvEventCauti cisAdvEventCauti, boolean withAllParts) {
        if (cisAdvEventCauti == null)
            return null;
        CisAdvEventCautiTo to = new CisAdvEventCautiTo();
        to.setId(cisAdvEventCauti.getId());
        to.setEventReportId(cisAdvEventCauti.getEventReportId());
        to.setPatType(cisAdvEventCauti.getPatType());
        to.setInpatientCode(cisAdvEventCauti.getInpatientCode());
        to.setVisitCode(cisAdvEventCauti.getVisitCode());
        to.setPatName(cisAdvEventCauti.getPatName());
        to.setSex(cisAdvEventCauti.getSex());
        to.setBirthDate(cisAdvEventCauti.getBirthDate());
        to.setAgeRange(cisAdvEventCauti.getAgeRange());
        to.setAreaCode(cisAdvEventCauti.getAreaCode());
        to.setAreaName(cisAdvEventCauti.getAreaName());
        to.setInDate(cisAdvEventCauti.getInDate());
        to.setIndwellReason(cisAdvEventCauti.getIndwellReason());
        to.setIndwellReasonName(cisAdvEventCauti.getIndwellReasonName());
        to.setCaptheterModel(cisAdvEventCauti.getCaptheterModel());
        to.setCaptheterModelName(cisAdvEventCauti.getCaptheterModelName());
        to.setCaptheterType(cisAdvEventCauti.getCaptheterType());
        to.setCaptheterTypeName(cisAdvEventCauti.getCaptheterTypeName());
        to.setExtubationQuality(cisAdvEventCauti.getExtubationQuality());
        to.setExtubationQualityName(cisAdvEventCauti.getExtubationQualityName());
        to.setUseDeviceFlag(cisAdvEventCauti.isUseDeviceFlag());
        to.setBladderIrrigationFlag(cisAdvEventCauti.isBladderIrrigationFlag());
        to.setCautiTime(cisAdvEventCauti.getCautiTime());
        to.setCreatedDate(cisAdvEventCauti.getCreatedDate());
        to.setCreatedStaff(cisAdvEventCauti.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventCauti.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventCauti.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventCauti.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventCauti.getUpdatedStaffName());

        if (withAllParts) {
        }
        return to;
    }

}