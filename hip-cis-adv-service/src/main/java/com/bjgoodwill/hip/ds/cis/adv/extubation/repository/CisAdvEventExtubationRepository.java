package com.bjgoodwill.hip.ds.cis.adv.extubation.repository;

import com.bjgoodwill.hip.ds.cis.adv.extubation.entity.CisAdvEventExtubation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.adv.extubation.repository.CisAdvEventExtubationRepository")
public interface CisAdvEventExtubationRepository extends JpaRepository<CisAdvEventExtubation, String>, JpaSpecificationExecutor<CisAdvEventExtubation> {

}