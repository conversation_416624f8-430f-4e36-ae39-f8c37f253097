package com.bjgoodwill.hip.ds.cis.adv.attached.entity;


import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.attached.repository.CisAdvEventAttachedRepository;
import com.bjgoodwill.hip.ds.cis.adv.attached.to.CisAdvEventAttachedEto;
import com.bjgoodwill.hip.ds.cis.adv.attached.to.CisAdvEventAttachedNto;
import com.bjgoodwill.hip.ds.cis.adv.attached.to.CisAdvEventAttachedQto;
import com.bjgoodwill.hip.ds.cis.adv.enmus.CisAdvBusinessErrorEnum;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "不良事件与附卡对照")
@Table(name = "cis_adv_event_attached", indexes = {}, uniqueConstraints = {})
public class CisAdvEventAttached {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件类别")
    @Column(name = "event_code", nullable = true, length = 32)
    private String eventCode;


    @Comment("附卡")
    @Column(name = "attached_card", nullable = true, length = 32)
    private String attachedCard;


    @Comment("医院编码")
    @Column(name = "hospital_code", nullable = true, length = 32)
    private String hospitalCode;


    @Comment("医院名称")
    @Column(name = "hospital_name", nullable = true, length = 32)
    private String hospitalName;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    public static Optional<CisAdvEventAttached> getCisAdvEventAttachedById(String id) {
        return dao().findById(id);
    }

    public static List<CisAdvEventAttached> getCisAdvEventAttacheds(CisAdvEventAttachedQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisAdvEventAttached> getCisAdvEventAttachedPage(CisAdvEventAttachedQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisAdvEventAttached> getSpecification(CisAdvEventAttachedQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getEventCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventCode"), qto.getEventCode()));
            }
            if (StringUtils.isNotBlank(qto.getAttachedCard())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("attachedCard"), qto.getAttachedCard()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            return predicate;
        };
    }

    private static CisAdvEventAttachedRepository dao() {
        return SpringUtil.getBean(CisAdvEventAttachedRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getEventCode() {
        return eventCode;
    }

    protected void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getAttachedCard() {
        return attachedCard;
    }

    protected void setAttachedCard(String attachedCard) {
        this.attachedCard = attachedCard;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    protected void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisAdvEventAttached other = (CisAdvEventAttached) obj;
        return Objects.equals(id, other.id);
    }

    public CisAdvEventAttached create(CisAdvEventAttachedNto cisAdvEventAttachedNto) {
        BusinessAssert.notNull(cisAdvEventAttachedNto, CisAdvBusinessErrorEnum.BUS_CIS_ADV_0001, "参数cisAdvEventAttachedNto");

        setId(cisAdvEventAttachedNto.getId());
        setEventCode(cisAdvEventAttachedNto.getEventCode());
        setAttachedCard(cisAdvEventAttachedNto.getAttachedCard());
        setHospitalCode(cisAdvEventAttachedNto.getHospitalCode());
        setHospitalName(cisAdvEventAttachedNto.getHospitalName());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventAttachedEto cisAdvEventAttachedEto) {
        setEventCode(cisAdvEventAttachedEto.getEventCode());
        setAttachedCard(cisAdvEventAttachedEto.getAttachedCard());
        setHospitalCode(cisAdvEventAttachedEto.getHospitalCode());
        setHospitalName(cisAdvEventAttachedEto.getHospitalName());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

}
