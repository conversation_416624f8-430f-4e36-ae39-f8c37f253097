package com.bjgoodwill.hip.ds.cis.adv.pressure.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.pressure.entity.CisAdvEventPressureReg;
import com.bjgoodwill.hip.ds.cis.adv.pressure.service.CisAdvEventPressureRegService;
import com.bjgoodwill.hip.ds.cis.adv.pressure.service.internal.assembler.CisAdvEventPressureRegAssembler;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegEto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegNto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegQto;
import com.bjgoodwill.hip.ds.cis.adv.pressure.to.CisAdvEventPressureRegTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.pressure.service.CisAdvEventPressureRegService")
@RequestMapping(value = "/api/cisadv/pressure/cisAdvEventPressureReg", produces = "application/json; charset=utf-8")
public class CisAdvEventPressureRegServiceImpl implements CisAdvEventPressureRegService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventPressureRegTo> getCisAdvEventPressureRegs(CisAdvEventPressureRegQto cisAdvEventPressureRegQto) {
        return CisAdvEventPressureRegAssembler.toTos(CisAdvEventPressureReg.getCisAdvEventPressureRegs(cisAdvEventPressureRegQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventPressureRegTo> getCisAdvEventPressureRegPage(CisAdvEventPressureRegQto cisAdvEventPressureRegQto) {
        Page<CisAdvEventPressureReg> page = CisAdvEventPressureReg.getCisAdvEventPressureRegPage(cisAdvEventPressureRegQto);
        Page<CisAdvEventPressureRegTo> result = page.map(CisAdvEventPressureRegAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventPressureRegTo getCisAdvEventPressureRegById(String id) {
        return CisAdvEventPressureRegAssembler.toTo(CisAdvEventPressureReg.getCisAdvEventPressureRegById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventPressureRegTo createCisAdvEventPressureReg(CisAdvEventPressureRegNto cisAdvEventPressureRegNto) {
        CisAdvEventPressureReg cisAdvEventPressureReg = new CisAdvEventPressureReg();
        cisAdvEventPressureReg = cisAdvEventPressureReg.create(cisAdvEventPressureRegNto);
        return CisAdvEventPressureRegAssembler.toTo(cisAdvEventPressureReg);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventPressureReg(String id, CisAdvEventPressureRegEto cisAdvEventPressureRegEto) {
        Optional<CisAdvEventPressureReg> cisAdvEventPressureRegOptional = CisAdvEventPressureReg.getCisAdvEventPressureRegById(id);
        cisAdvEventPressureRegOptional.ifPresent(cisAdvEventPressureReg -> cisAdvEventPressureReg.update(cisAdvEventPressureRegEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventPressureReg(String id) {
        Optional<CisAdvEventPressureReg> cisAdvEventPressureRegOptional = CisAdvEventPressureReg.getCisAdvEventPressureRegById(id);
        cisAdvEventPressureRegOptional.ifPresent(cisAdvEventPressureReg -> cisAdvEventPressureReg.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}