package com.bjgoodwill.hip.ds.cis.adv.vap.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.vap.entity.CisAdvEventVap;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventVapAssembler {

    public static List<CisAdvEventVapTo> toTos(List<CisAdvEventVap> cisAdvEventVaps) {
		return toTos(cisAdvEventVaps, false);
	}

	public static List<CisAdvEventVapTo> toTos(List<CisAdvEventVap> cisAdvEventVaps, boolean withAllParts) {
		Assert.notNull(cisAdvEventVaps, "参数cisAdvEventVaps不能为空！");

		List<CisAdvEventVapTo> tos = new ArrayList<>();
		for (CisAdvEventVap cisAdvEventVap : cisAdvEventVaps)
			tos.add(toTo(cisAdvEventVap, withAllParts));
		return tos;
	}

	public static CisAdvEventVapTo toTo(CisAdvEventVap cisAdvEventVap) {
		return toTo(cisAdvEventVap, false);
	}

	/**
	 * @generated
	 */
	public static CisAdvEventVapTo toTo(CisAdvEventVap cisAdvEventVap, boolean withAllParts) {
		if (cisAdvEventVap == null)
			return null;
		CisAdvEventVapTo to = new CisAdvEventVapTo();
        to.setId(cisAdvEventVap.getId());
        to.setEventReportId(cisAdvEventVap.getEventReportId());
        to.setPatType(cisAdvEventVap.getPatType());
        to.setInpatientCode(cisAdvEventVap.getInpatientCode());
        to.setVisitCode(cisAdvEventVap.getVisitCode());
        to.setPatName(cisAdvEventVap.getPatName());
        to.setSex(cisAdvEventVap.getSex());
        to.setBirthDate(cisAdvEventVap.getBirthDate());
        to.setAgeRange(cisAdvEventVap.getAgeRange());
        to.setAreaCode(cisAdvEventVap.getAreaCode());
        to.setAreaName(cisAdvEventVap.getAreaName());
        to.setInDate(cisAdvEventVap.getInDate());
        to.setArtificialType(cisAdvEventVap.getArtificialType());
        to.setArtificialTypeName(cisAdvEventVap.getArtificialTypeName());
        to.setExtubationType(cisAdvEventVap.getExtubationType());
        to.setExtubationTypeName(cisAdvEventVap.getExtubationTypeName());
        to.setHumidifyDevice(cisAdvEventVap.getHumidifyDevice());
        to.setHumidifyDeviceName(cisAdvEventVap.getHumidifyDeviceName());
        to.setSputumType(cisAdvEventVap.getSputumType());
        to.setSputumTypeName(cisAdvEventVap.getSputumTypeName());
        to.setOralCareType(cisAdvEventVap.getOralCareType());
        to.setOralCareTypeName(cisAdvEventVap.getOralCareTypeName());
        to.setOralCareTimes(cisAdvEventVap.getOralCareTimes());
        to.setOralNursing(cisAdvEventVap.getOralNursing());
        to.setOralNursingName(cisAdvEventVap.getOralNursingName());
        to.setOtherNursing(cisAdvEventVap.getOtherNursing());
        to.setNasogasticFlag(cisAdvEventVap.isNasogasticFlag());
        to.setVapTime(cisAdvEventVap.getVapTime());
        to.setCreatedDate(cisAdvEventVap.getCreatedDate());
        to.setCreatedStaff(cisAdvEventVap.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventVap.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventVap.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventVap.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventVap.getUpdatedStaffName());

		if (withAllParts) {
		}
		return to;
	}

}