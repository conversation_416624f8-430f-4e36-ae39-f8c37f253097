package com.bjgoodwill.hip.ds.cis.adv.bloodinfection.repository;

import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.entity.CisAdvEventPiccBloodInfection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.adv.bloodinfection.repository.CisAdvEventPiccBloodInfectionRepository")
public interface CisAdvEventPiccBloodInfectionRepository extends JpaRepository<CisAdvEventPiccBloodInfection, String>, JpaSpecificationExecutor<CisAdvEventPiccBloodInfection> {

}