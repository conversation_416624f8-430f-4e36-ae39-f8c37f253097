package com.bjgoodwill.hip.ds.cis.adv.bloodinfection.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.repository.CisAdvEventPiccBloodInfectionRepository;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionEto;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionNto;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventPiccBloodInfectionQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "PICC相关血流感染相关信息收集表")
@Table(name = "cis_adv_event_picc_blood_infection", indexes = {}, uniqueConstraints = {})
public class CisAdvEventPiccBloodInfection {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件id")
    @Column(name = "event_report_id", nullable = true, length = 50)
    private String eventReportId;


    @Comment("患者类型")
    @Column(name = "pat_type", nullable = true, length = 2)
    private String patType;


    @Comment("住院号(门诊就诊卡号)")
    @Column(name = "inpatient_code", nullable = true, length = 16)
    private String inpatientCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true, length = 16)
    private String visitCode;


    @Comment("患者姓名")
    @Column(name = "pat_name", nullable = true, length = 64)
    private String patName;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("年龄范围: 新生儿、1-6月、7-12月、1-6岁、7-12岁、13-18岁、19-64岁、65岁及以上、无法确定")
    @Column(name = "age_range", nullable = true, length = 64)
    private String ageRange;


    @Comment("病区科室")
    @Column(name = "area_code", nullable = true, length = 16)
    private String areaCode;


    @Comment("病区名称")
    @Column(name = "area_name", nullable = true, length = 64)
    private String areaName;


    @Comment("入院时间")
    @Column(name = "in_date", nullable = true)
    private LocalDateTime inDate;


    @Comment("留置导管的主要原因：输入高渗液体hypertonicfluid，输入化疗药物chemotherapydrugs，长期输液longterminfusion，抢救和监测需要rescueandmnitorin，其他other")
    @Column(name = "indwell_reason", nullable = true)
    private String indwellReason;


    @Comment("留置导管的主要原因名称：输入高渗液体hypertonicfluid，输入化疗药物chemotherapydrugs，长期输液longterminfusion，抢救和监测需要rescueandmnitorin，其他other")
    @Column(name = "indwell_reason_name", nullable = true)
    private String indwellReasonName;


    @Comment("picc 置管位置：贵要静脉basilic、头静脉cephalic、肱静脉brachial、肘正中静脉mediancubital、大隐静脉greatsaphenous、颞浅静脉superficialtemporal、耳后静脉posteriorauricular、股静脉femoral、其他other")
    @Column(name = "picc_location", nullable = true)
    private String piccLocation;


    @Comment("picc 置管位置：贵要静脉basilic、头静脉cephalic、肱静脉brachial、肘正中静脉mediancubital、大隐静脉greatsaphenous、颞浅静脉superficialtemporal、耳后静脉posteriorauricular、股静脉femoral、其他other")
    @Column(name = "picc_location_name", nullable = true)
    private String piccLocationName;


    @Comment("picc置管方式：超声引导ultrasoundguided、盲穿blindpuncture")
    @Column(name = "picc_type", nullable = true)
    private String piccType;


    @Comment("picc置管方式名称：超声引导ultrasoundguided、盲穿blindpuncture")
    @Column(name = "picc_type_name", nullable = true)
    private String piccTypeName;


    @Comment("导管类型：单腔导管endotrachealintubation、双腔导管doublelumen、三腔导管threelumen")
    @Column(name = "extubation_type", nullable = true)
    private String extubationType;


    @Comment("导管类型名称：单腔导管endotrachealintubation、双腔导管doublelumen、三腔导管threelumen")
    @Column(name = "extubation_type_name", nullable = true)
    private String extubationTypeName;


    @Comment("是否为抗菌导管：1是，0否")
    @Column(name = "antibacterial_flag", nullable = false)
    private boolean antibacterialFlag;


    @Comment("发生clabsi时picc留置时长：天")
    @Column(name = "picc_time", nullable = true)
    private Integer piccTime;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    public static Optional<CisAdvEventPiccBloodInfection> getCisAdvEventPiccBloodInfectionById(String id) {
        return dao().findById(id);
    }

    public static List<CisAdvEventPiccBloodInfection> getCisAdvEventPiccBloodInfections(CisAdvEventPiccBloodInfectionQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisAdvEventPiccBloodInfection> getCisAdvEventPiccBloodInfectionPage(CisAdvEventPiccBloodInfectionQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisAdvEventPiccBloodInfection> getSpecification(CisAdvEventPiccBloodInfectionQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getEventReportId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReportId"), qto.getEventReportId()));
            }
            if (StringUtils.isNotBlank(qto.getPatType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patType"), qto.getPatType()));
            }
            if (StringUtils.isNotBlank(qto.getInpatientCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getPatName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patName"), qto.getPatName()));
            }
            if (StringUtils.isNotBlank(qto.getSex())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
            }
            if (qto.getBirthDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
            }
            if (StringUtils.isNotBlank(qto.getAgeRange())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("ageRange"), qto.getAgeRange()));
            }
            if (StringUtils.isNotBlank(qto.getAreaCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaCode"), qto.getAreaCode()));
            }
            if (StringUtils.isNotBlank(qto.getAreaName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaName"), qto.getAreaName()));
            }
            if (qto.getInDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("inDate"), LocalDateUtil.beginOfDay(qto.getInDate()), LocalDateUtil.endOfDay(qto.getInDate())));
            }
            if (StringUtils.isNotBlank(qto.getIndwellReason())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("indwellReason"), qto.getIndwellReason()));
            }
            if (StringUtils.isNotBlank(qto.getIndwellReasonName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("indwellReasonName"), qto.getIndwellReasonName()));
            }
            if (StringUtils.isNotBlank(qto.getPiccLocation())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("piccLocation"), qto.getPiccLocation()));
            }
            if (StringUtils.isNotBlank(qto.getPiccLocationName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("piccLocationName"), qto.getPiccLocationName()));
            }
            if (StringUtils.isNotBlank(qto.getPiccType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("piccType"), qto.getPiccType()));
            }
            if (StringUtils.isNotBlank(qto.getPiccTypeName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("piccTypeName"), qto.getPiccTypeName()));
            }
            if (StringUtils.isNotBlank(qto.getExtubationType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("extubationType"), qto.getExtubationType()));
            }
            if (StringUtils.isNotBlank(qto.getExtubationTypeName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("extubationTypeName"), qto.getExtubationTypeName()));
            }
            if (qto.getAntibacterialFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("antibacterialFlag"), qto.getAntibacterialFlag()));
            }
            if (qto.getPiccTime() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("piccTime"), qto.getPiccTime()));
            }
            return predicate;
        };
    }

    private static CisAdvEventPiccBloodInfectionRepository dao() {
        return SpringUtil.getBean(CisAdvEventPiccBloodInfectionRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getEventReportId() {
        return eventReportId;
    }

    protected void setEventReportId(String eventReportId) {
        this.eventReportId = eventReportId;
    }

    public String getPatType() {
        return patType;
    }

    protected void setPatType(String patType) {
        this.patType = patType;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatName() {
        return patName;
    }

    protected void setPatName(String patName) {
        this.patName = patName;
    }

    public String getSex() {
        return sex;
    }

    protected void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getAgeRange() {
        return ageRange;
    }

    protected void setAgeRange(String ageRange) {
        this.ageRange = ageRange;
    }

    public String getAreaCode() {
        return areaCode;
    }

    protected void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    protected void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public LocalDateTime getInDate() {
        return inDate;
    }

    protected void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    public String getIndwellReason() {
        return indwellReason;
    }

    protected void setIndwellReason(String indwellReason) {
        this.indwellReason = indwellReason;
    }

    public String getIndwellReasonName() {
        return indwellReasonName;
    }

    protected void setIndwellReasonName(String indwellReasonName) {
        this.indwellReasonName = indwellReasonName;
    }

    public String getPiccLocation() {
        return piccLocation;
    }

    protected void setPiccLocation(String piccLocation) {
        this.piccLocation = piccLocation;
    }

    public String getPiccLocationName() {
        return piccLocationName;
    }

    protected void setPiccLocationName(String piccLocationName) {
        this.piccLocationName = piccLocationName;
    }

    public String getPiccType() {
        return piccType;
    }

    protected void setPiccType(String piccType) {
        this.piccType = piccType;
    }

    public String getPiccTypeName() {
        return piccTypeName;
    }

    protected void setPiccTypeName(String piccTypeName) {
        this.piccTypeName = piccTypeName;
    }

    public String getExtubationType() {
        return extubationType;
    }

    protected void setExtubationType(String extubationType) {
        this.extubationType = extubationType;
    }

    public String getExtubationTypeName() {
        return extubationTypeName;
    }

    protected void setExtubationTypeName(String extubationTypeName) {
        this.extubationTypeName = extubationTypeName;
    }

    public boolean isAntibacterialFlag() {
        return antibacterialFlag;
    }

    protected void setAntibacterialFlag(boolean antibacterialFlag) {
        this.antibacterialFlag = antibacterialFlag;
    }

    public Integer getPiccTime() {
        return piccTime;
    }

    protected void setPiccTime(Integer piccTime) {
        this.piccTime = piccTime;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisAdvEventPiccBloodInfection other = (CisAdvEventPiccBloodInfection) obj;
        return Objects.equals(id, other.id);
    }

    public CisAdvEventPiccBloodInfection create(CisAdvEventPiccBloodInfectionNto cisAdvEventPiccBloodInfectionNto) {
        Assert.notNull(cisAdvEventPiccBloodInfectionNto, "参数cisAdvEventPiccBloodInfectionNto不能为空！");

        setId(cisAdvEventPiccBloodInfectionNto.getId());
        setEventReportId(cisAdvEventPiccBloodInfectionNto.getEventReportId());
        setPatType(cisAdvEventPiccBloodInfectionNto.getPatType());
        setInpatientCode(cisAdvEventPiccBloodInfectionNto.getInpatientCode());
        setVisitCode(cisAdvEventPiccBloodInfectionNto.getVisitCode());
        setPatName(cisAdvEventPiccBloodInfectionNto.getPatName());
        setSex(cisAdvEventPiccBloodInfectionNto.getSex());
        setBirthDate(cisAdvEventPiccBloodInfectionNto.getBirthDate());
        setAgeRange(cisAdvEventPiccBloodInfectionNto.getAgeRange());
        setAreaCode(cisAdvEventPiccBloodInfectionNto.getAreaCode());
        setAreaName(cisAdvEventPiccBloodInfectionNto.getAreaName());
        setInDate(cisAdvEventPiccBloodInfectionNto.getInDate());
        setIndwellReason(cisAdvEventPiccBloodInfectionNto.getIndwellReason());
        setIndwellReasonName(cisAdvEventPiccBloodInfectionNto.getIndwellReasonName());
        setPiccLocation(cisAdvEventPiccBloodInfectionNto.getPiccLocation());
        setPiccLocationName(cisAdvEventPiccBloodInfectionNto.getPiccLocationName());
        setPiccType(cisAdvEventPiccBloodInfectionNto.getPiccType());
        setPiccTypeName(cisAdvEventPiccBloodInfectionNto.getPiccTypeName());
        setExtubationType(cisAdvEventPiccBloodInfectionNto.getExtubationType());
        setExtubationTypeName(cisAdvEventPiccBloodInfectionNto.getExtubationTypeName());
        setAntibacterialFlag(cisAdvEventPiccBloodInfectionNto.isAntibacterialFlag());
        setPiccTime(cisAdvEventPiccBloodInfectionNto.getPiccTime());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventPiccBloodInfectionEto cisAdvEventPiccBloodInfectionEto) {
        setEventReportId(cisAdvEventPiccBloodInfectionEto.getEventReportId());
        setPatType(cisAdvEventPiccBloodInfectionEto.getPatType());
        setInpatientCode(cisAdvEventPiccBloodInfectionEto.getInpatientCode());
        setVisitCode(cisAdvEventPiccBloodInfectionEto.getVisitCode());
        setPatName(cisAdvEventPiccBloodInfectionEto.getPatName());
        setSex(cisAdvEventPiccBloodInfectionEto.getSex());
        setBirthDate(cisAdvEventPiccBloodInfectionEto.getBirthDate());
        setAgeRange(cisAdvEventPiccBloodInfectionEto.getAgeRange());
        setAreaCode(cisAdvEventPiccBloodInfectionEto.getAreaCode());
        setAreaName(cisAdvEventPiccBloodInfectionEto.getAreaName());
        setInDate(cisAdvEventPiccBloodInfectionEto.getInDate());
        setIndwellReason(cisAdvEventPiccBloodInfectionEto.getIndwellReason());
        setIndwellReasonName(cisAdvEventPiccBloodInfectionEto.getIndwellReasonName());
        setPiccLocation(cisAdvEventPiccBloodInfectionEto.getPiccLocation());
        setPiccLocationName(cisAdvEventPiccBloodInfectionEto.getPiccLocationName());
        setPiccType(cisAdvEventPiccBloodInfectionEto.getPiccType());
        setPiccTypeName(cisAdvEventPiccBloodInfectionEto.getPiccTypeName());
        setExtubationType(cisAdvEventPiccBloodInfectionEto.getExtubationType());
        setExtubationTypeName(cisAdvEventPiccBloodInfectionEto.getExtubationTypeName());
        setAntibacterialFlag(cisAdvEventPiccBloodInfectionEto.isAntibacterialFlag());
        setPiccTime(cisAdvEventPiccBloodInfectionEto.getPiccTime());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

}
