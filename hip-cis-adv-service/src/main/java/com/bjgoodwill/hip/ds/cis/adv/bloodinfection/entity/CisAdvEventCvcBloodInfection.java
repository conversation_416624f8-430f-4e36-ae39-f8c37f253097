package com.bjgoodwill.hip.ds.cis.adv.bloodinfection.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.repository.CisAdvEventCvcBloodInfectionRepository;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventCvcBloodInfectionEto;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventCvcBloodInfectionNto;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventCvcBloodInfectionQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "CVC相关血流感染相关信息收集表")
@Table(name = "cis_adv_event_cvc_blood_infection", indexes = {}, uniqueConstraints = {})
public class CisAdvEventCvcBloodInfection {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件id")
    @Column(name = "event_report_id", nullable = true, length = 50)
    private String eventReportId;


    @Comment("患者类型")
    @Column(name = "pat_type", nullable = true, length = 2)
    private String patType;


    @Comment("住院号(门诊就诊卡号)")
    @Column(name = "inpatient_code", nullable = true, length = 16)
    private String inpatientCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true, length = 16)
    private String visitCode;


    @Comment("患者姓名")
    @Column(name = "pat_name", nullable = true, length = 64)
    private String patName;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("年龄范围: 新生儿、1-6月、7-12月、1-6岁、7-12岁、13-18岁、19-64岁、65岁及以上、无法确定")
    @Column(name = "age_range", nullable = true, length = 64)
    private String ageRange;


    @Comment("病区科室")
    @Column(name = "area_code", nullable = true, length = 16)
    private String areaCode;


    @Comment("病区科室名称")
    @Column(name = "area_name", nullable = true, length = 64)
    private String areaName;


    @Comment("入院时间")
    @Column(name = "in_date", nullable = true)
    private LocalDateTime inDate;


    @Comment("留置导管的主要原因：输入高渗液体hypertonicfluid，输入化疗药物chemotherapydrugs，长期输液longterminfusion，抢救和监测需要rescueandmnitorin，其他other")
    @Column(name = "indwell_reason", nullable = true)
    private String indwellReason;


    @Comment("留置导管的主要原因：输入高渗液体hypertonicfluid，输入化疗药物chemotherapydrugs，长期输液longterminfusion，抢救和监测需要rescueandmnitorin，其他other")
    @Column(name = "indwell_reason_name", nullable = true)
    private String indwellReasonName;


    @Comment("cvc置管位置：锁骨下静脉subclavian，颈内静脉internaljugular，股静脉femoral")
    @Column(name = "cvc_location", nullable = true, length = 64)
    private String cvcLocation;


    @Comment("cvc置管位置：锁骨下静脉subclavian，颈内静脉internaljugular，股静脉femoral")
    @Column(name = "cvc_location_name", nullable = true)
    private String cvcLocationName;


    @Comment("导管类型：单腔导管endotrachealintubation；双腔导管doublelumen；三腔导管threelumen")
    @Column(name = "extubation_type", nullable = true)
    private String extubationType;


    @Comment("导管类型名称：单腔导管endotrachealintubation；双腔导管doublelumen；三腔导管threelumen")
    @Column(name = "extubation_type_name", nullable = true)
    private String extubationTypeName;


    @Comment("是否为抗菌导管：1是，0否")
    @Column(name = "antibacterial_flag", nullable = false)
    private boolean antibacterialFlag;


    @Comment("发生clabsi时cvc留置时长：天")
    @Column(name = "cvc_time", nullable = true)
    private Integer cvcTime;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    public static Optional<CisAdvEventCvcBloodInfection> getCisAdvEventCvcBloodInfectionById(String id) {
        return dao().findById(id);
    }

    public static List<CisAdvEventCvcBloodInfection> getCisAdvEventCvcBloodInfections(CisAdvEventCvcBloodInfectionQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisAdvEventCvcBloodInfection> getCisAdvEventCvcBloodInfectionPage(CisAdvEventCvcBloodInfectionQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisAdvEventCvcBloodInfection> getSpecification(CisAdvEventCvcBloodInfectionQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getEventReportId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReportId"), qto.getEventReportId()));
            }
            if (StringUtils.isNotBlank(qto.getPatType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patType"), qto.getPatType()));
            }
            if (StringUtils.isNotBlank(qto.getInpatientCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getPatName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patName"), qto.getPatName()));
            }
            if (StringUtils.isNotBlank(qto.getSex())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
            }
            if (qto.getBirthDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
            }
            if (StringUtils.isNotBlank(qto.getAgeRange())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("ageRange"), qto.getAgeRange()));
            }
            if (StringUtils.isNotBlank(qto.getAreaCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaCode"), qto.getAreaCode()));
            }
            if (StringUtils.isNotBlank(qto.getAreaName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaName"), qto.getAreaName()));
            }
            if (qto.getInDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("inDate"), LocalDateUtil.beginOfDay(qto.getInDate()), LocalDateUtil.endOfDay(qto.getInDate())));
            }
            if (StringUtils.isNotBlank(qto.getIndwellReason())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("indwellReason"), qto.getIndwellReason()));
            }
            if (StringUtils.isNotBlank(qto.getIndwellReasonName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("indwellReasonName"), qto.getIndwellReasonName()));
            }
            if (StringUtils.isNotBlank(qto.getCvcLocation())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cvcLocation"), qto.getCvcLocation()));
            }
            if (StringUtils.isNotBlank(qto.getCvcLocationName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cvcLocationName"), qto.getCvcLocationName()));
            }
            if (StringUtils.isNotBlank(qto.getExtubationType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("extubationType"), qto.getExtubationType()));
            }
            if (StringUtils.isNotBlank(qto.getExtubationTypeName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("extubationTypeName"), qto.getExtubationTypeName()));
            }
            if (qto.getAntibacterialFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("antibacterialFlag"), qto.getAntibacterialFlag()));
            }
            if (qto.getCvcTime() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cvcTime"), qto.getCvcTime()));
            }
            return predicate;
        };
    }

    private static CisAdvEventCvcBloodInfectionRepository dao() {
        return SpringUtil.getBean(CisAdvEventCvcBloodInfectionRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getEventReportId() {
        return eventReportId;
    }

    protected void setEventReportId(String eventReportId) {
        this.eventReportId = eventReportId;
    }

    public String getPatType() {
        return patType;
    }

    protected void setPatType(String patType) {
        this.patType = patType;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatName() {
        return patName;
    }

    protected void setPatName(String patName) {
        this.patName = patName;
    }

    public String getSex() {
        return sex;
    }

    protected void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getAgeRange() {
        return ageRange;
    }

    protected void setAgeRange(String ageRange) {
        this.ageRange = ageRange;
    }

    public String getAreaCode() {
        return areaCode;
    }

    protected void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    protected void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public LocalDateTime getInDate() {
        return inDate;
    }

    protected void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    public String getIndwellReason() {
        return indwellReason;
    }

    protected void setIndwellReason(String indwellReason) {
        this.indwellReason = indwellReason;
    }

    public String getIndwellReasonName() {
        return indwellReasonName;
    }

    protected void setIndwellReasonName(String indwellReasonName) {
        this.indwellReasonName = indwellReasonName;
    }

    public String getCvcLocation() {
        return cvcLocation;
    }

    protected void setCvcLocation(String cvcLocation) {
        this.cvcLocation = cvcLocation;
    }

    public String getCvcLocationName() {
        return cvcLocationName;
    }

    protected void setCvcLocationName(String cvcLocationName) {
        this.cvcLocationName = cvcLocationName;
    }

    public String getExtubationType() {
        return extubationType;
    }

    protected void setExtubationType(String extubationType) {
        this.extubationType = extubationType;
    }

    public String getExtubationTypeName() {
        return extubationTypeName;
    }

    protected void setExtubationTypeName(String extubationTypeName) {
        this.extubationTypeName = extubationTypeName;
    }

    public boolean isAntibacterialFlag() {
        return antibacterialFlag;
    }

    protected void setAntibacterialFlag(boolean antibacterialFlag) {
        this.antibacterialFlag = antibacterialFlag;
    }

    public Integer getCvcTime() {
        return cvcTime;
    }

    protected void setCvcTime(Integer cvcTime) {
        this.cvcTime = cvcTime;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisAdvEventCvcBloodInfection other = (CisAdvEventCvcBloodInfection) obj;
        return Objects.equals(id, other.id);
    }

    public CisAdvEventCvcBloodInfection create(CisAdvEventCvcBloodInfectionNto cisAdvEventCvcBloodInfectionNto) {
        Assert.notNull(cisAdvEventCvcBloodInfectionNto, "参数cisAdvEventCvcBloodInfectionNto不能为空！");

        setId(cisAdvEventCvcBloodInfectionNto.getId());
        setEventReportId(cisAdvEventCvcBloodInfectionNto.getEventReportId());
        setPatType(cisAdvEventCvcBloodInfectionNto.getPatType());
        setInpatientCode(cisAdvEventCvcBloodInfectionNto.getInpatientCode());
        setVisitCode(cisAdvEventCvcBloodInfectionNto.getVisitCode());
        setPatName(cisAdvEventCvcBloodInfectionNto.getPatName());
        setSex(cisAdvEventCvcBloodInfectionNto.getSex());
        setBirthDate(cisAdvEventCvcBloodInfectionNto.getBirthDate());
        setAgeRange(cisAdvEventCvcBloodInfectionNto.getAgeRange());
        setAreaCode(cisAdvEventCvcBloodInfectionNto.getAreaCode());
        setAreaName(cisAdvEventCvcBloodInfectionNto.getAreaName());
        setInDate(cisAdvEventCvcBloodInfectionNto.getInDate());
        setIndwellReason(cisAdvEventCvcBloodInfectionNto.getIndwellReason());
        setIndwellReasonName(cisAdvEventCvcBloodInfectionNto.getIndwellReasonName());
        setCvcLocation(cisAdvEventCvcBloodInfectionNto.getCvcLocation());
        setCvcLocationName(cisAdvEventCvcBloodInfectionNto.getCvcLocationName());
        setExtubationType(cisAdvEventCvcBloodInfectionNto.getExtubationType());
        setExtubationTypeName(cisAdvEventCvcBloodInfectionNto.getExtubationTypeName());
        setAntibacterialFlag(cisAdvEventCvcBloodInfectionNto.isAntibacterialFlag());
        setCvcTime(cisAdvEventCvcBloodInfectionNto.getCvcTime());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventCvcBloodInfectionEto cisAdvEventCvcBloodInfectionEto) {
        setEventReportId(cisAdvEventCvcBloodInfectionEto.getEventReportId());
        setPatType(cisAdvEventCvcBloodInfectionEto.getPatType());
        setInpatientCode(cisAdvEventCvcBloodInfectionEto.getInpatientCode());
        setVisitCode(cisAdvEventCvcBloodInfectionEto.getVisitCode());
        setPatName(cisAdvEventCvcBloodInfectionEto.getPatName());
        setSex(cisAdvEventCvcBloodInfectionEto.getSex());
        setBirthDate(cisAdvEventCvcBloodInfectionEto.getBirthDate());
        setAgeRange(cisAdvEventCvcBloodInfectionEto.getAgeRange());
        setAreaCode(cisAdvEventCvcBloodInfectionEto.getAreaCode());
        setAreaName(cisAdvEventCvcBloodInfectionEto.getAreaName());
        setInDate(cisAdvEventCvcBloodInfectionEto.getInDate());
        setIndwellReason(cisAdvEventCvcBloodInfectionEto.getIndwellReason());
        setIndwellReasonName(cisAdvEventCvcBloodInfectionEto.getIndwellReasonName());
        setCvcLocation(cisAdvEventCvcBloodInfectionEto.getCvcLocation());
        setCvcLocationName(cisAdvEventCvcBloodInfectionEto.getCvcLocationName());
        setExtubationType(cisAdvEventCvcBloodInfectionEto.getExtubationType());
        setExtubationTypeName(cisAdvEventCvcBloodInfectionEto.getExtubationTypeName());
        setAntibacterialFlag(cisAdvEventCvcBloodInfectionEto.isAntibacterialFlag());
        setCvcTime(cisAdvEventCvcBloodInfectionEto.getCvcTime());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

}
