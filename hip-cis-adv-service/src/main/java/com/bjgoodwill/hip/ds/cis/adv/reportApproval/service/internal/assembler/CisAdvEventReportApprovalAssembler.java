package com.bjgoodwill.hip.ds.cis.adv.reportApproval.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.reportApproval.entity.CisAdvEventReportApproval;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.to.CisAdvEventReportApprovalTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventReportApprovalAssembler {

    public static List<CisAdvEventReportApprovalTo> toTos(List<CisAdvEventReportApproval> cisAdvEventReportApprovals) {
        return toTos(cisAdvEventReportApprovals, false);
    }

    public static List<CisAdvEventReportApprovalTo> toTos(List<CisAdvEventReportApproval> cisAdvEventReportApprovals, boolean withAllParts) {
        Assert.notNull(cisAdvEventReportApprovals, "参数cisAdvEventReportApprovals不能为空！");

        List<CisAdvEventReportApprovalTo> tos = new ArrayList<>();
        for (CisAdvEventReportApproval cisAdvEventReportApproval : cisAdvEventReportApprovals)
            tos.add(toTo(cisAdvEventReportApproval, withAllParts));
        return tos;
    }

    public static CisAdvEventReportApprovalTo toTo(CisAdvEventReportApproval cisAdvEventReportApproval) {
        return toTo(cisAdvEventReportApproval, false);
    }

    /**
     * @generated
     */
    public static CisAdvEventReportApprovalTo toTo(CisAdvEventReportApproval cisAdvEventReportApproval, boolean withAllParts) {
        if (cisAdvEventReportApproval == null)
            return null;
        CisAdvEventReportApprovalTo to = new CisAdvEventReportApprovalTo();
        to.setId(cisAdvEventReportApproval.getId());
        to.setEventReportId(cisAdvEventReportApproval.getEventReportId());
        to.setOpinionOrgCode(cisAdvEventReportApproval.getOpinionOrgCode());
        to.setOpinionOrgName(cisAdvEventReportApproval.getOpinionOrgName());
        to.setOpinionState(cisAdvEventReportApproval.getOpinionState());
        to.setOpinionStateUser(cisAdvEventReportApproval.getOpinionStateUser());
        to.setOpinionStateUserName(cisAdvEventReportApproval.getOpinionStateUserName());
        to.setOpinionStateDate(cisAdvEventReportApproval.getOpinionStateDate());
        to.setEventConclusion(cisAdvEventReportApproval.getEventConclusion());
        to.setStatusCode(cisAdvEventReportApproval.getStatusCode());
        to.setCreatedDate(cisAdvEventReportApproval.getCreatedDate());
        to.setCreatedStaff(cisAdvEventReportApproval.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventReportApproval.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventReportApproval.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventReportApproval.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventReportApproval.getUpdatedStaffName());

        if (withAllParts) {
        }
        return to;
    }

}