package com.bjgoodwill.hip.ds.cis.adv.instrument.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.instrument.entity.CisAdvEventInstrument;
import com.bjgoodwill.hip.ds.cis.adv.instrument.to.CisAdvEventInstrumentTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventInstrumentAssembler {

    public static List<CisAdvEventInstrumentTo> toTos(List<CisAdvEventInstrument> cisAdvEventInstruments) {
		return toTos(cisAdvEventInstruments, false);
	}

	public static List<CisAdvEventInstrumentTo> toTos(List<CisAdvEventInstrument> cisAdvEventInstruments, boolean withAllParts) {
		Assert.notNull(cisAdvEventInstruments, "参数cisAdvEventInstruments不能为空！");

		List<CisAdvEventInstrumentTo> tos = new ArrayList<>();
		for (CisAdvEventInstrument cisAdvEventInstrument : cisAdvEventInstruments)
			tos.add(toTo(cisAdvEventInstrument, withAllParts));
		return tos;
	}

	public static CisAdvEventInstrumentTo toTo(CisAdvEventInstrument cisAdvEventInstrument) {
		return toTo(cisAdvEventInstrument, false);
	}

	/**
	 * @generated
	 */
	public static CisAdvEventInstrumentTo toTo(CisAdvEventInstrument cisAdvEventInstrument, boolean withAllParts) {
		if (cisAdvEventInstrument == null)
			return null;
		CisAdvEventInstrumentTo to = new CisAdvEventInstrumentTo();
        to.setId(cisAdvEventInstrument.getId());
        to.setEventReportId(cisAdvEventInstrument.getEventReportId());
        to.setPatType(cisAdvEventInstrument.getPatType());
        to.setInpatientCode(cisAdvEventInstrument.getInpatientCode());
        to.setVisitCode(cisAdvEventInstrument.getVisitCode());
        to.setPatName(cisAdvEventInstrument.getPatName());
        to.setSex(cisAdvEventInstrument.getSex());
        to.setBirthDate(cisAdvEventInstrument.getBirthDate());
        to.setAreaCode(cisAdvEventInstrument.getAreaCode());
        to.setAreaName(cisAdvEventInstrument.getAreaName());
        to.setPastValue(cisAdvEventInstrument.getPastValue());
        to.setInstrumentName(cisAdvEventInstrument.getInstrumentName());
        to.setInstrumentTrademark(cisAdvEventInstrument.getInstrumentTrademark());
        to.setInstrumentModel(cisAdvEventInstrument.getInstrumentModel());
        to.setInstrumentBatchNo(cisAdvEventInstrument.getInstrumentBatchNo());
        to.setInstrumentNo(cisAdvEventInstrument.getInstrumentNo());
        to.setInstrumentUdi(cisAdvEventInstrument.getInstrumentUdi());
        to.setManufactureDate(cisAdvEventInstrument.getManufactureDate());
        to.setValidUntil(cisAdvEventInstrument.getValidUntil());
        to.setSourceInformation(cisAdvEventInstrument.getSourceInformation());
        to.setEventDate(cisAdvEventInstrument.getEventDate());
        to.setKnowledgeDate(cisAdvEventInstrument.getKnowledgeDate());
        to.setInjuryDegree(cisAdvEventInstrument.getInjuryDegree());
        to.setInjuryDegreeName(cisAdvEventInstrument.getInjuryDegreeName());
        to.setInjuryPerformance(cisAdvEventInstrument.getInjuryPerformance());
        to.setFaultPerformance(cisAdvEventInstrument.getFaultPerformance());
        to.setTreatmentEffect(cisAdvEventInstrument.getTreatmentEffect());
        to.setUseDate(cisAdvEventInstrument.getUseDate());
        to.setUsePlace(cisAdvEventInstrument.getUsePlace());
        to.setUsePlaceName(cisAdvEventInstrument.getUsePlaceName());
        to.setUseProcess(cisAdvEventInstrument.getUseProcess());
        to.setUseCombined(cisAdvEventInstrument.getUseCombined());
        to.setEventReasonsType(cisAdvEventInstrument.getEventReasonsType());
        to.setEventReasonsTypeName(cisAdvEventInstrument.getEventReasonsTypeName());
        to.setEventReasonsDescribe(cisAdvEventInstrument.getEventReasonsDescribe());
        to.setDisposalSituation(cisAdvEventInstrument.getDisposalSituation());
        to.setCreatedDate(cisAdvEventInstrument.getCreatedDate());
        to.setCreatedStaff(cisAdvEventInstrument.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventInstrument.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventInstrument.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventInstrument.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventInstrument.getUpdatedStaffName());

		if (withAllParts) {
		}
		return to;
	}

}