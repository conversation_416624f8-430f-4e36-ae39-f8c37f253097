package com.bjgoodwill.hip.ds.cis.adv.fall.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.fall.entity.CisAdvEventFall;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventFallAssembler {

    public static List<CisAdvEventFallTo> toTos(List<CisAdvEventFall> cisAdvEventFalls) {
		return toTos(cisAdvEventFalls, false);
	}

	public static List<CisAdvEventFallTo> toTos(List<CisAdvEventFall> cisAdvEventFalls, boolean withAllParts) {
		Assert.notNull(cisAdvEventFalls, "参数cisAdvEventFalls不能为空！");

		List<CisAdvEventFallTo> tos = new ArrayList<>();
		for (CisAdvEventFall cisAdvEventFall : cisAdvEventFalls)
			tos.add(toTo(cisAdvEventFall, withAllParts));
		return tos;
	}

	public static CisAdvEventFallTo toTo(CisAdvEventFall cisAdvEventFall) {
		return toTo(cisAdvEventFall, false);
	}

	/**
	 * @generated
	 */
	public static CisAdvEventFallTo toTo(CisAdvEventFall cisAdvEventFall, boolean withAllParts) {
		if (cisAdvEventFall == null)
			return null;
		CisAdvEventFallTo to = new CisAdvEventFallTo();
        to.setId(cisAdvEventFall.getId());
        to.setEventReportId(cisAdvEventFall.getEventReportId());
        to.setPatType(cisAdvEventFall.getPatType());
        to.setInpatientCode(cisAdvEventFall.getInpatientCode());
        to.setVisitCode(cisAdvEventFall.getVisitCode());
        to.setPatName(cisAdvEventFall.getPatName());
        to.setSex(cisAdvEventFall.getSex());
        to.setBirthDate(cisAdvEventFall.getBirthDate());
        to.setAreaCode(cisAdvEventFall.getAreaCode());
        to.setAreaName(cisAdvEventFall.getAreaName());
        to.setInDate(cisAdvEventFall.getInDate());
        to.setEventDate(cisAdvEventFall.getEventDate());
        to.setEventPlace(cisAdvEventFall.getEventPlace());
        to.setEventPlaceName(cisAdvEventFall.getEventPlaceName());
        to.setThisFallNum(cisAdvEventFall.getThisFallNum());
        to.setFallActivityAbility(cisAdvEventFall.getFallActivityAbility());
        to.setFallActivity(cisAdvEventFall.getFallActivity());
        to.setHurtFlag(cisAdvEventFall.isHurtFlag());
        to.setHurtLevel(cisAdvEventFall.getHurtLevel());
        to.setHurtLevelName(cisAdvEventFall.getHurtLevelName());
        to.setFallReasonsType(cisAdvEventFall.getFallReasonsType());
        to.setFallReasonsTypeName(cisAdvEventFall.getFallReasonsTypeName());
        to.setAssessmentInstrument(cisAdvEventFall.getAssessmentInstrument());
        to.setAssessmentInstrumentName(cisAdvEventFall.getAssessmentInstrumentName());
        to.setAssessmentFlag(cisAdvEventFall.isAssessmentFlag());
        to.setAssessmentScore(cisAdvEventFall.getAssessmentScore());
        to.setFallHighGroup(cisAdvEventFall.isFallHighGroup());
        to.setLastTime(cisAdvEventFall.getLastTime());
        to.setLastTimeName(cisAdvEventFall.getLastTimeName());
        to.setConstraintFlag(cisAdvEventFall.isConstraintFlag());
        to.setWorkingLife(cisAdvEventFall.getWorkingLife());
        to.setWorkingLifeName(cisAdvEventFall.getWorkingLifeName());
        to.setDutyNum(cisAdvEventFall.getDutyNum());
        to.setAreaNum(cisAdvEventFall.getAreaNum());
        to.setEventProcess(cisAdvEventFall.getEventProcess());
        to.setNursSignature(cisAdvEventFall.getNursSignature());
        to.setNursSignatureName(cisAdvEventFall.getNursSignatureName());
        to.setNursSignatureDate(cisAdvEventFall.getNursSignatureDate());
        to.setNursingMeasure(cisAdvEventFall.getNursingMeasure());
        to.setHeadSignature(cisAdvEventFall.getHeadSignature());
        to.setHeadSignatureName(cisAdvEventFall.getHeadSignatureName());
        to.setHeadSignatureDate(cisAdvEventFall.getHeadSignatureDate());
        to.setNursDeptOpinion(cisAdvEventFall.getNursDeptOpinion());
        to.setNursDeptSignature(cisAdvEventFall.getNursDeptSignature());
        to.setNursDeptSignatureName(cisAdvEventFall.getNursDeptSignatureName());
        to.setDeptSignatureDate(cisAdvEventFall.getDeptSignatureDate());
        to.setCreatedDate(cisAdvEventFall.getCreatedDate());
        to.setCreatedStaff(cisAdvEventFall.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventFall.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventFall.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventFall.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventFall.getUpdatedStaffName());

		if (withAllParts) {
		}
		return to;
	}

}