package com.bjgoodwill.hip.ds.cis.adv.maintenance.repository;

import com.bjgoodwill.hip.ds.cis.adv.maintenance.entity.CisAdvEventMaintenance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.adv.advEventMaintenance.repository.CisAdvEventMaintenanceRepository")
public interface CisAdvEventMaintenanceRepository extends JpaRepository<CisAdvEventMaintenance, String>, JpaSpecificationExecutor<CisAdvEventMaintenance> {

}