package com.bjgoodwill.hip.ds.cis.adv.maintenance.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.enmus.CisAdvBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.repository.CisAdvEventMaintenanceRepository;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.to.CisAdvEventMaintenanceEto;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.to.CisAdvEventMaintenanceNto;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.to.CisAdvEventMaintenanceQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "不良事件维护")
@Table(name = "cis_adv_event_maintenance", indexes = {}, uniqueConstraints = {})
public class CisAdvEventMaintenance {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("上级目录编码")
    @Column(name = "parent_code", nullable = true, length = 16)
    private String parentCode;


    @Comment("不良事件编码")
    @Column(name = "event_code", nullable = true, length = 16)
    private String eventCode;


    @Comment("不良事件名称")
    @Column(name = "event_name", nullable = true, length = 128)
    private String eventName;


    @Comment("不良事件名称")
    @Column(name = "event_level", nullable = true)
    private Integer eventLevel;


    @Comment("已启用")
    @Column(name = "enabled", nullable = false)
    private boolean enabled;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    public static Optional<CisAdvEventMaintenance> getCisAdvEventMaintenanceById(String id) {
        return dao().findById(id);
    }

    public static List<CisAdvEventMaintenance> getCisAdvEventMaintenances(CisAdvEventMaintenanceQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisAdvEventMaintenance> getCisAdvEventMaintenancePage(CisAdvEventMaintenanceQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisAdvEventMaintenance> getSpecification(CisAdvEventMaintenanceQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getParentCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("parentCode"), qto.getParentCode()));
            }
            if (StringUtils.isNotBlank(qto.getEventCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventCode"), qto.getEventCode()));
            }
            if (StringUtils.isNotBlank(qto.getEventName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventName"), qto.getEventName()));
            }
            if (qto.getEventLevel() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventLevel"), qto.getEventLevel()));
            }
            if (qto.getEnabled() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("enabled"), qto.getEnabled()));
            }
            return predicate;
        };
    }

    private static CisAdvEventMaintenanceRepository dao() {
        return SpringUtil.getBean(CisAdvEventMaintenanceRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getParentCode() {
        return parentCode;
    }

    protected void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getEventCode() {
        return eventCode;
    }

    protected void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getEventName() {
        return eventName;
    }

    protected void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public Integer getEventLevel() {
        return eventLevel;
    }

    protected void setEventLevel(Integer eventLevel) {
        this.eventLevel = eventLevel;
    }

    public boolean isEnabled() {
        return enabled;
    }

    protected void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisAdvEventMaintenance other = (CisAdvEventMaintenance) obj;
        return Objects.equals(id, other.id);
    }

    public CisAdvEventMaintenance create(CisAdvEventMaintenanceNto cisAdvEventMaintenanceNto) {
        BusinessAssert.notNull(cisAdvEventMaintenanceNto, CisAdvBusinessErrorEnum.BUS_CIS_ADV_0001, "参数cisAdvEventMaintenanceNto");

        setId(cisAdvEventMaintenanceNto.getId());
        setParentCode(cisAdvEventMaintenanceNto.getParentCode());
        setEventCode(cisAdvEventMaintenanceNto.getEventCode());
        setEventName(cisAdvEventMaintenanceNto.getEventName());
        setEventLevel(cisAdvEventMaintenanceNto.getEventLevel());
        setEnabled(true);
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventMaintenanceEto cisAdvEventMaintenanceEto) {
        setParentCode(cisAdvEventMaintenanceEto.getParentCode());
        setEventCode(cisAdvEventMaintenanceEto.getEventCode());
        setEventName(cisAdvEventMaintenanceEto.getEventName());
        setEventLevel(cisAdvEventMaintenanceEto.getEventLevel());
        setEnabled(cisAdvEventMaintenanceEto.isEnabled());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void enable() {
        setEnabled(true);
    }

    public void disable() {
        setEnabled(false);
    }

    public void delete() {
        dao().delete(this);
    }

}
