package com.bjgoodwill.hip.ds.cis.adv.drugExplain.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.entity.CisAdvEventDrugExplain;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.service.CisAdvEventDrugExplainService;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.service.internal.assembler.CisAdvEventDrugExplainAssembler;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.to.CisAdvEventDrugExplainEto;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.to.CisAdvEventDrugExplainNto;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.to.CisAdvEventDrugExplainQto;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.to.CisAdvEventDrugExplainTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.drugExplain.service.CisAdvEventDrugExplainService")
@RequestMapping(value = "/api/cisadv/drugExplain/cisAdvEventDrugExplain", produces = "application/json; charset=utf-8")
public class CisAdvEventDrugExplainServiceImpl implements CisAdvEventDrugExplainService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventDrugExplainTo> getCisAdvEventDrugExplains(CisAdvEventDrugExplainQto cisAdvEventDrugExplainQto) {
        return CisAdvEventDrugExplainAssembler.toTos(CisAdvEventDrugExplain.getCisAdvEventDrugExplains(cisAdvEventDrugExplainQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventDrugExplainTo> getCisAdvEventDrugExplainPage(CisAdvEventDrugExplainQto cisAdvEventDrugExplainQto) {
        Page<CisAdvEventDrugExplain> page = CisAdvEventDrugExplain.getCisAdvEventDrugExplainPage(cisAdvEventDrugExplainQto);
        Page<CisAdvEventDrugExplainTo> result = page.map(CisAdvEventDrugExplainAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventDrugExplainTo getCisAdvEventDrugExplainById(String id) {
        return CisAdvEventDrugExplainAssembler.toTo(CisAdvEventDrugExplain.getCisAdvEventDrugExplainById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventDrugExplainTo createCisAdvEventDrugExplain(CisAdvEventDrugExplainNto cisAdvEventDrugExplainNto) {
        CisAdvEventDrugExplain cisAdvEventDrugExplain = new CisAdvEventDrugExplain();
        cisAdvEventDrugExplain = cisAdvEventDrugExplain.create(cisAdvEventDrugExplainNto);
        return CisAdvEventDrugExplainAssembler.toTo(cisAdvEventDrugExplain);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventDrugExplain(String id, CisAdvEventDrugExplainEto cisAdvEventDrugExplainEto) {
        Optional<CisAdvEventDrugExplain> cisAdvEventDrugExplainOptional = CisAdvEventDrugExplain.getCisAdvEventDrugExplainById(id);
        cisAdvEventDrugExplainOptional.ifPresent(cisAdvEventDrugExplain -> cisAdvEventDrugExplain.update(cisAdvEventDrugExplainEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventDrugExplain(String id) {
        Optional<CisAdvEventDrugExplain> cisAdvEventDrugExplainOptional = CisAdvEventDrugExplain.getCisAdvEventDrugExplainById(id);
        cisAdvEventDrugExplainOptional.ifPresent(cisAdvEventDrugExplain -> cisAdvEventDrugExplain.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}