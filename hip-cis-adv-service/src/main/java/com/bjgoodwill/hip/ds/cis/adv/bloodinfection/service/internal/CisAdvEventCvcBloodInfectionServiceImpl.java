package com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service.internal;

import java.util.*;
import org.springframework.data.domain.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.WebDataBinder;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service.*;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.*;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.entity.*;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service.internal.assembler.*;

@RestController("com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service.CisAdvEventCvcBloodInfectionService")
@RequestMapping(value = "/api/cisadv/bloodinfection/cisAdvEventCvcBloodInfection", produces = "application/json; charset=utf-8")
public class CisAdvEventCvcBloodInfectionServiceImpl implements CisAdvEventCvcBloodInfectionService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventCvcBloodInfectionTo> getCisAdvEventCvcBloodInfections(CisAdvEventCvcBloodInfectionQto cisAdvEventCvcBloodInfectionQto) {
        return CisAdvEventCvcBloodInfectionAssembler.toTos(CisAdvEventCvcBloodInfection.getCisAdvEventCvcBloodInfections(cisAdvEventCvcBloodInfectionQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventCvcBloodInfectionTo> getCisAdvEventCvcBloodInfectionPage(CisAdvEventCvcBloodInfectionQto cisAdvEventCvcBloodInfectionQto) {
        Page<CisAdvEventCvcBloodInfection> page = CisAdvEventCvcBloodInfection.getCisAdvEventCvcBloodInfectionPage(cisAdvEventCvcBloodInfectionQto);
        Page<CisAdvEventCvcBloodInfectionTo> result = page.map(CisAdvEventCvcBloodInfectionAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventCvcBloodInfectionTo getCisAdvEventCvcBloodInfectionById(String id) {
        return CisAdvEventCvcBloodInfectionAssembler.toTo(CisAdvEventCvcBloodInfection.getCisAdvEventCvcBloodInfectionById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventCvcBloodInfectionTo createCisAdvEventCvcBloodInfection(CisAdvEventCvcBloodInfectionNto cisAdvEventCvcBloodInfectionNto) {
        CisAdvEventCvcBloodInfection cisAdvEventCvcBloodInfection = new CisAdvEventCvcBloodInfection();
		cisAdvEventCvcBloodInfection = cisAdvEventCvcBloodInfection.create(cisAdvEventCvcBloodInfectionNto);
		return CisAdvEventCvcBloodInfectionAssembler.toTo(cisAdvEventCvcBloodInfection);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventCvcBloodInfection(String id, CisAdvEventCvcBloodInfectionEto cisAdvEventCvcBloodInfectionEto) {
        Optional<CisAdvEventCvcBloodInfection> cisAdvEventCvcBloodInfectionOptional = CisAdvEventCvcBloodInfection.getCisAdvEventCvcBloodInfectionById(id);
		cisAdvEventCvcBloodInfectionOptional.ifPresent(cisAdvEventCvcBloodInfection -> cisAdvEventCvcBloodInfection.update(cisAdvEventCvcBloodInfectionEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventCvcBloodInfection(String id) {
        Optional<CisAdvEventCvcBloodInfection> cisAdvEventCvcBloodInfectionOptional = CisAdvEventCvcBloodInfection.getCisAdvEventCvcBloodInfectionById(id);
		cisAdvEventCvcBloodInfectionOptional.ifPresent(cisAdvEventCvcBloodInfection -> cisAdvEventCvcBloodInfection.delete());
    }

    @InitBinder
	public void initBinder(WebDataBinder binder) {
	}
}