package com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.entity.CisAdvEventSharpInjuries;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.to.CisAdvEventSharpInjuriesTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventSharpInjuriesAssembler {

    public static List<CisAdvEventSharpInjuriesTo> toTos(List<CisAdvEventSharpInjuries> cisAdvEventSharpInjuriess) {
        return toTos(cisAdvEventSharpInjuriess, false);
    }

    public static List<CisAdvEventSharpInjuriesTo> toTos(List<CisAdvEventSharpInjuries> cisAdvEventSharpInjuriess, boolean withAllParts) {
        Assert.notNull(cisAdvEventSharpInjuriess, "参数cisAdvEventSharpInjuriess不能为空！");

        List<CisAdvEventSharpInjuriesTo> tos = new ArrayList<>();
        for (CisAdvEventSharpInjuries cisAdvEventSharpInjuries : cisAdvEventSharpInjuriess)
            tos.add(toTo(cisAdvEventSharpInjuries, withAllParts));
        return tos;
    }

    public static CisAdvEventSharpInjuriesTo toTo(CisAdvEventSharpInjuries cisAdvEventSharpInjuries) {
        return toTo(cisAdvEventSharpInjuries, false);
    }

    /**
     * @generated
     */
    public static CisAdvEventSharpInjuriesTo toTo(CisAdvEventSharpInjuries cisAdvEventSharpInjuries, boolean withAllParts) {
        if (cisAdvEventSharpInjuries == null)
            return null;
        CisAdvEventSharpInjuriesTo to = new CisAdvEventSharpInjuriesTo();
        to.setId(cisAdvEventSharpInjuries.getId());
        to.setEventReportId(cisAdvEventSharpInjuries.getEventReportId());
        to.setPatType(cisAdvEventSharpInjuries.getPatType());
        to.setInpatientCode(cisAdvEventSharpInjuries.getInpatientCode());
        to.setVisitCode(cisAdvEventSharpInjuries.getVisitCode());
        to.setPatName(cisAdvEventSharpInjuries.getPatName());
        to.setSex(cisAdvEventSharpInjuries.getSex());
        to.setBirthDate(cisAdvEventSharpInjuries.getBirthDate());
        to.setAreaCode(cisAdvEventSharpInjuries.getAreaCode());
        to.setAreaName(cisAdvEventSharpInjuries.getAreaName());
        to.setEventPlace(cisAdvEventSharpInjuries.getEventPlace());
        to.setEventPlaceName(cisAdvEventSharpInjuries.getEventPlaceName());
        to.setUnimpOrgCode(cisAdvEventSharpInjuries.getUnimpOrgCode());
        to.setUnimpOrgName(cisAdvEventSharpInjuries.getUnimpOrgName());
        to.setStaffType(cisAdvEventSharpInjuries.getStaffType());
        to.setStaffTypeName(cisAdvEventSharpInjuries.getStaffTypeName());
        to.setWorkingLife(cisAdvEventSharpInjuries.getWorkingLife());
        to.setWorkingLifeName(cisAdvEventSharpInjuries.getWorkingLifeName());
        to.setEventDate(cisAdvEventSharpInjuries.getEventDate());
        to.setEventType(cisAdvEventSharpInjuries.getEventType());
        to.setEventTypeName(cisAdvEventSharpInjuries.getEventTypeName());
        to.setSharpDevice(cisAdvEventSharpInjuries.getSharpDevice());
        to.setSharpDeviceName(cisAdvEventSharpInjuries.getSharpDeviceName());
        to.setSharpOperation(cisAdvEventSharpInjuries.getSharpOperation());
        to.setSharpOperationName(cisAdvEventSharpInjuries.getSharpOperationName());
        to.setPollutionFlag(cisAdvEventSharpInjuries.getPollutionFlag());
        to.setPollutionFlagName(cisAdvEventSharpInjuries.getPollutionFlagName());
        to.setPollutionType(cisAdvEventSharpInjuries.getPollutionType());
        to.setPollutionTypeName(cisAdvEventSharpInjuries.getPollutionTypeName());
        to.setBloodTransmittFlag(cisAdvEventSharpInjuries.getBloodTransmittFlag());
        to.setBloodTransmittFlagName(cisAdvEventSharpInjuries.getBloodTransmittFlagName());
        to.setBloodTransmittType(cisAdvEventSharpInjuries.getBloodTransmittType());
        to.setBloodTransmittTypeName(cisAdvEventSharpInjuries.getBloodTransmittTypeName());
        to.setTrackFlag(cisAdvEventSharpInjuries.isTrackFlag());
        to.setUntrackReason(cisAdvEventSharpInjuries.getUntrackReason());
        to.setUntrackReasonName(cisAdvEventSharpInjuries.getUntrackReasonName());
        to.setConfirmedFlag(cisAdvEventSharpInjuries.getConfirmedFlag());
        to.setConfirmedFlagName(cisAdvEventSharpInjuries.getConfirmedFlagName());
        to.setDiseaseType(cisAdvEventSharpInjuries.getDiseaseType());
        to.setDiseaseTypeName(cisAdvEventSharpInjuries.getDiseaseTypeName());
        to.setCreatedDate(cisAdvEventSharpInjuries.getCreatedDate());
        to.setCreatedStaff(cisAdvEventSharpInjuries.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventSharpInjuries.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventSharpInjuries.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventSharpInjuries.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventSharpInjuries.getUpdatedStaffName());

        if (withAllParts) {
        }
        return to;
    }

}