package com.bjgoodwill.hip.ds.cis.adv.reportApproval.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.entity.CisAdvEventReportApproval;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.service.CisAdvEventReportApprovalService;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.service.internal.assembler.CisAdvEventReportApprovalAssembler;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.to.CisAdvEventReportApprovalEto;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.to.CisAdvEventReportApprovalNto;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.to.CisAdvEventReportApprovalQto;
import com.bjgoodwill.hip.ds.cis.adv.reportApproval.to.CisAdvEventReportApprovalTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.reportApproval.service.CisAdvEventReportApprovalService")
@RequestMapping(value = "/api/cisadv/reportApproval/cisAdvEventReportApproval", produces = "application/json; charset=utf-8")
public class CisAdvEventReportApprovalServiceImpl implements CisAdvEventReportApprovalService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventReportApprovalTo> getCisAdvEventReportApprovals(CisAdvEventReportApprovalQto cisAdvEventReportApprovalQto) {
        return CisAdvEventReportApprovalAssembler.toTos(CisAdvEventReportApproval.getCisAdvEventReportApprovals(cisAdvEventReportApprovalQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventReportApprovalTo> getCisAdvEventReportApprovalPage(CisAdvEventReportApprovalQto cisAdvEventReportApprovalQto) {
        Page<CisAdvEventReportApproval> page = CisAdvEventReportApproval.getCisAdvEventReportApprovalPage(cisAdvEventReportApprovalQto);
        Page<CisAdvEventReportApprovalTo> result = page.map(CisAdvEventReportApprovalAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventReportApprovalTo getCisAdvEventReportApprovalById(String id) {
        return CisAdvEventReportApprovalAssembler.toTo(CisAdvEventReportApproval.getCisAdvEventReportApprovalById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventReportApprovalTo createCisAdvEventReportApproval(CisAdvEventReportApprovalNto cisAdvEventReportApprovalNto) {
        CisAdvEventReportApproval cisAdvEventReportApproval = new CisAdvEventReportApproval();
		cisAdvEventReportApproval = cisAdvEventReportApproval.create(cisAdvEventReportApprovalNto);
		return CisAdvEventReportApprovalAssembler.toTo(cisAdvEventReportApproval);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventReportApproval(String id, CisAdvEventReportApprovalEto cisAdvEventReportApprovalEto) {
        Optional<CisAdvEventReportApproval> cisAdvEventReportApprovalOptional = CisAdvEventReportApproval.getCisAdvEventReportApprovalById(id);
		cisAdvEventReportApprovalOptional.ifPresent(cisAdvEventReportApproval -> cisAdvEventReportApproval.update(cisAdvEventReportApprovalEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventReportApproval(String id) {
        Optional<CisAdvEventReportApproval> cisAdvEventReportApprovalOptional = CisAdvEventReportApproval.getCisAdvEventReportApprovalById(id);
		cisAdvEventReportApprovalOptional.ifPresent(cisAdvEventReportApproval -> cisAdvEventReportApproval.delete());
    }

    @InitBinder
	public void initBinder(WebDataBinder binder) {
	}
}