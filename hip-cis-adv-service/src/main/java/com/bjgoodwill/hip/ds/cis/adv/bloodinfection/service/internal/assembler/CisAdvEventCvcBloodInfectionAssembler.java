package com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.entity.CisAdvEventCvcBloodInfection;
import com.bjgoodwill.hip.ds.cis.adv.bloodinfection.to.CisAdvEventCvcBloodInfectionTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventCvcBloodInfectionAssembler {

    public static List<CisAdvEventCvcBloodInfectionTo> toTos(List<CisAdvEventCvcBloodInfection> cisAdvEventCvcBloodInfections) {
        return toTos(cisAdvEventCvcBloodInfections, false);
    }

    public static List<CisAdvEventCvcBloodInfectionTo> toTos(List<CisAdvEventCvcBloodInfection> cisAdvEventCvcBloodInfections, boolean withAllParts) {
        Assert.notNull(cisAdvEventCvcBloodInfections, "参数cisAdvEventCvcBloodInfections不能为空！");

        List<CisAdvEventCvcBloodInfectionTo> tos = new ArrayList<>();
        for (CisAdvEventCvcBloodInfection cisAdvEventCvcBloodInfection : cisAdvEventCvcBloodInfections)
            tos.add(toTo(cisAdvEventCvcBloodInfection, withAllParts));
        return tos;
    }

    public static CisAdvEventCvcBloodInfectionTo toTo(CisAdvEventCvcBloodInfection cisAdvEventCvcBloodInfection) {
        return toTo(cisAdvEventCvcBloodInfection, false);
    }

    /**
     * @generated
     */
    public static CisAdvEventCvcBloodInfectionTo toTo(CisAdvEventCvcBloodInfection cisAdvEventCvcBloodInfection, boolean withAllParts) {
        if (cisAdvEventCvcBloodInfection == null)
            return null;
        CisAdvEventCvcBloodInfectionTo to = new CisAdvEventCvcBloodInfectionTo();
        to.setId(cisAdvEventCvcBloodInfection.getId());
        to.setEventReportId(cisAdvEventCvcBloodInfection.getEventReportId());
        to.setPatType(cisAdvEventCvcBloodInfection.getPatType());
        to.setInpatientCode(cisAdvEventCvcBloodInfection.getInpatientCode());
        to.setVisitCode(cisAdvEventCvcBloodInfection.getVisitCode());
        to.setPatName(cisAdvEventCvcBloodInfection.getPatName());
        to.setSex(cisAdvEventCvcBloodInfection.getSex());
        to.setBirthDate(cisAdvEventCvcBloodInfection.getBirthDate());
        to.setAgeRange(cisAdvEventCvcBloodInfection.getAgeRange());
        to.setAreaCode(cisAdvEventCvcBloodInfection.getAreaCode());
        to.setAreaName(cisAdvEventCvcBloodInfection.getAreaName());
        to.setInDate(cisAdvEventCvcBloodInfection.getInDate());
        to.setIndwellReason(cisAdvEventCvcBloodInfection.getIndwellReason());
        to.setIndwellReasonName(cisAdvEventCvcBloodInfection.getIndwellReasonName());
        to.setCvcLocation(cisAdvEventCvcBloodInfection.getCvcLocation());
        to.setCvcLocationName(cisAdvEventCvcBloodInfection.getCvcLocationName());
        to.setExtubationType(cisAdvEventCvcBloodInfection.getExtubationType());
        to.setExtubationTypeName(cisAdvEventCvcBloodInfection.getExtubationTypeName());
        to.setAntibacterialFlag(cisAdvEventCvcBloodInfection.isAntibacterialFlag());
        to.setCvcTime(cisAdvEventCvcBloodInfection.getCvcTime());
        to.setCreatedDate(cisAdvEventCvcBloodInfection.getCreatedDate());
        to.setCreatedStaff(cisAdvEventCvcBloodInfection.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventCvcBloodInfection.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventCvcBloodInfection.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventCvcBloodInfection.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventCvcBloodInfection.getUpdatedStaffName());

        if (withAllParts) {
        }
        return to;
    }

}