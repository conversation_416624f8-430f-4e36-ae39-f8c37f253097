package com.bjgoodwill.hip.ds.cis.adv.maintenance.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.entity.CisAdvEventMaintenance;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.service.CisAdvEventMaintenanceService;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.service.internal.assembler.CisAdvEventMaintenanceAssembler;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.to.CisAdvEventMaintenanceEto;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.to.CisAdvEventMaintenanceNto;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.to.CisAdvEventMaintenanceQto;
import com.bjgoodwill.hip.ds.cis.adv.maintenance.to.CisAdvEventMaintenanceTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.maintenance.service.CisAdvEventMaintenanceService")
@RequestMapping(value = "/api/cisadv/maintenance/cisAdvEventMaintenance", produces = "application/json; charset=utf-8")
public class CisAdvEventMaintenanceServiceImpl implements CisAdvEventMaintenanceService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventMaintenanceTo> getCisAdvEventMaintenances(CisAdvEventMaintenanceQto cisAdvEventMaintenanceQto) {
        return CisAdvEventMaintenanceAssembler.toTos(CisAdvEventMaintenance.getCisAdvEventMaintenances(cisAdvEventMaintenanceQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventMaintenanceTo> getCisAdvEventMaintenancePage(CisAdvEventMaintenanceQto cisAdvEventMaintenanceQto) {
        Page<CisAdvEventMaintenance> page = CisAdvEventMaintenance.getCisAdvEventMaintenancePage(cisAdvEventMaintenanceQto);
        Page<CisAdvEventMaintenanceTo> result = page.map(CisAdvEventMaintenanceAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventMaintenanceTo getCisAdvEventMaintenanceById(String id) {
        return CisAdvEventMaintenanceAssembler.toTo(CisAdvEventMaintenance.getCisAdvEventMaintenanceById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventMaintenanceTo createCisAdvEventMaintenance(CisAdvEventMaintenanceNto cisAdvEventMaintenanceNto) {
        CisAdvEventMaintenance cisAdvEventMaintenance = new CisAdvEventMaintenance();
		cisAdvEventMaintenance = cisAdvEventMaintenance.create(cisAdvEventMaintenanceNto);
		return CisAdvEventMaintenanceAssembler.toTo(cisAdvEventMaintenance);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventMaintenance(String id, CisAdvEventMaintenanceEto cisAdvEventMaintenanceEto) {
        Optional<CisAdvEventMaintenance> cisAdvEventMaintenanceOptional = CisAdvEventMaintenance.getCisAdvEventMaintenanceById(id);
		cisAdvEventMaintenanceOptional.ifPresent(cisAdvEventMaintenance -> cisAdvEventMaintenance.update(cisAdvEventMaintenanceEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void enableCisAdvEventMaintenance(String id) {
        Optional<CisAdvEventMaintenance> cisAdvEventMaintenanceOptional = CisAdvEventMaintenance.getCisAdvEventMaintenanceById(id);
		cisAdvEventMaintenanceOptional.ifPresent(cisAdvEventMaintenance -> cisAdvEventMaintenance.enable());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void disableCisAdvEventMaintenance(String id) {
        Optional<CisAdvEventMaintenance> cisAdvEventMaintenanceOptional = CisAdvEventMaintenance.getCisAdvEventMaintenanceById(id);
		cisAdvEventMaintenanceOptional.ifPresent(cisAdvEventMaintenance -> cisAdvEventMaintenance.disable());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventMaintenance(String id) {
        Optional<CisAdvEventMaintenance> cisAdvEventMaintenanceOptional = CisAdvEventMaintenance.getCisAdvEventMaintenanceById(id);
		cisAdvEventMaintenanceOptional.ifPresent(cisAdvEventMaintenance -> cisAdvEventMaintenance.delete());
    }

    @InitBinder
	public void initBinder(WebDataBinder binder) {
	}
}