package com.bjgoodwill.hip.ds.cis.adv.report.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.AdvEventsStatusEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.enmus.CisAdvBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.adv.report.repository.CisAdvEventReportRepository;
import com.bjgoodwill.hip.ds.cis.adv.report.to.CisAdvEventReportEto;
import com.bjgoodwill.hip.ds.cis.adv.report.to.CisAdvEventReportNto;
import com.bjgoodwill.hip.ds.cis.adv.report.to.CisAdvEventReportQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "不良事件报告")
@Table(name = "cis_adv_event_report", indexes = {}, uniqueConstraints = {})
public class CisAdvEventReport {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("主索引")
    @Column(name = "pat_mi_code", nullable = true, length = 20)
    private String patMiCode;


    @Comment("患者类型")
    @Column(name = "pat_type", nullable = true, length = 16)
    private String patType;


    @Comment("住院号(门诊就诊卡号)")
    @Column(name = "inpatient_code", nullable = true, length = 16)
    private String inpatientCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true, length = 16)
    private String visitCode;


    @Comment("患者姓名")
    @Column(name = "pat_name", nullable = true, length = 64)
    private String patName;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("患者职业")
    @Column(name = "work", nullable = true, length = 16)
    private String work;


    @Comment("患者所在科室(门诊挂号科室)")
    @Column(name = "area_code", nullable = true, length = 16)
    private String areaCode;

    @Comment("患者所在科室名称(门诊挂号科室)")
    @Column(name = "area_name", nullable = true, length = 16)
    private String areaName;


    @Comment("床号")
    @Column(name = "bed_name", nullable = true, length = 64)
    private String bedName;


    @Comment("事件发生时间")
    @Column(name = "event_date", nullable = true)
    private LocalDateTime eventDate;


    @Comment("临床诊断")
    @Column(name = "clinical_diagnosis", nullable = true, length = 255)
    private String clinicalDiagnosis;


    @Comment("事件发生场所：字典：advEventPlace急诊；门诊；住院部；医技部门；行政后勤部门；其它")
    @Column(name = "event_place", nullable = true)
    private String eventPlace;


    @Comment("其它事件发生地")
    @Column(name = "other_event_place", nullable = true)
    private String otherEventPlace;


    @Comment("不良后果标识")
    @Column(name = "adv_consequences_flag", nullable = true, length = 16)
    private String advConsequencesFlag;


    @Comment("不良后果内容")
    @Column(name = "adv_consequences", nullable = true)
    private String advConsequences;


    @Comment("事件经过")
    @Column(name = "event_after", nullable = true)
    private String eventAfter;


    @Comment("不良事件类别")
    @Column(name = "event_type", nullable = true, length = 16)
    private String eventType;


    @Comment("附卡")
    @Column(name = "event_card", nullable = true, length = 128)
    private String eventCard;


    @Comment("附卡标识")
    @Column(name = "addition_flag", nullable = false)
    private boolean additionFlag;


    @Comment("事件处理情况")
    @Column(name = "event_handle", nullable = true)
    private String eventHandle;


    @Comment("不良事件等级:字典advEventLevel：Ⅰ级事件;Ⅱ级事件;Ⅲ级事件;Ⅳ级事件;")
    @Column(name = "event_level", nullable = true)
    private String eventLevel;


    @Comment("导致事件的可能原因")
    @Column(name = "event_why", nullable = true)
    private String eventWhy;


    @Comment("不良事件评价人")
    @Column(name = "evaluation_user", nullable = true, length = 16)
    private String evaluationUser;


    @Comment("不良事件评价时间")
    @Column(name = "evaluation_date", nullable = true)
    private LocalDateTime evaluationDate;


    @Comment("持续改进措施")
    @Column(name = "improvement_measures", nullable = true)
    private String improvementMeasures;


    @Comment("持续改进措施填写人")
    @Column(name = "improvement_user", nullable = true, length = 16)
    private String improvementUser;

    @Comment("持续改进措施填写人名称")
    @Column(name = "improvement_user_name", nullable = true, length = 32)
    private String improvementUserName;


    @Comment("持续改进措施填写时间")
    @Column(name = "improvement_date", nullable = true)
    private LocalDateTime improvementDate;


    @Comment("转发接收科室")
    @Column(name = "receive_org_code", nullable = true, length = 16)
    private String receiveOrgCode;

    @Comment("转发接收科室名称")
    @Column(name = "receive_org_name", nullable = true, length = 32)
    private String receiveOrgName;


    @Comment("主管部门意见陈述")
    @Column(name = "opinion_state", nullable = true)
    private String opinionState;


    @Comment("主管部门意见陈述人")
    @Column(name = "opinion_state_user", nullable = true, length = 16)
    private String opinionStateUser;

    @Comment("主管部门意见陈述人名称")
    @Column(name = "opinion_state_user_name", nullable = true, length = 32)
    private String opinionStateUserName;


    @Comment("主管部门意见陈述时间")
    @Column(name = "opinion_state_date", nullable = true)
    private LocalDateTime opinionStateDate;


    @Comment("报告人类别：Doctor医师；Technician技师；Nurse护理人员；Other其他")
    @Column(name = "report_user_type", nullable = true, length = 16)
    private String reportUserType;

    @Comment("报告人类别名称：Doctor医师；Technician技师；Nurse护理人员；Other其他")
    @Column(name = "report_user_type_name", nullable = true, length = 32)
    private String reportUserTypeName;


    @Comment("当事人的类别")
    @Column(name = "litigant_type", nullable = true, length = 16)
    private String litigantType;


    @Comment("职称：Senior高级；Intermediate中级；Primary初级")
    @Column(name = "title", nullable = true, length = 16)
    private String title;

    @Comment("职称名称：Senior高级；Intermediate中级；Primary初级")
    @Column(name = "title_name", nullable = true, length = 16)
    private String titleName;


    @Comment("责任者姓名")
    @Column(name = "duty_user", nullable = true, length = 128)
    private String dutyUser;


    @Comment("责任者职称:Senior高级；Intermediate中级；Primary初级")
    @Column(name = "duty_title", nullable = true, length = 64)
    private String dutyTitle;

    @Comment("责任者职称名称:Senior高级；Intermediate中级；Primary初级")
    @Column(name = "duty_title_name", nullable = true, length = 64)
    private String dutyTitleName;


    @Comment("责任者工作年限：A小于一年；B 1年（包含）到2年；C 2年（包含）到5年；D 5年（包含）到10年；E 10年（包含）到20年；F 大于20年")
    @Column(name = "working_life", nullable = true, length = 16)
    private String workingLife;

    @Comment("责任者工作年限名称：A小于一年；B 1年（包含）到2年；C 2年（包含）到5年；D 5年（包含）到10年；E 10年（包含）到20年；F 大于20年")
    @Column(name = "working_life_name", nullable = true, length = 16)
    private String workingLifeName;


    @Comment("护士级别:N0,N1,N2,N3,N4")
    @Column(name = "duty_nurse_level", nullable = true, length = 16)
    private String dutyNurseLevel;


    @Comment("责任者姓名")
    @Column(name = "duty_user_2", nullable = true, length = 128)
    private String dutyUser2;


    @Comment("责任者职称:Senior高级；Intermediate中级；Primary初级")
    @Column(name = "duty_title_2", nullable = true, length = 64)
    private String dutyTitle2;

    @Comment("责任者职称名称:Senior高级；Intermediate中级；Primary初级")
    @Column(name = "duty_title_2_name", nullable = true, length = 64)
    private String dutyTitle2Name;


    @Comment("责任者工作年限：A小于一年；B 1年（包含）到2年；C 2年（包含）到5年；D 5年（包含）到10年；E 10年（包含）到20年；F 大于20年")
    @Column(name = "working_life_2", nullable = true, length = 16)
    private String workingLife2;

    @Comment("责任者工作年限名称：A小于一年；B 1年（包含）到2年；C 2年（包含）到5年；D 5年（包含）到10年；E 10年（包含）到20年；F 大于20年")
    @Column(name = "working_life_2_name", nullable = true, length = 16)
    private String workingLife2Name;


    @Comment("护士级别:N0,N1,N2,N3,N4")
    @Column(name = "duty_nurse_level_2", nullable = true, length = 16)
    private String dutyNurseLevel2;


    @Comment("报告人")
    @Column(name = "report_user", nullable = true, length = 16)
    private String reportUser;


    @Comment("报告科室")
    @Column(name = "report_org_code", nullable = true, length = 16)
    private String reportOrgCode;


    @Comment("报告人联系电话")
    @Column(name = "report_tel", nullable = true, length = 24)
    private String reportTel;


    @Comment("报告时间")
    @Column(name = "report_date", nullable = true)
    private LocalDateTime reportDate;


    @Comment("打回人")
    @Column(name = "back_user", nullable = true, length = 16)
    private String backUser;


    @Comment("打回原因")
    @Column(name = "back_remarks", nullable = true)
    private String backRemarks;


    @Comment("打回时间")
    @Column(name = "back_date", nullable = true)
    private LocalDateTime backDate;


    @Comment("发回人")
    @Column(name = "cancel_user", nullable = true, length = 16)
    private String cancelUser;


    @Comment("发回原因")
    @Column(name = "cancel_remarks", nullable = true)
    private String cancelRemarks;


    @Comment("发回时间")
    @Column(name = "cancel_date", nullable = true)
    private LocalDateTime cancelDate;


    @Comment("医院编码")
    @Column(name = "hospital_code", nullable = true, length = 16)
    private String hospitalCode;

    @Enumerated(EnumType.STRING)
    @Comment("状态:AdvEventsStatusEnum")
    @Column(name = "status_code", nullable = true, length = 10)
    private AdvEventsStatusEnum statusCode;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    public static Optional<CisAdvEventReport> getCisAdvEventReportById(String id) {
        return dao().findById(id);
    }

    public static List<CisAdvEventReport> getCisAdvEventReports(CisAdvEventReportQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisAdvEventReport> getCisAdvEventReportPage(CisAdvEventReportQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisAdvEventReport> getSpecification(CisAdvEventReportQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getPatType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patType"), qto.getPatType()));
            }
            if (StringUtils.isNotBlank(qto.getInpatientCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getPatName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patName"), qto.getPatName()));
            }
            if (StringUtils.isNotBlank(qto.getSex())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
            }
            if (qto.getBirthDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
            }
            if (StringUtils.isNotBlank(qto.getWork())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("work"), qto.getWork()));
            }
            if (StringUtils.isNotBlank(qto.getAreaCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaCode"), qto.getAreaCode()));
            }
            if (StringUtils.isNotBlank(qto.getBedName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("bedName"), qto.getBedName()));
            }
            if (qto.getEventDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("eventDate"), LocalDateUtil.beginOfDay(qto.getEventDate()), LocalDateUtil.endOfDay(qto.getEventDate())));
            }
            if (StringUtils.isNotBlank(qto.getClinicalDiagnosis())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("clinicalDiagnosis"), qto.getClinicalDiagnosis()));
            }
            if (StringUtils.isNotBlank(qto.getEventPlace())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventPlace"), qto.getEventPlace()));
            }
            if (StringUtils.isNotBlank(qto.getOtherEventPlace())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherEventPlace"), qto.getOtherEventPlace()));
            }
            if (StringUtils.isNotBlank(qto.getAdvConsequencesFlag())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("advConsequencesFlag"), qto.getAdvConsequencesFlag()));
            }
            if (StringUtils.isNotBlank(qto.getAdvConsequences())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("advConsequences"), qto.getAdvConsequences()));
            }
            if (StringUtils.isNotBlank(qto.getEventAfter())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventAfter"), qto.getEventAfter()));
            }
            if (StringUtils.isNotBlank(qto.getEventType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventType"), qto.getEventType()));
            }
            if (StringUtils.isNotBlank(qto.getEventCard())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventCard"), qto.getEventCard()));
            }
            if (qto.getAdditionFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("additionFlag"), qto.getAdditionFlag()));
            }
            if (StringUtils.isNotBlank(qto.getEventHandle())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventHandle"), qto.getEventHandle()));
            }
            if (StringUtils.isNotBlank(qto.getEventLevel())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventLevel"), qto.getEventLevel()));
            }
            if (StringUtils.isNotBlank(qto.getEventWhy())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventWhy"), qto.getEventWhy()));
            }
            if (StringUtils.isNotBlank(qto.getEvaluationUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("evaluationUser"), qto.getEvaluationUser()));
            }
            if (qto.getEvaluationDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("evaluationDate"), LocalDateUtil.beginOfDay(qto.getEvaluationDate()), LocalDateUtil.endOfDay(qto.getEvaluationDate())));
            }
            if (StringUtils.isNotBlank(qto.getImprovementMeasures())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("improvementMeasures"), qto.getImprovementMeasures()));
            }
            if (StringUtils.isNotBlank(qto.getImprovementUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("improvementUser"), qto.getImprovementUser()));
            }
            if (qto.getImprovementDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("improvementDate"), LocalDateUtil.beginOfDay(qto.getImprovementDate()), LocalDateUtil.endOfDay(qto.getImprovementDate())));
            }
            if (StringUtils.isNotBlank(qto.getReceiveOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("receiveOrgCode"), qto.getReceiveOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getOpinionState())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("opinionState"), qto.getOpinionState()));
            }
            if (StringUtils.isNotBlank(qto.getOpinionStateUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("opinionStateUser"), qto.getOpinionStateUser()));
            }
            if (qto.getOpinionStateDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("opinionStateDate"), LocalDateUtil.beginOfDay(qto.getOpinionStateDate()), LocalDateUtil.endOfDay(qto.getOpinionStateDate())));
            }
            if (StringUtils.isNotBlank(qto.getReportUserType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUserType"), qto.getReportUserType()));
            }
            if (StringUtils.isNotBlank(qto.getLitigantType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("litigantType"), qto.getLitigantType()));
            }
            if (StringUtils.isNotBlank(qto.getTitle())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("title"), qto.getTitle()));
            }
            if (StringUtils.isNotBlank(qto.getDutyUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("dutyUser"), qto.getDutyUser()));
            }
            if (StringUtils.isNotBlank(qto.getDutyTitle())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("dutyTitle"), qto.getDutyTitle()));
            }
            if (StringUtils.isNotBlank(qto.getWorkingLife())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("workingLife"), qto.getWorkingLife()));
            }
            if (StringUtils.isNotBlank(qto.getDutyNurseLevel())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("dutyNurseLevel"), qto.getDutyNurseLevel()));
            }
            if (StringUtils.isNotBlank(qto.getDutyUser2())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("dutyUser2"), qto.getDutyUser2()));
            }
            if (StringUtils.isNotBlank(qto.getDutyTitle2())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("dutyTitle2"), qto.getDutyTitle2()));
            }
            if (StringUtils.isNotBlank(qto.getWorkingLife2())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("workingLife2"), qto.getWorkingLife2()));
            }
            if (StringUtils.isNotBlank(qto.getDutyNurseLevel2())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("dutyNurseLevel2"), qto.getDutyNurseLevel2()));
            }
            if (StringUtils.isNotBlank(qto.getReportUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUser"), qto.getReportUser()));
            }
            if (StringUtils.isNotBlank(qto.getReportOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportOrgCode"), qto.getReportOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getReportTel())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportTel"), qto.getReportTel()));
            }
            if (qto.getReportDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("reportDate"), LocalDateUtil.beginOfDay(qto.getReportDate()), LocalDateUtil.endOfDay(qto.getReportDate())));
            }
            if (StringUtils.isNotBlank(qto.getBackUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("backUser"), qto.getBackUser()));
            }
            if (StringUtils.isNotBlank(qto.getBackRemarks())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("backRemarks"), qto.getBackRemarks()));
            }
            if (qto.getBackDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("backDate"), LocalDateUtil.beginOfDay(qto.getBackDate()), LocalDateUtil.endOfDay(qto.getBackDate())));
            }
            if (StringUtils.isNotBlank(qto.getCancelUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cancelUser"), qto.getCancelUser()));
            }
            if (StringUtils.isNotBlank(qto.getCancelRemarks())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cancelRemarks"), qto.getCancelRemarks()));
            }
            if (qto.getCancelDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("cancelDate"), LocalDateUtil.beginOfDay(qto.getCancelDate()), LocalDateUtil.endOfDay(qto.getCancelDate())));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            return predicate;
        };
    }

    private static CisAdvEventReportRepository dao() {
        return SpringUtil.getBean(CisAdvEventReportRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    protected void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getPatType() {
        return patType;
    }

    protected void setPatType(String patType) {
        this.patType = patType;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatName() {
        return patName;
    }

    protected void setPatName(String patName) {
        this.patName = patName;
    }

    public String getSex() {
        return sex;
    }

    protected void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getWork() {
        return work;
    }

    protected void setWork(String work) {
        this.work = work;
    }

    public String getAreaCode() {
        return areaCode;
    }

    protected void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getBedName() {
        return bedName;
    }

    protected void setBedName(String bedName) {
        this.bedName = bedName;
    }

    public LocalDateTime getEventDate() {
        return eventDate;
    }

    protected void setEventDate(LocalDateTime eventDate) {
        this.eventDate = eventDate;
    }

    public String getClinicalDiagnosis() {
        return clinicalDiagnosis;
    }

    protected void setClinicalDiagnosis(String clinicalDiagnosis) {
        this.clinicalDiagnosis = clinicalDiagnosis;
    }

    public String getEventPlace() {
        return eventPlace;
    }

    protected void setEventPlace(String eventPlace) {
        this.eventPlace = eventPlace;
    }

    public String getOtherEventPlace() {
        return otherEventPlace;
    }

    protected void setOtherEventPlace(String otherEventPlace) {
        this.otherEventPlace = otherEventPlace;
    }

    public String getAdvConsequencesFlag() {
        return advConsequencesFlag;
    }

    protected void setAdvConsequencesFlag(String advConsequencesFlag) {
        this.advConsequencesFlag = advConsequencesFlag;
    }

    public String getAdvConsequences() {
        return advConsequences;
    }

    protected void setAdvConsequences(String advConsequences) {
        this.advConsequences = advConsequences;
    }

    public String getEventAfter() {
        return eventAfter;
    }

    protected void setEventAfter(String eventAfter) {
        this.eventAfter = eventAfter;
    }

    public String getEventType() {
        return eventType;
    }

    protected void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventCard() {
        return eventCard;
    }

    protected void setEventCard(String eventCard) {
        this.eventCard = eventCard;
    }

    public boolean isAdditionFlag() {
        return additionFlag;
    }

    protected void setAdditionFlag(boolean additionFlag) {
        this.additionFlag = additionFlag;
    }

    public String getEventHandle() {
        return eventHandle;
    }

    protected void setEventHandle(String eventHandle) {
        this.eventHandle = eventHandle;
    }

    public String getEventLevel() {
        return eventLevel;
    }

    protected void setEventLevel(String eventLevel) {
        this.eventLevel = eventLevel;
    }

    public String getEventWhy() {
        return eventWhy;
    }

    protected void setEventWhy(String eventWhy) {
        this.eventWhy = eventWhy;
    }

    public String getEvaluationUser() {
        return evaluationUser;
    }

    protected void setEvaluationUser(String evaluationUser) {
        this.evaluationUser = evaluationUser;
    }

    public LocalDateTime getEvaluationDate() {
        return evaluationDate;
    }

    protected void setEvaluationDate(LocalDateTime evaluationDate) {
        this.evaluationDate = evaluationDate;
    }

    public String getImprovementMeasures() {
        return improvementMeasures;
    }

    protected void setImprovementMeasures(String improvementMeasures) {
        this.improvementMeasures = improvementMeasures;
    }

    public String getImprovementUser() {
        return improvementUser;
    }

    protected void setImprovementUser(String improvementUser) {
        this.improvementUser = improvementUser;
    }

    public LocalDateTime getImprovementDate() {
        return improvementDate;
    }

    protected void setImprovementDate(LocalDateTime improvementDate) {
        this.improvementDate = improvementDate;
    }

    public String getReceiveOrgCode() {
        return receiveOrgCode;
    }

    protected void setReceiveOrgCode(String receiveOrgCode) {
        this.receiveOrgCode = receiveOrgCode;
    }

    public String getOpinionState() {
        return opinionState;
    }

    protected void setOpinionState(String opinionState) {
        this.opinionState = opinionState;
    }

    public String getOpinionStateUser() {
        return opinionStateUser;
    }

    protected void setOpinionStateUser(String opinionStateUser) {
        this.opinionStateUser = opinionStateUser;
    }

    public LocalDateTime getOpinionStateDate() {
        return opinionStateDate;
    }

    protected void setOpinionStateDate(LocalDateTime opinionStateDate) {
        this.opinionStateDate = opinionStateDate;
    }

    public String getReportUserType() {
        return reportUserType;
    }

    protected void setReportUserType(String reportUserType) {
        this.reportUserType = reportUserType;
    }

    public String getLitigantType() {
        return litigantType;
    }

    protected void setLitigantType(String litigantType) {
        this.litigantType = litigantType;
    }

    public String getTitle() {
        return title;
    }

    protected void setTitle(String title) {
        this.title = title;
    }

    public String getDutyUser() {
        return dutyUser;
    }

    protected void setDutyUser(String dutyUser) {
        this.dutyUser = dutyUser;
    }

    public String getDutyTitle() {
        return dutyTitle;
    }

    protected void setDutyTitle(String dutyTitle) {
        this.dutyTitle = dutyTitle;
    }

    public String getWorkingLife() {
        return workingLife;
    }

    protected void setWorkingLife(String workingLife) {
        this.workingLife = workingLife;
    }

    public String getDutyNurseLevel() {
        return dutyNurseLevel;
    }

    protected void setDutyNurseLevel(String dutyNurseLevel) {
        this.dutyNurseLevel = dutyNurseLevel;
    }

    public String getDutyUser2() {
        return dutyUser2;
    }

    protected void setDutyUser2(String dutyUser2) {
        this.dutyUser2 = dutyUser2;
    }

    public String getDutyTitle2() {
        return dutyTitle2;
    }

    protected void setDutyTitle2(String dutyTitle2) {
        this.dutyTitle2 = dutyTitle2;
    }

    public String getWorkingLife2() {
        return workingLife2;
    }

    protected void setWorkingLife2(String workingLife2) {
        this.workingLife2 = workingLife2;
    }

    public String getDutyNurseLevel2() {
        return dutyNurseLevel2;
    }

    protected void setDutyNurseLevel2(String dutyNurseLevel2) {
        this.dutyNurseLevel2 = dutyNurseLevel2;
    }

    public String getReportUser() {
        return reportUser;
    }

    protected void setReportUser(String reportUser) {
        this.reportUser = reportUser;
    }

    public String getReportOrgCode() {
        return reportOrgCode;
    }

    protected void setReportOrgCode(String reportOrgCode) {
        this.reportOrgCode = reportOrgCode;
    }

    public String getReportTel() {
        return reportTel;
    }

    protected void setReportTel(String reportTel) {
        this.reportTel = reportTel;
    }

    public LocalDateTime getReportDate() {
        return reportDate;
    }

    protected void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    public String getBackUser() {
        return backUser;
    }

    protected void setBackUser(String backUser) {
        this.backUser = backUser;
    }

    public String getBackRemarks() {
        return backRemarks;
    }

    protected void setBackRemarks(String backRemarks) {
        this.backRemarks = backRemarks;
    }

    public LocalDateTime getBackDate() {
        return backDate;
    }

    protected void setBackDate(LocalDateTime backDate) {
        this.backDate = backDate;
    }

    public String getCancelUser() {
        return cancelUser;
    }

    protected void setCancelUser(String cancelUser) {
        this.cancelUser = cancelUser;
    }

    public String getCancelRemarks() {
        return cancelRemarks;
    }

    protected void setCancelRemarks(String cancelRemarks) {
        this.cancelRemarks = cancelRemarks;
    }

    public LocalDateTime getCancelDate() {
        return cancelDate;
    }

    protected void setCancelDate(LocalDateTime cancelDate) {
        this.cancelDate = cancelDate;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    protected void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public AdvEventsStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(AdvEventsStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getImprovementUserName() {
        return improvementUserName;
    }

    public void setImprovementUserName(String improvementUserName) {
        this.improvementUserName = improvementUserName;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public String getOpinionStateUserName() {
        return opinionStateUserName;
    }

    public void setOpinionStateUserName(String opinionStateUserName) {
        this.opinionStateUserName = opinionStateUserName;
    }

    public String getReportUserTypeName() {
        return reportUserTypeName;
    }

    public void setReportUserTypeName(String reportUserTypeName) {
        this.reportUserTypeName = reportUserTypeName;
    }

    public String getTitleName() {
        return titleName;
    }

    public void setTitleName(String titleName) {
        this.titleName = titleName;
    }

    public String getDutyTitleName() {
        return dutyTitleName;
    }

    public void setDutyTitleName(String dutyTitleName) {
        this.dutyTitleName = dutyTitleName;
    }

    public String getWorkingLifeName() {
        return workingLifeName;
    }

    public void setWorkingLifeName(String workingLifeName) {
        this.workingLifeName = workingLifeName;
    }

    public String getDutyTitle2Name() {
        return dutyTitle2Name;
    }

    public void setDutyTitle2Name(String dutyTitle2Name) {
        this.dutyTitle2Name = dutyTitle2Name;
    }

    public String getWorkingLife2Name() {
        return workingLife2Name;
    }

    public void setWorkingLife2Name(String workingLife2Name) {
        this.workingLife2Name = workingLife2Name;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisAdvEventReport other = (CisAdvEventReport) obj;
        return Objects.equals(id, other.id);
    }

    public CisAdvEventReport create(CisAdvEventReportNto cisAdvEventReportNto) {
        BusinessAssert.notNull(cisAdvEventReportNto, CisAdvBusinessErrorEnum.BUS_CIS_ADV_0001, "参数cisAdvEventReportNto");

        setId(cisAdvEventReportNto.getId());
        setPatMiCode(cisAdvEventReportNto.getPatMiCode());
        setPatType(cisAdvEventReportNto.getPatType());
        setInpatientCode(cisAdvEventReportNto.getInpatientCode());
        setVisitCode(cisAdvEventReportNto.getVisitCode());
        setPatName(cisAdvEventReportNto.getPatName());
        setSex(cisAdvEventReportNto.getSex());
        setBirthDate(cisAdvEventReportNto.getBirthDate());
        setWork(cisAdvEventReportNto.getWork());
        setAreaCode(cisAdvEventReportNto.getAreaCode());
        setAreaName(cisAdvEventReportNto.getAreaName());
        setBedName(cisAdvEventReportNto.getBedName());
        setEventDate(cisAdvEventReportNto.getEventDate());
        setClinicalDiagnosis(cisAdvEventReportNto.getClinicalDiagnosis());
        setEventPlace(cisAdvEventReportNto.getEventPlace());
        setOtherEventPlace(cisAdvEventReportNto.getOtherEventPlace());
        setAdvConsequencesFlag(cisAdvEventReportNto.getAdvConsequencesFlag());
        setAdvConsequences(cisAdvEventReportNto.getAdvConsequences());
        setEventAfter(cisAdvEventReportNto.getEventAfter());
        setEventType(cisAdvEventReportNto.getEventType());
        setEventCard(cisAdvEventReportNto.getEventCard());
        setAdditionFlag(cisAdvEventReportNto.isAdditionFlag());
        setEventHandle(cisAdvEventReportNto.getEventHandle());
        setEventLevel(cisAdvEventReportNto.getEventLevel());
        setEventWhy(cisAdvEventReportNto.getEventWhy());
        setEvaluationUser(cisAdvEventReportNto.getEvaluationUser());
        setEvaluationDate(cisAdvEventReportNto.getEvaluationDate());
        setImprovementMeasures(cisAdvEventReportNto.getImprovementMeasures());
        setImprovementUser(cisAdvEventReportNto.getImprovementUser());
        setImprovementUserName(cisAdvEventReportNto.getImprovementUserName());
        setImprovementDate(cisAdvEventReportNto.getImprovementDate());
        setReceiveOrgCode(cisAdvEventReportNto.getReceiveOrgCode());
        setReceiveOrgName(cisAdvEventReportNto.getReceiveOrgName());
        setOpinionState(cisAdvEventReportNto.getOpinionState());
        setOpinionStateUser(cisAdvEventReportNto.getOpinionStateUser());
        setOpinionStateUserName(cisAdvEventReportNto.getOpinionStateUserName());
        setOpinionStateDate(cisAdvEventReportNto.getOpinionStateDate());
        setReportUserType(cisAdvEventReportNto.getReportUserType());
        setReportUserTypeName(cisAdvEventReportNto.getReportUserTypeName());
        setLitigantType(cisAdvEventReportNto.getLitigantType());
        setTitle(cisAdvEventReportNto.getTitle());
        setTitleName(cisAdvEventReportNto.getTitleName());
        setDutyUser(cisAdvEventReportNto.getDutyUser());
        setDutyTitle(cisAdvEventReportNto.getDutyTitle());
        setDutyTitleName(cisAdvEventReportNto.getDutyTitleName());
        setWorkingLife(cisAdvEventReportNto.getWorkingLife());
        setWorkingLifeName(cisAdvEventReportNto.getWorkingLifeName());
        setDutyNurseLevel(cisAdvEventReportNto.getDutyNurseLevel());
        setDutyUser2(cisAdvEventReportNto.getDutyUser2());
        setDutyTitle2(cisAdvEventReportNto.getDutyTitle2());
        setDutyTitle2Name(cisAdvEventReportNto.getDutyTitle2Name());
        setWorkingLife2(cisAdvEventReportNto.getWorkingLife2());
        setWorkingLife2Name(cisAdvEventReportNto.getWorkingLife2Name());
        setDutyNurseLevel2(cisAdvEventReportNto.getDutyNurseLevel2());
        setReportUser(cisAdvEventReportNto.getReportUser());
        setReportOrgCode(cisAdvEventReportNto.getReportOrgCode());
        setReportTel(cisAdvEventReportNto.getReportTel());
        setReportDate(cisAdvEventReportNto.getReportDate());
        setBackUser(cisAdvEventReportNto.getBackUser());
        setBackRemarks(cisAdvEventReportNto.getBackRemarks());
        setBackDate(cisAdvEventReportNto.getBackDate());
        setCancelUser(cisAdvEventReportNto.getCancelUser());
        setCancelRemarks(cisAdvEventReportNto.getCancelRemarks());
        setCancelDate(cisAdvEventReportNto.getCancelDate());
        setHospitalCode(cisAdvEventReportNto.getHospitalCode());
        setStatusCode(cisAdvEventReportNto.getStatusCode());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventReportEto cisAdvEventReportEto) {
        setPatMiCode(cisAdvEventReportEto.getPatMiCode());
        setPatType(cisAdvEventReportEto.getPatType());
        setInpatientCode(cisAdvEventReportEto.getInpatientCode());
        setVisitCode(cisAdvEventReportEto.getVisitCode());
        setPatName(cisAdvEventReportEto.getPatName());
        setSex(cisAdvEventReportEto.getSex());
        setBirthDate(cisAdvEventReportEto.getBirthDate());
        setWork(cisAdvEventReportEto.getWork());
        setAreaCode(cisAdvEventReportEto.getAreaCode());
        setAreaName(cisAdvEventReportEto.getAreaName());
        setBedName(cisAdvEventReportEto.getBedName());
        setEventDate(cisAdvEventReportEto.getEventDate());
        setClinicalDiagnosis(cisAdvEventReportEto.getClinicalDiagnosis());
        setEventPlace(cisAdvEventReportEto.getEventPlace());
        setOtherEventPlace(cisAdvEventReportEto.getOtherEventPlace());
        setAdvConsequencesFlag(cisAdvEventReportEto.getAdvConsequencesFlag());
        setAdvConsequences(cisAdvEventReportEto.getAdvConsequences());
        setEventAfter(cisAdvEventReportEto.getEventAfter());
        setEventType(cisAdvEventReportEto.getEventType());
        setEventCard(cisAdvEventReportEto.getEventCard());
        setAdditionFlag(cisAdvEventReportEto.isAdditionFlag());
        setEventHandle(cisAdvEventReportEto.getEventHandle());
        setEventLevel(cisAdvEventReportEto.getEventLevel());
        setEventWhy(cisAdvEventReportEto.getEventWhy());
        setEvaluationUser(cisAdvEventReportEto.getEvaluationUser());
        setEvaluationDate(cisAdvEventReportEto.getEvaluationDate());
        setImprovementMeasures(cisAdvEventReportEto.getImprovementMeasures());
        setImprovementUser(cisAdvEventReportEto.getImprovementUser());
        setImprovementUserName(cisAdvEventReportEto.getImprovementUserName());
        setImprovementDate(cisAdvEventReportEto.getImprovementDate());
        setReceiveOrgCode(cisAdvEventReportEto.getReceiveOrgCode());
        setReceiveOrgName(cisAdvEventReportEto.getReceiveOrgName());
        setOpinionState(cisAdvEventReportEto.getOpinionState());
        setOpinionStateUser(cisAdvEventReportEto.getOpinionStateUser());
        setOpinionStateUserName(cisAdvEventReportEto.getOpinionStateUserName());
        setOpinionStateDate(cisAdvEventReportEto.getOpinionStateDate());
        setReportUserType(cisAdvEventReportEto.getReportUserType());
        setReportUserTypeName(cisAdvEventReportEto.getReportUserTypeName());
        setLitigantType(cisAdvEventReportEto.getLitigantType());
        setTitle(cisAdvEventReportEto.getTitle());
        setTitleName(cisAdvEventReportEto.getTitleName());
        setDutyUser(cisAdvEventReportEto.getDutyUser());
        setDutyTitle(cisAdvEventReportEto.getDutyTitle());
        setDutyTitleName(cisAdvEventReportEto.getDutyTitleName());
        setWorkingLife(cisAdvEventReportEto.getWorkingLife());
        setWorkingLifeName(cisAdvEventReportEto.getWorkingLifeName());
        setDutyNurseLevel(cisAdvEventReportEto.getDutyNurseLevel());
        setDutyUser2(cisAdvEventReportEto.getDutyUser2());
        setDutyTitle2(cisAdvEventReportEto.getDutyTitle2());
        setDutyTitle2Name(cisAdvEventReportEto.getDutyTitle2Name());
        setWorkingLife2(cisAdvEventReportEto.getWorkingLife2());
        setWorkingLife2Name(cisAdvEventReportEto.getWorkingLife2Name());
        setDutyNurseLevel2(cisAdvEventReportEto.getDutyNurseLevel2());
        setReportUser(cisAdvEventReportEto.getReportUser());
        setReportOrgCode(cisAdvEventReportEto.getReportOrgCode());
        setReportTel(cisAdvEventReportEto.getReportTel());
        setReportDate(cisAdvEventReportEto.getReportDate());
        setBackUser(cisAdvEventReportEto.getBackUser());
        setBackRemarks(cisAdvEventReportEto.getBackRemarks());
        setBackDate(cisAdvEventReportEto.getBackDate());
        setCancelUser(cisAdvEventReportEto.getCancelUser());
        setCancelRemarks(cisAdvEventReportEto.getCancelRemarks());
        setCancelDate(cisAdvEventReportEto.getCancelDate());
        setHospitalCode(cisAdvEventReportEto.getHospitalCode());
        setStatusCode(cisAdvEventReportEto.getStatusCode());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

}
