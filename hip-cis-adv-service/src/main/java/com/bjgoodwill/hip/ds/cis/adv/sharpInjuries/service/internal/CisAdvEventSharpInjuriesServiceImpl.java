package com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.entity.CisAdvEventSharpInjuries;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.service.CisAdvEventSharpInjuriesService;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.service.internal.assembler.CisAdvEventSharpInjuriesAssembler;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.to.CisAdvEventSharpInjuriesEto;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.to.CisAdvEventSharpInjuriesNto;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.to.CisAdvEventSharpInjuriesQto;
import com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.to.CisAdvEventSharpInjuriesTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.service.CisAdvEventSharpInjuriesService")
@RequestMapping(value = "/api/cisadv/sharpInjuries/cisAdvEventSharpInjuries", produces = "application/json; charset=utf-8")
public class CisAdvEventSharpInjuriesServiceImpl implements CisAdvEventSharpInjuriesService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventSharpInjuriesTo> getCisAdvEventSharpInjurieses(CisAdvEventSharpInjuriesQto cisAdvEventSharpInjuriesQto) {
        return CisAdvEventSharpInjuriesAssembler.toTos(CisAdvEventSharpInjuries.getCisAdvEventSharpInjurieses(cisAdvEventSharpInjuriesQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventSharpInjuriesTo> getCisAdvEventSharpInjuriesPage(CisAdvEventSharpInjuriesQto cisAdvEventSharpInjuriesQto) {
        Page<CisAdvEventSharpInjuries> page = CisAdvEventSharpInjuries.getCisAdvEventSharpInjuriesPage(cisAdvEventSharpInjuriesQto);
        Page<CisAdvEventSharpInjuriesTo> result = page.map(CisAdvEventSharpInjuriesAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventSharpInjuriesTo getCisAdvEventSharpInjuriesById(String id) {
        return CisAdvEventSharpInjuriesAssembler.toTo(CisAdvEventSharpInjuries.getCisAdvEventSharpInjuriesById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventSharpInjuriesTo createCisAdvEventSharpInjuries(CisAdvEventSharpInjuriesNto cisAdvEventSharpInjuriesNto) {
        CisAdvEventSharpInjuries cisAdvEventSharpInjuries = new CisAdvEventSharpInjuries();
        cisAdvEventSharpInjuries = cisAdvEventSharpInjuries.create(cisAdvEventSharpInjuriesNto);
        return CisAdvEventSharpInjuriesAssembler.toTo(cisAdvEventSharpInjuries);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventSharpInjuries(String id, CisAdvEventSharpInjuriesEto cisAdvEventSharpInjuriesEto) {
        Optional<CisAdvEventSharpInjuries> cisAdvEventSharpInjuriesOptional = CisAdvEventSharpInjuries.getCisAdvEventSharpInjuriesById(id);
        cisAdvEventSharpInjuriesOptional.ifPresent(cisAdvEventSharpInjuries -> cisAdvEventSharpInjuries.update(cisAdvEventSharpInjuriesEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventSharpInjuries(String id) {
        Optional<CisAdvEventSharpInjuries> cisAdvEventSharpInjuriesOptional = CisAdvEventSharpInjuries.getCisAdvEventSharpInjuriesById(id);
        cisAdvEventSharpInjuriesOptional.ifPresent(cisAdvEventSharpInjuries -> cisAdvEventSharpInjuries.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}