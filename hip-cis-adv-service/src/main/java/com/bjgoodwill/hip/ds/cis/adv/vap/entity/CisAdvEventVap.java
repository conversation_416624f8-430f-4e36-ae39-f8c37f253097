package com.bjgoodwill.hip.ds.cis.adv.vap.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.vap.repository.CisAdvEventVapRepository;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapEto;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapNto;
import com.bjgoodwill.hip.ds.cis.adv.vap.to.CisAdvEventVapQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "呼吸机相关肺炎（VAP）相关信息收集")
@Table(name = "cis_adv_event_vap", indexes = {}, uniqueConstraints = {})
public class CisAdvEventVap {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件id")
    @Column(name = "event_report_id", nullable = true, length = 50)
    private String eventReportId;


    @Comment("患者类型")
    @Column(name = "pat_type", nullable = true, length = 2)
    private String patType;


    @Comment("住院号(门诊就诊卡号)")
    @Column(name = "inpatient_code", nullable = true, length = 16)
    private String inpatientCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true, length = 16)
    private String visitCode;


    @Comment("患者姓名")
    @Column(name = "pat_name", nullable = true, length = 64)
    private String patName;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("年龄范围: 新生儿、1-6月、7-12月、1-6岁、7-12岁、13-18岁、19-64岁、65岁及以上、无法确定")
    @Column(name = "age_range", nullable = true, length = 64)
    private String ageRange;


    @Comment("病区科室")
    @Column(name = "area_code", nullable = true, length = 16)
    private String areaCode;


    @Comment("病区名称")
    @Column(name = "area_name", nullable = true, length = 64)
    private String areaName;


    @Comment("入院时间")
    @Column(name = "in_date", nullable = true)
    private LocalDateTime inDate;


    @Comment("人工气道类型：气管插管endotrachealintubation、气管切开tracheotomy")
    @Column(name = "artificial_type", nullable = true, length = 128)
    private String artificialType;


    @Comment("人工气道类型名称：气管插管endotrachealintubation、气管切开tracheotomy")
    @Column(name = "artificial_type_name", nullable = true, length = 128)
    private String artificialTypeName;


    @Comment("导管类型：普通general、声门下吸引型导subglottic")
    @Column(name = "extubation_type", nullable = true, length = 64)
    private String extubationType;


    @Comment("导管类型名称：普通general、声门下吸引型导subglottic")
    @Column(name = "extubation_type_name", nullable = true, length = 128)
    private String extubationTypeName;


    @Comment("湿化装置：呼吸机加温加湿ventilator、人工鼻湿化artificial、生理盐水滴注salineinfusion、其他other")
    @Column(name = "humidify_device", nullable = true, length = 128)
    private String humidifyDevice;


    @Comment("湿化装置名称：呼吸机加温加湿ventilator、人工鼻湿化artificial、生理盐水滴注salineinfusion、其他other")
    @Column(name = "humidify_device_name", nullable = true, length = 128)
    private String humidifyDeviceName;


    @Comment("吸痰方式：密闭式吸痰closed、开放式吸痰open")
    @Column(name = "sputum_type", nullable = true, length = 64)
    private String sputumType;


    @Comment("吸痰方式名称：密闭式吸痰closed、开放式吸痰open")
    @Column(name = "sputum_type_name", nullable = true, length = 64)
    private String sputumTypeName;


    @Comment("口腔护理方式：擦拭wipe、擦拭+冲洗wipeandrinse 、刷牙brushteeth")
    @Column(name = "oral_care_type", nullable = true, length = 64)
    private String oralCareType;


    @Comment("口腔护理方式名称：擦拭wipe、擦拭+冲洗wipeandrinse 、刷牙brushteeth")
    @Column(name = "oral_care_type_name", nullable = true, length = 64)
    private String oralCareTypeName;


    @Comment("每天口腔护理次数：次")
    @Column(name = "oral_care_times", nullable = true)
    private Integer oralCareTimes;


    @Comment("口腔护理液选择：生理盐水normalsaline、含洗必泰includingchlorhexidine、腔护理液cavitynursing、牙膏toothpaste、其他other")
    @Column(name = "oral_nursing", nullable = true, length = 128)
    private String oralNursing;


    @Comment("口腔护理液选择名称：生理盐水normalsaline、含洗必泰includingchlorhexidine、腔护理液cavitynursing、牙膏toothpaste、其他other")
    @Column(name = "oral_nursing_name", nullable = true, length = 128)
    private String oralNursingName;


    @Comment("其他选择")
    @Column(name = "other_nursing", nullable = true)
    private String otherNursing;


    @Comment("经人工气道通气的同时，是否有经鼻胃管肠内营养： 0否、1是")
    @Column(name = "nasogastic_flag", nullable = false)
    private boolean nasogasticFlag;


    @Comment("发生vap时，经人工气道机械通气时长:天")
    @Column(name = "vap_time", nullable = true)
    private Integer vapTime;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    public static Optional<CisAdvEventVap> getCisAdvEventVapById(String id) {
        return dao().findById(id);
    }

    public static List<CisAdvEventVap> getCisAdvEventVaps(CisAdvEventVapQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisAdvEventVap> getCisAdvEventVapPage(CisAdvEventVapQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisAdvEventVap> getSpecification(CisAdvEventVapQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getEventReportId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReportId"), qto.getEventReportId()));
            }
            if (StringUtils.isNotBlank(qto.getPatType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patType"), qto.getPatType()));
            }
            if (StringUtils.isNotBlank(qto.getInpatientCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getPatName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patName"), qto.getPatName()));
            }
            if (StringUtils.isNotBlank(qto.getSex())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
            }
            if (qto.getBirthDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
            }
            if (StringUtils.isNotBlank(qto.getAgeRange())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("ageRange"), qto.getAgeRange()));
            }
            if (StringUtils.isNotBlank(qto.getAreaCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaCode"), qto.getAreaCode()));
            }
            if (StringUtils.isNotBlank(qto.getAreaName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaName"), qto.getAreaName()));
            }
            if (qto.getInDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("inDate"), LocalDateUtil.beginOfDay(qto.getInDate()), LocalDateUtil.endOfDay(qto.getInDate())));
            }
            if (StringUtils.isNotBlank(qto.getArtificialType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("artificialType"), qto.getArtificialType()));
            }
            if (StringUtils.isNotBlank(qto.getArtificialTypeName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("artificialTypeName"), qto.getArtificialTypeName()));
            }
            if (StringUtils.isNotBlank(qto.getExtubationType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("extubationType"), qto.getExtubationType()));
            }
            if (StringUtils.isNotBlank(qto.getExtubationTypeName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("extubationTypeName"), qto.getExtubationTypeName()));
            }
            if (StringUtils.isNotBlank(qto.getHumidifyDevice())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("humidifyDevice"), qto.getHumidifyDevice()));
            }
            if (StringUtils.isNotBlank(qto.getHumidifyDeviceName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("humidifyDeviceName"), qto.getHumidifyDeviceName()));
            }
            if (StringUtils.isNotBlank(qto.getSputumType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sputumType"), qto.getSputumType()));
            }
            if (StringUtils.isNotBlank(qto.getSputumTypeName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sputumTypeName"), qto.getSputumTypeName()));
            }
            if (StringUtils.isNotBlank(qto.getOralCareType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("oralCareType"), qto.getOralCareType()));
            }
            if (StringUtils.isNotBlank(qto.getOralCareTypeName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("oralCareTypeName"), qto.getOralCareTypeName()));
            }
            if (qto.getOralCareTimes() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("oralCareTimes"), qto.getOralCareTimes()));
            }
            if (StringUtils.isNotBlank(qto.getOralNursing())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("oralNursing"), qto.getOralNursing()));
            }
            if (StringUtils.isNotBlank(qto.getOralNursingName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("oralNursingName"), qto.getOralNursingName()));
            }
            if (StringUtils.isNotBlank(qto.getOtherNursing())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherNursing"), qto.getOtherNursing()));
            }
            if (qto.getNasogasticFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nasogasticFlag"), qto.getNasogasticFlag()));
            }
            if (qto.getVapTime() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("vapTime"), qto.getVapTime()));
            }
            return predicate;
        };
    }

    private static CisAdvEventVapRepository dao() {
        return SpringUtil.getBean(CisAdvEventVapRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getEventReportId() {
        return eventReportId;
    }

    protected void setEventReportId(String eventReportId) {
        this.eventReportId = eventReportId;
    }

    public String getPatType() {
        return patType;
    }

    protected void setPatType(String patType) {
        this.patType = patType;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatName() {
        return patName;
    }

    protected void setPatName(String patName) {
        this.patName = patName;
    }

    public String getSex() {
        return sex;
    }

    protected void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getAgeRange() {
        return ageRange;
    }

    protected void setAgeRange(String ageRange) {
        this.ageRange = ageRange;
    }

    public String getAreaCode() {
        return areaCode;
    }

    protected void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    protected void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public LocalDateTime getInDate() {
        return inDate;
    }

    protected void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    public String getArtificialType() {
        return artificialType;
    }

    protected void setArtificialType(String artificialType) {
        this.artificialType = artificialType;
    }

    public String getArtificialTypeName() {
        return artificialTypeName;
    }

    protected void setArtificialTypeName(String artificialTypeName) {
        this.artificialTypeName = artificialTypeName;
    }

    public String getExtubationType() {
        return extubationType;
    }

    protected void setExtubationType(String extubationType) {
        this.extubationType = extubationType;
    }

    public String getExtubationTypeName() {
        return extubationTypeName;
    }

    protected void setExtubationTypeName(String extubationTypeName) {
        this.extubationTypeName = extubationTypeName;
    }

    public String getHumidifyDevice() {
        return humidifyDevice;
    }

    protected void setHumidifyDevice(String humidifyDevice) {
        this.humidifyDevice = humidifyDevice;
    }

    public String getHumidifyDeviceName() {
        return humidifyDeviceName;
    }

    protected void setHumidifyDeviceName(String humidifyDeviceName) {
        this.humidifyDeviceName = humidifyDeviceName;
    }

    public String getSputumType() {
        return sputumType;
    }

    protected void setSputumType(String sputumType) {
        this.sputumType = sputumType;
    }

    public String getSputumTypeName() {
        return sputumTypeName;
    }

    protected void setSputumTypeName(String sputumTypeName) {
        this.sputumTypeName = sputumTypeName;
    }

    public String getOralCareType() {
        return oralCareType;
    }

    protected void setOralCareType(String oralCareType) {
        this.oralCareType = oralCareType;
    }

    public String getOralCareTypeName() {
        return oralCareTypeName;
    }

    protected void setOralCareTypeName(String oralCareTypeName) {
        this.oralCareTypeName = oralCareTypeName;
    }

    public Integer getOralCareTimes() {
        return oralCareTimes;
    }

    protected void setOralCareTimes(Integer oralCareTimes) {
        this.oralCareTimes = oralCareTimes;
    }

    public String getOralNursing() {
        return oralNursing;
    }

    protected void setOralNursing(String oralNursing) {
        this.oralNursing = oralNursing;
    }

    public String getOralNursingName() {
        return oralNursingName;
    }

    protected void setOralNursingName(String oralNursingName) {
        this.oralNursingName = oralNursingName;
    }

    public String getOtherNursing() {
        return otherNursing;
    }

    protected void setOtherNursing(String otherNursing) {
        this.otherNursing = otherNursing;
    }

    public boolean isNasogasticFlag() {
        return nasogasticFlag;
    }

    protected void setNasogasticFlag(boolean nasogasticFlag) {
        this.nasogasticFlag = nasogasticFlag;
    }

    public Integer getVapTime() {
        return vapTime;
    }

    protected void setVapTime(Integer vapTime) {
        this.vapTime = vapTime;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisAdvEventVap other = (CisAdvEventVap) obj;
        return Objects.equals(id, other.id);
    }

    public CisAdvEventVap create(CisAdvEventVapNto cisAdvEventVapNto) {
        Assert.notNull(cisAdvEventVapNto, "参数cisAdvEventVapNto不能为空！");

        setId(cisAdvEventVapNto.getId());
        setEventReportId(cisAdvEventVapNto.getEventReportId());
        setPatType(cisAdvEventVapNto.getPatType());
        setInpatientCode(cisAdvEventVapNto.getInpatientCode());
        setVisitCode(cisAdvEventVapNto.getVisitCode());
        setPatName(cisAdvEventVapNto.getPatName());
        setSex(cisAdvEventVapNto.getSex());
        setBirthDate(cisAdvEventVapNto.getBirthDate());
        setAgeRange(cisAdvEventVapNto.getAgeRange());
        setAreaCode(cisAdvEventVapNto.getAreaCode());
        setAreaName(cisAdvEventVapNto.getAreaName());
        setInDate(cisAdvEventVapNto.getInDate());
        setArtificialType(cisAdvEventVapNto.getArtificialType());
        setArtificialTypeName(cisAdvEventVapNto.getArtificialTypeName());
        setExtubationType(cisAdvEventVapNto.getExtubationType());
        setExtubationTypeName(cisAdvEventVapNto.getExtubationTypeName());
        setHumidifyDevice(cisAdvEventVapNto.getHumidifyDevice());
        setHumidifyDeviceName(cisAdvEventVapNto.getHumidifyDeviceName());
        setSputumType(cisAdvEventVapNto.getSputumType());
        setSputumTypeName(cisAdvEventVapNto.getSputumTypeName());
        setOralCareType(cisAdvEventVapNto.getOralCareType());
        setOralCareTypeName(cisAdvEventVapNto.getOralCareTypeName());
        setOralCareTimes(cisAdvEventVapNto.getOralCareTimes());
        setOralNursing(cisAdvEventVapNto.getOralNursing());
        setOralNursingName(cisAdvEventVapNto.getOralNursingName());
        setOtherNursing(cisAdvEventVapNto.getOtherNursing());
        setNasogasticFlag(cisAdvEventVapNto.isNasogasticFlag());
        setVapTime(cisAdvEventVapNto.getVapTime());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventVapEto cisAdvEventVapEto) {
        setEventReportId(cisAdvEventVapEto.getEventReportId());
        setPatType(cisAdvEventVapEto.getPatType());
        setInpatientCode(cisAdvEventVapEto.getInpatientCode());
        setVisitCode(cisAdvEventVapEto.getVisitCode());
        setPatName(cisAdvEventVapEto.getPatName());
        setSex(cisAdvEventVapEto.getSex());
        setBirthDate(cisAdvEventVapEto.getBirthDate());
        setAgeRange(cisAdvEventVapEto.getAgeRange());
        setAreaCode(cisAdvEventVapEto.getAreaCode());
        setAreaName(cisAdvEventVapEto.getAreaName());
        setInDate(cisAdvEventVapEto.getInDate());
        setArtificialType(cisAdvEventVapEto.getArtificialType());
        setArtificialTypeName(cisAdvEventVapEto.getArtificialTypeName());
        setExtubationType(cisAdvEventVapEto.getExtubationType());
        setExtubationTypeName(cisAdvEventVapEto.getExtubationTypeName());
        setHumidifyDevice(cisAdvEventVapEto.getHumidifyDevice());
        setHumidifyDeviceName(cisAdvEventVapEto.getHumidifyDeviceName());
        setSputumType(cisAdvEventVapEto.getSputumType());
        setSputumTypeName(cisAdvEventVapEto.getSputumTypeName());
        setOralCareType(cisAdvEventVapEto.getOralCareType());
        setOralCareTypeName(cisAdvEventVapEto.getOralCareTypeName());
        setOralCareTimes(cisAdvEventVapEto.getOralCareTimes());
        setOralNursing(cisAdvEventVapEto.getOralNursing());
        setOralNursingName(cisAdvEventVapEto.getOralNursingName());
        setOtherNursing(cisAdvEventVapEto.getOtherNursing());
        setNasogasticFlag(cisAdvEventVapEto.isNasogasticFlag());
        setVapTime(cisAdvEventVapEto.getVapTime());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

}
