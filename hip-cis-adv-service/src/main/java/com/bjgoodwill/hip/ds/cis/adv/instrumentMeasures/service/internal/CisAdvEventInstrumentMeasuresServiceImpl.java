package com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.entity.CisAdvEventInstrumentMeasures;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.service.CisAdvEventInstrumentMeasuresService;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.service.internal.assembler.CisAdvEventInstrumentMeasuresAssembler;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.to.CisAdvEventInstrumentMeasuresEto;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.to.CisAdvEventInstrumentMeasuresNto;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.to.CisAdvEventInstrumentMeasuresQto;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.to.CisAdvEventInstrumentMeasuresTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.service.CisAdvEventInstrumentMeasuresService")
@RequestMapping(value = "/api/cisadv/instrumentMeasures/cisAdvEventInstrumentMeasures", produces = "application/json; charset=utf-8")
public class CisAdvEventInstrumentMeasuresServiceImpl implements CisAdvEventInstrumentMeasuresService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventInstrumentMeasuresTo> getCisAdvEventInstrumentMeasureses(CisAdvEventInstrumentMeasuresQto cisAdvEventInstrumentMeasuresQto) {
        return CisAdvEventInstrumentMeasuresAssembler.toTos(CisAdvEventInstrumentMeasures.getCisAdvEventInstrumentMeasureses(cisAdvEventInstrumentMeasuresQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventInstrumentMeasuresTo> getCisAdvEventInstrumentMeasuresPage(CisAdvEventInstrumentMeasuresQto cisAdvEventInstrumentMeasuresQto) {
        Page<CisAdvEventInstrumentMeasures> page = CisAdvEventInstrumentMeasures.getCisAdvEventInstrumentMeasuresPage(cisAdvEventInstrumentMeasuresQto);
        Page<CisAdvEventInstrumentMeasuresTo> result = page.map(CisAdvEventInstrumentMeasuresAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventInstrumentMeasuresTo getCisAdvEventInstrumentMeasuresById(String id) {
        return CisAdvEventInstrumentMeasuresAssembler.toTo(CisAdvEventInstrumentMeasures.getCisAdvEventInstrumentMeasuresById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventInstrumentMeasuresTo createCisAdvEventInstrumentMeasures(CisAdvEventInstrumentMeasuresNto cisAdvEventInstrumentMeasuresNto) {
        CisAdvEventInstrumentMeasures cisAdvEventInstrumentMeasures = new CisAdvEventInstrumentMeasures();
        cisAdvEventInstrumentMeasures = cisAdvEventInstrumentMeasures.create(cisAdvEventInstrumentMeasuresNto);
        return CisAdvEventInstrumentMeasuresAssembler.toTo(cisAdvEventInstrumentMeasures);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventInstrumentMeasures(String id, CisAdvEventInstrumentMeasuresEto cisAdvEventInstrumentMeasuresEto) {
        Optional<CisAdvEventInstrumentMeasures> cisAdvEventInstrumentMeasuresOptional = CisAdvEventInstrumentMeasures.getCisAdvEventInstrumentMeasuresById(id);
        cisAdvEventInstrumentMeasuresOptional.ifPresent(cisAdvEventInstrumentMeasures -> cisAdvEventInstrumentMeasures.update(cisAdvEventInstrumentMeasuresEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventInstrumentMeasures(String id) {
        Optional<CisAdvEventInstrumentMeasures> cisAdvEventInstrumentMeasuresOptional = CisAdvEventInstrumentMeasures.getCisAdvEventInstrumentMeasuresById(id);
        cisAdvEventInstrumentMeasuresOptional.ifPresent(cisAdvEventInstrumentMeasures -> cisAdvEventInstrumentMeasures.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}