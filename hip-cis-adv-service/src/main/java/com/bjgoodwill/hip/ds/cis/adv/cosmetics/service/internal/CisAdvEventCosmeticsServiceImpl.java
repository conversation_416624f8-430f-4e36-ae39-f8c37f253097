package com.bjgoodwill.hip.ds.cis.adv.cosmetics.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.entity.CisAdvEventCosmetics;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.service.CisAdvEventCosmeticsService;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.service.internal.assembler.CisAdvEventCosmeticsAssembler;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.CisAdvEventCosmeticsEto;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.CisAdvEventCosmeticsNto;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.CisAdvEventCosmeticsQto;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.CisAdvEventCosmeticsTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.cosmetics.service.CisAdvEventCosmeticsService")
@RequestMapping(value = "/api/cisadv/cosmetics/cisAdvEventCosmetics", produces = "application/json; charset=utf-8")
public class CisAdvEventCosmeticsServiceImpl implements CisAdvEventCosmeticsService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventCosmeticsTo> getCisAdvEventCosmeticses(CisAdvEventCosmeticsQto cisAdvEventCosmeticsQto) {
        return CisAdvEventCosmeticsAssembler.toTos(CisAdvEventCosmetics.getCisAdvEventCosmeticses(cisAdvEventCosmeticsQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventCosmeticsTo> getCisAdvEventCosmeticsPage(CisAdvEventCosmeticsQto cisAdvEventCosmeticsQto) {
        Page<CisAdvEventCosmetics> page = CisAdvEventCosmetics.getCisAdvEventCosmeticsPage(cisAdvEventCosmeticsQto);
        Page<CisAdvEventCosmeticsTo> result = page.map(CisAdvEventCosmeticsAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventCosmeticsTo getCisAdvEventCosmeticsById(String id) {
        return CisAdvEventCosmeticsAssembler.toTo(CisAdvEventCosmetics.getCisAdvEventCosmeticsById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventCosmeticsTo createCisAdvEventCosmetics(CisAdvEventCosmeticsNto cisAdvEventCosmeticsNto) {
        CisAdvEventCosmetics cisAdvEventCosmetics = new CisAdvEventCosmetics();
        cisAdvEventCosmetics = cisAdvEventCosmetics.create(cisAdvEventCosmeticsNto);
        return CisAdvEventCosmeticsAssembler.toTo(cisAdvEventCosmetics);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventCosmetics(String id, CisAdvEventCosmeticsEto cisAdvEventCosmeticsEto) {
        Optional<CisAdvEventCosmetics> cisAdvEventCosmeticsOptional = CisAdvEventCosmetics.getCisAdvEventCosmeticsById(id);
        cisAdvEventCosmeticsOptional.ifPresent(cisAdvEventCosmetics -> cisAdvEventCosmetics.update(cisAdvEventCosmeticsEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventCosmetics(String id) {
        Optional<CisAdvEventCosmetics> cisAdvEventCosmeticsOptional = CisAdvEventCosmetics.getCisAdvEventCosmeticsById(id);
        cisAdvEventCosmeticsOptional.ifPresent(cisAdvEventCosmetics -> cisAdvEventCosmetics.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}