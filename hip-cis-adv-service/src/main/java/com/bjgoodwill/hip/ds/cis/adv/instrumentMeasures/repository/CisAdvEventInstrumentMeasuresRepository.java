package com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.repository;

import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.entity.CisAdvEventInstrumentMeasures;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.repository.CisAdvEventInstrumentMeasuresRepository")
public interface CisAdvEventInstrumentMeasuresRepository extends JpaRepository<CisAdvEventInstrumentMeasures, String>, JpaSpecificationExecutor<CisAdvEventInstrumentMeasures> {

}