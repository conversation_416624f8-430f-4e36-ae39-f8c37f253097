package com.bjgoodwill.hip.ds.cis.adv.fall.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.adv.fall.entity.CisAdvEventFall;
import com.bjgoodwill.hip.ds.cis.adv.fall.service.CisAdvEventFallService;
import com.bjgoodwill.hip.ds.cis.adv.fall.service.internal.assembler.CisAdvEventFallAssembler;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallEto;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallNto;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallQto;
import com.bjgoodwill.hip.ds.cis.adv.fall.to.CisAdvEventFallTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.adv.fall.service.CisAdvEventFallService")
@RequestMapping(value = "/api/cisadv/fall/cisAdvEventFall", produces = "application/json; charset=utf-8")
public class CisAdvEventFallServiceImpl implements CisAdvEventFallService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAdvEventFallTo> getCisAdvEventFalls(CisAdvEventFallQto cisAdvEventFallQto) {
        return CisAdvEventFallAssembler.toTos(CisAdvEventFall.getCisAdvEventFalls(cisAdvEventFallQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAdvEventFallTo> getCisAdvEventFallPage(CisAdvEventFallQto cisAdvEventFallQto) {
        Page<CisAdvEventFall> page = CisAdvEventFall.getCisAdvEventFallPage(cisAdvEventFallQto);
        Page<CisAdvEventFallTo> result = page.map(CisAdvEventFallAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAdvEventFallTo getCisAdvEventFallById(String id) {
        return CisAdvEventFallAssembler.toTo(CisAdvEventFall.getCisAdvEventFallById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAdvEventFallTo createCisAdvEventFall(CisAdvEventFallNto cisAdvEventFallNto) {
        CisAdvEventFall cisAdvEventFall = new CisAdvEventFall();
		cisAdvEventFall = cisAdvEventFall.create(cisAdvEventFallNto);
		return CisAdvEventFallAssembler.toTo(cisAdvEventFall);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisAdvEventFall(String id, CisAdvEventFallEto cisAdvEventFallEto) {
        Optional<CisAdvEventFall> cisAdvEventFallOptional = CisAdvEventFall.getCisAdvEventFallById(id);
		cisAdvEventFallOptional.ifPresent(cisAdvEventFall -> cisAdvEventFall.update(cisAdvEventFallEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAdvEventFall(String id) {
        Optional<CisAdvEventFall> cisAdvEventFallOptional = CisAdvEventFall.getCisAdvEventFallById(id);
		cisAdvEventFallOptional.ifPresent(cisAdvEventFall -> cisAdvEventFall.delete());
    }

    @InitBinder
	public void initBinder(WebDataBinder binder) {
	}
}