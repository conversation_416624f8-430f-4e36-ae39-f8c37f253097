package com.bjgoodwill.hip.ds.cis.adv.drugExplain.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.repository.CisAdvEventDrugExplainRepository;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.to.CisAdvEventDrugExplainEto;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.to.CisAdvEventDrugExplainNto;
import com.bjgoodwill.hip.ds.cis.adv.drugExplain.to.CisAdvEventDrugExplainQto;
import com.bjgoodwill.hip.ds.cis.adv.enmus.CisAdvBusinessErrorEnum;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "不良事件药品说明")
@Table(name = "cis_adv_event_drug_explain", indexes = {}, uniqueConstraints = {})
public class CisAdvEventDrugExplain {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("药品不良事件id")
    @Column(name = "event_drug_id", nullable = true, length = 50)
    private String eventDrugId;


    @Comment("类型：doubt怀疑；together并用")
    @Column(name = "use_drug_type", nullable = true, length = 16)
    private String useDrugType;


    @Comment("批准文号（国药准字）")
    @Column(name = "approval_doc", nullable = true)
    private String approvalDoc;


    @Comment("商品编码")
    @Column(name = "drug_goods_code", nullable = true, length = 50)
    private String drugGoodsCode;


    @Comment("商品名称")
    @Column(name = "drug_goods_name", nullable = true, length = 50)
    private String drugGoodsName;


    @Comment("通用名")
    @Column(name = "common_name", nullable = true)
    private String commonName;


    @Comment("剂型")
    @Column(name = "dosageform", nullable = true, length = 50)
    private String dosageform;


    @Comment("生产厂家")
    @Column(name = "manufacture_firm", nullable = true)
    private String manufactureFirm;


    @Comment("生产批号")
    @Column(name = "batch_no", nullable = true, length = 16)
    private String batchNo;


    @Comment("用法用量（每次量 途径 频次）")
    @Column(name = "usage_dosage", nullable = true, length = 128)
    private String usageDosage;


    @Comment("开始时间")
    @Column(name = "effective_time_low", nullable = true)
    private LocalDateTime effectiveTimeLow;


    @Comment("终止时间")
    @Column(name = "effective_time_high", nullable = true)
    private LocalDateTime effectiveTimeHigh;


    @Comment("用药原因")
    @Column(name = "reasons_medication", nullable = true)
    private String reasonsMedication;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    public String getId() {
    	return id;
    }

    protected void setId(String id) {
    	this.id = id;
    }

    public String getEventDrugId() {
    	return eventDrugId;
    }

    protected void setEventDrugId(String eventDrugId) {
    	this.eventDrugId = eventDrugId;
    }

    public String getUseDrugType() {
    	return useDrugType;
    }

    protected void setUseDrugType(String useDrugType) {
    	this.useDrugType = useDrugType;
    }

    public String getApprovalDoc() {
    	return approvalDoc;
    }

    protected void setApprovalDoc(String approvalDoc) {
    	this.approvalDoc = approvalDoc;
    }

    public String getDrugGoodsCode() {
    	return drugGoodsCode;
    }

    protected void setDrugGoodsCode(String drugGoodsCode) {
    	this.drugGoodsCode = drugGoodsCode;
    }

    public String getDrugGoodsName() {
    	return drugGoodsName;
    }

    protected void setDrugGoodsName(String drugGoodsName) {
    	this.drugGoodsName = drugGoodsName;
    }

    public String getCommonName() {
    	return commonName;
    }

    protected void setCommonName(String commonName) {
    	this.commonName = commonName;
    }

    public String getDosageform() {
    	return dosageform;
    }

    protected void setDosageform(String dosageform) {
    	this.dosageform = dosageform;
    }

    public String getManufactureFirm() {
    	return manufactureFirm;
    }

    protected void setManufactureFirm(String manufactureFirm) {
    	this.manufactureFirm = manufactureFirm;
    }

    public String getBatchNo() {
    	return batchNo;
    }

    protected void setBatchNo(String batchNo) {
    	this.batchNo = batchNo;
    }

    public String getUsageDosage() {
    	return usageDosage;
    }

    protected void setUsageDosage(String usageDosage) {
    	this.usageDosage = usageDosage;
    }

    public LocalDateTime getEffectiveTimeLow() {
    	return effectiveTimeLow;
    }

    protected void setEffectiveTimeLow(LocalDateTime effectiveTimeLow) {
    	this.effectiveTimeLow = effectiveTimeLow;
    }

    public LocalDateTime getEffectiveTimeHigh() {
    	return effectiveTimeHigh;
    }

    protected void setEffectiveTimeHigh(LocalDateTime effectiveTimeHigh) {
    	this.effectiveTimeHigh = effectiveTimeHigh;
    }

    public String getReasonsMedication() {
    	return reasonsMedication;
    }

    protected void setReasonsMedication(String reasonsMedication) {
    	this.reasonsMedication = reasonsMedication;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventDrugExplain other = (CisAdvEventDrugExplain) obj;
		return Objects.equals(id, other.id);
	}

    public CisAdvEventDrugExplain create(CisAdvEventDrugExplainNto cisAdvEventDrugExplainNto) {
        BusinessAssert.notNull(cisAdvEventDrugExplainNto, CisAdvBusinessErrorEnum.BUS_CIS_ADV_0001,"参数cisAdvEventDrugExplainNto");

        setId(cisAdvEventDrugExplainNto.getId());
        setEventDrugId(cisAdvEventDrugExplainNto.getEventDrugId());
        setUseDrugType(cisAdvEventDrugExplainNto.getUseDrugType());
        setApprovalDoc(cisAdvEventDrugExplainNto.getApprovalDoc());
        setDrugGoodsCode(cisAdvEventDrugExplainNto.getDrugGoodsCode());
        setDrugGoodsName(cisAdvEventDrugExplainNto.getDrugGoodsName());
        setCommonName(cisAdvEventDrugExplainNto.getCommonName());
        setDosageform(cisAdvEventDrugExplainNto.getDosageform());
        setManufactureFirm(cisAdvEventDrugExplainNto.getManufactureFirm());
        setBatchNo(cisAdvEventDrugExplainNto.getBatchNo());
        setUsageDosage(cisAdvEventDrugExplainNto.getUsageDosage());
        setEffectiveTimeLow(cisAdvEventDrugExplainNto.getEffectiveTimeLow());
        setEffectiveTimeHigh(cisAdvEventDrugExplainNto.getEffectiveTimeHigh());
        setReasonsMedication(cisAdvEventDrugExplainNto.getReasonsMedication());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventDrugExplainEto cisAdvEventDrugExplainEto) {
        setEventDrugId(cisAdvEventDrugExplainEto.getEventDrugId());
        setUseDrugType(cisAdvEventDrugExplainEto.getUseDrugType());
        setApprovalDoc(cisAdvEventDrugExplainEto.getApprovalDoc());
        setDrugGoodsCode(cisAdvEventDrugExplainEto.getDrugGoodsCode());
        setDrugGoodsName(cisAdvEventDrugExplainEto.getDrugGoodsName());
        setCommonName(cisAdvEventDrugExplainEto.getCommonName());
        setDosageform(cisAdvEventDrugExplainEto.getDosageform());
        setManufactureFirm(cisAdvEventDrugExplainEto.getManufactureFirm());
        setBatchNo(cisAdvEventDrugExplainEto.getBatchNo());
        setUsageDosage(cisAdvEventDrugExplainEto.getUsageDosage());
        setEffectiveTimeLow(cisAdvEventDrugExplainEto.getEffectiveTimeLow());
        setEffectiveTimeHigh(cisAdvEventDrugExplainEto.getEffectiveTimeHigh());
        setReasonsMedication(cisAdvEventDrugExplainEto.getReasonsMedication());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

    public static Optional<CisAdvEventDrugExplain> getCisAdvEventDrugExplainById(String id) {
		return dao().findById(id);
	}

	public static List<CisAdvEventDrugExplain> getCisAdvEventDrugExplains(CisAdvEventDrugExplainQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
	}

	public static Page<CisAdvEventDrugExplain> getCisAdvEventDrugExplainPage(CisAdvEventDrugExplainQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
	}


	/**
	 * @generated
	 */
    private static Specification<CisAdvEventDrugExplain> getSpecification(CisAdvEventDrugExplainQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
        	if(StringUtils.isNotBlank(qto.getEventDrugId())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventDrugId"), qto.getEventDrugId()));
        	}
        	if(StringUtils.isNotBlank(qto.getUseDrugType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("useDrugType"), qto.getUseDrugType()));
        	}
        	if(StringUtils.isNotBlank(qto.getApprovalDoc())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("approvalDoc"), qto.getApprovalDoc()));
        	}
        	if(StringUtils.isNotBlank(qto.getDrugGoodsCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugGoodsCode"), qto.getDrugGoodsCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getDrugGoodsName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugGoodsName"), qto.getDrugGoodsName()));
        	}
        	if(StringUtils.isNotBlank(qto.getCommonName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("commonName"), qto.getCommonName()));
        	}
        	if(StringUtils.isNotBlank(qto.getDosageform())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("dosageform"), qto.getDosageform()));
        	}
        	if(StringUtils.isNotBlank(qto.getManufactureFirm())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("manufactureFirm"), qto.getManufactureFirm()));
        	}
        	if(StringUtils.isNotBlank(qto.getBatchNo())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("batchNo"), qto.getBatchNo()));
        	}
        	if(StringUtils.isNotBlank(qto.getUsageDosage())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("usageDosage"), qto.getUsageDosage()));
        	}
    		if(qto.getEffectiveTimeLow() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("effectiveTimeLow"), LocalDateUtil.beginOfDay(qto.getEffectiveTimeLow()), LocalDateUtil.endOfDay(qto.getEffectiveTimeLow())));
        	}
    		if(qto.getEffectiveTimeHigh() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("effectiveTimeHigh"), LocalDateUtil.beginOfDay(qto.getEffectiveTimeHigh()), LocalDateUtil.endOfDay(qto.getEffectiveTimeHigh())));
        	}
        	if(StringUtils.isNotBlank(qto.getReasonsMedication())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reasonsMedication"), qto.getReasonsMedication()));
        	}
            return predicate;
        };
    }

    private static CisAdvEventDrugExplainRepository dao() {
		return SpringUtil.getBean(CisAdvEventDrugExplainRepository.class);
	}

}
