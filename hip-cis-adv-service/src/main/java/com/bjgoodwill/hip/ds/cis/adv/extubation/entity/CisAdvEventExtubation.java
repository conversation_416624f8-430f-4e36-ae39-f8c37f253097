package com.bjgoodwill.hip.ds.cis.adv.extubation.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.extubation.repository.CisAdvEventExtubationRepository;
import com.bjgoodwill.hip.ds.cis.adv.extubation.to.CisAdvEventExtubationEto;
import com.bjgoodwill.hip.ds.cis.adv.extubation.to.CisAdvEventExtubationNto;
import com.bjgoodwill.hip.ds.cis.adv.extubation.to.CisAdvEventExtubationQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "非计划拔管事件上报表")
@Table(name = "cis_adv_event_extubation", indexes = {}, uniqueConstraints = {})
public class CisAdvEventExtubation {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件id")
    @Column(name = "event_report_id", nullable = true, length = 50)
    private String eventReportId;


    @Comment("非计划拔管类型：导尿管；气管导管；picc；cvc；胃肠管（经口鼻）")
    @Column(name = "unplanned_type", nullable = true, length = 32)
    private String unplannedType;


    @Comment("患者类型")
    @Column(name = "pat_type", nullable = true, length = 2)
    private String patType;


    @Comment("住院号(门诊就诊卡号)")
    @Column(name = "inpatient_code", nullable = true, length = 16)
    private String inpatientCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true, length = 16)
    private String visitCode;


    @Comment("患者姓名")
    @Column(name = "pat_name", nullable = true, length = 64)
    private String patName;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("床号")
    @Column(name = "bed_name", nullable = true, length = 64)
    private String bedName;


    @Comment("病区科室")
    @Column(name = "area_code", nullable = true, length = 16)
    private String areaCode;


    @Comment("病区科室名称")
    @Column(name = "area_name", nullable = true, length = 16)
    private String areaName;


    @Comment("事件发生时间")
    @Column(name = "event_date", nullable = true)
    private LocalDateTime eventDate;


    @Comment("事件发生场所：in病区内，out病区外（院内）")
    @Column(name = "event_place", nullable = true, length = 16)
    private String eventPlace;


    @Comment("事件发生场所：in病区内，out病区外（院内）")
    @Column(name = "event_place_name", nullable = true, length = 32)
    private String eventPlaceName;


    @Comment("该患者本次住院非计划拔管次数：1第一次；2第二次；3第三次；4大于3次")
    @Column(name = "extubation_num", nullable = true)
    private Integer extubationNum;


    @Comment("非计划拔管原因: oneself 患者自拔；slipping管路滑落；block阻塞；infected感染；material材质问题；other其他；")
    @Column(name = "extubation_reasons", nullable = true, length = 16)
    private String extubationReasons;


    @Comment("非计划拔管原因名称: oneself 患者自拔；slipping管路滑落；block阻塞；infected感染；material材质问题；other其他；")
    @Column(name = "extubation_reasons_name", nullable = true, length = 32)
    private String extubationReasonsName;


    @Comment("是否重置:0否；1是")
    @Column(name = "reset_flag", nullable = false)
    private boolean resetFlag;


    @Comment("重置时间：1非计划拔管后24小时内（含24小时）；2非计划拔管24小时后")
    @Column(name = "reset_24_time_flag", nullable = true, length = 32)
    private String reset24timeFlag;


    @Comment("非计划拔管时有无约束：0否；1是；")
    @Column(name = "constraint_flag", nullable = false)
    private boolean constraintFlag;


    @Comment("非计划拔管时患者状态：bedridden卧床时；turnover翻身时；passingbed过床时；transport转运时；inspect检查时；other其他；")
    @Column(name = "patient_state", nullable = true, length = 16)
    private String patientState;


    @Comment("非计划拔管时患者状态名称：bedridden卧床时；turnover翻身时；passingbed过床时；transport转运时；inspect检查时；other其他；")
    @Column(name = "patient_state_name", nullable = true, length = 32)
    private String patientStateName;


    @Comment("非计划拔管时患者神志：0不清醒；1清醒；")
    @Column(name = "mind_flag", nullable = false)
    private boolean mindFlag;


    @Comment("非计划拔管时患者是否镇静：0否；1是；2不知道")
    @Column(name = "calm_flag", nullable = true, length = 16)
    private String calmFlag;


    @Comment("风险评估工具:rass(richmond 躁动-镇静评分)，sas（镇静-躁动评分），其他量表，未评估")
    @Column(name = "assessment_tool", nullable = true, length = 128)
    private String assessmentTool;


    @Comment("非计划拔管时患者pass评分（richmond躁动-镇静量表）：4，3，2，1，0，-1，-2，-3，-4，-5，其他量表，未评估")
    @Column(name = "gauge_score", nullable = true, length = 16)
    private String gaugeScore;


    @Comment("其他量表名称")
    @Column(name = "other_gauge", nullable = true, length = 128)
    private String otherGauge;


    @Comment("其他量表分值")
    @Column(name = "other_gauge_score", nullable = true)
    private Integer otherGaugeScore;


    @Comment("非计划拔管发生时当班责任护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    @Column(name = "working_life", nullable = true, length = 16)
    private String workingLife;


    @Comment("非计划拔管发生时当班责任护士工作年限:a小于一年；b 1年（包含）到2年；c 2年（包含）到5年；d 5年（包含）到10年；e 10年（包含）到20年；f 大于20年")
    @Column(name = "working_life_name", nullable = true)
    private String workingLifeName;


    @Comment("非计划拔管发生时在岗责任护士人数")
    @Column(name = "duty_num", nullable = true)
    private Integer dutyNum;


    @Comment("非计划拔管发生时病区在院患者数")
    @Column(name = "area_num", nullable = true)
    private Integer areaNum;


    @Comment("整改措施")
    @Column(name = "improvement_measures", nullable = true)
    private String improvementMeasures;


    @Comment("护士长签名")
    @Column(name = "head_signature", nullable = true, length = 16)
    private String headSignature;


    @Comment("护士长签名名称")
    @Column(name = "head_signature_name", nullable = true, length = 32)
    private String headSignatureName;


    @Comment("护士长填写时间")
    @Column(name = "head_signature_date", nullable = true)
    private LocalDateTime headSignatureDate;


    @Comment("护理部意见")
    @Column(name = "nurs_dept_opinion", nullable = true)
    private String nursDeptOpinion;


    @Comment("护理部签名")
    @Column(name = "nurs_dept_signature", nullable = true, length = 16)
    private String nursDeptSignature;


    @Comment("护理部签名名称")
    @Column(name = "nurs_dept_signature_name", nullable = true, length = 32)
    private String nursDeptSignatureName;


    @Comment("护理部填写时间")
    @Column(name = "dept_signature_date", nullable = true)
    private LocalDateTime deptSignatureDate;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    public String getId() {
    	return id;
    }

    protected void setId(String id) {
    	this.id = id;
    }

    public String getEventReportId() {
    	return eventReportId;
    }

    protected void setEventReportId(String eventReportId) {
    	this.eventReportId = eventReportId;
    }

    public String getUnplannedType() {
    	return unplannedType;
    }

    protected void setUnplannedType(String unplannedType) {
    	this.unplannedType = unplannedType;
    }

    public String getPatType() {
    	return patType;
    }

    protected void setPatType(String patType) {
    	this.patType = patType;
    }

    public String getInpatientCode() {
    	return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
    	this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    protected void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public String getPatName() {
    	return patName;
    }

    protected void setPatName(String patName) {
    	this.patName = patName;
    }

    public String getSex() {
    	return sex;
    }

    protected void setSex(String sex) {
    	this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
    	this.birthDate = birthDate;
    }

    public String getBedName() {
    	return bedName;
    }

    protected void setBedName(String bedName) {
    	this.bedName = bedName;
    }

    public String getAreaCode() {
    	return areaCode;
    }

    protected void setAreaCode(String areaCode) {
    	this.areaCode = areaCode;
    }

    public String getAreaName() {
    	return areaName;
    }

    protected void setAreaName(String areaName) {
    	this.areaName = areaName;
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    protected void setEventDate(LocalDateTime eventDate) {
    	this.eventDate = eventDate;
    }

    public String getEventPlace() {
    	return eventPlace;
    }

    protected void setEventPlace(String eventPlace) {
    	this.eventPlace = eventPlace;
    }

    public String getEventPlaceName() {
    	return eventPlaceName;
    }

    protected void setEventPlaceName(String eventPlaceName) {
    	this.eventPlaceName = eventPlaceName;
    }

    public Integer getExtubationNum() {
    	return extubationNum;
    }

    protected void setExtubationNum(Integer extubationNum) {
    	this.extubationNum = extubationNum;
    }

    public String getExtubationReasons() {
    	return extubationReasons;
    }

    protected void setExtubationReasons(String extubationReasons) {
    	this.extubationReasons = extubationReasons;
    }

    public String getExtubationReasonsName() {
    	return extubationReasonsName;
    }

    protected void setExtubationReasonsName(String extubationReasonsName) {
    	this.extubationReasonsName = extubationReasonsName;
    }

    public boolean isResetFlag() {
    	return resetFlag;
    }

    protected void setResetFlag(boolean resetFlag) {
    	this.resetFlag = resetFlag;
    }

    public String getReset24timeFlag() {
    	return reset24timeFlag;
    }

    protected void setReset24timeFlag(String reset24timeFlag) {
    	this.reset24timeFlag = reset24timeFlag;
    }

    public boolean isConstraintFlag() {
    	return constraintFlag;
    }

    protected void setConstraintFlag(boolean constraintFlag) {
    	this.constraintFlag = constraintFlag;
    }

    public String getPatientState() {
    	return patientState;
    }

    protected void setPatientState(String patientState) {
    	this.patientState = patientState;
    }

    public String getPatientStateName() {
    	return patientStateName;
    }

    protected void setPatientStateName(String patientStateName) {
    	this.patientStateName = patientStateName;
    }

    public boolean isMindFlag() {
    	return mindFlag;
    }

    protected void setMindFlag(boolean mindFlag) {
    	this.mindFlag = mindFlag;
    }

    public String getCalmFlag() {
    	return calmFlag;
    }

    protected void setCalmFlag(String calmFlag) {
    	this.calmFlag = calmFlag;
    }

    public String getAssessmentTool() {
    	return assessmentTool;
    }

    protected void setAssessmentTool(String assessmentTool) {
    	this.assessmentTool = assessmentTool;
    }

    public String getGaugeScore() {
    	return gaugeScore;
    }

    protected void setGaugeScore(String gaugeScore) {
    	this.gaugeScore = gaugeScore;
    }

    public String getOtherGauge() {
    	return otherGauge;
    }

    protected void setOtherGauge(String otherGauge) {
    	this.otherGauge = otherGauge;
    }

    public Integer getOtherGaugeScore() {
    	return otherGaugeScore;
    }

    protected void setOtherGaugeScore(Integer otherGaugeScore) {
    	this.otherGaugeScore = otherGaugeScore;
    }

    public String getWorkingLife() {
    	return workingLife;
    }

    protected void setWorkingLife(String workingLife) {
    	this.workingLife = workingLife;
    }

    public String getWorkingLifeName() {
    	return workingLifeName;
    }

    protected void setWorkingLifeName(String workingLifeName) {
    	this.workingLifeName = workingLifeName;
    }

    public Integer getDutyNum() {
    	return dutyNum;
    }

    protected void setDutyNum(Integer dutyNum) {
    	this.dutyNum = dutyNum;
    }

    public Integer getAreaNum() {
    	return areaNum;
    }

    protected void setAreaNum(Integer areaNum) {
    	this.areaNum = areaNum;
    }

    public String getImprovementMeasures() {
    	return improvementMeasures;
    }

    protected void setImprovementMeasures(String improvementMeasures) {
    	this.improvementMeasures = improvementMeasures;
    }

    public String getHeadSignature() {
    	return headSignature;
    }

    protected void setHeadSignature(String headSignature) {
    	this.headSignature = headSignature;
    }

    public String getHeadSignatureName() {
    	return headSignatureName;
    }

    protected void setHeadSignatureName(String headSignatureName) {
    	this.headSignatureName = headSignatureName;
    }

    public LocalDateTime getHeadSignatureDate() {
    	return headSignatureDate;
    }

    protected void setHeadSignatureDate(LocalDateTime headSignatureDate) {
    	this.headSignatureDate = headSignatureDate;
    }

    public String getNursDeptOpinion() {
    	return nursDeptOpinion;
    }

    protected void setNursDeptOpinion(String nursDeptOpinion) {
    	this.nursDeptOpinion = nursDeptOpinion;
    }

    public String getNursDeptSignature() {
    	return nursDeptSignature;
    }

    protected void setNursDeptSignature(String nursDeptSignature) {
    	this.nursDeptSignature = nursDeptSignature;
    }

    public String getNursDeptSignatureName() {
    	return nursDeptSignatureName;
    }

    protected void setNursDeptSignatureName(String nursDeptSignatureName) {
    	this.nursDeptSignatureName = nursDeptSignatureName;
    }

    public LocalDateTime getDeptSignatureDate() {
    	return deptSignatureDate;
    }

    protected void setDeptSignatureDate(LocalDateTime deptSignatureDate) {
    	this.deptSignatureDate = deptSignatureDate;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventExtubation other = (CisAdvEventExtubation) obj;
		return Objects.equals(id, other.id);
	}

    public CisAdvEventExtubation create(CisAdvEventExtubationNto cisAdvEventExtubationNto) {
        Assert.notNull(cisAdvEventExtubationNto, "参数cisAdvEventExtubationNto不能为空！");

        setId(cisAdvEventExtubationNto.getId());
        setEventReportId(cisAdvEventExtubationNto.getEventReportId());
        setUnplannedType(cisAdvEventExtubationNto.getUnplannedType());
        setPatType(cisAdvEventExtubationNto.getPatType());
        setInpatientCode(cisAdvEventExtubationNto.getInpatientCode());
        setVisitCode(cisAdvEventExtubationNto.getVisitCode());
        setPatName(cisAdvEventExtubationNto.getPatName());
        setSex(cisAdvEventExtubationNto.getSex());
        setBirthDate(cisAdvEventExtubationNto.getBirthDate());
        setBedName(cisAdvEventExtubationNto.getBedName());
        setAreaCode(cisAdvEventExtubationNto.getAreaCode());
        setAreaName(cisAdvEventExtubationNto.getAreaName());
        setEventDate(cisAdvEventExtubationNto.getEventDate());
        setEventPlace(cisAdvEventExtubationNto.getEventPlace());
        setEventPlaceName(cisAdvEventExtubationNto.getEventPlaceName());
        setExtubationNum(cisAdvEventExtubationNto.getExtubationNum());
        setExtubationReasons(cisAdvEventExtubationNto.getExtubationReasons());
        setExtubationReasonsName(cisAdvEventExtubationNto.getExtubationReasonsName());
        setResetFlag(cisAdvEventExtubationNto.isResetFlag());
        setReset24timeFlag(cisAdvEventExtubationNto.getReset24timeFlag());
        setConstraintFlag(cisAdvEventExtubationNto.isConstraintFlag());
        setPatientState(cisAdvEventExtubationNto.getPatientState());
        setPatientStateName(cisAdvEventExtubationNto.getPatientStateName());
        setMindFlag(cisAdvEventExtubationNto.isMindFlag());
        setCalmFlag(cisAdvEventExtubationNto.getCalmFlag());
        setAssessmentTool(cisAdvEventExtubationNto.getAssessmentTool());
        setGaugeScore(cisAdvEventExtubationNto.getGaugeScore());
        setOtherGauge(cisAdvEventExtubationNto.getOtherGauge());
        setOtherGaugeScore(cisAdvEventExtubationNto.getOtherGaugeScore());
        setWorkingLife(cisAdvEventExtubationNto.getWorkingLife());
        setWorkingLifeName(cisAdvEventExtubationNto.getWorkingLifeName());
        setDutyNum(cisAdvEventExtubationNto.getDutyNum());
        setAreaNum(cisAdvEventExtubationNto.getAreaNum());
        setImprovementMeasures(cisAdvEventExtubationNto.getImprovementMeasures());
        setHeadSignature(cisAdvEventExtubationNto.getHeadSignature());
        setHeadSignatureName(cisAdvEventExtubationNto.getHeadSignatureName());
        setHeadSignatureDate(cisAdvEventExtubationNto.getHeadSignatureDate());
        setNursDeptOpinion(cisAdvEventExtubationNto.getNursDeptOpinion());
        setNursDeptSignature(cisAdvEventExtubationNto.getNursDeptSignature());
        setNursDeptSignatureName(cisAdvEventExtubationNto.getNursDeptSignatureName());
        setDeptSignatureDate(cisAdvEventExtubationNto.getDeptSignatureDate());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventExtubationEto cisAdvEventExtubationEto) {
        setEventReportId(cisAdvEventExtubationEto.getEventReportId());
        setUnplannedType(cisAdvEventExtubationEto.getUnplannedType());
        setPatType(cisAdvEventExtubationEto.getPatType());
        setInpatientCode(cisAdvEventExtubationEto.getInpatientCode());
        setVisitCode(cisAdvEventExtubationEto.getVisitCode());
        setPatName(cisAdvEventExtubationEto.getPatName());
        setSex(cisAdvEventExtubationEto.getSex());
        setBirthDate(cisAdvEventExtubationEto.getBirthDate());
        setBedName(cisAdvEventExtubationEto.getBedName());
        setAreaCode(cisAdvEventExtubationEto.getAreaCode());
        setAreaName(cisAdvEventExtubationEto.getAreaName());
        setEventDate(cisAdvEventExtubationEto.getEventDate());
        setEventPlace(cisAdvEventExtubationEto.getEventPlace());
        setEventPlaceName(cisAdvEventExtubationEto.getEventPlaceName());
        setExtubationNum(cisAdvEventExtubationEto.getExtubationNum());
        setExtubationReasons(cisAdvEventExtubationEto.getExtubationReasons());
        setExtubationReasonsName(cisAdvEventExtubationEto.getExtubationReasonsName());
        setResetFlag(cisAdvEventExtubationEto.isResetFlag());
        setReset24timeFlag(cisAdvEventExtubationEto.getReset24timeFlag());
        setConstraintFlag(cisAdvEventExtubationEto.isConstraintFlag());
        setPatientState(cisAdvEventExtubationEto.getPatientState());
        setPatientStateName(cisAdvEventExtubationEto.getPatientStateName());
        setMindFlag(cisAdvEventExtubationEto.isMindFlag());
        setCalmFlag(cisAdvEventExtubationEto.getCalmFlag());
        setAssessmentTool(cisAdvEventExtubationEto.getAssessmentTool());
        setGaugeScore(cisAdvEventExtubationEto.getGaugeScore());
        setOtherGauge(cisAdvEventExtubationEto.getOtherGauge());
        setOtherGaugeScore(cisAdvEventExtubationEto.getOtherGaugeScore());
        setWorkingLife(cisAdvEventExtubationEto.getWorkingLife());
        setWorkingLifeName(cisAdvEventExtubationEto.getWorkingLifeName());
        setDutyNum(cisAdvEventExtubationEto.getDutyNum());
        setAreaNum(cisAdvEventExtubationEto.getAreaNum());
        setImprovementMeasures(cisAdvEventExtubationEto.getImprovementMeasures());
        setHeadSignature(cisAdvEventExtubationEto.getHeadSignature());
        setHeadSignatureName(cisAdvEventExtubationEto.getHeadSignatureName());
        setHeadSignatureDate(cisAdvEventExtubationEto.getHeadSignatureDate());
        setNursDeptOpinion(cisAdvEventExtubationEto.getNursDeptOpinion());
        setNursDeptSignature(cisAdvEventExtubationEto.getNursDeptSignature());
        setNursDeptSignatureName(cisAdvEventExtubationEto.getNursDeptSignatureName());
        setDeptSignatureDate(cisAdvEventExtubationEto.getDeptSignatureDate());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

    public static Optional<CisAdvEventExtubation> getCisAdvEventExtubationById(String id) {
		return dao().findById(id);
	}

	public static List<CisAdvEventExtubation> getCisAdvEventExtubations(CisAdvEventExtubationQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
	}

	public static Page<CisAdvEventExtubation> getCisAdvEventExtubationPage(CisAdvEventExtubationQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
	}


	/**
	 * @generated
	 */
    private static Specification<CisAdvEventExtubation> getSpecification(CisAdvEventExtubationQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
        	if(StringUtils.isNotBlank(qto.getEventReportId())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReportId"), qto.getEventReportId()));
        	}
        	if(StringUtils.isNotBlank(qto.getUnplannedType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("unplannedType"), qto.getUnplannedType()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patType"), qto.getPatType()));
        	}
        	if(StringUtils.isNotBlank(qto.getInpatientCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getVisitCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patName"), qto.getPatName()));
        	}
        	if(StringUtils.isNotBlank(qto.getSex())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
        	}
    		if(qto.getBirthDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getBedName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("bedName"), qto.getBedName()));
        	}
        	if(StringUtils.isNotBlank(qto.getAreaCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaCode"), qto.getAreaCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getAreaName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaName"), qto.getAreaName()));
        	}
    		if(qto.getEventDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("eventDate"), LocalDateUtil.beginOfDay(qto.getEventDate()), LocalDateUtil.endOfDay(qto.getEventDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getEventPlace())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventPlace"), qto.getEventPlace()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventPlaceName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventPlaceName"), qto.getEventPlaceName()));
        	}
    		if(qto.getExtubationNum() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("extubationNum"), qto.getExtubationNum()));
        	}
        	if(StringUtils.isNotBlank(qto.getExtubationReasons())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("extubationReasons"), qto.getExtubationReasons()));
        	}
        	if(StringUtils.isNotBlank(qto.getExtubationReasonsName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("extubationReasonsName"), qto.getExtubationReasonsName()));
        	}
    		if(qto.getResetFlag() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("resetFlag"), qto.getResetFlag()));
        	}
        	if(StringUtils.isNotBlank(qto.getReset24timeFlag())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reset24timeFlag"), qto.getReset24timeFlag()));
        	}
    		if(qto.getConstraintFlag() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("constraintFlag"), qto.getConstraintFlag()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatientState())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patientState"), qto.getPatientState()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatientStateName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patientStateName"), qto.getPatientStateName()));
        	}
    		if(qto.getMindFlag() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("mindFlag"), qto.getMindFlag()));
        	}
        	if(StringUtils.isNotBlank(qto.getCalmFlag())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("calmFlag"), qto.getCalmFlag()));
        	}
        	if(StringUtils.isNotBlank(qto.getAssessmentTool())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("assessmentTool"), qto.getAssessmentTool()));
        	}
        	if(StringUtils.isNotBlank(qto.getGaugeScore())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("gaugeScore"), qto.getGaugeScore()));
        	}
        	if(StringUtils.isNotBlank(qto.getOtherGauge())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherGauge"), qto.getOtherGauge()));
        	}
    		if(qto.getOtherGaugeScore() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherGaugeScore"), qto.getOtherGaugeScore()));
        	}
        	if(StringUtils.isNotBlank(qto.getWorkingLife())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("workingLife"), qto.getWorkingLife()));
        	}
        	if(StringUtils.isNotBlank(qto.getWorkingLifeName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("workingLifeName"), qto.getWorkingLifeName()));
        	}
    		if(qto.getDutyNum() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("dutyNum"), qto.getDutyNum()));
        	}
    		if(qto.getAreaNum() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("areaNum"), qto.getAreaNum()));
        	}
        	if(StringUtils.isNotBlank(qto.getImprovementMeasures())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("improvementMeasures"), qto.getImprovementMeasures()));
        	}
        	if(StringUtils.isNotBlank(qto.getHeadSignature())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("headSignature"), qto.getHeadSignature()));
        	}
        	if(StringUtils.isNotBlank(qto.getHeadSignatureName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("headSignatureName"), qto.getHeadSignatureName()));
        	}
    		if(qto.getHeadSignatureDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("headSignatureDate"), LocalDateUtil.beginOfDay(qto.getHeadSignatureDate()), LocalDateUtil.endOfDay(qto.getHeadSignatureDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getNursDeptOpinion())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursDeptOpinion"), qto.getNursDeptOpinion()));
        	}
        	if(StringUtils.isNotBlank(qto.getNursDeptSignature())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursDeptSignature"), qto.getNursDeptSignature()));
        	}
        	if(StringUtils.isNotBlank(qto.getNursDeptSignatureName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nursDeptSignatureName"), qto.getNursDeptSignatureName()));
        	}
    		if(qto.getDeptSignatureDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("deptSignatureDate"), LocalDateUtil.beginOfDay(qto.getDeptSignatureDate()), LocalDateUtil.endOfDay(qto.getDeptSignatureDate())));
        	}
            return predicate;
        };
    }

    private static CisAdvEventExtubationRepository dao() {
		return SpringUtil.getBean(CisAdvEventExtubationRepository.class);
	}

}
