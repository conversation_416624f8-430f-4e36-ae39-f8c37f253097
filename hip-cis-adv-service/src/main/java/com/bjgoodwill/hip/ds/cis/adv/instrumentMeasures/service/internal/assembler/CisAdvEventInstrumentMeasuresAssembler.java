package com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.entity.CisAdvEventInstrumentMeasures;
import com.bjgoodwill.hip.ds.cis.adv.instrumentMeasures.to.CisAdvEventInstrumentMeasuresTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAdvEventInstrumentMeasuresAssembler {

    public static List<CisAdvEventInstrumentMeasuresTo> toTos(List<CisAdvEventInstrumentMeasures> cisAdvEventInstrumentMeasuress) {
        return toTos(cisAdvEventInstrumentMeasuress, false);
    }

    public static List<CisAdvEventInstrumentMeasuresTo> toTos(List<CisAdvEventInstrumentMeasures> cisAdvEventInstrumentMeasuress, boolean withAllParts) {
        Assert.notNull(cisAdvEventInstrumentMeasuress, "参数cisAdvEventInstrumentMeasuress不能为空！");

        List<CisAdvEventInstrumentMeasuresTo> tos = new ArrayList<>();
        for (CisAdvEventInstrumentMeasures cisAdvEventInstrumentMeasures : cisAdvEventInstrumentMeasuress)
            tos.add(toTo(cisAdvEventInstrumentMeasures, withAllParts));
        return tos;
    }

    public static CisAdvEventInstrumentMeasuresTo toTo(CisAdvEventInstrumentMeasures cisAdvEventInstrumentMeasures) {
        return toTo(cisAdvEventInstrumentMeasures, false);
    }

    /**
     * @generated
     */
    public static CisAdvEventInstrumentMeasuresTo toTo(CisAdvEventInstrumentMeasures cisAdvEventInstrumentMeasures, boolean withAllParts) {
        if (cisAdvEventInstrumentMeasures == null)
            return null;
        CisAdvEventInstrumentMeasuresTo to = new CisAdvEventInstrumentMeasuresTo();
        to.setId(cisAdvEventInstrumentMeasures.getId());
        to.setEventReportId(cisAdvEventInstrumentMeasures.getEventReportId());
        to.setEventDeptCode(cisAdvEventInstrumentMeasures.getEventDeptCode());
        to.setEventDeptName(cisAdvEventInstrumentMeasures.getEventDeptName());
        to.setEventDate(cisAdvEventInstrumentMeasures.getEventDate());
        to.setEventProcess(cisAdvEventInstrumentMeasures.getEventProcess());
        to.setImprovementMeasures(cisAdvEventInstrumentMeasures.getImprovementMeasures());
        to.setProcessingResult(cisAdvEventInstrumentMeasures.getProcessingResult());
        to.setLeaderSign(cisAdvEventInstrumentMeasures.getLeaderSign());
        to.setLeaderSignName(cisAdvEventInstrumentMeasures.getLeaderSignName());
        to.setSignDate(cisAdvEventInstrumentMeasures.getSignDate());
        to.setBatchNo(cisAdvEventInstrumentMeasures.getBatchNo());
        to.setCreatedDate(cisAdvEventInstrumentMeasures.getCreatedDate());
        to.setCreatedStaff(cisAdvEventInstrumentMeasures.getCreatedStaff());
        to.setCreatedStaffName(cisAdvEventInstrumentMeasures.getCreatedStaffName());
        to.setUpdatedDate(cisAdvEventInstrumentMeasures.getUpdatedDate());
        to.setUpdatedStaff(cisAdvEventInstrumentMeasures.getUpdatedStaff());
        to.setUpdatedStaffName(cisAdvEventInstrumentMeasures.getUpdatedStaffName());

        if (withAllParts) {
        }
        return to;
    }

}