package com.bjgoodwill.hip.ds.cis.adv.cosmetics.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.repository.CisAdvEventCosmeticsRepository;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.CisAdvEventCosmeticsEto;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.CisAdvEventCosmeticsNto;
import com.bjgoodwill.hip.ds.cis.adv.cosmetics.to.CisAdvEventCosmeticsQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "化妆品不良反应事件报告表")
@Table(name = "cis_adv_event_cosmetics", indexes = {}, uniqueConstraints = {})
public class CisAdvEventCosmetics {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("不良事件id")
    @Column(name = "event_report_id", nullable = true, length = 50)
    private String eventReportId;


    @Comment("报告类型：serious严重；commonly一般")
    @Column(name = "report_type", nullable = true, length = 16)
    private String reportType;


    @Comment("报告类型名称：serious严重；commonly一般")
    @Column(name = "report_type_name", nullable = true, length = 16)
    private String reportTypeName;


    @Comment("报告单位类别：medical医疗机构；management经营企业；produce生产企业；personal个人；other其它")
    @Column(name = "report_unit_category", nullable = true, length = 32)
    private String reportUnitCategory;


    @Comment("报告单位类别：medical医疗机构；management经营企业名称；produce生产企业；personal个人；other其它")
    @Column(name = "report_unit_category_name", nullable = true, length = 32)
    private String reportUnitCategoryName;


    @Comment("患者类型")
    @Column(name = "pat_type", nullable = true, length = 2)
    private String patType;


    @Comment("住院号(门诊就诊卡号)")
    @Column(name = "inpatient_code", nullable = true, length = 16)
    private String inpatientCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true, length = 16)
    private String visitCode;


    @Comment("患者姓名")
    @Column(name = "pat_name", nullable = true, length = 64)
    private String patName;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("民族")
    @Column(name = "nation", nullable = true, length = 16)
    private String nation;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("联系电话")
    @Column(name = "contact_tel", nullable = true, length = 24)
    private String contactTel;


    @Comment("通讯地址-居住省")
    @Column(name = "cz_province", nullable = true, length = 16)
    private String czProvince;


    @Comment("通讯地址-居住省")
    @Column(name = "cz_province_name", nullable = true, length = 32)
    private String czProvinceName;


    @Comment("通讯地址-居住市")
    @Column(name = "cz_city", nullable = true, length = 16)
    private String czCity;


    @Comment("通讯地址-居住市")
    @Column(name = "cz_city_name", nullable = true, length = 32)
    private String czCityName;


    @Comment("通讯地址-居住县（区）")
    @Column(name = "cz_county", nullable = true, length = 16)
    private String czCounty;


    @Comment("通讯地址-居住县（区）")
    @Column(name = "cz_county_name", nullable = true, length = 32)
    private String czCountyName;


    @Comment("通讯地址-居住乡（镇、街道）")
    @Column(name = "cz_home_jd", nullable = true, length = 32)
    private String czHomeJd;


    @Comment("通讯地址-居住乡（镇、街道）")
    @Column(name = "cz_home_jd_name", nullable = true)
    private String czHomeJdName;


    @Comment("通讯地址-居住村")
    @Column(name = "cz_village", nullable = true, length = 32)
    private String czVillage;


    @Comment("通讯地址-居住村")
    @Column(name = "cz_village_name", nullable = true)
    private String czVillageName;


    @Comment("有无化妆品过敏史1有2无3不详")
    @Column(name = "is_cosmetics_allergy", nullable = true, length = 2)
    private String isCosmeticsAllergy;


    @Comment("化妆品过敏史具体")
    @Column(name = "cosmetics_allergy_remark", nullable = true)
    private String cosmeticsAllergyRemark;


    @Comment("有无药品过敏史1有2无3不详")
    @Column(name = "is_drug_allergy", nullable = true, length = 2)
    private String isDrugAllergy;


    @Comment("药品过敏史具体")
    @Column(name = "drug_allergy_remark", nullable = true)
    private String drugAllergyRemark;


    @Comment("有无食物过敏史1有2无3不详")
    @Column(name = "is_food_allergy", nullable = true, length = 2)
    private String isFoodAllergy;


    @Comment("食物过敏史具体")
    @Column(name = "food_allergy_remark", nullable = true)
    private String foodAllergyRemark;


    @Comment("有无其他接触物过敏史1有2无3不详")
    @Column(name = "is_other_allergy", nullable = true, length = 2)
    private String isOtherAllergy;


    @Comment("其他接触物过敏史具体")
    @Column(name = "other_allergy_remark", nullable = true)
    private String otherAllergyRemark;


    @Comment("开始使用日期")
    @Column(name = "begin_date", nullable = true)
    private LocalDateTime beginDate;


    @Comment("不良反应发生日期")
    @Column(name = "event_date", nullable = true)
    private LocalDateTime eventDate;


    @Comment("停用日期")
    @Column(name = "end_date", nullable = true)
    private LocalDateTime endDate;


    @Comment("潜伏期可疑化妆品开始/停止时间,1开始，0停止")
    @Column(name = "event_process_1", nullable = true)
    private String eventProcess1;


    @Comment("出现临床表现的时间差")
    @Column(name = "event_process_11", nullable = true)
    private Integer eventProcess11;


    @Comment("可疑化妆品开始/停止时间单位，hours小时，day 天，month月")
    @Column(name = "event_process_12", nullable = true, length = 16)
    private String eventProcess12;


    @Comment("自觉症状,1瘙痒，2灼热感，3疼痛，4干燥，5紧绷感，6其他")
    @Column(name = "event_process_2", nullable = true)
    private String eventProcess2;


    @Comment("自觉症状其他")
    @Column(name = "event_process_21", nullable = true)
    private String eventProcess21;


    @Comment("皮损部位:1面部2头皮 3外耳廓 4颈部 5全身6胸部 7腹部 8背部9腋窝 10腹股沟11上肢 12下肢 13手部 14甲周 15甲板  16无   17其他")
    @Column(name = "event_process_3", nullable = true)
    private String eventProcess3;


    @Comment("皮损部位其他")
    @Column(name = "event_process_31", nullable = true)
    private String eventProcess31;


    @Comment("皮损形态: 1红斑  2丘疹 3斑块  4丘疱疹  5水肿6水疱  7粉刺8风团  9毛囊炎样  10毛细血管扩张 11色素沉着  12色素减退  13色素脱失 14毛发脱色  15毛发变脆  16毛发分叉  17毛发断裂  18毛发脱落19甲板变形  20甲板软化  21甲板剥离 22甲板脆裂  23甲周皮炎24伴糜烂   25渗出   26痂   27鳞屑   28苔藓样变   29萎缩   30抓痕   31无   32其他")
    @Column(name = "event_process_4", nullable = true)
    private String eventProcess4;


    @Comment("皮损形态其他")
    @Column(name = "event_process_41", nullable = true)
    private String eventProcess41;


    @Comment("其他损害：1神经系统  2全身性  3肾损害  4精神障碍  5无   6其他")
    @Column(name = "event_process_5", nullable = true)
    private String eventProcess5;


    @Comment("其他损害其他")
    @Column(name = "event_process_51", nullable = true)
    private String eventProcess51;


    @Comment("过程描述补充说明")
    @Column(name = "event_process_remark", nullable = true)
    private String eventProcessRemark;


    @Comment("初步判断1化妆品接触性皮炎   2化妆品光感性皮炎  3化妆品皮肤色素异常   4化妆品痤疮 5化妆品唇炎5化妆品毛发损害6化妆品甲损害7化妆品荨麻疹 8激素依赖性皮炎  9其他")
    @Column(name = "preliminary_diag", nullable = true)
    private String preliminaryDiag;


    @Comment("初步判断其他")
    @Column(name = "preliminary_diag_other", nullable = true)
    private String preliminaryDiagOther;


    @Comment("事件结果:1痊愈；2好转；3未好转 4并发症，5其他")
    @Column(name = "event_result", nullable = true, length = 1)
    private String eventResult;


    @Comment("事件结果:1痊愈；2好转；3未好转 4并发症，5其他")
    @Column(name = "event_result_name", nullable = true, length = 16)
    private String eventResultName;


    @Comment("事件结果并发症")
    @Column(name = "result_complication", nullable = true)
    private String resultComplication;


    @Comment("事件结果其他")
    @Column(name = "result_other", nullable = true)
    private String resultOther;


    @Comment("化妆品：1怀疑 2并用")
    @Column(name = "use_cosmetics", nullable = true, length = 2)
    private String useCosmetics;


    @Comment("批准文号（备案号）")
    @Column(name = "gb_code", nullable = true, length = 128)
    private String gbCode;


    @Comment("化妆品名称")
    @Column(name = "cosmetics_name", nullable = true)
    private String cosmeticsName;


    @Comment("商标名")
    @Column(name = "brand_name", nullable = true)
    private String brandName;


    @Comment("通用名")
    @Column(name = "common_name", nullable = true)
    private String commonName;


    @Comment("属性名")
    @Column(name = "property_name", nullable = true)
    private String propertyName;


    @Comment("类别：1特殊2普通 3育发类4染发类5烫发类6脱毛类7美乳类8健美类9除臭类10祛斑类11防晒类12发用类13护肤类14美容修饰类15香水类16洗发17护发18养发19固发20美发21膏22霜23乳液24化妆用油25面膜26化妆水类27胭脂香粉28唇膏（护唇膏、唇彩、口红）29洁肤类（沐浴液、洗手液）30眼部用彩妆（眉笔、眼线笔、睫毛膏）31指（趾）甲用化妆品32香水类")
    @Column(name = "cosmetics_class", nullable = true)
    private String cosmeticsClass;


    @Comment("类别：1特殊2普通 3育发类4染发类5烫发类6脱毛类7美乳类8健美类9除臭类10祛斑类11防晒类12发用类13护肤类14美容修饰类15香水类16洗发17护发18养发19固发20美发21膏22霜23乳液24化妆用油25面膜26化妆水类27胭脂香粉28唇膏（护唇膏、唇彩、口红）29洁肤类（沐浴液、洗手液）30眼部用彩妆（眉笔、眼线笔、睫毛膏）31指（趾）甲用化妆品32香水类")
    @Column(name = "cosmetics_class_name", nullable = true)
    private String cosmeticsClassName;


    @Comment("生产厂家")
    @Column(name = "manufacture_firm", nullable = true)
    private String manufactureFirm;


    @Comment("生产批号")
    @Column(name = "batch_no", nullable = true, length = 16)
    private String batchNo;


    @Comment("产品来源：1商场（超市、专柜） 2网购   3美容美发机构   4其他")
    @Column(name = "product_source", nullable = true, length = 16)
    private String productSource;


    @Comment("产品来源：1商场（超市、专柜） 2网购   3美容美发机构   4其他")
    @Column(name = "product_source_name", nullable = true, length = 32)
    private String productSourceName;


    @Comment("购买地点")
    @Column(name = "buy_site", nullable = true)
    private String buySite;


    @Comment("化妆品有关斑贴试验0未做，1己做")
    @Column(name = "patch_test_1", nullable = true, length = 2)
    private String patchTest1;


    @Comment("化妆品斑贴试验己做：1原物斑贴实验2光斑贴试验")
    @Column(name = "patch_test_2", nullable = true, length = 2)
    private String patchTest2;


    @Comment("实验结果1阳性0阴性")
    @Column(name = "patch_test_3", nullable = false)
    private boolean patchTest3;


    @Comment("阳性实验结果")
    @Column(name = "patch_test_4", nullable = true)
    private String patchTest4;


    @Comment("欧标、澳标变应原系列斑贴试验：0未做，1己做")
    @Column(name = "allergen_patch_test_1", nullable = false)
    private boolean allergenPatchTest1;


    @Comment("欧标、澳标变应原系列斑贴试验己做结果：1有呈阳性受试，0无呈阳性受试物质")
    @Column(name = "allergen_patch_test_2", nullable = false)
    private boolean allergenPatchTest2;


    @Comment("1有呈阳性受试内容")
    @Column(name = "allergen_patch_test_3", nullable = true)
    private String allergenPatchTest3;


    @Comment("其他辅助检查：1有2无3不详")
    @Column(name = "other_aux_exam_1", nullable = true, length = 2)
    private String otherAuxExam1;


    @Comment("其他辅助检查（1有）名称")
    @Column(name = "other_aux_exam_2", nullable = true)
    private String otherAuxExam2;


    @Comment("其他辅助检查（1有）结果")
    @Column(name = "other_aux_exam_3", nullable = true)
    private String otherAuxExam3;


    @Comment("化妆品使用与不良反应出现有无合理的时间关系1有0无")
    @Column(name = "relevance_eval_1", nullable = false)
    private boolean relevanceEval1;


    @Comment("停止使用化妆品后不良反应是否消失或减轻1是0否2不明")
    @Column(name = "relevance_eval_2", nullable = true, length = 2)
    private String relevanceEval2;


    @Comment("再次使用可疑化妆品是否再次出现同样反应1是0否2未再使用")
    @Column(name = "relevance_eval_3", nullable = true, length = 16)
    private String relevanceEval3;


    @Comment("不良反应是否可用其他接触物的作用，患者/消费者的病情进展解释1是0否")
    @Column(name = "relevance_eval_4", nullable = true, length = 16)
    private String relevanceEval4;


    @Comment("	斑贴试验结果是否可以说明化妆品使用与不良反应出现有明显的相关性1是0否2不明3未做")
    @Column(name = "relevance_eval_5", nullable = true, length = 16)
    private String relevanceEval5;


    @Comment("	报告人评价结果: 1肯定   2很可能  3可能  4可能无关  5待评价  6无法评价")
    @Column(name = "report_remark", nullable = true, length = 16)
    private String reportRemark;


    @Comment("报告人")
    @Column(name = "report_user", nullable = true, length = 16)
    private String reportUser;


    @Comment("报告人名称")
    @Column(name = "report_user_name", nullable = true, length = 32)
    private String reportUserName;


    @Comment("报告人联系电话")
    @Column(name = "report_tel", nullable = true, length = 24)
    private String reportTel;


    @Comment("报告时间")
    @Column(name = "report_date", nullable = true)
    private LocalDateTime reportDate;


    @Comment("报告人职业: 1医护人员  2美容美发师  3销售人员  4其他")
    @Column(name = "report_user_work", nullable = true, length = 16)
    private String reportUserWork;


    @Comment("报告人职业名称: 1医护人员  2美容美发师  3销售人员 4其他")
    @Column(name = "report_user_work_name", nullable = true, length = 32)
    private String reportUserWorkName;


    @Comment("报告单位名称")
    @Column(name = "report_unit", nullable = true, length = 64)
    private String reportUnit;


    @Comment("备注")
    @Column(name = "remark", nullable = true)
    private String remark;


    @Comment("附件（汉字说明）")
    @Column(name = "attachment_name", nullable = true)
    private String attachmentName;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    public String getId() {
    	return id;
    }

    protected void setId(String id) {
    	this.id = id;
    }

    public String getEventReportId() {
    	return eventReportId;
    }

    protected void setEventReportId(String eventReportId) {
    	this.eventReportId = eventReportId;
    }

    public String getReportType() {
    	return reportType;
    }

    protected void setReportType(String reportType) {
    	this.reportType = reportType;
    }

    public String getReportTypeName() {
    	return reportTypeName;
    }

    protected void setReportTypeName(String reportTypeName) {
    	this.reportTypeName = reportTypeName;
    }

    public String getReportUnitCategory() {
    	return reportUnitCategory;
    }

    protected void setReportUnitCategory(String reportUnitCategory) {
    	this.reportUnitCategory = reportUnitCategory;
    }

    public String getReportUnitCategoryName() {
    	return reportUnitCategoryName;
    }

    protected void setReportUnitCategoryName(String reportUnitCategoryName) {
    	this.reportUnitCategoryName = reportUnitCategoryName;
    }

    public String getPatType() {
    	return patType;
    }

    protected void setPatType(String patType) {
    	this.patType = patType;
    }

    public String getInpatientCode() {
    	return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
    	this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    protected void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public String getPatName() {
    	return patName;
    }

    protected void setPatName(String patName) {
    	this.patName = patName;
    }

    public String getSex() {
    	return sex;
    }

    protected void setSex(String sex) {
    	this.sex = sex;
    }

    public String getNation() {
    	return nation;
    }

    protected void setNation(String nation) {
    	this.nation = nation;
    }

    public LocalDateTime getBirthDate() {
    	return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
    	this.birthDate = birthDate;
    }

    public String getContactTel() {
    	return contactTel;
    }

    protected void setContactTel(String contactTel) {
    	this.contactTel = contactTel;
    }

    public String getCzProvince() {
    	return czProvince;
    }

    protected void setCzProvince(String czProvince) {
    	this.czProvince = czProvince;
    }

    public String getCzProvinceName() {
    	return czProvinceName;
    }

    protected void setCzProvinceName(String czProvinceName) {
    	this.czProvinceName = czProvinceName;
    }

    public String getCzCity() {
    	return czCity;
    }

    protected void setCzCity(String czCity) {
    	this.czCity = czCity;
    }

    public String getCzCityName() {
    	return czCityName;
    }

    protected void setCzCityName(String czCityName) {
    	this.czCityName = czCityName;
    }

    public String getCzCounty() {
    	return czCounty;
    }

    protected void setCzCounty(String czCounty) {
    	this.czCounty = czCounty;
    }

    public String getCzCountyName() {
    	return czCountyName;
    }

    protected void setCzCountyName(String czCountyName) {
    	this.czCountyName = czCountyName;
    }

    public String getCzHomeJd() {
    	return czHomeJd;
    }

    protected void setCzHomeJd(String czHomeJd) {
    	this.czHomeJd = czHomeJd;
    }

    public String getCzHomeJdName() {
    	return czHomeJdName;
    }

    protected void setCzHomeJdName(String czHomeJdName) {
    	this.czHomeJdName = czHomeJdName;
    }

    public String getCzVillage() {
    	return czVillage;
    }

    protected void setCzVillage(String czVillage) {
    	this.czVillage = czVillage;
    }

    public String getCzVillageName() {
    	return czVillageName;
    }

    protected void setCzVillageName(String czVillageName) {
    	this.czVillageName = czVillageName;
    }

    public String getIsCosmeticsAllergy() {
    	return isCosmeticsAllergy;
    }

    protected void setIsCosmeticsAllergy(String isCosmeticsAllergy) {
    	this.isCosmeticsAllergy = isCosmeticsAllergy;
    }

    public String getCosmeticsAllergyRemark() {
    	return cosmeticsAllergyRemark;
    }

    protected void setCosmeticsAllergyRemark(String cosmeticsAllergyRemark) {
    	this.cosmeticsAllergyRemark = cosmeticsAllergyRemark;
    }

    public String getIsDrugAllergy() {
    	return isDrugAllergy;
    }

    protected void setIsDrugAllergy(String isDrugAllergy) {
    	this.isDrugAllergy = isDrugAllergy;
    }

    public String getDrugAllergyRemark() {
    	return drugAllergyRemark;
    }

    protected void setDrugAllergyRemark(String drugAllergyRemark) {
    	this.drugAllergyRemark = drugAllergyRemark;
    }

    public String getIsFoodAllergy() {
    	return isFoodAllergy;
    }

    protected void setIsFoodAllergy(String isFoodAllergy) {
    	this.isFoodAllergy = isFoodAllergy;
    }

    public String getFoodAllergyRemark() {
    	return foodAllergyRemark;
    }

    protected void setFoodAllergyRemark(String foodAllergyRemark) {
    	this.foodAllergyRemark = foodAllergyRemark;
    }

    public String getIsOtherAllergy() {
    	return isOtherAllergy;
    }

    protected void setIsOtherAllergy(String isOtherAllergy) {
    	this.isOtherAllergy = isOtherAllergy;
    }

    public String getOtherAllergyRemark() {
    	return otherAllergyRemark;
    }

    protected void setOtherAllergyRemark(String otherAllergyRemark) {
    	this.otherAllergyRemark = otherAllergyRemark;
    }

    public LocalDateTime getBeginDate() {
    	return beginDate;
    }

    protected void setBeginDate(LocalDateTime beginDate) {
    	this.beginDate = beginDate;
    }

    public LocalDateTime getEventDate() {
    	return eventDate;
    }

    protected void setEventDate(LocalDateTime eventDate) {
    	this.eventDate = eventDate;
    }

    public LocalDateTime getEndDate() {
    	return endDate;
    }

    protected void setEndDate(LocalDateTime endDate) {
    	this.endDate = endDate;
    }

    public String getEventProcess1() {
    	return eventProcess1;
    }

    protected void setEventProcess1(String eventProcess1) {
    	this.eventProcess1 = eventProcess1;
    }

    public Integer getEventProcess11() {
    	return eventProcess11;
    }

    protected void setEventProcess11(Integer eventProcess11) {
    	this.eventProcess11 = eventProcess11;
    }

    public String getEventProcess12() {
    	return eventProcess12;
    }

    protected void setEventProcess12(String eventProcess12) {
    	this.eventProcess12 = eventProcess12;
    }

    public String getEventProcess2() {
    	return eventProcess2;
    }

    protected void setEventProcess2(String eventProcess2) {
    	this.eventProcess2 = eventProcess2;
    }

    public String getEventProcess21() {
    	return eventProcess21;
    }

    protected void setEventProcess21(String eventProcess21) {
    	this.eventProcess21 = eventProcess21;
    }

    public String getEventProcess3() {
    	return eventProcess3;
    }

    protected void setEventProcess3(String eventProcess3) {
    	this.eventProcess3 = eventProcess3;
    }

    public String getEventProcess31() {
    	return eventProcess31;
    }

    protected void setEventProcess31(String eventProcess31) {
    	this.eventProcess31 = eventProcess31;
    }

    public String getEventProcess4() {
    	return eventProcess4;
    }

    protected void setEventProcess4(String eventProcess4) {
    	this.eventProcess4 = eventProcess4;
    }

    public String getEventProcess41() {
    	return eventProcess41;
    }

    protected void setEventProcess41(String eventProcess41) {
    	this.eventProcess41 = eventProcess41;
    }

    public String getEventProcess5() {
    	return eventProcess5;
    }

    protected void setEventProcess5(String eventProcess5) {
    	this.eventProcess5 = eventProcess5;
    }

    public String getEventProcess51() {
    	return eventProcess51;
    }

    protected void setEventProcess51(String eventProcess51) {
    	this.eventProcess51 = eventProcess51;
    }

    public String getEventProcessRemark() {
    	return eventProcessRemark;
    }

    protected void setEventProcessRemark(String eventProcessRemark) {
    	this.eventProcessRemark = eventProcessRemark;
    }

    public String getPreliminaryDiag() {
    	return preliminaryDiag;
    }

    protected void setPreliminaryDiag(String preliminaryDiag) {
    	this.preliminaryDiag = preliminaryDiag;
    }

    public String getPreliminaryDiagOther() {
    	return preliminaryDiagOther;
    }

    protected void setPreliminaryDiagOther(String preliminaryDiagOther) {
    	this.preliminaryDiagOther = preliminaryDiagOther;
    }

    public String getEventResult() {
    	return eventResult;
    }

    protected void setEventResult(String eventResult) {
    	this.eventResult = eventResult;
    }

    public String getEventResultName() {
    	return eventResultName;
    }

    protected void setEventResultName(String eventResultName) {
    	this.eventResultName = eventResultName;
    }

    public String getResultComplication() {
    	return resultComplication;
    }

    protected void setResultComplication(String resultComplication) {
    	this.resultComplication = resultComplication;
    }

    public String getResultOther() {
    	return resultOther;
    }

    protected void setResultOther(String resultOther) {
    	this.resultOther = resultOther;
    }

    public String getUseCosmetics() {
    	return useCosmetics;
    }

    protected void setUseCosmetics(String useCosmetics) {
    	this.useCosmetics = useCosmetics;
    }

    public String getGbCode() {
    	return gbCode;
    }

    protected void setGbCode(String gbCode) {
    	this.gbCode = gbCode;
    }

    public String getCosmeticsName() {
    	return cosmeticsName;
    }

    protected void setCosmeticsName(String cosmeticsName) {
    	this.cosmeticsName = cosmeticsName;
    }

    public String getBrandName() {
    	return brandName;
    }

    protected void setBrandName(String brandName) {
    	this.brandName = brandName;
    }

    public String getCommonName() {
    	return commonName;
    }

    protected void setCommonName(String commonName) {
    	this.commonName = commonName;
    }

    public String getPropertyName() {
    	return propertyName;
    }

    protected void setPropertyName(String propertyName) {
    	this.propertyName = propertyName;
    }

    public String getCosmeticsClass() {
    	return cosmeticsClass;
    }

    protected void setCosmeticsClass(String cosmeticsClass) {
    	this.cosmeticsClass = cosmeticsClass;
    }

    public String getCosmeticsClassName() {
    	return cosmeticsClassName;
    }

    protected void setCosmeticsClassName(String cosmeticsClassName) {
    	this.cosmeticsClassName = cosmeticsClassName;
    }

    public String getManufactureFirm() {
    	return manufactureFirm;
    }

    protected void setManufactureFirm(String manufactureFirm) {
    	this.manufactureFirm = manufactureFirm;
    }

    public String getBatchNo() {
    	return batchNo;
    }

    protected void setBatchNo(String batchNo) {
    	this.batchNo = batchNo;
    }

    public String getProductSource() {
    	return productSource;
    }

    protected void setProductSource(String productSource) {
    	this.productSource = productSource;
    }

    public String getProductSourceName() {
    	return productSourceName;
    }

    protected void setProductSourceName(String productSourceName) {
    	this.productSourceName = productSourceName;
    }

    public String getBuySite() {
    	return buySite;
    }

    protected void setBuySite(String buySite) {
    	this.buySite = buySite;
    }

    public String getPatchTest1() {
    	return patchTest1;
    }

    protected void setPatchTest1(String patchTest1) {
    	this.patchTest1 = patchTest1;
    }

    public String getPatchTest2() {
    	return patchTest2;
    }

    protected void setPatchTest2(String patchTest2) {
    	this.patchTest2 = patchTest2;
    }

    public boolean isPatchTest3() {
    	return patchTest3;
    }

    protected void setPatchTest3(boolean patchTest3) {
    	this.patchTest3 = patchTest3;
    }

    public String getPatchTest4() {
    	return patchTest4;
    }

    protected void setPatchTest4(String patchTest4) {
    	this.patchTest4 = patchTest4;
    }

    public boolean isAllergenPatchTest1() {
    	return allergenPatchTest1;
    }

    protected void setAllergenPatchTest1(boolean allergenPatchTest1) {
    	this.allergenPatchTest1 = allergenPatchTest1;
    }

    public boolean isAllergenPatchTest2() {
    	return allergenPatchTest2;
    }

    protected void setAllergenPatchTest2(boolean allergenPatchTest2) {
    	this.allergenPatchTest2 = allergenPatchTest2;
    }

    public String getAllergenPatchTest3() {
    	return allergenPatchTest3;
    }

    protected void setAllergenPatchTest3(String allergenPatchTest3) {
    	this.allergenPatchTest3 = allergenPatchTest3;
    }

    public String getOtherAuxExam1() {
    	return otherAuxExam1;
    }

    protected void setOtherAuxExam1(String otherAuxExam1) {
    	this.otherAuxExam1 = otherAuxExam1;
    }

    public String getOtherAuxExam2() {
    	return otherAuxExam2;
    }

    protected void setOtherAuxExam2(String otherAuxExam2) {
    	this.otherAuxExam2 = otherAuxExam2;
    }

    public String getOtherAuxExam3() {
    	return otherAuxExam3;
    }

    protected void setOtherAuxExam3(String otherAuxExam3) {
    	this.otherAuxExam3 = otherAuxExam3;
    }

    public boolean isRelevanceEval1() {
    	return relevanceEval1;
    }

    protected void setRelevanceEval1(boolean relevanceEval1) {
    	this.relevanceEval1 = relevanceEval1;
    }

    public String getRelevanceEval2() {
    	return relevanceEval2;
    }

    protected void setRelevanceEval2(String relevanceEval2) {
    	this.relevanceEval2 = relevanceEval2;
    }

    public String getRelevanceEval3() {
    	return relevanceEval3;
    }

    protected void setRelevanceEval3(String relevanceEval3) {
    	this.relevanceEval3 = relevanceEval3;
    }

    public String getRelevanceEval4() {
    	return relevanceEval4;
    }

    protected void setRelevanceEval4(String relevanceEval4) {
    	this.relevanceEval4 = relevanceEval4;
    }

    public String getRelevanceEval5() {
    	return relevanceEval5;
    }

    protected void setRelevanceEval5(String relevanceEval5) {
    	this.relevanceEval5 = relevanceEval5;
    }

    public String getReportRemark() {
    	return reportRemark;
    }

    protected void setReportRemark(String reportRemark) {
    	this.reportRemark = reportRemark;
    }

    public String getReportUser() {
    	return reportUser;
    }

    protected void setReportUser(String reportUser) {
    	this.reportUser = reportUser;
    }

    public String getReportUserName() {
    	return reportUserName;
    }

    protected void setReportUserName(String reportUserName) {
    	this.reportUserName = reportUserName;
    }

    public String getReportTel() {
    	return reportTel;
    }

    protected void setReportTel(String reportTel) {
    	this.reportTel = reportTel;
    }

    public LocalDateTime getReportDate() {
    	return reportDate;
    }

    protected void setReportDate(LocalDateTime reportDate) {
    	this.reportDate = reportDate;
    }

    public String getReportUserWork() {
    	return reportUserWork;
    }

    protected void setReportUserWork(String reportUserWork) {
    	this.reportUserWork = reportUserWork;
    }

    public String getReportUserWorkName() {
    	return reportUserWorkName;
    }

    protected void setReportUserWorkName(String reportUserWorkName) {
    	this.reportUserWorkName = reportUserWorkName;
    }

    public String getReportUnit() {
    	return reportUnit;
    }

    protected void setReportUnit(String reportUnit) {
    	this.reportUnit = reportUnit;
    }

    public String getRemark() {
    	return remark;
    }

    protected void setRemark(String remark) {
    	this.remark = remark;
    }

    public String getAttachmentName() {
    	return attachmentName;
    }

    protected void setAttachmentName(String attachmentName) {
    	this.attachmentName = attachmentName;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisAdvEventCosmetics other = (CisAdvEventCosmetics) obj;
		return Objects.equals(id, other.id);
	}

    public CisAdvEventCosmetics create(CisAdvEventCosmeticsNto cisAdvEventCosmeticsNto) {
        Assert.notNull(cisAdvEventCosmeticsNto, "参数cisAdvEventCosmeticsNto不能为空！");

        setId(cisAdvEventCosmeticsNto.getId());
        setEventReportId(cisAdvEventCosmeticsNto.getEventReportId());
        setReportType(cisAdvEventCosmeticsNto.getReportType());
        setReportTypeName(cisAdvEventCosmeticsNto.getReportTypeName());
        setReportUnitCategory(cisAdvEventCosmeticsNto.getReportUnitCategory());
        setReportUnitCategoryName(cisAdvEventCosmeticsNto.getReportUnitCategoryName());
        setPatType(cisAdvEventCosmeticsNto.getPatType());
        setInpatientCode(cisAdvEventCosmeticsNto.getInpatientCode());
        setVisitCode(cisAdvEventCosmeticsNto.getVisitCode());
        setPatName(cisAdvEventCosmeticsNto.getPatName());
        setSex(cisAdvEventCosmeticsNto.getSex());
        setNation(cisAdvEventCosmeticsNto.getNation());
        setBirthDate(cisAdvEventCosmeticsNto.getBirthDate());
        setContactTel(cisAdvEventCosmeticsNto.getContactTel());
        setCzProvince(cisAdvEventCosmeticsNto.getCzProvince());
        setCzProvinceName(cisAdvEventCosmeticsNto.getCzProvinceName());
        setCzCity(cisAdvEventCosmeticsNto.getCzCity());
        setCzCityName(cisAdvEventCosmeticsNto.getCzCityName());
        setCzCounty(cisAdvEventCosmeticsNto.getCzCounty());
        setCzCountyName(cisAdvEventCosmeticsNto.getCzCountyName());
        setCzHomeJd(cisAdvEventCosmeticsNto.getCzHomeJd());
        setCzHomeJdName(cisAdvEventCosmeticsNto.getCzHomeJdName());
        setCzVillage(cisAdvEventCosmeticsNto.getCzVillage());
        setCzVillageName(cisAdvEventCosmeticsNto.getCzVillageName());
        setIsCosmeticsAllergy(cisAdvEventCosmeticsNto.getIsCosmeticsAllergy());
        setCosmeticsAllergyRemark(cisAdvEventCosmeticsNto.getCosmeticsAllergyRemark());
        setIsDrugAllergy(cisAdvEventCosmeticsNto.getIsDrugAllergy());
        setDrugAllergyRemark(cisAdvEventCosmeticsNto.getDrugAllergyRemark());
        setIsFoodAllergy(cisAdvEventCosmeticsNto.getIsFoodAllergy());
        setFoodAllergyRemark(cisAdvEventCosmeticsNto.getFoodAllergyRemark());
        setIsOtherAllergy(cisAdvEventCosmeticsNto.getIsOtherAllergy());
        setOtherAllergyRemark(cisAdvEventCosmeticsNto.getOtherAllergyRemark());
        setBeginDate(cisAdvEventCosmeticsNto.getBeginDate());
        setEventDate(cisAdvEventCosmeticsNto.getEventDate());
        setEndDate(cisAdvEventCosmeticsNto.getEndDate());
        setEventProcess1(cisAdvEventCosmeticsNto.getEventProcess1());
        setEventProcess11(cisAdvEventCosmeticsNto.getEventProcess11());
        setEventProcess12(cisAdvEventCosmeticsNto.getEventProcess12());
        setEventProcess2(cisAdvEventCosmeticsNto.getEventProcess2());
        setEventProcess21(cisAdvEventCosmeticsNto.getEventProcess21());
        setEventProcess3(cisAdvEventCosmeticsNto.getEventProcess3());
        setEventProcess31(cisAdvEventCosmeticsNto.getEventProcess31());
        setEventProcess4(cisAdvEventCosmeticsNto.getEventProcess4());
        setEventProcess41(cisAdvEventCosmeticsNto.getEventProcess41());
        setEventProcess5(cisAdvEventCosmeticsNto.getEventProcess5());
        setEventProcess51(cisAdvEventCosmeticsNto.getEventProcess51());
        setEventProcessRemark(cisAdvEventCosmeticsNto.getEventProcessRemark());
        setPreliminaryDiag(cisAdvEventCosmeticsNto.getPreliminaryDiag());
        setPreliminaryDiagOther(cisAdvEventCosmeticsNto.getPreliminaryDiagOther());
        setEventResult(cisAdvEventCosmeticsNto.getEventResult());
        setEventResultName(cisAdvEventCosmeticsNto.getEventResultName());
        setResultComplication(cisAdvEventCosmeticsNto.getResultComplication());
        setResultOther(cisAdvEventCosmeticsNto.getResultOther());
        setUseCosmetics(cisAdvEventCosmeticsNto.getUseCosmetics());
        setGbCode(cisAdvEventCosmeticsNto.getGbCode());
        setCosmeticsName(cisAdvEventCosmeticsNto.getCosmeticsName());
        setBrandName(cisAdvEventCosmeticsNto.getBrandName());
        setCommonName(cisAdvEventCosmeticsNto.getCommonName());
        setPropertyName(cisAdvEventCosmeticsNto.getPropertyName());
        setCosmeticsClass(cisAdvEventCosmeticsNto.getCosmeticsClass());
        setCosmeticsClassName(cisAdvEventCosmeticsNto.getCosmeticsClassName());
        setManufactureFirm(cisAdvEventCosmeticsNto.getManufactureFirm());
        setBatchNo(cisAdvEventCosmeticsNto.getBatchNo());
        setProductSource(cisAdvEventCosmeticsNto.getProductSource());
        setProductSourceName(cisAdvEventCosmeticsNto.getProductSourceName());
        setBuySite(cisAdvEventCosmeticsNto.getBuySite());
        setPatchTest1(cisAdvEventCosmeticsNto.getPatchTest1());
        setPatchTest2(cisAdvEventCosmeticsNto.getPatchTest2());
        setPatchTest3(cisAdvEventCosmeticsNto.isPatchTest3());
        setPatchTest4(cisAdvEventCosmeticsNto.getPatchTest4());
        setAllergenPatchTest1(cisAdvEventCosmeticsNto.isAllergenPatchTest1());
        setAllergenPatchTest2(cisAdvEventCosmeticsNto.isAllergenPatchTest2());
        setAllergenPatchTest3(cisAdvEventCosmeticsNto.getAllergenPatchTest3());
        setOtherAuxExam1(cisAdvEventCosmeticsNto.getOtherAuxExam1());
        setOtherAuxExam2(cisAdvEventCosmeticsNto.getOtherAuxExam2());
        setOtherAuxExam3(cisAdvEventCosmeticsNto.getOtherAuxExam3());
        setRelevanceEval1(cisAdvEventCosmeticsNto.isRelevanceEval1());
        setRelevanceEval2(cisAdvEventCosmeticsNto.getRelevanceEval2());
        setRelevanceEval3(cisAdvEventCosmeticsNto.getRelevanceEval3());
        setRelevanceEval4(cisAdvEventCosmeticsNto.getRelevanceEval4());
        setRelevanceEval5(cisAdvEventCosmeticsNto.getRelevanceEval5());
        setReportRemark(cisAdvEventCosmeticsNto.getReportRemark());
        setReportUser(cisAdvEventCosmeticsNto.getReportUser());
        setReportUserName(cisAdvEventCosmeticsNto.getReportUserName());
        setReportTel(cisAdvEventCosmeticsNto.getReportTel());
        setReportDate(cisAdvEventCosmeticsNto.getReportDate());
        setReportUserWork(cisAdvEventCosmeticsNto.getReportUserWork());
        setReportUserWorkName(cisAdvEventCosmeticsNto.getReportUserWorkName());
        setReportUnit(cisAdvEventCosmeticsNto.getReportUnit());
        setRemark(cisAdvEventCosmeticsNto.getRemark());
        setAttachmentName(cisAdvEventCosmeticsNto.getAttachmentName());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisAdvEventCosmeticsEto cisAdvEventCosmeticsEto) {
        setEventReportId(cisAdvEventCosmeticsEto.getEventReportId());
        setReportType(cisAdvEventCosmeticsEto.getReportType());
        setReportTypeName(cisAdvEventCosmeticsEto.getReportTypeName());
        setReportUnitCategory(cisAdvEventCosmeticsEto.getReportUnitCategory());
        setReportUnitCategoryName(cisAdvEventCosmeticsEto.getReportUnitCategoryName());
        setPatType(cisAdvEventCosmeticsEto.getPatType());
        setInpatientCode(cisAdvEventCosmeticsEto.getInpatientCode());
        setVisitCode(cisAdvEventCosmeticsEto.getVisitCode());
        setPatName(cisAdvEventCosmeticsEto.getPatName());
        setSex(cisAdvEventCosmeticsEto.getSex());
        setNation(cisAdvEventCosmeticsEto.getNation());
        setBirthDate(cisAdvEventCosmeticsEto.getBirthDate());
        setContactTel(cisAdvEventCosmeticsEto.getContactTel());
        setCzProvince(cisAdvEventCosmeticsEto.getCzProvince());
        setCzProvinceName(cisAdvEventCosmeticsEto.getCzProvinceName());
        setCzCity(cisAdvEventCosmeticsEto.getCzCity());
        setCzCityName(cisAdvEventCosmeticsEto.getCzCityName());
        setCzCounty(cisAdvEventCosmeticsEto.getCzCounty());
        setCzCountyName(cisAdvEventCosmeticsEto.getCzCountyName());
        setCzHomeJd(cisAdvEventCosmeticsEto.getCzHomeJd());
        setCzHomeJdName(cisAdvEventCosmeticsEto.getCzHomeJdName());
        setCzVillage(cisAdvEventCosmeticsEto.getCzVillage());
        setCzVillageName(cisAdvEventCosmeticsEto.getCzVillageName());
        setIsCosmeticsAllergy(cisAdvEventCosmeticsEto.getIsCosmeticsAllergy());
        setCosmeticsAllergyRemark(cisAdvEventCosmeticsEto.getCosmeticsAllergyRemark());
        setIsDrugAllergy(cisAdvEventCosmeticsEto.getIsDrugAllergy());
        setDrugAllergyRemark(cisAdvEventCosmeticsEto.getDrugAllergyRemark());
        setIsFoodAllergy(cisAdvEventCosmeticsEto.getIsFoodAllergy());
        setFoodAllergyRemark(cisAdvEventCosmeticsEto.getFoodAllergyRemark());
        setIsOtherAllergy(cisAdvEventCosmeticsEto.getIsOtherAllergy());
        setOtherAllergyRemark(cisAdvEventCosmeticsEto.getOtherAllergyRemark());
        setBeginDate(cisAdvEventCosmeticsEto.getBeginDate());
        setEventDate(cisAdvEventCosmeticsEto.getEventDate());
        setEndDate(cisAdvEventCosmeticsEto.getEndDate());
        setEventProcess1(cisAdvEventCosmeticsEto.getEventProcess1());
        setEventProcess11(cisAdvEventCosmeticsEto.getEventProcess11());
        setEventProcess12(cisAdvEventCosmeticsEto.getEventProcess12());
        setEventProcess2(cisAdvEventCosmeticsEto.getEventProcess2());
        setEventProcess21(cisAdvEventCosmeticsEto.getEventProcess21());
        setEventProcess3(cisAdvEventCosmeticsEto.getEventProcess3());
        setEventProcess31(cisAdvEventCosmeticsEto.getEventProcess31());
        setEventProcess4(cisAdvEventCosmeticsEto.getEventProcess4());
        setEventProcess41(cisAdvEventCosmeticsEto.getEventProcess41());
        setEventProcess5(cisAdvEventCosmeticsEto.getEventProcess5());
        setEventProcess51(cisAdvEventCosmeticsEto.getEventProcess51());
        setEventProcessRemark(cisAdvEventCosmeticsEto.getEventProcessRemark());
        setPreliminaryDiag(cisAdvEventCosmeticsEto.getPreliminaryDiag());
        setPreliminaryDiagOther(cisAdvEventCosmeticsEto.getPreliminaryDiagOther());
        setEventResult(cisAdvEventCosmeticsEto.getEventResult());
        setEventResultName(cisAdvEventCosmeticsEto.getEventResultName());
        setResultComplication(cisAdvEventCosmeticsEto.getResultComplication());
        setResultOther(cisAdvEventCosmeticsEto.getResultOther());
        setUseCosmetics(cisAdvEventCosmeticsEto.getUseCosmetics());
        setGbCode(cisAdvEventCosmeticsEto.getGbCode());
        setCosmeticsName(cisAdvEventCosmeticsEto.getCosmeticsName());
        setBrandName(cisAdvEventCosmeticsEto.getBrandName());
        setCommonName(cisAdvEventCosmeticsEto.getCommonName());
        setPropertyName(cisAdvEventCosmeticsEto.getPropertyName());
        setCosmeticsClass(cisAdvEventCosmeticsEto.getCosmeticsClass());
        setCosmeticsClassName(cisAdvEventCosmeticsEto.getCosmeticsClassName());
        setManufactureFirm(cisAdvEventCosmeticsEto.getManufactureFirm());
        setBatchNo(cisAdvEventCosmeticsEto.getBatchNo());
        setProductSource(cisAdvEventCosmeticsEto.getProductSource());
        setProductSourceName(cisAdvEventCosmeticsEto.getProductSourceName());
        setBuySite(cisAdvEventCosmeticsEto.getBuySite());
        setPatchTest1(cisAdvEventCosmeticsEto.getPatchTest1());
        setPatchTest2(cisAdvEventCosmeticsEto.getPatchTest2());
        setPatchTest3(cisAdvEventCosmeticsEto.isPatchTest3());
        setPatchTest4(cisAdvEventCosmeticsEto.getPatchTest4());
        setAllergenPatchTest1(cisAdvEventCosmeticsEto.isAllergenPatchTest1());
        setAllergenPatchTest2(cisAdvEventCosmeticsEto.isAllergenPatchTest2());
        setAllergenPatchTest3(cisAdvEventCosmeticsEto.getAllergenPatchTest3());
        setOtherAuxExam1(cisAdvEventCosmeticsEto.getOtherAuxExam1());
        setOtherAuxExam2(cisAdvEventCosmeticsEto.getOtherAuxExam2());
        setOtherAuxExam3(cisAdvEventCosmeticsEto.getOtherAuxExam3());
        setRelevanceEval1(cisAdvEventCosmeticsEto.isRelevanceEval1());
        setRelevanceEval2(cisAdvEventCosmeticsEto.getRelevanceEval2());
        setRelevanceEval3(cisAdvEventCosmeticsEto.getRelevanceEval3());
        setRelevanceEval4(cisAdvEventCosmeticsEto.getRelevanceEval4());
        setRelevanceEval5(cisAdvEventCosmeticsEto.getRelevanceEval5());
        setReportRemark(cisAdvEventCosmeticsEto.getReportRemark());
        setReportUser(cisAdvEventCosmeticsEto.getReportUser());
        setReportUserName(cisAdvEventCosmeticsEto.getReportUserName());
        setReportTel(cisAdvEventCosmeticsEto.getReportTel());
        setReportDate(cisAdvEventCosmeticsEto.getReportDate());
        setReportUserWork(cisAdvEventCosmeticsEto.getReportUserWork());
        setReportUserWorkName(cisAdvEventCosmeticsEto.getReportUserWorkName());
        setReportUnit(cisAdvEventCosmeticsEto.getReportUnit());
        setRemark(cisAdvEventCosmeticsEto.getRemark());
        setAttachmentName(cisAdvEventCosmeticsEto.getAttachmentName());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

    public static Optional<CisAdvEventCosmetics> getCisAdvEventCosmeticsById(String id) {
		return dao().findById(id);
	}

	public static List<CisAdvEventCosmetics> getCisAdvEventCosmeticses(CisAdvEventCosmeticsQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
	}

	public static Page<CisAdvEventCosmetics> getCisAdvEventCosmeticsPage(CisAdvEventCosmeticsQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
	}


	/**
	 * @generated
	 */
    private static Specification<CisAdvEventCosmetics> getSpecification(CisAdvEventCosmeticsQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
        	if(StringUtils.isNotBlank(qto.getEventReportId())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventReportId"), qto.getEventReportId()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportType"), qto.getReportType()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportTypeName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportTypeName"), qto.getReportTypeName()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUnitCategory())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUnitCategory"), qto.getReportUnitCategory()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUnitCategoryName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUnitCategoryName"), qto.getReportUnitCategoryName()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatType())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patType"), qto.getPatType()));
        	}
        	if(StringUtils.isNotBlank(qto.getInpatientCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getVisitCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patName"), qto.getPatName()));
        	}
        	if(StringUtils.isNotBlank(qto.getSex())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
        	}
        	if(StringUtils.isNotBlank(qto.getNation())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("nation"), qto.getNation()));
        	}
    		if(qto.getBirthDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getContactTel())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("contactTel"), qto.getContactTel()));
        	}
        	if(StringUtils.isNotBlank(qto.getCzProvince())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("czProvince"), qto.getCzProvince()));
        	}
        	if(StringUtils.isNotBlank(qto.getCzProvinceName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("czProvinceName"), qto.getCzProvinceName()));
        	}
        	if(StringUtils.isNotBlank(qto.getCzCity())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("czCity"), qto.getCzCity()));
        	}
        	if(StringUtils.isNotBlank(qto.getCzCityName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("czCityName"), qto.getCzCityName()));
        	}
        	if(StringUtils.isNotBlank(qto.getCzCounty())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("czCounty"), qto.getCzCounty()));
        	}
        	if(StringUtils.isNotBlank(qto.getCzCountyName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("czCountyName"), qto.getCzCountyName()));
        	}
        	if(StringUtils.isNotBlank(qto.getCzHomeJd())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("czHomeJd"), qto.getCzHomeJd()));
        	}
        	if(StringUtils.isNotBlank(qto.getCzHomeJdName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("czHomeJdName"), qto.getCzHomeJdName()));
        	}
        	if(StringUtils.isNotBlank(qto.getCzVillage())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("czVillage"), qto.getCzVillage()));
        	}
        	if(StringUtils.isNotBlank(qto.getCzVillageName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("czVillageName"), qto.getCzVillageName()));
        	}
        	if(StringUtils.isNotBlank(qto.getIsCosmeticsAllergy())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isCosmeticsAllergy"), qto.getIsCosmeticsAllergy()));
        	}
        	if(StringUtils.isNotBlank(qto.getCosmeticsAllergyRemark())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cosmeticsAllergyRemark"), qto.getCosmeticsAllergyRemark()));
        	}
        	if(StringUtils.isNotBlank(qto.getIsDrugAllergy())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isDrugAllergy"), qto.getIsDrugAllergy()));
        	}
        	if(StringUtils.isNotBlank(qto.getDrugAllergyRemark())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugAllergyRemark"), qto.getDrugAllergyRemark()));
        	}
        	if(StringUtils.isNotBlank(qto.getIsFoodAllergy())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isFoodAllergy"), qto.getIsFoodAllergy()));
        	}
        	if(StringUtils.isNotBlank(qto.getFoodAllergyRemark())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("foodAllergyRemark"), qto.getFoodAllergyRemark()));
        	}
        	if(StringUtils.isNotBlank(qto.getIsOtherAllergy())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isOtherAllergy"), qto.getIsOtherAllergy()));
        	}
        	if(StringUtils.isNotBlank(qto.getOtherAllergyRemark())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherAllergyRemark"), qto.getOtherAllergyRemark()));
        	}
    		if(qto.getBeginDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("beginDate"), LocalDateUtil.beginOfDay(qto.getBeginDate()), LocalDateUtil.endOfDay(qto.getBeginDate())));
        	}
    		if(qto.getEventDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("eventDate"), LocalDateUtil.beginOfDay(qto.getEventDate()), LocalDateUtil.endOfDay(qto.getEventDate())));
        	}
    		if(qto.getEndDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("endDate"), LocalDateUtil.beginOfDay(qto.getEndDate()), LocalDateUtil.endOfDay(qto.getEndDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcess1())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess1"), qto.getEventProcess1()));
        	}
    		if(qto.getEventProcess11() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess11"), qto.getEventProcess11()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcess12())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess12"), qto.getEventProcess12()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcess2())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess2"), qto.getEventProcess2()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcess21())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess21"), qto.getEventProcess21()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcess3())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess3"), qto.getEventProcess3()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcess31())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess31"), qto.getEventProcess31()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcess4())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess4"), qto.getEventProcess4()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcess41())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess41"), qto.getEventProcess41()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcess5())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess5"), qto.getEventProcess5()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcess51())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcess51"), qto.getEventProcess51()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventProcessRemark())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventProcessRemark"), qto.getEventProcessRemark()));
        	}
        	if(StringUtils.isNotBlank(qto.getPreliminaryDiag())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("preliminaryDiag"), qto.getPreliminaryDiag()));
        	}
        	if(StringUtils.isNotBlank(qto.getPreliminaryDiagOther())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("preliminaryDiagOther"), qto.getPreliminaryDiagOther()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventResult())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventResult"), qto.getEventResult()));
        	}
        	if(StringUtils.isNotBlank(qto.getEventResultName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eventResultName"), qto.getEventResultName()));
        	}
        	if(StringUtils.isNotBlank(qto.getResultComplication())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("resultComplication"), qto.getResultComplication()));
        	}
        	if(StringUtils.isNotBlank(qto.getResultOther())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("resultOther"), qto.getResultOther()));
        	}
        	if(StringUtils.isNotBlank(qto.getUseCosmetics())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("useCosmetics"), qto.getUseCosmetics()));
        	}
        	if(StringUtils.isNotBlank(qto.getGbCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("gbCode"), qto.getGbCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getCosmeticsName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cosmeticsName"), qto.getCosmeticsName()));
        	}
        	if(StringUtils.isNotBlank(qto.getBrandName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("brandName"), qto.getBrandName()));
        	}
        	if(StringUtils.isNotBlank(qto.getCommonName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("commonName"), qto.getCommonName()));
        	}
        	if(StringUtils.isNotBlank(qto.getPropertyName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("propertyName"), qto.getPropertyName()));
        	}
        	if(StringUtils.isNotBlank(qto.getCosmeticsClass())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cosmeticsClass"), qto.getCosmeticsClass()));
        	}
        	if(StringUtils.isNotBlank(qto.getCosmeticsClassName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cosmeticsClassName"), qto.getCosmeticsClassName()));
        	}
        	if(StringUtils.isNotBlank(qto.getManufactureFirm())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("manufactureFirm"), qto.getManufactureFirm()));
        	}
        	if(StringUtils.isNotBlank(qto.getBatchNo())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("batchNo"), qto.getBatchNo()));
        	}
        	if(StringUtils.isNotBlank(qto.getProductSource())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("productSource"), qto.getProductSource()));
        	}
        	if(StringUtils.isNotBlank(qto.getProductSourceName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("productSourceName"), qto.getProductSourceName()));
        	}
        	if(StringUtils.isNotBlank(qto.getBuySite())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("buySite"), qto.getBuySite()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatchTest1())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patchTest1"), qto.getPatchTest1()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatchTest2())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patchTest2"), qto.getPatchTest2()));
        	}
    		if(qto.getPatchTest3() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patchTest3"), qto.getPatchTest3()));
        	}
        	if(StringUtils.isNotBlank(qto.getPatchTest4())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patchTest4"), qto.getPatchTest4()));
        	}
    		if(qto.getAllergenPatchTest1() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("allergenPatchTest1"), qto.getAllergenPatchTest1()));
        	}
    		if(qto.getAllergenPatchTest2() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("allergenPatchTest2"), qto.getAllergenPatchTest2()));
        	}
        	if(StringUtils.isNotBlank(qto.getAllergenPatchTest3())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("allergenPatchTest3"), qto.getAllergenPatchTest3()));
        	}
        	if(StringUtils.isNotBlank(qto.getOtherAuxExam1())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherAuxExam1"), qto.getOtherAuxExam1()));
        	}
        	if(StringUtils.isNotBlank(qto.getOtherAuxExam2())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherAuxExam2"), qto.getOtherAuxExam2()));
        	}
        	if(StringUtils.isNotBlank(qto.getOtherAuxExam3())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherAuxExam3"), qto.getOtherAuxExam3()));
        	}
    		if(qto.getRelevanceEval1() != null) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("relevanceEval1"), qto.getRelevanceEval1()));
        	}
        	if(StringUtils.isNotBlank(qto.getRelevanceEval2())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("relevanceEval2"), qto.getRelevanceEval2()));
        	}
        	if(StringUtils.isNotBlank(qto.getRelevanceEval3())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("relevanceEval3"), qto.getRelevanceEval3()));
        	}
        	if(StringUtils.isNotBlank(qto.getRelevanceEval4())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("relevanceEval4"), qto.getRelevanceEval4()));
        	}
        	if(StringUtils.isNotBlank(qto.getRelevanceEval5())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("relevanceEval5"), qto.getRelevanceEval5()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportRemark())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportRemark"), qto.getReportRemark()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUser())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUser"), qto.getReportUser()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUserName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUserName"), qto.getReportUserName()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportTel())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportTel"), qto.getReportTel()));
        	}
    		if(qto.getReportDate() != null) {
        		predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("reportDate"), LocalDateUtil.beginOfDay(qto.getReportDate()), LocalDateUtil.endOfDay(qto.getReportDate())));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUserWork())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUserWork"), qto.getReportUserWork()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUserWorkName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUserWorkName"), qto.getReportUserWorkName()));
        	}
        	if(StringUtils.isNotBlank(qto.getReportUnit())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUnit"), qto.getReportUnit()));
        	}
        	if(StringUtils.isNotBlank(qto.getRemark())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("remark"), qto.getRemark()));
        	}
        	if(StringUtils.isNotBlank(qto.getAttachmentName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("attachmentName"), qto.getAttachmentName()));
        	}
            return predicate;
        };
    }

    private static CisAdvEventCosmeticsRepository dao() {
		return SpringUtil.getBean(CisAdvEventCosmeticsRepository.class);
	}

}
