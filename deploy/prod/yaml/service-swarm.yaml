version: "3"
services:
  hip-cis-base-dc:
    image: jh-harbor.bjgoodwill.com/hip5.0/hip-cis-base-dc:latest
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - JDK_JAVA_OPTIONS="-Xmx2g
        -Xms2g
        -XX:MaxDirectMemorySize=256m
        -XX:+UseG1GC
        -XX:+UseCompressedOops
        -XX:+UseCompressedClassPointers
        -XX:+SegmentedCodeCache
        -XX:+PrintCommandLineFlags
        -XX:+ExplicitGCInvokesConcurrent
        -Duser.timezone=GMT+8
        -Ddruid.mysql.usePingMethod=false
        --illegal-access=deny"
    ports:
      - "8080:8080"
    networks:
      - hip
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
    deploy:
      mode: replicated
      replicas: 1
      endpoint_mode: vip
      labels:
        description: "hip-cis-base-dc"
      resources:
        limits:
          cpus: 1
          memory: 3072M
        reservations:
          cpus: '0.3'
          memory: 2048M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      placement:
        constraints:
          - "node.role==worker"
      update_config:
        parallelism: 2
        delay: 10s
        order: stop-first