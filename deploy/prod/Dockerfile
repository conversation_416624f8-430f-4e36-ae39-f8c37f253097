FROM jh-harbor.bjgoodwill.com/hip5.0/openjdk:17.0.2-dev

LABEL maintainer="baijie <<EMAIL>>"
ENV TZ="Asia/Shanghai"
ENV TERM=xterm

ENV LC_ALL=C.UTF-8
ENV LANG=C.UTF-8

# 添加版本环境变量
ARG HIP_SERVICE_VERSION
ENV HIP_SERVICE_VERSION=$HIP_SERVICE_VERSION

ADD /target/*.jar /app.jar

EXPOSE 8080

ENTRYPOINT ["sh", "-c", "java -Djava.security.egd=file:/dev/./urandom -Duser.timezone=Asia/Shanghai -jar /app.jar"]