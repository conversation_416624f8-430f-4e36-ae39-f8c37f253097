apiVersion: app.k8s.io/v1beta1
kind: Application
metadata:
  name: hip
  annotations:
    kubesphere.io/creator: hip
    servicemesh.kubesphere.io/enabled: "false"
  labels:
    app.kubernetes.io/compose: "true"
    app.kubernetes.io/name: hip
    app.kubernetes.io/version: v1
spec:
  addOwnerRef: true
  componentKinds:
    - group: ""
      kind: Service
    - group: apps
      kind: Deployment
    - group: apps
      kind: StatefulSet
    - group: extensions
      kind: Ingress
    - group: servicemesh.kubesphere.io
      kind: Strategy
    - group: servicemesh.kubesphere.io
      kind: ServicePolicy
  selector:
    matchLabels:
      app.kubernetes.io/name: hip
      app.kubernetes.io/version: v1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hip-cis-base-dc-v1
  annotations:
    servicemesh.kubesphere.io/enabled: "true"
  labels:
    app: hip-cis-base-dc
    app.kubernetes.io/name: hip
    app.kubernetes.io/version: v1
    version: v1
spec:
  selector:
    matchLabels:
      app: hip-cis-base-dc
      app.kubernetes.io/name: hip
      app.kubernetes.io/version: v1
      version: v1
  replicas: 1
  template:
    metadata:
      labels:
        app: hip-cis-base-dc
        app.kubernetes.io/name: hip
        app.kubernetes.io/version: v1
        version: v1
      annotations:
        sidecar.istio.io/inject: "false"
    spec:
      serviceAccountName: default
      imagePullSecrets:
        - name: registry-token
      containers:
        - name: hip-cis-base-dc
          image: jh-harbor.bjgoodwill.com/hip5.0/hip-cis-base-dc:latest
          resources:
            limits:
              cpu: 2
              memory: 3072Mi
            requests:
              cpu: 1
              memory: 2048Mi
          ports:
            - name: service-port
              containerPort: 8080
              protocol: TCP
          env:
            - name: HOST_ADDRESS
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: SPRING_CLOUD_KUBERNETES_ENABLED
              value: "true"
            - name: JDK_JAVA_OPTIONS
              value: '-javaagent:/opt/skywalking-agent/skywalking-agent.jar
                      -Dskywalking.agent.service_name=hip::hip-cis-base-dc
                      -Dskywalking.sampling.percentage=100
                      -Dskywalking.collector.backend_service=*************:11800
                      -Xmx2g
                      -Xms2g
                      -XX:MaxDirectMemorySize=256m
                      -XX:+UseG1GC
                      -XX:+UseCompressedOops
                      -XX:+UseCompressedClassPointers
                      -XX:+SegmentedCodeCache
                      -XX:+PrintCommandLineFlags
                      -XX:+ExplicitGCInvokesConcurrent
                      -Duser.timezone=GMT+8'
              # -XX:+PrintGC
            - name: SPRING_PROFILES_ACTIVE
              value: dev
          livenessProbe:
            httpGet:
              path: /health
              port: service-port
              scheme: HTTP
            initialDelaySeconds: 120
            timeoutSeconds: 3
            periodSeconds: 20
            successThreshold: 1
            failureThreshold: 5
          readinessProbe:
            httpGet:
              path: /health
              port: service-port
              scheme: HTTP
            initialDelaySeconds: 120
            timeoutSeconds: 3
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 5
---
apiVersion: v1
kind: Service
metadata:
  name: hip-cis-base-dc
  annotations:
    servicemesh.kubesphere.io/enabled: "false"
  labels:
    app: hip-cis-base-dc
    app.kubernetes.io/name: hip
    app.kubernetes.io/version: v1
    version: v1
spec:
  type: ClusterIP
  ports:
    - name: http-8080
      port: 8080
      targetPort: 8080
  selector:
    app: hip-cis-base-dc
    app.kubernetes.io/name: hip
    app.kubernetes.io/version: v1