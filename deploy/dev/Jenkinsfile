pipeline{

  agent any

  options {
      buildDiscarder(
          logRotator(
              numToKeepStr:'5',
              daysToKeepStr: '3',
              artifactNumToKeepStr: '5',
              artifactDaysToKeepStr: '3'
          )
      )
  }

  environment {
       SERVICE_NAME = 'hip-cis-base-dc'
       REGISTRY = 'jh-harbor.bjgoodwill.com'
       REGISTRY_NAMESPACE = 'hip5.0'
       K8S_NAMESPACE = 'hip-dev'
  }

  stages {

    stage('generate tag') {
        steps {
            script {
                env.commitid = sh(script: 'git rev-parse  --short HEAD ', returnStdout: true).trim()
                env.imageTag = "${commitid}.$BUILD_NUMBER"
            }
        }
    }

    stage('maven build') {
        steps {
            sh 'cd $WORKSPACE/ && mvn clean package -Dmaven.test.skip=true -s /etc/maven/settings-dev.xml'
            archiveArtifacts 'target/*.*'
        }
    }

    stage('build image') {
        steps {
            sh 'cd $WORKSPACE/ && docker build -f deploy/dev/Dockerfile --build-arg HIP_SERVICE_VERSION=${imageTag} -t $REGISTRY/$REGISTRY_NAMESPACE/$SERVICE_NAME:${imageTag} .'
            withCredentials([usernamePassword(passwordVariable : 'REGISTRY_PASSWORD', usernameVariable : 'REGISTRY_USERNAME', credentialsId : "registry-token")]) {
                 sh 'echo "$REGISTRY_PASSWORD" | docker login $REGISTRY -u "$REGISTRY_USERNAME" --password-stdin'
                 sh 'docker push $REGISTRY/$REGISTRY_NAMESPACE/$SERVICE_NAME:${imageTag}'
                 sh 'docker rmi $REGISTRY/$REGISTRY_NAMESPACE/$SERVICE_NAME:${imageTag}'
            }
        }
    }

    stage('deploy') {
        steps {
            sh "sed -i 's#latest#${imageTag}#g' deploy/dev/yaml/service-kubernetes.yaml"
            sh "kubectl apply -n $K8S_NAMESPACE -f deploy/dev/yaml/service-kubernetes.yaml"
            echo "Waiting for service $SERVICE_NAME to be updated..."
            timeout(time: 8, unit: 'MINUTES') {
                script {
                    sh 'kubectl rollout status deployment/$(printf "%s-v1" $SERVICE_NAME) -n $K8S_NAMESPACE'
                }
            }
        }
    }

  }

}