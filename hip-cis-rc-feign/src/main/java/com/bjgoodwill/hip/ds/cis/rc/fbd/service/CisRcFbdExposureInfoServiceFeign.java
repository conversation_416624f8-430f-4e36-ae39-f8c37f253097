package com.bjgoodwill.hip.ds.cis.rc.fbd.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisrc.name}", url = "${hip.domains.cisrc.url}", path = "/api/cisrc/fbd/cisRcFbdExposureInfo", contextId = "com.bjgoodwill.hip.ds.cis.rc.fbd.service.CisRcFbdExposureInfoServiceFeign")
public interface CisRcFbdExposureInfoServiceFeign extends CisRcFbdExposureInfoService {

}