package com.bjgoodwill.hip.ds.cis.rc.rcCard.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisrc.name}", url = "${hip.domains.cisrc.url}", path = "/api/cisrc/rcCard/cisRcFbdPatCase", contextId = "com.bjgoodwill.hip.ds.cis.rc.rcCard.service.CisRcFbdPatCaseServiceFeign")
public interface CisRcFbdPatCaseServiceFeign extends CisRcFbdPatCaseService {

}