package com.bjgoodwill.hip.ds.cis.cdr.orderlog.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.ExecLogEnum;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.repository.CisOrderClLogRepository;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisOrderClLogEto;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisOrderClLogNto;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisOrderClLogQto;
import com.bjgoodwill.hip.jpa.core.SnowflakeId;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "医嘱全闭环日志")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "visitType", discriminatorType = DiscriminatorType.STRING, length = 20)
@Table(name = "cis_order_cl_log", indexes = {}, uniqueConstraints = {})
public abstract class CisOrderClLog {

    @Id
    @SnowflakeId
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("医嘱id")
    @Column(name = "order_id", nullable = false, length = 50)
    private String orderId;


    @Comment("主索引")
    @Column(name = "pat_mi_code", nullable = false, length = 20)
    private String patMiCode;


    @Comment("流水号")
    @Column(name = "visit_code", nullable = false, length = 50)
    private String visitCode;


    @Comment("医嘱序号")
    @Column(name = "order_no", nullable = false)
    private Double orderNo;


    @Comment("医嘱名称")
    @Column(name = "order_name", nullable = true)
    private String orderName;


    @Enumerated(EnumType.STRING)
    @Comment("操作类型")
    @Column(name = "exec_log_type", nullable = true)
    private ExecLogEnum execLogType;


    @Comment("工作组")
    @Column(name = "org_code", nullable = false, length = 39)
    private String orgCode;


    @Comment("工作组名称")
    @Column(name = "org_name", nullable = true)
    private String orgName;


    @Comment("备注")
    @Column(name = "re_mark", nullable = true)
    private String reMark;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    private boolean deleted;

    public static Optional<CisOrderClLog> getCisOrderClLogById(String id) {
        return dao().findById(id);
    }

    public static List<CisOrderClLog> getCisOrderClLogs(CisOrderClLogQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisOrderClLog> getCisOrderClLogPage(CisOrderClLogQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static CisOrderClLog newInstanceByNto(CisOrderClLogNto cisOrderClLogNto) {
        try {
            return (CisOrderClLog) Class.forName("com.bjgoodwill.hip.ds.cis.cdr.orderlog.entity."
                    + StringUtils.removeEnd(cisOrderClLogNto.getClass().getSimpleName(), "Nto")).getConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @generated
     */
    private static Specification<CisOrderClLog> getSpecification(CisOrderClLogQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getOrderId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderId"), qto.getOrderId()));
            }
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (qto.getOrderNo() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderNo"), qto.getOrderNo()));
            }
            if (StringUtils.isNotBlank(qto.getOrderName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderName"), qto.getOrderName()));
            }
            if (qto.getExecLogType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("execLogType"), qto.getExecLogType()));
            }
            if (StringUtils.isNotBlank(qto.getOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgCode"), qto.getOrgCode()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            return predicate;
        };
    }

    private static CisOrderClLogRepository dao() {
        return SpringUtil.getBean(CisOrderClLogRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    protected void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    protected void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public Double getOrderNo() {
        return orderNo;
    }

    protected void setOrderNo(Double orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderName() {
        return orderName;
    }

    protected void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public ExecLogEnum getExecLogType() {
        return execLogType;
    }

    protected void setExecLogType(ExecLogEnum execLogType) {
        this.execLogType = execLogType;
    }

    public String getOrgCode() {
        return orgCode;
    }

    protected void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    protected void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getReMark() {
        return reMark;
    }

    protected void setReMark(String reMark) {
        this.reMark = reMark;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOrderClLog other = (CisOrderClLog) obj;
        return Objects.equals(id, other.id);
    }

    public CisOrderClLog create(CisOrderClLogNto cisOrderClLogNto) {
        Assert.notNull(cisOrderClLogNto, "参数cisOrderClLogNto不能为空！");

        setOrderId(cisOrderClLogNto.getOrderId());
        setPatMiCode(cisOrderClLogNto.getPatMiCode());
        setVisitCode(cisOrderClLogNto.getVisitCode());
        setOrderNo(cisOrderClLogNto.getOrderNo());
        setOrderName(cisOrderClLogNto.getOrderName());
        setExecLogType(cisOrderClLogNto.getExecLogType());
        setOrgCode(cisOrderClLogNto.getOrgCode());
        setOrgName(cisOrderClLogNto.getOrgName());
        setReMark(cisOrderClLogNto.getReMark());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setDeleted(false);
        return this;
    }

    public void update(CisOrderClLogEto cisOrderClLogEto) {
    }

    public void delete() {
        setDeleted(true);
    }

}
