package com.bjgoodwill.hip.ds.cis.cdr.diagnose.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.entity.CisCdrClinicDiagnose;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.service.CisCdrClinicDiagnoseService;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.service.internal.assembler.CisCdrClinicDiagnoseAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseEto;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseNto;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseQto;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.diagnose.service.CisCdrClinicDiagnoseService")
@RequestMapping(value = "/api/cdr/diagnose/cisCdrClinicDiagnose", produces = "application/json; charset=utf-8")
public class CisCdrClinicDiagnoseServiceImpl implements CisCdrClinicDiagnoseService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisCdrClinicDiagnoseTo> getCisCdrClinicDiagnoses(CisCdrClinicDiagnoseQto cisCdrClinicDiagnoseQto) {
        return CisCdrClinicDiagnoseAssembler.toTos(CisCdrClinicDiagnose.getCisCdrClinicDiagnoses(cisCdrClinicDiagnoseQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisCdrClinicDiagnoseTo> getCisCdrClinicDiagnosePage(CisCdrClinicDiagnoseQto cisCdrClinicDiagnoseQto) {
        Page<CisCdrClinicDiagnose> page = CisCdrClinicDiagnose.getCisCdrClinicDiagnosePage(cisCdrClinicDiagnoseQto);
        Page<CisCdrClinicDiagnoseTo> result = page.map(CisCdrClinicDiagnoseAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisCdrClinicDiagnoseTo createCisCdrClinicDiagnose(CisCdrClinicDiagnoseNto cisCdrClinicDiagnoseNto) {
        CisCdrClinicDiagnose cisCdrClinicDiagnose = new CisCdrClinicDiagnose();
        cisCdrClinicDiagnose = cisCdrClinicDiagnose.create(cisCdrClinicDiagnoseNto);
        return CisCdrClinicDiagnoseAssembler.toTo(cisCdrClinicDiagnose);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisCdrClinicDiagnose(String id, CisCdrClinicDiagnoseEto cisCdrClinicDiagnoseEto) {
        Optional<CisCdrClinicDiagnose> cisCdrClinicDiagnoseOptional = CisCdrClinicDiagnose.getCisCdrClinicDiagnoseById(id);
        cisCdrClinicDiagnoseOptional.ifPresent(cisCdrClinicDiagnose -> cisCdrClinicDiagnose.update(cisCdrClinicDiagnoseEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisCdrClinicDiagnose(String id) {
        Optional<CisCdrClinicDiagnose> cisCdrClinicDiagnoseOptional = CisCdrClinicDiagnose.getCisCdrClinicDiagnoseById(id);
        cisCdrClinicDiagnoseOptional.ifPresent(cisCdrClinicDiagnose -> cisCdrClinicDiagnose.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}