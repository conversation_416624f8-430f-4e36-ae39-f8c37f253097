package com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.repository;

import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.entity.CisAntimicrobialsSkinExecReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.repository.CisAntimicrobialsSkinExecReportRepository")
public interface CisAntimicrobialsSkinExecReportRepository extends JpaRepository<CisAntimicrobialsSkinExecReport, String>, JpaSpecificationExecutor<CisAntimicrobialsSkinExecReport> {

    List<CisAntimicrobialsSkinExecReport> findCisAntimicrobialsSkinExecReportsByVisitCode(String visitCode);

    boolean existsByExecPlanId(String execPlanId);
}