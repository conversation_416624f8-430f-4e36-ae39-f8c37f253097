package com.bjgoodwill.hip.ds.cis.cdr.report.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.report.entity.CisReportResult;
import com.bjgoodwill.hip.ds.cis.cdr.report.to.CisReportResultTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisReportResultAssembler {

    public static List<CisReportResultTo> toTos(List<CisReportResult> cisReportResults) {
        return toTos(cisReportResults, false);
    }

    public static List<CisReportResultTo> toTos(List<CisReportResult> cisReportResults, boolean withAllParts) {
        Assert.notNull(cisReportResults, "参数cisReportResults不能为空！");

        List<CisReportResultTo> tos = new ArrayList<>();
        for (CisReportResult cisReportResult : cisReportResults)
            tos.add(toTo(cisReportResult, withAllParts));
        return tos;
    }

    public static CisReportResultTo toTo(CisReportResult cisReportResult) {
        return toTo(cisReportResult, false);
    }

    /**
     * @generated
     */
    public static CisReportResultTo toTo(CisReportResult cisReportResult, boolean withAllParts) {
        if (cisReportResult == null)
            return null;
        CisReportResultTo to = new CisReportResultTo();
        to.setId(cisReportResult.getId());
        to.setOrderID(cisReportResult.getOrderID());
        to.setApplyId(cisReportResult.getApplyId());
        to.setReportId(cisReportResult.getReportId());
        to.setPatMiCode(cisReportResult.getPatMiCode());
        to.setVisitCode(cisReportResult.getVisitCode());
        to.setReportType(cisReportResult.getReportType());
        to.setServiceItemCode(cisReportResult.getServiceItemCode());
        to.setServiceItemName(cisReportResult.getServiceItemName());
        to.setExecOrgCode(cisReportResult.getExecOrgCode());
        to.setCreatedStaff(cisReportResult.getCreatedStaff());
        to.setCreatedStaffName(cisReportResult.getCreatedStaffName());
        to.setCreatedDate(cisReportResult.getCreatedDate());
        to.setReportDate(cisReportResult.getReportDate());
        to.setUpdatedStaff(cisReportResult.getUpdatedStaff());
        to.setUpdatedStaffName(cisReportResult.getUpdatedStaffName());
        to.setUpdatedDate(cisReportResult.getUpdatedDate());
        to.setDeleted(cisReportResult.isDeleted());
        to.setExecStaff(cisReportResult.getExecStaff());
        to.setExecStaffName(cisReportResult.getExecStaffName());
        to.setDeviceType(cisReportResult.getDeviceType());
        if (withAllParts) {
            to.setCisReportResultDetails(CisReportResultDetailAssembler.toTos(cisReportResult.getCisReportResultDetails()));
        }
        return to;
    }

}