package com.bjgoodwill.hip.ds.cis.cdr.report.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.ds.cis.cdr.report.repository.CisReportResultDgimgDetailRepository;
import com.bjgoodwill.hip.ds.cis.cdr.report.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "检查报告明细")
@DiscriminatorValue("0")
public class CisReportResultDgimgDetail extends CisReportResultDetail {

    @Comment("部位")
    @Column(name = "human_organs", nullable = true, length = 50)
    private String humanOrgans;

    @Comment("报告结果")
    @Column(name = "result_content", nullable = true, length = 1024)
    private String resultContent;

    public static Optional<CisReportResultDgimgDetail> getCisReportResultDgimgDetailById(String id) {
        return dao().findById(id);
    }

    public static List<CisReportResultDgimgDetail> getCisReportResultDgimgDetails(String cisReportResultId, CisReportResultDgimgDetailQto qto) {
        if (cisReportResultId != null) {
            qto.setCisReportResultId(cisReportResultId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static List<CisReportResultDgimgDetail> getCisReportResultDgimgDetailByVisitCode(String visitCode) {
        return dao().getCisReportResultDgimgDetailByVisitCode(visitCode);
    }

    public static Page<CisReportResultDgimgDetail> getCisReportResultDgimgDetailPage(String cisReportResultId, CisReportResultDgimgDetailQto qto) {
        if (cisReportResultId != null) {
            qto.setCisReportResultId(cisReportResultId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisReportResultDgimgDetail> getSpecification(CisReportResultDgimgDetailQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getCisReportResultId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisReportResultId"), qto.getCisReportResultId()));
            }
            if (StringUtils.isNotBlank(qto.getReportDetailCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportDetailCode"), qto.getReportDetailCode()));
            }
            if (StringUtils.isNotBlank(qto.getSubItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("subItemCode"), qto.getSubItemCode()));
            }
            if (StringUtils.isNotBlank(qto.getSubItemName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("subItemName"), qto.getSubItemName()));
            }
            if (qto.getResultFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("resultFlag"), qto.getResultFlag()));
            }
            if (StringUtils.isNotBlank(qto.getCrisisResultFlag())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("crisisResultFlag"), qto.getCrisisResultFlag()));
            }
            if (StringUtils.isNotBlank(qto.getRemark())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("remark"), qto.getRemark()));
            }
            return predicate;
        };
    }

    private static CisReportResultDgimgDetailRepository dao() {
        return SpringUtil.getBean(CisReportResultDgimgDetailRepository.class);
    }

    public String getResultContent() {
        return resultContent;
    }

    protected void setResultContent(String resultContent) {
        this.resultContent = resultContent;
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    @Override
    public CisReportResultDetail create(String cisReportResultId, CisReportResultDetailNto cisReportResultDetailNto) {
        return create(cisReportResultId, (CisReportResultDgimgDetailNto) cisReportResultDetailNto);
    }

    @Override
    public void update(CisReportResultDetailEto cisReportResultDetailEto) {
        update((CisReportResultDgimgDetailEto) cisReportResultDetailEto);
    }

    public CisReportResultDgimgDetail create(String cisReportResultId, CisReportResultDgimgDetailNto cisReportResultDgimgDetailNto) {
        Assert.notNull(cisReportResultDgimgDetailNto, "参数cisReportResultDgimgDetailNto不能为空！");
        super.create(cisReportResultId, cisReportResultDgimgDetailNto);

        setResultContent(cisReportResultDgimgDetailNto.getResultContent());
        setHumanOrgans(cisReportResultDgimgDetailNto.getHumanOrgans());
        dao().save(this);
        return this;
    }

    public void update(CisReportResultDgimgDetailEto cisReportResultDgimgDetailEto) {
        super.update(cisReportResultDgimgDetailEto);
        setResultContent(cisReportResultDgimgDetailEto.getResultContent());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
