package com.bjgoodwill.hip.ds.cis.cdr.diagnose.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.repository.CisCdrClinicDiagnoseRepository;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseEto;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseNto;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "诊断记录")
@Table(name = "cis_cdr_clinic_diagnose", indexes = {
        @Index(name = "cis_cdr_clinic_diagnose_pat_code", columnList = "pat_code")}, uniqueConstraints = {})
public class CisCdrClinicDiagnose {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Enumerated(EnumType.STRING)
    @Comment("门诊，住院")
    @Column(name = "visit_type", nullable = true)
    private VisitTypeEnum visitType;

    @Comment("患者主索引")
    @Column(name = "pat_code", nullable = false, length = 32)
    private String patCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = false, length = 32)
    private String visitCode;


    @Enumerated(EnumType.STRING)
    @Comment("诊断类型")
    @Column(name = "diagnosis_type", nullable = true)
    private DiagnosisTypeEnum diagnosisType;


    @Comment("诊断编码")
    @Column(name = "diagnosis_code", nullable = false, length = 16)
    private String diagnosisCode;


    @Comment("诊断名称")
    @Column(name = "diagnosis_name", nullable = true, length = 50)
    private String diagnosisName;


    @Comment("中医证型")
    @Column(name = "tcm_syndrome", nullable = true, length = 50)
    private String tcmSyndrome;


    @Comment("诊断病情")
    @Column(name = "diagnosis_condition", nullable = true, length = 50)
    private String diagnosisCondition;


    @Comment("主诊断")
    @Column(name = "chief_flag", nullable = false)
    private boolean chiefFlag;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;

    public static Optional<CisCdrClinicDiagnose> getCisCdrClinicDiagnoseById(String id) {
        return dao().findById(id);
    }

    public static List<CisCdrClinicDiagnose> getCisCdrClinicDiagnoses(CisCdrClinicDiagnoseQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisCdrClinicDiagnose> getCisCdrClinicDiagnosePage(CisCdrClinicDiagnoseQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisCdrClinicDiagnose> getSpecification(CisCdrClinicDiagnoseQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (qto.getDiagnosisType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("diagnosisType"), qto.getDiagnosisType()));
            }
            if (qto.getChiefFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("chiefFlag"), qto.getChiefFlag()));
            }
            if (StringUtils.isNotBlank(qto.getPatCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patCode"), qto.getPatCode()));
            }
            return predicate;
        };
    }

    private static CisCdrClinicDiagnoseRepository dao() {
        return SpringUtil.getBean(CisCdrClinicDiagnoseRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    protected void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatCode() {
        return patCode;
    }

    protected void setPatCode(String patCode) {
        this.patCode = patCode;
    }

    public DiagnosisTypeEnum getDiagnosisType() {
        return diagnosisType;
    }

    protected void setDiagnosisType(DiagnosisTypeEnum diagnosisType) {
        this.diagnosisType = diagnosisType;
    }

    public String getDiagnosisCode() {
        return diagnosisCode;
    }

    protected void setDiagnosisCode(String diagnosisCode) {
        this.diagnosisCode = diagnosisCode;
    }

    public String getDiagnosisName() {
        return diagnosisName;
    }

    protected void setDiagnosisName(String diagnosisName) {
        this.diagnosisName = diagnosisName;
    }

    public String getTcmSyndrome() {
        return tcmSyndrome;
    }

    protected void setTcmSyndrome(String tcmSyndrome) {
        this.tcmSyndrome = tcmSyndrome;
    }

    public String getDiagnosisCondition() {
        return diagnosisCondition;
    }

    protected void setDiagnosisCondition(String diagnosisCondition) {
        this.diagnosisCondition = diagnosisCondition;
    }

    public boolean isChiefFlag() {
        return chiefFlag;
    }

    protected void setChiefFlag(boolean chiefFlag) {
        this.chiefFlag = chiefFlag;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisCdrClinicDiagnose other = (CisCdrClinicDiagnose) obj;
        return Objects.equals(id, other.id);
    }

    public CisCdrClinicDiagnose create(CisCdrClinicDiagnoseNto cisCdrClinicDiagnoseNto) {
        Assert.notNull(cisCdrClinicDiagnoseNto, "参数cisCdrClinicDiagnoseNto不能为空！");

        setId(cisCdrClinicDiagnoseNto.getId());
        setVisitType(cisCdrClinicDiagnoseNto.getVisitType());
        setVisitCode(cisCdrClinicDiagnoseNto.getVisitCode());
        setDiagnosisType(cisCdrClinicDiagnoseNto.getDiagnosisType());
        setDiagnosisCode(cisCdrClinicDiagnoseNto.getDiagnosisCode());
        setDiagnosisName(cisCdrClinicDiagnoseNto.getDiagnosisName());
        setTcmSyndrome(cisCdrClinicDiagnoseNto.getTcmSyndrome());
        setDiagnosisCondition(cisCdrClinicDiagnoseNto.getDiagnosisCondition());
        setChiefFlag(cisCdrClinicDiagnoseNto.isChiefFlag());
        setCreatedDate(cisCdrClinicDiagnoseNto.getCreatedDate());
        dao().save(this);
        return this;
    }

    public void update(CisCdrClinicDiagnoseEto cisCdrClinicDiagnoseEto) {
        setDiagnosisType(cisCdrClinicDiagnoseEto.getDiagnosisType());
        setDiagnosisCode(cisCdrClinicDiagnoseEto.getDiagnosisCode());
        setDiagnosisName(cisCdrClinicDiagnoseEto.getDiagnosisName());
        setTcmSyndrome(cisCdrClinicDiagnoseEto.getTcmSyndrome());
        setChiefFlag(cisCdrClinicDiagnoseEto.isChiefFlag());
    }

    public void delete() {
        dao().delete(this);
    }

}
