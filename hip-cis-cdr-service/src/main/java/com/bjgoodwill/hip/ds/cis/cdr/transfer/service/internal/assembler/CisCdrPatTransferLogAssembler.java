package com.bjgoodwill.hip.ds.cis.cdr.transfer.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.transfer.entity.CisCdrPatTransferLog;
import com.bjgoodwill.hip.ds.cis.cdr.transfer.to.CisCdrPatTransferLogTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisCdrPatTransferLogAssembler {

    public static List<CisCdrPatTransferLogTo> toTos(List<CisCdrPatTransferLog> cisCdrPatTransferLogs) {
        return toTos(cisCdrPatTransferLogs, false);
    }

    public static List<CisCdrPatTransferLogTo> toTos(List<CisCdrPatTransferLog> cisCdrPatTransferLogs, boolean withAllParts) {
        Assert.notNull(cisCdrPatTransferLogs, "参数cisCdrPatTransferLogs不能为空！");

        List<CisCdrPatTransferLogTo> tos = new ArrayList<>();
        for (CisCdrPatTransferLog cisCdrPatTransferLog : cisCdrPatTransferLogs)
            tos.add(toTo(cisCdrPatTransferLog, withAllParts));
        return tos;
    }

    public static CisCdrPatTransferLogTo toTo(CisCdrPatTransferLog cisCdrPatTransferLog) {
        return toTo(cisCdrPatTransferLog, false);
    }

    /**
     * @generated
     */
    public static CisCdrPatTransferLogTo toTo(CisCdrPatTransferLog cisCdrPatTransferLog, boolean withAllParts) {
        if (cisCdrPatTransferLog == null)
            return null;
        CisCdrPatTransferLogTo to = new CisCdrPatTransferLogTo();
        to.setId(cisCdrPatTransferLog.getId());
        to.setVisitCode(cisCdrPatTransferLog.getVisitCode());
        to.setPatMiCode(cisCdrPatTransferLog.getPatMiCode());
        to.setTransferClass(cisCdrPatTransferLog.getTransferClass());
        to.setTransferDate(cisCdrPatTransferLog.getTransferDate());
        to.setBeforeOrg(cisCdrPatTransferLog.getBeforeOrg());
        to.setAfterOrg(cisCdrPatTransferLog.getAfterOrg());
        to.setCreatedDate(cisCdrPatTransferLog.getCreatedDate());

        if (withAllParts) {
        }
        return to;
    }

}