package com.bjgoodwill.hip.ds.cis.cdr.plan.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.ds.cis.cdr.plan.repository.CisCdrOrderPlanDetailRecordRepository;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisCdrOrderPlanDetailRecordNto;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisCdrOrderPlanDetailRecordQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "患者医嘱执行历史费用明细")
@Table(name = "cis_cdr_order_plan_detail_record", indexes = {@Index(name = "cis_cdr_order_plan_detail_record_order_plan_id",
        columnList = "order_plan_id")}, uniqueConstraints = {})
public class CisCdrOrderPlanDetailRecord {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;

    @Comment("执行单id")
    @Column(name = "order_plan_id", nullable = false, length = 50)
    private String orderPlanId;

    @Comment("流水号")
    @Column(name = "visit_code", nullable = false, length = 32)
    private String visitCode;

    @Comment("每次剂量")
    @Column(name = "dosage", nullable = true)
    private Double dosage;

    // 剂量单位名称 字典DosageUnit
    @Comment("剂量单位名称 字典DosageUnit")
    @Column(name = "dosage_unit_name", nullable = true, length = 32)
    private String dosageUnitName;

    @Comment("包装总量")
    @Column(name = "package_num", nullable = true)
    private Double packageNum;

    @Comment("包装单位名称 MinUnit/PackageUnit")
    @Column(name = "package_unit_name", nullable = true, length = 32)
    private String packageUnitName;

    @Comment("收费项目名称")
    @Column(name = "price_item_name", nullable = true, length = 32)
    private String priceItemName;

    @Comment("执行科室")
    @Column(name = "execute_org", nullable = true, length = 32)
    private String executeOrg;

    @Comment("数据抽取时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;

    public static Optional<CisCdrOrderPlanDetailRecord> getCisCdrOrderPlanDetailRecordById(String id) {
        return dao().findById(id);
    }

    public static List<CisCdrOrderPlanDetailRecord> getCisCdrOrderPlanDetailRecords(String orderPlanId, CisCdrOrderPlanDetailRecordQto qto) {
        if (orderPlanId != null) {
            qto.setOrderPlanId(orderPlanId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisCdrOrderPlanDetailRecord> getCisCdrOrderPlanDetailRecordPage(String orderPlanId, CisCdrOrderPlanDetailRecordQto qto) {
        if (orderPlanId != null) {
            qto.setOrderPlanId(orderPlanId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisCdrOrderPlanDetailRecord> getByOrderPlanId(String orderPlanId) {
        return dao().findByOrderPlanId(orderPlanId);
    }

    public static void deleteByOrderPlanId(String orderPlanId) {
        dao().deleteByOrderPlanId(orderPlanId);
    }

    /**
     * @generated
     */
    private static Specification<CisCdrOrderPlanDetailRecord> getSpecification(CisCdrOrderPlanDetailRecordQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getOrderPlanId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderPlanId"), qto.getOrderPlanId()));
            }
            return predicate;
        };
    }

    private static CisCdrOrderPlanDetailRecordRepository dao() {
        return SpringUtil.getBean(CisCdrOrderPlanDetailRecordRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getOrderPlanId() {
        return orderPlanId;
    }

    protected void setOrderPlanId(String orderPlanId) {
        this.orderPlanId = orderPlanId;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPriceItemName() {
        return priceItemName;
    }

    protected void setPriceItemName(String priceItemName) {
        this.priceItemName = priceItemName;
    }

    public String getExecuteOrg() {
        return executeOrg;
    }

    protected void setExecuteOrg(String executeOrg) {
        this.executeOrg = executeOrg;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    public Double getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    public String getPackageUnitName() {
        return packageUnitName;
    }

    public void setPackageUnitName(String packageUnitName) {
        this.packageUnitName = packageUnitName;
    }

    @Transient
    public CisCdrOrderPlanRecord getCisCdrOrderPlanRecord() {
        return CisCdrOrderPlanRecord.getCisCdrOrderPlanRecordById(getOrderPlanId()).orElse(null);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisCdrOrderPlanDetailRecord other = (CisCdrOrderPlanDetailRecord) obj;
        return Objects.equals(id, other.id);
    }

    public CisCdrOrderPlanDetailRecord create(String orderPlanId, CisCdrOrderPlanDetailRecordNto cisCdrOrderPlanDetailRecordNto) {
        Assert.notNull(cisCdrOrderPlanDetailRecordNto, "参数cisCdrOrderPlanDetailRecordNto不能为空！");

        setOrderPlanId(orderPlanId);

        setId(cisCdrOrderPlanDetailRecordNto.getId());
        setVisitCode(cisCdrOrderPlanDetailRecordNto.getVisitCode());
        setPriceItemName(cisCdrOrderPlanDetailRecordNto.getPriceItemName());
        setExecuteOrg(cisCdrOrderPlanDetailRecordNto.getExecuteOrg());
        setCreatedDate(cisCdrOrderPlanDetailRecordNto.getCreatedDate());
        setDosage(cisCdrOrderPlanDetailRecordNto.getDosage());
        setDosageUnitName(cisCdrOrderPlanDetailRecordNto.getDosageUnitName());
        setPackageNum(cisCdrOrderPlanDetailRecordNto.getPackageNum());
        setPackageUnitName(cisCdrOrderPlanDetailRecordNto.getPackageUnitName());

        dao().save(this);
        return this;
    }

    public void delete() {
        dao().delete(this);
    }

}
