package com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.entity.CisAntimicrobialsSkinExecReport;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.to.CisAntimicrobialsSkinExecReportTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisAntimicrobialsSkinExecReportAssembler {

    public static List<CisAntimicrobialsSkinExecReportTo> toTos(List<CisAntimicrobialsSkinExecReport> cisAntimicrobialsSkinExecReports) {
        return toTos(cisAntimicrobialsSkinExecReports, false);
    }

    public static List<CisAntimicrobialsSkinExecReportTo> toTos(List<CisAntimicrobialsSkinExecReport> cisAntimicrobialsSkinExecReports, boolean withAllParts) {
        Assert.notNull(cisAntimicrobialsSkinExecReports, "参数cisAntimicrobialsSkinExecReports不能为空！");

        List<CisAntimicrobialsSkinExecReportTo> tos = new ArrayList<>();
        for (CisAntimicrobialsSkinExecReport cisAntimicrobialsSkinExecReport : cisAntimicrobialsSkinExecReports)
            tos.add(toTo(cisAntimicrobialsSkinExecReport, withAllParts));
        return tos;
    }

    public static CisAntimicrobialsSkinExecReportTo toTo(CisAntimicrobialsSkinExecReport cisAntimicrobialsSkinExecReport) {
        return toTo(cisAntimicrobialsSkinExecReport, false);
    }

    /**
     * @generated
     */
    public static CisAntimicrobialsSkinExecReportTo toTo(CisAntimicrobialsSkinExecReport cisAntimicrobialsSkinExecReport, boolean withAllParts) {
        if (cisAntimicrobialsSkinExecReport == null)
            return null;
        CisAntimicrobialsSkinExecReportTo to = new CisAntimicrobialsSkinExecReportTo();
        to.setId(cisAntimicrobialsSkinExecReport.getId());
        to.setVisitCode(cisAntimicrobialsSkinExecReport.getVisitCode());
        to.setPatMiCode(cisAntimicrobialsSkinExecReport.getPatMiCode());
        to.setVisitType(cisAntimicrobialsSkinExecReport.getVisitType());
        to.setSystemType(cisAntimicrobialsSkinExecReport.getSystemType());
        to.setServiceItemCode(cisAntimicrobialsSkinExecReport.getServiceItemCode());
        to.setServiceItemName(cisAntimicrobialsSkinExecReport.getServiceItemName());
        to.setOrderId(cisAntimicrobialsSkinExecReport.getOrderId());
        to.setApplyId(cisAntimicrobialsSkinExecReport.getApplyId());
        to.setExecPlanId(cisAntimicrobialsSkinExecReport.getExecPlanId());
        to.setExecPlanDate(cisAntimicrobialsSkinExecReport.getExecPlanDate());
        to.setCreatedDate(cisAntimicrobialsSkinExecReport.getCreatedDate());
        to.setCreatedStaff(cisAntimicrobialsSkinExecReport.getCreatedStaff());
        to.setCreatedStaffName(cisAntimicrobialsSkinExecReport.getCreatedStaffName());
        to.setUpdatedDate(cisAntimicrobialsSkinExecReport.getUpdatedDate());
        to.setUpdatedStaff(cisAntimicrobialsSkinExecReport.getUpdatedStaff());
        to.setUpdatedStaffName(cisAntimicrobialsSkinExecReport.getUpdatedStaffName());
        to.setDeleted(cisAntimicrobialsSkinExecReport.isDeleted());

        if (withAllParts) {
        }
        return to;
    }

}