package com.bjgoodwill.hip.ds.cis.cdr.report.repository;

import com.bjgoodwill.hip.ds.cis.cdr.report.entity.CisReportResultDgimgDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.report.repository.CisReportResultDgimgDetailRepository")
public interface CisReportResultDgimgDetailRepository extends JpaRepository<CisReportResultDgimgDetail, String>, JpaSpecificationExecutor<CisReportResultDgimgDetail> {

    @Query("select t1 from CisReportResultDgimgDetail t1,CisReportResult t2 where t1.cisReportResultId = t2.id and t2.visitCode = ?1")
    List<CisReportResultDgimgDetail> getCisReportResultDgimgDetailByVisitCode(String visitCode);
}