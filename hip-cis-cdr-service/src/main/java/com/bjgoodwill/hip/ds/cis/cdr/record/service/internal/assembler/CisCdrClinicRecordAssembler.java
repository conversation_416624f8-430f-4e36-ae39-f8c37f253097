package com.bjgoodwill.hip.ds.cis.cdr.record.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.record.entity.CisCdrClinicIpdRecord;
import com.bjgoodwill.hip.ds.cis.cdr.record.entity.CisCdrClinicOpdRecord;
import com.bjgoodwill.hip.ds.cis.cdr.record.entity.CisCdrClinicRecord;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicRecordTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisCdrClinicRecordAssembler {

    public static List<CisCdrClinicRecordTo> toTos(List<CisCdrClinicRecord> cisCdrClinicRecords) {
        return toTos(cisCdrClinicRecords, false);
    }

    public static List<CisCdrClinicRecordTo> toTos(List<CisCdrClinicRecord> cisCdrClinicRecords, boolean withAllParts) {
        Assert.notNull(cisCdrClinicRecords, "参数cisCdrClinicRecords不能为空！");

        List<CisCdrClinicRecordTo> tos = new ArrayList<>();
        for (CisCdrClinicRecord cisCdrClinicRecord : cisCdrClinicRecords)
            tos.add(toTo(cisCdrClinicRecord, withAllParts));
        return tos;
    }

    public static CisCdrClinicRecordTo toTo(CisCdrClinicRecord cisCdrClinicRecord) {
        return toTo(cisCdrClinicRecord, false);
    }

    /**
     * @generated
     */
    public static CisCdrClinicRecordTo toTo(CisCdrClinicRecord cisCdrClinicRecord, boolean withAllParts) {
        if (cisCdrClinicRecord == null)
            return null;
        if (cisCdrClinicRecord instanceof CisCdrClinicIpdRecord) {
            return CisCdrClinicIpdRecordAssembler.toTo((CisCdrClinicIpdRecord) cisCdrClinicRecord, withAllParts);
        }
        if (cisCdrClinicRecord instanceof CisCdrClinicOpdRecord) {
            return CisCdrClinicOpdRecordAssembler.toTo((CisCdrClinicOpdRecord) cisCdrClinicRecord, withAllParts);
        }
        return null;
    }

}