package com.bjgoodwill.hip.ds.cis.cdr.order.service.internal;

import com.bjgoodwill.hip.ds.cis.cdr.order.entity.CisCdrIpdTermOrder;
import com.bjgoodwill.hip.ds.cis.cdr.order.service.CisCdrIpdTermOrderService;
import com.bjgoodwill.hip.ds.cis.cdr.order.service.internal.assembler.CisCdrIpdTermOrderAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrIpdTermOrderEto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrIpdTermOrderNto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrIpdTermOrderTo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.order.service.CisCdrIpdTermOrderService")
@RequestMapping(value = "/api/cdr/order/cisCdrIpdTermOrder", produces = "application/json; charset=utf-8")
public class CisCdrIpdTermOrderServiceImpl implements CisCdrIpdTermOrderService {

//    @Override
//    @Transactional(rollbackFor = Throwable.class, readOnly = true)
//    public List<CisCdrIpdTermOrderTo> getCisCdrIpdTermOrders(CisCdrIpdTermOrderQto cisCdrIpdTermOrderQto) {
//        return CisCdrIpdTermOrderAssembler.toTos(CisCdrIpdTermOrder.getCisCdrIpdTermOrders(cisCdrIpdTermOrderQto));
//    }
//
//    @Override
//    @Transactional(rollbackFor = Throwable.class, readOnly = true)
//    public GridResultSet<CisCdrIpdTermOrderTo> getCisCdrIpdTermOrderPage(CisCdrIpdTermOrderQto cisCdrIpdTermOrderQto) {
//        Page<CisCdrIpdTermOrder> page = CisCdrIpdTermOrder.getCisCdrIpdTermOrderPage(cisCdrIpdTermOrderQto);
//        Page<CisCdrIpdTermOrderTo> result = page.map(CisCdrIpdTermOrderAssembler::toTo);
//        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
//    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisCdrIpdTermOrderTo createCisCdrIpdTermOrder(CisCdrIpdTermOrderNto cisCdrIpdTermOrderNto) {
        CisCdrIpdTermOrder cisCdrIpdTermOrder = new CisCdrIpdTermOrder();
        cisCdrIpdTermOrder = cisCdrIpdTermOrder.create(cisCdrIpdTermOrderNto);
        return CisCdrIpdTermOrderAssembler.toTo(cisCdrIpdTermOrder);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisCdrIpdTermOrder(String id, CisCdrIpdTermOrderEto cisCdrIpdTermOrderEto) {
        Optional<CisCdrIpdTermOrder> cisCdrIpdTermOrderOptional = CisCdrIpdTermOrder.getCisCdrIpdTermOrderById(id);
        cisCdrIpdTermOrderOptional.ifPresent(cisCdrIpdTermOrder -> cisCdrIpdTermOrder.update(cisCdrIpdTermOrderEto));
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}