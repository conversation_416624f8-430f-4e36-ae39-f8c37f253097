package com.bjgoodwill.hip.ds.cis.cdr.order.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.order.entity.CisCdrIpdTermOrder;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrIpdTermOrderTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisCdrIpdTermOrderAssembler {

    public static List<CisCdrIpdTermOrderTo> toTos(List<CisCdrIpdTermOrder> cisCdrIpdTermOrders) {
        return toTos(cisCdrIpdTermOrders, false);
    }

    public static List<CisCdrIpdTermOrderTo> toTos(List<CisCdrIpdTermOrder> cisCdrIpdTermOrders, boolean withAllParts) {
        Assert.notNull(cisCdrIpdTermOrders, "参数cisCdrIpdTermOrders不能为空！");

        List<CisCdrIpdTermOrderTo> tos = new ArrayList<>();
        for (CisCdrIpdTermOrder cisCdrIpdTermOrder : cisCdrIpdTermOrders)
            tos.add(toTo(cisCdrIpdTermOrder, withAllParts));
        return tos;
    }

    public static CisCdrIpdTermOrderTo toTo(CisCdrIpdTermOrder cisCdrIpdTermOrder) {
        return toTo(cisCdrIpdTermOrder, false);
    }

    /**
     * @generated
     */
    public static CisCdrIpdTermOrderTo toTo(CisCdrIpdTermOrder cisCdrIpdTermOrder, boolean withAllParts) {
        if (cisCdrIpdTermOrder == null)
            return null;
        CisCdrIpdTermOrderTo to = new CisCdrIpdTermOrderTo();
        to.setId(cisCdrIpdTermOrder.getId());
        to.setSortNo(cisCdrIpdTermOrder.getSortNo());
        to.setPatMiCode(cisCdrIpdTermOrder.getPatMiCode());
        to.setVisitCode(cisCdrIpdTermOrder.getVisitCode());
        to.setOrderServiceCode(cisCdrIpdTermOrder.getOrderServiceCode());
        to.setOrderClass(cisCdrIpdTermOrder.getOrderClass());
        to.setApplyCode(cisCdrIpdTermOrder.getApplyCode());
        to.setTreatmentCourse(cisCdrIpdTermOrder.getTreatmentCourse());
        to.setTreatmentCourseUnit(cisCdrIpdTermOrder.getTreatmentCourseUnit());
        to.setBabyFlag(cisCdrIpdTermOrder.getBabyFlag());
        to.setExecuteOrgCode(cisCdrIpdTermOrder.getExecuteOrgCode());
        to.setExecuteOrgName(cisCdrIpdTermOrder.getExecuteOrgName());
        to.setDeptNurseCode(cisCdrIpdTermOrder.getDeptNurseCode());
        to.setDeptNurseName(cisCdrIpdTermOrder.getDeptNurseName());
        to.setEffectiveLowDate(cisCdrIpdTermOrder.getEffectiveLowDate());
        to.setRepairFlag(cisCdrIpdTermOrder.getRepairFlag());
        to.setParentCode(cisCdrIpdTermOrder.getParentCode());
        to.setCriticalId(cisCdrIpdTermOrder.getCriticalId());
        to.setPrescriptionFlag(cisCdrIpdTermOrder.getPrescriptionFlag());
        to.setPassValue(cisCdrIpdTermOrder.getPassValue());
        to.setThirdFlag(cisCdrIpdTermOrder.getThirdFlag());
        to.setReceiveOrgCode(cisCdrIpdTermOrder.getReceiveOrgCode());
        to.setReceiveOrgName(cisCdrIpdTermOrder.getReceiveOrgName());
        to.setUsage(cisCdrIpdTermOrder.getUsage());
        to.setUsageName(cisCdrIpdTermOrder.getUsageName());
        to.setSbadmWay(cisCdrIpdTermOrder.getSbadmWay());
        to.setSkinFlag(cisCdrIpdTermOrder.getSkinFlag());
        to.setSkinType(cisCdrIpdTermOrder.getSkinType());
        to.setCreatedStaff(cisCdrIpdTermOrder.getCreatedStaff());
        to.setCreatedStaffName(cisCdrIpdTermOrder.getCreatedStaffName());
        to.setCreatedDate(cisCdrIpdTermOrder.getCreatedDate());
        to.setCommitDate(cisCdrIpdTermOrder.getCommitDate());
        to.setSubmitStaffId(cisCdrIpdTermOrder.getSubmitStaffId());
        to.setSubmitStaffName(cisCdrIpdTermOrder.getSubmitStaffName());
        to.setProofStaff(cisCdrIpdTermOrder.getProofStaff());
        to.setProofStaffName(cisCdrIpdTermOrder.getProofStaffName());
        to.setLimitConformFlag(cisCdrIpdTermOrder.getLimitConformFlag());
        to.setHospitalCode(cisCdrIpdTermOrder.getHospitalCode());
        to.setHospitalName(cisCdrIpdTermOrder.getHospitalName());
        to.setCancelStaff(cisCdrIpdTermOrder.getCancelStaff());
        to.setCancelStaffName(cisCdrIpdTermOrder.getCancelStaffName());
        to.setCancelDate(cisCdrIpdTermOrder.getCancelDate());
        to.setCancelRemark(cisCdrIpdTermOrder.getCancelRemark());
        to.setReMark(cisCdrIpdTermOrder.getReMark());
        to.setStatusCode(cisCdrIpdTermOrder.getStatusCode());

        if (withAllParts) {
        }
        return to;
    }

}