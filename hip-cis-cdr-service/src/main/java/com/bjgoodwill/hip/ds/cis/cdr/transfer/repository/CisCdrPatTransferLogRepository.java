package com.bjgoodwill.hip.ds.cis.cdr.transfer.repository;

import com.bjgoodwill.hip.ds.cis.cdr.transfer.entity.CisCdrPatTransferLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.transfer.repository.CisCdrPatTransferLogRepository")
public interface CisCdrPatTransferLogRepository extends JpaRepository<CisCdrPatTransferLog, String>, JpaSpecificationExecutor<CisCdrPatTransferLog> {

}