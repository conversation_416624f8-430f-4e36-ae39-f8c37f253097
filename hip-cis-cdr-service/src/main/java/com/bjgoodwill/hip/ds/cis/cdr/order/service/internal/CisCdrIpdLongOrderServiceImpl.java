package com.bjgoodwill.hip.ds.cis.cdr.order.service.internal;

import com.bjgoodwill.hip.ds.cis.cdr.order.entity.CisCdrIpdLongOrder;
import com.bjgoodwill.hip.ds.cis.cdr.order.service.CisCdrIpdLongOrderService;
import com.bjgoodwill.hip.ds.cis.cdr.order.service.internal.assembler.CisCdrIpdLongOrderAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrIpdLongOrderEto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrIpdLongOrderNto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrIpdLongOrderTo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.order.service.CisCdrIpdLongOrderService")
@RequestMapping(value = "/api/cdr/order/cisCdrIpdLongOrder", produces = "application/json; charset=utf-8")
public class CisCdrIpdLongOrderServiceImpl implements CisCdrIpdLongOrderService {

//    @Override
//    @Transactional(rollbackFor = Throwable.class, readOnly = true)
//    public List<CisCdrIpdLongOrderTo> getCisCdrIpdLongOrders(CisCdrIpdLongOrderQto cisCdrIpdLongOrderQto) {
//        return CisCdrIpdLongOrderAssembler.toTos(CisCdrIpdLongOrder.getCisCdrIpdLongOrders(cisCdrIpdLongOrderQto));
//    }
//
//    @Override
//    @Transactional(rollbackFor = Throwable.class, readOnly = true)
//    public GridResultSet<CisCdrIpdLongOrderTo> getCisCdrIpdLongOrderPage(CisCdrIpdLongOrderQto cisCdrIpdLongOrderQto) {
//        Page<CisCdrIpdLongOrder> page = CisCdrIpdLongOrder.getCisCdrIpdLongOrderPage(cisCdrIpdLongOrderQto);
//        Page<CisCdrIpdLongOrderTo> result = page.map(CisCdrIpdLongOrderAssembler::toTo);
//        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
//    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisCdrIpdLongOrderTo createCisCdrIpdLongOrder(CisCdrIpdLongOrderNto cisCdrIpdLongOrderNto) {
        CisCdrIpdLongOrder cisCdrIpdLongOrder = new CisCdrIpdLongOrder();
        cisCdrIpdLongOrder = cisCdrIpdLongOrder.create(cisCdrIpdLongOrderNto);
        return CisCdrIpdLongOrderAssembler.toTo(cisCdrIpdLongOrder);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisCdrIpdLongOrder(String id, CisCdrIpdLongOrderEto cisCdrIpdLongOrderEto) {
        Optional<CisCdrIpdLongOrder> cisCdrIpdLongOrderOptional = CisCdrIpdLongOrder.getCisCdrIpdLongOrderById(id);
        cisCdrIpdLongOrderOptional.ifPresent(cisCdrIpdLongOrder -> cisCdrIpdLongOrder.update(cisCdrIpdLongOrderEto));
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}