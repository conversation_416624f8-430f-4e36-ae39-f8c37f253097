package com.bjgoodwill.hip.ds.cis.cdr.plan.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.plan.entity.CisCdrOrderPlanDetailRecord;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisCdrOrderPlanDetailRecordTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisCdrOrderPlanDetailRecordAssembler {

    public static List<CisCdrOrderPlanDetailRecordTo> toTos(List<CisCdrOrderPlanDetailRecord> cisCdrOrderPlanDetailRecords) {
        return toTos(cisCdrOrderPlanDetailRecords, false);
    }

    public static List<CisCdrOrderPlanDetailRecordTo> toTos(List<CisCdrOrderPlanDetailRecord> cisCdrOrderPlanDetailRecords, boolean withAllParts) {
        Assert.notNull(cisCdrOrderPlanDetailRecords, "参数cisCdrOrderPlanDetailRecords不能为空！");

        List<CisCdrOrderPlanDetailRecordTo> tos = new ArrayList<>();
        for (CisCdrOrderPlanDetailRecord cisCdrOrderPlanDetailRecord : cisCdrOrderPlanDetailRecords)
            tos.add(toTo(cisCdrOrderPlanDetailRecord, withAllParts));
        return tos;
    }

    public static CisCdrOrderPlanDetailRecordTo toTo(CisCdrOrderPlanDetailRecord cisCdrOrderPlanDetailRecord) {
        return toTo(cisCdrOrderPlanDetailRecord, false);
    }

    /**
     * @generated
     */
    public static CisCdrOrderPlanDetailRecordTo toTo(CisCdrOrderPlanDetailRecord cisCdrOrderPlanDetailRecord, boolean withAllParts) {
        if (cisCdrOrderPlanDetailRecord == null)
            return null;
        CisCdrOrderPlanDetailRecordTo to = new CisCdrOrderPlanDetailRecordTo();
        to.setId(cisCdrOrderPlanDetailRecord.getId());
        to.setOrderPlanId(cisCdrOrderPlanDetailRecord.getOrderPlanId());
        to.setVisitCode(cisCdrOrderPlanDetailRecord.getVisitCode());
        to.setPriceItemName(cisCdrOrderPlanDetailRecord.getPriceItemName());
        to.setExecuteOrg(cisCdrOrderPlanDetailRecord.getExecuteOrg());
        to.setCreatedDate(cisCdrOrderPlanDetailRecord.getCreatedDate());
        to.setDosage(cisCdrOrderPlanDetailRecord.getDosage());
        to.setDosageUnitName(cisCdrOrderPlanDetailRecord.getDosageUnitName());
        to.setPackageNum(cisCdrOrderPlanDetailRecord.getPackageNum());
        to.setPackageUnitName(cisCdrOrderPlanDetailRecord.getPackageUnitName());
        if (withAllParts) {
        }
        return to;
    }

}