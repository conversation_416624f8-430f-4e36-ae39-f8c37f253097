package com.bjgoodwill.hip.ds.cis.cdr.orderlog.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.entity.CisIpdOrderLcLog;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.service.CisIpdOrderLcLogService;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.service.internal.assembler.CisIpdOrderLcLogAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisIpdOrderLcLogEto;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisIpdOrderLcLogNto;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisIpdOrderLcLogQto;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisIpdOrderLcLogTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.orderlog.service.CisIpdOrderLcLogService")
@RequestMapping(value = "/api/cdr/orderlog/cisIpdOrderLcLog", produces = "application/json; charset=utf-8")
public class CisIpdOrderLcLogServiceImpl implements CisIpdOrderLcLogService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisIpdOrderLcLogTo> getCisIpdOrderLcLogs(CisIpdOrderLcLogQto cisIpdOrderLcLogQto) {
        return CisIpdOrderLcLogAssembler.toTos(CisIpdOrderLcLog.getCisIpdOrderLcLogs(cisIpdOrderLcLogQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisIpdOrderLcLogTo> getCisIpdOrderLcLogPage(CisIpdOrderLcLogQto cisIpdOrderLcLogQto) {
        Page<CisIpdOrderLcLog> page = CisIpdOrderLcLog.getCisIpdOrderLcLogPage(cisIpdOrderLcLogQto);
        Page<CisIpdOrderLcLogTo> result = page.map(CisIpdOrderLcLogAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisIpdOrderLcLogTo createCisIpdOrderLcLog(CisIpdOrderLcLogNto cisIpdOrderLcLogNto) {
        CisIpdOrderLcLog cisIpdOrderLcLog = new CisIpdOrderLcLog();
        cisIpdOrderLcLog = cisIpdOrderLcLog.create(cisIpdOrderLcLogNto);
        return CisIpdOrderLcLogAssembler.toTo(cisIpdOrderLcLog);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisIpdOrderLcLog(String id, CisIpdOrderLcLogEto cisIpdOrderLcLogEto) {
        Optional<CisIpdOrderLcLog> cisIpdOrderLcLogOptional = CisIpdOrderLcLog.getCisIpdOrderLcLogById(id);
        cisIpdOrderLcLogOptional.ifPresent(cisIpdOrderLcLog -> cisIpdOrderLcLog.update(cisIpdOrderLcLogEto));
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}