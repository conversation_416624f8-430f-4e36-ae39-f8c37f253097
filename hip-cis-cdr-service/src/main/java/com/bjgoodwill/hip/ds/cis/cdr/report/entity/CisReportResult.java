package com.bjgoodwill.hip.ds.cis.cdr.report.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cdr.report.repository.CisReportResultRepository;
import com.bjgoodwill.hip.ds.cis.cdr.report.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "报告结果")
@Table(name = "cis_report_result", indexes = {@Index(name = "idx_cis_report_result_apply_id", columnList = "applyId"),
        @Index(name = "idx_cis_report_result_visit_Code", columnList = "visitCode")}, uniqueConstraints = {})
public class CisReportResult {

    // 标识
    private String id;
    // 医嘱id
    private String orderID;
    // 申请单ID
    private String applyId;
    // 存储其他系统报告主键，用来取消其他系统报告
    private String reportId;
    // 主索引编码
    private String patMiCode;
    // 接诊流水编码
    private String visitCode;
    // 结果类型 SPCOBS/DGIMG
    private SystemTypeEnum reportType;
    // 诊疗服务项目
    private String serviceItemCode;
    //诊疗服务项目名称
    private String serviceItemName;
    // 设备类型
    private String deviceType;
    // 执行科室代码
    private String execOrgCode;
    // 创建的人员
    private String createdStaff;
    // 创建的人员姓名
    private String createdStaffName;
    // 创建的时间
    private LocalDateTime createdDate;
    // 
    private LocalDateTime reportDate;
    // 最后修改的人员
    private String updatedStaff;
    // 最后修改的人员姓名
    private String updatedStaffName;
    // 最后修改的时间
    private LocalDateTime updatedDate;
    // 逻辑删除标记
    private boolean deleted;
    // 执行人编码
    private String execStaff;
    // 执行人
    private String execStaffName;

    private String subItemCode;

    private String subItemName;

    public static Optional<CisReportResult> getCisReportResultById(String id) {
        return dao().findById(id);
    }

    public static List<CisReportResult> getCisReportResults(CisReportResultQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisReportResult> getCisReportResultPage(CisReportResultQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisReportResult> getSpecification(CisReportResultQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("applyId"), qto.getApplyId()));
            }
            if (StringUtils.isNotBlank(qto.getReportId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportId"), qto.getReportId()));
            }
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (qto.getReportType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportType"), qto.getReportType()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (StringUtils.isNotBlank(qto.getExecOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("execOrgCode"), qto.getExecOrgCode()));
            }
            if (qto.getReportDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("reportDate"), LocalDateUtil.beginOfDay(qto.getReportDate()), LocalDateUtil.endOfDay(qto.getReportDate())));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            if (StringUtils.isNotBlank(qto.getExecStaff())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("execStaff"), qto.getExecStaff()));
            }
            if (StringUtils.isNotBlank(qto.getExecStaffName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("execStaffName"), qto.getExecStaffName()));
            }
            return predicate;
        };
    }

    private static CisReportResultRepository dao() {
        return SpringUtil.getBean(CisReportResultRepository.class);
    }

    public static Optional<CisReportResult> getCisReportResultByApplyId(String applyId) {
        return dao().findCisReportResultByApplyId(applyId);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("医嘱id")
    @Column(name = "order_id", nullable = true)
    public String getOrderID() {
        return orderID;
    }

    protected void setOrderID(String orderID) {
        this.orderID = orderID;
    }

    @Comment("申请单ID")
    @Column(name = "apply_id", nullable = false)
    public String getApplyId() {
        return applyId;
    }

    protected void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    @Comment("报告项编码")
    @Column(name = "sub_item_code", nullable = true)
    public String getSubItemCode() {
        return subItemCode;
    }

    public void setSubItemCode(String subItemCode) {
        this.subItemCode = subItemCode;
    }

    @Comment("报告项名称")
    @Column(name = "sub_item_name", nullable = true)
    public String getSubItemName() {
        return subItemName;
    }

    public void setSubItemName(String subItemName) {
        this.subItemName = subItemName;
    }

    @Comment("存储其他系统报告主键，用来取消其他系统报告")
    @Column(name = "report_id", nullable = true)
    public String getReportId() {
        return reportId;
    }

    protected void setReportId(String reportId) {
        this.reportId = reportId;
    }

    @Comment("主索引编码")
    @Column(name = "pat_mi_code", nullable = true)
    public String getPatMiCode() {
        return patMiCode;
    }

    protected void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    @Comment("接诊流水编码")
    @Column(name = "visit_code", nullable = true)
    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    @Enumerated(EnumType.STRING)
    @Comment("结果类型 SPCOBS/DGIMG")
    @Column(name = "report_type", nullable = true)
    public SystemTypeEnum getReportType() {
        return reportType;
    }

    protected void setReportType(SystemTypeEnum reportType) {
        this.reportType = reportType;
    }

    @Comment("诊疗服务项目")
    @Column(name = "service_item_code", nullable = true)
    public String getServiceItemCode() {
        return serviceItemCode;
    }

    protected void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    @Comment("诊疗服务项目名称")
    @Column(name = "service_item_name", nullable = true)
    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    @Comment("设备类型")
    @Column(name = "device_type", nullable = true)
    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    @Comment("执行科室代码")
    @Column(name = "exec_org_code", nullable = true)
    public String getExecOrgCode() {
        return execOrgCode;
    }

    protected void setExecOrgCode(String execOrgCode) {
        this.execOrgCode = execOrgCode;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("")
    @Column(name = "report_date", nullable = true)
    public LocalDateTime getReportDate() {
        return reportDate;
    }

    protected void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Comment("执行人编码")
    @Column(name = "exec_staff", nullable = true)
    public String getExecStaff() {
        return execStaff;
    }

    protected void setExecStaff(String execStaff) {
        this.execStaff = execStaff;
    }

    @Comment("执行人")
    @Column(name = "exec_staff_name", nullable = true)
    public String getExecStaffName() {
        return execStaffName;
    }

    protected void setExecStaffName(String execStaffName) {
        this.execStaffName = execStaffName;
    }

    @Transient
    public List<CisReportResultDetail> getCisReportResultDetails() {
        return CisReportResultDetail.getByCisReportResultId(getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisReportResult other = (CisReportResult) obj;
        return Objects.equals(id, other.id);
    }

    public CisReportResult create(CisReportResultNto cisReportResultNto) {
        Assert.notNull(cisReportResultNto, "参数cisReportResultNto不能为空！");

        setId(cisReportResultNto.getId());
        setOrderID(cisReportResultNto.getOrderID());
        setApplyId(cisReportResultNto.getApplyId());
        setReportId(cisReportResultNto.getReportId());
        setPatMiCode(cisReportResultNto.getPatMiCode());
        setVisitCode(cisReportResultNto.getVisitCode());
        setReportType(cisReportResultNto.getReportType());
        setServiceItemCode(cisReportResultNto.getServiceItemCode());
        setServiceItemName(cisReportResultNto.getServiceItemName());
        setExecOrgCode(cisReportResultNto.getExecOrgCode());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        setReportDate(cisReportResultNto.getReportDate());
        setDeleted(false);
        setExecStaff(cisReportResultNto.getExecStaff());
        setExecStaffName(cisReportResultNto.getExecStaffName());
        setDeviceType(cisReportResultNto.getDeviceType());
        dao().save(this);
        if (cisReportResultNto.getCisReportResultDetails() != null) {
            for (CisReportResultDetailNto cisReportResultDetailNto_ : cisReportResultNto.getCisReportResultDetails()) {
                if (cisReportResultDetailNto_ instanceof CisReportResultSpcobsDetailNto) {
                    CisReportResultSpcobsDetail cisReportResultDetail = new CisReportResultSpcobsDetail();
                    cisReportResultDetail.create(getId(), cisReportResultDetailNto_);
                } else if (cisReportResultDetailNto_ instanceof CisReportResultDgimgDetailNto) {
                    CisReportResultDgimgDetail cisReportResultDetail = new CisReportResultDgimgDetail();
                    cisReportResultDetail.create(getId(), cisReportResultDetailNto_);
                }
            }
        }
        return this;
    }

    public void update(CisReportResultEto cisReportResultEto) {
        setReportDate(cisReportResultEto.getReportDate());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        setExecStaffName(cisReportResultEto.getExecStaffName());
    }

    public void delete() {
        for (CisReportResultDetail cisReportResultDetail : CisReportResultDetail.getByCisReportResultId(getId())) {
            cisReportResultDetail.delete();
        }
        // CisReportResultDetail.deleteByCisReportResultId(getId());
        setDeleted(true);
    }

}
