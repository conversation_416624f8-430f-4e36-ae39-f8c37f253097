package com.bjgoodwill.hip.ds.cis.cdr.order.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.order.entity.CisCdrOrderContent;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderContentTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisCdrOrderContentAssembler {

    public static List<CisCdrOrderContentTo> toTos(List<CisCdrOrderContent> cisCdrOrderContents) {
        return toTos(cisCdrOrderContents, false);
    }

    public static List<CisCdrOrderContentTo> toTos(List<CisCdrOrderContent> cisCdrOrderContents, boolean withAllParts) {
        Assert.notNull(cisCdrOrderContents, "参数cisCdrOrderContents不能为空！");

        List<CisCdrOrderContentTo> tos = new ArrayList<>();
        for (CisCdrOrderContent cisCdrOrderContent : cisCdrOrderContents)
            tos.add(toTo(cisCdrOrderContent, withAllParts));
        return tos;
    }

    public static CisCdrOrderContentTo toTo(CisCdrOrderContent cisCdrOrderContent) {
        return toTo(cisCdrOrderContent, false);
    }

    /**
     * @generated
     */
    public static CisCdrOrderContentTo toTo(CisCdrOrderContent cisCdrOrderContent, boolean withAllParts) {
        if (cisCdrOrderContent == null)
            return null;
        CisCdrOrderContentTo to = new CisCdrOrderContentTo();
        to.setId(cisCdrOrderContent.getId());
        to.setOrderId(cisCdrOrderContent.getOrderId());
        to.setOrderContent(cisCdrOrderContent.getOrderContent());

        if (withAllParts) {
        }
        return to;
    }

}