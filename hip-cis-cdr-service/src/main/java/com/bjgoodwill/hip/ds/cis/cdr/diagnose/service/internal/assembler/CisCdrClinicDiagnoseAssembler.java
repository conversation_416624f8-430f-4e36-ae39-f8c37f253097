package com.bjgoodwill.hip.ds.cis.cdr.diagnose.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.diagnose.entity.CisCdrClinicDiagnose;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisCdrClinicDiagnoseAssembler {

    public static List<CisCdrClinicDiagnoseTo> toTos(List<CisCdrClinicDiagnose> cisCdrClinicDiagnoses) {
        return toTos(cisCdrClinicDiagnoses, false);
    }

    public static List<CisCdrClinicDiagnoseTo> toTos(List<CisCdrClinicDiagnose> cisCdrClinicDiagnoses, boolean withAllParts) {
        Assert.notNull(cisCdrClinicDiagnoses, "参数cisCdrClinicDiagnoses不能为空！");

        List<CisCdrClinicDiagnoseTo> tos = new ArrayList<>();
        for (CisCdrClinicDiagnose cisCdrClinicDiagnose : cisCdrClinicDiagnoses)
            tos.add(toTo(cisCdrClinicDiagnose, withAllParts));
        return tos;
    }

    public static CisCdrClinicDiagnoseTo toTo(CisCdrClinicDiagnose cisCdrClinicDiagnose) {
        return toTo(cisCdrClinicDiagnose, false);
    }

    /**
     * @generated
     */
    public static CisCdrClinicDiagnoseTo toTo(CisCdrClinicDiagnose cisCdrClinicDiagnose, boolean withAllParts) {
        if (cisCdrClinicDiagnose == null)
            return null;
        CisCdrClinicDiagnoseTo to = new CisCdrClinicDiagnoseTo();
        to.setId(cisCdrClinicDiagnose.getId());
        to.setVisitType(cisCdrClinicDiagnose.getVisitType());
        to.setVisitCode(cisCdrClinicDiagnose.getVisitCode());
        to.setDiagnosisType(cisCdrClinicDiagnose.getDiagnosisType());
        to.setDiagnosisCode(cisCdrClinicDiagnose.getDiagnosisCode());
        to.setDiagnosisName(cisCdrClinicDiagnose.getDiagnosisName());
        to.setTcmSyndrome(cisCdrClinicDiagnose.getTcmSyndrome());
        to.setDiagnosisCondition(cisCdrClinicDiagnose.getDiagnosisCondition());
        to.setChiefFlag(cisCdrClinicDiagnose.isChiefFlag());
        to.setCreatedDate(cisCdrClinicDiagnose.getCreatedDate());
        to.setPatCode(cisCdrClinicDiagnose.getPatCode());
        if (withAllParts) {
        }
        return to;
    }

}