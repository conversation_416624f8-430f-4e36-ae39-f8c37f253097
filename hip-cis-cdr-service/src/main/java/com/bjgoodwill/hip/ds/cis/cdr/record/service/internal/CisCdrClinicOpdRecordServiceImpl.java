package com.bjgoodwill.hip.ds.cis.cdr.record.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.record.entity.CisCdrClinicOpdRecord;
import com.bjgoodwill.hip.ds.cis.cdr.record.service.CisCdrClinicOpdRecordService;
import com.bjgoodwill.hip.ds.cis.cdr.record.service.internal.assembler.CisCdrClinicOpdRecordAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicOpdRecordEto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicOpdRecordNto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicOpdRecordQto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicOpdRecordTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.record.service.CisCdrClinicOpdRecordService")
@RequestMapping(value = "/api/cdr/record/cisCdrClinicOpdRecord", produces = "application/json; charset=utf-8")
public class CisCdrClinicOpdRecordServiceImpl implements CisCdrClinicOpdRecordService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisCdrClinicOpdRecordTo> getCisCdrClinicOpdRecords(CisCdrClinicOpdRecordQto cisCdrClinicOpdRecordQto) {
        return CisCdrClinicOpdRecordAssembler.toTos(CisCdrClinicOpdRecord.getCisCdrClinicOpdRecords(cisCdrClinicOpdRecordQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisCdrClinicOpdRecordTo> getCisCdrClinicOpdRecordPage(CisCdrClinicOpdRecordQto cisCdrClinicOpdRecordQto) {
        Page<CisCdrClinicOpdRecord> page = CisCdrClinicOpdRecord.getCisCdrClinicOpdRecordPage(cisCdrClinicOpdRecordQto);
        Page<CisCdrClinicOpdRecordTo> result = page.map(CisCdrClinicOpdRecordAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisCdrClinicOpdRecordTo createCisCdrClinicOpdRecord(CisCdrClinicOpdRecordNto cisCdrClinicOpdRecordNto) {
        CisCdrClinicOpdRecord cisCdrClinicOpdRecord = new CisCdrClinicOpdRecord();
        cisCdrClinicOpdRecord = cisCdrClinicOpdRecord.create(cisCdrClinicOpdRecordNto);
        return CisCdrClinicOpdRecordAssembler.toTo(cisCdrClinicOpdRecord);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisCdrClinicOpdRecord(String id, CisCdrClinicOpdRecordEto cisCdrClinicOpdRecordEto) {
        Optional<CisCdrClinicOpdRecord> cisCdrClinicOpdRecordOptional = CisCdrClinicOpdRecord.getCisCdrClinicOpdRecordById(id);
        cisCdrClinicOpdRecordOptional.ifPresent(cisCdrClinicOpdRecord -> cisCdrClinicOpdRecord.update(cisCdrClinicOpdRecordEto));
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}