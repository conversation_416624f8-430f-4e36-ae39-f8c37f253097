package com.bjgoodwill.hip.ds.cis.cdr.plan.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.ds.cis.cdr.plan.repository.CisCdrOrderPlanRecordRepository;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisCdrOrderPlanDetailRecordNto;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisCdrOrderPlanRecordNto;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisCdrOrderPlanRecordQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "患者医嘱执行历史")
@Table(name = "cis_cdr_order_plan_record", indexes = {@Index(name = "cis_cdr_order_plan_record_pat_code", columnList = "pat_code")}, uniqueConstraints = {})
public class CisCdrOrderPlanRecord {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("患者主索引")
    @Column(name = "pat_code", nullable = false, length = 32)
    private String patCode;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = false, length = 32)
    private String visitCode;


    @Comment("医嘱id")
    @Column(name = "order_id", nullable = true, length = 50)
    private String orderId;


    @Comment("申请单id")
    @Column(name = "apply_id", nullable = false, length = 32)
    private String applyId;


    @Enumerated(EnumType.STRING)
    @Comment("医嘱类型")
    @Column(name = "order_class", nullable = true)
    private SystemTypeEnum orderClass;


    @Comment("医嘱项目名称医嘱项目名称")
    @Column(name = "service_item_name", nullable = true, length = 64)
    private String serviceItemName;


    @Comment("执行科室")
    @Column(name = "execute_org", nullable = false)
    private String executeOrg;

    @Comment("执行科室名称")
    @Column(name = "execute_org_name", nullable = false)
    private String executeOrgName;

    @Comment("执行人")
    @Column(name = "execute_staff", nullable = true, length = 32)
    private String executeStaff;


    @Comment("执行人姓名")
    @Column(name = "execute_staff_name", nullable = true, length = 32)
    private String executeStaffName;

    @Comment("执行时间")
    @Column(name = "execute_date", nullable = true)
    private LocalDateTime executeDate;


    @Comment("药品编码")
    @Column(name = "drug_code", nullable = true, length = 100)
    private String drugCode;

    @Comment("药品规格")
    @Column(name = "spec", nullable = true, length = 100)
    private String spec;

    @Comment("用法名称")
    @Column(name = "usage_name", nullable = true, length = 20)
    private String usageName;

    @Comment("频次名称")
    @Column(name = "frequency_name", nullable = true, length = 20)
    private String frequencyName;

    @Comment("单次用量")
    @Column(name = "dosage", nullable = true, length = 20)
    private Double dosage;

    @Comment("单次用量单位名称")
    @Column(name = "dosage_unit_name", nullable = true, length = 20)
    private String dosageUnitName;

    @Comment("抽取的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;

    public static Optional<CisCdrOrderPlanRecord> getCisCdrOrderPlanRecordById(String id) {
        return dao().findById(id);
    }

    public static List<CisCdrOrderPlanRecord> getCisCdrOrderPlanRecords(CisCdrOrderPlanRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisCdrOrderPlanRecord> getCisCdrOrderPlanRecordPage(CisCdrOrderPlanRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisCdrOrderPlanRecord> getSpecification(CisCdrOrderPlanRecordQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getPatCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patCode"), qto.getPatCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderId"), qto.getOrderId()));
            }
            if (StringUtils.isNotBlank(qto.getApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("applyId"), qto.getApplyId()));
            }
            if (qto.getOrderClass() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderClass"), qto.getOrderClass()));
            }
            return predicate;
        };
    }

    private static CisCdrOrderPlanRecordRepository dao() {
        return SpringUtil.getBean(CisCdrOrderPlanRecordRepository.class);
    }

    public static LocalDateTime getLastCreatedDate() {
        return dao().findTopByCreatedDate().orElse(new CisCdrOrderPlanRecord()).getCreatedDate();
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getPatCode() {
        return patCode;
    }

    protected void setPatCode(String patCode) {
        this.patCode = patCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getOrderId() {
        return orderId;
    }

    protected void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getApplyId() {
        return applyId;
    }

    protected void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    protected void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    protected void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getExecuteOrg() {
        return executeOrg;
    }

    protected void setExecuteOrg(String executeOrg) {
        this.executeOrg = executeOrg;
    }

    public String getExecuteStaff() {
        return executeStaff;
    }

    protected void setExecuteStaff(String executeStaff) {
        this.executeStaff = executeStaff;
    }

    public LocalDateTime getExecuteDate() {
        return executeDate;
    }

    protected void setExecuteDate(LocalDateTime executeDate) {
        this.executeDate = executeDate;
    }

    public String getDrugCode() {
        return drugCode;
    }

    protected void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    public String getExecuteStaffName() {
        return executeStaffName;
    }

    public void setExecuteStaffName(String executeStaffName) {
        this.executeStaffName = executeStaffName;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    @Transient
    public List<CisCdrOrderPlanDetailRecord> getCisCdrOrderPlanDetailRecords() {
        return CisCdrOrderPlanDetailRecord.getByOrderPlanId(getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisCdrOrderPlanRecord other = (CisCdrOrderPlanRecord) obj;
        return Objects.equals(id, other.id);
    }

    public CisCdrOrderPlanRecord create(CisCdrOrderPlanRecordNto cisCdrOrderPlanRecordNto) {
        Assert.notNull(cisCdrOrderPlanRecordNto, "参数cisCdrOrderPlanRecordNto不能为空！");

        setId(cisCdrOrderPlanRecordNto.getId());
        setPatCode(cisCdrOrderPlanRecordNto.getPatCode());
        setVisitCode(cisCdrOrderPlanRecordNto.getVisitCode());
        setOrderId(cisCdrOrderPlanRecordNto.getOrderId());
        setApplyId(cisCdrOrderPlanRecordNto.getApplyId());
        setOrderClass(cisCdrOrderPlanRecordNto.getOrderClass());
        setServiceItemName(cisCdrOrderPlanRecordNto.getServiceItemName());
        setExecuteOrg(cisCdrOrderPlanRecordNto.getExecuteOrg());
        setExecuteStaff(cisCdrOrderPlanRecordNto.getExecuteStaff());
        setExecuteDate(cisCdrOrderPlanRecordNto.getExecuteDate());
        setDrugCode(cisCdrOrderPlanRecordNto.getDrugCode());
        setCreatedDate(cisCdrOrderPlanRecordNto.getCreatedDate());
        setExecuteOrgName(cisCdrOrderPlanRecordNto.getExecuteOrgName());
        setExecuteStaffName(cisCdrOrderPlanRecordNto.getExecuteStaffName());
        setSpec(cisCdrOrderPlanRecordNto.getSpec());
        setUsageName(cisCdrOrderPlanRecordNto.getUsageName());
        setFrequencyName(cisCdrOrderPlanRecordNto.getFrequencyName());
        setDosage(cisCdrOrderPlanRecordNto.getDosage());
        setDosageUnitName(cisCdrOrderPlanRecordNto.getDosageUnitName());

        dao().save(this);
        if (cisCdrOrderPlanRecordNto.getCisCdrOrderPlanDetailRecords() != null) {
            for (CisCdrOrderPlanDetailRecordNto cisCdrOrderPlanDetailRecordNto_ : cisCdrOrderPlanRecordNto.getCisCdrOrderPlanDetailRecords()) {
                CisCdrOrderPlanDetailRecord cisCdrOrderPlanDetailRecord = new CisCdrOrderPlanDetailRecord();
                cisCdrOrderPlanDetailRecord.create(getId(), cisCdrOrderPlanDetailRecordNto_);
            }
        }
        return this;
    }

    public void delete() {
        for (CisCdrOrderPlanDetailRecord cisCdrOrderPlanDetailRecord : CisCdrOrderPlanDetailRecord.getByOrderPlanId(getId())) {
            cisCdrOrderPlanDetailRecord.delete();
        }
        // CisCdrOrderPlanDetailRecord.deleteByOrderPlanId(getId());
        dao().delete(this);
    }
}
