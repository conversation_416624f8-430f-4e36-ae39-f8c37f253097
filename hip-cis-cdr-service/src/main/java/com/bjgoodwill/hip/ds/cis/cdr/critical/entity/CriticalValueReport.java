package com.bjgoodwill.hip.ds.cis.cdr.critical.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cdr.critical.repository.CriticalValueReportRepository;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportEto;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportNto;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "危急值报告")
@Table(name = "cis_critical_value_report", indexes = {@Index(name = "idx_apply_code", columnList = "apply_code"),
        @Index(name = "IDX_cis_critical_value_report_inDeptCode", columnList = "inDeptCode"),
        @Index(name = "IDX_cis_critical_value_report_visitOrgCode", columnList = "visitOrgCode"),
        @Index(name = "IDX_cis_critical_value_report_visitCode", columnList = "visitCode"),
        @Index(name = "IDX_cis_critical_value_report_doctCode", columnList = "doctCode")}, uniqueConstraints = {})
public class CriticalValueReport {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;

    @Enumerated(EnumType.STRING)
    @Comment("患者类型")
    @Column(name = "visit_type", nullable = true)
    private VisitTypeEnum visitType;

    @Comment("住院号")
    @Column(name = "inpatient_code", nullable = true)
    private String inpatientCode;

    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = true)
    private String visitCode;

    @Comment("患者在院科室编码")
    @Column(name = "in_dept_code", nullable = true)
    private String inDeptCode;

    @Comment("门诊开单科室编码")
    @Column(name = "visit_org_code", nullable = true)
    private String visitOrgCode;

    @Comment("开单医生编码")
    @Column(name = "doct_code", nullable = true)
    private String doctCode;

    @Comment("申请单号")
    @Column(name = "apply_code", nullable = true)
    private String applyCode;

    @Comment("服务项目名称")
    @Column(name = "service_item_code", nullable = true)
    private String serviceItemCode;

    @Comment("服务项目名称")
    @Column(name = "service_item_name", nullable = true)
    private String serviceItemName;

    @Comment("危急值医嘱Id")
    @Column(name = "order_id", nullable = true)
    private String orderId;

    @Comment("医嘱开立时间")
    @Column(name = "order_created_date", nullable = true)
    private LocalDateTime orderCreatedDate;

    @Comment("采样日期")
    @Column(name = "sampling_date", nullable = true)
    private LocalDateTime samplingDate;

    @Comment("调用方标识")
    @Column(name = "call", nullable = true)
    private String call;

    @Comment("调用Url")
    @Column(name = "call_url", nullable = true)
    private String callUrl;

    @Comment("调用方唯一标识")
    @Column(name = "crisis_id", nullable = true)
    private String crisisId;

    @Comment("危急值上报人")
    @Column(name = "report_user", nullable = true)
    private String reportUser;

    @Comment("危急值上报日期")
    @Column(name = "report_date", nullable = true)
    private LocalDateTime reportDate;

    @Comment("危急值上报科室")
    @Column(name = "report_org_code", nullable = true)
    private String reportOrgCode;

    @Comment("危急值描述")
    @Column(name = "critical_value", nullable = true)
    private String criticalValue;

    @Comment("医技电话发起人")
    @Column(name = "tel_response_user", nullable = true)
    private String telResponseUser;

    @Comment("医技电话发起时间")
    @Column(name = "tel_response_date", nullable = true)
    private LocalDateTime telResponseDate;

    @Comment("医技补充说明")
    @Column(name = "tel_response_remark", nullable = true)
    private String telResponseRemark;

    @Comment("医技电话确认标识")
    @Column(name = "tel_reponse_flag", nullable = true)
    private Boolean telReponseFlag;

    @Comment("报告人姓名")
    @Column(name = "report_user_name", nullable = true)
    private String reportUserName;

    @Comment("报告科室名称")
    @Column(name = "report_org_name", nullable = true)
    private String reportOrgName;

    @Comment("应答科室")
    @Column(name = "response_org_code", nullable = true)
    private String responseOrgCode;

    @Comment("临床答复人")
    @Column(name = "response_user", nullable = true)
    private String responseUser;

    @Comment("临床答复时间")
    @Column(name = "response_date", nullable = true)
    private LocalDateTime responseDate;

    @Comment("临床答复内容")
    @Column(name = "response_value", nullable = true)
    private String responseValue;

    @Comment("应答科室名称")
    @Column(name = "response_org_name", nullable = true)
    private String responseOrgName;

    @Comment("应答人姓名")
    @Column(name = "response_user_name", nullable = true)
    private String responseUserName;

    @Comment("病程记录")
    @Column(name = "course_record", nullable = true)
    private String courseRecord;

    @Comment("病程记录人")
    @Column(name = "course_record_user", nullable = true)
    private String courseRecordUser;

    @Comment("病程记录时间")
    @Column(name = "course_record_date", nullable = true)
    private LocalDateTime courseRecordDate;

    @Comment("答复状态（未答复、已答复）")
    @Column(name = "status_code", nullable = true)
    private Boolean statusCode;

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;

    @Comment("创建人员")
    @Column(name = "created_staff", nullable = true)
    private String createdStaff;

    @Comment("创建人员姓名")
    @Column(name = "created_staff_name", nullable = true)
    private String createdStaffName;

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;

    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    protected void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    protected void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getInDeptCode() {
        return inDeptCode;
    }

    protected void setInDeptCode(String inDeptCode) {
        this.inDeptCode = inDeptCode;
    }

    public String getVisitOrgCode() {
        return visitOrgCode;
    }

    protected void setVisitOrgCode(String visitOrgCode) {
        this.visitOrgCode = visitOrgCode;
    }

    public String getDoctCode() {
        return doctCode;
    }

    protected void setDoctCode(String doctCode) {
        this.doctCode = doctCode;
    }

    public String getApplyCode() {
        return applyCode;
    }

    protected void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    protected void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    protected void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getOrderId() {
        return orderId;
    }

    protected void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public LocalDateTime getOrderCreatedDate() {
        return orderCreatedDate;
    }

    protected void setOrderCreatedDate(LocalDateTime orderCreatedDate) {
        this.orderCreatedDate = orderCreatedDate;
    }

    public LocalDateTime getSamplingDate() {
        return samplingDate;
    }

    protected void setSamplingDate(LocalDateTime samplingDate) {
        this.samplingDate = samplingDate;
    }

    public String getCall() {
        return call;
    }

    protected void setCall(String call) {
        this.call = call;
    }

    public String getCallUrl() {
        return callUrl;
    }

    protected void setCallUrl(String callUrl) {
        this.callUrl = callUrl;
    }

    public String getCrisisId() {
        return crisisId;
    }

    protected void setCrisisId(String crisisId) {
        this.crisisId = crisisId;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getReportUser() {
        return reportUser;
    }

    protected void setReportUser(String reportUser) {
        this.reportUser = reportUser;
    }

    public LocalDateTime getReportDate() {
        return reportDate;
    }

    protected void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    public String getReportOrgCode() {
        return reportOrgCode;
    }

    protected void setReportOrgCode(String reportOrgCode) {
        this.reportOrgCode = reportOrgCode;
    }

    public String getCriticalValue() {
        return criticalValue;
    }

    protected void setCriticalValue(String criticalValue) {
        this.criticalValue = criticalValue;
    }

    public String getTelResponseUser() {
        return telResponseUser;
    }

    protected void setTelResponseUser(String telResponseUser) {
        this.telResponseUser = telResponseUser;
    }

    public LocalDateTime getTelResponseDate() {
        return telResponseDate;
    }

    protected void setTelResponseDate(LocalDateTime telResponseDate) {
        this.telResponseDate = telResponseDate;
    }

    public String getTelResponseRemark() {
        return telResponseRemark;
    }

    protected void setTelResponseRemark(String telResponseRemark) {
        this.telResponseRemark = telResponseRemark;
    }

    public Boolean getTelReponseFlag() {
        return telReponseFlag;
    }

    protected void setTelReponseFlag(Boolean telReponseFlag) {
        this.telReponseFlag = telReponseFlag;
    }

    public String getReportUserName() {
        return reportUserName;
    }

    public void setReportUserName(String reportUserName) {
        this.reportUserName = reportUserName;
    }

    public String getReportOrgName() {
        return reportOrgName;
    }

    public void setReportOrgName(String reportOrgName) {
        this.reportOrgName = reportOrgName;
    }

    public String getResponseOrgCode() {
        return responseOrgCode;
    }

    protected void setResponseOrgCode(String responseOrgCode) {
        this.responseOrgCode = responseOrgCode;
    }

    public String getResponseUser() {
        return responseUser;
    }

    protected void setResponseUser(String responseUser) {
        this.responseUser = responseUser;
    }

    public LocalDateTime getResponseDate() {
        return responseDate;
    }

    protected void setResponseDate(LocalDateTime responseDate) {
        this.responseDate = responseDate;
    }

    public String getResponseValue() {
        return responseValue;
    }

    protected void setResponseValue(String responseValue) {
        this.responseValue = responseValue;
    }

    public String getResponseOrgName() {
        return responseOrgName;
    }

    public void setResponseOrgName(String responseOrgName) {
        this.responseOrgName = responseOrgName;
    }

    public String getResponseUserName() {
        return responseUserName;
    }

    public void setResponseUserName(String responseUserName) {
        this.responseUserName = responseUserName;
    }

    public String getCourseRecord() {
        return courseRecord;
    }

    protected void setCourseRecord(String courseRecord) {
        this.courseRecord = courseRecord;
    }

    public String getCourseRecordUser() {
        return courseRecordUser;
    }

    protected void setCourseRecordUser(String courseRecordUser) {
        this.courseRecordUser = courseRecordUser;
    }

    public LocalDateTime getCourseRecordDate() {
        return courseRecordDate;
    }

    protected void setCourseRecordDate(LocalDateTime courseRecordDate) {
        this.courseRecordDate = courseRecordDate;
    }

    public Boolean getStatusCode() {
        return statusCode;
    }

    protected void setStatusCode(Boolean statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CriticalValueReport other = (CriticalValueReport) obj;
        return Objects.equals(id, other.id);
    }

    private static CriticalValueReportRepository dao() {
        return SpringUtil.getBean(CriticalValueReportRepository.class);
    }

    /**
     * 根据id查询危急值报告
     * @param id 危急值报告id
     * @return 危急值报告信息
     */
    public static Optional<CriticalValueReport> getCriticalValueReportById(String id) {
        return dao().findById(id);
    }

    /**
     * 根据条件查询危急值报告列表
     * @param qto 查询条件
     * @return 危急值报告列表
     */
    public static List<CriticalValueReport> getCriticalValueReports(CriticalValueReportQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    /**
     * 根据条件分页查询危急值报告列表
     * @param qto 查询条件
     * @return 危急值报告分页信息
     */
    public static Page<CriticalValueReport> getCriticalValueReportPage(CriticalValueReportQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * 根据条件构建查询条件
     * @param qto 查询条件
     * @return 查询条件
     */
    private static Specification<CriticalValueReport> getSpecification(CriticalValueReportQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getInpatientCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inpatientCode"), qto.getInpatientCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getInDeptCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inDeptCode"), qto.getInDeptCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitOrgCode"), qto.getVisitOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getDoctCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("doctCode"), qto.getDoctCode()));
            }
            if (StringUtils.isNotBlank(qto.getApplyCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("applyCode"), qto.getApplyCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemName"), qto.getServiceItemName()));
            }
            if (StringUtils.isNotBlank(qto.getReportUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUser"), qto.getReportUser()));
            }
            if (qto.getReportDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("reportDate"), LocalDateUtil.beginOfDay(qto.getBeginReportDate()), LocalDateUtil.endOfDay(qto.getEndReportDate())));
            }
            if (StringUtils.isNotBlank(qto.getReportOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportOrgCode"), qto.getReportOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getCriticalValue())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("criticalValue"), qto.getCriticalValue()));
            }
            if (StringUtils.isNotBlank(qto.getResponseOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("responseOrgCode"), qto.getResponseOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getResponseUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("responseUser"), qto.getResponseUser()));
            }
            if (qto.getResponseDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("responseDate"), LocalDateUtil.beginOfDay(qto.getResponseDate()), LocalDateUtil.endOfDay(qto.getResponseDate())));
            }
            if (StringUtils.isNotBlank(qto.getResponseValue())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("responseValue"), qto.getResponseValue()));
            }
            if (StringUtils.isNotBlank(qto.getTelResponseUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("telResponseUser"), qto.getTelResponseUser()));
            }
            if (qto.getTelResponseDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("telResponseDate"), LocalDateUtil.beginOfDay(qto.getTelResponseDate()), LocalDateUtil.endOfDay(qto.getTelResponseDate())));
            }
            if (StringUtils.isNotBlank(qto.getTelResponseRemark())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("telResponseRemark"), qto.getTelResponseRemark()));
            }
            if (qto.getTelReponseFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("telReponseFlag"), qto.getTelReponseFlag()));
            }
            if (StringUtils.isNotBlank(qto.getOrderId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderId"), qto.getOrderId()));
            }
            if (StringUtils.isNotBlank(qto.getCourseRecord())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("courseRecord"), qto.getCourseRecord()));
            }
            if (StringUtils.isNotBlank(qto.getCourseRecordUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("courseRecordUser"), qto.getCourseRecordUser()));
            }
            if (qto.getCourseRecordDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("courseRecordDate"), LocalDateUtil.beginOfDay(qto.getCourseRecordDate()), LocalDateUtil.endOfDay(qto.getCourseRecordDate())));
            }
            if (qto.getStatusCode() != null) {
                if(Boolean.TRUE.equals(qto.getStatusCode())){
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
                }else{
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(
                            criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()),
                            criteriaBuilder.isNull(root.get("statusCode"))
                    ));
                }
            }
            if (qto.getOrderCreatedDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("orderCreatedDate"), LocalDateUtil.beginOfDay(qto.getOrderCreatedDate()), LocalDateUtil.endOfDay(qto.getOrderCreatedDate())));
            }
            if (qto.getSamplingDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("samplingDate"), LocalDateUtil.beginOfDay(qto.getSamplingDate()), LocalDateUtil.endOfDay(qto.getSamplingDate())));
            }
            if (StringUtils.isNotBlank(qto.getCall())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("call"), qto.getCall()));
            }
            if (StringUtils.isNotBlank(qto.getCallUrl())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("callUrl"), qto.getCallUrl()));
            }
            if (StringUtils.isNotBlank(qto.getCrisisId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("crisisId"), qto.getCrisisId()));
            }
            return predicate;
        };
    }

    /**
     * 创建危急值报告
     * @param criticalValueReportNto 危急值报告信息
     * @return 危急值报告信息
     */
    public CriticalValueReport create(CriticalValueReportNto criticalValueReportNto) {
        Assert.notNull(criticalValueReportNto, "参数criticalValueReportNto不能为空！");

        setId(criticalValueReportNto.getId());
        setVisitType(criticalValueReportNto.getVisitType());
        setInpatientCode(criticalValueReportNto.getInpatientCode());
        setVisitCode(criticalValueReportNto.getVisitCode());
        setInDeptCode(criticalValueReportNto.getInDeptCode());
        setVisitOrgCode(criticalValueReportNto.getVisitOrgCode());
        setDoctCode(criticalValueReportNto.getDoctCode());
        setApplyCode(criticalValueReportNto.getApplyCode());
        setServiceItemCode(criticalValueReportNto.getServiceItemCode());
        setServiceItemName(criticalValueReportNto.getServiceItemName());
        setReportUser(criticalValueReportNto.getReportUser());
        setReportDate(criticalValueReportNto.getReportDate());
        setReportOrgCode(criticalValueReportNto.getReportOrgCode());
        setCriticalValue(criticalValueReportNto.getCriticalValue());
        setTelResponseUser(criticalValueReportNto.getTelResponseUser());
        setTelResponseDate(criticalValueReportNto.getTelResponseDate());
        setTelResponseRemark(criticalValueReportNto.getTelResponseRemark());
        setTelReponseFlag(criticalValueReportNto.getTelReponseFlag());
        setOrderId(criticalValueReportNto.getOrderId());
        setCourseRecord(criticalValueReportNto.getCourseRecord());
        setCourseRecordUser(criticalValueReportNto.getCourseRecordUser());
        setCourseRecordDate(criticalValueReportNto.getCourseRecordDate());
        setStatusCode(criticalValueReportNto.getStatusCode());
        setOrderCreatedDate(criticalValueReportNto.getOrderCreatedDate());
        setSamplingDate(criticalValueReportNto.getSamplingDate());
        setCall(criticalValueReportNto.getCall());
        setCallUrl(criticalValueReportNto.getCallUrl());
        setCrisisId(criticalValueReportNto.getCrisisId());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setReportOrgName(criticalValueReportNto.getReportOrgName());
        setReportUserName(criticalValueReportNto.getReportUserName());
        dao().save(this);
        return this;
    }

    /**
     * 更新危急值报告信息
     * @param criticalValueReportEto 危急值报告信息
     */
    public void update(CriticalValueReportEto criticalValueReportEto) {
        setServiceItemCode(criticalValueReportEto.getServiceItemCode());
        setServiceItemName(criticalValueReportEto.getServiceItemName());
        setReportUser(criticalValueReportEto.getReportUser());
        setReportDate(criticalValueReportEto.getReportDate());
        setReportOrgCode(criticalValueReportEto.getReportOrgCode());
        setCriticalValue(criticalValueReportEto.getCriticalValue());
        setResponseOrgCode(criticalValueReportEto.getResponseOrgCode());
        setResponseUser(criticalValueReportEto.getResponseUser());
        setResponseDate(criticalValueReportEto.getResponseDate());
        setResponseValue(criticalValueReportEto.getResponseValue());
        setTelResponseUser(criticalValueReportEto.getTelResponseUser());
        setTelResponseDate(criticalValueReportEto.getTelResponseDate());
        setTelResponseRemark(criticalValueReportEto.getTelResponseRemark());
        setTelReponseFlag(criticalValueReportEto.getTelReponseFlag());
        setOrderId(criticalValueReportEto.getOrderId());
        setCourseRecord(criticalValueReportEto.getCourseRecord());
        setCourseRecordUser(criticalValueReportEto.getCourseRecordUser());
        setCourseRecordDate(criticalValueReportEto.getCourseRecordDate());
        setStatusCode(criticalValueReportEto.getStatusCode());
        setOrderCreatedDate(criticalValueReportEto.getOrderCreatedDate());
        setSamplingDate(criticalValueReportEto.getSamplingDate());
        setCrisisId(criticalValueReportEto.getCrisisId());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
    }

    /**
     * 删除危急值报告
     */
    public void delete() {
        dao().delete(this);
    }

    /**
     * 回复危急值报告
     * @param criticalValueReportEto 危急值报告信息
     */
    public void replyCriticalValueReport(CriticalValueReportEto criticalValueReportEto) {
        setResponseOrgCode(criticalValueReportEto.getResponseOrgCode());
        setResponseOrgName(criticalValueReportEto.getResponseOrgName());
        setResponseUser(criticalValueReportEto.getResponseUser());
        setResponseUserName(criticalValueReportEto.getResponseUserName());
        setResponseDate(criticalValueReportEto.getResponseDate());
        setResponseValue(criticalValueReportEto.getResponseValue());
        setStatusCode(true);
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
    }

}
