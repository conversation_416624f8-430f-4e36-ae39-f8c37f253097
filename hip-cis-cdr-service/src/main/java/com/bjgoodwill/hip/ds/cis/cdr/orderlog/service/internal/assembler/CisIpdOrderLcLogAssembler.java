package com.bjgoodwill.hip.ds.cis.cdr.orderlog.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.orderlog.entity.CisIpdOrderLcLog;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisIpdOrderLcLogTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisIpdOrderLcLogAssembler {

    public static List<CisIpdOrderLcLogTo> toTos(List<CisIpdOrderLcLog> cisIpdOrderLcLogs) {
        return toTos(cisIpdOrderLcLogs, false);
    }

    public static List<CisIpdOrderLcLogTo> toTos(List<CisIpdOrderLcLog> cisIpdOrderLcLogs, boolean withAllParts) {
        Assert.notNull(cisIpdOrderLcLogs, "参数cisIpdOrderLcLogs不能为空！");

        List<CisIpdOrderLcLogTo> tos = new ArrayList<>();
        for (CisIpdOrderLcLog cisIpdOrderLcLog : cisIpdOrderLcLogs)
            tos.add(toTo(cisIpdOrderLcLog, withAllParts));
        return tos;
    }

    public static CisIpdOrderLcLogTo toTo(CisIpdOrderLcLog cisIpdOrderLcLog) {
        return toTo(cisIpdOrderLcLog, false);
    }

    /**
     * @generated
     */
    public static CisIpdOrderLcLogTo toTo(CisIpdOrderLcLog cisIpdOrderLcLog, boolean withAllParts) {
        if (cisIpdOrderLcLog == null)
            return null;
        CisIpdOrderLcLogTo to = new CisIpdOrderLcLogTo();
        to.setId(cisIpdOrderLcLog.getId());
        to.setOrderId(cisIpdOrderLcLog.getOrderId());
        to.setPatMiCode(cisIpdOrderLcLog.getPatMiCode());
        to.setVisitCode(cisIpdOrderLcLog.getVisitCode());
        to.setOrderNo(cisIpdOrderLcLog.getOrderNo());
        to.setOrderName(cisIpdOrderLcLog.getOrderName());
        to.setExecLogType(cisIpdOrderLcLog.getExecLogType());
        to.setOrgCode(cisIpdOrderLcLog.getOrgCode());
        to.setOrgName(cisIpdOrderLcLog.getOrgName());
        to.setReMark(cisIpdOrderLcLog.getReMark());
        to.setCreatedDate(cisIpdOrderLcLog.getCreatedDate());
        to.setCreatedStaff(cisIpdOrderLcLog.getCreatedStaff());
        to.setCreatedStaffName(cisIpdOrderLcLog.getCreatedStaffName());
        to.setDeleted(cisIpdOrderLcLog.isDeleted());
        to.setPatCode(cisIpdOrderLcLog.getPatCode());
        to.setOrderSplitId(cisIpdOrderLcLog.getOrderSplitId());

        if (withAllParts) {
        }
        return to;
    }

}