package com.bjgoodwill.hip.ds.cis.cdr.order.repository;

import com.bjgoodwill.hip.ds.cis.cdr.order.entity.CisCdrOpdOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.order.repository.CisCdrOpdOrderRepository")
public interface CisCdrOpdOrderRepository extends JpaRepository<CisCdrOpdOrder, String>, JpaSpecificationExecutor<CisCdrOpdOrder> {

}