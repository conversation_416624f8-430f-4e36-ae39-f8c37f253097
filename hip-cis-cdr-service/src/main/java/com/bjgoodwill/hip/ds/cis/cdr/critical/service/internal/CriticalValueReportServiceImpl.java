package com.bjgoodwill.hip.ds.cis.cdr.critical.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.critical.entity.CriticalValueReport;
import com.bjgoodwill.hip.ds.cis.cdr.critical.service.CriticalValueReportService;
import com.bjgoodwill.hip.ds.cis.cdr.critical.service.internal.assembler.CriticalValueReportAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportEto;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportNto;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportQto;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.critical.service.CriticalValueReportService")
@RequestMapping(value = "/api/cdr/critical/criticalValueReport", produces = "application/json; charset=utf-8")
public class CriticalValueReportServiceImpl implements CriticalValueReportService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CriticalValueReportTo> getCriticalValueReports(CriticalValueReportQto criticalValueReportQto) {
        return CriticalValueReportAssembler.toTos(CriticalValueReport.getCriticalValueReports(criticalValueReportQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CriticalValueReportTo> getCriticalValueReportPage(CriticalValueReportQto criticalValueReportQto) {
        Page<CriticalValueReport> page = CriticalValueReport.getCriticalValueReportPage(criticalValueReportQto);
        Page<CriticalValueReportTo> result = page.map(CriticalValueReportAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CriticalValueReportTo getCriticalValueReportById(String id){
        return CriticalValueReportAssembler.toTo(CriticalValueReport.getCriticalValueReportById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CriticalValueReportTo createCriticalValueReport(CriticalValueReportNto criticalValueReportNto) {
        CriticalValueReport criticalValueReport = new CriticalValueReport();
        criticalValueReport = criticalValueReport.create(criticalValueReportNto);
        return CriticalValueReportAssembler.toTo(criticalValueReport);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CriticalValueReportTo> createCriticalValueReportBatch(List<CriticalValueReportNto> criticalValueReportNtos) {
        return CriticalValueReportAssembler.toTos(criticalValueReportNtos.stream()
                .map(nto -> {
                    CriticalValueReport criticalValueReport = new CriticalValueReport();
                    return criticalValueReport.create(nto);
                }).toList());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCriticalValueReport(String id, CriticalValueReportEto criticalValueReportEto) {
        Optional<CriticalValueReport> criticalValueReportOptional = CriticalValueReport.getCriticalValueReportById(id);
        criticalValueReportOptional.ifPresent(criticalValueReport -> criticalValueReport.update(criticalValueReportEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCriticalValueReport(String id){
        Optional<CriticalValueReport> criticalValueReportOptional = CriticalValueReport.getCriticalValueReportById(id);
        criticalValueReportOptional.ifPresent(criticalValueReport -> criticalValueReport.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder){
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void replyCriticalValueReport(String id, CriticalValueReportEto criticalValueReportEto) {
        Optional<CriticalValueReport> criticalValueReportOptional = CriticalValueReport.getCriticalValueReportById(id);
        criticalValueReportOptional.ifPresent(criticalValueReport -> criticalValueReport.replyCriticalValueReport(criticalValueReportEto));
    }
}