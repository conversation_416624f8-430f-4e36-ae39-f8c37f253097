package com.bjgoodwill.hip.ds.cis.cdr.orderlog.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.entity.CisOrderClLog;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.service.CisOrderClLogService;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.service.internal.assembler.CisOrderClLogAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisOrderClLogQto;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisOrderClLogTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.util.List;
import java.util.Optional;

public abstract class CisOrderClLogServiceImpl implements CisOrderClLogService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderClLogTo> getCisOrderClLogs(CisOrderClLogQto cisOrderClLogQto) {
        return CisOrderClLogAssembler.toTos(CisOrderClLog.getCisOrderClLogs(cisOrderClLogQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisOrderClLogTo> getCisOrderClLogPage(CisOrderClLogQto cisOrderClLogQto) {
        Page<CisOrderClLog> page = CisOrderClLog.getCisOrderClLogPage(cisOrderClLogQto);
        Page<CisOrderClLogTo> result = page.map(CisOrderClLogAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOrderClLog(String id) {
        Optional<CisOrderClLog> cisOrderClLogOptional = CisOrderClLog.getCisOrderClLogById(id);
        cisOrderClLogOptional.ifPresent(cisOrderClLog -> cisOrderClLog.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}