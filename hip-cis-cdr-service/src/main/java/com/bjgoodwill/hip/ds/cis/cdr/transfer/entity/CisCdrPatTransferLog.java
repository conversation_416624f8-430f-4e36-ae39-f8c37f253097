package com.bjgoodwill.hip.ds.cis.cdr.transfer.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.ds.cis.cdr.transfer.repository.CisCdrPatTransferLogRepository;
import com.bjgoodwill.hip.ds.cis.cdr.transfer.to.CisCdrPatTransferLogNto;
import com.bjgoodwill.hip.ds.cis.cdr.transfer.to.CisCdrPatTransferLogQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "患者流转日志记录")
@Table(name = "cis_cdr_pat_transfer_log", indexes = {@Index(name = "cis_cdr_pat_transfer_log_visit_code", columnList = "visit_code")}, uniqueConstraints = {})
public class CisCdrPatTransferLog {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("住院流水号")
    @Column(name = "visit_code", nullable = false, length = 32)
    private String visitCode;

    @Comment("患者主索引")
    @Column(name = "pat_mi_code", nullable = false, length = 32)
    private String patMiCode;

    @Comment("流转类型:直接存汉字")
    @Column(name = "transfer_class", nullable = true, length = 32)
    private String transferClass;


    @Comment("流转时间")
    @Column(name = "transfer_date", nullable = true)
    private LocalDateTime transferDate;


    @Comment("变更前机构")
    @Column(name = "before_org", nullable = true, length = 32)
    private String beforeOrg;


    @Comment("变更后机构")
    @Column(name = "after_org", nullable = true, length = 32)
    private String afterOrg;


    @Comment("数据抽取时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;

    public static Optional<CisCdrPatTransferLog> getCisCdrPatTransferLogById(String id) {
        return dao().findById(id);
    }

    public static List<CisCdrPatTransferLog> getCisCdrPatTransferLogs(CisCdrPatTransferLogQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisCdrPatTransferLog> getCisCdrPatTransferLogPage(CisCdrPatTransferLogQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisCdrPatTransferLog> getSpecification(CisCdrPatTransferLogQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            return predicate;
        };
    }

    private static CisCdrPatTransferLogRepository dao() {
        return SpringUtil.getBean(CisCdrPatTransferLogRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getTransferClass() {
        return transferClass;
    }

    protected void setTransferClass(String transferClass) {
        this.transferClass = transferClass;
    }

    public LocalDateTime getTransferDate() {
        return transferDate;
    }

    protected void setTransferDate(LocalDateTime transferDate) {
        this.transferDate = transferDate;
    }

    public String getBeforeOrg() {
        return beforeOrg;
    }

    protected void setBeforeOrg(String beforeOrg) {
        this.beforeOrg = beforeOrg;
    }

    public String getAfterOrg() {
        return afterOrg;
    }

    protected void setAfterOrg(String afterOrg) {
        this.afterOrg = afterOrg;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisCdrPatTransferLog other = (CisCdrPatTransferLog) obj;
        return Objects.equals(id, other.id);
    }

    public CisCdrPatTransferLog create(CisCdrPatTransferLogNto cisCdrPatTransferLogNto) {
        Assert.notNull(cisCdrPatTransferLogNto, "参数cisCdrPatTransferLogNto不能为空！");

        setId(cisCdrPatTransferLogNto.getId());
        setVisitCode(cisCdrPatTransferLogNto.getVisitCode());
        setPatMiCode(cisCdrPatTransferLogNto.getPatMiCode());
        setTransferClass(cisCdrPatTransferLogNto.getTransferClass());
        setTransferDate(cisCdrPatTransferLogNto.getTransferDate());
        setBeforeOrg(cisCdrPatTransferLogNto.getBeforeOrg());
        setAfterOrg(cisCdrPatTransferLogNto.getAfterOrg());
        setCreatedDate(cisCdrPatTransferLogNto.getCreatedDate());
        dao().save(this);
        return this;
    }

    public void delete() {
        dao().delete(this);
    }

}
