package com.bjgoodwill.hip.ds.cis.cdr.orderlog.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.orderlog.entity.CisOpdOrderLcLog;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisOpdOrderLcLogTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisOpdOrderLcLogAssembler {

    public static List<CisOpdOrderLcLogTo> toTos(List<CisOpdOrderLcLog> cisOpdOrderLcLogs) {
        return toTos(cisOpdOrderLcLogs, false);
    }

    public static List<CisOpdOrderLcLogTo> toTos(List<CisOpdOrderLcLog> cisOpdOrderLcLogs, boolean withAllParts) {
        Assert.notNull(cisOpdOrderLcLogs, "参数cisOpdOrderLcLogs不能为空！");

        List<CisOpdOrderLcLogTo> tos = new ArrayList<>();
        for (CisOpdOrderLcLog cisOpdOrderLcLog : cisOpdOrderLcLogs)
            tos.add(toTo(cisOpdOrderLcLog, withAllParts));
        return tos;
    }

    public static CisOpdOrderLcLogTo toTo(CisOpdOrderLcLog cisOpdOrderLcLog) {
        return toTo(cisOpdOrderLcLog, false);
    }

    /**
     * @generated
     */
    public static CisOpdOrderLcLogTo toTo(CisOpdOrderLcLog cisOpdOrderLcLog, boolean withAllParts) {
        if (cisOpdOrderLcLog == null)
            return null;
        CisOpdOrderLcLogTo to = new CisOpdOrderLcLogTo();
        to.setId(cisOpdOrderLcLog.getId());
        to.setOrderId(cisOpdOrderLcLog.getOrderId());
        to.setPatMiCode(cisOpdOrderLcLog.getPatMiCode());
        to.setVisitCode(cisOpdOrderLcLog.getVisitCode());
        to.setOrderNo(cisOpdOrderLcLog.getOrderNo());
        to.setOrderName(cisOpdOrderLcLog.getOrderName());
        to.setExecLogType(cisOpdOrderLcLog.getExecLogType());
        to.setOrgCode(cisOpdOrderLcLog.getOrgCode());
        to.setOrgName(cisOpdOrderLcLog.getOrgName());
        to.setReMark(cisOpdOrderLcLog.getReMark());
        to.setCreatedDate(cisOpdOrderLcLog.getCreatedDate());
        to.setCreatedStaff(cisOpdOrderLcLog.getCreatedStaff());
        to.setCreatedStaffName(cisOpdOrderLcLog.getCreatedStaffName());
        to.setDeleted(cisOpdOrderLcLog.isDeleted());

        if (withAllParts) {
        }
        return to;
    }

}