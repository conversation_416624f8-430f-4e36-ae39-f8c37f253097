package com.bjgoodwill.hip.ds.cis.cdr.record.service.internal;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-03-25 10:42
 * @className: CisCdrClinicRecordServiceTangibleImpl
 * @description:
 **/
@RestController("com.bjgoodwill.hip.ds.cis.cdr.record.service.CisCdrClinicRecordService")
@RequestMapping(value = "/api/cdr/record/cisCdrClinicRecord", produces = "application/json; charset=utf-8")
public class CisCdrClinicRecordServiceTangibleImpl extends CisCdrClinicRecordServiceImpl {

}