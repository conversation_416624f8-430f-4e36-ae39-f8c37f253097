package com.bjgoodwill.hip.ds.cis.cdr.report.repository;

import com.bjgoodwill.hip.ds.cis.cdr.report.entity.CisReportResultDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.report.repository.CisReportResultDetailRepository")
public interface CisReportResultDetailRepository extends JpaRepository<CisReportResultDetail, String>, JpaSpecificationExecutor<CisReportResultDetail> {

    List<CisReportResultDetail> findByCisReportResultId(String cisReportResultId);

    Page<CisReportResultDetail> findByCisReportResultId(String cisReportResultId, Pageable pageable);

    boolean existsByCisReportResultId(String cisReportResultId);

    void deleteByCisReportResultId(String cisReportResultId);

}