package com.bjgoodwill.hip.ds.cis.cdr.order.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SkinTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cdr.order.repository.CisCdrOrderRepository;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderEto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderNto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Formula;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "Cdr的医嘱表")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "orderType", discriminatorType = DiscriminatorType.STRING, length = 20)
@Table(name = "cis_cdr_order", indexes = {@Index(name = "cis_cdr_order_visit_code", columnList = "visit_code")
        , @Index(name = "cis_cdr_order_apply_code", columnList = "apply_code"),
        @Index(name = "cis_cdr_order_pat_mi_code", columnList = "pat_mi_code")}, uniqueConstraints = {})
public abstract class CisCdrOrder {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


//    @Comment("分类，0门诊，1临时 2 住院")
//    @Column(name = "order_type", nullable = true)
//    private String orderType;


    @Comment("医嘱序号")
    @Column(name = "sort_no", nullable = false)
    private String sortNo;


    @Comment("主索引")
    @Column(name = "pat_mi_code", nullable = false)
    private String patMiCode;


    @Comment("流水号")
    @Column(name = "visit_code", nullable = false)
    private String visitCode;


    @Comment("医嘱编码")
    @Column(name = "order_service_code", nullable = true)
    private String orderServiceCode;

    private String orderContent;


    @Enumerated(EnumType.STRING)
    @Comment("医嘱类型")
    @Column(name = "order_class", nullable = false)
    private SystemTypeEnum orderClass;


    @Comment("申请单号")
    @Column(name = "apply_code", nullable = false)
    private String applyCode;


    @Comment("疗程")
    @Column(name = "treatment_course", nullable = false)
    private long treatmentCourse;


    @Comment("疗程单位")
    @Column(name = "treatment_course_unit", nullable = true)
    private String treatmentCourseUnit;


    @Comment("婴儿标记")
    @Column(name = "baby_flag", nullable = true)
    private Boolean babyFlag;


    @Comment("执行科室编码")
    @Column(name = "execute_org_code", nullable = false)
    private String executeOrgCode;


    @Comment("执行科室名称")
    @Column(name = "execute_org_name", nullable = true)
    private String executeOrgName;


    @Comment("护理组编码")
    @Column(name = "dept_nurse_code", nullable = true)
    private String deptNurseCode;


    @Comment("护理组名称")
    @Column(name = "dept_nurse_name", nullable = true)
    private String deptNurseName;


    @Comment("医嘱开始时间")
    @Column(name = "effective_low_date", nullable = true)
    private LocalDateTime effectiveLowDate;


    @Comment("补录标识,1补录")
    @Column(name = "repair_flag", nullable = true)
    private Boolean repairFlag;


    @Comment("主从父节点")
    @Column(name = "parent_code", nullable = true)
    private String parentCode;


    @Comment("危急值ID")
    @Column(name = "critical_id", nullable = true)
    private String criticalId;


    @Comment("协定处方")
    @Column(name = "prescription_flag", nullable = true)
    private Boolean prescriptionFlag;


    @Comment("合理用药")
    @Column(name = "pass_value", nullable = true)
    private String passValue;


    @Comment("第三方")
    @Column(name = "third_flag", nullable = true)
    private String thirdFlag;


    @Comment("领药科室编码")
    @Column(name = "receive_org_code", nullable = true)
    private String receiveOrgCode;


    @Comment("领药科室名称")
    @Column(name = "receive_org_name", nullable = true)
    private String receiveOrgName;


    @Comment("用法编码")
    @Column(name = "usage", nullable = true)
    private String usage;


    @Comment("用法名称")
    @Column(name = "usage_name", nullable = true)
    private String usageName;


    @Enumerated(EnumType.STRING)
    @Comment("取药方式")
    @Column(name = "sbadm_way", nullable = true)
    private SbadmWayEnum sbadmWay;


    @Comment("皮试标记")
    @Column(name = "skin_flag", nullable = true)
    private Boolean skinFlag;


    @Enumerated(EnumType.STRING)
    @Comment("皮试结果")
    @Column(name = "skin_type", nullable = true)
    private SkinTypeEnum skinType;


    @Comment("创建医生")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的创建医生姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = true)
    private LocalDateTime createdDate;


    @Comment("签发时间")
    @Column(name = "commit_date", nullable = true)
    private LocalDateTime commitDate;


    @Comment("签发人")
    @Column(name = "submit_staff_id", nullable = true)
    private String submitStaffId;


    @Comment("签发人姓名")
    @Column(name = "submit_staff_name", nullable = true)
    private String submitStaffName;


    @Comment("校对人")
    @Column(name = "proof_staff", nullable = true)
    private String proofStaff;


    @Comment("校对人姓名")
    @Column(name = "proof_staff_name", nullable = true)
    private String proofStaffName;


    @Comment("特显符合标识 1符合 0不符合 null不是特限项目")
    @Column(name = "limit_conform_flag", nullable = true)
    private Boolean limitConformFlag;


    @Comment("医院编码")
    @Column(name = "hospital_code", nullable = true)
    private String hospitalCode;


    @Comment("医院名称")
    @Column(name = "hospital_name", nullable = true)
    private String hospitalName;


    @Comment("作废人")
    @Column(name = "cancel_staff", nullable = true)
    private String cancelStaff;


    @Comment("作废人姓名")
    @Column(name = "cancel_staff_name", nullable = true)
    private String cancelStaffName;


    @Comment("作废时间")
    @Column(name = "cancel_date", nullable = true)
    private LocalDateTime cancelDate;


    @Comment("作废原因")
    @Column(name = "cancel_remark", nullable = true)
    private String cancelRemark;


    @Comment("备注")
    @Column(name = "re_mark", nullable = true)
    private String reMark;


    @Enumerated(EnumType.STRING)
    @Comment("状态")
    @Column(name = "status_code", nullable = true)
    private CisStatusEnum statusCode;

    public static Optional<CisCdrOrder> getCisCdrOrderById(String id) {
        return dao().findById(id);
    }

    public static List<CisCdrOrder> findCisCdrOrderByCreateDateAfter(LocalDateTime dateTime, SystemTypeEnum orderCLass) {
        return dao().findCisCdrOrderByCreateDateAfter(dateTime, orderCLass);
    }

    public static List<CisCdrOrder> getCisCdrOrders(CisCdrOrderQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisCdrOrder> getCisCdrOrderPage(CisCdrOrderQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static CisCdrOrder newInstanceByNto(CisCdrOrderNto cisCdrOrderNto) {
        try {
            return (CisCdrOrder) Class.forName("com.bjgoodwill.hip.ds.cis.cdr.order.entity."
                    + StringUtils.removeEnd(cisCdrOrderNto.getClass().getSimpleName(), "Nto")).getConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @generated
     */
    private static Specification<CisCdrOrder> getSpecification(CisCdrOrderQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getOrderType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderType"), qto.getOrderType()));
            }
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (qto.getOrderClass() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderClass"), qto.getOrderClass()));
            }

            if (qto.getStartDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("commitDate"), LocalDateUtil.beginOfDay(qto.getStartDate())));
            }
            if (qto.getEndDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("commitDate"), LocalDateUtil.beginOfDay(qto.getEndDate())));
            }

            return predicate;
        };
    }

    private static CisCdrOrderRepository dao() {
        return SpringUtil.getBean(CisCdrOrderRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

//    public String getOrderType() {
//        return orderType;
//    }
//
//    protected void setOrderType(String orderType) {
//        this.orderType = orderType;
//    }

    public String getSortNo() {
        return sortNo;
    }

    protected void setSortNo(String sortNo) {
        this.sortNo = sortNo;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    protected void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getOrderServiceCode() {
        return orderServiceCode;
    }

    protected void setOrderServiceCode(String orderServiceCode) {
        this.orderServiceCode = orderServiceCode;
    }

    @Formula("select a.order_content from cis_cdr_order_content a where a.order_id = id")
    public String getOrderContent() {
        return orderContent;
    }

    @Transient
    public void setOrderContent(String orderContent) {
        this.orderContent = orderContent;
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    protected void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    public String getApplyCode() {
        return applyCode;
    }

    protected void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public long getTreatmentCourse() {
        return treatmentCourse;
    }

    protected void setTreatmentCourse(long treatmentCourse) {
        this.treatmentCourse = treatmentCourse;
    }

    public String getTreatmentCourseUnit() {
        return treatmentCourseUnit;
    }

    protected void setTreatmentCourseUnit(String treatmentCourseUnit) {
        this.treatmentCourseUnit = treatmentCourseUnit;
    }

    public Boolean getBabyFlag() {
        return babyFlag;
    }

    protected void setBabyFlag(Boolean babyFlag) {
        this.babyFlag = babyFlag;
    }

    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    protected void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = executeOrgCode;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    protected void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    protected void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    public String getDeptNurseName() {
        return deptNurseName;
    }

    protected void setDeptNurseName(String deptNurseName) {
        this.deptNurseName = deptNurseName;
    }

    public LocalDateTime getEffectiveLowDate() {
        return effectiveLowDate;
    }

    protected void setEffectiveLowDate(LocalDateTime effectiveLowDate) {
        this.effectiveLowDate = effectiveLowDate;
    }

    public Boolean getRepairFlag() {
        return repairFlag;
    }

    protected void setRepairFlag(Boolean repairFlag) {
        this.repairFlag = repairFlag;
    }

    public String getParentCode() {
        return parentCode;
    }

    protected void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getCriticalId() {
        return criticalId;
    }

    protected void setCriticalId(String criticalId) {
        this.criticalId = criticalId;
    }

    public Boolean getPrescriptionFlag() {
        return prescriptionFlag;
    }

    protected void setPrescriptionFlag(Boolean prescriptionFlag) {
        this.prescriptionFlag = prescriptionFlag;
    }

    public String getPassValue() {
        return passValue;
    }

    protected void setPassValue(String passValue) {
        this.passValue = passValue;
    }

    public String getThirdFlag() {
        return thirdFlag;
    }

    protected void setThirdFlag(String thirdFlag) {
        this.thirdFlag = thirdFlag;
    }

    public String getReceiveOrgCode() {
        return receiveOrgCode;
    }

    protected void setReceiveOrgCode(String receiveOrgCode) {
        this.receiveOrgCode = receiveOrgCode;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    protected void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public String getUsage() {
        return usage;
    }

    protected void setUsage(String usage) {
        this.usage = usage;
    }

    public String getUsageName() {
        return usageName;
    }

    protected void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    protected void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }

    public Boolean getSkinFlag() {
        return skinFlag;
    }

    protected void setSkinFlag(Boolean skinFlag) {
        this.skinFlag = skinFlag;
    }

    public SkinTypeEnum getSkinType() {
        return skinType;
    }

    protected void setSkinType(SkinTypeEnum skinType) {
        this.skinType = skinType;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getCommitDate() {
        return commitDate;
    }

    protected void setCommitDate(LocalDateTime commitDate) {
        this.commitDate = commitDate;
    }

    public String getSubmitStaffId() {
        return submitStaffId;
    }

    protected void setSubmitStaffId(String submitStaffId) {
        this.submitStaffId = submitStaffId;
    }

    public String getSubmitStaffName() {
        return submitStaffName;
    }

    protected void setSubmitStaffName(String submitStaffName) {
        this.submitStaffName = submitStaffName;
    }

    public String getProofStaff() {
        return proofStaff;
    }

    protected void setProofStaff(String proofStaff) {
        this.proofStaff = proofStaff;
    }

    public String getProofStaffName() {
        return proofStaffName;
    }

    protected void setProofStaffName(String proofStaffName) {
        this.proofStaffName = proofStaffName;
    }

    public Boolean getLimitConformFlag() {
        return limitConformFlag;
    }

    protected void setLimitConformFlag(Boolean limitConformFlag) {
        this.limitConformFlag = limitConformFlag;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    protected void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    protected void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getCancelStaff() {
        return cancelStaff;
    }

    protected void setCancelStaff(String cancelStaff) {
        this.cancelStaff = cancelStaff;
    }

    public String getCancelStaffName() {
        return cancelStaffName;
    }

    protected void setCancelStaffName(String cancelStaffName) {
        this.cancelStaffName = cancelStaffName;
    }

    public LocalDateTime getCancelDate() {
        return cancelDate;
    }

    protected void setCancelDate(LocalDateTime cancelDate) {
        this.cancelDate = cancelDate;
    }

    public String getCancelRemark() {
        return cancelRemark;
    }

    protected void setCancelRemark(String cancelRemark) {
        this.cancelRemark = cancelRemark;
    }

    public String getReMark() {
        return reMark;
    }

    protected void setReMark(String reMark) {
        this.reMark = reMark;
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    protected void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisCdrOrder other = (CisCdrOrder) obj;
        return Objects.equals(id, other.id);
    }

    public CisCdrOrder create(CisCdrOrderNto cisCdrOrderNto) {
        Assert.notNull(cisCdrOrderNto, "参数cisCdrOrderNto不能为空！");

        setId(cisCdrOrderNto.getId());
//        setOrderType(cisCdrOrderNto.getOrderType());
        setSortNo(cisCdrOrderNto.getSortNo());
        setPatMiCode(cisCdrOrderNto.getPatMiCode());
        setVisitCode(cisCdrOrderNto.getVisitCode());
        setOrderServiceCode(cisCdrOrderNto.getOrderServiceCode());
        setOrderClass(cisCdrOrderNto.getOrderClass());
        setApplyCode(cisCdrOrderNto.getApplyCode());
        setTreatmentCourse(cisCdrOrderNto.getTreatmentCourse());
        setTreatmentCourseUnit(cisCdrOrderNto.getTreatmentCourseUnit());
        setBabyFlag(cisCdrOrderNto.getBabyFlag());
        setExecuteOrgCode(cisCdrOrderNto.getExecuteOrgCode());
        setExecuteOrgName(cisCdrOrderNto.getExecuteOrgName());
        setDeptNurseCode(cisCdrOrderNto.getDeptNurseCode());
        setDeptNurseName(cisCdrOrderNto.getDeptNurseName());
        setEffectiveLowDate(cisCdrOrderNto.getEffectiveLowDate());
        setRepairFlag(cisCdrOrderNto.getRepairFlag());
        setParentCode(cisCdrOrderNto.getParentCode());
        setCriticalId(cisCdrOrderNto.getCriticalId());
        setPrescriptionFlag(cisCdrOrderNto.getPrescriptionFlag());
        setPassValue(cisCdrOrderNto.getPassValue());
        setThirdFlag(cisCdrOrderNto.getThirdFlag());
        setReceiveOrgCode(cisCdrOrderNto.getReceiveOrgCode());
        setReceiveOrgName(cisCdrOrderNto.getReceiveOrgName());
        setUsage(cisCdrOrderNto.getUsage());
        setUsageName(cisCdrOrderNto.getUsageName());
        setSbadmWay(cisCdrOrderNto.getSbadmWay());
        setSkinFlag(cisCdrOrderNto.getSkinFlag());
        setSkinType(cisCdrOrderNto.getSkinType());
        setCreatedStaff(cisCdrOrderNto.getCreatedStaff());
        setCreatedStaffName(cisCdrOrderNto.getCreatedStaffName());
        setCreatedDate(cisCdrOrderNto.getCreatedDate());
        setCommitDate(cisCdrOrderNto.getCommitDate());
        setSubmitStaffId(cisCdrOrderNto.getSubmitStaffId());
        setSubmitStaffName(cisCdrOrderNto.getSubmitStaffName());
        setProofStaff(cisCdrOrderNto.getProofStaff());
        setProofStaffName(cisCdrOrderNto.getProofStaffName());
        setLimitConformFlag(cisCdrOrderNto.getLimitConformFlag());
        setHospitalCode(cisCdrOrderNto.getHospitalCode());
        setHospitalName(cisCdrOrderNto.getHospitalName());
        setCancelStaff(cisCdrOrderNto.getCancelStaff());
        setCancelStaffName(cisCdrOrderNto.getCancelStaffName());
        setCancelDate(cisCdrOrderNto.getCancelDate());
        setCancelRemark(cisCdrOrderNto.getCancelRemark());
        setReMark(cisCdrOrderNto.getReMark());
        setStatusCode(cisCdrOrderNto.getStatusCode());
        return this;
    }

    public void update(CisCdrOrderEto cisCdrOrderEto) {
//        setOrderType(cisCdrOrderEto.getOrderType());
        setEffectiveLowDate(cisCdrOrderEto.getEffectiveLowDate());
        setCancelDate(cisCdrOrderEto.getCancelDate());
        setStatusCode(cisCdrOrderEto.getStatusCode());
    }

    public void delete() {
    }

}
