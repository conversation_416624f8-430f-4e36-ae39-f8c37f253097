package com.bjgoodwill.hip.ds.cis.cdr.allergy.repository;

import com.bjgoodwill.hip.ds.cis.cdr.allergy.entity.CisPatDrugAllergy;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("cis_nurse.allergy.repository.CisPatDrugAllergyRepository")
public interface CisPatDrugAllergyRepository extends JpaRepository<CisPatDrugAllergy, String>, JpaSpecificationExecutor<CisPatDrugAllergy> {

}