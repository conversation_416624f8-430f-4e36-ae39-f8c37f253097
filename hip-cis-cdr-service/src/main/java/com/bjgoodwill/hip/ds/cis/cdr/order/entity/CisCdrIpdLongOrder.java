package com.bjgoodwill.hip.ds.cis.cdr.order.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cdr.order.repository.CisCdrIpdLongOrderRepository;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "住院长期医嘱")
@DiscriminatorValue("2")
public class CisCdrIpdLongOrder extends CisCdrOrder {

    @Comment("医嘱截止时间")
    @Column(name = "effective_high_date", nullable = true)
    private LocalDateTime effectiveHighDate;


    @Comment("停止人")
    @Column(name = "stop_staff", nullable = true)
    private String stopStaff;


    @Comment("停止人姓名")
    @Column(name = "stop_staff_name", nullable = true)
    private String stopStaffName;


    @Comment("停止时间")
    @Column(name = "stop_date", nullable = true)
    private LocalDateTime stopDate;


    @Comment("停止校对人")
    @Column(name = "stop_proof_staff", nullable = true)
    private String stopProofStaff;


    @Comment("停止校对人姓名")
    @Column(name = "stop_proof_staff_name", nullable = true)
    private String stopProofStaffName;


    @Comment("停止校对时间")
    @Column(name = "stop_proof_date", nullable = true)
    private LocalDateTime stopProofDate;


    @Comment("最后拆分日期")
    @Column(name = "last_split_date", nullable = true)
    private LocalDateTime lastSplitDate;


    @Comment("首日次数")
    @Column(name = "first_day_timepoint", nullable = true)
    private String firstDayTimepoint;

    public static Optional<CisCdrIpdLongOrder> getCisCdrIpdLongOrderById(String id) {
        return dao().findById(id);
    }

    public static List<CisCdrIpdLongOrder> getCisCdrIpdLongOrders(CisCdrIpdLongOrderQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisCdrIpdLongOrder> getCisCdrIpdLongOrderPage(CisCdrIpdLongOrderQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisCdrIpdLongOrder> getSpecification(CisCdrIpdLongOrderQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getOrderType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderType"), qto.getOrderType()));
            }
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (qto.getOrderClass() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderClass"), qto.getOrderClass()));
            }
            if (StringUtils.isNotBlank(qto.getOrderType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderType"), qto.getOrderType()));
            }
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (qto.getOrderClass() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderClass"), qto.getOrderClass()));
            }

            if (qto.getStartDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("commitDate"), LocalDateUtil.beginOfDay(qto.getStartDate())));
            }
            if (qto.getEndDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("commitDate"), LocalDateUtil.beginOfDay(qto.getEndDate())));
            }
            return predicate;
        };
    }

    private static CisCdrIpdLongOrderRepository dao() {
        return SpringUtil.getBean(CisCdrIpdLongOrderRepository.class);
    }

    public LocalDateTime getEffectiveHighDate() {
        return effectiveHighDate;
    }

    protected void setEffectiveHighDate(LocalDateTime effectiveHighDate) {
        this.effectiveHighDate = effectiveHighDate;
    }

    public String getStopStaff() {
        return stopStaff;
    }

    protected void setStopStaff(String stopStaff) {
        this.stopStaff = stopStaff;
    }

    public String getStopStaffName() {
        return stopStaffName;
    }

    protected void setStopStaffName(String stopStaffName) {
        this.stopStaffName = stopStaffName;
    }

    public LocalDateTime getStopDate() {
        return stopDate;
    }

    protected void setStopDate(LocalDateTime stopDate) {
        this.stopDate = stopDate;
    }

    public String getStopProofStaff() {
        return stopProofStaff;
    }

    protected void setStopProofStaff(String stopProofStaff) {
        this.stopProofStaff = stopProofStaff;
    }

    public String getStopProofStaffName() {
        return stopProofStaffName;
    }

    protected void setStopProofStaffName(String stopProofStaffName) {
        this.stopProofStaffName = stopProofStaffName;
    }

    public LocalDateTime getStopProofDate() {
        return stopProofDate;
    }

    protected void setStopProofDate(LocalDateTime stopProofDate) {
        this.stopProofDate = stopProofDate;
    }

    public LocalDateTime getLastSplitDate() {
        return lastSplitDate;
    }

    protected void setLastSplitDate(LocalDateTime lastSplitDate) {
        this.lastSplitDate = lastSplitDate;
    }

    public String getFirstDayTimepoint() {
        return firstDayTimepoint;
    }

    protected void setFirstDayTimepoint(String firstDayTimepoint) {
        this.firstDayTimepoint = firstDayTimepoint;
    }

    @Override
    public CisCdrOrder create(CisCdrOrderNto cisCdrOrderNto) {
        return create((CisCdrIpdLongOrderNto) cisCdrOrderNto);
    }

    @Override
    public void update(CisCdrOrderEto cisCdrOrderEto) {
        update((CisCdrIpdLongOrderEto) cisCdrOrderEto);
    }

    public CisCdrIpdLongOrder create(CisCdrIpdLongOrderNto cisCdrIpdLongOrderNto) {
        Assert.notNull(cisCdrIpdLongOrderNto, "参数cisCdrIpdLongOrderNto不能为空！");
        super.create(cisCdrIpdLongOrderNto);

        setEffectiveHighDate(cisCdrIpdLongOrderNto.getEffectiveHighDate());
        setStopStaff(cisCdrIpdLongOrderNto.getStopStaff());
        setStopStaffName(cisCdrIpdLongOrderNto.getStopStaffName());
        setStopDate(cisCdrIpdLongOrderNto.getStopDate());
        setStopProofStaff(cisCdrIpdLongOrderNto.getStopProofStaff());
        setStopProofStaffName(cisCdrIpdLongOrderNto.getStopProofStaffName());
        setStopProofDate(cisCdrIpdLongOrderNto.getStopProofDate());
        setLastSplitDate(cisCdrIpdLongOrderNto.getLastSplitDate());
        setFirstDayTimepoint(cisCdrIpdLongOrderNto.getFirstDayTimepoint());
        dao().save(this);
        return this;
    }

    public void update(CisCdrIpdLongOrderEto cisCdrIpdLongOrderEto) {
        super.update(cisCdrIpdLongOrderEto);
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
