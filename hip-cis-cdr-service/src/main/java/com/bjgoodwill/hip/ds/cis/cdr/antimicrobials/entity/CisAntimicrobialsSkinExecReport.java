package com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.repository.CisAntimicrobialsSkinExecReportRepository;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.to.CisAntimicrobialsSkinExecReportNto;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.to.CisAntimicrobialsSkinExecReportQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "抗菌药皮试药执行记录")
@Table(name = "cis_antimicrobials_skin_exec_report", indexes = {@Index(name = "idx_cis_antimicrobials_skin_exec_report_visit_code", columnList = "visit_code")}, uniqueConstraints = {})
public class CisAntimicrobialsSkinExecReport {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("流水号")
    @Column(name = "visit_code", nullable = true)
    private String visitCode;


    @Comment("主索引")
    @Column(name = "pat_mi_code", nullable = true)
    private String patMiCode;


    @Enumerated(EnumType.STRING)
    @Comment("患者类型")
    @Column(name = "visit_type", nullable = true)
    private VisitTypeEnum visitType;


    @Enumerated(EnumType.STRING)
    @Comment("系统类型")
    @Column(name = "system_type", nullable = true)
    private SystemTypeEnum systemType;


    @Comment("服务项目编码")
    @Column(name = "service_item_code", nullable = true)
    private String serviceItemCode;


    @Comment("服务项目名称")
    @Column(name = "service_item_name", nullable = true)
    private String serviceItemName;


    @Comment("医嘱号")
    @Column(name = "order_id", nullable = true)
    private String orderId;


    @Comment("申请单号")
    @Column(name = "apply_id", nullable = true)
    private String applyId;


    @Comment("执行记录id")
    @Column(name = "exec_plan_id", nullable = true)
    private String execPlanId;


    @Comment("执行时间")
    @Column(name = "exec_plan_date", nullable = true)
    private LocalDateTime execPlanDate;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    private boolean deleted;

    public static Optional<CisAntimicrobialsSkinExecReport> getCisAntimicrobialsSkinExecReportById(String id) {
        return dao().findById(id);
    }

    public static List<CisAntimicrobialsSkinExecReport> getCisAntimicrobialsSkinExecReports(CisAntimicrobialsSkinExecReportQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisAntimicrobialsSkinExecReport> getCisAntimicrobialsSkinExecReportPage(CisAntimicrobialsSkinExecReportQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisAntimicrobialsSkinExecReport> getSpecification(CisAntimicrobialsSkinExecReportQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (qto.getSystemType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("systemType"), qto.getSystemType()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemName"), qto.getServiceItemName()));
            }
            if (StringUtils.isNotBlank(qto.getOrderId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderId"), qto.getOrderId()));
            }
            if (StringUtils.isNotBlank(qto.getApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("applyId"), qto.getApplyId()));
            }
            if (StringUtils.isNotBlank(qto.getExecPlanId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("execPlanId"), qto.getExecPlanId()));
            }
            if (qto.getExecPlanDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("execPlanDate"), LocalDateUtil.beginOfDay(qto.getExecPlanDate()), LocalDateUtil.endOfDay(qto.getExecPlanDate())));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            return predicate;
        };
    }

    private static CisAntimicrobialsSkinExecReportRepository dao() {
        return SpringUtil.getBean(CisAntimicrobialsSkinExecReportRepository.class);
    }

    public static List<CisAntimicrobialsSkinExecReport> findreportsByVisitCode(String visitCode) {
        return dao().findCisAntimicrobialsSkinExecReportsByVisitCode(visitCode);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    protected void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    protected void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    protected void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    protected void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    protected void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getOrderId() {
        return orderId;
    }

    protected void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getApplyId() {
        return applyId;
    }

    protected void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getExecPlanId() {
        return execPlanId;
    }

    protected void setExecPlanId(String execPlanId) {
        this.execPlanId = execPlanId;
    }

    public LocalDateTime getExecPlanDate() {
        return execPlanDate;
    }

    protected void setExecPlanDate(LocalDateTime execPlanDate) {
        this.execPlanDate = execPlanDate;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisAntimicrobialsSkinExecReport other = (CisAntimicrobialsSkinExecReport) obj;
        return Objects.equals(id, other.id);
    }

    public CisAntimicrobialsSkinExecReport create(CisAntimicrobialsSkinExecReportNto cisAntimicrobialsSkinExecReportNto) {
        Assert.notNull(cisAntimicrobialsSkinExecReportNto, "参数cisAntimicrobialsSkinExecReportNto不能为空！");
        Assert.isTrue(!dao().existsByExecPlanId(cisAntimicrobialsSkinExecReportNto.getExecPlanId()), "数据已经存在");

        setId(cisAntimicrobialsSkinExecReportNto.getId());
        setVisitCode(cisAntimicrobialsSkinExecReportNto.getVisitCode());
        setPatMiCode(cisAntimicrobialsSkinExecReportNto.getPatMiCode());
        setVisitType(cisAntimicrobialsSkinExecReportNto.getVisitType());
        setSystemType(cisAntimicrobialsSkinExecReportNto.getSystemType());
        setServiceItemCode(cisAntimicrobialsSkinExecReportNto.getServiceItemCode());
        setServiceItemName(cisAntimicrobialsSkinExecReportNto.getServiceItemName());
        setOrderId(cisAntimicrobialsSkinExecReportNto.getOrderId());
        setApplyId(cisAntimicrobialsSkinExecReportNto.getApplyId());
        setExecPlanId(cisAntimicrobialsSkinExecReportNto.getExecPlanId());
        setExecPlanDate(cisAntimicrobialsSkinExecReportNto.getExecPlanDate());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(StringUtils.isEmpty(cisAntimicrobialsSkinExecReportNto.getCreatedStaff()) ? HIPLoginUtil.getStaffId() : cisAntimicrobialsSkinExecReportNto.getCreatedStaff());
        setCreatedStaffName(StringUtils.isEmpty(cisAntimicrobialsSkinExecReportNto.getCreatedStaff()) ? HIPLoginUtil.getLoginName() : cisAntimicrobialsSkinExecReportNto.getCreatedStaffName());
        setDeleted(false);
        dao().save(this);
        return this;
    }

    public void delete() {
        setDeleted(true);
    }
}
