package com.bjgoodwill.hip.ds.cis.cdr.order.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cdr.order.repository.CisCdrOrderContentRepository;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderContentEto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderContentNto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderContentQto;
import com.bjgoodwill.hip.jpa.core.SnowflakeId;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "医嘱内容")
@Table(name = "cis_cdr_order_content", indexes = {@Index(name = "cis_cdr_order_content_order_id",
        columnList = "order_id")}, uniqueConstraints = {})
public class CisCdrOrderContent {

    @Id
    @SnowflakeId
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("医嘱id")
    @Column(name = "order_id", nullable = false, unique = true)
    private String orderId;


    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Comment("医嘱内容")
    @Column(name = "order_content", nullable = true)
    private byte[] orderContent;

    public static Optional<CisCdrOrderContent> getCisCdrOrderContentById(String id) {
        return dao().findById(id);
    }

    public static List<CisCdrOrderContent> getCisCdrOrderContents(CisCdrOrderContentQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisCdrOrderContent> getCisCdrOrderContentPage(CisCdrOrderContentQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisCdrOrderContent> getSpecification(CisCdrOrderContentQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getOrderId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderId"), qto.getOrderId()));
            }
            return predicate;
        };
    }

    private static CisCdrOrderContentRepository dao() {
        return SpringUtil.getBean(CisCdrOrderContentRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    protected void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public byte[] getOrderContent() {
        return orderContent;
    }

    protected void setOrderContent(byte[] orderContent) {
        this.orderContent = orderContent;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisCdrOrderContent other = (CisCdrOrderContent) obj;
        return Objects.equals(id, other.id);
    }

    public CisCdrOrderContent create(CisCdrOrderContentNto cisCdrOrderContentNto) {
        Assert.notNull(cisCdrOrderContentNto, "参数cisCdrOrderContentNto不能为空！");
        BusinessAssert.isTrue(!dao().existsByOrderId(cisCdrOrderContentNto.getOrderId()), "医嘱id[%s]已经存在！", cisCdrOrderContentNto.getOrderId());

        setOrderId(cisCdrOrderContentNto.getOrderId());
        setOrderContent(cisCdrOrderContentNto.getOrderContent());
        dao().save(this);
        return this;
    }

    public void update(CisCdrOrderContentEto cisCdrOrderContentEto) {
        setOrderContent(cisCdrOrderContentEto.getOrderContent());
    }

    public void delete() {
        dao().delete(this);
    }

}
