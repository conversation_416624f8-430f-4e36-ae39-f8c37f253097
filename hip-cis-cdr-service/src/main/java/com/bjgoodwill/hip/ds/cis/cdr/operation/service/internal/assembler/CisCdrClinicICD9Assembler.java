package com.bjgoodwill.hip.ds.cis.cdr.operation.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.operation.entity.CisCdrClinicICD9;
import com.bjgoodwill.hip.ds.cis.cdr.operation.to.CisCdrClinicICD9To;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisCdrClinicICD9Assembler {

    public static List<CisCdrClinicICD9To> toTos(List<CisCdrClinicICD9> cisCdrClinicICD9s) {
        return toTos(cisCdrClinicICD9s, false);
    }

    public static List<CisCdrClinicICD9To> toTos(List<CisCdrClinicICD9> cisCdrClinicICD9s, boolean withAllParts) {
        Assert.notNull(cisCdrClinicICD9s, "参数cisCdrClinicICD9s不能为空！");

        List<CisCdrClinicICD9To> tos = new ArrayList<>();
        for (CisCdrClinicICD9 cisCdrClinicICD9 : cisCdrClinicICD9s)
            tos.add(toTo(cisCdrClinicICD9, withAllParts));
        return tos;
    }

    public static CisCdrClinicICD9To toTo(CisCdrClinicICD9 cisCdrClinicICD9) {
        return toTo(cisCdrClinicICD9, false);
    }

    /**
     * @generated
     */
    public static CisCdrClinicICD9To toTo(CisCdrClinicICD9 cisCdrClinicICD9, boolean withAllParts) {
        if (cisCdrClinicICD9 == null)
            return null;
        CisCdrClinicICD9To to = new CisCdrClinicICD9To();
        to.setId(cisCdrClinicICD9.getId());
        to.setVisitCode(cisCdrClinicICD9.getVisitCode());
        to.setPatMiCode(cisCdrClinicICD9.getPatMiCode());
        to.setOrderId(cisCdrClinicICD9.getOrderId());
        to.setApplyId(cisCdrClinicICD9.getApplyId());
        to.setIcd9Code(cisCdrClinicICD9.getIcd9Code());
        to.setIcd9Name(cisCdrClinicICD9.getIcd9Name());
        to.setServiceItemName(cisCdrClinicICD9.getServiceItemName());
        to.setOperationStartDate(cisCdrClinicICD9.getOperationStartDate());
        to.setOperationEndDate(cisCdrClinicICD9.getOperationEndDate());
        to.setCreatedDate(cisCdrClinicICD9.getCreatedDate());

        if (withAllParts) {
        }
        return to;
    }

}