package com.bjgoodwill.hip.ds.cis.cdr.orderlog.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.orderlog.entity.CisIpdOrderLcLog;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.entity.CisOpdOrderLcLog;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.entity.CisOrderClLog;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisOrderClLogTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisOrderClLogAssembler {

    public static List<CisOrderClLogTo> toTos(List<CisOrderClLog> cisOrderClLogs) {
        return toTos(cisOrderClLogs, false);
    }

    public static List<CisOrderClLogTo> toTos(List<CisOrderClLog> cisOrderClLogs, boolean withAllParts) {
        Assert.notNull(cisOrderClLogs, "参数cisOrderClLogs不能为空！");

        List<CisOrderClLogTo> tos = new ArrayList<>();
        for (CisOrderClLog cisOrderClLog : cisOrderClLogs)
            tos.add(toTo(cisOrderClLog, withAllParts));
        return tos;
    }

    public static CisOrderClLogTo toTo(CisOrderClLog cisOrderClLog) {
        return toTo(cisOrderClLog, false);
    }

    /**
     * @generated
     */
    public static CisOrderClLogTo toTo(CisOrderClLog cisOrderClLog, boolean withAllParts) {
        if (cisOrderClLog == null)
            return null;
        if (cisOrderClLog instanceof CisIpdOrderLcLog) {
            return CisIpdOrderLcLogAssembler.toTo((CisIpdOrderLcLog) cisOrderClLog, withAllParts);
        }
        if (cisOrderClLog instanceof CisOpdOrderLcLog) {
            return CisOpdOrderLcLogAssembler.toTo((CisOpdOrderLcLog) cisOrderClLog, withAllParts);
        }
        return null;
    }

}