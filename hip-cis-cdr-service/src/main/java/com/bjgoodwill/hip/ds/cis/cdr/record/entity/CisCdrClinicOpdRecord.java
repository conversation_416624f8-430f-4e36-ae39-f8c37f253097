package com.bjgoodwill.hip.ds.cis.cdr.record.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.ds.cis.cdr.record.repository.CisCdrClinicOpdRecordRepository;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicOpdRecordEto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicOpdRecordNto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicOpdRecordQto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicRecordNto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "门诊就诊记录")
@DiscriminatorValue("OPD")
public class CisCdrClinicOpdRecord extends CisCdrClinicRecord {

    @Comment("急诊标记")
    @Column(name = "emr_flag", nullable = true)
    private boolean emrFlag;

    public static Optional<CisCdrClinicOpdRecord> getCisCdrClinicOpdRecordById(String id) {
        return dao().findById(id);
    }

    public static List<CisCdrClinicOpdRecord> getCisCdrClinicOpdRecords(CisCdrClinicOpdRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisCdrClinicOpdRecord> getCisCdrClinicOpdRecordPage(CisCdrClinicOpdRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisCdrClinicOpdRecord> getSpecification(CisCdrClinicOpdRecordQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();

            if (StringUtils.isNotBlank(qto.getPatCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patCode"), qto.getPatCode()));
            }

            if (qto.getStartDate() != null && qto.getEndDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("clinicStaff"), qto.getStartDate(), qto.getEndDate()));
            }

            if (qto.getEmrFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("emrFlag"), qto.getEmrFlag()));
            }
            return predicate;
        };
    }

    private static CisCdrClinicOpdRecordRepository dao() {
        return SpringUtil.getBean(CisCdrClinicOpdRecordRepository.class);
    }

    public boolean isEmrFlag() {
        return emrFlag;
    }

    protected void setEmrFlag(boolean emrFlag) {
        this.emrFlag = emrFlag;
    }

    @Override
    public CisCdrClinicRecord create(CisCdrClinicRecordNto cisCdrClinicRecordNto) {
        return create((CisCdrClinicOpdRecordNto) cisCdrClinicRecordNto);
    }

    public CisCdrClinicOpdRecord create(CisCdrClinicOpdRecordNto cisCdrClinicOpdRecordNto) {
        Assert.notNull(cisCdrClinicOpdRecordNto, "参数cisCdrClinicOpdRecordNto不能为空！");
        super.create(cisCdrClinicOpdRecordNto);

        setEmrFlag(cisCdrClinicOpdRecordNto.isEmrFlag());
        dao().save(this);
        return this;
    }

    public void update(CisCdrClinicOpdRecordEto cisCdrClinicOpdRecordEto) {
        super.update(cisCdrClinicOpdRecordEto);
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
