package com.bjgoodwill.hip.ds.cis.cdr.report.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cdr.report.entity.CisReportResult;
import com.bjgoodwill.hip.ds.cis.cdr.report.entity.CisReportResultDetail;
import com.bjgoodwill.hip.ds.cis.cdr.report.entity.CisReportResultDgimgDetail;
import com.bjgoodwill.hip.ds.cis.cdr.report.entity.CisReportResultSpcobsDetail;
import com.bjgoodwill.hip.ds.cis.cdr.report.service.CisReportResultService;
import com.bjgoodwill.hip.ds.cis.cdr.report.service.internal.assembler.CisReportResultAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.report.service.internal.assembler.CisReportResultDetailAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.report.service.internal.assembler.CisReportResultDgimgDetailAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.report.service.internal.assembler.CisReportResultSpcobsDetailAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.report.to.*;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.report.service.CisReportResultService")
@RequestMapping(value = "/api/cdr/report/cisReportResult", produces = "application/json; charset=utf-8")
public class CisReportResultServiceImpl implements CisReportResultService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisReportResultTo> getCisReportResults(CisReportResultQto cisReportResultQto) {
        return CisReportResultAssembler.toTos(CisReportResult.getCisReportResults(cisReportResultQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisReportResultTo> getCisReportResultPage(CisReportResultQto cisReportResultQto) {
        Page<CisReportResult> page = CisReportResult.getCisReportResultPage(cisReportResultQto);
        Page<CisReportResultTo> result = page.map(CisReportResultAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisReportResultTo createCisReportResult(CisReportResultNto cisReportResultNto) {
        CisReportResult cisReportResult = new CisReportResult();
        cisReportResult = cisReportResult.create(cisReportResultNto);
        return CisReportResultAssembler.toTo(cisReportResult);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisReportResult(String id, CisReportResultEto cisReportResultEto) {
        Optional<CisReportResult> cisReportResultOptional = CisReportResult.getCisReportResultById(id);
        cisReportResultOptional.ifPresent(cisReportResult -> cisReportResult.update(cisReportResultEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisReportResult(String id) {
        Optional<CisReportResult> cisReportResultOptional = CisReportResult.getCisReportResultById(id);
        cisReportResultOptional.ifPresent(cisReportResult -> cisReportResult.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisReportResultDetailTo getCisReportResultDetailById(String id) {
        return CisReportResultDetailAssembler.toTo(CisReportResultDetail.getCisReportResultDetailById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisReportResultDetailTo createCisReportResultDetail(String cisReportResultId, CisReportResultDetailNto cisReportResultDetailNto) {

        CisReportResultDetail cisReportResultDetail = cisReportResultDetailNto instanceof CisReportResultSpcobsDetailNto ?
                new CisReportResultSpcobsDetail() :
                new CisReportResultDgimgDetail();

        cisReportResultDetail = cisReportResultDetail.create(cisReportResultId, cisReportResultDetailNto);
        return CisReportResultDetailAssembler.toTo(cisReportResultDetail);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisReportResultDetail(String id, CisReportResultDetailEto cisReportResultDetailEto) {
        Optional<CisReportResultDetail> cisReportResultDetailOptional = CisReportResultDetail.getCisReportResultDetailById(id);
        cisReportResultDetailOptional.ifPresent(cisReportResultDetail -> cisReportResultDetail.update(cisReportResultDetailEto));
    }

    /**
     * 根据申请ID获取CIS报告结果
     * 该方法被@Transactional注解标记，意味着它在一个事务中执行，任何抛出的异常都会导致事务回滚
     * 由于该方法只读取数据而不修改数据库，因此设置了readOnly = true以优化性能
     *
     * @param applyId 申请ID，用于查询CIS报告结果
     * @return 返回转换后的CisReportResultTo对象，包含CIS报告结果
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisReportResultTo getCisReportResultByApplyId(String applyId) {
        // 使用CisReportResultAssembler将查询到的CIS报告结果转换为传输对象(Transfer Object)
        return CisReportResultAssembler
                .toTo(CisReportResult.getCisReportResultByApplyId(applyId).get(), true);
    }

    /**
     * 根据就诊码获取CIS报告结果详情
     * <p>
     * 该方法使用了事务管理，确保在查询过程中，数据库的一致性和隔离性
     * 事务设置为只读，因为该操作仅查询数据，不进行修改
     * 在出现异常时，事务将回滚，以保持数据的一致性
     *
     * @param visitCode 就诊码，用于标识特定的就诊记录
     * @param itemCode  项目码，用于标识特定的检查或报告项目
     * @param startDate 开始日期时间，用于限定查询的时间范围
     * @param endDate   结束日期时间，用于限定查询的时间范围
     * @return 返回一个包含CisReportResultDetailTo对象的列表，代表查询到的报告结果详情
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisReportResultSpcobsDetailTo> getCisReportResultDetialByVisitCode(String visitCode, String itemCode, LocalDateTime startDate, LocalDateTime endDate) {
        // 校验项目码非空，确保参数itemCode有值
        BusinessAssert.hasText(itemCode, "参数itemCode");
        // 调用CisReportResultDetailAssembler的静态方法toTos，将查询结果转换为CisReportResultDetailTo对象列表
        // 并返回查询到的Cis报告结果详情列表
        return getSpcobsDetailToWithDate(CisReportResultSpcobsDetail.findCisReportResultDetailWithVisitCode(itemCode, visitCode), startDate, endDate);

    }

    /**
     * 根据患者就诊代码、项目代码和日期范围获取CIS报告结果详情
     *
     * @param patMiCode 患者就诊代码，用于标识特定的患者就诊记录
     * @param itemCode  项目代码，用于标识特定的检查或检验项目
     * @param startDate 起始日期，用于限定查询的时间范围
     * @param endDate   结束日期，用于限定查询的时间范围
     * @return 返回符合条件的Cis报告结果详情列表
     * <p>
     * 此方法使用了@Transactional注解，指明该方法在执行时需要进行事务管理
     * rollbackFor = Throwable.class 表示任何异常都会导致事务回滚
     * readOnly = true 表示该方法只读取数据，不修改数据库状态，这有助于提高性能
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisReportResultSpcobsDetailTo> getCisReportResultDetialByPatMiCode(String patMiCode, String itemCode, LocalDateTime startDate, LocalDateTime endDate) {
        // 校验itemCode参数是否有值，确保后续查询可以正常执行
        BusinessAssert.hasText(itemCode, "参数itemCode");
        // 调用CisReportResultDetailAssembler的静态方法toTos，将查询结果转换为CisReportResultDetailTo对象列表
        // 并返回查询到的Cis报告结果详情列表
        return getSpcobsDetailToWithDate(CisReportResultSpcobsDetail.findCisReportResultDetailWithPatMiCode(itemCode, patMiCode), startDate, endDate);
    }

    /**
     * 根据就诊码获取LIS报告结果详情
     *
     * @param visitCode 就诊码，用于标识特定的就诊记录
     * @return 返回一个包含CisReportResultDetailTo对象的列表，代表查询到的报告结果详情
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisReportResultSpcobsDetailTo> getSpcobsDetailByVisitCode(String visitCode) {
        return CisReportResultSpcobsDetailAssembler.toTos(CisReportResultSpcobsDetail.getSpcobsDetailByVisitCode(visitCode));
    }

    /**
     * 根据就诊码获取PACS报告结果详情
     *
     * @param visitCode 就诊码，用于标识特定的就诊记录
     * @return 返回一个包含CisReportResultDetailTo对象的列表，代表查询到的报告结果详情
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisReportResultTo> getDgimgResultsByVisitCode(String visitCode) {
        CisReportResultQto cisReportResultQto = new CisReportResultQto();
        cisReportResultQto.setVisitCode(visitCode);
        List<CisReportResultTo> cisReportResults = CisReportResultAssembler.toTos(CisReportResult.getCisReportResults(cisReportResultQto));

        List<CisReportResultDgimgDetailTo> dgimgDetails = CisReportResultDgimgDetailAssembler.toTos(CisReportResultDgimgDetail.getCisReportResultDgimgDetailByVisitCode(visitCode));
        Map<String, List<CisReportResultDetailTo>> dgimgDetailMap = dgimgDetails.stream()
                .collect(Collectors.groupingBy(CisReportResultDetailTo::getCisReportResultId));

        cisReportResults.forEach(p -> p.setCisReportResultDetails(dgimgDetailMap.get(p.getId())));

        return cisReportResults;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisReportResultSpcobsDetailTo> getSpcobsDetailByResultId(String resultId) {
        // 根据CisReportResultId获取CisReportResultSpcobsDetail对象列表
        return CisReportResultSpcobsDetailAssembler.toTos(
                CisReportResultSpcobsDetail.getByCisReportResultId(resultId).stream()
                        // 过滤出CisReportResultSpcobsDetail实例
                        .filter(CisReportResultSpcobsDetail.class::isInstance)
                        // 将对象转换为CisReportResultSpcobsDetail类型
                        .map(CisReportResultSpcobsDetail.class::cast)
                        // 将流转换为List
                        .toList()
        );
    }


    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisReportResultDgimgDetailTo> getDgimgDetailByResultId(String resultId) {
        // 根据CisReportResultId获取CisReportResultDgimgDetail对象列表
        // 使用stream进行过滤和类型转换，并最终转换为List
        return CisReportResultDgimgDetailAssembler.toTos(
                CisReportResultDgimgDetail.getByCisReportResultId(resultId).stream()
                        // 过滤出CisReportResultDgimgDetail实例
                        .filter(CisReportResultDgimgDetail.class::isInstance)
                        // 将对象转换为CisReportResultDgimgDetail类型
                        .map(CisReportResultDgimgDetail.class::cast)
                        // 将流转换为List
                        .toList()
        );
    }


    /**
     * 根据指定日期范围过滤并转换Spcobs详细信息列表
     * 此方法用于从给定的Spcobs详细信息列表中，筛选出创建日期在指定开始日期和结束日期范围内的项
     * 并将这些项转换为CisReportResultSpcobsDetailTo对象列表
     *
     * @param item      SisReportResultSpcobsDetail对象列表，作为筛选和转换的输入
     * @param startDate 起始日期时间，用于筛选创建日期在此日期之后（不包括该日期）的项
     * @param endDate   结束日期时间，用于筛选创建日期在此日期之前（不包括该日期）的项
     * @return 返回一个CisReportResultSpcobsDetailTo对象列表，这些对象对应于筛选后的Spcobs详细信息
     */
    private List<CisReportResultSpcobsDetailTo> getSpcobsDetailToWithDate(List<CisReportResultSpcobsDetail> item, LocalDateTime startDate, LocalDateTime endDate) {
        // 调用CisReportResultDetailAssembler的静态方法toTos，将查询结果转换为CisReportResultDetailTo对象列表
        // 并返回查询到的Cis报告结果详情列表
        return CisReportResultSpcobsDetailAssembler.toTos(item.stream()
                // 过滤条件：如果startDate为null，则不过滤开始日期；否则，筛选创建日期在startDate之后的项
                .filter(p -> startDate == null || p.getCreatedDate().isAfter(startDate.minusSeconds(1)))
                // 过滤条件：如果endDate为null，则不过滤结束日期；否则，筛选创建日期在endDate之前的项
                .filter(p -> endDate == null || p.getCreatedDate().isBefore(endDate.plusSeconds(1)))
                .toList());
    }


    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}