package com.bjgoodwill.hip.ds.cis.cdr.record.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.record.entity.CisCdrClinicIpdRecord;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicIpdRecordTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisCdrClinicIpdRecordAssembler {

    public static List<CisCdrClinicIpdRecordTo> toTos(List<CisCdrClinicIpdRecord> cisCdrClinicIpdRecords) {
        return toTos(cisCdrClinicIpdRecords, false);
    }

    public static List<CisCdrClinicIpdRecordTo> toTos(List<CisCdrClinicIpdRecord> cisCdrClinicIpdRecords, boolean withAllParts) {
        Assert.notNull(cisCdrClinicIpdRecords, "参数cisCdrClinicIpdRecords不能为空！");

        List<CisCdrClinicIpdRecordTo> tos = new ArrayList<>();
        for (CisCdrClinicIpdRecord cisCdrClinicIpdRecord : cisCdrClinicIpdRecords)
            tos.add(toTo(cisCdrClinicIpdRecord, withAllParts));
        return tos;
    }

    public static CisCdrClinicIpdRecordTo toTo(CisCdrClinicIpdRecord cisCdrClinicIpdRecord) {
        return toTo(cisCdrClinicIpdRecord, false);
    }

    /**
     * @generated
     */
    public static CisCdrClinicIpdRecordTo toTo(CisCdrClinicIpdRecord cisCdrClinicIpdRecord, boolean withAllParts) {
        if (cisCdrClinicIpdRecord == null)
            return null;
        CisCdrClinicIpdRecordTo to = new CisCdrClinicIpdRecordTo();
        to.setId(cisCdrClinicIpdRecord.getId());
        to.setVisitCode(cisCdrClinicIpdRecord.getVisitCode());
        to.setPatCode(cisCdrClinicIpdRecord.getPatCode());
        to.setName(cisCdrClinicIpdRecord.getName());
        to.setSex(cisCdrClinicIpdRecord.getSex());
        to.setBirthDate(cisCdrClinicIpdRecord.getBirthDate());
        to.setCardType(cisCdrClinicIpdRecord.getCardType());
        to.setCardCode(cisCdrClinicIpdRecord.getCardCode());
        to.setNation(cisCdrClinicIpdRecord.getNation());
        to.setNationality(cisCdrClinicIpdRecord.getNationality());
        to.setTel(cisCdrClinicIpdRecord.getTel());
        to.setNativePlace(cisCdrClinicIpdRecord.getNativePlace());
        to.setLiveAddr(cisCdrClinicIpdRecord.getLiveAddr());
        to.setLiveExactAddr(cisCdrClinicIpdRecord.getLiveExactAddr());
        to.setClinicDate(cisCdrClinicIpdRecord.getClinicDate());
        to.setClinicDept(cisCdrClinicIpdRecord.getClinicDept());
        to.setClinicStaff(cisCdrClinicIpdRecord.getClinicStaff());
        to.setClinicStaffName(cisCdrClinicIpdRecord.getClinicStaffName());
        to.setCreateDate(cisCdrClinicIpdRecord.getCreateDate());
        to.setClinicNurse(cisCdrClinicIpdRecord.getClinicNurse());
        to.setOutDate(cisCdrClinicIpdRecord.getOutDate());

        if (withAllParts) {
        }
        return to;
    }

}