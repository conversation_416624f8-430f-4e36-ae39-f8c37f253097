package com.bjgoodwill.hip.ds.cis.cdr.report.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.ReportResultFlagEnum;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cdr.report.repository.CisReportResultDetailRepository;
import com.bjgoodwill.hip.ds.cis.cdr.report.to.CisReportResultDetailEto;
import com.bjgoodwill.hip.ds.cis.cdr.report.to.CisReportResultDetailNto;
import com.bjgoodwill.hip.ds.cis.cdr.report.to.CisReportResultDetailQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "医嘱报告明细")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "discriminator", discriminatorType = DiscriminatorType.STRING, length = 20)
@Table(name = "cis_report_result_detail", indexes = {@Index(name = "cis_report_result_detail_item_code", columnList = "item_code")}, uniqueConstraints = {})
public abstract class CisReportResultDetail {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("报告结果标识")
    @Column(name = "cis_report_result_id", nullable = false, length = 50)
    private String cisReportResultId;


    @Comment("报告明细编码,存储其他系统报告明细主键，用来取消其他系统报告")
    @Column(name = "report_detail_code", nullable = true)
    private String reportDetailCode;


    @Comment("报告内容")
    @Column(name = "report_content", nullable = true)
    private String reportContent;

    @Comment("报告项目编码")
    @Column(name = "sub_item_code", nullable = true)
    private String subItemCode;


    @Comment("报告项目名称")
    @Column(name = "sub_item_name", nullable = true)
    private String subItemName;

    @Enumerated(EnumType.STRING)
    @Comment("结果值标识")
    @Column(name = "result_flag", nullable = true)
    private ReportResultFlagEnum resultFlag;


    @Comment("危机值结果标识")
    @Column(name = "crisis_result_flag", nullable = true)
    private String crisisResultFlag;


    @Comment("补充说明")
    @Column(name = "remark", nullable = true)
    private String remark;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;

    public static Optional<CisReportResultDetail> getCisReportResultDetailById(String id) {
        return dao().findById(id);
    }

    public static List<CisReportResultDetail> getCisReportResultDetails(String cisReportResultId, CisReportResultDetailQto qto) {
        if (cisReportResultId != null) {
            qto.setCisReportResultId(cisReportResultId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisReportResultDetail> getCisReportResultDetailPage(String cisReportResultId, CisReportResultDetailQto qto) {
        if (cisReportResultId != null) {
            qto.setCisReportResultId(cisReportResultId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisReportResultDetail> getByCisReportResultId(String cisReportResultId) {
        return dao().findByCisReportResultId(cisReportResultId);
    }

    public static void deleteByCisReportResultId(String cisReportResultId) {
        dao().deleteByCisReportResultId(cisReportResultId);
    }

    public static CisReportResultDetail newInstanceByNto(CisReportResultDetailNto cisReportResultDetailNto) {
        try {
            return (CisReportResultDetail) Class.forName("com.bjgoodwill.hip.ds.cis.cdr.report.entity."
                    + StringUtils.removeEnd(cisReportResultDetailNto.getClass().getSimpleName(), "Nto")).getConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @generated
     */
    private static Specification<CisReportResultDetail> getSpecification(CisReportResultDetailQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getCisReportResultId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisReportResultId"), qto.getCisReportResultId()));
            }
            if (StringUtils.isNotBlank(qto.getReportDetailCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportDetailCode"), qto.getReportDetailCode()));
            }
            if (StringUtils.isNotBlank(qto.getSubItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("subItemCode"), qto.getSubItemCode()));
            }
            if (StringUtils.isNotBlank(qto.getSubItemName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("subItemName"), qto.getSubItemName()));
            }
            if (qto.getResultFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("resultFlag"), qto.getResultFlag()));
            }
            if (StringUtils.isNotBlank(qto.getCrisisResultFlag())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("crisisResultFlag"), qto.getCrisisResultFlag()));
            }
            if (StringUtils.isNotBlank(qto.getRemark())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("remark"), qto.getRemark()));
            }
            return predicate;
        };
    }

    private static CisReportResultDetailRepository dao() {
        return SpringUtil.getBean(CisReportResultDetailRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getCisReportResultId() {
        return cisReportResultId;
    }

    protected void setCisReportResultId(String cisReportResultId) {
        this.cisReportResultId = cisReportResultId;
    }

    public String getReportDetailCode() {
        return reportDetailCode;
    }

    protected void setReportDetailCode(String reportDetailCode) {
        this.reportDetailCode = reportDetailCode;
    }

    public String getReportContent() {
        return reportContent;
    }

    public void setReportContent(String reportContent) {
        this.reportContent = reportContent;
    }

    public String getSubItemCode() {
        return subItemCode;
    }

    protected void setSubItemCode(String subItemCode) {
        this.subItemCode = subItemCode;
    }

    public String getSubItemName() {
        return subItemName;
    }

    protected void setSubItemName(String subItemName) {
        this.subItemName = subItemName;
    }

    public ReportResultFlagEnum getResultFlag() {
        return resultFlag;
    }

    public void setResultFlag(ReportResultFlagEnum resultFlag) {
        this.resultFlag = resultFlag;
    }

    public String getCrisisResultFlag() {
        return crisisResultFlag;
    }

    protected void setCrisisResultFlag(String crisisResultFlag) {
        this.crisisResultFlag = crisisResultFlag;
    }

    public String getRemark() {
        return remark;
    }

    protected void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Transient
    public CisReportResult getCisReportResult() {
        return CisReportResult.getCisReportResultById(getCisReportResultId()).orElse(null);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisReportResultDetail other = (CisReportResultDetail) obj;
        return Objects.equals(id, other.id);
    }

    public CisReportResultDetail create(String cisReportResultId, CisReportResultDetailNto cisReportResultDetailNto) {
        Assert.notNull(cisReportResultDetailNto, "参数cisReportResultDetailNto不能为空！");

        setCisReportResultId(cisReportResultId);
        setReportContent(cisReportResultDetailNto.getReportContent());
        setId(cisReportResultDetailNto.getId());
        setReportDetailCode(cisReportResultDetailNto.getReportDetailCode());
        setSubItemCode(cisReportResultDetailNto.getSubItemCode());
        setSubItemName(cisReportResultDetailNto.getSubItemName());
        setResultFlag(cisReportResultDetailNto.getResultFlag());
        setCrisisResultFlag(cisReportResultDetailNto.getCrisisResultFlag());
        setRemark(cisReportResultDetailNto.getRemark());
        setCreatedDate(LocalDateUtil.now());
        return this;
    }

    public void update(CisReportResultDetailEto cisReportResultDetailEto) {
        setSubItemCode(cisReportResultDetailEto.getSubItemCode());
        setSubItemName(cisReportResultDetailEto.getSubItemName());
        setResultFlag(cisReportResultDetailEto.getResultFlag());
        setCrisisResultFlag(cisReportResultDetailEto.getCrisisResultFlag());
        setRemark(cisReportResultDetailEto.getRemark());
    }

    public void delete() {
    }

}
