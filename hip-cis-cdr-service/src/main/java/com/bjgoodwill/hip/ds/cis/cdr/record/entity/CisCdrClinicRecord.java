package com.bjgoodwill.hip.ds.cis.cdr.record.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.ds.cis.cdr.record.repository.CisCdrClinicRecordRepository;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicRecordEto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicRecordNto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicRecordQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "就诊记录")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "visitType", discriminatorType = DiscriminatorType.STRING, length = 20)
@Table(name = "cis_cdr_clinic_record", indexes = {@Index(name = "cis_cdr_clinic_record_visit_code", columnList = "visit_code"),
        @Index(name = "cis_cdr_clinic_record_pat_code", columnList = "pat_code")}, uniqueConstraints = {})
public abstract class CisCdrClinicRecord {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = false, length = 32)
    private String visitCode;


    @Comment("主索引编码")
    @Column(name = "pat_code", nullable = false, length = 32)
    private String patCode;


    @Comment("患者姓名")
    @Column(name = "name", nullable = true, length = 32)
    private String name;


    @Comment("性别")
    @Column(name = "sex", nullable = true, length = 16)
    private String sex;


    @Comment("出生日期")
    @Column(name = "birth_date", nullable = true)
    private LocalDateTime birthDate;


    @Comment("证件类型")
    @Column(name = "card_type", nullable = true, length = 16)
    private String cardType;


    @Comment("证件号")
    @Column(name = "card_code", nullable = true, length = 32)
    private String cardCode;


    @Comment("民族")
    @Column(name = "nation", nullable = true, length = 16)
    private String nation;


    @Comment("国籍")
    @Column(name = "nationality", nullable = true, length = 16)
    private String nationality;


    @Comment("电话")
    @Column(name = "tel", nullable = true, length = 16)
    private String tel;


    @Comment("籍贯")
    @Column(name = "native_place", nullable = true, length = 32)
    private String nativePlace;


    @Comment("现住址")
    @Column(name = "live_addr", nullable = true, length = 255)
    private String liveAddr;


    @Comment("详细现住址")
    @Column(name = "live_exact_addr", nullable = true, length = 255)
    private String liveExactAddr;


    @Comment("就诊时间：取接诊时间/入科时间")
    @Column(name = "clinic_date", nullable = true)
    private LocalDateTime clinicDate;


    @Comment("入院科室/取挂号科室")
    @Column(name = "clinic_dept", nullable = true, length = 64)
    private String clinicDept;


    @Comment("就诊医生/住院医生")
    @Column(name = "clinic_staff", nullable = true, length = 32)
    private String clinicStaff;


    @Comment("医生姓名")
    @Column(name = "clinic_staff_name", nullable = true, length = 32)
    private String clinicStaffName;


    @Comment("抽取数据时间")
    @Column(name = "create_date", nullable = true)
    private LocalDateTime createDate;

    public static Optional<CisCdrClinicRecord> getCisCdrClinicRecordById(String id) {
        return dao().findById(id);
    }

    public static List<CisCdrClinicRecord> getCisCdrClinicRecords(CisCdrClinicRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisCdrClinicRecord> getCisCdrClinicRecordPage(CisCdrClinicRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static CisCdrClinicRecord newInstanceByNto(CisCdrClinicRecordNto cisCdrClinicRecordNto) {
        try {
            return (CisCdrClinicRecord) Class.forName("com.bjgoodwill.hip.ds.cis.cdr.record.entity."
                    + StringUtils.removeEnd(cisCdrClinicRecordNto.getClass().getSimpleName(), "Nto")).getConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @generated
     */
    private static Specification<CisCdrClinicRecord> getSpecification(CisCdrClinicRecordQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();

            if (StringUtils.isNotBlank(qto.getPatCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patCode"), qto.getPatCode()));
            }

            if (qto.getStartDate() != null && qto.getEndDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("clinicStaff"), qto.getStartDate(), qto.getEndDate()));
            }

            return predicate;
        };
    }

    private static CisCdrClinicRecordRepository dao() {
        return SpringUtil.getBean(CisCdrClinicRecordRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatCode() {
        return patCode;
    }

    protected void setPatCode(String patCode) {
        this.patCode = patCode;
    }

    public String getName() {
        return name;
    }

    protected void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    protected void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    protected void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getCardType() {
        return cardType;
    }

    protected void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardCode() {
        return cardCode;
    }

    protected void setCardCode(String cardCode) {
        this.cardCode = cardCode;
    }

    public String getNation() {
        return nation;
    }

    protected void setNation(String nation) {
        this.nation = nation;
    }

    public String getNationality() {
        return nationality;
    }

    protected void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getTel() {
        return tel;
    }

    protected void setTel(String tel) {
        this.tel = tel;
    }

    public String getNativePlace() {
        return nativePlace;
    }

    protected void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }

    public String getLiveAddr() {
        return liveAddr;
    }

    protected void setLiveAddr(String liveAddr) {
        this.liveAddr = liveAddr;
    }

    public String getLiveExactAddr() {
        return liveExactAddr;
    }

    protected void setLiveExactAddr(String liveExactAddr) {
        this.liveExactAddr = liveExactAddr;
    }

    public LocalDateTime getClinicDate() {
        return clinicDate;
    }

    protected void setClinicDate(LocalDateTime clinicDate) {
        this.clinicDate = clinicDate;
    }

    public String getClinicDept() {
        return clinicDept;
    }

    protected void setClinicDept(String clinicDept) {
        this.clinicDept = clinicDept;
    }

    public String getClinicStaff() {
        return clinicStaff;
    }

    protected void setClinicStaff(String clinicStaff) {
        this.clinicStaff = clinicStaff;
    }

    public String getClinicStaffName() {
        return clinicStaffName;
    }

    protected void setClinicStaffName(String clinicStaffName) {
        this.clinicStaffName = clinicStaffName;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    protected void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisCdrClinicRecord other = (CisCdrClinicRecord) obj;
        return Objects.equals(id, other.id);
    }

    public CisCdrClinicRecord create(CisCdrClinicRecordNto cisCdrClinicRecordNto) {
        Assert.notNull(cisCdrClinicRecordNto, "参数cisCdrClinicRecordNto不能为空！");

        setId(cisCdrClinicRecordNto.getId());
        setVisitCode(cisCdrClinicRecordNto.getVisitCode());
        setPatCode(cisCdrClinicRecordNto.getPatCode());
        setName(cisCdrClinicRecordNto.getName());
        setSex(cisCdrClinicRecordNto.getSex());
        setBirthDate(cisCdrClinicRecordNto.getBirthDate());
        setCardType(cisCdrClinicRecordNto.getCardType());
        setCardCode(cisCdrClinicRecordNto.getCardCode());
        setNation(cisCdrClinicRecordNto.getNation());
        setNationality(cisCdrClinicRecordNto.getNationality());
        setTel(cisCdrClinicRecordNto.getTel());
        setNativePlace(cisCdrClinicRecordNto.getNativePlace());
        setLiveAddr(cisCdrClinicRecordNto.getLiveAddr());
        setLiveExactAddr(cisCdrClinicRecordNto.getLiveExactAddr());
        setClinicDate(cisCdrClinicRecordNto.getClinicDate());
        setClinicDept(cisCdrClinicRecordNto.getClinicDept());
        setClinicStaff(cisCdrClinicRecordNto.getClinicStaff());
        setClinicStaffName(cisCdrClinicRecordNto.getClinicStaffName());
        setCreateDate(cisCdrClinicRecordNto.getCreateDate());
        return this;
    }

    public void update(CisCdrClinicRecordEto cisCdrClinicRecordEto) {
    }

    public void delete() {
    }

}
