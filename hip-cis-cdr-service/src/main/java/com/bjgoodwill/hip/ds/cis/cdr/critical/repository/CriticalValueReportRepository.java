package com.bjgoodwill.hip.ds.cis.cdr.critical.repository;

import com.bjgoodwill.hip.ds.cis.cdr.critical.entity.CriticalValueReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.critical.repository.CriticalValueReportRepository")
public interface CriticalValueReportRepository extends JpaRepository<CriticalValueReport, String>, JpaSpecificationExecutor<CriticalValueReport> {

}