package com.bjgoodwill.hip.ds.cis.cdr.order.repository;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.ds.cis.cdr.order.entity.CisCdrOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.order.repository.CisCdrOrderRepository")
public interface CisCdrOrderRepository extends JpaRepository<CisCdrOrder, String>, JpaSpecificationExecutor<CisCdrOrder> {

    @Query(value = "SELECT a FROM CisCdrOrder a WHERE a.createdDate >= ?1 and (a.orderClass = ?2 or ?2 is null) ")
    List<CisCdrOrder> findCisCdrOrderByCreateDateAfter(LocalDateTime dateTime, SystemTypeEnum orderCLass);
}