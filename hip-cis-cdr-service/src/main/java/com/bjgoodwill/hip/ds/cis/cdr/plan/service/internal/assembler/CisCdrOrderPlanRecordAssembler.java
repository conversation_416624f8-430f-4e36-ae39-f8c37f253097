package com.bjgoodwill.hip.ds.cis.cdr.plan.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.plan.entity.CisCdrOrderPlanRecord;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisCdrOrderPlanRecordTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisCdrOrderPlanRecordAssembler {

    public static List<CisCdrOrderPlanRecordTo> toTos(List<CisCdrOrderPlanRecord> cisCdrOrderPlanRecords) {
        return toTos(cisCdrOrderPlanRecords, false);
    }

    public static List<CisCdrOrderPlanRecordTo> toTos(List<CisCdrOrderPlanRecord> cisCdrOrderPlanRecords, boolean withAllParts) {
        Assert.notNull(cisCdrOrderPlanRecords, "参数cisCdrOrderPlanRecords不能为空！");

        List<CisCdrOrderPlanRecordTo> tos = new ArrayList<>();
        for (CisCdrOrderPlanRecord cisCdrOrderPlanRecord : cisCdrOrderPlanRecords)
            tos.add(toTo(cisCdrOrderPlanRecord, withAllParts));
        return tos;
    }

    public static CisCdrOrderPlanRecordTo toTo(CisCdrOrderPlanRecord cisCdrOrderPlanRecord) {
        return toTo(cisCdrOrderPlanRecord, false);
    }

    /**
     * @generated
     */
    public static CisCdrOrderPlanRecordTo toTo(CisCdrOrderPlanRecord cisCdrOrderPlanRecord, boolean withAllParts) {
        if (cisCdrOrderPlanRecord == null)
            return null;
        CisCdrOrderPlanRecordTo to = new CisCdrOrderPlanRecordTo();
        to.setId(cisCdrOrderPlanRecord.getId());
        to.setPatCode(cisCdrOrderPlanRecord.getPatCode());
        to.setVisitCode(cisCdrOrderPlanRecord.getVisitCode());
        to.setOrderId(cisCdrOrderPlanRecord.getOrderId());
        to.setApplyId(cisCdrOrderPlanRecord.getApplyId());
        to.setOrderClass(cisCdrOrderPlanRecord.getOrderClass());
        to.setServiceItemName(cisCdrOrderPlanRecord.getServiceItemName());
        to.setExecuteOrg(cisCdrOrderPlanRecord.getExecuteOrg());
        to.setExecuteStaff(cisCdrOrderPlanRecord.getExecuteStaff());
        to.setExecuteDate(cisCdrOrderPlanRecord.getExecuteDate());
        to.setDrugCode(cisCdrOrderPlanRecord.getDrugCode());
        to.setCreatedDate(cisCdrOrderPlanRecord.getCreatedDate());
        to.setExecuteOrgName(cisCdrOrderPlanRecord.getExecuteOrgName());
        to.setExecuteStaffName(cisCdrOrderPlanRecord.getExecuteStaffName());
        to.setSpec(cisCdrOrderPlanRecord.getSpec());
        to.setUsageName(cisCdrOrderPlanRecord.getUsageName());
        to.setFrequencyName(cisCdrOrderPlanRecord.getFrequencyName());
        to.setDosage(cisCdrOrderPlanRecord.getDosage());
        to.setDosageUnitName(cisCdrOrderPlanRecord.getDosageUnitName());
        if (withAllParts) {
            to.setCisCdrOrderPlanDetailRecords(CisCdrOrderPlanDetailRecordAssembler.toTos(cisCdrOrderPlanRecord.getCisCdrOrderPlanDetailRecords()));
        }
        return to;
    }

}