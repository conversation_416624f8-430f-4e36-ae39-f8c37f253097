package com.bjgoodwill.hip.ds.cis.cdr.report.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.report.entity.CisReportResultSpcobsDetail;
import com.bjgoodwill.hip.ds.cis.cdr.report.to.CisReportResultSpcobsDetailTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisReportResultSpcobsDetailAssembler {

    public static List<CisReportResultSpcobsDetailTo> toTos(List<CisReportResultSpcobsDetail> cisReportResultSpcobsDetails) {
        return toTos(cisReportResultSpcobsDetails, false);
    }

    public static List<CisReportResultSpcobsDetailTo> toTos(List<CisReportResultSpcobsDetail> cisReportResultSpcobsDetails, boolean withAllParts) {
        Assert.notNull(cisReportResultSpcobsDetails, "参数cisReportResultSpcobsDetails不能为空！");

        List<CisReportResultSpcobsDetailTo> tos = new ArrayList<>();
        for (CisReportResultSpcobsDetail cisReportResultSpcobsDetail : cisReportResultSpcobsDetails)
            tos.add(toTo(cisReportResultSpcobsDetail, withAllParts));
        return tos;
    }

    public static CisReportResultSpcobsDetailTo toTo(CisReportResultSpcobsDetail cisReportResultSpcobsDetail) {
        return toTo(cisReportResultSpcobsDetail, false);
    }

    /**
     * @generated
     */
    public static CisReportResultSpcobsDetailTo toTo(CisReportResultSpcobsDetail cisReportResultSpcobsDetail, boolean withAllParts) {
        if (cisReportResultSpcobsDetail == null)
            return null;
        CisReportResultSpcobsDetailTo to = new CisReportResultSpcobsDetailTo();
        to.setId(cisReportResultSpcobsDetail.getId());
        to.setCisReportResultId(cisReportResultSpcobsDetail.getCisReportResultId());
        to.setReportDetailCode(cisReportResultSpcobsDetail.getReportDetailCode());
        to.setSubItemCode(cisReportResultSpcobsDetail.getSubItemCode());
        to.setSubItemName(cisReportResultSpcobsDetail.getSubItemName());
        to.setResultFlag(cisReportResultSpcobsDetail.getResultFlag());
        to.setCrisisResultFlag(cisReportResultSpcobsDetail.getCrisisResultFlag());
        to.setRemark(cisReportResultSpcobsDetail.getRemark());
        to.setCreatedDate(cisReportResultSpcobsDetail.getCreatedDate());
        to.setItemCode(cisReportResultSpcobsDetail.getItemCode());
        to.setItemName(cisReportResultSpcobsDetail.getItemName());
        to.setSampleCode(cisReportResultSpcobsDetail.getSampleCode());
        to.setSampleType(cisReportResultSpcobsDetail.getSampleType());
        to.setResultLow(cisReportResultSpcobsDetail.getResultLow());
        to.setResultHigh(cisReportResultSpcobsDetail.getResultHigh());
        to.setUnitCode(cisReportResultSpcobsDetail.getUnitCode());
        to.setUnitName(cisReportResultSpcobsDetail.getUnitName());
        to.setReferenceValueLow(cisReportResultSpcobsDetail.getReferenceValueLow());
        to.setReferenceValueHigh(cisReportResultSpcobsDetail.getReferenceValueHigh());
        to.setCrisisLowLimit(cisReportResultSpcobsDetail.getCrisisLowLimit());
        to.setCrisisHighLimit(cisReportResultSpcobsDetail.getCrisisHighLimit());
        to.setReportContent(cisReportResultSpcobsDetail.getReportContent());
        if (withAllParts) {
        }
        return to;
    }

}