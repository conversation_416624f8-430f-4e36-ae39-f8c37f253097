package com.bjgoodwill.hip.ds.cis.cdr.record.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisIpdDrugCdrOrderPlanRecordQto;
import com.bjgoodwill.hip.ds.cis.cdr.record.entity.CisCdrClinicIpdRecord;
import com.bjgoodwill.hip.ds.cis.cdr.record.service.CisCdrClinicIpdRecordService;
import com.bjgoodwill.hip.ds.cis.cdr.record.service.internal.assembler.CisCdrClinicIpdRecordAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicIpdRecordEto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicIpdRecordNto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicIpdRecordTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.record.service.CisCdrClinicIpdRecordService")
@RequestMapping(value = "/api/cdr/record/cisCdrClinicIpdRecord", produces = "application/json; charset=utf-8")
public class CisCdrClinicIpdRecordServiceImpl implements CisCdrClinicIpdRecordService {

//    @Override
//    @Transactional(rollbackFor = Throwable.class, readOnly = true)
//    public List<CisCdrClinicIpdRecordTo> getCisCdrClinicIpdRecords(CisCdrClinicIpdRecordQto cisCdrClinicIpdRecordQto) {
//        return CisCdrClinicIpdRecordAssembler.toTos(CisCdrClinicIpdRecord.getCisCdrClinicIpdRecords(cisCdrClinicIpdRecordQto));
//    }
//
//    @Override
//    @Transactional(rollbackFor = Throwable.class, readOnly = true)
//    public GridResultSet<CisCdrClinicIpdRecordTo> getCisCdrClinicIpdRecordPage(CisCdrClinicIpdRecordQto cisCdrClinicIpdRecordQto) {
//        Page<CisCdrClinicIpdRecord> page = CisCdrClinicIpdRecord.getCisCdrClinicIpdRecordPage(cisCdrClinicIpdRecordQto);
//        Page<CisCdrClinicIpdRecordTo> result = page.map(CisCdrClinicIpdRecordAssembler::toTo);
//        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
//    }


    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisCdrClinicIpdRecordTo createCisCdrClinicIpdRecord(CisCdrClinicIpdRecordNto cisCdrClinicIpdRecordNto) {
        CisCdrClinicIpdRecord cisCdrClinicIpdRecord = new CisCdrClinicIpdRecord();
        cisCdrClinicIpdRecord = cisCdrClinicIpdRecord.create(cisCdrClinicIpdRecordNto);
        return CisCdrClinicIpdRecordAssembler.toTo(cisCdrClinicIpdRecord);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisCdrClinicIpdRecord(String id, CisCdrClinicIpdRecordEto cisCdrClinicIpdRecordEto) {
        Optional<CisCdrClinicIpdRecord> cisCdrClinicIpdRecordOptional = CisCdrClinicIpdRecord.getCisCdrClinicIpdRecordById(id);
        cisCdrClinicIpdRecordOptional.ifPresent(cisCdrClinicIpdRecord -> cisCdrClinicIpdRecord.update(cisCdrClinicIpdRecordEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisCdrClinicIpdRecordTo> getCisCdrClinicIpdRecords(CisIpdDrugCdrOrderPlanRecordQto cisIpdDrugCdrOrderPlanRecordQto) {
        return CisCdrClinicIpdRecordAssembler.toTos(CisCdrClinicIpdRecord.getCisCdrClinicIpdRecords(cisIpdDrugCdrOrderPlanRecordQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisCdrClinicIpdRecordTo> getCisCdrClinicIpdRecordPage(CisIpdDrugCdrOrderPlanRecordQto cisIpdDrugCdrOrderPlanRecordQto) {
        Page<CisCdrClinicIpdRecord> page = CisCdrClinicIpdRecord.getCisCdrClinicIpdRecordPage(cisIpdDrugCdrOrderPlanRecordQto);
        Page<CisCdrClinicIpdRecordTo> result = page.map(CisCdrClinicIpdRecordAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}