package com.bjgoodwill.hip.ds.cis.cdr.order.service.internal;

import com.bjgoodwill.hip.ds.cis.cdr.order.entity.CisCdrOpdOrder;
import com.bjgoodwill.hip.ds.cis.cdr.order.service.CisCdrOpdOrderService;
import com.bjgoodwill.hip.ds.cis.cdr.order.service.internal.assembler.CisCdrOpdOrderAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOpdOrderEto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOpdOrderNto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOpdOrderTo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.order.service.CisCdrOpdOrderService")
@RequestMapping(value = "/api/cdr/order/cisCdrOpdOrder", produces = "application/json; charset=utf-8")
public class CisCdrOpdOrderServiceImpl implements CisCdrOpdOrderService {

//    @Override
//    @Transactional(rollbackFor = Throwable.class, readOnly = true)
//    public List<CisCdrOpdOrderTo> getCisCdrOpdOrders(CisCdrOpdOrderQto cisCdrOpdOrderQto) {
//        return CisCdrOpdOrderAssembler.toTos(CisCdrOpdOrder.getCisCdrOpdOrders(cisCdrOpdOrderQto));
//    }
//
//    @Override
//    @Transactional(rollbackFor = Throwable.class, readOnly = true)
//    public GridResultSet<CisCdrOpdOrderTo> getCisCdrOpdOrderPage(CisCdrOpdOrderQto cisCdrOpdOrderQto) {
//        Page<CisCdrOpdOrder> page = CisCdrOpdOrder.getCisCdrOpdOrderPage(cisCdrOpdOrderQto);
//        Page<CisCdrOpdOrderTo> result = page.map(CisCdrOpdOrderAssembler::toTo);
//        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
//    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisCdrOpdOrderTo createCisCdrOpdOrder(CisCdrOpdOrderNto cisCdrOpdOrderNto) {
        CisCdrOpdOrder cisCdrOpdOrder = new CisCdrOpdOrder();
        cisCdrOpdOrder = cisCdrOpdOrder.create(cisCdrOpdOrderNto);
        return CisCdrOpdOrderAssembler.toTo(cisCdrOpdOrder);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisCdrOpdOrder(String id, CisCdrOpdOrderEto cisCdrOpdOrderEto) {
        Optional<CisCdrOpdOrder> cisCdrOpdOrderOptional = CisCdrOpdOrder.getCisCdrOpdOrderById(id);
        cisCdrOpdOrderOptional.ifPresent(cisCdrOpdOrder -> cisCdrOpdOrder.update(cisCdrOpdOrderEto));
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}