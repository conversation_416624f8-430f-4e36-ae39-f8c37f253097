package com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.entity.CisAntimicrobialsSkinExecReport;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.service.CisAntimicrobialsSkinExecReportService;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.service.internal.assembler.CisAntimicrobialsSkinExecReportAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.to.CisAntimicrobialsSkinExecReportNto;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.to.CisAntimicrobialsSkinExecReportQto;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.to.CisAntimicrobialsSkinExecReportTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.service.CisAntimicrobialsSkinExecReportService")
@RequestMapping(value = "/api/cdr/antimicrobials/cisAntimicrobialsSkinExecReport", produces = "application/json; charset=utf-8")
public class CisAntimicrobialsSkinExecReportServiceImpl implements CisAntimicrobialsSkinExecReportService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAntimicrobialsSkinExecReportTo> getCisAntimicrobialsSkinExecReports(CisAntimicrobialsSkinExecReportQto cisAntimicrobialsSkinExecReportQto) {
        return CisAntimicrobialsSkinExecReportAssembler.toTos(CisAntimicrobialsSkinExecReport.getCisAntimicrobialsSkinExecReports(cisAntimicrobialsSkinExecReportQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisAntimicrobialsSkinExecReportTo> getCisAntimicrobialsSkinExecReportPage(CisAntimicrobialsSkinExecReportQto cisAntimicrobialsSkinExecReportQto) {
        Page<CisAntimicrobialsSkinExecReport> page = CisAntimicrobialsSkinExecReport.getCisAntimicrobialsSkinExecReportPage(cisAntimicrobialsSkinExecReportQto);
        Page<CisAntimicrobialsSkinExecReportTo> result = page.map(CisAntimicrobialsSkinExecReportAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisAntimicrobialsSkinExecReportTo getCisAntimicrobialsSkinExecReportById(String id) {
        return CisAntimicrobialsSkinExecReportAssembler.toTo(CisAntimicrobialsSkinExecReport.getCisAntimicrobialsSkinExecReportById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisAntimicrobialsSkinExecReportTo createCisAntimicrobialsSkinExecReport(CisAntimicrobialsSkinExecReportNto cisAntimicrobialsSkinExecReportNto) {
        CisAntimicrobialsSkinExecReport cisAntimicrobialsSkinExecReport = new CisAntimicrobialsSkinExecReport();
        cisAntimicrobialsSkinExecReport = cisAntimicrobialsSkinExecReport.create(cisAntimicrobialsSkinExecReportNto);
        return CisAntimicrobialsSkinExecReportAssembler.toTo(cisAntimicrobialsSkinExecReport);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisAntimicrobialsSkinExecReport(String id) {
        Optional<CisAntimicrobialsSkinExecReport> cisAntimicrobialsSkinExecReportOptional = CisAntimicrobialsSkinExecReport.getCisAntimicrobialsSkinExecReportById(id);
        cisAntimicrobialsSkinExecReportOptional.ifPresent(cisAntimicrobialsSkinExecReport -> cisAntimicrobialsSkinExecReport.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisAntimicrobialsSkinExecReportTo> createCisAntimicrobialsSkinExecReport(List<CisAntimicrobialsSkinExecReportNto> cisAntimicrobialsSkinExecReportNtos) {
        CisAntimicrobialsSkinExecReport cisAntimicrobialsSkinExecReport = new CisAntimicrobialsSkinExecReport();
        return CisAntimicrobialsSkinExecReportAssembler.toTos(
                cisAntimicrobialsSkinExecReportNtos.stream().map(nto -> cisAntimicrobialsSkinExecReport.create(nto)).toList());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }

    /**
     * 根据就诊编号查询抗菌药物皮肤测试执行报告
     * 此方法使用了@Transactional注解，指明该方法在执行时需要开启一个事务，并且任何异常都会导致事务回滚
     * 注解的readOnly属性设置为true，表示此事务只读取数据，不修改数据库
     *
     * @param visitCode 就诊编号，用于查询相关的抗菌药物皮肤测试执行报告
     * @return 返回一个包含抗菌药物皮肤测试执行报告传输对象的列表
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisAntimicrobialsSkinExecReportTo> findreportsByVisitCode(String visitCode) {
        // 查询抗菌药物皮肤测试执行报告
        List<CisAntimicrobialsSkinExecReport> reports = CisAntimicrobialsSkinExecReport.findreportsByVisitCode(visitCode);

        // 按服务项目代码分组，并获取每个分组中最新的一条记录
        Map<String, CisAntimicrobialsSkinExecReport> latestReports = reports.stream()
                .collect(Collectors.toMap(
                        CisAntimicrobialsSkinExecReport::getServiceItemCode,
                        Function.identity(),
                        (existing, replacement) -> existing.getExecPlanDate().isAfter(replacement.getExecPlanDate()) ? existing : replacement
                ));

        // 将查询结果转换为传输对象（TO）列表并返回
        return CisAntimicrobialsSkinExecReportAssembler.toTos(new ArrayList<>(latestReports.values()));
    }

}