package com.bjgoodwill.hip.ds.cis.cdr.report.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.ds.cis.cdr.report.repository.CisReportResultSpcobsDetailRepository;
import com.bjgoodwill.hip.ds.cis.cdr.report.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "检验报告明细")
@DiscriminatorValue("1")
public class CisReportResultSpcobsDetail extends CisReportResultDetail {

    @Comment("检验项目编码")
    @Column(name = "item_code", nullable = true)
    private String itemCode;


    @Comment("检验项目名称")
    @Column(name = "item_name", nullable = true)
    private String itemName;


    @Comment("标本编码（试管号）")
    @Column(name = "sample_code", nullable = true)
    private String sampleCode;


    @Comment("标本类型")
    @Column(name = "sample_type", nullable = true)
    private String sampleType;


    @Comment("结果低值（单值结果）")
    @Column(name = "result_low", nullable = true)
    private String resultLow;


    @Comment("结果高值")
    @Column(name = "result_high", nullable = true)
    private String resultHigh;


    @Comment("单位编码")
    @Column(name = "unit_code", nullable = true)
    private String unitCode;


    @Comment("单位名称")
    @Column(name = "unit_name", nullable = true)
    private String unitName;


    @Comment("参考范围低值")
    @Column(name = "reference_value_low", nullable = true)
    private String referenceValueLow;


    @Comment("参考范围高值")
    @Column(name = "reference_value_high", nullable = true)
    private String referenceValueHigh;


    @Comment("危机值下限")
    @Column(name = "crisis_low_limit", nullable = true)
    private String crisisLowLimit;


    @Comment("危机值上限")
    @Column(name = "crisis_high_limit", nullable = true)
    private String crisisHighLimit;

    public static Optional<CisReportResultSpcobsDetail> getCisReportResultSpcobsDetailById(String id) {
        return dao().findById(id);
    }

    public static List<CisReportResultSpcobsDetail> getCisReportResultSpcobsDetails(String cisReportResultId, CisReportResultSpcobsDetailQto qto) {
        if (cisReportResultId != null) {
            qto.setCisReportResultId(cisReportResultId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisReportResultSpcobsDetail> getCisReportResultSpcobsDetailPage(String cisReportResultId, CisReportResultSpcobsDetailQto qto) {
        if (cisReportResultId != null) {
            qto.setCisReportResultId(cisReportResultId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisReportResultSpcobsDetail> getSpecification(CisReportResultSpcobsDetailQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getCisReportResultId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisReportResultId"), qto.getCisReportResultId()));
            }
            if (StringUtils.isNotBlank(qto.getReportDetailCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportDetailCode"), qto.getReportDetailCode()));
            }
            if (StringUtils.isNotBlank(qto.getSubItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("subItemCode"), qto.getSubItemCode()));
            }
            if (StringUtils.isNotBlank(qto.getSubItemName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("subItemName"), qto.getSubItemName()));
            }
            if (qto.getResultFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("resultFlag"), qto.getResultFlag()));
            }
            if (StringUtils.isNotBlank(qto.getCrisisResultFlag())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("crisisResultFlag"), qto.getCrisisResultFlag()));
            }
            if (StringUtils.isNotBlank(qto.getRemark())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("remark"), qto.getRemark()));
            }
            if (StringUtils.isNotBlank(qto.getItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("itemCode"), qto.getItemCode()));
            }
            if (StringUtils.isNotBlank(qto.getItemName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("itemName"), qto.getItemName()));
            }
            if (StringUtils.isNotBlank(qto.getSampleCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sampleCode"), qto.getSampleCode()));
            }
            if (StringUtils.isNotBlank(qto.getSampleType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sampleType"), qto.getSampleType()));
            }
            if (StringUtils.isNotBlank(qto.getResultLow())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("resultLow"), qto.getResultLow()));
            }
            if (StringUtils.isNotBlank(qto.getResultHigh())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("resultHigh"), qto.getResultHigh()));
            }
            if (StringUtils.isNotBlank(qto.getUnitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("unitCode"), qto.getUnitCode()));
            }
            if (StringUtils.isNotBlank(qto.getUnitName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("unitName"), qto.getUnitName()));
            }
            if (StringUtils.isNotBlank(qto.getReferenceValueLow())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("referenceValueLow"), qto.getReferenceValueLow()));
            }
            if (StringUtils.isNotBlank(qto.getReferenceValueHigh())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("referenceValueHigh"), qto.getReferenceValueHigh()));
            }
            if (StringUtils.isNotBlank(qto.getCrisisLowLimit())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("crisisLowLimit"), qto.getCrisisLowLimit()));
            }
            if (StringUtils.isNotBlank(qto.getCrisisHighLimit())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("crisisHighLimit"), qto.getCrisisHighLimit()));
            }
            return predicate;
        };
    }

    private static CisReportResultSpcobsDetailRepository dao() {
        return SpringUtil.getBean(CisReportResultSpcobsDetailRepository.class);
    }

    public static List<CisReportResultSpcobsDetail> findCisReportResultDetailWithVisitCode(String itemCode, String visitCode) {
        return dao().findCisReportResultDetailWithVisitCode(itemCode, visitCode);
    }

    public static List<CisReportResultSpcobsDetail> getSpcobsDetailByVisitCode(String visitCode) {
        return dao().getSpcobsDetailByVisitCode(visitCode);
    }


    public static List<CisReportResultSpcobsDetail> findCisReportResultDetailWithPatMiCode(String itemCode, String patMiCode) {
        return dao().findCisReportResultDetailWithPatMiCode(itemCode, patMiCode);
    }

    public String getItemCode() {
        return itemCode;
    }

    protected void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    protected void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSampleCode() {
        return sampleCode;
    }

    protected void setSampleCode(String sampleCode) {
        this.sampleCode = sampleCode;
    }

    public String getSampleType() {
        return sampleType;
    }

    protected void setSampleType(String sampleType) {
        this.sampleType = sampleType;
    }

    public String getResultLow() {
        return resultLow;
    }

    protected void setResultLow(String resultLow) {
        this.resultLow = resultLow;
    }

    public String getResultHigh() {
        return resultHigh;
    }

    protected void setResultHigh(String resultHigh) {
        this.resultHigh = resultHigh;
    }

    public String getUnitCode() {
        return unitCode;
    }

    protected void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    protected void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getReferenceValueLow() {
        return referenceValueLow;
    }

    protected void setReferenceValueLow(String referenceValueLow) {
        this.referenceValueLow = referenceValueLow;
    }

    public String getReferenceValueHigh() {
        return referenceValueHigh;
    }

    protected void setReferenceValueHigh(String referenceValueHigh) {
        this.referenceValueHigh = referenceValueHigh;
    }

    public String getCrisisLowLimit() {
        return crisisLowLimit;
    }

    protected void setCrisisLowLimit(String crisisLowLimit) {
        this.crisisLowLimit = crisisLowLimit;
    }

    public String getCrisisHighLimit() {
        return crisisHighLimit;
    }

    protected void setCrisisHighLimit(String crisisHighLimit) {
        this.crisisHighLimit = crisisHighLimit;
    }

    @Override
    public CisReportResultDetail create(String cisReportResultId, CisReportResultDetailNto cisReportResultDetailNto) {
        return create(cisReportResultId, (CisReportResultSpcobsDetailNto) cisReportResultDetailNto);
    }

    @Override
    public void update(CisReportResultDetailEto cisReportResultDetailEto) {
        update((CisReportResultSpcobsDetailEto) cisReportResultDetailEto);
    }

    public CisReportResultSpcobsDetail create(String cisReportResultId, CisReportResultSpcobsDetailNto cisReportResultSpcobsDetailNto) {
        Assert.notNull(cisReportResultSpcobsDetailNto, "参数cisReportResultSpcobsDetailNto不能为空！");
        super.create(cisReportResultId, cisReportResultSpcobsDetailNto);

        setItemCode(cisReportResultSpcobsDetailNto.getItemCode());
        setItemName(cisReportResultSpcobsDetailNto.getItemName());
        setSampleCode(cisReportResultSpcobsDetailNto.getSampleCode());
        setSampleType(cisReportResultSpcobsDetailNto.getSampleType());
        setResultLow(cisReportResultSpcobsDetailNto.getResultLow());
        setResultHigh(cisReportResultSpcobsDetailNto.getResultHigh());
        setUnitCode(cisReportResultSpcobsDetailNto.getUnitCode());
        setUnitName(cisReportResultSpcobsDetailNto.getUnitName());
        setReferenceValueLow(cisReportResultSpcobsDetailNto.getReferenceValueLow());
        setReferenceValueHigh(cisReportResultSpcobsDetailNto.getReferenceValueHigh());
        setCrisisLowLimit(cisReportResultSpcobsDetailNto.getCrisisLowLimit());
        setCrisisHighLimit(cisReportResultSpcobsDetailNto.getCrisisHighLimit());
        dao().save(this);
        return this;
    }

    public void update(CisReportResultSpcobsDetailEto cisReportResultSpcobsDetailEto) {
        super.update(cisReportResultSpcobsDetailEto);
        setItemCode(cisReportResultSpcobsDetailEto.getItemCode());
        setItemName(cisReportResultSpcobsDetailEto.getItemName());
        setSampleCode(cisReportResultSpcobsDetailEto.getSampleCode());
        setSampleType(cisReportResultSpcobsDetailEto.getSampleType());
        setResultLow(cisReportResultSpcobsDetailEto.getResultLow());
        setResultHigh(cisReportResultSpcobsDetailEto.getResultHigh());
        setUnitCode(cisReportResultSpcobsDetailEto.getUnitCode());
        setUnitName(cisReportResultSpcobsDetailEto.getUnitName());
        setReferenceValueLow(cisReportResultSpcobsDetailEto.getReferenceValueLow());
        setReferenceValueHigh(cisReportResultSpcobsDetailEto.getReferenceValueHigh());
        setCrisisLowLimit(cisReportResultSpcobsDetailEto.getCrisisLowLimit());
        setCrisisHighLimit(cisReportResultSpcobsDetailEto.getCrisisHighLimit());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
