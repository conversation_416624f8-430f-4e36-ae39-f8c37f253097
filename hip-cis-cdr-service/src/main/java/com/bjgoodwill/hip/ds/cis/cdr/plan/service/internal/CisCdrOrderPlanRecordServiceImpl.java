package com.bjgoodwill.hip.ds.cis.cdr.plan.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.plan.entity.CisCdrOrderPlanDetailRecord;
import com.bjgoodwill.hip.ds.cis.cdr.plan.entity.CisCdrOrderPlanRecord;
import com.bjgoodwill.hip.ds.cis.cdr.plan.service.CisCdrOrderPlanRecordService;
import com.bjgoodwill.hip.ds.cis.cdr.plan.service.internal.assembler.CisCdrOrderPlanDetailRecordAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.plan.service.internal.assembler.CisCdrOrderPlanRecordAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.*;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.plan.service.CisCdrOrderPlanRecordService")
@RequestMapping(value = "/api/cdr/plan/cisCdrOrderPlanRecord", produces = "application/json; charset=utf-8")
public class CisCdrOrderPlanRecordServiceImpl implements CisCdrOrderPlanRecordService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisCdrOrderPlanRecordTo> getCisCdrOrderPlanRecords(CisCdrOrderPlanRecordQto cisCdrOrderPlanRecordQto) {
        return CisCdrOrderPlanRecordAssembler.toTos(CisCdrOrderPlanRecord.getCisCdrOrderPlanRecords(cisCdrOrderPlanRecordQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisCdrOrderPlanRecordTo> getCisCdrOrderPlanRecordPage(CisCdrOrderPlanRecordQto cisCdrOrderPlanRecordQto) {
        Page<CisCdrOrderPlanRecord> page = CisCdrOrderPlanRecord.getCisCdrOrderPlanRecordPage(cisCdrOrderPlanRecordQto);
        Page<CisCdrOrderPlanRecordTo> result = page.map(CisCdrOrderPlanRecordAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisCdrOrderPlanRecordTo createCisCdrOrderPlanRecord(CisCdrOrderPlanRecordNto cisCdrOrderPlanRecordNto) {
        CisCdrOrderPlanRecord cisCdrOrderPlanRecord = new CisCdrOrderPlanRecord();
        cisCdrOrderPlanRecord = cisCdrOrderPlanRecord.create(cisCdrOrderPlanRecordNto);
        return CisCdrOrderPlanRecordAssembler.toTo(cisCdrOrderPlanRecord);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisCdrOrderPlanRecord(String id) {
        Optional<CisCdrOrderPlanRecord> cisCdrOrderPlanRecordOptional = CisCdrOrderPlanRecord.getCisCdrOrderPlanRecordById(id);
        cisCdrOrderPlanRecordOptional.ifPresent(cisCdrOrderPlanRecord -> cisCdrOrderPlanRecord.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisCdrOrderPlanDetailRecordTo getCisCdrOrderPlanDetailRecordById(String id) {
        return CisCdrOrderPlanDetailRecordAssembler.toTo(CisCdrOrderPlanDetailRecord.getCisCdrOrderPlanDetailRecordById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisCdrOrderPlanDetailRecordTo createCisCdrOrderPlanDetailRecord(String orderPlanId, CisCdrOrderPlanDetailRecordNto cisCdrOrderPlanDetailRecordNto) {
        CisCdrOrderPlanDetailRecord cisCdrOrderPlanDetailRecord = new CisCdrOrderPlanDetailRecord();
        cisCdrOrderPlanDetailRecord = cisCdrOrderPlanDetailRecord.create(orderPlanId, cisCdrOrderPlanDetailRecordNto);
        return CisCdrOrderPlanDetailRecordAssembler.toTo(cisCdrOrderPlanDetailRecord);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisCdrOrderPlanDetailRecord(String id) {
        Optional<CisCdrOrderPlanDetailRecord> cisCdrOrderPlanDetailRecordOptional = CisCdrOrderPlanDetailRecord.getCisCdrOrderPlanDetailRecordById(id);
        cisCdrOrderPlanDetailRecordOptional.ifPresent(cisCdrOrderPlanDetailRecord -> cisCdrOrderPlanDetailRecord.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void createCisCdrOrderPlanRecords(CisCdrOrderPlanRecordWithDetailNto cisCdrOrderPlanRecordWithDetailNto) {

        CisCdrOrderPlanRecord cisCdrOrderPlanRecord = new CisCdrOrderPlanRecord();

        List<CisCdrOrderPlanRecordNto> cisCdrOrderPlanRecordNtos = cisCdrOrderPlanRecordWithDetailNto.getCisCdrOrderPlanRecordNtos();
        cisCdrOrderPlanRecordNtos.forEach(cisCdrOrderPlanRecordNto -> {
            cisCdrOrderPlanRecord.create(cisCdrOrderPlanRecordNto);
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public LocalDateTime getLastUpdateTime() {
        return CisCdrOrderPlanRecord.getLastCreatedDate();
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}