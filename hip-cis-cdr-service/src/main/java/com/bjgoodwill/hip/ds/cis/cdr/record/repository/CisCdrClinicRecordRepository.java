package com.bjgoodwill.hip.ds.cis.cdr.record.repository;

import com.bjgoodwill.hip.ds.cis.cdr.record.entity.CisCdrClinicRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.record.repository.CisCdrClinicRecordRepository")
public interface CisCdrClinicRecordRepository extends JpaRepository<CisCdrClinicRecord, String>, JpaSpecificationExecutor<CisCdrClinicRecord> {

}