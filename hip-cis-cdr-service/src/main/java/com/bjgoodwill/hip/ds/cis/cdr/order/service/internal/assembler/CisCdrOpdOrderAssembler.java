package com.bjgoodwill.hip.ds.cis.cdr.order.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.order.entity.CisCdrOpdOrder;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOpdOrderTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisCdrOpdOrderAssembler {

    public static List<CisCdrOpdOrderTo> toTos(List<CisCdrOpdOrder> cisCdrOpdOrders) {
        return toTos(cisCdrOpdOrders, false);
    }

    public static List<CisCdrOpdOrderTo> toTos(List<CisCdrOpdOrder> cisCdrOpdOrders, boolean withAllParts) {
        Assert.notNull(cisCdrOpdOrders, "参数cisCdrOpdOrders不能为空！");

        List<CisCdrOpdOrderTo> tos = new ArrayList<>();
        for (CisCdrOpdOrder cisCdrOpdOrder : cisCdrOpdOrders)
            tos.add(toTo(cisCdrOpdOrder, withAllParts));
        return tos;
    }

    public static CisCdrOpdOrderTo toTo(CisCdrOpdOrder cisCdrOpdOrder) {
        return toTo(cisCdrOpdOrder, false);
    }

    /**
     * @generated
     */
    public static CisCdrOpdOrderTo toTo(CisCdrOpdOrder cisCdrOpdOrder, boolean withAllParts) {
        if (cisCdrOpdOrder == null)
            return null;
        CisCdrOpdOrderTo to = new CisCdrOpdOrderTo();
        to.setId(cisCdrOpdOrder.getId());
        to.setSortNo(cisCdrOpdOrder.getSortNo());
        to.setPatMiCode(cisCdrOpdOrder.getPatMiCode());
        to.setVisitCode(cisCdrOpdOrder.getVisitCode());
        to.setOrderServiceCode(cisCdrOpdOrder.getOrderServiceCode());
        to.setOrderClass(cisCdrOpdOrder.getOrderClass());
        to.setApplyCode(cisCdrOpdOrder.getApplyCode());
        to.setTreatmentCourse(cisCdrOpdOrder.getTreatmentCourse());
        to.setTreatmentCourseUnit(cisCdrOpdOrder.getTreatmentCourseUnit());
        to.setBabyFlag(cisCdrOpdOrder.getBabyFlag());
        to.setExecuteOrgCode(cisCdrOpdOrder.getExecuteOrgCode());
        to.setExecuteOrgName(cisCdrOpdOrder.getExecuteOrgName());
        to.setDeptNurseCode(cisCdrOpdOrder.getDeptNurseCode());
        to.setDeptNurseName(cisCdrOpdOrder.getDeptNurseName());
        to.setEffectiveLowDate(cisCdrOpdOrder.getEffectiveLowDate());
        to.setRepairFlag(cisCdrOpdOrder.getRepairFlag());
        to.setParentCode(cisCdrOpdOrder.getParentCode());
        to.setCriticalId(cisCdrOpdOrder.getCriticalId());
        to.setPrescriptionFlag(cisCdrOpdOrder.getPrescriptionFlag());
        to.setPassValue(cisCdrOpdOrder.getPassValue());
        to.setThirdFlag(cisCdrOpdOrder.getThirdFlag());
        to.setReceiveOrgCode(cisCdrOpdOrder.getReceiveOrgCode());
        to.setReceiveOrgName(cisCdrOpdOrder.getReceiveOrgName());
        to.setUsage(cisCdrOpdOrder.getUsage());
        to.setUsageName(cisCdrOpdOrder.getUsageName());
        to.setSbadmWay(cisCdrOpdOrder.getSbadmWay());
        to.setSkinFlag(cisCdrOpdOrder.getSkinFlag());
        to.setSkinType(cisCdrOpdOrder.getSkinType());
        to.setCreatedStaff(cisCdrOpdOrder.getCreatedStaff());
        to.setCreatedStaffName(cisCdrOpdOrder.getCreatedStaffName());
        to.setCreatedDate(cisCdrOpdOrder.getCreatedDate());
        to.setCommitDate(cisCdrOpdOrder.getCommitDate());
        to.setSubmitStaffId(cisCdrOpdOrder.getSubmitStaffId());
        to.setSubmitStaffName(cisCdrOpdOrder.getSubmitStaffName());
        to.setProofStaff(cisCdrOpdOrder.getProofStaff());
        to.setProofStaffName(cisCdrOpdOrder.getProofStaffName());
        to.setLimitConformFlag(cisCdrOpdOrder.getLimitConformFlag());
        to.setHospitalCode(cisCdrOpdOrder.getHospitalCode());
        to.setHospitalName(cisCdrOpdOrder.getHospitalName());
        to.setCancelStaff(cisCdrOpdOrder.getCancelStaff());
        to.setCancelStaffName(cisCdrOpdOrder.getCancelStaffName());
        to.setCancelDate(cisCdrOpdOrder.getCancelDate());
        to.setCancelRemark(cisCdrOpdOrder.getCancelRemark());
        to.setReMark(cisCdrOpdOrder.getReMark());
        to.setStatusCode(cisCdrOpdOrder.getStatusCode());
        to.setPrescriptionCode(cisCdrOpdOrder.getPrescriptionCode());

        if (withAllParts) {
        }
        return to;
    }

}