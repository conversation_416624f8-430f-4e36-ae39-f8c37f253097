package com.bjgoodwill.hip.ds.cis.cdr.order.repository;

import com.bjgoodwill.hip.ds.cis.cdr.order.entity.CisCdrIpdTermOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.order.repository.CisCdrIpdTermOrderRepository")
public interface CisCdrIpdTermOrderRepository extends JpaRepository<CisCdrIpdTermOrder, String>, JpaSpecificationExecutor<CisCdrIpdTermOrder> {

}