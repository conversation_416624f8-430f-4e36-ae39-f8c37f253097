package com.bjgoodwill.hip.ds.cis.cdr.plan.repository;

import com.bjgoodwill.hip.ds.cis.cdr.critical.entity.CriticalValueReport;
import com.bjgoodwill.hip.ds.cis.cdr.plan.entity.CisCdrOrderPlanRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.plan.repository.CisCdrOrderPlanRecordRepository")
public interface CisCdrOrderPlanRecordRepository extends JpaRepository<CisCdrOrderPlanRecord, String>, JpaSpecificationExecutor<CisCdrOrderPlanRecord> {

    @Query("select c from CisCdrOrderPlanRecord c order by c.createdDate desc LIMIT 1")
    Optional<CisCdrOrderPlanRecord> findTopByCreatedDate();
}