package com.bjgoodwill.hip.ds.cis.cdr.critical.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.critical.entity.CriticalValueReport;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CriticalValueReportAssembler {

    public static List<CriticalValueReportTo> toTos(List<CriticalValueReport> criticalValueReports) {
        return toTos(criticalValueReports, false);
    }

    public static List<CriticalValueReportTo> toTos(List<CriticalValueReport> criticalValueReports, boolean withAllParts) {
        Assert.notNull(criticalValueReports, "参数criticalValueReports不能为空！");

        List<CriticalValueReportTo> tos = new ArrayList<>();
        for (CriticalValueReport criticalValueReport : criticalValueReports)
            tos.add(toTo(criticalValueReport, withAllParts));
        return tos;
    }

    public static CriticalValueReportTo toTo(CriticalValueReport criticalValueReport) {
        return toTo(criticalValueReport, false);
    }

    /**
     * @generated
     */
    public static CriticalValueReportTo toTo(CriticalValueReport criticalValueReport, boolean withAllParts) {
        if (criticalValueReport == null)
            return null;
        CriticalValueReportTo to = new CriticalValueReportTo();
        to.setId(criticalValueReport.getId());
        to.setVisitType(criticalValueReport.getVisitType());
        to.setInpatientCode(criticalValueReport.getInpatientCode());
        to.setVisitCode(criticalValueReport.getVisitCode());
        to.setInDeptCode(criticalValueReport.getInDeptCode());
        to.setVisitOrgCode(criticalValueReport.getVisitOrgCode());
        to.setDoctCode(criticalValueReport.getDoctCode());
        to.setApplyCode(criticalValueReport.getApplyCode());
        to.setServiceItemCode(criticalValueReport.getServiceItemCode());
        to.setServiceItemName(criticalValueReport.getServiceItemName());
        to.setReportUser(criticalValueReport.getReportUser());
        to.setReportDate(criticalValueReport.getReportDate());
        to.setReportOrgCode(criticalValueReport.getReportOrgCode());
        to.setCriticalValue(criticalValueReport.getCriticalValue());
        to.setResponseOrgCode(criticalValueReport.getResponseOrgCode());
        to.setResponseUser(criticalValueReport.getResponseUser());
        to.setResponseDate(criticalValueReport.getResponseDate());
        to.setResponseValue(criticalValueReport.getResponseValue());
        to.setTelResponseUser(criticalValueReport.getTelResponseUser());
        to.setTelResponseDate(criticalValueReport.getTelResponseDate());
        to.setTelResponseRemark(criticalValueReport.getTelResponseRemark());
        to.setTelReponseFlag(criticalValueReport.getTelReponseFlag());
        to.setOrderId(criticalValueReport.getOrderId());
        to.setCourseRecord(criticalValueReport.getCourseRecord());
        to.setCourseRecordUser(criticalValueReport.getCourseRecordUser());
        to.setCourseRecordDate(criticalValueReport.getCourseRecordDate());
        to.setStatusCode(criticalValueReport.getStatusCode());
        to.setOrderCreatedDate(criticalValueReport.getOrderCreatedDate());
        to.setSamplingDate(criticalValueReport.getSamplingDate());
        to.setCall(criticalValueReport.getCall());
        to.setCallUrl(criticalValueReport.getCallUrl());
        to.setCrisisId(criticalValueReport.getCrisisId());
        to.setCreatedDate(criticalValueReport.getCreatedDate());
        to.setUpdatedStaff(criticalValueReport.getUpdatedStaff());
        to.setUpdatedStaffName(criticalValueReport.getUpdatedStaffName());
        to.setUpdatedDate(criticalValueReport.getUpdatedDate());
        to.setReportUserName(criticalValueReport.getReportUserName());
        to.setReportOrgName(criticalValueReport.getReportOrgName());
        to.setResponseOrgName(criticalValueReport.getResponseOrgName());
        to.setResponseUserName(criticalValueReport.getResponseUserName());
        if (withAllParts) {
        }
        return to;
    }

}