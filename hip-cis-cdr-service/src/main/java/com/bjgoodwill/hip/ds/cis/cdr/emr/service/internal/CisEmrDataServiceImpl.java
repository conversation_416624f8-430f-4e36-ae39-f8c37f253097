package com.bjgoodwill.hip.ds.cis.cdr.emr.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.emr.entity.CisEmrData;
import com.bjgoodwill.hip.ds.cis.cdr.emr.service.CisEmrDataService;
import com.bjgoodwill.hip.ds.cis.cdr.emr.service.internal.assembler.CisEmrDataAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.emr.to.CisEmrDataEto;
import com.bjgoodwill.hip.ds.cis.cdr.emr.to.CisEmrDataNto;
import com.bjgoodwill.hip.ds.cis.cdr.emr.to.CisEmrDataQto;
import com.bjgoodwill.hip.ds.cis.cdr.emr.to.CisEmrDataTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cdr.emr.service.CisEmrDataService")
@RequestMapping(value = "/api/cdr/emr/cisEmrData", produces = "application/json; charset=utf-8")
public class CisEmrDataServiceImpl implements CisEmrDataService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisEmrDataTo> getCisEmrDatas(CisEmrDataQto cisEmrDataQto) {
        return CisEmrDataAssembler.toTos(CisEmrData.getCisEmrDatas(cisEmrDataQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisEmrDataTo> getCisEmrDataPage(CisEmrDataQto cisEmrDataQto) {
        Page<CisEmrData> page = CisEmrData.getCisEmrDataPage(cisEmrDataQto);
        Page<CisEmrDataTo> result = page.map(CisEmrDataAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisEmrDataTo getCisEmrDataById(String id) {
        return CisEmrDataAssembler.toTo(CisEmrData.getCisEmrDataById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisEmrDataTo createCisEmrData(CisEmrDataNto cisEmrDataNto) {
        CisEmrData cisEmrData = new CisEmrData();
        cisEmrData = cisEmrData.create(cisEmrDataNto);
        return CisEmrDataAssembler.toTo(cisEmrData);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisEmrData(String id, CisEmrDataEto cisEmrDataEto) {
        Optional<CisEmrData> cisEmrDataOptional = CisEmrData.getCisEmrDataById(id);
        cisEmrDataOptional.ifPresent(cisEmrData -> cisEmrData.update(cisEmrDataEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisEmrData(String id) {
        Optional<CisEmrData> cisEmrDataOptional = CisEmrData.getCisEmrDataById(id);
        cisEmrDataOptional.ifPresent(cisEmrData -> cisEmrData.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}