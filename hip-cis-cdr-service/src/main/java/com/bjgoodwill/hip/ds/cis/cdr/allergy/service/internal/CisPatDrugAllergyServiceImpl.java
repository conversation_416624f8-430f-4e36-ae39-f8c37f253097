package com.bjgoodwill.hip.ds.cis.cdr.allergy.service.internal;


import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.entity.CisPatDrugAllergy;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.service.CisPatDrugAllergyService;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.service.internal.assembler.CisPatDrugAllergyAssembler;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.to.CisPatDrugAllergyEto;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.to.CisPatDrugAllergyNto;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.to.CisPatDrugAllergyQto;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.to.CisPatDrugAllergyTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("cis_nurse.allergy.service.CisPatDrugAllergyService")
@RequestMapping(value = "/api/cdr/allergy/cisPatDrugAllergy", produces = "application/json; charset=utf-8")
public class CisPatDrugAllergyServiceImpl implements CisPatDrugAllergyService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisPatDrugAllergyTo> getCisPatDrugAllergies(CisPatDrugAllergyQto cisPatDrugAllergyQto) {
        return CisPatDrugAllergyAssembler.toTos(CisPatDrugAllergy.getCisPatDrugAllergies(cisPatDrugAllergyQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisPatDrugAllergyTo> getCisPatDrugAllergyPage(CisPatDrugAllergyQto cisPatDrugAllergyQto) {
        Page<CisPatDrugAllergy> page = CisPatDrugAllergy.getCisPatDrugAllergyPage(cisPatDrugAllergyQto);
        Page<CisPatDrugAllergyTo> result = page.map(CisPatDrugAllergyAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisPatDrugAllergyTo getCisPatDrugAllergyById(String id) {
        return CisPatDrugAllergyAssembler.toTo(CisPatDrugAllergy.getCisPatDrugAllergyById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisPatDrugAllergyTo createCisPatDrugAllergy(CisPatDrugAllergyNto cisPatDrugAllergyNto) {
        CisPatDrugAllergy cisPatDrugAllergy = new CisPatDrugAllergy();
        cisPatDrugAllergy = cisPatDrugAllergy.create(cisPatDrugAllergyNto);
        return CisPatDrugAllergyAssembler.toTo(cisPatDrugAllergy);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisPatDrugAllergy(String id, CisPatDrugAllergyEto cisPatDrugAllergyEto) {
        Optional<CisPatDrugAllergy> cisPatDrugAllergyOptional = CisPatDrugAllergy.getCisPatDrugAllergyById(id);
        cisPatDrugAllergyOptional.ifPresent(cisPatDrugAllergy -> cisPatDrugAllergy.update(cisPatDrugAllergyEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisPatDrugAllergy(String id) {
        Optional<CisPatDrugAllergy> cisPatDrugAllergyOptional = CisPatDrugAllergy.getCisPatDrugAllergyById(id);
        cisPatDrugAllergyOptional.ifPresent(cisPatDrugAllergy -> cisPatDrugAllergy.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}