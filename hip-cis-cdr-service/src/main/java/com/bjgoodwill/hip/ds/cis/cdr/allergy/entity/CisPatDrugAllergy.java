package com.bjgoodwill.hip.ds.cis.cdr.allergy.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.enmus.RecordTypeEnum;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.repository.CisPatDrugAllergyRepository;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.to.CisPatDrugAllergyEto;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.to.CisPatDrugAllergyNto;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.to.CisPatDrugAllergyQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "患者药品过敏记录")
@Table(name = "cis_pat_drug_allergy", indexes = {}, uniqueConstraints = {})
public class CisPatDrugAllergy {

    // 标识
    private String id;
    // 主索引编码
    private String patMiCode;
    // 就诊流水号
    private String visitCode;
    // 患者类型
    private VisitTypeEnum visitType;
    // 记录类型：ast皮试，adv药品不良事件
    private RecordTypeEnum recordType;
    // 药理归类：字典pharmacologyClass
    private String actionType;
    // 商品编码
    private String drugCode;
    // 商品名称
    private String drugName;
    // 批号
    private String batchNo;
    // 医嘱ID或药品不良事件ID
    private String recordId;
    // 逻辑删除标记
    private boolean deleted;
    // 创建的人员
    private String createdStaff;
    // 创建的人员姓名
    private String createdStaffName;
    // 创建的时间
    private LocalDateTime createdDate;
    // 最后修改的人员
    private String updatedStaff;
    // 最后修改的人员姓名
    private String updatedStaffName;
    // 最后修改的时间
    private LocalDateTime updatedDate;

    public static Optional<CisPatDrugAllergy> getCisPatDrugAllergyById(String id) {
        return dao().findById(id);
    }

    public static List<CisPatDrugAllergy> getCisPatDrugAllergies(CisPatDrugAllergyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisPatDrugAllergy> getCisPatDrugAllergyPage(CisPatDrugAllergyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisPatDrugAllergy> getSpecification(CisPatDrugAllergyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (qto.getRecordType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("recordType"), qto.getRecordType()));
            }
            if (StringUtils.isNotBlank(qto.getActionType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("actionType"), qto.getActionType()));
            }
            if (StringUtils.isNotBlank(qto.getDrugCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugCode"), qto.getDrugCode()));
            }
            if (StringUtils.isNotBlank(qto.getDrugName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugName"), qto.getDrugName()));
            }
            if (StringUtils.isNotBlank(qto.getBatchNo())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("batchNo"), qto.getBatchNo()));
            }
            if (StringUtils.isNotBlank(qto.getRecordId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("recordId"), qto.getRecordId()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            return predicate;
        };
    }

    private static CisPatDrugAllergyRepository dao() {
        return SpringUtil.getBean(CisPatDrugAllergyRepository.class);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("主索引编码")
    @Column(name = "pat_mi_code", nullable = false)
    public String getPatMiCode() {
        return patMiCode;
    }

    protected void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = false)
    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    @Enumerated(EnumType.STRING)
    @Comment("患者类型")
    @Column(name = "visit_type", nullable = true)
    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    protected void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    @Enumerated(EnumType.STRING)
    @Comment("记录类型：ast皮试，adv药品不良事件")
    @Column(name = "record_type", nullable = true)
    public RecordTypeEnum getRecordType() {
        return recordType;
    }

    protected void setRecordType(RecordTypeEnum recordType) {
        this.recordType = recordType;
    }

    @Comment("药理归类：字典pharmacologyClass")
    @Column(name = "action_type", nullable = true)
    public String getActionType() {
        return actionType;
    }

    protected void setActionType(String actionType) {
        this.actionType = actionType;
    }

    @Comment("商品编码")
    @Column(name = "drug_code", nullable = false)
    public String getDrugCode() {
        return drugCode;
    }

    protected void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    @Comment("商品名称")
    @Column(name = "drug_name", nullable = true)
    public String getDrugName() {
        return drugName;
    }

    protected void setDrugName(String drugName) {
        this.drugName = drugName;
    }

    @Comment("批号")
    @Column(name = "batch_no", nullable = true)
    public String getBatchNo() {
        return batchNo;
    }

    protected void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    @Comment("医嘱ID或药品不良事件ID")
    @Column(name = "record_id", nullable = true)
    public String getRecordId() {
        return recordId;
    }

    protected void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisPatDrugAllergy other = (CisPatDrugAllergy) obj;
        return Objects.equals(id, other.id);
    }

    public CisPatDrugAllergy create(CisPatDrugAllergyNto cisPatDrugAllergyNto) {
        Assert.notNull(cisPatDrugAllergyNto, "参数cisPatDrugAllergyNto不能为空！");

        setId(cisPatDrugAllergyNto.getId());
        setPatMiCode(cisPatDrugAllergyNto.getPatMiCode());
        setVisitCode(cisPatDrugAllergyNto.getVisitCode());
        setVisitType(cisPatDrugAllergyNto.getVisitType());
        setRecordType(cisPatDrugAllergyNto.getRecordType());
        setActionType(cisPatDrugAllergyNto.getActionType());
        setDrugCode(cisPatDrugAllergyNto.getDrugCode());
        setDrugName(cisPatDrugAllergyNto.getDrugName());
        setBatchNo(cisPatDrugAllergyNto.getBatchNo());
        setRecordId(cisPatDrugAllergyNto.getRecordId());
        setDeleted(false);
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        dao().save(this);
        return this;
    }

    public void update(CisPatDrugAllergyEto cisPatDrugAllergyEto) {
        setPatMiCode(cisPatDrugAllergyEto.getPatMiCode());
        setVisitCode(cisPatDrugAllergyEto.getVisitCode());
        setVisitType(cisPatDrugAllergyEto.getVisitType());
        setRecordType(cisPatDrugAllergyEto.getRecordType());
        setActionType(cisPatDrugAllergyEto.getActionType());
        setDrugCode(cisPatDrugAllergyEto.getDrugCode());
        setDrugName(cisPatDrugAllergyEto.getDrugName());
        setBatchNo(cisPatDrugAllergyEto.getBatchNo());
        setRecordId(cisPatDrugAllergyEto.getRecordId());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
    }

    public void delete() {
        setDeleted(true);
    }

}
