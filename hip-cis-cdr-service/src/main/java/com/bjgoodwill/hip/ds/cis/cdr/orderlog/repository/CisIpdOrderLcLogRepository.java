package com.bjgoodwill.hip.ds.cis.cdr.orderlog.repository;

import com.bjgoodwill.hip.ds.cis.cdr.orderlog.entity.CisIpdOrderLcLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.orderlog.repository.CisIpdOrderLcLogRepository")
public interface CisIpdOrderLcLogRepository extends JpaRepository<CisIpdOrderLcLog, String>, JpaSpecificationExecutor<CisIpdOrderLcLog> {

}