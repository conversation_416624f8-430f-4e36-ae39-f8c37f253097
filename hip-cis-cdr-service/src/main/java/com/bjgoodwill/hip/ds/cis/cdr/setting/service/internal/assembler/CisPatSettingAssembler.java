package com.bjgoodwill.hip.ds.cis.cdr.setting.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cdr.setting.entity.CisPatSetting;
import com.bjgoodwill.hip.ds.cis.cdr.setting.to.CisPatSettingTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisPatSettingAssembler {

    public static List<CisPatSettingTo> toTos(List<CisPatSetting> cisPatSettings) {
        return toTos(cisPatSettings, false);
    }

    public static List<CisPatSettingTo> toTos(List<CisPatSetting> cisPatSettings, boolean withAllParts) {
        Assert.notNull(cisPatSettings, "参数cisPatSettings不能为空！");

        List<CisPatSettingTo> tos = new ArrayList<>();
        for (CisPatSetting cisPatSetting : cisPatSettings)
            tos.add(toTo(cisPatSetting, withAllParts));
        return tos;
    }

    public static CisPatSettingTo toTo(CisPatSetting cisPatSetting) {
        return toTo(cisPatSetting, false);
    }

    /**
     * @generated
     */
    public static CisPatSettingTo toTo(CisPatSetting cisPatSetting, boolean withAllParts) {
        if (cisPatSetting == null)
            return null;
        CisPatSettingTo to = new CisPatSettingTo();
        to.setId(cisPatSetting.getId());
        to.setOrderClass(cisPatSetting.getOrderClass());
        to.setValue(cisPatSetting.getValue());
        to.setDocCode(cisPatSetting.getDocCode());
        to.setOrgCode(cisPatSetting.getOrgCode());
        to.setCreatedStaff(cisPatSetting.getCreatedStaff());
        to.setCreatedStaffName(cisPatSetting.getCreatedStaffName());
        to.setCreatedDate(cisPatSetting.getCreatedDate());
        to.setUpdatedStaff(cisPatSetting.getUpdatedStaff());
        to.setUpdatedStaffName(cisPatSetting.getUpdatedStaffName());
        to.setUpdatedDate(cisPatSetting.getUpdatedDate());
        to.setVersion(cisPatSetting.getVersion());

        if (withAllParts) {
        }
        return to;
    }

}