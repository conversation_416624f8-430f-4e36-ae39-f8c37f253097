package com.bjgoodwill.hip.ds.cis.cdr.record.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisIpdDrugCdrOrderPlanRecordQto;
import com.bjgoodwill.hip.ds.cis.cdr.record.repository.CisCdrClinicIpdRecordRepository;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicIpdRecordEto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicIpdRecordNto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicIpdRecordQto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicRecordNto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "住院就诊记录")
@DiscriminatorValue("IPD")
public class CisCdrClinicIpdRecord extends CisCdrClinicRecord {

    @Comment("住院护理组")
    @Column(name = "clinic_nurse", nullable = true, length = 32)
    private String clinicNurse;


    @Comment("出院时间")
    @Column(name = "out_date", nullable = true)
    private LocalDateTime outDate;

    public static Optional<CisCdrClinicIpdRecord> getCisCdrClinicIpdRecordById(String id) {
        return dao().findById(id);
    }

    public static List<CisCdrClinicIpdRecord> getCisCdrClinicIpdRecords(CisCdrClinicIpdRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static List<CisCdrClinicIpdRecord> getCisCdrClinicIpdRecords(CisIpdDrugCdrOrderPlanRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }


    public static Page<CisCdrClinicIpdRecord> getCisCdrClinicIpdRecordPage(CisCdrClinicIpdRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static Page<CisCdrClinicIpdRecord> getCisCdrClinicIpdRecordPage(CisIpdDrugCdrOrderPlanRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisCdrClinicIpdRecord> getSpecification(CisCdrClinicIpdRecordQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();

            if (StringUtils.isNotBlank(qto.getPatCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patCode"), qto.getPatCode()));
            }

            if (qto.getStartDate() != null && qto.getEndDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("clinicStaff"), qto.getStartDate(), qto.getEndDate()));
            }
            return predicate;
        };
    }

    private static Specification<CisCdrClinicIpdRecord> getSpecification(CisIpdDrugCdrOrderPlanRecordQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();

            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getDrugGood())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugGood"), qto.getDrugGood()));
            }

            if (qto.getStartDate() != null && qto.getEndDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("clinicStaff"), qto.getStartDate(), qto.getEndDate()));
            }
            return predicate;
        };
    }

    private static CisCdrClinicIpdRecordRepository dao() {
        return SpringUtil.getBean(CisCdrClinicIpdRecordRepository.class);
    }

    public String getClinicNurse() {
        return clinicNurse;
    }

    protected void setClinicNurse(String clinicNurse) {
        this.clinicNurse = clinicNurse;
    }

    public LocalDateTime getOutDate() {
        return outDate;
    }

    protected void setOutDate(LocalDateTime outDate) {
        this.outDate = outDate;
    }

    @Override
    public CisCdrClinicRecord create(CisCdrClinicRecordNto cisCdrClinicRecordNto) {
        return create((CisCdrClinicIpdRecordNto) cisCdrClinicRecordNto);
    }

    public CisCdrClinicIpdRecord create(CisCdrClinicIpdRecordNto cisCdrClinicIpdRecordNto) {
        Assert.notNull(cisCdrClinicIpdRecordNto, "参数cisCdrClinicIpdRecordNto不能为空！");
        super.create(cisCdrClinicIpdRecordNto);

        setClinicNurse(cisCdrClinicIpdRecordNto.getClinicNurse());
        setOutDate(cisCdrClinicIpdRecordNto.getOutDate());
        dao().save(this);
        return this;
    }

    public void update(CisCdrClinicIpdRecordEto cisCdrClinicIpdRecordEto) {
        super.update(cisCdrClinicIpdRecordEto);
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
