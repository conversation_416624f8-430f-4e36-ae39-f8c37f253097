package com.bjgoodwill.hip.ds.cis.cdr.record.repository;

import com.bjgoodwill.hip.ds.cis.cdr.record.entity.CisCdrClinicIpdRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.cdr.record.repository.CisCdrClinicIpdRecordRepository")
public interface CisCdrClinicIpdRecordRepository extends JpaRepository<CisCdrClinicIpdRecord, String>, JpaSpecificationExecutor<CisCdrClinicIpdRecord> {

}