package com.bjgoodwill.hip.business.util.mq.enums;

/**
 * 消息发送、接收领域类型
 */
public enum MqDomainTypeEnum {
    ORG("org", "组织机构"),
    SECURITY("security", "安全"),
    NOTICE("notice", "公告通知"),
    PAT_INDEX("patIndex", "患者基本信息"),
    SERVICE_PRICE("servicePrice", "诊疗收费项目"),
    ECON_IPD_BILL("econIpdBill", "住院费用"),
    ECON_OPD_BILL("econOpdBill", "门诊费用"),
    MATE_PRICE("matePrice", "物资价表信息"),
    GOODS("goods", "药品信息"),
    INOUT("inout", "药品出入库"),
    STOCK("stock", "药品库存"),
    DRUG_PRICE("drugPrice", "药品调价"),
    IPD_CPOE("ipdCpoe", "住院医嘱"),
    OPD_CPOE("opdCpoe", "门诊医嘱"),
    CPOE("Cpoe", "所有医嘱"),
    APPLY("apply", "申请单"),
    PAT_APPLY("patApply", "住院申请"),
    IN_HOSPITAL("inHospital", "住院患者"),
    NEWBORN("newBorn", "新生儿登记"),
    OUT_PATIENT("outPatient", "门诊患者"),
    DRUG_IPD("drugIpd", "住院摆发药"),
    DRUG_OPD("drugOpd", "门诊摆发药"),
    DRUG_PLAN("drugPlan", "药品采购"),
    CLINICAL_DATA("clinicalData", "临床数据"),
    MI_BASE("miBase", "医保基础数据");


    private final String code;
    private final String name;

    MqDomainTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
