package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: hip-base
 * @author: xdguo
 * @create: 2025-04-01 10:45
 * @className: CisUsageTypeEnum
 * @description: 用法大类
 **/
public enum CisMedicationRoute {

    INHALATION("05", "吸入用药"),
    INJECTION("04", "注射用药"),
    SUBLINGUAL("03", "舌下用药"),
    RECTAL("02", "直肠用药"),
    ORAL("01", "口服");

    private final String code;
    private final String description;

    CisMedicationRoute(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 获取编码
    public String getCode() {
        return code;
    }

    // 获取用药方式描述
    public String getDescription() {
        return description;
    }

    // 通过编码查找枚举项
    public static CisMedicationRoute fromCode(String code) {
        for (CisMedicationRoute route : values()) {
            if (route.code.equals(code)) {
                return route;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return code + " " + description;
    }

}
