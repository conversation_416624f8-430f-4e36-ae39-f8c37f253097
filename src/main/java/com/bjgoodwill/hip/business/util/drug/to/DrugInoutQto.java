package com.bjgoodwill.hip.business.util.drug.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "药品出入库查询")
public class DrugInoutQto extends BaseQto implements Serializable {

    @NotBlank(message = "库房编码不能为空！")
    @Schema(description = "库房编码")
    private String storageCode;

    @Schema(description = "去向/来源科室")
    private String receiveOrg;

    @Schema(description = "入出方向")
    private String inoutType;

    @Schema(description = "入出类型")
    private String settingType;

    @Schema(description = "单号")
    private String orderCode;

    @Schema(description = "药品编码")
    private String drugGoodsCode;

    @NotNull(message = "开始时间不能为空！")
    @Schema(description = "开始时间")
    private LocalDateTime beginDate;

    @NotNull(message = "结束时间不能为空！")
    @Schema(description = "结束时间")
    private LocalDateTime endDate;

    @Schema(description = "主索引")
    private String patMiCode;

    @Schema(description = "患者姓名")
    private String patName;

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getPatName() {
        return patName;
    }

    public void setPatName(String patName) {
        this.patName = patName;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    public String getInoutType() {
        return inoutType;
    }

    public void setInoutType(String inoutType) {
        this.inoutType = inoutType;
    }

    public String getSettingType() {
        return settingType;
    }

    public void setSettingType(String settingType) {
        this.settingType = settingType;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public String getStorageCode() {
        return storageCode;
    }

    public void setStorageCode(String storageCode) {
        this.storageCode = storageCode;
    }

    public String getDrugGoodsCode() {
        return drugGoodsCode;
    }

    public void setDrugGoodsCode(String drugGoodsCode) {
        this.drugGoodsCode = drugGoodsCode;
    }

    public LocalDateTime getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDateTime beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }
}