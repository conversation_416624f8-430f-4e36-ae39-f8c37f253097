package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-03-13 11:12
 * @className: CisDividedRule
 * @description: 门诊处方分方规则
 **/
public enum CisDividedRuleEnum {
    ANTITRAINING("ANTITRAINING", "抗菌药"),
    TOXIPROPERTY("TOXIPROPERTY", "药理属性"),
    DRUGUSAGE("DRUGUSAGE", "药品用法归类"),
    PHARMACY("PHARMACY", "领药药房"),
    DRUGNUMS("DRUGNUMS", "药品数量"),
    DIAG("DIAG", "药品诊断");


    private String code;
    private String name;

    CisDividedRuleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static CisDividedRuleEnum getByCode(String code) {
        for (CisDividedRuleEnum cisDividedRule : CisDividedRuleEnum.values()) {
            if (cisDividedRule.getCode().equals(code)) {
                return cisDividedRule;
            }
        }
        return null;
    }

}
