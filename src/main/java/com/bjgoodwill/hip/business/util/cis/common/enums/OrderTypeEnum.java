package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-06-24 15:09
 * @className: OrderTypeEnum
 * @description: 医嘱类型
 **/
public enum OrderTypeEnum {

    // 1-长期医嘱 2-临时医嘱
    LONG_TERM_ORDER("1", "长期医嘱"),
    TEMPORARY_ORDER("2", "临时医嘱");

    private String code;
    private String name;

    OrderTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}