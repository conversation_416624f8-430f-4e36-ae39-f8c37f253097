package com.bjgoodwill.hip.business.util.cis.validation;

import java.lang.annotation.*;

/**
 * @program: HIP5.0-CIS
 * @author: yanht
 * @create: 2024-9-20
 * @className: ConServiceClinicItem
 * @description:
 **/
@Documented
@Retention(RetentionPolicy.RUNTIME)// 注解会在class字节码文件中存在，在运行时可以通过反射获取到
@Target({ElementType.METHOD, ElementType.PARAMETER})//@Target：定义注解的作用目标，方法和方法参数
@Inherited//说明子类可以继承父类中的该注解
public @interface ConServiceClinicItem {
}
