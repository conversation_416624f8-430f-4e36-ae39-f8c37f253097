package com.bjgoodwill.hip.business.util.drug.enums;

import com.bjgoodwill.hip.common.bean.EnumTo;

import java.util.ArrayList;
import java.util.List;


/**
 * 药品出入库查询类型枚举
 *
 * <AUTHOR>
 */
public enum DrugInoutQueryTypeEnum {

    入库("IN"),
    出库("OUT"),
    门诊发药("OPD"),
    住院发药("IPD");

    private final String code;

    DrugInoutQueryTypeEnum(String code) {
        this.code = code;
    }

    public static boolean validate(String code) {
        return DrugInoutQueryTypeEnum.getEnum(code) != null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getName(String code) {
        for (DrugInoutQueryTypeEnum enumItem : DrugInoutQueryTypeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem.name();
            }
        }
        return null;
    }

    public static DrugInoutQueryTypeEnum getEnum(String code) {
        for (DrugInoutQueryTypeEnum enumItem : DrugInoutQueryTypeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 获取List列表
     */
    public static List<EnumTo<String>> getList() {
        List<EnumTo<String>> list = new ArrayList<>();
        EnumTo<String> enumTo;
        for (DrugInoutQueryTypeEnum enumItem : DrugInoutQueryTypeEnum.values()) {
            enumTo = new EnumTo<>();
            enumTo.setCode(enumItem.getCode());
            enumTo.setName(enumItem.name());
            list.add(enumTo);
        }
        return list;
    }

    /**
     * 获取出入List列表
     */
    public static List<EnumTo<String>> getStorageList() {
        List<EnumTo<String>> list = new ArrayList<>();
        EnumTo<String> enumTo;
        for (DrugInoutQueryTypeEnum enumItem : DrugInoutQueryTypeEnum.values()) {
            if (enumItem.equals(DrugInoutQueryTypeEnum.入库) || enumItem.equals(DrugInoutQueryTypeEnum.出库)) {
                enumTo = new EnumTo<>();
                enumTo.setCode(enumItem.getCode());
                enumTo.setName(enumItem.name());
                list.add(enumTo);
            }
        }
        return list;
    }

    public String getCode() {
        return code;
    }

}