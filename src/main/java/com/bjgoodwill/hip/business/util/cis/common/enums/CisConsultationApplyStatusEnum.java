package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * <AUTHOR>
 * @Description 会诊申请单状态
 * @date 2024/12/19 9:39
 */
public enum CisConsultationApplyStatusEnum {

    NO_RECEIVE("NO_RECEIVE", "未接收"),
    RECEIVE("RECEIVE", "接收"),
    COMPLETED("COMPLETED", "已完成");

    private String code;
    private String name;


    CisConsultationApplyStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
