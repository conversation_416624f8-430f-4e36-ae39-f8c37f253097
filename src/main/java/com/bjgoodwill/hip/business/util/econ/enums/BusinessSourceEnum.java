package com.bjgoodwill.hip.business.util.econ.enums;

/**
 * 业务来源渠道
 */
public enum BusinessSourceEnum {
    HOS("HOS", "院内"),
    SELF("SELF", "自助机"),
    CLINIC("CLINIC", "诊间结算"),
    OFFICIAL_ACCT("OFFICIAL_ACCT ", "公众号"),
    APP("APP", "小程序");

    private final String code;
    private final String name;

    BusinessSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (BusinessSourceEnum value : BusinessSourceEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}