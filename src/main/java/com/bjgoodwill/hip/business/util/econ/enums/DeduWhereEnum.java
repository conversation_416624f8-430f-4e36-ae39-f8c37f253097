package com.bjgoodwill.hip.business.util.econ.enums;

import java.util.ArrayList;
import java.util.List;

public enum DeduWhereEnum {

    IDENTITY("IDENTITY", "身份", "VALUE", DeduLimitEnum.ALL),

    POOR("POOR", "贫困类型", "VALUE", DeduLimitEnum.ALL),

    SEX("SEX", "性别", "VALUE", DeduLimitEnum.ALL),

    VISIT("VISIT", "就诊类型", "VALUE", DeduLimitEnum.OPD),

    AGE("AGE", "年龄", "RANGE", DeduLimitEnum.OPD),

    MMDD("MM-DD", "月-日", "RANGE", DeduLimitEnum.OPD),

    HHMM("HH-MM", "小时", "RANGE", DeduLimitEnum.OPD),

    YYYYMMDD("YYYY-MM-DD", "日期", "RANGE", DeduLimitEnum.OPD);

    private final String code;
    private final String name;
    private final String type;
    private final DeduLimitEnum limit;

    DeduWhereEnum(String code, String name, String type, DeduLimitEnum limit) {
        this.code = code;
        this.name = name;
        this.type = type;
        this.limit = limit;
    }

    public static String getName(String code) {
        for (DeduWhereEnum value : DeduWhereEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public static List<DeduWhereEnum> getLimitDeduWhere(DeduLimitEnum limit) {
        List<DeduWhereEnum> deduWhereEnumList = new ArrayList<>();
        for (DeduWhereEnum value : DeduWhereEnum.values()) {
            if (value.getLimit().equals(limit) || DeduLimitEnum.ALL.equals(limit)) {
                deduWhereEnumList.add(value);
            }
        }
        return deduWhereEnumList;
    }

    public static boolean isConform(DeduLimitEnum limitEnum, DeduWhereEnum whereEnum) {
        if (whereEnum.getLimit().equals(limitEnum) || DeduLimitEnum.ALL.equals(whereEnum.getLimit())) {
            return true;
        } else {
            return false;
        }
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public DeduLimitEnum getLimit() {
        return limit;
    }
}