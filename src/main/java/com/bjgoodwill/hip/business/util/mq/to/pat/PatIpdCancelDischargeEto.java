package com.bjgoodwill.hip.business.util.mq.to.pat;

import com.bjgoodwill.hip.business.util.common.to.BaseTo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Schema(description = "住院患者-取消出院Eto")
public class PatIpdCancelDischargeEto extends BaseTo {

    @Schema(description = "患者流水号")
    @NotBlank(message = "患者流水号不能为空！")
    private String visitCode;
    @Schema(description = "当前床位")
    @NotBlank(message = "不能为空！")
    private String bedId;
    @Schema(description = "当前床位名称")
    @NotBlank(message = "不能为空！")
    private String bedName;
    @Schema(description = "当前住院医师")
    @NotBlank(message = "不能为空！")
    private String admittedDoctor;
    @Schema(description = "当前住院医师名称")
    @NotBlank(message = "不能为空！")
    private String admittedDoctorName;
    @Schema(description = "当前主治医师")
    @NotBlank(message = "不能为空！")
    private String masterDoctor;
    @Schema(description = "当前主治医师名称")
    @NotBlank(message = "不能为空！")
    private String masterDoctorName;
    @Schema(description = "当前主任医师")
    @NotBlank(message = "不能为空！")
    private String directorDoctor;
    @Schema(description = "当前主任医师名称")
    @NotBlank(message = "不能为空！")
    private String directorDoctorName;
    @Schema(description = "当前责任护士")
    @NotBlank(message = "不能为空！")
    private String masterNurse;
    @Schema(description = "当前责任护士名称")
    @NotBlank(message = "不能为空！")
    private String masterNurseName;
    @Schema(description = "当前登录科室")
    @NotNull(message = "不能为空！")
    private String loginDeptCode;

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getBedId() {
        return bedId;
    }

    public void setBedId(String bedId) {
        this.bedId = bedId;
    }

    public String getBedName() {
        return bedName;
    }

    public void setBedName(String bedName) {
        this.bedName = bedName;
    }

    public String getAdmittedDoctor() {
        return admittedDoctor;
    }

    public void setAdmittedDoctor(String admittedDoctor) {
        this.admittedDoctor = admittedDoctor;
    }

    public String getAdmittedDoctorName() {
        return admittedDoctorName;
    }

    public void setAdmittedDoctorName(String admittedDoctorName) {
        this.admittedDoctorName = admittedDoctorName;
    }

    public String getMasterDoctor() {
        return masterDoctor;
    }

    public void setMasterDoctor(String masterDoctor) {
        this.masterDoctor = masterDoctor;
    }

    public String getMasterDoctorName() {
        return masterDoctorName;
    }

    public void setMasterDoctorName(String masterDoctorName) {
        this.masterDoctorName = masterDoctorName;
    }

    public String getDirectorDoctor() {
        return directorDoctor;
    }

    public void setDirectorDoctor(String directorDoctor) {
        this.directorDoctor = directorDoctor;
    }

    public String getDirectorDoctorName() {
        return directorDoctorName;
    }

    public void setDirectorDoctorName(String directorDoctorName) {
        this.directorDoctorName = directorDoctorName;
    }

    public String getMasterNurse() {
        return masterNurse;
    }

    public void setMasterNurse(String masterNurse) {
        this.masterNurse = masterNurse;
    }

    public String getMasterNurseName() {
        return masterNurseName;
    }

    public void setMasterNurseName(String masterNurseName) {
        this.masterNurseName = masterNurseName;
    }

    public String getLoginDeptCode() {
        return loginDeptCode;
    }

    public void setLoginDeptCode(String loginDeptCode) {
        this.loginDeptCode = loginDeptCode;
    }
}
