package com.bjgoodwill.hip.business.util.mq.conf.cis;

import org.springframework.context.annotation.Configuration;

/**
 * @program: HIP5.0-CIS
 * @author: yanht
 * @create: 2024-10-27
 * @className: CisCdrRabbitConfig
 * @description:
 **/
@Configuration
public class CisCdrRabbitConfig {

    public static final String CIS_CDR_SYNC_EXCHANGE = "cisCdrTopicExchange";

    public static final String CIS_CDR_ANTIMICROBIALS_SKIN_ROUTING_KEY = "cis.cdr.antimicrobials.skin.sync.routing.key";

    public static final String CIS_CDR_ANTIMICROBIALS_SKIN_QUEUE = "cis.cdr.antimicrobials.skin.sync.queue";

}