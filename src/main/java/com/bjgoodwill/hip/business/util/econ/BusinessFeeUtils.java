package com.bjgoodwill.hip.business.util.econ;

import com.bjgoodwill.hip.business.util.econ.enums.RoundedRuleEnum;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;

/**
 * 费用计算工具类
 *
 * @Author: lzh
 */
public class BusinessFeeUtils {
    /**
     * 系统经济计算应收
     *
     * @param price      价表单价(必填)
     * @param num        使用数量(必填,最小单位对应的数量)
     * @param packageNum 包装数(必填,药品材料传包装数,诊疗项目传1)
     * @param rounded    四舍五入规则(必填,查参数[Econ_Rounded])
     * @return
     */
    public static BigDecimal computerAmount(BigDecimal price, BigDecimal num, int packageNum, String rounded) {
        BigDecimal chargeAmount = BigDecimal.ZERO;
        if (StringUtils.isEmpty(rounded) || (!RoundedRuleEnum.HALF_UP.getCode().equals(rounded) && !RoundedRuleEnum.DOWN.getCode().equals(rounded))) {
            throw new RuntimeException("计算金额传入四舍五入规则不合法");
        }
        if (packageNum <= 0.0) {
            throw new RuntimeException("计算金额传入包装数必须大于0");
        }
        if (RoundedRuleEnum.HALF_UP.getCode().equals(rounded)) {
            chargeAmount = halfUpAmount(price, num, packageNum);
        } else if (RoundedRuleEnum.DOWN.getCode().equals(rounded)) {
            chargeAmount = downAmount(price, num, packageNum);
        }
        return chargeAmount;
    }

    private static BigDecimal halfUpAmount(BigDecimal price, BigDecimal useNum, int packageNum) {
        BigDecimal remainder = useNum.remainder(new BigDecimal(String.valueOf(packageNum)));
        BigDecimal packageNumDecimal = new BigDecimal(String.valueOf(packageNum));
        if (remainder.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal minPackageCharge = price.divide(packageNumDecimal, 6, BigDecimal.ROUND_HALF_UP);
            return minPackageCharge.multiply(useNum).setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            BigDecimal usePackageNum = useNum.divide(packageNumDecimal, 2, BigDecimal.ROUND_HALF_UP);
            return usePackageNum.multiply(price).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    private static BigDecimal downAmount(BigDecimal price, BigDecimal useNum, int packageNum) {
        BigDecimal remainder = useNum.remainder(new BigDecimal(String.valueOf(packageNum)));
        BigDecimal packageNumDecimal = new BigDecimal(String.valueOf(packageNum));
        if (remainder.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal minPackageCharge = price.divide(packageNumDecimal, 6, BigDecimal.ROUND_DOWN);
            return minPackageCharge.multiply(useNum).setScale(2, BigDecimal.ROUND_DOWN);
        } else {
            BigDecimal usePackageNum = useNum.divide(packageNumDecimal, 2, BigDecimal.ROUND_DOWN);
            return usePackageNum.multiply(price).setScale(2, BigDecimal.ROUND_DOWN);
        }
    }
}
