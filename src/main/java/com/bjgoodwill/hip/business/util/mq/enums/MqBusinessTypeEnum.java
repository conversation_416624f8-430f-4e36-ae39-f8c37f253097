package com.bjgoodwill.hip.business.util.mq.enums;

/**
 * 消息发送、接收业务类型
 */
public enum MqBusinessTypeEnum {
    STAFF("staff", "职工维护"),
    WORK_GROUP("workGroup", "工作组维护"),
    INFORM("inform", "通知发送"),
    ANNOUNCEMENT("announcement", "公告发送"),
    UPDATE_PAT_INDEX("updatePatIndex", "患者基本信息修改"),
    UPDATE_SERVICE_PRICE("updateServicePrice", "诊疗收费项目修改"),
    CHANGE_SERVICE_PRICE("changeServicePrice", "诊疗收费项目调价"),
    DELETE_IPD_AMOUNT("deleteIpdAmount", "取消登记删除余额表"),
    OUT_SETTLE("outSettle", "出院结算"),
    OUT_SETTLE_PAY("outSettlePay", "出院结算支付"),
    CANCEL_OUT_SETTLE("cancelOutSettle", "取消出院结算"),
    UPDATE_PRICE("updatePrice", "入库更新药品信息价格"),
    UPDATE_DRUG_PLAN("updateDrugPlan", "入库更新采购计划"),
    REDUNDANCY_DRUG("redundancyDrug", "同步药品信息到冗余表"),
    STOCK_APPLY_INSERT("stockApplyInsert", "插入药品预扣"),
    STOCK_APPLY_DELETE("stockApplyDelete", "删除药品预扣"),
    IPD_OUT("ipdOut", "住院发药扣库存"),
    IPD_IN("ipdIn", "住院退药还库存"),
    IPD_IN_CHANGE_PRICE("ipdInChangePrice", "退药调价插入盈亏"),
    OPD_OUT("opdOut", "门诊发药扣库存"),
    OPD_IN("opdIn", "门诊退药还库存"),
    DRUG_STOCK_HISTORY("drugStockHistory", "发药历史库存表同步"),
    SURCHANGE("surchange", "补费"),
    REFUND("refund", "退费"),
    RERUREDRUG("reruredrug", "药品回写"),
    EXECUTE("execute", "执行单执行"),
    CANCEL("cancel", "取消执行单"),
    SPLIT("split", "拆分执行单"),
    CHECK_TYPE("checkType","医嘱越级审批"),
    DISCHARGE("discharge", "更新患者为待出院"),
    CHANGEDEPT("changedept", "更新患者为待转科"),
    CBEDCARD("cbedcard", "创建床头卡"),
    DBEDCARD("dbedcard", "作废床头卡"),
    CANCELDEPT("canceldept", "取消转科"),
    CANCELDISCHARGE("canceldischarge", "取消出院"),
    EXECUTE_CHANGE_DEPT("EXECUTE_CHANGE_DEPT", "执行转科"),
    EXECUTE_DISCHARGE("EXECUTE_DISCHARGE", "执行出院"),
    PAT_INDEX_SYNC_APPLY("patIndexSyncApply", "患者主索引修改后同步住院申请相关表"),
    PAT_INDEX_SYNC_IN_HOSPITAL("patIndexSyncInHospital", "患者主索引修改后同步住院患者相关表"),
    PAT_INDEX_SYNC_NEWBORN("patIndexSyncNewBorn", "患者主索引修改后同步新生儿登记表"),
    PAT_INDEX_SYNC_DRUG_IPD("patIndexSyncDrugIpd", "患者主索引修改后同步住院发药相关表"),
    PAT_INDEX_SYNC_DRUG_OPD("patIndexSyncDrugOpd", "患者主索引修改后同步门诊发药相关表"),
    ANTIMICROBIALS_SKIN_EXEC_INSERT("antimicrobialsSkinExecInsert", "创建抗菌药皮试药执行记录"),
    MI_BASE_LISTCONT("miBaseListcont", "医保基础数据目录对照数据"),
    ECON_OPD_SETL("econOpdSetl", "门诊结算"),
    ECON_OPD_SETL_PAY("econOpdSetlPay", "门诊结算支付"),
    ECON_OPD_SETL_CANCEL("econOpdSetlCancel", "取消门结算"),
    ECON_OPD_REFUND_AUDIT("econOpdRefundAudit", "门诊退费审核"),
    ECON_OPD_REFUND("econOpdRefund", "门诊结算退费"),
    PAT_OPD_REGIST_SETL_PAY("patOpdRegistSetlPay", "门诊挂号结算"),
    MATERIAL_PRICE("materialPrice", "物资价表修改同步补费组套明细"),
    MATERIAL_NAME("materialName", "物资名称修改同步补费组套明细"),
    DRUG_PRICE("drugPrice", "药品信息价格修改同步补费组套明细"),
    DRUG_NAME("drugName", "药品信息名称修改同步补费组套明细"),
    APPLYDRUG("applydrug", "护士申请领药记录医嘱日志"),
    PHARMACIESSEND("pharmaciessend", "药房发药记录医嘱日志");


    private final String code;
    private final String name;

    MqBusinessTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
