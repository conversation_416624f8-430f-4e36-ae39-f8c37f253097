package com.bjgoodwill.hip.business.util.pat.age;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

@Service
public class PatAgeUtil {

    // 一天毫秒数
    public final static Long oneDayMillis = 86400000L;
    // 一小时毫秒数
    public final static Long oneHourMillis = 3600000L;
    // 一分钟毫秒数
    public final static Long oneMinuteMillis = 60000L;

    public static String getPatAge(LocalDateTime birthDate, LocalDateTime inDeptDate) {

        BusinessAssert.notNull(birthDate, "患者出生日期为空！！");
        BusinessAssert.notNull(birthDate, "患者入院时间为空！！");

        // 转换为Calendar
        Calendar birthdayCalendar = Calendar.getInstance();
        birthdayCalendar.setTime(Date.from(birthDate.atZone(ZoneId.systemDefault()).toInstant()));

        Calendar inDeptCalendar = Calendar.getInstance();
        inDeptCalendar.setTime(Date.from(inDeptDate.atZone(ZoneId.systemDefault()).toInstant()));

        long birthdayLong = birthdayCalendar.getTimeInMillis();
        long inDeptDateLong = inDeptCalendar.getTimeInMillis();
        Long dateDiffer = inDeptDateLong - birthdayLong;
        if (dateDiffer <= 0) {
            return "";
        }

        int birthyear = birthdayCalendar.get(Calendar.YEAR);
        int birthmonth = birthdayCalendar.get(Calendar.MONTH);
        int birthdate = birthdayCalendar.get(Calendar.DATE);
        int birthhour = birthdayCalendar.get(Calendar.HOUR_OF_DAY);
        int birthMinute = birthdayCalendar.get(Calendar.MINUTE);

        int nowyear = inDeptCalendar.get(Calendar.YEAR);
        int nowmonth = inDeptCalendar.get(Calendar.MONTH);
        int nowdate = inDeptCalendar.get(Calendar.DATE);
        int nowhour = inDeptCalendar.get(Calendar.HOUR_OF_DAY);
        int nowMinute = inDeptCalendar.get(Calendar.MINUTE);

        int ageyear = nowyear - birthyear;
        int agemonth = nowmonth - birthmonth;
        int agedate = nowdate - birthdate;
        int agehour = nowhour - birthhour;
        int ageMinute = nowMinute - birthMinute;

        int mArray[] = new int[]{31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
        // 判断闰年的情况 ，2月份有29天；
        if ((nowyear % 400 == 0) || ((nowyear % 100 != 0) && (nowyear % 4 == 0))) {
            mArray[1] = 29;
        }
        if (agedate < 0) {
            if (nowmonth == 0) {
                nowmonth = 1;
            }
            agedate = agedate + mArray[nowmonth - 1];
            agemonth = agemonth - 1;
        }
        if (agemonth < 0) {
            agemonth = agemonth + 12;
            ageyear = ageyear - 1;
        }
        if (ageyear >= 3) {
            return ageyear + "岁";
        } else if (ageyear >= 1 && agemonth >= 1) {
            return ageyear + "岁" + agemonth + "月";
        } else if (ageyear >= 1) {
            return ageyear + "岁";
        } else if (agemonth >= 1 && agedate >= 1) {
            return agemonth + "月" + agedate + "天";
        } else if (agemonth >= 1) {
            return agemonth + "月";
        } else if (agedate >= 28) {
            return agedate + "天";
        } else if (agedate >= 1 && agehour >= 1) {
            if (dateDiffer < oneDayMillis) {
                return agehour + "时";
            }
            return agedate + "天" + agehour + "时";
        } else if (agedate >= 1) {
            if (dateDiffer < oneHourMillis) {
                return ageMinute + 60 + "分";
            }
            if (dateDiffer < oneDayMillis) {
                return agehour + 24 + "时";
            }
            return agedate + "天";
        } else if (agehour >= 1 && ageMinute >= 1) {
            return agehour + "时" + ageMinute + "分";
        } else if (agehour >= 1) {
            if (dateDiffer <= oneHourMillis && ageMinute < 0) {
                return ageMinute + 60 + "分";
            }
            return agehour + "时";
        } else if (ageMinute >= 0) {
            return ageMinute + "分";
        } else {
            return "";
        }


    }

}
