package com.bjgoodwill.hip.business.util.cis.common.enums;


/**
 * 定义记录类型的枚举类，用于表示不同业务操作类型
 */
public enum RecordTypeEnum {

    // 创建新记录
    NEW("NEW", "创建"),
    // 上报记录
    REPORT("REPORT", "上报"),
    // 审核通过记录
    APPROVED("APPROVED", "审核通过"),
    // 打回记录，需要进一步修改或补充
    BACK("BACK", "打回"),
    // 作废记录，不再处理
    VOID("VOID", "作废"),

    // 其他记录类型，用于扩展
    FIRSTCORRECTION("FIRSTCORRECTION", "初次订正"),
    // 未知记录类型，用于处理未知或无效的记录类型
    AGAINCORRECTION("AGAINCORRECTION", "再次订正");


    // 类型代码，用于系统内部标识记录类型
    private String code;
    // 类型名称，用于用户界面显示
    private String name;

    /**
     * 构造函数，初始化RecordTypeEnum实例
     *
     * @param code 类型代码
     * @param name 类型名称
     */
    private RecordTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取类型代码
     *
     * @return 类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取类型名称
     *
     * @return 类型名称
     */
    public String getName() {
        return name;
    }
}
