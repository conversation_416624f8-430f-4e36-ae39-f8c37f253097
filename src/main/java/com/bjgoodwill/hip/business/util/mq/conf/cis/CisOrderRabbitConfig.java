package com.bjgoodwill.hip.business.util.mq.conf.cis;

import org.springframework.context.annotation.Configuration;

/**
 * @program: HIP5.0-CIS
 * @author: yanht
 * @create: 2024-10-24
 * @className: CisOrderRabbitConfig
 * @description:
 **/
@Configuration
public class CisOrderRabbitConfig {

    public static final String CIS_CPOE_SYNC_EXCHANGE = "cisCpoeTopicExchange";

    public static final String CIS_ORDER_SERVICECODE_UPDATE_KEY = "cis.order.serviceCode.sync.update.key";

    public static final String CIS_ORDER_SERVICECODE_UPDATE_QUEUE = "cis.order.serviceCode.sync.update.queue";

    public static final String CIS_IPD_ORDER_SPLIT_LOG_ROUTING_KEY = "cis.ipd.order.split.log.sync.routing.key";

    public static final String CIS_IPD_ORDER_SPLIT_LOG_QUEUE = "cis.ipd.order.split.log.sync.queue";

    public static final String CIS_IPD_ORDER_EXT_SAVE_ROUTING_KEY = "cis.ipd.order.ext.save.routing.key";

    public static final String CIS_IPD_ORDER_EXT_SAVE_QUEUE = "cis.ipd.order.ext.save.queue";

    public static final String CIS_IPD_ORDER_CHECK_TYPE_ROUTING_KEY = "cis.ipd.order.check.type.sync.routing.key";
    public static final String CIS_IPD_ORDER_CHECK_TYPE_QUEUE = "cis.ipd.order.check.type.sync.queue";

    public static final String CIS_ORDER_APPLYDRUG_LOG_ROUTING_KEY = "cis.order.applydrug.log.routing.key";
    public static final String CIS_ORDER_APPLYDRUG_LOG_QUEUE = "cis.order.applydrug.log.queue";

    public static final String CIS_ORDER_PHARMACIESSEND_LOG_ROUTING_KEY = "cis.order.pharmaciessend.log.routing.key";
    public static final String CIS_ORDER_PHARMACIESSEND_LOG_QUEUE = "cis.order.pharmaciessend.log.queue";

    public static final String CIS_OPD_ORDER_PHARMACIESSEND_LOG_ROUTING_KEY = "cis.opd.order.pharmaciessend.log.routing.key";
    public static final String CIS_OPD_ORDER_PHARMACIESSEND_LOG_QUEUE = "cis.opd.order.pharmaciessend.log.queue";

    public static final String CIS_OPD_ORDER_PAYMENT_LOG_ROUTING_KEY = "cis.opd.order.payment.log.routing.key";
    public static final String CIS_OPD_ORDER_PAYMENT_LOG_QUEUE = "cis.opd.order.payment.log.queue";

    public static final String CIS_IPD_ORDER_PAYMENT_LOG_ROUTING_KEY = "cis.ipd.order.payment.log.routing.key";
    public static final String CIS_IPD_ORDER_PAYMENT_LOG_QUEUE = "cis.ipd.order.payment.log.queue";

}