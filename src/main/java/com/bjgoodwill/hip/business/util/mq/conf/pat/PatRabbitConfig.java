package com.bjgoodwill.hip.business.util.mq.conf.pat;

import org.springframework.context.annotation.Configuration;

/**
 * @program: HIP5.0-PAT
 * @author: ch<PERSON><PERSON><PERSON>
 * @create: 2024-09-19 09:34
 * @className: PatRabbitConfig
 * @description:
 **/
@Configuration
public class PatRabbitConfig {
    //住院患者交换机
    public static final String PAT_IPD_SYNC_EXCHANGE = "pat.ipd.sync.exchange";
    //住院患者交换机
    public static final String PAT_OPD_SYNC_EXCHANGE = "pat.opd.sync.exchange";

    /**
     * 消费消息队列
     */
    //校对转科医嘱，修改住院患者状态待转科
    public static final String PAT_IPD_CHANGE_DEPT_ROUTING_KEY = "pat.ipd.changeDept.routingKey";
    //校对转科医嘱，修改住院患者状态待转科 队列
    public static final String PAT_IPD_CHANGE_DEPT_QUEUE = "pat.ipd.changeDept.queue";

    //校对出院医嘱，修改住院患者状态待出院
    public static final String PAT_IPD_DISCHARGE_ROUTING_KEY = "pat.ipd.discharge.routingKey";
    //校对出院医嘱，修改住院患者状态待出院 队列
    public static final String PAT_IPD_DISCHARGE_QUEUE = "pat.ipd.discharge.queue";

    //取消转科
    public static final String PAT_IPD_CANCEL_CHANGE_DEPT_ROUTING_KEY = "pat.ipd.cancel.changeDept.routingKey";
    //取消转科队列
    public static final String PAT_IPD_CANCEL_CHANGE_DEPT_QUEUE = "pat.ipd.cancel.changeDept.queue";

    //取消住院
    public static final String PAT_IPD_CANCEL_DISCHARGE_ROUTING_KEY = "pat.ipd.cancel.discharge.routingKey";
    //取消住院队列
    public static final String PAT_IPD_CANCEL_DISCHARGE_QUEUE = "pat.ipd.cancel.discharge.queue";

    //新增住院患者信息医嘱扩展表
    public static final String PAT_IPD_INSERT_INPATIENT_EXT_ROUTING_KEY = "pat.ipd.insert.inpatientExt.routingKey";
    //新增住院患者信息医嘱扩展表队列
    public static final String PAT_IPD_INSERT_INPATIENT_EXT_QUEUE = "pat.ipd.insert.inpatientExt.queue";

    //作废住院患者信息医嘱扩展表
    public static final String PAT_IPD_DELETE_INPATIENT_EXT_ROUTING_KEY = "pat.ipd.delete.inpatientExt.routingKey";
    //作废住院患者信息医嘱扩展表队列
    public static final String PAT_IPD_DELETE_INPATIENT_EXT_QUEUE = "pat.ipd.delete.inpatientExt.queue";

    //患者主索引变动后，同步修改住院申请表applyDS
    public static final String PAT_IPD_UPDATE_APPLY_DS_ROUTING_KEY = "pat.ipd.update.applyDS.routingKey";
    //患者主索引变动后，同步修改住院申请表applyDS队列
    public static final String PAT_IPD_UPDATE_APPLY_DS_QUEUE = "pat.ipd.update.applyDS.queue";

    //患者主索引变动后，同步修改住院患者/出院患者/关注患者表inHospitalDS
    public static final String PAT_IPD_UPDATE_INHOSPITAL_DS_ROUTING_KEY = "pat.ipd.update.inHospitalDS.routingKey";
    //患者主索引变动后，同步修改住院患者/出院患者/关注患者表inHospitalDS队列
    public static final String PAT_IPD_UPDATE_INHOSPITAL_DS_QUEUE = "pat.ipd.update.inHospitalDS.queue";

    //患者主索引变动后，同步新生儿登记队列
    public static final String PAT_IPD_UPDATE_NEWBORN_QUEUE = "pat.ipd.update.newBorn.queue";

    /**
     * 门诊患者
     ********************************************************************************************************************************/

    //门诊挂号结算支付成功消息接收
    public static final String PAT_OPD_REGIST_SETL_PAY_QUEUE = "pat.opd.regist.setl.pay.queue";
}