package com.bjgoodwill.hip.business.util.econ.validation;

import cn.hutool.core.util.IdcardUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jdk.jfr.Registered;
import org.springframework.util.ObjectUtils;

/**
 * Description:身份证号验证
 *
 * <AUTHOR>
 * &#064;date 2024/5/30 上午10:14
 */
@Registered
public class IDNoValidator implements ConstraintValidator<IDNo, String> {

    @Override
    public void initialize(IDNo constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }


    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (ObjectUtils.isEmpty(value)) {
            return true;
        }
        if (value.contains(" ")) {
            constraintValidatorContext.disableDefaultConstraintViolation();
            constraintValidatorContext.buildConstraintViolationWithTemplate("身份证号码格式错误：不能包含空格。").addConstraintViolation();
            return false;
        }

        return IdcardUtil.isValidCard(value);
    }
}
