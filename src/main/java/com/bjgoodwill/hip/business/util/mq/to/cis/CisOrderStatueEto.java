package com.bjgoodwill.hip.business.util.mq.to.cis;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-10-24 09:15
 * @className: CisOrderStatueEto
 * @description:
 **/
@Schema(description = "回写医嘱状态")
public class CisOrderStatueEto implements Serializable {
    @Serial
    private static final long serialVersionUID = -1290103657283437554L;
    private String orderId;
    private CisStatusEnum status;
    private String serviceItemCode;
    private String orgCode;
    private String orgName;

    public CisOrderStatueEto(String orderId, CisStatusEnum status, String orgCode, String orgName) {
        this.orderId = orderId;
        this.status = status;
        this.orgCode = orgCode;
        this.orgName = orgName;
    }

    public CisOrderStatueEto(String orderId, String serviceItemCode) {
        this.orderId = orderId;
        this.serviceItemCode = serviceItemCode;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public CisStatusEnum getStatus() {
        return status;
    }

    public void setStatus(CisStatusEnum status) {
        this.status = status;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}