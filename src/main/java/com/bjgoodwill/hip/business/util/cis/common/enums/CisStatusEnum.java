package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-06-20 16:59
 * @className: CisStatusEnum
 * @description:
 **/

/**
 * @program: HIP5.0
 * @author: xdguo
 * @create: 2024-04-15 16:53
 * @className: StatueEnum
 * @description: 医护数据状态
 **/
public enum CisStatusEnum {

    NEW("NEW", "新建"),
    ACTIVE("ACTIVE", "提交"),
    COMPLETED("COMPLETED", "完成"),
    OBSOLETE("OBSOLETE", "作废"),
    BACK("BACK", "回退"),
    PASS("PASS", "校对"),
    //    SUSPENDED("SUSPENDED","挂起"),
    VOID("VOID", "预作废"),
    STOP("STOP", "停止"),
    PRESTOP("PRESTOP", "预停止"),
    REGISTER("REGISTER", "登记(门诊)"),
    //    DISPENS("DISPENS", "配液"),
    CLONE("CLONE", "备份"),//申请单状态
    EXCUTE("EXCUTE", "执行"),//执行单状态,临时医嘱
    REJECT("REJECT", "不执行"),//执行单状态
    CANCELEXCUTE("CANCELEXCUTE", "取消执行")//执行单状态
    ;

    private String code;
    private String name;


    CisStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


}
