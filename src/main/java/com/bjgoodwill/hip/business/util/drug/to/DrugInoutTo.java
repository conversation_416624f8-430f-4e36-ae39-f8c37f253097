package com.bjgoodwill.hip.business.util.drug.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "药库入出库查询")
public class DrugInoutTo implements Serializable {


    @Schema(description = "库房编码")
    private String storageCode;

    @Schema(description = "库房名称")
    private String storageName;

    @Schema(description = "单号")
    private String orderCode;

    @Schema(description = "药品编码")
    private String drugGoodsCode;

    @Schema(description = "药品名称")
    private String drugGoodsName;

    @Schema(description = "药品规格")
    private String drugSpec;

    @Schema(description = "出入库数量")
    private BigDecimal quantity;

    @Schema(description = "出入库数量")
    private String quantityStr;

    @Schema(description = "大包装数量")
    private BigDecimal quantityMax;

    @Schema(description = "小包装数量")
    private BigDecimal quantityMin;

    @Schema(description = "结存数量")
    private BigDecimal historyQuantity;

    @Schema(description = "结存数量")
    private String historyQuantityStr;
    @Schema(description = "结存数量大包装数量")
    private BigDecimal historyQuantityMax;

    @Schema(description = "结存数量小包装数量")
    private BigDecimal historyQuantityMin;

    @Schema(description = "购入价")
    private BigDecimal purchasePrice;

    @Schema(description = "零售价")
    private BigDecimal salePrice;

    @Schema(description = "购入总额")
    private BigDecimal purchaseAmount;

    @Schema(description = "零售总额")
    private BigDecimal saleAmount;

    @Schema(description = "包装数")
    private Integer packageNum;

    @Schema(description = "有效期")
    private LocalDate effectiveDate;

    @Schema(description = "批号")
    private String batchNo;

    @Schema(description = "国药准字")
    private String approvalDoc;

    @Schema(description = "出入数据类型")
    private String inout;

    @Schema(description = "出入库类型")
    private String inoutType;

    @Schema(description = "出入库类型名称")
    private String inoutTypeName;

    @Schema(description = "出入库来源/去向")
    private String receiveOrg;

    @Schema(description = "出入库来源/去向名称")
    private String receiveOrgName;

    @Schema(description = "生产厂商")
    private String manufactureName;

    @Schema(description = "操作员")
    private String createdStaff;

    @Schema(description = "出入库时间")
    private LocalDateTime inoutDate;

    @Schema(description = "大单位")
    private String maxUnit;

    @Schema(description = "小单位")
    private String minUnit;

    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "患者姓名")
    private String patName;
    @Schema(description = "开立科室编码")
    private String createOrgCode;
    @Schema(description = "开立科室名称")
    private String createOrgName;
    @Schema(description = "开立医生编码")
    private String createDoctorCode;
    @Schema(description = "开立医生名称")
    private String createDoctorName;
    @Schema(description = "开立时间")
    private LocalDateTime createPresDate;

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getPatName() {
        return patName;
    }

    public void setPatName(String patName) {
        this.patName = patName;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public String getCreateOrgName() {
        return createOrgName;
    }

    public void setCreateOrgName(String createOrgName) {
        this.createOrgName = createOrgName;
    }

    public String getCreateDoctorCode() {
        return createDoctorCode;
    }

    public void setCreateDoctorCode(String createDoctorCode) {
        this.createDoctorCode = createDoctorCode;
    }

    public String getCreateDoctorName() {
        return createDoctorName;
    }

    public void setCreateDoctorName(String createDoctorName) {
        this.createDoctorName = createDoctorName;
    }

    public LocalDateTime getCreatePresDate() {
        return createPresDate;
    }

    public void setCreatePresDate(LocalDateTime createPresDate) {
        this.createPresDate = createPresDate;
    }

    public String getStorageCode() {
        return storageCode;
    }

    public void setStorageCode(String storageCode) {
        this.storageCode = storageCode;
    }

    public String getStorageName() {
        return storageName;
    }

    public void setStorageName(String storageName) {
        this.storageName = storageName;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public String getInout() {
        return inout;
    }

    public void setInout(String inout) {
        this.inout = inout;
    }

    public String getInoutType() {
        return inoutType;
    }

    public void setInoutType(String inoutType) {
        this.inoutType = inoutType;
    }

    public String getInoutTypeName() {
        return inoutTypeName;
    }

    public void setInoutTypeName(String inoutTypeName) {
        this.inoutTypeName = inoutTypeName;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getQuantityStr() {
        return quantityStr;
    }

    public void setQuantityStr(String quantityStr) {
        this.quantityStr = quantityStr;
    }

    public BigDecimal getQuantityMax() {
        return quantityMax;
    }

    public void setQuantityMax(BigDecimal quantityMax) {
        this.quantityMax = quantityMax;
    }

    public BigDecimal getQuantityMin() {
        return quantityMin;
    }

    public void setQuantityMin(BigDecimal quantityMin) {
        this.quantityMin = quantityMin;
    }

    public String getMaxUnit() {
        return maxUnit;
    }

    public void setMaxUnit(String maxUnit) {
        this.maxUnit = maxUnit;
    }

    public String getMinUnit() {
        return minUnit;
    }

    public void setMinUnit(String minUnit) {
        this.minUnit = minUnit;
    }

    public LocalDateTime getInoutDate() {
        return inoutDate;
    }

    public void setInoutDate(LocalDateTime inoutDate) {
        this.inoutDate = inoutDate;
    }

    public String getDrugGoodsCode() {
        return drugGoodsCode;
    }

    public void setDrugGoodsCode(String drugGoodsCode) {
        this.drugGoodsCode = drugGoodsCode;
    }

    public String getDrugGoodsName() {
        return drugGoodsName;
    }

    public void setDrugGoodsName(String drugGoodsName) {
        this.drugGoodsName = drugGoodsName;
    }

    public String getDrugSpec() {
        return drugSpec;
    }

    public void setDrugSpec(String drugSpec) {
        this.drugSpec = drugSpec;
    }

    public BigDecimal getHistoryQuantity() {
        return historyQuantity;
    }

    public void setHistoryQuantity(BigDecimal historyQuantity) {
        this.historyQuantity = historyQuantity;
    }

    public String getHistoryQuantityStr() {
        return historyQuantityStr;
    }

    public void setHistoryQuantityStr(String historyQuantityStr) {
        this.historyQuantityStr = historyQuantityStr;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getApprovalDoc() {
        return approvalDoc;
    }

    public void setApprovalDoc(String approvalDoc) {
        this.approvalDoc = approvalDoc;
    }

    public String getManufactureName() {
        return manufactureName;
    }

    public void setManufactureName(String manufactureName) {
        this.manufactureName = manufactureName;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public BigDecimal getHistoryQuantityMax() {
        return historyQuantityMax;
    }

    public void setHistoryQuantityMax(BigDecimal historyQuantityMax) {
        this.historyQuantityMax = historyQuantityMax;
    }

    public BigDecimal getHistoryQuantityMin() {
        return historyQuantityMin;
    }

    public void setHistoryQuantityMin(BigDecimal historyQuantityMin) {
        this.historyQuantityMin = historyQuantityMin;
    }

    public BigDecimal getPurchaseAmount() {
        return purchaseAmount;
    }

    public void setPurchaseAmount(BigDecimal purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    public BigDecimal getSaleAmount() {
        return saleAmount;
    }

    public void setSaleAmount(BigDecimal saleAmount) {
        this.saleAmount = saleAmount;
    }

    public Integer getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Integer packageNum) {
        this.packageNum = packageNum;
    }
}