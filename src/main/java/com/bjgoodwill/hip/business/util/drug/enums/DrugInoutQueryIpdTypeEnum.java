package com.bjgoodwill.hip.business.util.drug.enums;

import com.bjgoodwill.hip.common.bean.EnumTo;

import java.util.ArrayList;
import java.util.List;


/**
 * 住院发退药查询枚举
 *
 * <AUTHOR>
 */
public enum DrugInoutQueryIpdTypeEnum {

    住院发药("OUT"),
    住院退药("IN");

    private final String code;

    DrugInoutQueryIpdTypeEnum(String code) {
        this.code = code;
    }

    public static boolean validate(String code) {
        return DrugInoutQueryIpdTypeEnum.getEnum(code) != null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getName(String code) {
        for (DrugInoutQueryIpdTypeEnum enumItem : DrugInoutQueryIpdTypeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem.name();
            }
        }
        return null;
    }

    public static DrugInoutQueryIpdTypeEnum getEnum(String code) {
        for (DrugInoutQueryIpdTypeEnum enumItem : DrugInoutQueryIpdTypeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 获取List列表
     */
    public static List<EnumTo<String>> getList() {
        List<EnumTo<String>> list = new ArrayList<>();
        EnumTo<String> enumTo;
        for (DrugInoutQueryIpdTypeEnum enumItem : DrugInoutQueryIpdTypeEnum.values()) {
            enumTo = new EnumTo<>();
            enumTo.setCode(enumItem.getCode());
            enumTo.setName(enumItem.name());
            list.add(enumTo);
        }
        return list;
    }

    public String getCode() {
        return code;
    }

}