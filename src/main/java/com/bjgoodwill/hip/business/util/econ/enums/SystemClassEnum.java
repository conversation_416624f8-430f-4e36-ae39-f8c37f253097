package com.bjgoodwill.hip.business.util.econ.enums;

/**
 * 系统分类
 */
public enum SystemClassEnum {

    西药("01", "西药"),
    中成药("02", "中成药"),
    草药("03", "草药");

    private final String code;
    private final String name;

    SystemClassEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SystemClassEnum getEnum(String code) {
        for (SystemClassEnum value : SystemClassEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}