package com.bjgoodwill.hip.business.util.pat.enums;

import com.bjgoodwill.hip.common.bean.EnumTo;

import java.util.ArrayList;
import java.util.List;


/**
 * 患者相关编码枚举
 *
 * <AUTHOR>
 */
public enum PatCodeEnum {
    住院流水号("ipdVisitCode"),
    门诊流水号("opdVisitCode"),
    ;

    private final String code;

    PatCodeEnum(String code) {
        this.code = code;
    }

    public static boolean validate(String code) {
        return PatCodeEnum.getEnum(code) != null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getName(String code) {
        for (PatCodeEnum enumItem : PatCodeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem.name();
            }
        }
        return null;
    }

    public static PatCodeEnum getEnum(String code) {
        for (PatCodeEnum enumItem : PatCodeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 获取List列表
     */
    public static List<EnumTo<String>> getList() {
        List<EnumTo<String>> list = new ArrayList<>();
        EnumTo<String> enumTo;
        for (PatCodeEnum enumItem : PatCodeEnum.values()) {
            enumTo = new EnumTo<>();
            enumTo.setCode(enumItem.getCode());
            enumTo.setName(enumItem.name());
            list.add(enumTo);
        }
        return list;
    }

    public String getCode() {
        return code;
    }

}