package com.bjgoodwill.hip.business.util.drug.util;

import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class DrugAmountUtils {

    /**
     * 根据大包装数量、小包装数量、包装数计算小包装数量
     *
     * @param quantityPackage
     * @param quantityMin
     * @param packageNum
     * @return
     */
    public static BigDecimal getQuantity(BigDecimal quantityPackage, BigDecimal quantityMin, Integer packageNum) {

        //计算总数量，大包装数量*包装数+小包装数量
        BigDecimal quantity = NumberUtil.add(NumberUtil.mul(quantityPackage, packageNum), quantityMin);

        return quantity;
    }

    /**
     * 根据小包装数量计算大包装数
     *
     * @param quantity
     * @param packageNum
     * @return
     */
    public static BigDecimal getMax(BigDecimal quantity, Integer packageNum) {
        if (quantity == null) {
            return BigDecimal.ZERO;
        }
        //计算大包装数量
        BigDecimal quantityPackage = NumberUtil.div(quantity, packageNum, 0, RoundingMode.DOWN);
        return quantityPackage;
    }

    /**
     * 根据小包装数量计算大包装数后剩余小包装数
     *
     * @param quantity
     * @param packageNum
     * @return
     */
    public static BigDecimal getMin(BigDecimal quantity, Integer packageNum) {
        if (quantity == null) {
            return BigDecimal.ZERO;
        }
        //计算小包装数量
        BigDecimal quantityMin = quantity.remainder(BigDecimal.valueOf(packageNum));
        return quantityMin;
    }

    /**
     * 根据小包装数量、包装数、单价计算总金额
     *
     * @param quantity
     * @param packageNum
     * @param price
     * @return
     */
    public static BigDecimal getPriceAmount(BigDecimal quantity, Integer packageNum, BigDecimal price) {
        //计算金额
        BigDecimal priceAmount = NumberUtil.div(NumberUtil.mul(quantity, price), packageNum, 4);
        return priceAmount;
    }

    /**
     * 拼装大小包装数量和单位
     *
     * @param quantity
     * @param packageNum
     * @param maxUnit
     * @param minUnit
     * @return
     */
    public static String getQuantityAndUnit(BigDecimal quantity, Integer packageNum, String maxUnit, String minUnit) {
        if (quantity == null || BigDecimal.ZERO.compareTo(quantity) == 0) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        BigDecimal max = getMax(quantity, packageNum);
        if (BigDecimal.ZERO.compareTo(max) != 0) {
            sb.append(max).append(maxUnit);
        }
        BigDecimal min = getMin(quantity, packageNum);
        if (BigDecimal.ZERO.compareTo(min) != 0) {
            sb.append(min).append(minUnit);
        }
        return sb.toString();
    }

}
