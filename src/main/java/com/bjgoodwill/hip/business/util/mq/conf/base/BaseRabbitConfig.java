package com.bjgoodwill.hip.business.util.mq.conf.base;

import org.springframework.context.annotation.Configuration;

/**
 * @Author: lzh
 */
@Configuration
public class BaseRabbitConfig {
    //基础业务交换机,发送方定义
    public static final String BASE_SYNC_EXCHANGE = "base.sync.exchange";

    /**
     * 消息识别key,发送方定义
     */
    //患者基本信息修改
    public static final String BASE_PAT_INDEX_UPDATE_ROUTING_KEY = "base.patIndex.update.routingKey";
    //诊疗收费项目修改
    public static final String BASE_SERVICE_PRICE_UPDATE_ROUTING_KEY = "base.servicePrice.update.routingKey";
    //诊疗收费项目调价
    public static final String BASE_SERVICE_PRICE_CHANGE_PRICE_ROUTING_KEY = "base.servicePrice.changePrice.routingKey";

    //物资价表 价格更新，发消息给经济补费组套明细，更新价格
    public static final String BASE_MATERIAL_CHANGE_PRICE_ROUTING_KEY = "base.material.changePrice.routingKey";
    //物资价表 名称更新，发消息给经济补费组套明细，更新名称
    public static final String BASE_MATERIAL_CHANGE_NAME_ROUTING_KEY = "base.material.changeName.routingKey";
    //药品信息 价格更新，发消息给经济补费组套明细，更新价格
    public static final String BASE_DRUG_CHANGE_PRICE_ROUTING_KEY = "base.drug.changePrice.routingKey";
    //药品信息 名称更新，发消息给经济补费组套明细，更新名称
    public static final String BASE_DRUG_CHANGE_NAME_ROUTING_KEY = "base.drug.changeName.routingKey";

    //经济补费组套 价格更新消息队列
    public static final String BASE_ECON_TEMPLATE_CHANGE_PRICE_QUEUE = "base.econtemplate.change.price.queue";
    //经济补费组套 名称更新消息队列
    public static final String BASE_ECON_TEMPLATE_CHANGE_NAME_QUEUE = "base.econtemplate.change.name.queue";
}