package com.bjgoodwill.hip.business.util.cis.common.enums;

public enum ExecLogEnum {
    CREATE_ORDER("开立医嘱"),
    SUBMIT_ORDER("提交医嘱"),
    PROOF_ORDER("校对医嘱"),
    DELETE_ORDER("删除医嘱"),
    BACK_ORDER("回退医嘱"),
    SPLIT_ORDER("拆分医嘱"),
    EXEC_ORDER("执行医嘱"),
    CANCEL_EXEC_ORDER("取消执行医嘱"),
    STOP_ORDER("停止医嘱"),
    PRE_STOP_ORDER("预停止医嘱"),
    SUSPENDED_ORDER("挂起医嘱"),
    CANCEL_SUSPENDED_ORDER("取消挂起医嘱"),
    OBSOLETE_ORDER("作废医嘱"),
    WITHDRAW("医生撤回"),
    STOP_CONFIRM("停止确认"),
    CNSLT_RECEIVE("会诊接收"),
    SPLIT_GROUP("拆组"),
    MERGE_GROUP("并组"),
    UPDATE_ORDER("修改"),
    OVERAPPLY("越级申请"),
    CHECK_TYPE("越级审核通过"),
    SKIN_RESULTS("皮试结果"),
    THIRDSTATUE("三方状态"),
    JUDGE("药师审方"),
    APPLYDRUG("护士申请领药"),
    PHARMACIESSEND("药房发药"),
    PHARMACIES_REFUND("药房退药"),
    PHARMACIES_REFUND_CANCEL("药房退药撤销"),
    PAYMENT_ORDER("缴费医嘱"),
    REFUND_ORDER("退费医嘱");


    private String name;

    ExecLogEnum(String name) {
        this.name = name;
    }

    ExecLogEnum() {

    }

    public String getName() {
        return name;
    }
}