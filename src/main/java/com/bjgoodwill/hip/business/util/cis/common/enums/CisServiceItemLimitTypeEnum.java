package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: HIP5.0-BASE
 * @author: xdguo
 * @create: 2024-10-11 13:34
 * @className: CisServiceItemLimitType
 * @description:
 **/
public enum CisServiceItemLimitTypeEnum {

    // 1-长期医嘱 2-临时医嘱
    LONG("1", "长期医嘱"),
    TEMPORARY("2", "临时医嘱"),
    ALL("3", "所有医嘱");

    private String code;
    private String name;

    CisServiceItemLimitTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}