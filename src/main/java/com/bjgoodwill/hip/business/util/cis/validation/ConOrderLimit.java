package com.bjgoodwill.hip.business.util.cis.validation;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-08-12 17:28
 * @className: ConOrderLimit
 * @description:
 **/

import java.lang.annotation.*;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-08-12 16:38
 * @className: ConOrderLimit
 * @description:
 **/
@Documented
@Retention(RetentionPolicy.RUNTIME)// 注解会在class字节码文件中存在，在运行时可以通过反射获取到
@Target({ElementType.METHOD, ElementType.PARAMETER})//@Target：定义注解的作用目标，方法和方法参数
@Inherited//说明子类可以继承父类中的该注解
public @interface ConOrderLimit {
}
