package com.bjgoodwill.hip.business.util.mq.to.pat;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.io.Serializable;
import java.util.List;

@Schema(description = "住院患者扩展医嘱信息新增")
public class PatIpdInpatientExtListNto implements Serializable {

    @Schema(description = "住院流水号")
    @NotBlank(message = "住院流水号！")
    private String visitCode;

    @Schema(description = "医嘱类型")
    @NotBlank(message = "医嘱类型不能为空！")
    private List<PatIpdInpatientExtMqNto> patIpdInpatientExtMqNto;

    @Schema(description = "登录人")
    private String staffId;

    @Schema(description = "登录人姓名")
    private String staffName;

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public List<PatIpdInpatientExtMqNto> getPatIpdInpatientExtMqNto() {
        return patIpdInpatientExtMqNto;
    }

    public void setPatIpdInpatientExtMqNto(List<PatIpdInpatientExtMqNto> patIpdInpatientExtMqNto) {
        this.patIpdInpatientExtMqNto = patIpdInpatientExtMqNto;
    }

    public String getStaffId() {
        return staffId;
    }

    public void setStaffId(String staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }
}