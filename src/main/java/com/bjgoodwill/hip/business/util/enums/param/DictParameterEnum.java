package com.bjgoodwill.hip.business.util.enums.param;

/**
 * 参数维护
 */
public enum DictParameterEnum {
    /**
     * 经济参数
     */
    担保金顺序("ECON", "担保金顺序", "GuaranteLevel", "HOS全院 | ORG病区 | PAT个人 | FEE费别,按照顺序竖线分割"),
    计费四舍五入规则("ECON", "计费四舍五入规则", "RoundedRule", "HALF_UP保留两位小数并四舍五入 , DOWN保留两位小数后全舍"),
    门诊结算支付方式("ECON", "门诊结算支付方式", "OpdSetlPayWay", "Acct-账户|Cash-现金|WeChat-微信支付|Alipay-支付宝"),
    门诊退费申请是否只能退当前登录科室的("ECON", "门诊医生站退费申请权限)", "OpdRefundApply", "1是，0否"),

    /**
     * 患者参数
     */
    住院标识产科医生工作组编码("PAT", "住院标识产科医生工作组编码", "PatNewBornDocWorkGroupCode", "维护住院产科医生工作组编码（多条|分隔）不使用病区是为了预防借床患者查不到情况"),

    /**
     * 药品参数
     */
    库房出库分配药品库存顺序("DRUG", "库房出库分配药品库存顺序", "DrugOutType", "近效期先出NearEffective,先入先出FirstInFirstOut，默认NearEffective "),
    摆药单维护类型("DRUG", "摆药单维护类型", "DrugDispenseType", "剂型drugForm,用法usage，默认drugForm"),
    门诊是否走配药流程("DRUG", "门诊是否走配药流程", "DrugOpdMatch", "true是，false否。默认false"),
    门诊药品处方有效期天数("DRUG", "门诊药品处方有效期天数", "DrugPrescriptionDays", "默认1天"),
    门诊发药毒麻代办人弹框参数("DRUG", "门诊发药毒麻代办人弹框参数", "DrugOpdAgentAlert",
            "门诊发药时判断关于毒麻代办人页面弹出情况参数：0 不弹出。 1 弹出判断必填，此时取消页面不继续发药。2 弹出不判断必填，点击继续，继续发药，点击取消，取消发药。"),

    /**
     * JSON参数
     */
    门诊时段("JSON", "门诊时段", "002", "json格式:门诊排班时段是否启用参数以及参数值"),
    ;

    private final String type;
    private final String name;
    private final String code;
    private final String remark;

    DictParameterEnum(String type, String name, String code, String remark) {
        this.type = type;
        this.name = name;
        this.code = code;
        this.remark = remark;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getRemark() {
        return remark;
    }

}