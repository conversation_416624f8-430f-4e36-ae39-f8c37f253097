package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-06-28 14:45
 * @className: VisitTypeEnum
 * @description:
 **/
public enum VisitTypeEnum {
    IPD("IPD", "住院"),
    OPD("OPD", "门诊");

    private String code;
    private String name;

    VisitTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SystemTypeEnum getValue(String code) {
        for (SystemTypeEnum value : SystemTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
