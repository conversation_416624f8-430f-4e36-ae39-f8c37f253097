package com.bjgoodwill.hip.business.util.enums.role;

import com.bjgoodwill.hip.common.bean.EnumTo;

import java.util.ArrayList;
import java.util.List;

/**
 * 内置角色枚举
 */
public enum BuiltInRolesEnum {

    住院护士长授权("InpatientHeadNurseAuth"),
    收费员("TollCollectors");

    private final String code;

    public String getCode() {
        return code;
    }

    BuiltInRolesEnum(String code) {
        this.code = code;
    }

    /**
     * 根据编码获取名称
     * @param code 编码
     * @return 名称
     */
    public static String getName(String code) {
        for (BuiltInRolesEnum enumItem : BuiltInRolesEnum.values()) {
            if(enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem.name();
            }
        }
        return null;
    }

    public static BuiltInRolesEnum getEnum(String code) {
        for (BuiltInRolesEnum enumItem : BuiltInRolesEnum.values()) {
            if(enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 获取List列表
     */
    public static List<EnumTo<?>> getList() {
        List<EnumTo<?>> list = new ArrayList<>();
        EnumTo<String> enumTo;
        for (BuiltInRolesEnum enumItem : BuiltInRolesEnum.values()) {
            enumTo = new EnumTo<>();
            enumTo.setCode(enumItem.getCode());
            enumTo.setName(enumItem.name());
            list.add(enumTo);
        }
        return list;
    }
}
