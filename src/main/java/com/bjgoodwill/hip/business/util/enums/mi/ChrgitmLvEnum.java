package com.bjgoodwill.hip.business.util.enums.mi;

/**
 * 收费项目等级
 */
public enum ChrgitmLvEnum {

    甲类("01", "甲类"),
    乙类("02", "乙类"),
    丙类("03", "丙类");

    private final String code;
    private final String name;

    ChrgitmLvEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (ChrgitmLvEnum value : ChrgitmLvEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public static ChrgitmLvEnum getValue(String code) {
        for (ChrgitmLvEnum value : ChrgitmLvEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}