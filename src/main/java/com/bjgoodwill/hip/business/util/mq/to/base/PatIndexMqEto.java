package com.bjgoodwill.hip.business.util.mq.to.base;

import java.time.LocalDateTime;

/**
 * 患者基本信息改动消息实体
 *
 * @Author: lzh
 */
public class PatIndexMqEto {
    // 索引编码
    private String indexCode;
    // 患者编码
    private String code;
    // 平台主索引
    private String epmi;
    // 姓名
    private String name;
    // 性别
    private String sex;
    // 性别名称
    private String sexName;
    // 证件类型
    private String cardType;
    // 证件类型名称
    private String cardTypeName;
    // 证件号
    private String cardCode;
    // 出生日期
    private LocalDateTime birthDate;
    // 费别
    private String feeType;
    // 费别名称
    private String feeTypeName;
    // 身份
    private String identity;
    // 身份名称
    private String identityName;
    // 重点人群分类
    private String focusGroupsType;
    // 重点人群分类名称
    private String focusGroupsTypeName;
    // 民族
    private String nation;
    // 民族名称
    private String nationName;
    // 国籍
    private String nationality;
    // 国籍名称
    private String nationalityName;
    // 婚姻
    private String marriage;
    // 婚姻名称
    private String marriageName;
    // 职业
    private String work;
    // 职业名称
    private String workName;
    // 联系电话
    private String tel;
    // 籍贯
    private String nativePlace;
    // 户口地址
    private String nativeAddr;
    // 户口详细地址
    private String nativeExactAddr;
    // 户口邮编
    private String nativePostalCode;
    // 现住址
    private String liveAddr;
    // 现详细住址
    private String liveExactAddr;
    // 现住址邮编
    private String livePostalCode;
    // 工作单位名称
    private String companyName;
    // 工作单位地址
    private String companyAddr;
    // 工作单位联系电话
    private String companyTel;
    // 贫困类型
    private String poorType;
    // 贫困类型名称
    private String poorTypeName;
    // 三无人员标志
    private Boolean threeNoPersionnelFlag;
    // 职工标志
    private Boolean staffFlag;
    // abo血型
    private String abo;
    // Rh血型
    private String rh;
    // 过敏标志
    private Boolean allergyFlag;
    // 医生评价内容
    private String doctorAssessment;
    // 出生地
    private String birthPlace;
    // 出生体重
    private String birthWeight;
    // 母亲患者编码
    private String motherCode;
    // 母亲名称
    private String motherName;
    // 已启用
    private boolean enabled;

    public String getIndexCode() {
        return indexCode;
    }

    public void setIndexCode(String indexCode) {
        this.indexCode = indexCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEpmi() {
        return epmi;
    }

    public void setEpmi(String epmi) {
        this.epmi = epmi;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardTypeName() {
        return cardTypeName;
    }

    public void setCardTypeName(String cardTypeName) {
        this.cardTypeName = cardTypeName;
    }

    public String getCardCode() {
        return cardCode;
    }

    public void setCardCode(String cardCode) {
        this.cardCode = cardCode;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getFeeTypeName() {
        return feeTypeName;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getIdentityName() {
        return identityName;
    }

    public void setIdentityName(String identityName) {
        this.identityName = identityName;
    }

    public String getFocusGroupsType() {
        return focusGroupsType;
    }

    public void setFocusGroupsType(String focusGroupsType) {
        this.focusGroupsType = focusGroupsType;
    }

    public String getFocusGroupsTypeName() {
        return focusGroupsTypeName;
    }

    public void setFocusGroupsTypeName(String focusGroupsTypeName) {
        this.focusGroupsTypeName = focusGroupsTypeName;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getNationName() {
        return nationName;
    }

    public void setNationName(String nationName) {
        this.nationName = nationName;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getNationalityName() {
        return nationalityName;
    }

    public void setNationalityName(String nationalityName) {
        this.nationalityName = nationalityName;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getMarriageName() {
        return marriageName;
    }

    public void setMarriageName(String marriageName) {
        this.marriageName = marriageName;
    }

    public String getWork() {
        return work;
    }

    public void setWork(String work) {
        this.work = work;
    }

    public String getWorkName() {
        return workName;
    }

    public void setWorkName(String workName) {
        this.workName = workName;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getNativePlace() {
        return nativePlace;
    }

    public void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }

    public String getNativeAddr() {
        return nativeAddr;
    }

    public void setNativeAddr(String nativeAddr) {
        this.nativeAddr = nativeAddr;
    }

    public String getNativeExactAddr() {
        return nativeExactAddr;
    }

    public void setNativeExactAddr(String nativeExactAddr) {
        this.nativeExactAddr = nativeExactAddr;
    }

    public String getNativePostalCode() {
        return nativePostalCode;
    }

    public void setNativePostalCode(String nativePostalCode) {
        this.nativePostalCode = nativePostalCode;
    }

    public String getLiveAddr() {
        return liveAddr;
    }

    public void setLiveAddr(String liveAddr) {
        this.liveAddr = liveAddr;
    }

    public String getLiveExactAddr() {
        return liveExactAddr;
    }

    public void setLiveExactAddr(String liveExactAddr) {
        this.liveExactAddr = liveExactAddr;
    }

    public String getLivePostalCode() {
        return livePostalCode;
    }

    public void setLivePostalCode(String livePostalCode) {
        this.livePostalCode = livePostalCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddr() {
        return companyAddr;
    }

    public void setCompanyAddr(String companyAddr) {
        this.companyAddr = companyAddr;
    }

    public String getCompanyTel() {
        return companyTel;
    }

    public void setCompanyTel(String companyTel) {
        this.companyTel = companyTel;
    }

    public String getPoorType() {
        return poorType;
    }

    public void setPoorType(String poorType) {
        this.poorType = poorType;
    }

    public String getPoorTypeName() {
        return poorTypeName;
    }

    public void setPoorTypeName(String poorTypeName) {
        this.poorTypeName = poorTypeName;
    }

    public Boolean getThreeNoPersionnelFlag() {
        return threeNoPersionnelFlag;
    }

    public void setThreeNoPersionnelFlag(Boolean threeNoPersionnelFlag) {
        this.threeNoPersionnelFlag = threeNoPersionnelFlag;
    }

    public Boolean getStaffFlag() {
        return staffFlag;
    }

    public void setStaffFlag(Boolean staffFlag) {
        this.staffFlag = staffFlag;
    }

    public String getAbo() {
        return abo;
    }

    public void setAbo(String abo) {
        this.abo = abo;
    }

    public String getRh() {
        return rh;
    }

    public void setRh(String rh) {
        this.rh = rh;
    }

    public Boolean getAllergyFlag() {
        return allergyFlag;
    }

    public void setAllergyFlag(Boolean allergyFlag) {
        this.allergyFlag = allergyFlag;
    }

    public String getDoctorAssessment() {
        return doctorAssessment;
    }

    public void setDoctorAssessment(String doctorAssessment) {
        this.doctorAssessment = doctorAssessment;
    }

    public String getBirthPlace() {
        return birthPlace;
    }

    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public String getBirthWeight() {
        return birthWeight;
    }

    public void setBirthWeight(String birthWeight) {
        this.birthWeight = birthWeight;
    }

    public String getMotherCode() {
        return motherCode;
    }

    public void setMotherCode(String motherCode) {
        this.motherCode = motherCode;
    }

    public String getMotherName() {
        return motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
