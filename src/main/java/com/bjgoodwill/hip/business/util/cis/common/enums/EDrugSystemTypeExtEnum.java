package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: hip-base
 * @author: xdguo
 * @create: 2025-03-15 11:00
 * @className: EDrugSystemTypeExtEnum
 * @description:
 **/
public enum EDrugSystemTypeExtEnum {
    OWNDRUG("1","自带药"),
    COMMON("0","药品字典");

    private String code;
    private String name;

    EDrugSystemTypeExtEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static EDrugSystemTypeExtEnum getByCode(String code) {
        for (EDrugSystemTypeExtEnum eDrugSystemTypeExtEnum : EDrugSystemTypeExtEnum.values()) {
            if (eDrugSystemTypeExtEnum.getCode().equals(code)) {
                return eDrugSystemTypeExtEnum;
            }
        }
        return null;
    }
}