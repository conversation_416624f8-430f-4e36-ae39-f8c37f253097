package com.bjgoodwill.hip.business.util.econ.enums;

/**
 * 四舍五入规则枚举
 *
 * @Author: lzh
 */
public enum RoundedRuleEnum {
    HALF_UP("HALF_UP", "保留两位小数并四舍五入"),

    DOWN("DOWN", "保留两位小数后全舍");

    private String code;
    private String name;

    RoundedRuleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
