package com.bjgoodwill.hip.business.util.cis.util;

import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "处理循环引用")
public abstract class CisBaseApplyCommonNto extends BaseNto  {
    @Serial
    private static final long serialVersionUID = -4730190805295111661L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "门诊诊疗号")
    private String treatmentCode;
    @Schema(description = "医嘱编码")
    private String serviceItemCode;
    @Schema(description = "医嘱名称")
    private String serviceItemName;
    @Schema(description = "是否允许加急标识,1允许0不允许")
    private String isCanPriorityFlag;
    //    @Schema(description = "病历及查体摘要标识")
//    private String medrecordExamabstractId;

    @Schema(description = "IPD住院，OPD门诊")
    private VisitTypeEnum visitType;
    @Schema(description = "护理组编码")
    private String deptNurseCode;
    @Schema(description = "护理组名称")
    private String deptNurseName;
    @Schema(description = "医嘱ID")
    private String orderID;
    @Schema(description = "申请医院编码")
    private String hospitalCode;
    @Schema(description = "处方号")
    private String prescriptionID;
    //    @Schema(description = "打印标识：1己打印，0未打印；默认0")
//    private Boolean isPrint;
//    @Schema(description = "打印人")
//    private String printStaff;
//    @Schema(description = "打印时间")
//    private LocalDate printDate;
    @Schema(description = "备注")
    private String reMark;
    @Schema(description = "重症患者执行时间")
    private LocalDateTime icuExecuteDate;
    @Schema(description = "是否跨院申请项目")
    private Boolean isChargeManager;
    @Schema(description = "开方人所在科室")
    private String createOrgCode;
    @Schema(description = "排序序号")
    private Double sortNo;
    @Schema(description = "isBaby")
    private Boolean isBaby;
    @Schema(description = "医嘱类别")
    private OrderTypeEnum orderType;

    @Schema(description = "开方科室")
    private String visitOrgCode;
    private String hospitalName;
    private String visitOrgName;
    private String createOrgName;

    @Schema(description = "频次")
    private String frequency;
    @Schema(description = "频次名称")
    private String frequencyName;
    // 执行科室编码
    private String executorOrgCode;
    private String executorOrgName;
    private Double num;
    @Schema(description = "是否隔离")
    private Boolean isOlation;
    @Schema(description = "是否申请")
    private Boolean isApply;
    private Boolean mutualExclusionFlag;
    private Boolean sendPatitentFlag;
//    @Schema(description = "绿通标识")
//    private Boolean greenChannelFlag;

    //    @NotBlank(message = "标识不能为空！")
//    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "主索引不能为空！")
    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = StringUtils.trimToNull(patMiCode);
    }

    @NotBlank(message = "流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    public String getTreatmentCode() {
        return treatmentCode;
    }

    public void setTreatmentCode(String treatmentCode) {
        this.treatmentCode = treatmentCode;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = StringUtils.trimToNull(serviceItemCode);
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = StringUtils.trimToNull(serviceItemName);
    }

//    @Size(max = 50, message = "病历及查体摘要标识长度不能超过50个字符！")
//    public String getMedrecordExamabstractId() {
//        return medrecordExamabstractId;
//    }
//
//    public void setMedrecordExamabstractId(String medrecordExamabstractId) {
//        this.medrecordExamabstractId = StringUtils.trimToNull(medrecordExamabstractId);
//    }

    public String getIsCanPriorityFlag() {
        return isCanPriorityFlag;
    }

    public void setIsCanPriorityFlag(String isCanPriorityFlag) {
        this.isCanPriorityFlag = StringUtils.trimToNull(isCanPriorityFlag);
    }

    @NotNull(message = "IPD住院，OPD门诊不能为空！")
    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    @NotBlank(message = "护理组编码不能为空！")
    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = StringUtils.trimToNull(deptNurseCode);
    }

    @NotBlank(message = "护理组名称不能为空！")
    public String getDeptNurseName() {
        return deptNurseName;
    }

    public void setDeptNurseName(String deptNurseName) {
        this.deptNurseName = StringUtils.trimToNull(deptNurseName);
    }

    @NotBlank(message = "医嘱ID不能为空！")
    public String getOrderID() {
        return orderID;
    }

    public void setOrderID(String orderID) {
        this.orderID = StringUtils.trimToNull(orderID);
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = StringUtils.trimToNull(hospitalCode);
    }

//    public Boolean getIsPrint() {
//        return isPrint;
//    }
//
//    public void setIsPrint(Boolean isPrint) {
//        this.isPrint = isPrint;
//    }
//
//    public String getPrintStaff() {
//        return printStaff;
//    }
//
//    public void setPrintStaff(String printStaff) {
//        this.printStaff = StringUtils.trimToNull(printStaff);
//    }
//
//    public LocalDate getPrintDate() {
//        return printDate;
//    }
//
//    public void setPrintDate(LocalDate printDate) {
//        this.printDate = printDate;
//    }

    public String getPrescriptionID() {
        return prescriptionID;
    }

    public void setPrescriptionID(String prescriptionID) {
        this.prescriptionID = StringUtils.trimToNull(prescriptionID);
    }

    public String getReMark() {
        return reMark;
    }

    public void setReMark(String reMark) {
        this.reMark = StringUtils.trimToNull(reMark);
    }

    public LocalDateTime getIcuExecuteDate() {
        return icuExecuteDate;
    }

    public void setIcuExecuteDate(LocalDateTime icuExecuteDate) {
        this.icuExecuteDate = icuExecuteDate;
    }

    public Boolean getIsChargeManager() {
        return isChargeManager;
    }

    public void setIsChargeManager(Boolean isChargeManager) {
        this.isChargeManager = isChargeManager;
    }

    @NotBlank(message = "开方人所在科室不能为空！")
    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = StringUtils.trimToNull(createOrgCode);
    }

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    public Boolean getIsBaby() {
        return isBaby;
    }

    public void setIsBaby(Boolean isBaby) {
        this.isBaby = isBaby;
    }

    public String getVisitOrgCode() {
        return visitOrgCode;
    }

    public void setVisitOrgCode(String visitOrgCode) {
        this.visitOrgCode = visitOrgCode;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    //endregion

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getVisitOrgName() {
        return visitOrgName;
    }

    public void setVisitOrgName(String visitOrgName) {
        this.visitOrgName = visitOrgName;
    }

    public String getCreateOrgName() {
        return createOrgName;
    }

    public void setCreateOrgName(String createOrgName) {
        this.createOrgName = createOrgName;
    }

    @NotBlank(message = "频次不能为空！")
    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = StringUtils.trimToNull(frequency);
    }

    @NotBlank(message = "频次名称不能为空！")
    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = StringUtils.trimToNull(frequencyName);
    }

    @NotNull(message = "医嘱类别不能为空！")
    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderTypeEnum orderType) {
        this.orderType = orderType;
    }

    @NotNull(message = "执行科室不能为空！")
    public String getExecutorOrgCode() {
        return executorOrgCode;
    }

    public void setExecutorOrgCode(String executorOrgCode) {
        this.executorOrgCode = executorOrgCode;
    }

    public String getExecutorOrgName() {
        return executorOrgName;
    }

    public void setExecutorOrgName(String executorOrgName) {
        this.executorOrgName = executorOrgName;
    }

    //region 医嘱校验 回传数据
//    private CisServiceItemLimitTypeEnum cisServiceItemLimitTypeEnum;
//
//    public CisServiceItemLimitTypeEnum getCisServiceItemLimitTypeEnum() {
//        return cisServiceItemLimitTypeEnum;
//    }
//
//    public void setCisServiceItemLimitTypeEnum(CisServiceItemLimitTypeEnum cisServiceItemLimitTypeEnum) {
//        this.cisServiceItemLimitTypeEnum = cisServiceItemLimitTypeEnum;
//    }

    @NotNull(message = "数量不能为空！")
    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public Boolean getMutualExclusionFlag() {
        return mutualExclusionFlag;
    }

    public void setMutualExclusionFlag(Boolean mutualExclusionFlag) {
        this.mutualExclusionFlag = mutualExclusionFlag;
    }

    public Boolean getSendPatitentFlag() {
        return sendPatitentFlag;
    }

    public void setSendPatitentFlag(Boolean sendPatitentFlag) {
        this.sendPatitentFlag = sendPatitentFlag;
    }

    public Boolean getIsOlation() {
        return isOlation;
    }

    public void setIsOlation(Boolean isOlation) {
        this.isOlation = isOlation;
    }

    public Boolean getIsApply() {
        return isApply;
    }

    public void setIsApply(Boolean isApply) {
        this.isApply = isApply;
    }

//    public Boolean getGreenChannelFlag() {
//        return greenChannelFlag;
//    }
//
//    public void setGreenChannelFlag(Boolean greenChannelFlag) {
//        this.greenChannelFlag = greenChannelFlag;
//    }
}