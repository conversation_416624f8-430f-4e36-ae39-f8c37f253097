package com.bjgoodwill.hip.business.util.mq.to.econ;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * &#064;date 2025/2/13 上午9:22
 */
@Schema(description = "门诊退费审核通知实体")
public class EconOpdRefundAuditMqTo {

    @Schema(description = "处方号")
    private String rxNo;

    @Schema(description = "医嘱号")
    private String drordNo;

    @Schema(description = "申请单号")
    private String applyNo;

    @Schema(description = "执行单号")
    private String execNo;

    @Schema(description = "申请单明细号")
    private List<String> applyDelNos;

    @Schema(description = "退药申请单明细号")
    private List<String> drugApplyDelNos;

    @Schema(description = "执行单明细号")
    private List<String> execDelNos;

    @Schema(description = "退费审核人")
    private String reAuditStaff;
    @Schema(description = "退费审核人姓名")
    private String reAuditStaffName;
    @Schema(description = "退费审核结果：Pass通过，Reject驳回")
    private String reAuditResult;
    @Schema(description = "退费审核结果说明")
    private String reAuditMemo;

    public String getRxNo() {
        return rxNo;
    }

    public void setRxNo(String rxNo) {
        this.rxNo = rxNo;
    }

    public String getDrordNo() {
        return drordNo;
    }

    public void setDrordNo(String drordNo) {
        this.drordNo = drordNo;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getExecNo() {
        return execNo;
    }

    public void setExecNo(String execNo) {
        this.execNo = execNo;
    }

    public List<String> getApplyDelNos() {
        return applyDelNos;
    }

    public void setApplyDelNos(List<String> applyDelNos) {
        this.applyDelNos = applyDelNos;
    }

    public List<String> getDrugApplyDelNos() {
        return drugApplyDelNos;
    }

    public void setDrugApplyDelNos(List<String> drugApplyDelNos) {
        this.drugApplyDelNos = drugApplyDelNos;
    }

    public List<String> getExecDelNos() {
        return execDelNos;
    }

    public void setExecDelNos(List<String> execDelNos) {
        this.execDelNos = execDelNos;
    }

    public String getReAuditStaff() {
        return reAuditStaff;
    }

    public void setReAuditStaff(String reAuditStaff) {
        this.reAuditStaff = reAuditStaff;
    }

    public String getReAuditStaffName() {
        return reAuditStaffName;
    }

    public void setReAuditStaffName(String reAuditStaffName) {
        this.reAuditStaffName = reAuditStaffName;
    }

    public String getReAuditResult() {
        return reAuditResult;
    }

    public void setReAuditResult(String reAuditResult) {
        this.reAuditResult = reAuditResult;
    }

    public String getReAuditMemo() {
        return reAuditMemo;
    }

    public void setReAuditMemo(String reAuditMemo) {
        this.reAuditMemo = reAuditMemo;
    }
}
