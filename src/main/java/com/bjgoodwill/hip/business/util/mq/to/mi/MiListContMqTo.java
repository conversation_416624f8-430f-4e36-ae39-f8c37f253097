package com.bjgoodwill.hip.business.util.mq.to.mi;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;

/**
 * 医保目录对照
 */
public class MiListContMqTo {
    @Schema(description = "医院目录编码")
    private String hisCode;
    @Schema(description = "医院目录名称")
    private String hisName;
    @Schema(description = "医保目录编码")
    private String miCode;
    @Schema(description = "医保目录名称")
    private String miName;
    @Schema(description = "开始有效日期")
    private LocalDate effectiveStartDate;
    @Schema(description = "截止有效日期")
    private LocalDate effectiveEndDate;
    @Schema(description = "收费项目等级名称")
    private String chrgitmLvName;
    @Schema(description = "医疗收费项目类别名称")
    private String medChrgitmTypeName;
    @Schema(description = "限制使用标志")
    private String lmtUsedFlag;
    @Schema(description = "医保限用范围")
    private String miRestrictScope;
    @Schema(description = "医保目录使用类别名称")
    private String hilistUseTypeName;
    @Schema(description = "医疗使用标志")
    private String medUseFlag;
    @Schema(description = "生育使用标识名称")
    private String matnUseFlagName;
    @Schema(description = "限复方使用类型名称")
    private String lmtCpndTypeName;
    // 已启用
    private boolean enabled;

    public String getHisCode() {
        return hisCode;
    }

    public void setHisCode(String hisCode) {
        this.hisCode = hisCode;
    }

    public String getHisName() {
        return hisName;
    }

    public void setHisName(String hisName) {
        this.hisName = hisName;
    }

    public String getMiCode() {
        return miCode;
    }

    public void setMiCode(String miCode) {
        this.miCode = miCode;
    }

    public String getMiName() {
        return miName;
    }

    public void setMiName(String miName) {
        this.miName = miName;
    }

    public LocalDate getEffectiveStartDate() {
        return effectiveStartDate;
    }

    public void setEffectiveStartDate(LocalDate effectiveStartDate) {
        this.effectiveStartDate = effectiveStartDate;
    }

    public LocalDate getEffectiveEndDate() {
        return effectiveEndDate;
    }

    public void setEffectiveEndDate(LocalDate effectiveEndDate) {
        this.effectiveEndDate = effectiveEndDate;
    }

    public String getChrgitmLvName() {
        return chrgitmLvName;
    }

    public void setChrgitmLvName(String chrgitmLvName) {
        this.chrgitmLvName = chrgitmLvName;
    }

    public String getMedChrgitmTypeName() {
        return medChrgitmTypeName;
    }

    public void setMedChrgitmTypeName(String medChrgitmTypeName) {
        this.medChrgitmTypeName = medChrgitmTypeName;
    }

    public String getLmtUsedFlag() {
        return lmtUsedFlag;
    }

    public void setLmtUsedFlag(String lmtUsedFlag) {
        this.lmtUsedFlag = lmtUsedFlag;
    }

    public String getMiRestrictScope() {
        return miRestrictScope;
    }

    public void setMiRestrictScope(String miRestrictScope) {
        this.miRestrictScope = miRestrictScope;
    }

    public String getHilistUseTypeName() {
        return hilistUseTypeName;
    }

    public void setHilistUseTypeName(String hilistUseTypeName) {
        this.hilistUseTypeName = hilistUseTypeName;
    }

    public String getMedUseFlag() {
        return medUseFlag;
    }

    public void setMedUseFlag(String medUseFlag) {
        this.medUseFlag = medUseFlag;
    }

    public String getMatnUseFlagName() {
        return matnUseFlagName;
    }

    public void setMatnUseFlagName(String matnUseFlagName) {
        this.matnUseFlagName = matnUseFlagName;
    }

    public String getLmtCpndTypeName() {
        return lmtCpndTypeName;
    }

    public void setLmtCpndTypeName(String lmtCpndTypeName) {
        this.lmtCpndTypeName = lmtCpndTypeName;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
