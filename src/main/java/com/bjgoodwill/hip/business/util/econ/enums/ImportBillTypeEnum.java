package com.bjgoodwill.hip.business.util.econ.enums;

/**
 * 导费类型
 */
public enum ImportBillTypeEnum {


    NONE("NONE", "未导过"),

    IN("ER", "导入"),

    OUT("OP", "导出");

    private final String code;
    private final String name;

    ImportBillTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (ImportBillTypeEnum value : ImportBillTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}