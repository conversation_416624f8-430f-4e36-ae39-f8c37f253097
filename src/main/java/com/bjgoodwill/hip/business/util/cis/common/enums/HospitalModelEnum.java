package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * 医嘱项目适用范围枚举
 */
public enum HospitalModelEnum {

    全院("ALL", "全院"),
    门诊("OPD", "门诊"),
    住院("IPD", "住院"),
    急诊("EMER", "急诊"),
    其他("OTHER", "其他");


    private String name;
    private String code;

    HospitalModelEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return this.name();
    }

    public String getCode() {
        return this.code;
    }

}
