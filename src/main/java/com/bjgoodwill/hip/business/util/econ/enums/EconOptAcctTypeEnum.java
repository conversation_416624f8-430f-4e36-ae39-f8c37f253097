package com.bjgoodwill.hip.business.util.econ.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门诊账户类型
 *
 * <AUTHOR>
 */
public enum EconOptAcctTypeEnum {

    OPD_ACCT("OpdAcct", "门诊普通账户", ""),
    STAFF_ACCT("StaffAcct", "院内职工账户", "");


    private final String code;
    private final String name;
    private final String type;

    EconOptAcctTypeEnum(String code, String name, String type) {
        this.code = code;
        this.name = name;
        this.type = type;
    }

    /**
     * 根据Type获取枚举集合
     *
     * @param type
     * @return
     */
    public static List<EconOptAcctTypeEnum> getByType(String type) {
        EconOptAcctTypeEnum[] values = EconOptAcctTypeEnum.values();
        //根据type过滤
        return StringUtils.isEmpty(type) ? null : Arrays.stream(values)
                .filter(day -> day.getType().equals(type))
                .collect(Collectors.toList());
    }

    /**
     * 根据编码获取枚举值对象
     *
     * @param code
     * @return
     */
    public static EconOptAcctTypeEnum getByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        List<EconOptAcctTypeEnum> list = Arrays.stream(EconOptAcctTypeEnum.values())
                .filter(day -> day.getCode().equals(code)).toList();
        return list.isEmpty() ? null : list.get(0);
    }

    public String getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}