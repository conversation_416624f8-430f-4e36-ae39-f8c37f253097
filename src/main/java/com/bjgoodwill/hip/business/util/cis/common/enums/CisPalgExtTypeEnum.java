package com.bjgoodwill.hip.business.util.cis.common.enums;

public enum CisPalgExtTypeEnum {

    OPERATION("OPERATION", "手术填写"),

    TUMOR("TUMOR", "肿瘤填写"),

    GYNECOLOGY("GYNECO<PERSON>OGY", "妇产填写");

    private String code;
    private String name;

    CisPalgExtTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CisPalgExtTypeEnum getEnum(String code) {
        for (CisPalgExtTypeEnum value : CisPalgExtTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}