package com.bjgoodwill.hip.business.util.enums.dict;

import com.bjgoodwill.hip.common.bean.EnumTo;

import java.util.ArrayList;
import java.util.List;


/**
 * 通用术语获取
 *
 * <AUTHOR>
 */
public enum DictCodeEnum {
    //基础
    性别("AdministrativeGender"),
    职称("Title"),
    民族("Nation"),
    婚姻("MaritalStatus"),
    技术职务("ProfessionalTechnicalPosition"),
    行政职务("Position"),
    政治面貌("PoliticalStatus"),
    编制("CV08.30.001"),
    科室("CT08.00.002"),
    给药途径("Usage"),
    药品类别("DrugType"),
    草药属性("HerbPro"),
    行政划分三级("AdministrativeRegions"),

    //患者
//    费别(""),
    国家("CountryRegion"),
    行政区划("AdministrativeDivesions"),
    证件类型("IdentifierName"),
    身份("PatIdentity"),
    重点人群分类("CVA01.00.002"),
    贫困类型("PoorType"),
    职业("Occupation"),
    ABO血型("BloodType"),
    Rh血型("CF04.50.999"),
    联系人类型("FamilyRelationship"),
    账户类型("ambAccountType"),
    过敏源("CV05.01.038"),

    //药品
    抗肿瘤分级("AntineoplasticLevel"),
    基药属性("BasicMedicine"),
    草药特殊煎法("DecoctMethod"),
    剂量单位("DosageUnit"),
    毒理属性("DrugToxiProperty"),
    //    药品费用分类("DrugFeeClass"),用经济的FeeClass
    剂型("DrugForm"),
    引进方式("DrugIntroduceWay"),
    //    药品病案费用分类("DrugMrFeeClass"),用经济的MRFeeClass
    最小单位("MinUnit"),
    包装单位("PackageUnit"),
    大包装单位("BigPackageUnit"),
    包装包材("PackMaterial"),
    药理归类("PharmacologyClass"),
    储藏条件("StoreageCondition"),
    疗程单位("TreatmentCourseUnit"),
    容积单位("VolumeUnit"),
    滴速单位("DripSpeedUnit"),
    //物资
    物资分类("MaterialType"),
    物资单位("MateUnit"),
    物资采购类别("MateIntroduceWay"),
    物资包装包材("PackMaterial"),
    物资加价率("MateAddPrice"),

    //费用
    支付方式("PayWay"),
    统计分类("InvoiceType"),
    系统分类("SystemType"),
    费用分类("FeeClass"),
    病案费用分类("MRFeeClass"),
    收费项目单位("PriceUnit"),
    结算减免("BpubType"),
    //    国家医保目录等级(""),
    //    医保类型(""),
    //住院患者
    住院申请入院来源("PatientSource"),
    住院申请入院方式("InpWay"),
    住院申请患者病情("PatAdmCondition"),
    新生儿入院类型("NewbornInType"),

    //门诊患者
    挂号等级("PermType"),

    //诊疗
    医嘱项目单位("Measures"),
    服务项目分类("ServiceItem"),
    标本("Speciman"),
    实验方法("DgimgMethod"),
    范围("DgimgRange"),
    层数("CheckNum"),
    方位("DgimgDirection"),
    部位("HumanOrgans"),
    检查设备类型("DgimgDeviceType"),
    入路("Approach"),
    辅助器械("Instrument"),
    基本操作("DiagnosisOperation"),
    检验设备类型("SpcobsDeviceType"),
    每次持续单位("TimeUnit"),
    切口等级("WoundGrade"),
    麻醉方式("AnaesthesiaType"),
    全身麻醉("AnaesthesiaMode1"),
    椎管内麻醉("AnaesthesiaMode2"),
    局部麻醉("AnaesthesiaMode3"),
    神经及神经丛阻滞("AnaesthesiaMode5"),
    其他麻醉方法("AnaesthesiaMode7"),
    无麻醉("AnaesthesiaMode8"),
    特殊手术类型("OperationSpecialAttr"),
    手术体位("OperationPosition"),
    手术部位("OISHumanOrgans"),
    手术类别("OperationType"),
    诊疗类别("TreatmentType"),
    //    麻醉类型("AnaesthesiaType"),
    //护理
    //医保
    费别("ChargeType"),
    险种类型("insutype"),
    收费项目等级("chrgitm_lv");

    private final String code;

    DictCodeEnum(String code) {
        this.code = code;
    }

    public static boolean validate(String code) {
        return DictCodeEnum.getEnum(code) != null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getName(String code) {
        for (DictCodeEnum enumItem : DictCodeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem.name();
            }
        }
        return null;
    }

    public static DictCodeEnum getEnum(String code) {
        for (DictCodeEnum enumItem : DictCodeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 获取List列表
     */
    public static List<EnumTo<String>> getList() {
        List<EnumTo<String>> list = new ArrayList<>();
        EnumTo<String> enumTo;
        for (DictCodeEnum enumItem : DictCodeEnum.values()) {
            enumTo = new EnumTo<>();
            enumTo.setCode(enumItem.getCode());
            enumTo.setName(enumItem.name());
            list.add(enumTo);
        }
        return list;
    }

    public String getCode() {
        return code;
    }

}