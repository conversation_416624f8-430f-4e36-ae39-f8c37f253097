package com.bjgoodwill.hip.business.util.mq.to.base;

import java.math.BigDecimal;

/**
 * 调价消息实体
 *
 * @Author: lzh
 */
public class EconPriceChangeMqTo {
    // 编码
    private String code;
    // 新价
    private BigDecimal price;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }
}
