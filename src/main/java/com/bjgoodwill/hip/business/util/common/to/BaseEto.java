package com.bjgoodwill.hip.business.util.common.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * @program: hip-base
 * @author: xdguo
 * @create: 2025-04-11 15:10
 * @className: BaseEto
 * @description:
 **/
@Schema(description = "Base基础Eto")
public abstract class BaseEto implements Serializable {

    @Schema(description = "当前登录人")
    private String curLoginStaff;

    @Schema(description = "当前登录人姓名")
    private String curLoginStaffName;

    @Schema(description = "分院编码")
    private String branchHospitalCode;

    @Schema(description = "分院名称")
    private String branchHospitalName;

    @Schema(description = "工作组编码")
    private String curWorkGroupCode;

    @Schema(description = "工作组名称")
    private String curWorkGroupName;

    public String getCurLoginStaff() {
        return curLoginStaff;
    }

    public void setCurLoginStaff(String curLoginStaff) {
        this.curLoginStaff = curLoginStaff;
    }

    public String getCurLoginStaffName() {
        return curLoginStaffName;
    }

    public void setCurLoginStaffName(String curLoginStaffName) {
        this.curLoginStaffName = curLoginStaffName;
    }

    public String getBranchHospitalCode() {
        return branchHospitalCode;
    }

    public void setBranchHospitalCode(String branchHospitalCode) {
        this.branchHospitalCode = branchHospitalCode;
    }

    public String getBranchHospitalName() {
        return branchHospitalName;
    }

    public void setBranchHospitalName(String branchHospitalName) {
        this.branchHospitalName = branchHospitalName;
    }

    public String getCurWorkGroupCode() {
        return curWorkGroupCode;
    }

    public void setCurWorkGroupCode(String curWorkGroupCode) {
        this.curWorkGroupCode = curWorkGroupCode;
    }

    public String getCurWorkGroupName() {
        return curWorkGroupName;
    }

    public void setCurWorkGroupName(String curWorkGroupName) {
        this.curWorkGroupName = curWorkGroupName;
    }
}