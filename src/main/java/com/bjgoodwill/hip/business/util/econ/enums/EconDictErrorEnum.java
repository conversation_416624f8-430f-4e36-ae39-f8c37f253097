package com.bjgoodwill.hip.business.util.econ.enums;

import com.bjgoodwill.hip.common.exception.BusinessErrorEnum;

public enum EconDictErrorEnum implements BusinessErrorEnum {
    BUS_FIXED_000("[%s]"),
    BUS_FIXED_001("字典[%s]未维护字典项[%s]数据，请联系相关人员维护字典数据。"),
    BUS_FIXED_002("获取字典项名称时入参[%s]不允许为空。");

    private final String message;

    EconDictErrorEnum(String message) {
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.name();
    }

    @Override
    public String getMessage() {
        return message;
    }
}
