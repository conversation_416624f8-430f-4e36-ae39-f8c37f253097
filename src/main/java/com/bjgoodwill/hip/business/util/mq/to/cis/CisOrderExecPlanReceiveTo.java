package com.bjgoodwill.hip.business.util.mq.to.cis;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-10-23 15:40
 * @className: CisOrderExecPlanReceiveTo
 * @description:
 **/
@Schema(description = "执行单 执行 接收消息")
public class CisOrderExecPlanReceiveTo implements Serializable {
    @Serial
    private static final long serialVersionUID = -1190103657283437554L;

    private String execPlanId;

    private String visitCode;

    private SystemTypeEnum systemType;

    public String getExecPlanId() {
        return execPlanId;
    }

    public void setExecPlanId(String execPlanId) {
        this.execPlanId = execPlanId;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }
}