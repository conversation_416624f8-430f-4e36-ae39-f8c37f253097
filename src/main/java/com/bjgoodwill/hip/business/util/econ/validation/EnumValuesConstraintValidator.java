package com.bjgoodwill.hip.business.util.econ.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jdk.jfr.Registered;

import java.util.HashSet;
import java.util.Set;

/**
 * Description:
 *
 * <AUTHOR>
 * &#064;date 2024/4/23 上午10:35
 */
@Registered
public class EnumValuesConstraintValidator implements ConstraintValidator<EnumValues, String> {

    // 存储枚举的值
    private Set<String> strs = new HashSet<>();

    // 初始化方法
    // EnumValues 校验的注解
    @Override
    public void initialize(EnumValues values) {
        for (String item : values.values()) {
            strs.add(item);
        }
    }

    /**
     * @param value   入参传的值
     * @param context
     * @return
     */
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        return strs.contains(value);
    }
}

