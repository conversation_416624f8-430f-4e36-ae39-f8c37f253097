package com.bjgoodwill.hip.business.util.drug.enums;

import com.bjgoodwill.hip.common.bean.EnumTo;

import java.util.ArrayList;
import java.util.List;

/**
 * 药品处方类型枚举类
 *
 * <AUTHOR>
 */
public enum DrugPresTypeEnum {
    普("1","普"),
    麻精一("2","麻、精一"),
    精二("3","精二"),
    儿("4","儿"),
    急("5","急"),
    慢("6","慢"),
    毒("7","毒"),
    麻("8","麻"),
    精一("9","精一");

    private final String code;

    private final String name;

    DrugPresTypeEnum(String code,String name) {
        this.code = code;
        this.name = name;
    }

    public static boolean validate(String code) {
        return DrugPresTypeEnum.getEnum(code) != null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getName(String code) {
        for (DrugPresTypeEnum enumItem : DrugPresTypeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem.getName();
            }
        }
        return null;
    }

    public static DrugPresTypeEnum getEnum(String code) {
        for (DrugPresTypeEnum enumItem : DrugPresTypeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 获取List列表
     */
    public static List<EnumTo<String>> getList() {
        List<EnumTo<String>> list = new ArrayList<>();
        EnumTo<String> enumTo;
        for (DrugPresTypeEnum enumItem : DrugPresTypeEnum.values()) {
            enumTo = new EnumTo<>();
            enumTo.setCode(enumItem.getCode());
            enumTo.setName(enumItem.getName());
            list.add(enumTo);
        }
        return list;
    }

    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
}