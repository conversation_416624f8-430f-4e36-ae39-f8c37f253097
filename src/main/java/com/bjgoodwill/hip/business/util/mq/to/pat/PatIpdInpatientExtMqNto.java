package com.bjgoodwill.hip.business.util.mq.to.pat;

import com.bjgoodwill.hip.business.util.pat.enums.PatIpdInpatientExtEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "住院患者扩展医嘱信息")
public class PatIpdInpatientExtMqNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -3702725482572940978L;

    @Schema(description = "医嘱类型")
    @NotNull(message = "医嘱类型不能为空！")
    private PatIpdInpatientExtEnum type;

    @Schema(description = "护理常规")
    private String routineCare;
    @Schema(description = "护理常规名称")
    private String routineCareName;
    @Schema(description = "病情")
    private String criticalCarePatient;
    @Schema(description = "病情名称")
    private String criticalCarePatientName;
    @Schema(description = "术前")
    private Boolean preoperative;
    @Schema(description = "术后天数")
    private LocalDateTime postoperativeDays;
    @Schema(description = "路径患者标识")
    private Boolean pathwayFlag;

    public PatIpdInpatientExtEnum getType() {
        return type;
    }

    public void setType(PatIpdInpatientExtEnum type) {
        this.type = type;
    }

    public String getRoutineCare() {
        return routineCare;
    }

    public void setRoutineCare(String routineCare) {
        this.routineCare = routineCare;
    }

    public String getRoutineCareName() {
        return routineCareName;
    }

    public void setRoutineCareName(String routineCareName) {
        this.routineCareName = routineCareName;
    }

    public String getCriticalCarePatient() {
        return criticalCarePatient;
    }

    public void setCriticalCarePatient(String criticalCarePatient) {
        this.criticalCarePatient = criticalCarePatient;
    }

    public String getCriticalCarePatientName() {
        return criticalCarePatientName;
    }

    public void setCriticalCarePatientName(String criticalCarePatientName) {
        this.criticalCarePatientName = criticalCarePatientName;
    }

    public Boolean getPreoperative() {
        return preoperative;
    }

    public void setPreoperative(Boolean preoperative) {
        this.preoperative = preoperative;
    }

    public LocalDateTime getPostoperativeDays() {
        return postoperativeDays;
    }

    public void setPostoperativeDays(LocalDateTime postoperativeDays) {
        this.postoperativeDays = postoperativeDays;
    }

    public Boolean getPathwayFlag() {
        return pathwayFlag;
    }

    public void setPathwayFlag(Boolean pathwayFlag) {
        this.pathwayFlag = pathwayFlag;
    }
}