package com.bjgoodwill.hip.business.util.pat.util;

import com.bjgoodwill.hip.common.util.LocalDateUtil;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 患者通用工具类
 *
 * @Author: changwenqing
 */
public class PatCommonUtils {
    /**
     * 根据入院时间计算是否新入院患者
     *
     * @param inTime
     * @return
     */
    public static Boolean getNewPatFlag(LocalDateTime inTime) {
        LocalDateTime now = LocalDateTime.now();
        int hour = 12;
        //入院 hour 小时内为新入院
        LocalDateTime beginDate = LocalDateUtil.offset(now, -hour, ChronoUnit.HOURS);
        if (beginDate.compareTo(inTime) <= 0) {
            return true;
        }
        // 或12至24小时内，不跨天的为新入院。
        if (inTime.toLocalDate().isEqual(now.toLocalDate())) {
            return true;
        }
        return false;
    }

}
