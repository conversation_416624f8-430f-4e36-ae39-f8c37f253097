package com.bjgoodwill.hip.business.util.common.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @program: hip-base
 * @author: xdguo
 * @create: 2025-04-11 15:10
 * @className: BaseTo
 * @description:
 **/
@Schema(description = "Base基础To")
public class BaseTo extends BaseQto {

    @Schema(description = "院区编码")
    private String hospitalAreaCode;
    @Schema(description = "院区名称")
    private String hospitalAreaName;
    @Schema(description = "分院编码")
    private String hospitalBranchCode;
    @Schema(description = "分院名称")
    private String hospitalBranchName;
    @Schema(description = "工作组编码")
    private String workGroupCode;
    @Schema(description = "工作组名称")
    private String workGroupName;
    @Schema(description = "行政科室编码")
    private String deptCode;
    @Schema(description = "行政科室名称")
    private String deptName;
    @Schema(description = "工作组类型")
    private String workGroupType;
    @Schema(description = "工作组类型")
    private String workGroupTypeName;

    public String getHospitalAreaCode() {
        return hospitalAreaCode;
    }

    public void setHospitalAreaCode(String hospitalAreaCode) {
        this.hospitalAreaCode = hospitalAreaCode;
    }

    public String getHospitalAreaName() {
        return hospitalAreaName;
    }

    public void setHospitalAreaName(String hospitalAreaName) {
        this.hospitalAreaName = hospitalAreaName;
    }

    public String getHospitalBranchCode() {
        return hospitalBranchCode;
    }

    public void setHospitalBranchCode(String hospitalBranchCode) {
        this.hospitalBranchCode = hospitalBranchCode;
    }

    public String getHospitalBranchName() {
        return hospitalBranchName;
    }

    public void setHospitalBranchName(String hospitalBranchName) {
        this.hospitalBranchName = hospitalBranchName;
    }

    public String getWorkGroupCode() {
        return workGroupCode;
    }

    public void setWorkGroupCode(String workGroupCode) {
        this.workGroupCode = workGroupCode;
    }

    public String getWorkGroupName() {
        return workGroupName;
    }

    public void setWorkGroupName(String workGroupName) {
        this.workGroupName = workGroupName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getWorkGroupType() {
        return workGroupType;
    }

    public void setWorkGroupType(String workGroupType) {
        this.workGroupType = workGroupType;
    }

    public String getWorkGroupTypeName() {
        return workGroupTypeName;
    }

    public void setWorkGroupTypeName(String workGroupTypeName) {
        this.workGroupTypeName = workGroupTypeName;
    }
}