package com.bjgoodwill.hip.business.util.econ.enums;

/**
 * 系统项目分类
 */
public enum SystemItemClassEnum {

    PRICE("PRICE", "物价项目"),
    DRUG("DRUG", "药品"),
    MATE("MATE", "材料");

    private final String code;
    private final String name;

    SystemItemClassEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SystemItemClassEnum getEnum(String code) {
        for (SystemItemClassEnum value : SystemItemClassEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}