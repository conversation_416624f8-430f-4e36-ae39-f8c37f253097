package com.bjgoodwill.hip.business.util.econ;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.econ.enums.PayWayEnum;
import com.bjgoodwill.hip.business.util.enums.dict.DictCodeEnum;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import org.springframework.util.Assert;

/**
 * 大数据类型工具类
 *
 * <AUTHOR>
 */
public class DictToPayWayEnumUtils {
    private static DictElementService dictElementService() {
        return SpringUtil.getBean(DictElementService.class);
    }

    public static PayWayEnum dictToPayWayEnum(String payWayCode) {
        DictElementTo payWayElement = dictElementService().getCustomDictElement(DictCodeEnum.支付方式.getCode(), payWayCode);
        Assert.notNull(payWayElement, "支付方式字典项编码[" + payWayCode + "]的信息不存在");
        Assert.hasText(payWayElement.getContrastRelation(), "支付方式字典项编码[" + payWayCode + "]未维护对照枚举编码,请维护后重试");
        PayWayEnum payWayEnum = PayWayEnum.getEnum(payWayElement.getContrastRelation());
        Assert.notNull(payWayElement, "支付方式字典项编码[" + payWayCode + "]对照匹配不到枚举值,请重新维护");
        return payWayEnum;
    }
}

