package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-03-13 11:09
 * @className: CisDividedRule
 * @description: 门诊处方分方范围
 **/
public enum CisDividedRangeEnum {
    PERSON("PERSON", "个人"),
    DEPARTMENT("DEPARTMENT", "科室"),
    HOSPITAL("HOSPITAL", "医院");

    private String code;
    private String name;
    CisDividedRangeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
    public static CisDividedRangeEnum getByCode(String code) {
        for (CisDividedRangeEnum cisDividedRange : CisDividedRangeEnum.values()) {
            if (cisDividedRange.getCode().equals(code)) {
                return cisDividedRange;
            }
        }
        return null;
    }
}
