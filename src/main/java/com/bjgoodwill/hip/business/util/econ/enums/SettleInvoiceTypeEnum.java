package com.bjgoodwill.hip.business.util.econ.enums;

public enum SettleInvoiceTypeEnum {

    OPEN("OPEN", "开票"),
    BACK("BACK", "退票"),
    REPRINT("REPRINT", "重打");

    private final String code;
    private final String name;

    SettleInvoiceTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(String code) {
        for (SettleInvoiceTypeEnum value : SettleInvoiceTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }
}