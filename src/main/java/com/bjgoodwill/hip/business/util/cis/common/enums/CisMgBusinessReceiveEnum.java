package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-11-13 15:42
 * @className: CisMgBusinessReceiveEnum
 * @description:
 **/
public enum CisMgBusinessReceiveEnum {
    SURCHANGE("SURCHANGE"),//补费
    REFUND("REFUND"),//退费
    RERUREDRUG("RERUREDRUG"),//药品回写
    EXECUTE("EXECUTE"),//执行单执行
    CANCEL("CANCEL"),//取消执行单
    DISCHARGE("DISCHARGE"),//更新患者为待出院
    CHANGEDEPT("CHANGEDEPT"),//更新患者为待转科
    CBEDCARD("CBEDCARD"),//创建床头卡
    DBEDCARD("DBEDCARD"), //作废床头卡
    CANCELDEPT("CANCELDEPT"),//取消转科
    CANCELDISCHARGE("CANCELDISCHARGE") //取消出院
    ;

    private String value;

    CisMgBusinessReceiveEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
