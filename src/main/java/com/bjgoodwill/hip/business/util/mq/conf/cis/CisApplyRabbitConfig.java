package com.bjgoodwill.hip.business.util.mq.conf.cis;

import org.springframework.context.annotation.Configuration;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-09-19 09:34
 * @className: CisApplyRabbitConfig
 * @description:
 **/
@Configuration
public class CisApplyRabbitConfig {
    //
    public static final String CIS_APPLY_SYNC_EXCHANGE = "cisApplyTopicExchange";
    //发送
    public static final String STORAGE_IN_EXTERNAL_SAVE_ROUTING_KEY = "storage.in.external.save";

    public static final String CIS_APPLY_SYNC_INOUT_QUEUE = "cis.apply.sync.inout.queue";
    public static final String CIS_APPLY_CHARGE_SYNC_ROUTING_KEY = "cis.apply.sync.change";

    public static final String CIS_APPLY_DRUG_SYNC_ROUTING_KEY = "cis.apply.sync.drug";

    public static final String CIS_APPLY_PLAN_SYNC_ROUTING_KEY = "cis.apply.sync.plan";

    public static final String CIS_APPLY_PLAN_SYNC_CANCEL_QUEUE = "cis.apply.sync.plan.cancel.queue";
    public static final String CIS_APPLY_PLAN_SYNC_CANCEL_ROUTING_KEY = "cis.apply.sync.plan.cancel";

    public static final String CIS_IPD_ORDER_EXT_SAVE_KEY = "cis.ipd.order.sync.save.key";

    public static final String CIS_IPD_ORDER_EXT_SYNC_UPDATE = "cis.ipd.order.sync.update";

    public static final String CIS_IPD_ORDER_STATUER_UPDATE_KEY = "cis.ipd.order.statue.sync.update.key";

    public static final String CIS_IPD_ORDER_STATUER_UPDATE = "cis.ipd.order.statue.sync.update";

    public static final String CIS_IPD_ORDER_SERVICECODE_UPDATE_KEY = "cis.ipd.order.serviceCode.sync.update.key";

    public static final String CIS_IPD_ORDER_SERVICECODE_UPDATE = "cis.ipd.order.serviceCode.sync.update";
}