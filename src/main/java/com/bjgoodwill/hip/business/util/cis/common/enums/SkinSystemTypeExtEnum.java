package com.bjgoodwill.hip.business.util.cis.common.enums;

import java.util.Arrays;

/**
 * @program: HIP5.0-BASE
 * @author: xdguo
 * @create: 2024-10-16 11:19
 * @className: SKINSystemTypeExtEnum
 * @description:皮试类的医嘱类型，扩展枚举
 **/
public enum SkinSystemTypeExtEnum {
    DOPE("DOPE", "皮试原液"),
    SOL("SOL", "皮试液");

    private String code;
    private String name;

    SkinSystemTypeExtEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SkinSystemTypeExtEnum getByCode(String code) {
        return Arrays.stream(SkinSystemTypeExtEnum.values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
