package com.bjgoodwill.hip.business.util.pat.enums;

import com.bjgoodwill.hip.common.bean.EnumTo;

import java.util.ArrayList;
import java.util.List;


/**
 * 住院患者信息扩展枚举类
 *
 * <AUTHOR>
 */
public enum PatIpdInpatientExtEnum {
    护理常规("ROUTINE_CARE"),
    病情("CRITICAL_CARE_PATIENT"),
    术前("DIET"),
    术后天数("POSITION"),
    路径患者("ACCOMPANYING");

    private final String code;

    PatIpdInpatientExtEnum(String code) {
        this.code = code;
    }

    public static boolean validate(String code) {
        return PatIpdInpatientExtEnum.getEnum(code) != null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getName(String code) {
        for (PatIpdInpatientExtEnum enumItem : PatIpdInpatientExtEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem.name();
            }
        }
        return null;
    }

    public static PatIpdInpatientExtEnum getEnum(String code) {
        for (PatIpdInpatientExtEnum enumItem : PatIpdInpatientExtEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 获取List列表
     */
    public static List<EnumTo<String>> getList() {
        List<EnumTo<String>> list = new ArrayList<>();
        EnumTo<String> enumTo;
        for (PatIpdInpatientExtEnum enumItem : PatIpdInpatientExtEnum.values()) {
            enumTo = new EnumTo<>();
            enumTo.setCode(enumItem.getCode());
            enumTo.setName(enumItem.name());
            list.add(enumTo);
        }
        return list;
    }

    public String getCode() {
        return code;
    }

}