package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: hip-base
 * @author: xdguo
 * @create: 2025-01-17 13:31
 * @className: CisFeeExecEnum
 * @description: 费用执行科室归属枚举
 **/
public enum CisFeeExecEnum {
    //    IN_DEPT_CODE("IN_DEPT_CODE", "对应护理组"),
    IN_NURSE_CODE("IN_NURSE_CODE", "对应护理组"),
    EXEC_ORG_CODE("EXEC_ORG_CODE", "医嘱执行科室"),
    CREATE_CODE("CREATE_CODE", "开单科室");

    private String code;
    private String name;

    CisFeeExecEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
