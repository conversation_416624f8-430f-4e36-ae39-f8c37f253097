package com.bjgoodwill.hip.business.util.econ.enums;

/**
 * 交易类型
 */
public enum TransactionTypeEnum {

    IN("IN", "收"),
    OUT("OUT", "退");

    private final String code;
    private final String name;

    TransactionTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (TransactionTypeEnum value : TransactionTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}