package com.bjgoodwill.hip.business.util.mate.enums;

import com.bjgoodwill.hip.common.bean.EnumTo;

import java.util.ArrayList;
import java.util.List;


/**
 * 物资收费类型枚举
 *
 * <AUTHOR>
 */
public enum MateChargeTypeEnum {

    全部收费("1"),
    门诊收费("2"),
    住院收费("3"),
    不收费("4");

    private final String code;

    MateChargeTypeEnum(String code) {
        this.code = code;
    }

    public static boolean validate(String code) {
        return MateChargeTypeEnum.getEnum(code) != null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getName(String code) {
        for (MateChargeTypeEnum enumItem : MateChargeTypeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem.name();
            }
        }
        return null;
    }

    public static MateChargeTypeEnum getEnum(String code) {
        for (MateChargeTypeEnum enumItem : MateChargeTypeEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 获取List列表
     */
    public static List<EnumTo<String>> getList() {
        List<EnumTo<String>> list = new ArrayList<>();
        EnumTo<String> enumTo;
        for (MateChargeTypeEnum enumItem : MateChargeTypeEnum.values()) {
            enumTo = new EnumTo<>();
            enumTo.setCode(enumItem.getCode());
            enumTo.setName(enumItem.name());
            list.add(enumTo);
        }
        return list;
    }

    public String getCode() {
        return code;
    }

}