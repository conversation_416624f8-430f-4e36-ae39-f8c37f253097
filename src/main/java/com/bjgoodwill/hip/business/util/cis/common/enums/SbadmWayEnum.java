package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-22 09:41
 * @className: SbadmWayEnum
 * @description:
 **/
public enum SbadmWayEnum {

    /**
     * 1.直接执行
     */
    NORMAL("01", "正常"),
    /**
     * 2.转诊
     */
    DISCHARGE("02", "出院带药"),
    /**
     * 3.转院
     */
    NOTAKE("03", "不取药"),

    WHOLETAKE("04", "整取");


    private final String message;

    SbadmWayEnum(String code, String message) {
        this.message = message;
    }

    public String getCode() {
        return this.name();
    }

    public String getMessage() {
        return message;
    }
}