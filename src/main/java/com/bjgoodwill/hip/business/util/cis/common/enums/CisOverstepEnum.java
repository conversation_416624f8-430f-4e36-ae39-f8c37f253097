package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-03-07 10:40
 * @className: CisDoctDrugAuthorityEnum
 * @description: 越级审批类型枚举
 **/

/**
 * 越级审批类型枚举
 * 用于定义不同类型的越级审批及其对应的权限代码
 */
public enum CisOverstepEnum {

    /**
     * 抗菌药枚举实例
     */
    ANTITRAINING("ANTITRAINING", "抗菌药"),

    /**
     * 肿瘤药枚举实例
     */
    ONCOLOGY("ONCOLOGY", "肿瘤药"),
    /**
     * 输血枚举实例
     */
    BLOOD("BLOOD", "输血"),
    /**
     * 手术药枚举实例
     */
    OPERATION("OPERATION", "手术");

    /**
     * 权限代码
     */
    private String code;

    /**
     * 药品名称
     */
    private String name;

    /**
     * 构造方法
     * 初始化药品权限枚举实例
     *
     * @param code 权限代码
     * @param name 药品名称
     */
    CisOverstepEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据权限代码获取对应的药品权限枚举实例
     *
     * @param code 权限代码
     * @return 对应的药品权限枚举实例，如果找不到匹配的则返回null
     */
    public static CisOverstepEnum getEnumByCode(String code) {
        for (CisOverstepEnum cisOverstepEnum : CisOverstepEnum.values()) {
            if (cisOverstepEnum.getCode().equals(code)) {
                return cisOverstepEnum;
            }
        }
        return null;
    }

    /**
     * 获取权限代码
     *
     * @return 权限代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取药品名称
     *
     * @return 药品名称
     */
    public String getName() {
        return name;
    }


}

