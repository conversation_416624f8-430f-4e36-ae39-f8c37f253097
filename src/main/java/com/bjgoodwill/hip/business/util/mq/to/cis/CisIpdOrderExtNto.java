package com.bjgoodwill.hip.business.util.mq.to.cis;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-10-09 09:16
 * @className: CisIpdOrderExtNto
 * @description:
 **/
public class CisIpdOrderExtNto implements Serializable {
    @Schema(description = "住院医嘱标识")
    private String orderId;
    @Schema(description = "key")
    private String key;
    @Schema(description = "value")
    private String value;

    @Schema(description = "患者流水号")
    private String visitCode;

    @NotBlank(message = "住院医嘱标识不能为空！")
    @Size(max = 50, message = "住院医嘱标识长度不能超过50个字符！")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = StringUtils.trimToNull(orderId);
    }

    @NotBlank(message = "key不能为空！")
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = StringUtils.trimToNull(key);
    }

    @NotBlank(message = "value不能为空！")
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = StringUtils.trimToNull(value);
    }

    @NotBlank(message = "患者流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }
}