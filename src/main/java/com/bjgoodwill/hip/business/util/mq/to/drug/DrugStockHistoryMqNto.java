package com.bjgoodwill.hip.business.util.mq.to.drug;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "发药库存历史NTO")
public class DrugStockHistoryMqNto implements Serializable {

	@Serial
    private static final long serialVersionUID = -3591498950823271237L;

    @Schema(description = "业务ID")
    private String id;
    @Schema(description = "库存ID")
    private String drugStockId;
    @Schema(description = "操作前数量")
    private BigDecimal beforeNum;
    @Schema(description = "操作数量")
    private BigDecimal num;
    @Schema(description = "操作后数量")
    private BigDecimal afterNum;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;

    @NotBlank(message = "业务ID不能为空！")
    @Size(max = 50, message = "业务ID长度不能超过50个字符！")
    public String getId() {
    	return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    public String getDrugStockId() {
    	return drugStockId;
    }

    public void setDrugStockId(String drugStockId) {
        this.drugStockId = StringUtils.trimToNull(drugStockId);
    }

    public BigDecimal getBeforeNum() {
    	return beforeNum;
    }

    public void setBeforeNum(BigDecimal beforeNum) {
        this.beforeNum = beforeNum;
    }

    public BigDecimal getNum() {
    	return num;
    }

    public void setNum(BigDecimal num) {
        this.num = num;
    }

    public BigDecimal getAfterNum() {
    	return afterNum;
    }

    public void setAfterNum(BigDecimal afterNum) {
        this.afterNum = afterNum;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
}