package com.bjgoodwill.hip.business.util.cis.common;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "CisOrgCommonNto")
public class CisOrgCommonNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2290865502228247860L;

    @Schema(description = "工作组编码")
    private String orgCode;
    @Schema(description = "工作组名称")
    private String orgName;

    @NotBlank(message = "工作组编码不能为空！")
    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    @NotBlank(message = "工作组名称不能为空！")
    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}