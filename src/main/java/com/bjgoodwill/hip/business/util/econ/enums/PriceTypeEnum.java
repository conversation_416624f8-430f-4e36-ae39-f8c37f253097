package com.bjgoodwill.hip.business.util.econ.enums;

/**
 * 单价类型
 */
public enum PriceTypeEnum {

    NONE("NONE", "正常"),

    CHILD("CHILD", "儿童价"),

    SPECIAL("SPECIAL", "特需价");

    private final String code;
    private final String name;

    PriceTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (PriceTypeEnum value : PriceTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}