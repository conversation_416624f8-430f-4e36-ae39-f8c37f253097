package com.bjgoodwill.hip.business.util.cis.common.enums;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-09-23 09:22
 * @className: DiagnosisTypeEnum
 * @description:
 **/
public enum DiagnosisTypeEnum {
    A("A", "中医门诊诊断"),
    B("B", "入院诊断"),
    C("C", "出院诊断"),
    D("D", "病理诊断"),
    E("E", "院内感染"),
    F("F", "损伤和中毒的外部原因"),
    G("G", "手术并发症"),
    H("H", "并发症"),
    I("I", "中医入院诊断"),
    J("J", "中医出院诊断"),
    K("K", "初步诊断"),
    L("L", "确定诊断"),
    M("M", "修正诊断"),
    N("N", "补充诊断"),
    O("O", "术前诊断"),
    P("P", "术后诊断"),
    Q("Q", "门诊诊断"),
    R("R", "中医诊断");

    private String code;
    private String name;

    DiagnosisTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
