package com.bjgoodwill.hip.business.util.mq.to.drug;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "药品调价盈亏")
public class DrugPricePalNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -3152621859103784455L;

    @Schema(description = "药品调价明细标识")
    private String drugPriceChangeDetailId;
    @Schema(description = "数据类型（调价、退药）")
    private String dataType;
    @Schema(description = "门诊住院退药ID")
    private String drugRefundId;
    @Schema(description = "库房编码")
    private String storageCode;
    @Schema(description = "库房名称")
    private String storageName;
    @Schema(description = "药品编码")
    private String drugGoodsCode;
    @Schema(description = "药品名称")
    private String drugGoodsName;
    @Schema(description = "药品类别")
    private String drugType;
    @Schema(description = "药品类别名称")
    private String drugTypeName;
    @Schema(description = "规格")
    private String drugSpec;
    @Schema(description = "包装单位名称")
    private String maxUnit;
    @Schema(description = "小单位名称")
    private String minUnit;
    @Schema(description = "包装数")
    private Integer packageNum;
    @Schema(description = "批号")
    private String batchNo;
    @Schema(description = "入库次")
    private String batchNumNo;
    @Schema(description = "库存总数量")
    private BigDecimal quantity;
    @Schema(description = "门诊已收费未发药数量")
    private BigDecimal opdNum;
    @Schema(description = "住院已计费未发药数量")
    private BigDecimal ipdNum;
    @Schema(description = "调价数量")
    private BigDecimal changeNum;
    @Schema(description = "调价数量大包装")
    private BigDecimal changeNumMax;
    @Schema(description = "调价数量小包装")
    private BigDecimal changeNumMin;
    @Schema(description = "原购入价")
    private BigDecimal oldPurchasePrice;
    @Schema(description = "新购入价")
    private BigDecimal newPurchasePrice;
    @Schema(description = "原零售价")
    private BigDecimal oldSalePrice;
    @Schema(description = "新零售价")
    private BigDecimal newSalePrice;
    @Schema(description = "购入价调价总额")
    private BigDecimal changePurchaseAmount;
    @Schema(description = "零售价调价总额")
    private BigDecimal changeSaleAmount;

    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;

    public String getDrugPriceChangeDetailId() {
        return drugPriceChangeDetailId;
    }

    public void setDrugPriceChangeDetailId(String drugPriceChangeDetailId) {
        this.drugPriceChangeDetailId = StringUtils.trimToNull(drugPriceChangeDetailId);
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = StringUtils.trimToNull(dataType);
    }

    public String getDrugRefundId() {
        return drugRefundId;
    }

    public void setDrugRefundId(String drugRefundId) {
        this.drugRefundId = StringUtils.trimToNull(drugRefundId);
    }

    public String getStorageCode() {
        return storageCode;
    }

    public void setStorageCode(String storageCode) {
        this.storageCode = StringUtils.trimToNull(storageCode);
    }

    public String getStorageName() {
        return storageName;
    }

    public void setStorageName(String storageName) {
        this.storageName = StringUtils.trimToNull(storageName);
    }

    public String getDrugGoodsCode() {
        return drugGoodsCode;
    }

    public void setDrugGoodsCode(String drugGoodsCode) {
        this.drugGoodsCode = StringUtils.trimToNull(drugGoodsCode);
    }

    public String getDrugGoodsName() {
        return drugGoodsName;
    }

    public void setDrugGoodsName(String drugGoodsName) {
        this.drugGoodsName = StringUtils.trimToNull(drugGoodsName);
    }

    public String getDrugType() {
        return drugType;
    }

    public void setDrugType(String drugType) {
        this.drugType = StringUtils.trimToNull(drugType);
    }

    public String getDrugTypeName() {
        return drugTypeName;
    }

    public void setDrugTypeName(String drugTypeName) {
        this.drugTypeName = StringUtils.trimToNull(drugTypeName);
    }

    public String getDrugSpec() {
        return drugSpec;
    }

    public void setDrugSpec(String drugSpec) {
        this.drugSpec = StringUtils.trimToNull(drugSpec);
    }

    public String getMaxUnit() {
        return maxUnit;
    }

    public void setMaxUnit(String maxUnit) {
        this.maxUnit = StringUtils.trimToNull(maxUnit);
    }

    public String getMinUnit() {
        return minUnit;
    }

    public void setMinUnit(String minUnit) {
        this.minUnit = StringUtils.trimToNull(minUnit);
    }

    public Integer getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Integer packageNum) {
        this.packageNum = packageNum;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = StringUtils.trimToNull(batchNo);
    }

    public String getBatchNumNo() {
        return batchNumNo;
    }

    public void setBatchNumNo(String batchNumNo) {
        this.batchNumNo = StringUtils.trimToNull(batchNumNo);
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getOpdNum() {
        return opdNum;
    }

    public void setOpdNum(BigDecimal opdNum) {
        this.opdNum = opdNum;
    }

    public BigDecimal getIpdNum() {
        return ipdNum;
    }

    public void setIpdNum(BigDecimal ipdNum) {
        this.ipdNum = ipdNum;
    }

    public BigDecimal getChangeNum() {
        return changeNum;
    }

    public void setChangeNum(BigDecimal changeNum) {
        this.changeNum = changeNum;
    }

    public BigDecimal getChangeNumMax() {
        return changeNumMax;
    }

    public void setChangeNumMax(BigDecimal changeNumMax) {
        this.changeNumMax = changeNumMax;
    }

    public BigDecimal getChangeNumMin() {
        return changeNumMin;
    }

    public void setChangeNumMin(BigDecimal changeNumMin) {
        this.changeNumMin = changeNumMin;
    }

    public BigDecimal getOldPurchasePrice() {
        return oldPurchasePrice;
    }

    public void setOldPurchasePrice(BigDecimal oldPurchasePrice) {
        this.oldPurchasePrice = oldPurchasePrice;
    }

    public BigDecimal getNewPurchasePrice() {
        return newPurchasePrice;
    }

    public void setNewPurchasePrice(BigDecimal newPurchasePrice) {
        this.newPurchasePrice = newPurchasePrice;
    }

    public BigDecimal getOldSalePrice() {
        return oldSalePrice;
    }

    public void setOldSalePrice(BigDecimal oldSalePrice) {
        this.oldSalePrice = oldSalePrice;
    }

    public BigDecimal getNewSalePrice() {
        return newSalePrice;
    }

    public void setNewSalePrice(BigDecimal newSalePrice) {
        this.newSalePrice = newSalePrice;
    }

    public BigDecimal getChangePurchaseAmount() {
        return changePurchaseAmount;
    }

    public void setChangePurchaseAmount(BigDecimal changePurchaseAmount) {
        this.changePurchaseAmount = changePurchaseAmount;
    }

    public BigDecimal getChangeSaleAmount() {
        return changeSaleAmount;
    }

    public void setChangeSaleAmount(BigDecimal changeSaleAmount) {
        this.changeSaleAmount = changeSaleAmount;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

}