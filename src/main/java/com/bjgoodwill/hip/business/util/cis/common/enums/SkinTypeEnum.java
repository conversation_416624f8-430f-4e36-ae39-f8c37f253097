package com.bjgoodwill.hip.business.util.cis.common.enums;

public enum SkinTypeEnum {
    FEMININE("-", "阴"),
    MASCULINE("+", "阳");

    private String code;
    private String name;

    SkinTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SkinTypeEnum getByCode(String code) {
        for (SkinTypeEnum skinTypeEnum : SkinTypeEnum.values()) {
            if (skinTypeEnum.getCode().equals(code)) {
                return skinTypeEnum;
            }
        }
        return null;
    }

    public static SkinTypeEnum fromString(String text) {
        if (text == null) return null;
        for (SkinTypeEnum type : values()) {
            if (type.name().equalsIgnoreCase(text.trim())) {
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}