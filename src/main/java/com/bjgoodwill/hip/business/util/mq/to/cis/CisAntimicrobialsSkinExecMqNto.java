package com.bjgoodwill.hip.business.util.mq.to.cis;


import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "抗菌药皮试药执行记录")
public class CisAntimicrobialsSkinExecMqNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -301217308638535881L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "患者类型")
    private VisitTypeEnum visitType;
    @Schema(description = "系统类型")
    private SystemTypeEnum systemType;
    @Schema(description = "服务项目编码")
    private String serviceItemCode;
    @Schema(description = "服务项目名称")
    private String serviceItemName;
    @Schema(description = "医嘱号")
    private String orderId;
    @Schema(description = "申请单号")
    private String applyId;
    @Schema(description = "执行记录id")
    private String execPlanId;
    @Schema(description = "执行时间")
    private LocalDateTime execPlanDate;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = StringUtils.trimToNull(patMiCode);
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = StringUtils.trimToNull(serviceItemCode);
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = StringUtils.trimToNull(serviceItemName);
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = StringUtils.trimToNull(orderId);
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = StringUtils.trimToNull(applyId);
    }

    public String getExecPlanId() {
        return execPlanId;
    }

    public void setExecPlanId(String execPlanId) {
        this.execPlanId = StringUtils.trimToNull(execPlanId);
    }

    public LocalDateTime getExecPlanDate() {
        return execPlanDate;
    }

    public void setExecPlanDate(LocalDateTime execPlanDate) {
        this.execPlanDate = execPlanDate;
    }
}