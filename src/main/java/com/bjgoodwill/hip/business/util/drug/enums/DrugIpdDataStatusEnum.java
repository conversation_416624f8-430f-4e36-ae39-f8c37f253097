package com.bjgoodwill.hip.business.util.drug.enums;

import com.bjgoodwill.hip.common.bean.EnumTo;

import java.util.ArrayList;
import java.util.List;


/**
 * 发药状态显示
 *
 * <AUTHOR>
 */
public enum DrugIpdDataStatusEnum {
    未发("1"),
    未退("2"),
    已发("3"),
    已退("4");

    private final String code;

    DrugIpdDataStatusEnum(String code) {
        this.code = code;
    }

    public static boolean validate(String code) {
        return DrugIpdDataStatusEnum.getEnum(code) != null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getName(String code) {
        for (DrugIpdDataStatusEnum enumItem : DrugIpdDataStatusEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem.name();
            }
        }
        return null;
    }

    public static DrugIpdDataStatusEnum getEnum(String code) {
        for (DrugIpdDataStatusEnum enumItem : DrugIpdDataStatusEnum.values()) {
            if (enumItem.getCode().equalsIgnoreCase(code)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 获取List列表
     */
    public static List<EnumTo<String>> getList() {
        List<EnumTo<String>> list = new ArrayList<>();
        EnumTo<String> enumTo;
        for (DrugIpdDataStatusEnum enumItem : DrugIpdDataStatusEnum.values()) {
            enumTo = new EnumTo<>();
            enumTo.setCode(enumItem.getCode());
            enumTo.setName(enumItem.name());
            list.add(enumTo);
        }
        return list;
    }

    public String getCode() {
        return code;
    }
}