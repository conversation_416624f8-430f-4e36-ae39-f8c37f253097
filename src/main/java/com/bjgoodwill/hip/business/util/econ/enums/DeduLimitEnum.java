package com.bjgoodwill.hip.business.util.econ.enums;

public enum DeduLimitEnum {

    ALL("ALL", "全可用"),
    OPD("OPD", "门诊可用"),
    IPD("IPD", "住院可用");

    private final String code;
    private final String name;

    DeduLimitEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (DeduLimitEnum value : DeduLimitEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}