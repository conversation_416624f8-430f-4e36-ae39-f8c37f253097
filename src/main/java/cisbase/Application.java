package cisbase;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@EntityScan(basePackages = {"com.bjgoodwill.hip"})
@EnableJpaRepositories(basePackages = {"com.bjgoodwill.hip"})
@SpringBootApplication(scanBasePackages = {"com.bjgoodwill.hip"})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        System.out.println("hip-cis-base-dc 服务启动成功...");
    }

}
