server:
  port: 8188
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
spring:
  #应用唯一标识
  application:
    name: hip-cis-base-dc
  #配置文件
  profiles:
    active: @profiles.active@
  mvc:
    servlet:
      load-on-startup: 1
  aop:
    proxy-target-class: true
  cache:
    type: redis
####################################
# 健康检查配置 http//ip:port/health
####################################
management:
  endpoints:
    web:
      base-path: /
  endpoint:
    health:
      show-details: ALWAYS
springdoc:
  swagger-ui:
    # 修改Swagger UI路径
    path: /swagger-ui.html
    # 开启Swagger UI界面
    enabled: true
  api-docs:
    # 修改api-docs路径
    path: /v3/api-docs
    # 开启api-docs
    enabled: true
  group-configs:
    - group: 999
      displayName: 全部
      packagesToScan: com.bjgoodwill.hip
    #    - group: 1
    #      displayName: 用法
    #      packagesToScan: com.bjgoodwill.hip.ds.base.cis.dict.usage
    #    - group: 2
    #      displayName: 频次
    #      packagesToScan: com.bjgoodwill.hip.ds.base.cis.dict.frequency
    #    - group: 3
    #      displayName: 诊断
    #      packagesToScan: com.bjgoodwill.hip.ds.base.cis.diagnose
    - group: 5
      displayName: 流程控制
      packagesToScan: com.bjgoodwill.hip.ds.cis.rule
    - group: 6
      displayName: 临床辅助与决策支持
      packagesToScan: com.bjgoodwill.hip.ds.cis.cds
    - group: 8
      displayName: 医技医嘱
      packagesToScan: com.bjgoodwill.hip.ds.cis.mtcpoe
    - group: 12
      displayName: 申请单
      packagesToScan: com.bjgoodwill.hip.ds.cis.apply
    - group: 13
      displayName: 临床数据服务
      packagesToScan: com.bjgoodwill.hip.ds.cis.cdr
logging:
  file:
    path: ./logs
    name: hip-cis-dc
  level:
    com.hexadecimal: debug