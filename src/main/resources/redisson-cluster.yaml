  集群配置
  clusterServersConfig:
    # 连接空闲超时，单位：毫秒，默认10000
    idleConnectionTimeout: 10000
    # 连接超时，单位：毫秒，默认10000
    connectTimeout: 10000
    # 命令等待超时，单位：毫秒，默认3000
    timeout: 3000
    # 命令失败重试次数，默认3
    retryAttempts: 3
    # 命令重试发送时间间隔，单位：毫秒，默认1500
    retryInterval: 1500
    # Slave从可用服务器的内部列表中排除时重新连接尝试的间隔。在每次超时事件中Redisson都会尝试连接到断开连接的Redis服务器。值（以毫秒为单位）(默认3000)
    failedSlaveReconnectionInterval: 3000
    failedSlaveCheckInterval: 60000
    # 密码
    #  password: null
    # 定义每个连接到Redis的PING命令发送间隔，默认0
    pingConnectionInterval: 0
    # 客户端名称，默认null
    clientName: null
    # 多个Redis服务器的连接负载均衡器(默认org.redisson.connection.balancer.RoundRobinLoadBalancer)，可用值：
    # org.redisson.connection.balancer.CommandsLoadBalancer
    # org.redisson.connection.balancer.WeightedRoundRobinBalancer
    # org.redisson.connection.balancer.RoundRobinLoadBalancer
    # org.redisson.connection.balancer.RandomLoadBalancer
    loadBalancer: !<org.redisson.connection.balancer.RoundRobinLoadBalancer> { }
    # 单个连接最大订阅数量(默认5)
    subscriptionsPerConnection: 5
    # 发布和订阅连接的最小空闲连接数(默认1)
    subscriptionConnectionMinimumIdleSize: 1
    # 发布和订阅连接池大小(默认50)
    subscriptionConnectionPoolSize: 50
    # ‘slave’节点每个从节点的最小空闲连接量(默认24)
    slaveConnectionMinimumIdleSize: 24
    # “slave”节点每个从节点的最大连接池大小(默认64)
    slaveConnectionPoolSize: 64
    # 每个Redis主节点的最小空闲连接数(默认24)
    masterConnectionMinimumIdleSize: 24
    # “master”节点的最大连接池大小(默认64)
    masterConnectionPoolSize: 64
    # 设置用于读取操作的节点类型(默认SLAVE)
    # SLAVE：如果没有可用的从节点则使用MASTER
    # MASTER：从主节点读取
    # MASTER_SLAVE：从主节点和从节点读取
    readMode: "SLAVE"
    # 设置用于订阅操作的节点类型(默认MASTER)
    # SLAVE-订阅从节点
    # MASTER-订阅主节点
    subscriptionMode: "SLAVE"
    # 以host:port格式添加Redis集群节点或Redis端点地址。Redisson自动发现集群拓扑。使用redis://协议进行SSL连接
    nodeAddresses:
      - "redis://127.0.0.1:7004"
      - "redis://127.0.0.1:7001"
      - "redis://127.0.0.1:7000"
    # 定义代理节点扫描间隔（以毫秒为单位）。0表示禁用(默认5000)
    scanInterval: 1000
    # 为连接启用TCP keepAlive(默认false)
    keepAlive: false
    # 为连接启用TCP noDelay(默认true)
    tcpNoDelay: false
  # 线程池数量(默认16)，线程用于执行RTopic对象的侦听器逻辑、RRemoteService、RTopic目标和RExecutorService任务的调用处理程序
  threads: 16
  # Netty线程池数量(默认32)，Redisson使用的所有内部redis客户端之间共享的线程数。Netty线程用于Redis响应解码和命令发送。0 = cores_amount * 2
  nettyThreads: 32
  # Redis数据编解码器，在读写Redis数据时使用。(默认org.redisson.codec.Kryo5Codec)
  codec: !<org.redisson.codec.JsonJacksonCodec> { }
  transportMode: "NIO"
