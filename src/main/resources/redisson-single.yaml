# 单节点配置
singleServerConfig:
  # 节点地址
  address: "redis://*************:6381"
  # 密码
  password: ^5kTgji@xxre]{s
  # 数据库编号，默认0
  database: 1
  # 连接空闲超时，单位：毫秒，默认10000
  idleConnectionTimeout: 10000
  # 连接超时，单位：毫秒，默认10000
  connectTimeout: 20000
  # 命令等待超时，单位：毫秒，默认3000
  timeout: 20000
  # 命令失败重试次数，默认3
  retryAttempts: 3
  # 命令重试发送时间间隔，单位：毫秒，默认1500
  retryInterval: 1500
  # 单个连接最大订阅数量，默认5
  subscriptionsPerConnection: 5
  # 定义每个连接到Redis的PING命令发送间隔，默认0
  pingConnectionInterval: 5000
  # 客户端名称，默认null
  clientName: null
  # 发布和订阅连接的最小空闲连接数(默认1)
  subscriptionConnectionMinimumIdleSize: 1
  # 发布和订阅连接池大小(默认50)
  subscriptionConnectionPoolSize: 50
  # 最小空闲连接数(默认32)
  connectionMinimumIdleSize: 10
  # 连接池大小(默认64)
  connectionPoolSize: 64
# 线程池数量(默认16)，线程用于执行RTopic对象的侦听器逻辑、RRemoteService、RTopic目标和RExecutorService任务的调用处理程序
threads: 2
# Netty线程池数量(默认32)，Redisson使用的所有内部redis客户端之间共享的线程数。Netty线程用于Redis响应解码和命令发送。0 = cores_amount * 2
nettyThreads: 2
# Redis数据编解码器，在读写Redis数据时使用。(默认org.redisson.codec.Kryo5Codec)
codec: !<org.redisson.codec.JsonJacksonCodec> { }