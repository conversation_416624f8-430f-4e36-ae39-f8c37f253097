package com.bjgoodwill.hip.ds.cis.mtcpoe.order.to.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SkinTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医技医嘱")
public class CisMtOrderNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1697827075141250907L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "来源申请单ID")
    private String sApplyId;
    @Schema(description = "医嘱序号")
    private Double sortNo;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "医嘱编码")
    private String orderServiceCode;
    @Schema(description = "医嘱内容")
    private String orderContent;
    @Schema(description = "医嘱类型")
    private SystemTypeEnum orderClass;
    @Schema(description = "申请单号")
    private String applyCode;
    @Schema(description = "执行科室编码")
    private String executeOrgCode;
    @Schema(description = "执行科室名称")
    private String executeOrgName;
    @Schema(description = "创建医生科室")
    private String createOrgCode;
    @Schema(description = "备注")
    private String reMark;
    @Schema(description = "状态")
    private CisStatusEnum statusCode;
    @Schema(description = "领药科室")
    private String receiveOrgCode;
    @Schema(description = "特显符合标识 1符合 0不符合 null不是特限项目")
    private Boolean limitConformFlag;
    @Schema(description = "医院编码")
    private String hospitalCode;
    @Schema(description = "是否皮试")
    private Boolean skinFlag;
    @Schema(description = "取药方式")
    private SbadmWayEnum sbadmWay;
    @Schema(description = "皮试结果")
    private SkinTypeEnum skinType;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "来源申请单ID不能为空！")
    public String getSApplyId() {
        return sApplyId;
    }

    public void setSApplyId(String sApplyId) {
        this.sApplyId = StringUtils.trimToNull(sApplyId);
    }

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    @NotBlank(message = "主索引不能为空！")
    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = StringUtils.trimToNull(patMiCode);
    }

    @NotBlank(message = "流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    public String getOrderServiceCode() {
        return orderServiceCode;
    }

    public void setOrderServiceCode(String orderServiceCode) {
        this.orderServiceCode = StringUtils.trimToNull(orderServiceCode);
    }

    @Size(max = 1024, message = "医嘱内容长度不能超过1,024个字符！")
    public String getOrderContent() {
        return orderContent;
    }

    public void setOrderContent(String orderContent) {
        this.orderContent = StringUtils.trimToNull(orderContent);
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    @NotBlank(message = "申请单号不能为空！")
    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = StringUtils.trimToNull(applyCode);
    }

    @NotBlank(message = "执行科室编码不能为空！")
    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    public void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = StringUtils.trimToNull(executeOrgCode);
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = StringUtils.trimToNull(executeOrgName);
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = StringUtils.trimToNull(createOrgCode);
    }

    public String getReMark() {
        return reMark;
    }

    public void setReMark(String reMark) {
        this.reMark = StringUtils.trimToNull(reMark);
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public String getReceiveOrgCode() {
        return receiveOrgCode;
    }

    public void setReceiveOrgCode(String receiveOrgCode) {
        this.receiveOrgCode = StringUtils.trimToNull(receiveOrgCode);
    }

    public Boolean getLimitConformFlag() {
        return limitConformFlag;
    }

    public void setLimitConformFlag(Boolean limitConformFlag) {
        this.limitConformFlag = limitConformFlag;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = StringUtils.trimToNull(hospitalCode);
    }

    public Boolean getSkinFlag() {
        return skinFlag;
    }

    public void setSkinFlag(Boolean skinFlag) {
        this.skinFlag = skinFlag;
    }

    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }

    public SkinTypeEnum getSkinType() {
        return skinType;
    }

    public void setSkinType(SkinTypeEnum skinType) {
        this.skinType = skinType;
    }
}