package com.bjgoodwill.hip.ds.cis.mtcpoe.order.to.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SkinTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医技医嘱")
public class CisMtOrderQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -9189372103442891981L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "来源申请单ID")
    private String sApplyId;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "医嘱编码")
    private String orderServiceCode;
    @Schema(description = "医嘱内容")
    private String orderContent;
    @Schema(description = "医嘱类型")
    private SystemTypeEnum orderClass;
    @Schema(description = "申请单号")
    private String applyCode;
    @Schema(description = "执行科室编码")
    private String executeOrgCode;
    @Schema(description = "创建医生科室")
    private String createOrgCode;
    @Schema(description = "备注")
    private String reMark;
    @Schema(description = "状态")
    private CisStatusEnum statusCode;
    @Schema(description = "领药科室")
    private String receiveOrgCode;
    @Schema(description = "特显符合标识 1符合 0不符合 null不是特限项目")
    private Boolean limitConformFlag;
    @Schema(description = "医院编码")
    private String hospitalCode;
    @Schema(description = "是否皮试")
    private Boolean skinFlag;
    @Schema(description = "取药方式")
    private SbadmWayEnum sbadmWay;
    @Schema(description = "皮试结果")
    private SkinTypeEnum skinType;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getSApplyId() {
        return sApplyId;
    }

    public void setSApplyId(String sApplyId) {
        this.sApplyId = sApplyId;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getOrderServiceCode() {
        return orderServiceCode;
    }

    public void setOrderServiceCode(String orderServiceCode) {
        this.orderServiceCode = orderServiceCode;
    }

    public String getOrderContent() {
        return orderContent;
    }

    public void setOrderContent(String orderContent) {
        this.orderContent = orderContent;
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    public void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = executeOrgCode;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public String getReMark() {
        return reMark;
    }

    public void setReMark(String reMark) {
        this.reMark = reMark;
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public String getReceiveOrgCode() {
        return receiveOrgCode;
    }

    public void setReceiveOrgCode(String receiveOrgCode) {
        this.receiveOrgCode = receiveOrgCode;
    }

    public Boolean getLimitConformFlag() {
        return limitConformFlag;
    }

    public void setLimitConformFlag(Boolean limitConformFlag) {
        this.limitConformFlag = limitConformFlag;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public Boolean getSkinFlag() {
        return skinFlag;
    }

    public void setSkinFlag(Boolean skinFlag) {
        this.skinFlag = skinFlag;
    }

    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }

    public SkinTypeEnum getSkinType() {
        return skinType;
    }

    public void setSkinType(SkinTypeEnum skinType) {
        this.skinType = skinType;
    }
}