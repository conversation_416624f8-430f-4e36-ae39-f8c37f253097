package com.bjgoodwill.hip.ds.cis.mtcpoe.order.service.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.mtcpoe.order.to.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "医技医嘱领域服务", description = "医技医嘱领域服务")
public interface CisMtOrderService {

    @Operation(summary = "根据查询条件对医技医嘱进行查询。")
    @GetMapping("/cisMtOrders")
    List<CisMtOrderTo> getCisMtOrders(@ParameterObject @SpringQueryMap CisMtOrderQto cisMtOrderQto);

    @Operation(summary = "根据查询条件对医技医嘱进行分页查询。")
    @GetMapping("/cisMtOrders/pages")
    GridResultSet<CisMtOrderTo> getCisMtOrderPage(@ParameterObject @SpringQueryMap CisMtOrderQto cisMtOrderQto);

    @Operation(summary = "根据唯一标识返回医技医嘱。")
    @GetMapping("/cisMtOrders/{id:.+}")
    CisMtOrderTo getCisMtOrderById(@PathVariable("id") String id);

    @Operation(summary = "创建医技医嘱。")
    @PostMapping("/cisMtOrders")
    CisMtOrderTo createCisMtOrder(@RequestBody @Valid CisMtOrderNto cisMtOrderNto);

    @Operation(summary = "根据唯一标识修改医技医嘱。")
    @PutMapping("/cisMtOrders/{id:.+}")
    void updateCisMtOrder(@PathVariable("id") String id, @RequestBody @Valid CisMtOrderEto cisMtOrderEto);

    @Operation(summary = "根据唯一标识删除医技医嘱。")
    @DeleteMapping("/cisMtOrders/{id:.+}")
    void deleteCisMtOrder(@PathVariable("id") String id);

    @Operation(summary = "创建医技医嘱日志。")
    @PostMapping("/cisMtOrders/{orderId}/cisMtOrderExecLogs")
    CisMtOrderExecLogTo createCisMtOrderExecLog(@PathVariable("orderId") String orderId, @RequestBody @Valid CisMtOrderExecLogNto cisMtOrderExecLogNto);

    @Operation(summary = "根据唯一标识修改医技医嘱日志。")
    @PutMapping("/cisMtOrders/xId/cisMtOrderExecLogs/{id:.+}")
    void updateCisMtOrderExecLog(@PathVariable("id") String id, @RequestBody @Valid CisMtOrderExecLogEto cisMtOrderExecLogEto);

    @Operation(summary = "根据唯一标识删除医技医嘱日志。")
    @DeleteMapping("/cisMtOrders/xId/cisMtOrderExecLogs/{id:.+}")
    void deleteCisMtOrderExecLog(@PathVariable("id") String id);

}