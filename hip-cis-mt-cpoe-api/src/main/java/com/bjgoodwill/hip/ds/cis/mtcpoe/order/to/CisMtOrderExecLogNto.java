package com.bjgoodwill.hip.ds.cis.mtcpoe.order.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.ExecLogEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医技医嘱日志")
public class CisMtOrderExecLogNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6593809166401344413L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "医嘱序号")
    private Double orderNo;
    @Schema(description = "医嘱名称")
    private String orderName;
    @Schema(description = "操作类型")
    private ExecLogEnum execLogType;
    @Schema(description = "备注")
    private String reMark;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = StringUtils.trimToNull(patMiCode);
    }

    @NotBlank(message = "流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    public Double getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Double orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = StringUtils.trimToNull(orderName);
    }

    public ExecLogEnum getExecLogType() {
        return execLogType;
    }

    public void setExecLogType(ExecLogEnum execLogType) {
        this.execLogType = execLogType;
    }

    public String getReMark() {
        return reMark;
    }

    public void setReMark(String reMark) {
        this.reMark = StringUtils.trimToNull(reMark);
    }
}