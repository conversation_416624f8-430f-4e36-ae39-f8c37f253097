package com.bjgoodwill.hip.ds.cis.mtcpoe.order.to.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.ExecLogEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医技医嘱日志")
public class CisMtOrderExecLogEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -3983718886608500928L;

    @Schema(description = "操作类型")
    private ExecLogEnum execLogType;

    public ExecLogEnum getExecLogType() {
        return execLogType;
    }

    public void setExecLogType(ExecLogEnum execLogType) {
        this.execLogType = execLogType;
    }
}