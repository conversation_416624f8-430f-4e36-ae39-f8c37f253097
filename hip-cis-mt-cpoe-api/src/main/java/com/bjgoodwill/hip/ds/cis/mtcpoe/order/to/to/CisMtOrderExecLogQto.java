package com.bjgoodwill.hip.ds.cis.mtcpoe.order.to.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.ExecLogEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医技医嘱日志")
public class CisMtOrderExecLogQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -5302096683156820834L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "医技医嘱标识")
    private String orderId;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "医嘱序号")
    private Double orderNo;
    @Schema(description = "操作类型")
    private ExecLogEnum execLogType;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public Double getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Double orderNo) {
        this.orderNo = orderNo;
    }

    public ExecLogEnum getExecLogType() {
        return execLogType;
    }

    public void setExecLogType(ExecLogEnum execLogType) {
        this.execLogType = execLogType;
    }
}