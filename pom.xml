<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bjgoodwill.hip</groupId>
        <artifactId>hip-parent</artifactId>
        <version>5.0-SNAPSHOT</version>
    </parent>

    <artifactId>hip-cis-base-dc</artifactId>
    <version>5.0-SNAPSHOT</version>
    <name>hip-cis-base-dc</name>
    <properties>
        <hip_base_cis_dict_ds.version>5.0-SNAPSHOT</hip_base_cis_dict_ds.version>
        <hip_base_cis_diagnose_ds.version>5.0-SNAPSHOT</hip_base_cis_diagnose_ds.version>
        <hip_base_cis_medicineitem_ds.version>5.0-SNAPSHOT</hip_base_cis_medicineitem_ds.version>
        <hip_cis_cds_ds.version>5.0-SNAPSHOT</hip_cis_cds_ds.version>
        <hip_cis_apply_ds.version>5.0-SNAPSHOT</hip_cis_apply_ds.version>
        <hip_pat_in_hospital_ds.version>5.0-SNAPSHOT</hip_pat_in_hospital_ds.version>
        <hip_org_ds.version>5.0-SNAPSHOT</hip_org_ds.version>
        <hip_term_ds.version>5.0-SNAPSHOT</hip_term_ds.version>
        <hip_security_ds.version>5.0-SNAPSHOT</hip_security_ds.version>
        <hip_base_pat_index_ds.version>5.0-SNAPSHOT</hip_base_pat_index_ds.version>
        <hip_drug_goods_ds.version>5.0-SNAPSHOT</hip_drug_goods_ds.version>
        <hip_cis_rule_ds.version>5.0-SNAPSHOT</hip_cis_rule_ds.version>
        <hip_cis_cdr_ds.version>5.0-SNAPSHOT</hip_cis_cdr_ds.version>
        <hip-cis-mt-cpoe-ds.version>5.0-SNAPSHOT</hip-cis-mt-cpoe-ds.version>
        <hip-econ-price-ds.version>5.0-SNAPSHOT</hip-econ-price-ds.version>
        <hip-cis-rs-ds.version>5.0-SNAPSHOT</hip-cis-rs-ds.version>
        <hip-cis-adv-ds.version>5.0-SNAPSHOT</hip-cis-adv-ds.version>
        <image.tag>0.0.2</image.tag>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-idempotent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-seata</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-cis-dict-feign</artifactId>
            <version>${hip_base_cis_dict_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-cis-diagnose-feign</artifactId>
            <version>${hip_base_cis_diagnose_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-cis-medicineitem-feign</artifactId>
            <version>${hip_base_cis_medicineitem_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-pat-in-hospital-feign</artifactId>
            <version>${hip_base_cis_medicineitem_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-org-feign</artifactId>
            <version>${hip_org_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-goods-feign</artifactId>
            <version>${hip_drug_goods_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-pat-index-feign</artifactId>
            <version>${hip_base_cis_medicineitem_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-apply-service</artifactId>
            <version>${hip_cis_apply_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-term-feign</artifactId>
            <version>${hip_term_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-security-feign</artifactId>
            <version>${hip_security_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-rule-service</artifactId>
            <version>${hip_cis_rule_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-cdr-service</artifactId>
            <version>${hip_cis_cdr_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-cds-service</artifactId>
            <version>${hip_base_cis_dict_ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-mt-cpoe-service</artifactId>
            <version>${hip-cis-mt-cpoe-ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-price-feign</artifactId>
            <version>${hip-econ-price-ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-rc-service</artifactId>
            <version>${hip-cis-rs-ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-adv-service</artifactId>
            <version>${hip-cis-adv-ds.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-code-generation-feign</artifactId>
            <version>${hip-cis-mt-cpoe-ds.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
