package com.bjgoodwill.hip.ds.cis.cdr.operation.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-cdr.name}", url = "${hip.domains.cis-cdr.url}", path = "/api/cdr/operation/cisCdrClinicICD9", contextId = "com.bjgoodwill.hip.ds.cis.cdr.operation.service.CisCdrClinicICD9ServiceFeign")
public interface CisCdrClinicICD9ServiceFeign extends CisCdrClinicICD9Service {

}