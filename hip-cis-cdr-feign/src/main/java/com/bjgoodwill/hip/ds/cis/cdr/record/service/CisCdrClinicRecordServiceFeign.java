package com.bjgoodwill.hip.ds.cis.cdr.record.service;

import org.springframework.cloud.openfeign.FeignClient;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-03-25 10:42
 * @className: CisCdrClinicRecordServiceFeign
 * @description:
 **/
@FeignClient(name = "${hip.domains.cis-cdr.name}", url = "${hip.domains.cis-cdr.url}", path = "/api/cdr/record/cisCdrClinicRecord", contextId = "com.bjgoodwill.hip.ds.cis.cdr.record.service.CisCdrClinicRecordServiceFeign")
public interface CisCdrClinicRecordServiceFeign extends CisCdrClinicRecordService {
}
