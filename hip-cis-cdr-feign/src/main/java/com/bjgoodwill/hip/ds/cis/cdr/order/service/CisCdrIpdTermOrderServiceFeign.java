package com.bjgoodwill.hip.ds.cis.cdr.order.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-cdr.name}", url = "${hip.domains.cis-cdr.url}", path = "/api/cdr/order/cisCdrIpdTermOrder", contextId = "com.bjgoodwill.hip.ds.cis.cdr.order.service.CisCdrIpdTermOrderServiceFeign")
public interface CisCdrIpdTermOrderServiceFeign extends CisCdrIpdTermOrderService {

}