package com.bjgoodwill.hip.ds.cis.cdr.order.service;

import org.springframework.cloud.openfeign.FeignClient;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-02-27 14:22
 * @className: CisCdrOrderServiceFeign
 * @description:
 **/
@FeignClient(name = "${hip.domains.cis-cdr.name}", url = "${hip.domains.cis-cdr.url}", path = "/api/cdr/order/cisCdrOrder", contextId = "com.bjgoodwill.hip.ds.cis.cdr.order.service.CisCdrOrderServiceFeign")
public interface CisCdrOrderServiceFeign extends CisCdrOrderService {
}
