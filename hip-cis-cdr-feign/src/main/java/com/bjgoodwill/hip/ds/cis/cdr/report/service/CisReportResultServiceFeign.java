package com.bjgoodwill.hip.ds.cis.cdr.report.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-cdr.name}", url = "${hip.domains.cis-cdr.url}", path = "/api/cdr/report/cisReportResult", contextId = "com.bjgoodwill.hip.ds.cis.cdr.report.service.CisReportResultServiceFeign")
public interface CisReportResultServiceFeign extends CisReportResultService {

}