package com.bjgoodwill.hip.ds.cis.cdr.orderlog.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-cdr.name}", url = "${hip.domains.cis-cdr.url}", path = "/api/cdr/orderlog/cisIpdOrderLcLog", contextId = "com.bjgoodwill.hip.ds.cis.cdr.orderlog.service.CisIpdOrderLcLogServiceFeign")
public interface CisIpdOrderLcLogServiceFeign extends CisIpdOrderLcLogService {

}