package com.bjgoodwill.hip.ds.cis.cdr.setting.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-cdr.name}", url = "${hip.domains.cis-cdr.url}", path = "/api/cdr/setting/cisPatSetting", contextId = "com.bjgoodwill.hip.ds.cis.cdr.setting.service.CisPatSettingServiceFeign")
public interface CisPatSettingServiceFeign extends CisPatSettingService {

}