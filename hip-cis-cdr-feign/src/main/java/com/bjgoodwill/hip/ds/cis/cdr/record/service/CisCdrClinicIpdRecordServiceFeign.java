package com.bjgoodwill.hip.ds.cis.cdr.record.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-cdr.name}", url = "${hip.domains.cis-cdr.url}", path = "/api/cdr/record/cisCdrClinicIpdRecord", contextId = "com.bjgoodwill.hip.ds.cis.cdr.record.service.CisCdrClinicIpdRecordServiceFeign")
public interface CisCdrClinicIpdRecordServiceFeign extends CisCdrClinicIpdRecordService {

}