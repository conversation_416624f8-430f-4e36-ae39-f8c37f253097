package com.bjgoodwill.hip.ds.cis.apply.skin;

import com.bjgoodwill.hip.ds.cis.apply.skin.service.CisSkinApplyService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/apply/cisSkinApply", contextId = "com.bjgoodwill.hip.ds.cis.apply.apply.service.CisSkinApplyServiceFeign")
public interface CisSkinApplyServiceFeign extends CisSkinApplyService {

}