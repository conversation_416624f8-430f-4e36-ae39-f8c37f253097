package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.ds.cis.apply.operation.service.CisOperationApplyService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/apply/cisOperationApply", contextId = "com.bjgoodwill.hip.ds.cis.apply.apply.service.CisOperationApplyServiceFeign")
public interface CisOperationApplyServiceFeign extends CisOperationApplyService {

}