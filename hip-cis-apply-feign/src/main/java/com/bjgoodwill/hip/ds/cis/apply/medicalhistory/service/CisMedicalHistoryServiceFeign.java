package com.bjgoodwill.hip.ds.cis.apply.medicalhistory.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/medicalhistory/cisMedicalHistoryService", contextId = "com.bjgoodwill.hip.ds.cis.apply.medicalhistory.service.CisMedicalHistoryServiceFeign")
public interface CisMedicalHistoryServiceFeign extends CisMedicalHistoryService {

}