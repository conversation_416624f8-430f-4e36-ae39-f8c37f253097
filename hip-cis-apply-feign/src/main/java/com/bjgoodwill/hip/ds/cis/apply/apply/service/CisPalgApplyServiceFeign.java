package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.ds.cis.apply.palg.service.CisPalgApplyService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/apply/cisPalgApply", contextId = "com.bjgoodwill.hip.ds.cis.apply.apply.service.CisPalgApplyServiceFeign")
public interface CisPalgApplyServiceFeign extends CisPalgApplyService {

}