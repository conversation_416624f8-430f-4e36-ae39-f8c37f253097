package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.ds.cis.apply.dgimg.service.CisDgimgApplyService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/apply/cisDgimgApply", contextId = "com.bjgoodwill.hip.ds.cis.apply.apply.service.CisDgimgApplyServiceFeign")
public interface CisDgimgApplyServiceFeign extends CisDgimgApplyService {

}