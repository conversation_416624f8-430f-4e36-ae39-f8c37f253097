package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.ds.cis.apply.material.service.CisMaterialApplyService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/apply/cisMaterialApply", contextId = "com.bjgoodwill.hip.ds.cis.apply.apply.service.CisMaterialApplyServiceFeign")
public interface CisMaterialApplyServiceFeign extends CisMaterialApplyService {

}