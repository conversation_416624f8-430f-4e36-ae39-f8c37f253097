package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.ds.cis.apply.drug.service.CisCDrugApplyService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/apply/cisCDrugApply", contextId = "com.bjgoodwill.hip.ds.cis.apply.apply.service.CisCDrugApplyServiceFeign")
public interface CisCDrugApplyServiceFeign extends CisCDrugApplyService {

}