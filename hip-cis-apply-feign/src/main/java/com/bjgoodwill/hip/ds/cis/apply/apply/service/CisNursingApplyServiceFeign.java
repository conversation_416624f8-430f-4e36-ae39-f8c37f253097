package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.ds.cis.apply.nursing.service.CisNursingApplyService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/apply/cisNursingApply", contextId = "com.bjgoodwill.hip.ds.cis.apply.apply.service.CisNursingApplyServiceFeign")
public interface CisNursingApplyServiceFeign extends CisNursingApplyService {

}