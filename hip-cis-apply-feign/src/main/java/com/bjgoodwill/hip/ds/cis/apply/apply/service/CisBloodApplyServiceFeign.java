package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.ds.cis.apply.blood.service.CisBloodApplyService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/apply/cisBloodApply", contextId = "com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBloodApplyServiceFeign")
public interface CisBloodApplyServiceFeign extends CisBloodApplyService {

}