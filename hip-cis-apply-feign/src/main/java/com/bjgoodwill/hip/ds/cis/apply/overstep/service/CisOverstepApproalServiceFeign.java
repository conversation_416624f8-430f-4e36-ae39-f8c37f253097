package com.bjgoodwill.hip.ds.cis.apply.overstep.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/overstep/cisOverstepApproal", contextId = "com.bjgoodwill.hip.ds.cis.apply.overstep.service.CisOverstepApproalServiceFeign")
public interface CisOverstepApproalServiceFeign extends CisOverstepApproalService {

}