package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/apply/cisConsultationApply", contextId = "com.bjgoodwill.hip.ds.cis.apply.apply.service.CisConsultationApplyServiceFeign")
public interface CisConsultationApplyServiceFeign extends CisConsultationApplyService {

}