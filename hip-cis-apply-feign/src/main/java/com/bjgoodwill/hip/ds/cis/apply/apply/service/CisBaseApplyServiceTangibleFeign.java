package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import org.springframework.cloud.openfeign.FeignClient;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-09 16:11
 * @className: CisBaseApplyServiceTangibleFeign
 * @description:
 **/
@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}",
        path = "/api/apply/cisBaseApplyService", contextId = "com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyServiceTangibleFeign")
public interface CisBaseApplyServiceTangibleFeign extends CisBaseApplyTangibleService {
}