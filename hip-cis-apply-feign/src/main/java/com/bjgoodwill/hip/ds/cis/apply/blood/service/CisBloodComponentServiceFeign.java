package com.bjgoodwill.hip.ds.cis.apply.blood.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-apply.name}", url = "${hip.domains.cis-apply.url}", path = "/api/apply/blood/cisBloodComponent", contextId = "com.bjgoodwill.hip.ds.cis.apply.blood.service.CisBloodComponentServiceFeign")
public interface CisBloodComponentServiceFeign extends CisBloodComponentService {

}