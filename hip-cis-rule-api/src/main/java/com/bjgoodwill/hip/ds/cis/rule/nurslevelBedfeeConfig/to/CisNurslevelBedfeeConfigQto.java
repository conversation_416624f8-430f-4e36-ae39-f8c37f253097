package com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "级别护理床位维护")
public class CisNurslevelBedfeeConfigQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -6147479579713755660L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "费别")
    private String feeType;
    @Schema(description = "护理医嘱项目编码")
    private String serviceItemCode;
    @Schema(description = "护理医嘱项目名称")
    private String serviceItemName;
    @Schema(description = "收费项目编码")
    private String priceItemCode;
    @Schema(description = "收费项目名称")
    private String priceItemName;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getPriceItemCode() {
        return priceItemCode;
    }

    public void setPriceItemCode(String priceItemCode) {
        this.priceItemCode = priceItemCode;
    }

    public String getPriceItemName() {
        return priceItemName;
    }

    public void setPriceItemName(String priceItemName) {
        this.priceItemName = priceItemName;
    }
}