package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "医嘱节点从表")
public class CisFlowNodeSubNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -6776514710877815528L;

    @Schema(description = "医嘱类型")
    private SystemTypeEnum cisSystemType;
    @Schema(description = "医嘱状态")
    private String cisOrderStatus;
    @Schema(description = "申请单状态")
    private String cisApplyStatus;
    @Schema(description = "申请单从表状态")
    private String cisSubApplyStatus;
    @Schema(description = "是否计费")
    private Boolean isBilling;
    @Schema(description = "是发送pda")
    private Boolean isSendPda;
    @Schema(description = "节点规则列表")
    private List<CisNodeRuleNto> nodeRules = new ArrayList<>();

    public SystemTypeEnum getCisSystemType() {
        return cisSystemType;
    }

    public void setCisSystemType(SystemTypeEnum cisSystemType) {
        this.cisSystemType = cisSystemType;
    }

    public String getCisOrderStatus() {
        return cisOrderStatus;
    }

    public void setCisOrderStatus(String cisOrderStatus) {
        this.cisOrderStatus = StringUtils.trimToNull(cisOrderStatus);
    }

    public String getCisApplyStatus() {
        return cisApplyStatus;
    }

    public void setCisApplyStatus(String cisApplyStatus) {
        this.cisApplyStatus = StringUtils.trimToNull(cisApplyStatus);
    }

    public String getCisSubApplyStatus() {
        return cisSubApplyStatus;
    }

    public void setCisSubApplyStatus(String cisSubApplyStatus) {
        this.cisSubApplyStatus = StringUtils.trimToNull(cisSubApplyStatus);
    }

    public Boolean getIsBilling() {
        return isBilling;
    }

    public void setIsBilling(Boolean isBilling) {
        this.isBilling = isBilling;
    }

    public Boolean getIsSendPda() {
        return isSendPda;
    }

    public void setIsSendPda(Boolean isSendPda) {
        this.isSendPda = isSendPda;
    }

    public List<CisNodeRuleNto> getNodeRules() {
        return nodeRules;
    }

    public void setNodeRules(List<CisNodeRuleNto> nodeRules) {
        this.nodeRules = nodeRules;
    }

//    private String id;
//
//    public String getId() {
//        return id;
//    }
//
//    public void setId(String id) {
//        this.id = id;
//    }
}