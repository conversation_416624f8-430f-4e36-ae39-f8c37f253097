package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DetailTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "医护限制明细")
public class CisConfOrderLimitDetailQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -129751120726128676L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "医护限制维护标识")
    private String cisConfOrderLimitId;

    @Schema(description = "医护限制维护规则标识")
    private String cisConfOrderLimitRuleId;

    @Schema(description = "已启用")
    private Boolean enabled;

    @Schema(description = "detailType")
    private DetailTypeEnum detailType;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getCisConfOrderLimitId() {
        return cisConfOrderLimitId;
    }

    public void setCisConfOrderLimitId(String cisConfOrderLimitId) {
        this.cisConfOrderLimitId = cisConfOrderLimitId;
    }

    public String getCisConfOrderLimitRuleId() {
        return cisConfOrderLimitRuleId;
    }

    public void setCisConfOrderLimitRuleId(String cisConfOrderLimitRuleId) {
        this.cisConfOrderLimitRuleId = cisConfOrderLimitRuleId;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public DetailTypeEnum getDetailType() {
        return detailType;
    }

    public void setDetailType(DetailTypeEnum detailType) {
        this.detailType = detailType;
    }
}