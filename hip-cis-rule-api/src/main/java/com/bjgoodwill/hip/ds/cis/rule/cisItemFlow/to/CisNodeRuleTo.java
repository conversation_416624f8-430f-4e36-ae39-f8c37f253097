package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "节点规则")
public class CisNodeRuleTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -4243924608555289775L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "医嘱节点从表标识")
    private String cisFlowNodeSubId;
    @Schema(description = "顺序")
    private Double sequence;
    @Schema(description = "限制方法路径")
    private String ruleMethodName;
    @Schema(description = "限制方法路径")
    private String ruleMethodPath;
    @Schema(description = "限制方法说明")
    private String remark;
    @Schema(description = "参数")
    private String parameter;
    @Schema(description = "已启用")
    private boolean enabled;
    @Schema(description = "多医嘱项目一起调用")
    private String isMulTypesUse;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建的人员名称")
    private String createdStaffName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCisFlowNodeSubId() {
        return cisFlowNodeSubId;
    }

    public void setCisFlowNodeSubId(String cisFlowNodeSubId) {
        this.cisFlowNodeSubId = cisFlowNodeSubId;
    }

    public Double getSequence() {
        return sequence;
    }

    public void setSequence(Double sequence) {
        this.sequence = sequence;
    }

    public String getRuleMethodName() {
        return ruleMethodName;
    }

    public void setRuleMethodName(String ruleMethodName) {
        this.ruleMethodName = ruleMethodName;
    }

    public String getRuleMethodPath() {
        return ruleMethodPath;
    }

    public void setRuleMethodPath(String ruleMethodPath) {
        this.ruleMethodPath = ruleMethodPath;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getParameter() {
        return parameter;
    }

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getIsMulTypesUse() {
        return isMulTypesUse;
    }

    public void setIsMulTypesUse(String isMulTypesUse) {
        this.isMulTypesUse = isMulTypesUse;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisNodeRuleTo other = (CisNodeRuleTo) obj;
        return Objects.equals(id, other.id);
    }
}