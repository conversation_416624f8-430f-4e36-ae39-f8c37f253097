package com.bjgoodwill.hip.ds.cis.rule.exclusion.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.HospitalModelEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.LimitTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.RuleTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "互斥规则维护")
public class ItemExclusionRuleTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -6525669917598076573L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "判定规则编码，如“ForVisit”、“ForTime”")
    private RuleTypeEnum ruleType;
    @Schema(description = "收费项目或医嘱项目编码或费用类别编码")
    private String itemCode;
    @Schema(description = "收费项目还是医嘱项目还是费用类别，如PRICE(收费项目)、SERVICE(服务项目（包含药品）)、FEECLASS（费用类别）")
    private String itemName;
    @Schema(description = "限制次数	限制能开几次")
    private Integer ruleDetailTimes;
    @Schema(description = "限制间隔时间(天)	当rule_code 等于“fortime”时填写")
    private Integer ruleDetailDays;
    @Schema(description = "适用范围")
    private HospitalModelEnum hospitalModel;
    @Schema(description = "Hint—仅提示（可提交成功）Intercept—提示并拦截（提交不成功）Intervene—干预(提交成功系统自行处理费用)")
    private LimitTypeEnum limitType;
    @Schema(description = "已启用")
    private boolean enabled;
    @Schema(description = "逻辑删除标记")
    private boolean deleted;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "版本")
    private Integer version;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public RuleTypeEnum getRuleType() {
        return ruleType;
    }

    public void setRuleType(RuleTypeEnum ruleType) {
        this.ruleType = ruleType;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Integer getRuleDetailTimes() {
        return ruleDetailTimes;
    }

    public void setRuleDetailTimes(Integer ruleDetailTimes) {
        this.ruleDetailTimes = ruleDetailTimes;
    }

    public Integer getRuleDetailDays() {
        return ruleDetailDays;
    }

    public void setRuleDetailDays(Integer ruleDetailDays) {
        this.ruleDetailDays = ruleDetailDays;
    }

    public HospitalModelEnum getHospitalModel() {
        return hospitalModel;
    }

    public void setHospitalModel(HospitalModelEnum hospitalModel) {
        this.hospitalModel = hospitalModel;
    }

    public LimitTypeEnum getLimitType() {
        return limitType;
    }

    public void setLimitType(LimitTypeEnum limitType) {
        this.limitType = limitType;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ItemExclusionRuleTo other = (ItemExclusionRuleTo) obj;
        return Objects.equals(id, other.id);
    }
}