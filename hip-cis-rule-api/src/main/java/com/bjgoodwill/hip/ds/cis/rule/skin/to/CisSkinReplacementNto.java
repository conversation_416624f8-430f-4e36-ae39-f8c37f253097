package com.bjgoodwill.hip.ds.cis.rule.skin.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.bjgoodwill.hip.ds.cis.rule.skin.enmus.AlternativesTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "皮试管理替代")
public class CisSkinReplacementNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -1909911737370975704L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "替换编码")
    private String replacementCode;
    @Schema(description = "替换名称")
    private String replacementName;
    @Schema(description = "系统类型")
    private AlternativesTypeEnum systemType;
    @Schema(description = "默认替代皮试")
    private Boolean defaultFlag;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "替换编码不能为空！")
    public String getReplacementCode() {
        return replacementCode;
    }

    public void setReplacementCode(String replacementCode) {
        this.replacementCode = StringUtils.trimToNull(replacementCode);
    }

    public String getReplacementName() {
        return replacementName;
    }

    public void setReplacementName(String replacementName) {
        this.replacementName = StringUtils.trimToNull(replacementName);
    }

    public AlternativesTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(AlternativesTypeEnum systemType) {
        this.systemType = systemType;
    }

    public Boolean getDefaultFlag() {
        return defaultFlag;
    }

    public void setDefaultFlag(Boolean defaultFlag) {
        this.defaultFlag = defaultFlag;
    }
}