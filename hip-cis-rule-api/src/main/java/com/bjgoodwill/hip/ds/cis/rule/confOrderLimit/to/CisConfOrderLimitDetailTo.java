package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to;

import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DetailTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "医护限制明细")
public class CisConfOrderLimitDetailTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -276171873820830739L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "医护限制维护标识")
    private String cisConfOrderLimitId;

    @Schema(description = "医护限制维护规则标识")
    private String cisConfOrderLimitRuleId;
    @Schema(description = "itemCode")
    private String itemCode;
    @Schema(description = "itemName")
    private String itemName;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;

    @Schema(description = "已启用")
    private boolean enabled;

    @Schema(description = "detailType")
    private DetailTypeEnum detailType;
    @Schema(description = "创建的人员")
    private String createdStaffName;
    @Schema(description = "最后修改的人员")
    private String updatedStaffName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCisConfOrderLimitId() {
        return cisConfOrderLimitId;
    }

    public void setCisConfOrderLimitId(String cisConfOrderLimitId) {
        this.cisConfOrderLimitId = cisConfOrderLimitId;
    }

    public String getCisConfOrderLimitRuleId() {
        return cisConfOrderLimitRuleId;
    }

    public void setCisConfOrderLimitRuleId(String cisConfOrderLimitRuleId) {
        this.cisConfOrderLimitRuleId = cisConfOrderLimitRuleId;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public DetailTypeEnum getDetailType() {
        return detailType;
    }

    public void setDetailType(DetailTypeEnum detailType) {
        this.detailType = detailType;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisConfOrderLimitDetailTo other = (CisConfOrderLimitDetailTo) obj;
        return Objects.equals(id, other.id);
    }
}