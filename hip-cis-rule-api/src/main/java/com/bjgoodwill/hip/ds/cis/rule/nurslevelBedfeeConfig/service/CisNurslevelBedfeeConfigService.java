package com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.to.CisNurslevelBedfeeConfigEto;
import com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.to.CisNurslevelBedfeeConfigNto;
import com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.to.CisNurslevelBedfeeConfigQto;
import com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.to.CisNurslevelBedfeeConfigTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "级别护理床位维护领域服务", description = "级别护理床位维护领域服务")
public interface CisNurslevelBedfeeConfigService {

    @Operation(summary = "根据查询条件对级别护理床位维护进行查询。")
    @GetMapping("/cisNurslevelBedfeeConfigs")
    List<CisNurslevelBedfeeConfigTo> getCisNurslevelBedfeeConfigs(@ParameterObject @SpringQueryMap CisNurslevelBedfeeConfigQto cisNurslevelBedfeeConfigQto);

    @Operation(summary = "根据查询条件对级别护理床位维护进行分页查询。")
    @GetMapping("/cisNurslevelBedfeeConfigs/pages")
    GridResultSet<CisNurslevelBedfeeConfigTo> getCisNurslevelBedfeeConfigPage(@ParameterObject @SpringQueryMap CisNurslevelBedfeeConfigQto cisNurslevelBedfeeConfigQto);

    @Operation(summary = "根据唯一标识返回级别护理床位维护。")
    @GetMapping("/cisNurslevelBedfeeConfigs/{id:.+}")
    CisNurslevelBedfeeConfigTo getCisNurslevelBedfeeConfigById(@PathVariable("id") String id);

    @Operation(summary = "创建级别护理床位维护。")
    @PostMapping("/cisNurslevelBedfeeConfigs")
    CisNurslevelBedfeeConfigTo createCisNurslevelBedfeeConfig(@RequestBody @Valid CisNurslevelBedfeeConfigNto cisNurslevelBedfeeConfigNto);

    @Operation(summary = "根据唯一标识修改级别护理床位维护。")
    @PutMapping("/cisNurslevelBedfeeConfigs/{id:.+}")
    void updateCisNurslevelBedfeeConfig(@PathVariable("id") String id, @RequestBody @Valid CisNurslevelBedfeeConfigEto cisNurslevelBedfeeConfigEto);

    @Operation(summary = "根据唯一标识删除级别护理床位维护。")
    @DeleteMapping("/cisNurslevelBedfeeConfigs/{id:.+}")
    void deleteCisNurslevelBedfeeConfig(@PathVariable("id") String id);

}