package com.bjgoodwill.hip.ds.cis.rule.exclusion.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleEto;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleNto;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleQto;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "互斥规则维护领域服务", description = "互斥规则维护领域服务")
public interface ItemExclusionRuleService {

    @Operation(summary = "根据查询条件对互斥规则维护进行查询。")
    @GetMapping("/itemExclusionRules")
    List<ItemExclusionRuleTo> getItemExclusionRules(@ParameterObject @SpringQueryMap ItemExclusionRuleQto itemExclusionRuleQto);

    @Operation(summary = "根据查询条件对互斥规则维护进行分页查询。")
    @GetMapping("/itemExclusionRules/pages")
    GridResultSet<ItemExclusionRuleTo> getItemExclusionRulePage(@ParameterObject @SpringQueryMap ItemExclusionRuleQto itemExclusionRuleQto);

    @Operation(summary = "根据唯一标识返回互斥规则维护。")
    @GetMapping("/itemExclusionRules/{id:.+}")
    ItemExclusionRuleTo getItemExclusionRuleById(@PathVariable("id") String id);

    @Operation(summary = "创建互斥规则维护。")
    @PostMapping("/itemExclusionRules")
    ItemExclusionRuleTo createItemExclusionRule(@RequestBody @Valid ItemExclusionRuleNto itemExclusionRuleNto);

    @Operation(summary = "根据唯一标识修改互斥规则维护。")
    @PutMapping("/itemExclusionRules/{id:.+}")
    void updateItemExclusionRule(@PathVariable("id") String id, @RequestBody @Valid ItemExclusionRuleEto itemExclusionRuleEto);

    @Operation(summary = "根据唯一标识启用互斥规则维护。")
    @PutMapping("/itemExclusionRules/{id}/enable")
    void enableItemExclusionRule(@PathVariable("id") String id);

    @Operation(summary = "根据唯一标识禁用互斥规则维护。")
    @PutMapping("/itemExclusionRules/{id}/disable")
    void disableItemExclusionRule(@PathVariable("id") String id);

    @Operation(summary = "根据唯一标识删除互斥规则维护。")
    @DeleteMapping("/itemExclusionRules/{id:.+}")
    void deleteItemExclusionRule(@PathVariable("id") String id);

}