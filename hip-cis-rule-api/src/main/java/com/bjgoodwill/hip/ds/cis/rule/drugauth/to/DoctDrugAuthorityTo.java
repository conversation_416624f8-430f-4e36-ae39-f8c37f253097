package com.bjgoodwill.hip.ds.cis.rule.drugauth.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "医生开立药品权限")
public class DoctDrugAuthorityTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -7809491994323804144L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "员工工号")
    private String staffId;
    @Schema(description = "中草药权限")
    private Boolean chineseHerbFlag;
    @Schema(description = "中成药权限")
    private Boolean chinesePatentFlag;
    @Schema(description = "处方权限")
    private Boolean canPresc;
    @Schema(description = "医师资格证")
    private Boolean physicianCertificate;
    @Schema(description = "可做手术等级")
    private String canSurgeryLevel;
    @Schema(description = "远程会诊权限")
    private Boolean isRemoteConsultation;
    @Schema(description = "抗菌药使用培训标识")
    private Boolean antiTrainingFlag;
    @Schema(description = "员工维护增加抗菌药物分级管理权限：1、限制使用级、2、非限制使用级、3、特殊使用级")
    private String antiTrainingLevel;
    @Schema(description = "药理属性")
    private String toxiPropertyNames;
    @Schema(description = "贵重药权限")
    private Boolean canValuableMedicineFlag;
    @Schema(description = "抗肿瘤药权限")
    private Boolean canAntineoplasticFlag;
    @Schema(description = "抗肿瘤药权限等级")
    private String antineoplasticLevel;
    @Schema(description = "逻辑删除标记")
    private boolean deleted;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "员工名称")
    private String staffName;
    @Schema(description = "所属科室")
    private String administrativeDept;
    @Schema(description = "所属科室名称")
    private String administrativeDeptName;
    @Schema(description = "版本号")
    private Integer version;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStaffId() {
        return staffId;
    }

    public void setStaffId(String staffId) {
        this.staffId = staffId;
    }

    public Boolean getChineseHerbFlag() {
        return chineseHerbFlag;
    }

    public void setChineseHerbFlag(Boolean chineseHerbFlag) {
        this.chineseHerbFlag = chineseHerbFlag;
    }

    public Boolean getChinesePatentFlag() {
        return chinesePatentFlag;
    }

    public void setChinesePatentFlag(Boolean chinesePatentFlag) {
        this.chinesePatentFlag = chinesePatentFlag;
    }

    public Boolean getCanPresc() {
        return canPresc;
    }

    public void setCanPresc(Boolean canPresc) {
        this.canPresc = canPresc;
    }


    public String getCanSurgeryLevel() {
        return canSurgeryLevel;
    }

    public void setCanSurgeryLevel(String canSurgeryLevel) {
        this.canSurgeryLevel = canSurgeryLevel;
    }

    public Boolean getIsRemoteConsultation() {
        return isRemoteConsultation;
    }

    public void setIsRemoteConsultation(Boolean isRemoteConsultation) {
        this.isRemoteConsultation = isRemoteConsultation;
    }

    public Boolean getAntiTrainingFlag() {
        return antiTrainingFlag;
    }

    public void setAntiTrainingFlag(Boolean antiTrainingFlag) {
        this.antiTrainingFlag = antiTrainingFlag;
    }

    public String getAntiTrainingLevel() {
        return antiTrainingLevel;
    }

    public void setAntiTrainingLevel(String antiTrainingLevel) {
        this.antiTrainingLevel = antiTrainingLevel;
    }


    public String getToxiPropertyNames() {
        return toxiPropertyNames;
    }

    public void setToxiPropertyNames(String toxiPropertyNames) {
        this.toxiPropertyNames = toxiPropertyNames;
    }

    public Boolean getCanValuableMedicineFlag() {
        return canValuableMedicineFlag;
    }

    public void setCanValuableMedicineFlag(Boolean canValuableMedicineFlag) {
        this.canValuableMedicineFlag = canValuableMedicineFlag;
    }

    public Boolean getCanAntineoplasticFlag() {
        return canAntineoplasticFlag;
    }

    public void setCanAntineoplasticFlag(Boolean canAntineoplasticFlag) {
        this.canAntineoplasticFlag = canAntineoplasticFlag;
    }

    public String getAntineoplasticLevel() {
        return antineoplasticLevel;
    }

    public void setAntineoplasticLevel(String antineoplasticLevel) {
        this.antineoplasticLevel = antineoplasticLevel;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getAdministrativeDept() {
        return administrativeDept;
    }

    public void setAdministrativeDept(String administrativeDept) {
        this.administrativeDept = administrativeDept;
    }

    public String getAdministrativeDeptName() {
        return administrativeDeptName;
    }

    public void setAdministrativeDeptName(String administrativeDeptName) {
        this.administrativeDeptName = administrativeDeptName;
    }

    public Boolean getPhysicianCertificate() {
        return physicianCertificate;
    }

    public void setPhysicianCertificate(Boolean physicianCertificate) {
        this.physicianCertificate = physicianCertificate;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        DoctDrugAuthorityTo other = (DoctDrugAuthorityTo) obj;
        return Objects.equals(id, other.id);
    }
}