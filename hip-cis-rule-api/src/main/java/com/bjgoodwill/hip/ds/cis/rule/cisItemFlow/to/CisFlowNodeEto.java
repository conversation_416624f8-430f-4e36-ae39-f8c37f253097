package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "医嘱节点")
public class CisFlowNodeEto extends BaseEto {

    @Serial
    private static final long serialVersionUID = -4512141644544769442L;

    @Schema(description = "节点名称")
    private String nodeName;
    @Schema(description = "方法名称")
    private String methodName;
    @Schema(description = "方法类名")
    private String methodClassName;
    @Schema(description = "顺序")
    private Double sequence;
    @Schema(description = "是否固定")
    private Boolean isFixed;
    @Schema(description = "已启用")
    private boolean enabled;
    @Schema(description = "是否可以作废")
    private Boolean isCanCancel;
    @Schema(description = "是否可以退费")
    private Boolean isCanRefund;

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = StringUtils.trimToNull(nodeName);
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = StringUtils.trimToNull(methodName);
    }

    @NotBlank(message = "方法类名不能为空！")
    public String getMethodClassName() {
        return methodClassName;
    }

    public void setMethodClassName(String methodClassName) {
        this.methodClassName = StringUtils.trimToNull(methodClassName);
    }

    @NotNull(message = "顺序不能为空！")
    public Double getSequence() {
        return sequence;
    }

    public void setSequence(Double sequence) {
        this.sequence = sequence;
    }

    public Boolean getIsFixed() {
        return isFixed;
    }

    public void setIsFixed(Boolean isFixed) {
        this.isFixed = isFixed;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getIsCanCancel() {
        return isCanCancel;
    }

    public void setIsCanCancel(Boolean isCanCancel) {
        this.isCanCancel = isCanCancel;
    }

    public Boolean getIsCanRefund() {
        return isCanRefund;
    }

    public void setIsCanRefund(Boolean isCanRefund) {
        this.isCanRefund = isCanRefund;
    }
}