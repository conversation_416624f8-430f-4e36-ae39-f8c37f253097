package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to;

import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DocNurseTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Schema(description = "医护限制维护")
public class CisConfOrderLimitTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -3324322149271248399L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "parentNode")
    private String parentNode;
    @Schema(description = "nodeCode")
    private String nodeCode;
    @Schema(description = "节点名称")
    private String nodeName;
    @Schema(description = "inputCode")
    private String inputCode;
    @Schema(description = "已启用")
    private boolean enabled;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "type")
    private DocNurseTypeEnum type;
    @Schema(description = "医护限制维护规则列表")
    private List<CisConfOrderLimitRuleTo> cisConfOrderLimitRules;

    @Schema(description = "医护限制明细列表")
    private List<CisConfOrderLimitDetailTo> cisConfOrderLimitDetails;
    @Schema(description = "创建的人员")
    private String createdStaffName;
    @Schema(description = "最后修改的人员")
    private String updatedStaffName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentNode() {
        return parentNode;
    }

    public void setParentNode(String parentNode) {
        this.parentNode = parentNode;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getInputCode() {
        return inputCode;
    }

    public void setInputCode(String inputCode) {
        this.inputCode = inputCode;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public DocNurseTypeEnum getType() {
        return type;
    }

    public void setType(DocNurseTypeEnum type) {
        this.type = type;
    }

    public List<CisConfOrderLimitRuleTo> getCisConfOrderLimitRules() {
        return cisConfOrderLimitRules;
    }

    public void setCisConfOrderLimitRules(List<CisConfOrderLimitRuleTo> cisConfOrderLimitRules) {
        this.cisConfOrderLimitRules = cisConfOrderLimitRules;
    }

    public List<CisConfOrderLimitDetailTo> getCisConfOrderLimitDetails() {
        return cisConfOrderLimitDetails;
    }

    public void setCisConfOrderLimitDetails(List<CisConfOrderLimitDetailTo> cisConfOrderLimitDetails) {
        this.cisConfOrderLimitDetails = cisConfOrderLimitDetails;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisConfOrderLimitTo other = (CisConfOrderLimitTo) obj;
        return Objects.equals(id, other.id);
    }
}