package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.RuleTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "医护限制维护规则")
public class CisConfOrderLimitRuleQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -6026461912432657127L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "医护限制维护标识")
    private String cisConfOrderLimitId;
    @Schema(description = "明细名称")
    private String detailName;
    @Schema(description = "规则分类")
    private RuleTypeEnum detailType;
    @Schema(description = "已启用")
    private Boolean enabled;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getCisConfOrderLimitId() {
        return cisConfOrderLimitId;
    }

    public void setCisConfOrderLimitId(String cisConfOrderLimitId) {
        this.cisConfOrderLimitId = cisConfOrderLimitId;
    }

    public String getDetailName() {
        return detailName;
    }

    public void setDetailName(String detailName) {
        this.detailName = detailName;
    }

    public RuleTypeEnum getDetailType() {
        return detailType;
    }

    public void setDetailType(RuleTypeEnum detailType) {
        this.detailType = detailType;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}