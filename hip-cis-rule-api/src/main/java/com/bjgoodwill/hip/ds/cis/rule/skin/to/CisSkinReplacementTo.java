package com.bjgoodwill.hip.ds.cis.rule.skin.to;

import com.bjgoodwill.hip.ds.cis.rule.skin.enmus.AlternativesTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "皮试管理替代")
public class CisSkinReplacementTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -2398946484387314147L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "皮试规则标识")
    private String cisSkinLimitId;
    @Schema(description = "替换编码")
    private String replacementCode;
    @Schema(description = "替换名称")
    private String replacementName;
    @Schema(description = "替代物类型")
    private AlternativesTypeEnum systemType;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;
    @Schema(description = "逻辑删除标记")
    private boolean deleted;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCisSkinLimitId() {
        return cisSkinLimitId;
    }

    public void setCisSkinLimitId(String cisSkinLimitId) {
        this.cisSkinLimitId = cisSkinLimitId;
    }

    public String getReplacementCode() {
        return replacementCode;
    }

    public void setReplacementCode(String replacementCode) {
        this.replacementCode = replacementCode;
    }

    public String getReplacementName() {
        return replacementName;
    }

    public void setReplacementName(String replacementName) {
        this.replacementName = replacementName;
    }

    public AlternativesTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(AlternativesTypeEnum systemType) {
        this.systemType = systemType;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisSkinReplacementTo other = (CisSkinReplacementTo) obj;
        return Objects.equals(id, other.id);
    }
}