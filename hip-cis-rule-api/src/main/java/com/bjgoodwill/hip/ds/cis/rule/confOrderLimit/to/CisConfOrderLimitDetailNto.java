package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DetailTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "医护限制明细")
public class CisConfOrderLimitDetailNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -4730190805295111667L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "itemCode")
    private String itemCode;
    @Schema(description = "itemName")
    private String itemName;

    @Schema(description = "detailType")
    private DetailTypeEnum detailType;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "itemCode不能为空！")
    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = StringUtils.trimToNull(itemCode);
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = StringUtils.trimToNull(itemName);
    }

    public DetailTypeEnum getDetailType() {
        return detailType;
    }

    public void setDetailType(DetailTypeEnum detailType) {
        this.detailType = detailType;
    }
}