package com.bjgoodwill.hip.ds.cis.rule.exclusion.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.HospitalModelEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.LimitTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "互斥规则维护")
public class ItemExclusionRuleQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -2088266719699350996L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "收费项目或医嘱项目编码或费用类别编码")
    private String itemCode;
    @Schema(description = "收费项目还是医嘱项目还是费用类别，如PRICE(收费项目)、SERVICE(服务项目（包含药品）)、FEECLASS（费用类别）")
    private String itemName;
    @Schema(description = "适用范围")
    private HospitalModelEnum hospitalModel;
    @Schema(description = "Hint—仅提示（可提交成功）Intercept—提示并拦截（提交不成功）Intervene—干预(提交成功系统自行处理费用)")
    private LimitTypeEnum limitType;
    @Schema(description = "已启用")
    private Boolean enabled;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public HospitalModelEnum getHospitalModel() {
        return hospitalModel;
    }

    public void setHospitalModel(HospitalModelEnum hospitalModel) {
        this.hospitalModel = hospitalModel;
    }

    public LimitTypeEnum getLimitType() {
        return limitType;
    }

    public void setLimitType(LimitTypeEnum limitType) {
        this.limitType = limitType;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}