package com.bjgoodwill.hip.ds.cis.rule.drugLimit.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "药品限制")
public class CisDrugLimitQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -2918065481290078080L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "drugCode")
    private String drugCode;
    @Schema(description = "drugName")
    private String drugName;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    public String getDrugName() {
        return drugName;
    }

    public void setDrugName(String drugName) {
        this.drugName = drugName;
    }
}