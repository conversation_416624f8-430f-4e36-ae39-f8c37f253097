package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "节点规则")
public class CisNodeRuleQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -4671473916055480406L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "医嘱节点从表标识")
    private String cisFlowNodeSubId;
    @Schema(description = "顺序")
    private Double sequence;
    @Schema(description = "限制方法路径")
    private String ruleMethodName;
    @Schema(description = "限制方法路径")
    private String ruleMethodPath;
    @Schema(description = "限制方法说明")
    private String remark;
    @Schema(description = "参数")
    private String parameter;
    @Schema(description = "已启用")
    private Boolean enabled;
    @Schema(description = "多医嘱项目一起调用")
    private String isMulTypesUse;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getCisFlowNodeSubId() {
        return cisFlowNodeSubId;
    }

    public void setCisFlowNodeSubId(String cisFlowNodeSubId) {
        this.cisFlowNodeSubId = cisFlowNodeSubId;
    }

    public Double getSequence() {
        return sequence;
    }

    public void setSequence(Double sequence) {
        this.sequence = sequence;
    }

    public String getRuleMethodName() {
        return ruleMethodName;
    }

    public void setRuleMethodName(String ruleMethodName) {
        this.ruleMethodName = ruleMethodName;
    }

    public String getRuleMethodPath() {
        return ruleMethodPath;
    }

    public void setRuleMethodPath(String ruleMethodPath) {
        this.ruleMethodPath = ruleMethodPath;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getParameter() {
        return parameter;
    }

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getIsMulTypesUse() {
        return isMulTypesUse;
    }

    public void setIsMulTypesUse(String isMulTypesUse) {
        this.isMulTypesUse = isMulTypesUse;
    }
}