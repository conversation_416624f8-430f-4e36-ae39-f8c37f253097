package com.bjgoodwill.hip.ds.cis.rule.drugLimit.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.DocLimitEto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.DocLimitNto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.DocLimitQto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.DocLimitTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "医生限制领域服务", description = "医生限制领域服务")
public interface DocLimitService {

    @Operation(summary = "根据查询条件对医生限制进行查询。")
    @GetMapping("/docLimits")
    List<DocLimitTo> getDocLimits(@ParameterObject @SpringQueryMap DocLimitQto docLimitQto);

    @Operation(summary = "根据查询条件对医生限制进行分页查询。")
    @GetMapping("/docLimits/pages")
    GridResultSet<DocLimitTo> getDocLimitPage(@ParameterObject @SpringQueryMap DocLimitQto docLimitQto);

    @Operation(summary = "根据唯一标识返回医生限制。")
    @GetMapping("/docLimits/{id:.+}")
    DocLimitTo getDocLimitById(@PathVariable("id") String id);

    @Operation(summary = "创建医生限制。")
    @PostMapping("/docLimits")
    DocLimitTo createDocLimit(@RequestBody @Valid DocLimitNto docLimitNto);

    @Operation(summary = "根据唯一标识修改医生限制。")
    @PutMapping("/docLimits/{id:.+}")
    void updateDocLimit(@PathVariable("id") String id, @RequestBody @Valid DocLimitEto docLimitEto);

    @Operation(summary = "根据唯一标识删除医生限制。")
    @DeleteMapping("/docLimits/{id:.+}")
    void deleteDocLimit(@PathVariable("id") String id);

    @Operation(summary = "批量查询药品限制医生。")
    @GetMapping("/docLimits/drug_codes")
    List<DocLimitTo> getDocLimitByDrugCodes(@RequestParam List<String> drugCodes);
}