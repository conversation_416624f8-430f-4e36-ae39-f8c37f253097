package com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "级别护理床位维护")
public class CisNurslevelBedfeeConfigEto extends BaseEto {

    @Serial
    private static final long serialVersionUID = -5755509970134871809L;

    @Schema(description = "费别")
    private String feeType;
    @Schema(description = "护理医嘱项目编码")
    private String serviceItemCode;
    @Schema(description = "护理医嘱项目名称")
    private String serviceItemName;
    @Schema(description = "收费项目编码")
    private String priceItemCode;
    @Schema(description = "收费项目名称")
    private String priceItemName;

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = StringUtils.trimToNull(feeType);
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = StringUtils.trimToNull(serviceItemCode);
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = StringUtils.trimToNull(serviceItemName);
    }

    public String getPriceItemCode() {
        return priceItemCode;
    }

    public void setPriceItemCode(String priceItemCode) {
        this.priceItemCode = StringUtils.trimToNull(priceItemCode);
    }

    public String getPriceItemName() {
        return priceItemName;
    }

    public void setPriceItemName(String priceItemName) {
        this.priceItemName = StringUtils.trimToNull(priceItemName);
    }
}