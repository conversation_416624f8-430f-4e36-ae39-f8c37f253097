package com.bjgoodwill.hip.ds.cis.rule.drugauth.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "医嘱提交诊断说明")
public class DoctCommitDiagnoseMsgQto implements Serializable {

    @Serial
    private static final long serialVersionUID = 5148430286577117503L;
    @Schema(description = "提交需要诊断提示说明的医嘱列表")
    private List<DiagnoseMsgQto> diagnoseMsgList;
    @Schema(description = "住院诊断列表")
    private List<IpdDiagnoseQto> ipdDiagnoseQtoList;

    public List<DiagnoseMsgQto> getDiagnoseMsgList() {
        return diagnoseMsgList;
    }

    public void setDiagnoseMsgList(List<DiagnoseMsgQto> diagnoseMsgList) {
        this.diagnoseMsgList = diagnoseMsgList;
    }

    public List<IpdDiagnoseQto> getIpdDiagnoseQtoList() {
        return ipdDiagnoseQtoList;
    }

    public void setIpdDiagnoseQtoList(List<IpdDiagnoseQto> ipdDiagnoseQtoList) {
        this.ipdDiagnoseQtoList = ipdDiagnoseQtoList;
    }

    @Schema(description = "提交需要诊断提示说明的医嘱")
    public static class DiagnoseMsgQto extends DoctCommitOrderMsgQto {
        @Serial
        private static final long serialVersionUID = 7902310735987012142L;
        @Schema(description = "医嘱类型")
        @NotNull(message = "医嘱类型不能为空！")
        private SystemTypeEnum orderClass;
        @Schema(description = "患者流水号")
        @NotBlank(message = "患者流水号不能为空！")
        private String visitCode;

        public SystemTypeEnum getOrderClass() {
            return orderClass;
        }

        public void setOrderClass(SystemTypeEnum orderClass) {
            this.orderClass = orderClass;
        }

        public String getVisitCode() {
            return visitCode;
        }

        public void setVisitCode(String visitCode) {
            this.visitCode = visitCode;
        }
    }

    @Schema(description = "住院诊断")
    public static class IpdDiagnoseQto implements Serializable {
        @Serial
        private static final long serialVersionUID = 4834316186240977783L;

        @NotNull(message = "诊断类型不能为空！")
        @Schema(description = "诊断类型")
        private DiagnosisTypeEnum diagnosisType;

        @Schema(description = "诊断分类 中，西 ")
        @NotNull(message = "诊断分类不能为空！")
        private DiagnosisEnum diagnosisClass;

        @Schema(description = "主诊断")
        private Boolean chiefFlag;

        public DiagnosisTypeEnum getDiagnosisType() {
            return diagnosisType;
        }

        public void setDiagnosisType(DiagnosisTypeEnum diagnosisType) {
            this.diagnosisType = diagnosisType;
        }

        public DiagnosisEnum getDiagnosisClass() {
            return diagnosisClass;
        }

        public void setDiagnosisClass(DiagnosisEnum diagnosisClass) {
            this.diagnosisClass = diagnosisClass;
        }

        public Boolean getChiefFlag() {
            return chiefFlag;
        }

        public void setChiefFlag(Boolean chiefFlag) {
            this.chiefFlag = chiefFlag;
        }
    }
}