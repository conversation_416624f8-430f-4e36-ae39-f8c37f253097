package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DocNurseTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "医护限制维护")
public class CisConfOrderLimitQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -8954436333640262080L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "inputCode")
    private String inputCode;
    //    @Schema(description = "已启用")
//    private Boolean enabled;
    @Schema(description = "type")
    private DocNurseTypeEnum type;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getInputCode() {
        return inputCode;
    }

    public void setInputCode(String inputCode) {
        this.inputCode = inputCode;
    }

//    public Boolean getEnabled() {
//    	return enabled;
//    }
//
//    public void setEnabled(Boolean enabled) {
//    	this.enabled = enabled;
//    }

    public DocNurseTypeEnum getType() {
        return type;
    }

    public void setType(DocNurseTypeEnum type) {
        this.type = type;
    }

}