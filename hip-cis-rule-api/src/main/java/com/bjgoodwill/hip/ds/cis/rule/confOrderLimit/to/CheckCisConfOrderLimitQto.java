package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to;

import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DetailTypeEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DocNurseTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-08-08 13:39
 * @className: CheckCisConfOrderLimitQto
 * @description:
 **/
@Schema(description = "医护限制校验")
public class CheckCisConfOrderLimitQto implements Serializable {
    @Serial
    private static final long serialVersionUID = -1647755719511583919L;

    private DocNurseTypeEnum docNurseType;

    private DetailTypeEnum detailType;

    //1男 2女
    private String sex;

    private LocalDateTime birthday;
    private List<String> codes;

    @NotNull(message = "医护类型不能为空")
    public DocNurseTypeEnum getDocNurseType() {
        return docNurseType;
    }

    public void setDocNurseType(DocNurseTypeEnum docNurseType) {
        this.docNurseType = docNurseType;
    }

    @NotNull(message = "限制类型不能为空")
    public DetailTypeEnum getDetailType() {
        return detailType;
    }

    public void setDetailType(DetailTypeEnum detailType) {
        this.detailType = detailType;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthday() {
        return birthday;
    }

    public void setBirthday(LocalDateTime birthday) {
        this.birthday = birthday;
    }

    public List<String> getCodes() {
        return codes;
    }

    public void setCodes(List<String> codes) {
        this.codes = codes;
    }
}