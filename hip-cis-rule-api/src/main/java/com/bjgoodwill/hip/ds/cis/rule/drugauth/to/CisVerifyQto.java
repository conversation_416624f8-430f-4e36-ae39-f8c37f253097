package com.bjgoodwill.hip.ds.cis.rule.drugauth.to;

import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.ServiceClinicItemTo;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsWestTo;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-03-08 09:52
 * @className: CisVerifyQto
 * @description: 权限校验入参
 **/
public class CisVerifyQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4950611776436591018L;


    private List<DrugGoodsWestTo> drugGoodsTos;

    private List<ServiceClinicItemTo> serviceClinicItemTos;

    public List<DrugGoodsWestTo> getDrugGoodsTos() {
        return drugGoodsTos;
    }

    public void setDrugGoodsTos(List<DrugGoodsWestTo> drugGoodsTos) {
        this.drugGoodsTos = drugGoodsTos;
    }

    public List<ServiceClinicItemTo> getServiceClinicItemTos() {
        return serviceClinicItemTos;
    }

    public void setServiceClinicItemTos(List<ServiceClinicItemTo> serviceClinicItemTos) {
        this.serviceClinicItemTos = serviceClinicItemTos;
    }
}