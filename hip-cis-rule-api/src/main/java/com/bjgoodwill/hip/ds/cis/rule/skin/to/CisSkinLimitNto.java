package com.bjgoodwill.hip.ds.cis.rule.skin.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "皮试规则")
public class CisSkinLimitNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -6385149833088440022L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "药品编码")
    private String drugCode;
    @Schema(description = "药品名称")
    private String drugName;
    @Schema(description = "成人限制天数")
    private Long limitDay;
    @Schema(description = "儿童限制天数")
    private Long childLimitDay;
    @Schema(description = "科室编码")
    private String orgCode;
    @Schema(description = "全院标识")
    private Boolean hospitalFlag;
    @Schema(description = "皮试管理替代列表")
    private List<CisSkinReplacementNto> cisSkinReplacements = new ArrayList<>();
    @Schema(description = "科室名称")
    private String orgName;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "药品编码不能为空！")
    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = StringUtils.trimToNull(drugCode);
    }

    public String getDrugName() {
        return drugName;
    }

    public void setDrugName(String drugName) {
        this.drugName = StringUtils.trimToNull(drugName);
    }

    @NotNull(message = "成人限制天数不能为空！")
    public Long getLimitDay() {
        return limitDay;
    }

    public void setLimitDay(Long limitDay) {
        this.limitDay = limitDay;
    }

    @NotNull(message = "儿童限制天数不能为空！")
    public Long getChildLimitDay() {
        return childLimitDay;
    }

    public void setChildLimitDay(Long childLimitDay) {
        this.childLimitDay = childLimitDay;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    @NotNull(message = "全院标识不能为空！")
    public Boolean getHospitalFlag() {
        return hospitalFlag;
    }

    public void setHospitalFlag(Boolean hospitalFlag) {
        this.hospitalFlag = hospitalFlag;
    }

    public List<CisSkinReplacementNto> getCisSkinReplacements() {
        return cisSkinReplacements;
    }

    public void setCisSkinReplacements(List<CisSkinReplacementNto> cisSkinReplacements) {
        this.cisSkinReplacements = cisSkinReplacements;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}