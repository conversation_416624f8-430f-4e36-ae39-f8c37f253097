package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums;

import com.bjgoodwill.hip.common.exception.BusinessErrorEnum;

/**
 * @program: HIP5.0
 * @author: xdguo
 * @create: 2024-06-05 09:06
 * @className: CisRuleBusinessErrorEnum
 * @description:
 **/
public enum CisRuleBusinessErrorEnum implements BusinessErrorEnum {
    BUS_CIS_RULE_0001(" [%s]不可为空！"),
    BUS_CIS_RULE_0002("[%s]已经存在！"),
    BUS_CIS_RULE_0003("数据[%s]已被修改,请刷新！"),
    BUS_CIS_RULE_0004("顺序[%s]已经存在！"),
    BUS_CIS_RULE_0005("[%s]不允许添加！"),
    BUS_CIS_RULE_0006("[%s]不符合[%s]条件！"),
    BUS_CIS_RULE_0007("[%s]"),
    BUS_CIS_RULE_0008("[%s]没有权限使用[%s]药品"),
    BUS_CIS_RULE_0009("[%s][%s]数据已无效"),
    BUS_CIS_RULE_00010("当前登录用户id为空"),
    BUS_CIS_RULE_00011("[%s]无权限!");

    private final String message;

    CisRuleBusinessErrorEnum(String message) {
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.name();
    }

    @Override
    public String getMessage() {
        return message;
    }
}