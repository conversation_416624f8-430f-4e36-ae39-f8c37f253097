package com.bjgoodwill.hip.ds.cis.rule.exclusion.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.HospitalModelEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.LimitTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.RuleTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "互斥规则维护")
public class ItemExclusionRuleNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -3408049111693860584L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "判定规则编码，如“ForVisit”、“ForTime”")
    private RuleTypeEnum ruleType;
    @Schema(description = "收费项目或医嘱项目编码或费用类别编码")
    private String itemCode;
    @Schema(description = "收费项目还是医嘱项目还是费用类别，如PRICE(收费项目)、SERVICE(服务项目（包含药品）)、FEECLASS（费用类别）")
    private String itemName;
    @Schema(description = "限制次数	限制能开几次")
    private Integer ruleDetailTimes;
    @Schema(description = "限制间隔时间(天)	当rule_code 等于“fortime”时填写")
    private Integer ruleDetailDays;
    @Schema(description = "适用范围")
    private HospitalModelEnum hospitalModel;
    @Schema(description = "Hint—仅提示（可提交成功）Intercept—提示并拦截（提交不成功）Intervene—干预(提交成功系统自行处理费用)")
    private LimitTypeEnum limitType;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    public RuleTypeEnum getRuleType() {
        return ruleType;
    }

    public void setRuleType(RuleTypeEnum ruleType) {
        this.ruleType = ruleType;
    }

    @NotBlank(message = "收费项目或医嘱项目编码或费用类别编码不能为空！")
    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = StringUtils.trimToNull(itemCode);
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = StringUtils.trimToNull(itemName);
    }

    public Integer getRuleDetailTimes() {
        return ruleDetailTimes;
    }

    public void setRuleDetailTimes(Integer ruleDetailTimes) {
        this.ruleDetailTimes = ruleDetailTimes;
    }

    public Integer getRuleDetailDays() {
        return ruleDetailDays;
    }

    public void setRuleDetailDays(Integer ruleDetailDays) {
        this.ruleDetailDays = ruleDetailDays;
    }

    public HospitalModelEnum getHospitalModel() {
        return hospitalModel;
    }

    public void setHospitalModel(HospitalModelEnum hospitalModel) {
        this.hospitalModel = hospitalModel;
    }

    public LimitTypeEnum getLimitType() {
        return limitType;
    }

    public void setLimitType(LimitTypeEnum limitType) {
        this.limitType = limitType;
    }
}