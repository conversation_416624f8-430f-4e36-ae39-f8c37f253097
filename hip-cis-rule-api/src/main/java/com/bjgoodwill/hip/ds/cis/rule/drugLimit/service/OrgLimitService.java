package com.bjgoodwill.hip.ds.cis.rule.drugLimit.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.OrgLimitEto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.OrgLimitNto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.OrgLimitQto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.OrgLimitTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "科室限制领域服务", description = "科室限制领域服务")
public interface OrgLimitService {

    @Operation(summary = "根据查询条件对科室限制进行查询。")
    @GetMapping("/orgLimits")
    List<OrgLimitTo> getOrgLimits(@ParameterObject @SpringQueryMap OrgLimitQto orgLimitQto);

    @Operation(summary = "根据查询条件对科室限制进行分页查询。")
    @GetMapping("/orgLimits/pages")
    GridResultSet<OrgLimitTo> getOrgLimitPage(@ParameterObject @SpringQueryMap OrgLimitQto orgLimitQto);

    @Operation(summary = "根据唯一标识返回科室限制。")
    @GetMapping("/orgLimits/{id:.+}")
    OrgLimitTo getOrgLimitById(@PathVariable("id") String id);

    @Operation(summary = "创建科室限制。")
    @PostMapping("/orgLimits")
    OrgLimitTo createOrgLimit(@RequestBody @Valid OrgLimitNto orgLimitNto);

    @Operation(summary = "根据唯一标识修改科室限制。")
    @PutMapping("/orgLimits/{id:.+}")
    void updateOrgLimit(@PathVariable("id") String id, @RequestBody @Valid OrgLimitEto orgLimitEto);

    @Operation(summary = "根据唯一标识删除科室限制。")
    @DeleteMapping("/orgLimits/{id:.+}")
    void deleteOrgLimit(@PathVariable("id") String id);

    @Operation(summary = "批量查询药品限制科室。")
    @GetMapping("/orgLimits/drug_codes")
    List<OrgLimitTo> getOrgLimitByDrugCodes(@RequestParam List<String> drugCodes);

}