package com.bjgoodwill.hip.ds.cis.rule.drugauth.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;

@Schema(description = "提交医嘱信息说明查询通用")
public class DoctCommitMsgCommonQto extends DoctCommitOrderMsgQto  {

    @Serial
    private static final long serialVersionUID = 3479229804467718852L;
    @Schema(description = "医嘱类型")
    @NotNull(message = "医嘱类型不能为空！")
    private SystemTypeEnum orderClass;
    @Schema(description = "医嘱编码")
    @NotBlank(message = "医嘱编码不能为空！")
    private String orderServiceCode;

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    public String getOrderServiceCode() {
        return orderServiceCode;
    }

    public void setOrderServiceCode(String orderServiceCode) {
        this.orderServiceCode = orderServiceCode;
    }

}