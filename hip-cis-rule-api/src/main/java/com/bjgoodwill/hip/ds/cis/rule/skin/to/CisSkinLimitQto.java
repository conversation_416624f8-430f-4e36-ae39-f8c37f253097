package com.bjgoodwill.hip.ds.cis.rule.skin.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "皮试规则")
public class CisSkinLimitQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -4549430138151388834L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "药品编码")
    private String drugCode;
    @Schema(description = "已启用")
    private Boolean enabled;
    @Schema(description = "科室编码")
    private String orgCode;
    @Schema(description = "全院标识")
    private Boolean hospitalFlag;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Boolean getHospitalFlag() {
        return hospitalFlag;
    }

    public void setHospitalFlag(Boolean hospitalFlag) {
        this.hospitalFlag = hospitalFlag;
    }
}