package com.bjgoodwill.hip.ds.cis.rule.drugLimit.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@Schema(description = "药品限制")
public abstract class CisDrugLimitNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -2293424623171464901L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "drugCode")
    private String drugCode;
    @Schema(description = "drugName")
    private String drugName;
    @Schema(description = "工作组类型")
    private String workGroupTypeCode;
    @Schema(description = "工作组类型名称")
    private String workGroupTypeName;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "drugCode不能为空！")
    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = StringUtils.trimToNull(drugCode);
    }

    @NotBlank(message = "drugName不能为空！")
    public String getDrugName() {
        return drugName;
    }

    public void setDrugName(String drugName) {
        this.drugName = StringUtils.trimToNull(drugName);
    }

    public String getWorkGroupTypeCode() {
        return workGroupTypeCode;
    }

    public void setWorkGroupTypeCode(String workGroupTypeCode) {
        this.workGroupTypeCode = workGroupTypeCode;
    }

    public String getWorkGroupTypeName() {
        return workGroupTypeName;
    }

    public void setWorkGroupTypeName(String workGroupTypeName) {
        this.workGroupTypeName = workGroupTypeName;
    }

    public String getMinimal_class() {
        return "." + this.getClass().getSimpleName();
    }
}