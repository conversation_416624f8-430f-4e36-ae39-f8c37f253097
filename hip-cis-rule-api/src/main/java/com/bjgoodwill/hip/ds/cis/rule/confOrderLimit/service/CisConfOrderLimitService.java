package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "医护限制维护领域服务", description = "医护限制维护领域服务")
public interface CisConfOrderLimitService {

    @Operation(summary = "P0根据查询条件对医护限制维护进行查询。")
    @GetMapping("/cisConfOrderLimits")
    List<CisConfOrderLimitTo> getCisConfOrderLimits(@ParameterObject @SpringQueryMap CisConfOrderLimitQto cisConfOrderLimitQto);

    @Operation(summary = "根据查询条件对医护限制维护进行分页查询。")
    @GetMapping("/cisConfOrderLimits/pages")
    GridResultSet<CisConfOrderLimitTo> getCisConfOrderLimitPage(@ParameterObject @SpringQueryMap CisConfOrderLimitQto cisConfOrderLimitQto);

    @Operation(summary = "创建医护限制维护。")
    @PostMapping("/cisConfOrderLimits")
    CisConfOrderLimitTo createCisConfOrderLimit(@RequestBody @Valid CisConfOrderLimitNto cisConfOrderLimitNto);

    @Operation(summary = "根据唯一标识修改医护限制维护。")
    @PutMapping("/cisConfOrderLimits/{id:.+}")
    void updateCisConfOrderLimit(@PathVariable("id") String id, @RequestBody @Valid CisConfOrderLimitEto cisConfOrderLimitEto);

    @Operation(summary = "根据唯一标识启用医护限制维护。")
    @PutMapping("/cisConfOrderLimits/{id}/enable")
    void enableCisConfOrderLimit(@PathVariable("id") String id);

    @Operation(summary = "根据唯一标识禁用医护限制维护。")
    @PutMapping("/cisConfOrderLimits/{id}/disable")
    void disableCisConfOrderLimit(@PathVariable("id") String id);

    @Operation(summary = "根据查询条件对医护限制维护规则进行查询。")
    @GetMapping("/cisConfOrderLimits/{cisConfOrderLimitId}/cisConfOrderLimitRules")
    List<CisConfOrderLimitRuleTo> getCisConfOrderLimitRules(@PathVariable("cisConfOrderLimitId") String cisConfOrderLimitId, @ParameterObject @SpringQueryMap CisConfOrderLimitRuleQto cisConfOrderLimitRuleQto);

    @Operation(summary = "根据查询条件对医护限制维护规则进行分页查询。")
    @GetMapping("/cisConfOrderLimit/{cisConfOrderLimitId}/cisConfOrderLimitRules/pages")
    GridResultSet<CisConfOrderLimitRuleTo> getCisConfOrderLimitRulePage(@PathVariable("cisConfOrderLimitId") String cisConfOrderLimitId, @ParameterObject @SpringQueryMap CisConfOrderLimitRuleQto cisConfOrderLimitRuleQto);

    @Operation(summary = "根据唯一标识返回医护限制维护规则。")
    @GetMapping("/cisConfOrderLimits/xId/cisConfOrderLimitRules/{id:.+}")
    CisConfOrderLimitRuleTo getCisConfOrderLimitRuleById(@PathVariable("id") String id);

    @Operation(summary = "创建医护限制维护规则。")
    @PostMapping("/cisConfOrderLimits/{cisConfOrderLimitId}/cisConfOrderLimitRules")
    CisConfOrderLimitRuleTo createCisConfOrderLimitRule(@PathVariable("cisConfOrderLimitId") String cisConfOrderLimitId, @RequestBody @Valid CisConfOrderLimitRuleNto cisConfOrderLimitRuleNto);

    @Operation(summary = "根据唯一标识修改医护限制维护规则。")
    @PutMapping("/cisConfOrderLimits/xId/cisConfOrderLimitRules/{id:.+}")
    void updateCisConfOrderLimitRule(@PathVariable("id") String id, @RequestBody @Valid CisConfOrderLimitRuleEto cisConfOrderLimitRuleEto);

    @Operation(summary = "根据唯一标识启用医护限制维护规则。")
    @PutMapping("/cisConfOrderLimits/xId/cisConfOrderLimitRules/{id}/enable")
    void enableCisConfOrderLimitRule(@PathVariable("id") String id);

    @Operation(summary = "根据唯一标识禁用医护限制维护规则。")
    @PutMapping("/cisConfOrderLimits/xId/cisConfOrderLimitRules/{id}/disable")
    void disableCisConfOrderLimitRule(@PathVariable("id") String id);

    @Operation(summary = "根据查询条件对医护限制明细进行查询。")
    @GetMapping("/cisConfOrderLimits/{cisConfOrderLimitId}/cisConfOrderLimitDetails")
    List<CisConfOrderLimitDetailTo> getCisConfOrderLimitDetails(@PathVariable("cisConfOrderLimitId") String cisConfOrderLimitId, @ParameterObject @SpringQueryMap CisConfOrderLimitDetailQto cisConfOrderLimitDetailQto);

    @Operation(summary = "根据查询条件对医护限制明细进行分页查询。")
    @GetMapping("/cisConfOrderLimit/{cisConfOrderLimitId}/cisConfOrderLimitDetails/pages")
    GridResultSet<CisConfOrderLimitDetailTo> getCisConfOrderLimitDetailPage(@PathVariable("cisConfOrderLimitId") String cisConfOrderLimitId, @ParameterObject @SpringQueryMap CisConfOrderLimitDetailQto cisConfOrderLimitDetailQto);

    @Operation(summary = "创建医护限制明细。")
    @PostMapping("/cisConfOrderLimits/{cisConfOrderLimitId}/cisConfOrderLimitDetails")
    CisConfOrderLimitDetailTo createCisConfOrderLimitDetail(@PathVariable("cisConfOrderLimitId") String cisConfOrderLimitId, @RequestBody @Valid CisConfOrderLimitDetailNto cisConfOrderLimitDetailNto);

    @Operation(summary = "根据唯一标识修改医护限制明细。")
    @PutMapping("/cisConfOrderLimits/xId/cisConfOrderLimitDetails/{id:.+}")
    void updateCisConfOrderLimitDetail(@PathVariable("id") String id, @RequestBody @Valid CisConfOrderLimitDetailEto cisConfOrderLimitDetailEto);

    @Operation(summary = "根据唯一标识启用医护限制明细。")
    @PutMapping("/cisConfOrderLimits/xId/cisConfOrderLimitDetails/{id}/enable")
    void enableCisConfOrderLimitDetail(@PathVariable("id") String id);

    @Operation(summary = "根据唯一标识禁用医护限制明细。")
    @PutMapping("/cisConfOrderLimits/xId/cisConfOrderLimitDetails/{id}/disable")
    void disableCisConfOrderLimitDetail(@PathVariable("id") String id);

    @Operation(summary = "获取护士限制")
    @GetMapping("/cisConfOrderLimits/nurseLimit")
    List<CisConfOrderLimitTo> getNurseLimit();
}