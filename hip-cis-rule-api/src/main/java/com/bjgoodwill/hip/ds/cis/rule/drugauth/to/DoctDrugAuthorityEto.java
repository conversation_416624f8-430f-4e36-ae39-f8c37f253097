package com.bjgoodwill.hip.ds.cis.rule.drugauth.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "医生开立药品权限")
public class DoctDrugAuthorityEto extends BaseEto {

    @Serial
    private static final long serialVersionUID = -1158189987046601671L;

    @Schema(description = "所属科室")
    private String administrativeDept;
    @Schema(description = "中草药权限")
    private Boolean chineseHerbFlag;
    @Schema(description = "中成药权限")
    private Boolean chinesePatentFlag;
    @Schema(description = "处方权限")
    private Boolean canPresc;
    @Schema(description = "医师资格证")
    private Boolean physicianCertificate;
    @Schema(description = "可做手术等级")
    private String canSurgeryLevel;
    @Schema(description = "远程会诊权限")
    private Boolean isRemoteConsultation;
    @Schema(description = "抗菌药使用培训标识")
    private Boolean antiTrainingFlag;
    @Schema(description = "员工维护增加抗菌药物分级管理权限：1、限制使用级、2、非限制使用级、3、特殊使用级")
    private String antiTrainingLevel;
    @Schema(description = "药理属性")
    private String toxiPropertyNames;
    @Schema(description = "贵重药权限")
    private Boolean canValuableMedicineFlag;
    @Schema(description = "抗肿瘤药权限")
    private Boolean canAntineoplasticFlag;
    @Schema(description = "抗肿瘤药权限等级")
    private String antineoplasticLevel;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "所属科室名称")
    private String administrativeDeptName;

    @NotBlank(message = "所属科室不能为空！")
    public String getAdministrativeDept() {
        return administrativeDept;
    }

    public void setAdministrativeDept(String administrativeDept) {
        this.administrativeDept = administrativeDept;
    }

    public Boolean getChineseHerbFlag() {
        return chineseHerbFlag;
    }

    public void setChineseHerbFlag(Boolean chineseHerbFlag) {
        this.chineseHerbFlag = chineseHerbFlag;
    }

    public Boolean getChinesePatentFlag() {
        return chinesePatentFlag;
    }

    public void setChinesePatentFlag(Boolean chinesePatentFlag) {
        this.chinesePatentFlag = chinesePatentFlag;
    }

    public Boolean getCanPresc() {
        return canPresc;
    }

    public void setCanPresc(Boolean canPresc) {
        this.canPresc = canPresc;
    }

    public Boolean getPhysicianCertificate() {
        return physicianCertificate;
    }

    public void setPhysicianCertificate(Boolean physicianCertificate) {
        this.physicianCertificate = physicianCertificate;
    }

    public String getCanSurgeryLevel() {
        return canSurgeryLevel;
    }

    public void setCanSurgeryLevel(String canSurgeryLevel) {
        this.canSurgeryLevel = StringUtils.trimToNull(canSurgeryLevel);
    }

    public Boolean getIsRemoteConsultation() {
        return isRemoteConsultation;
    }

    public void setIsRemoteConsultation(Boolean isRemoteConsultation) {
        this.isRemoteConsultation = isRemoteConsultation;
    }

    public Boolean getAntiTrainingFlag() {
        return antiTrainingFlag;
    }

    public void setAntiTrainingFlag(Boolean antiTrainingFlag) {
        this.antiTrainingFlag = antiTrainingFlag;
    }

    public String getAntiTrainingLevel() {
        return antiTrainingLevel;
    }

    public void setAntiTrainingLevel(String antiTrainingLevel) {
        this.antiTrainingLevel = StringUtils.trimToNull(antiTrainingLevel);
    }

    public Boolean getCanValuableMedicineFlag() {
        return canValuableMedicineFlag;
    }

    public void setCanValuableMedicineFlag(Boolean canValuableMedicineFlag) {
        this.canValuableMedicineFlag = canValuableMedicineFlag;
    }

    public Boolean getCanAntineoplasticFlag() {
        return canAntineoplasticFlag;
    }

    public void setCanAntineoplasticFlag(Boolean canAntineoplasticFlag) {
        this.canAntineoplasticFlag = canAntineoplasticFlag;
    }

    @NotNull(message = "版本不能为空")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getToxiPropertyNames() {
        return toxiPropertyNames;
    }

    public void setToxiPropertyNames(String toxiPropertyNames) {
        this.toxiPropertyNames = toxiPropertyNames;
    }

    public String getAdministrativeDeptName() {
        return administrativeDeptName;
    }

    public void setAdministrativeDeptName(String administrativeDeptName) {
        this.administrativeDeptName = administrativeDeptName;
    }

    public String getAntineoplasticLevel() {
        return antineoplasticLevel;
    }

    public void setAntineoplasticLevel(String antineoplasticLevel) {
        this.antineoplasticLevel = antineoplasticLevel;
    }
}