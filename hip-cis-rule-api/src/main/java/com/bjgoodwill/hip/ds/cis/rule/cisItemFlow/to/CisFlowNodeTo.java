package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Schema(description = "医嘱节点")
public class CisFlowNodeTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -2244679987900016481L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "节点名称")
    private String nodeName;
    @Schema(description = "方法名称")
    private String methodName;
    @Schema(description = "方法类名")
    private String methodClassName;
    @Schema(description = "顺序")
    private Double sequence;
    @Schema(description = "是否固定")
    private Boolean isFixed;
    @Schema(description = "已启用")
    private boolean enabled;
    @Schema(description = "是否可以作废")
    private Boolean isCanCancel;
    @Schema(description = "是否可以退费")
    private Boolean isCanRefund;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "医嘱节点从表列表")
    private List<CisFlowNodeSubTo> cisFlowNodeSubs;
    @Schema(description = "创建的人员名称")
    private String createdStaffName;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getMethodClassName() {
        return methodClassName;
    }

    public void setMethodClassName(String methodClassName) {
        this.methodClassName = methodClassName;
    }

    public Double getSequence() {
        return sequence;
    }

    public void setSequence(Double sequence) {
        this.sequence = sequence;
    }

    public Boolean getIsFixed() {
        return isFixed;
    }

    public void setIsFixed(Boolean isFixed) {
        this.isFixed = isFixed;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getIsCanCancel() {
        return isCanCancel;
    }

    public void setIsCanCancel(Boolean isCanCancel) {
        this.isCanCancel = isCanCancel;
    }

    public Boolean getIsCanRefund() {
        return isCanRefund;
    }

    public void setIsCanRefund(Boolean isCanRefund) {
        this.isCanRefund = isCanRefund;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public List<CisFlowNodeSubTo> getCisFlowNodeSubs() {
        return cisFlowNodeSubs;
    }

    public void setCisFlowNodeSubs(List<CisFlowNodeSubTo> cisFlowNodeSubs) {
        this.cisFlowNodeSubs = cisFlowNodeSubs;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisFlowNodeTo other = (CisFlowNodeTo) obj;
        return Objects.equals(id, other.id);
    }
}