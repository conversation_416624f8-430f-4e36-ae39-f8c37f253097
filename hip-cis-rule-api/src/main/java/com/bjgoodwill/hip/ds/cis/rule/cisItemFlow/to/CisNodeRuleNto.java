package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "节点规则")
public class CisNodeRuleNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -1660311502439027600L;

    @Schema(description = "顺序")
    private Double sequence;
    @Schema(description = "限制方法路径")
    private String ruleMethodName;
    @Schema(description = "限制方法路径")
    private String ruleMethodPath;
    @Schema(description = "限制方法说明")
    private String remark;
    @Schema(description = "参数")
    private String parameter;
    @Schema(description = "多医嘱项目一起调用")
    private String isMulTypesUse;

    @NotNull(message = "顺序不能为空！")
    public Double getSequence() {
        return sequence;
    }

    public void setSequence(Double sequence) {
        this.sequence = sequence;
    }

    public String getRuleMethodName() {
        return ruleMethodName;
    }

    public void setRuleMethodName(String ruleMethodName) {
        this.ruleMethodName = StringUtils.trimToNull(ruleMethodName);
    }

    public String getRuleMethodPath() {
        return ruleMethodPath;
    }

    public void setRuleMethodPath(String ruleMethodPath) {
        this.ruleMethodPath = StringUtils.trimToNull(ruleMethodPath);
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = StringUtils.trimToNull(remark);
    }

    public String getParameter() {
        return parameter;
    }

    public void setParameter(String parameter) {
        this.parameter = StringUtils.trimToNull(parameter);
    }

    public String getIsMulTypesUse() {
        return isMulTypesUse;
    }

    public void setIsMulTypesUse(String isMulTypesUse) {
        this.isMulTypesUse = StringUtils.trimToNull(isMulTypesUse);
    }
}