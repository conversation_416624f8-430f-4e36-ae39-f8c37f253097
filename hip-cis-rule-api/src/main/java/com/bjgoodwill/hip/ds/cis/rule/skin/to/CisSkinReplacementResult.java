package com.bjgoodwill.hip.ds.cis.rule.skin.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-11-26 11:18
 * @className: CisSkinReplacementResult
 * @description: 皮试结果返回
 **/
public class CisSkinReplacementResult implements Serializable {

    @Schema(description = "是否需要皮试")
    private Boolean needSkinFlag;

    @Schema(description = "皮试替代药")
    private List<CisSkinReplacementTo> skinReplacementToList;

    public CisSkinReplacementResult(Boolean needSkinFlag, List<CisSkinReplacementTo> skinReplacementToList) {
        setNeedSkinFlag(needSkinFlag);
        setSkinReplacementToList(skinReplacementToList);
    }

    public CisSkinReplacementResult(Boolean needSkinFlag) {
        setNeedSkinFlag(needSkinFlag);
    }

    public Boolean getNeedSkinFlag() {
        return needSkinFlag;
    }

    public void setNeedSkinFlag(Boolean needSkinFlag) {
        this.needSkinFlag = needSkinFlag;
    }

    public List<CisSkinReplacementTo> getSkinReplacementToList() {
        return skinReplacementToList;
    }

    public void setSkinReplacementToList(List<CisSkinReplacementTo> skinReplacementToList) {
        this.skinReplacementToList = skinReplacementToList;
    }
}