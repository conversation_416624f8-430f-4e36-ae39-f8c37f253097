package com.bjgoodwill.hip.ds.cis.rule.skin.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rule.skin.to.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "皮试规则领域服务", description = "皮试规则领域服务")
public interface CisSkinLimitService {

    @Operation(summary = "P0根据查询条件对皮试规则进行查询。")
    @GetMapping("/cisSkinLimits")
    List<CisSkinLimitTo> getCisSkinLimits(@ParameterObject @SpringQueryMap CisSkinLimitQto cisSkinLimitQto);

    @Operation(summary = "根据查询条件对皮试规则进行分页查询。")
    @GetMapping("/cisSkinLimits/pages")
    GridResultSet<CisSkinLimitTo> getCisSkinLimitPage(@ParameterObject @SpringQueryMap CisSkinLimitQto cisSkinLimitQto);

    @Operation(summary = "根据唯一标识返回皮试规则。")
    @GetMapping("/cisSkinLimits/{id:.+}")
    CisSkinLimitTo getCisSkinLimitById(@PathVariable("id") String id);

    @Operation(summary = "创建皮试规则。")
    @PostMapping("/cisSkinLimits")
    CisSkinLimitTo createCisSkinLimit(@RequestBody @Valid CisSkinLimitNto cisSkinLimitNto);

    @Operation(summary = "根据唯一标识修改皮试规则。")
    @PutMapping("/cisSkinLimits/{id:.+}")
    void updateCisSkinLimit(@PathVariable("id") String id, @RequestBody @Valid CisSkinLimitEto cisSkinLimitEto);

    @Operation(summary = "根据唯一标识删除皮试规则。")
    @DeleteMapping("/cisSkinLimits/{id:.+}")
    void deleteCisSkinLimit(@PathVariable("id") String id);

    @Operation(summary = "根据查询条件对皮试管理替代进行查询。")
    @GetMapping("/cisSkinLimits/{cisSkinLimitId}/cisSkinReplacements")
    List<CisSkinReplacementTo> getCisSkinReplacements(@PathVariable("cisSkinLimitId") String cisSkinLimitId, @ParameterObject @SpringQueryMap CisSkinReplacementQto cisSkinReplacementQto);

    @Operation(summary = "根据查询条件对皮试管理替代进行分页查询。")
    @GetMapping("/cisSkinLimit/{cisSkinLimitId}/cisSkinReplacements/pages")
    GridResultSet<CisSkinReplacementTo> getCisSkinReplacementPage(@PathVariable("cisSkinLimitId") String cisSkinLimitId, @ParameterObject @SpringQueryMap CisSkinReplacementQto cisSkinReplacementQto);

    @Operation(summary = "创建皮试管理替代。")
    @PostMapping("/cisSkinLimits/{cisSkinLimitId}/cisSkinReplacements")
    CisSkinReplacementTo createCisSkinReplacement(@PathVariable("cisSkinLimitId") String cisSkinLimitId, @RequestBody @Valid CisSkinReplacementNto cisSkinReplacementNto);

    @Operation(summary = "根据唯一标识修改皮试管理替代。")
    @PutMapping("/cisSkinLimits/xId/cisSkinReplacements/{id:.+}")
    void updateCisSkinReplacement(@PathVariable("id") String id, @RequestBody @Valid CisSkinReplacementEto cisSkinReplacementEto);

    @Operation(summary = "根据唯一标识删除皮试管理替代。")
    @DeleteMapping("/cisSkinLimits/xId/cisSkinReplacements/{id:.+}")
    void deleteCisSkinReplacement(@PathVariable("id") String id);

    @Operation(summary = "根据药品编码查询是否需要皮试和皮试药品。")
    @GetMapping("/cisSkinLimits/drugCode/{drugCode:.+}")
    CisSkinReplacementResult getCisSkinLimitReplacementCode(@PathVariable("drugCode") String drugCode, @RequestParam String visitCode, @RequestParam Boolean childFlag, @RequestParam String orgCode);

    @Operation(summary = "P0批量保存皮试管理替代。")
    @PostMapping("/cisSkinLimits/{cisSkinLimitId}/cisSkinReplacements/save-batch")
    void createCisSkinReplacementBatch(@PathVariable("cisSkinLimitId") String cisSkinLimitId, @RequestBody @Valid List<CisSkinReplacementNto> cisSkinReplacementNto);

    @Operation(summary = "P0根据cisSkinLimitId删除皮试管理替代。")
    @DeleteMapping("/cisSkinLimits/cisSkinReplacements/cisSkinLimitId/{cisSkinLimitId:.+}")
    void deleteCisSkinReplacementByCisSkinLimitId(@PathVariable("cisSkinLimitId") String cisSkinLimitId);

    @Operation(summary = "P0批量创建皮试规则。")
    @PostMapping("/cisSkinLimits/create-batch")
    void batchCreateCisSkinLimit(@RequestBody @Valid List<CisSkinLimitNto> cisSkinLimitNtos);

    @Operation(summary = "P0根据唯药品编码删除皮试规则。")
    @DeleteMapping("/cisSkinLimits/delete-by-drugCode")
    void deleteCisSkinLimitByDrugCode(@RequestParam("drugCodes") List<String> drugCodes);
}