package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus;

public enum DetailTypeEnum {

    DRUG("DRUG", "药品"),
    USAGE("USAGE", "用法"),
    SystemType("SystemType", "医嘱类型"),
    DIAGNOSIS("DIAGNOSIS", "诊断"),
    ITEMCODE("ITEMCODE", "医嘱项目编码"),
    ;

    private String value;
    private String name;

    DetailTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

}