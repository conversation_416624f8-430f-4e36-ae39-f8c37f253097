package com.bjgoodwill.hip.ds.cis.rule.drugLimit.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "医生限制")
public class DocLimitQto extends CisDrugLimitQto  {

    @Serial
    private static final long serialVersionUID = -8788814344950682907L;

    @Schema(description = "docCode")
    private String docCode;
    @Schema(description = "医生名字")
    private String docName;


    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = docCode;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }
}