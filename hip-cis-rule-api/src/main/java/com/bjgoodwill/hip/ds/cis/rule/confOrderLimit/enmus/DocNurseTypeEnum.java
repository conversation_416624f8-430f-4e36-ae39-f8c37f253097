package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus;

public enum DocNurseTypeEnum {
    DOC("DOC", "医生"),
    NURSE("NURSE", "护士");
    private String value;
    private String name;

    DocNurseTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

}