package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Schema(description = "医嘱节点从表")
public class CisFlowNodeSubTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -6252711302034124040L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "医嘱节点标识")
    private String cisFlowNodeId;
    @Schema(description = "医嘱类型")
    private SystemTypeEnum cisSystemType;
    @Schema(description = "医嘱状态")
    private String cisOrderStatus;
    @Schema(description = "申请单状态")
    private String cisApplyStatus;
    @Schema(description = "申请单从表状态")
    private String cisSubApplyStatus;
    @Schema(description = "是否计费")
    private Boolean isBilling;
    @Schema(description = "是发送pda")
    private Boolean isSendPda;
    @Schema(description = "已启用")
    private boolean enabled;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "节点规则列表")
    private List<CisNodeRuleTo> nodeRules;
    @Schema(description = "创建的人员名称")
    private String createdStaffName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCisFlowNodeId() {
        return cisFlowNodeId;
    }

    public void setCisFlowNodeId(String cisFlowNodeId) {
        this.cisFlowNodeId = cisFlowNodeId;
    }

    public SystemTypeEnum getCisSystemType() {
        return cisSystemType;
    }

    public void setCisSystemType(SystemTypeEnum cisSystemType) {
        this.cisSystemType = cisSystemType;
    }

    public String getCisOrderStatus() {
        return cisOrderStatus;
    }

    public void setCisOrderStatus(String cisOrderStatus) {
        this.cisOrderStatus = cisOrderStatus;
    }

    public String getCisApplyStatus() {
        return cisApplyStatus;
    }

    public void setCisApplyStatus(String cisApplyStatus) {
        this.cisApplyStatus = cisApplyStatus;
    }

    public String getCisSubApplyStatus() {
        return cisSubApplyStatus;
    }

    public void setCisSubApplyStatus(String cisSubApplyStatus) {
        this.cisSubApplyStatus = cisSubApplyStatus;
    }

    public Boolean getIsBilling() {
        return isBilling;
    }

    public void setIsBilling(Boolean isBilling) {
        this.isBilling = isBilling;
    }

    public Boolean getIsSendPda() {
        return isSendPda;
    }

    public void setIsSendPda(Boolean isSendPda) {
        this.isSendPda = isSendPda;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public List<CisNodeRuleTo> getNodeRules() {
        return nodeRules;
    }

    public void setNodeRules(List<CisNodeRuleTo> nodeRules) {
        this.nodeRules = nodeRules;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisFlowNodeSubTo other = (CisFlowNodeSubTo) obj;
        return Objects.equals(id, other.id);
    }
}