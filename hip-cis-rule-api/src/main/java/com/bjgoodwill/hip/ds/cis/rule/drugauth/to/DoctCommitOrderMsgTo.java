package com.bjgoodwill.hip.ds.cis.rule.drugauth.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Schema(description = "医生提交医嘱信息说明")
public class DoctCommitOrderMsgTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -4950611776436591098L;

    @Schema(description = "医嘱唯一标识")
    private String id;
    @Schema(description = "说明")
    private List<String> msg;

    public DoctCommitOrderMsgTo() {
    }

    public DoctCommitOrderMsgTo(String id, List<String> msg) {
        this.id = id;
        this.msg = msg;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<String> getMsg() {
        return msg;
    }

    public void setMsg(List<String> msg) {
        this.msg = msg;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null)
            return false;
        if (getClass() != o.getClass())
            return false;
        DoctCommitOrderMsgTo other = (DoctCommitOrderMsgTo) o;
        return Objects.equals(id, other.id);
    }
}
