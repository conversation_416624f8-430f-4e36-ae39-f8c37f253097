package com.bjgoodwill.hip.ds.cis.rule.drugLimit.to;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "OrgLimit")
public class OrgLimitEto extends CisDrugLimitEto {

    @Serial
    private static final long serialVersionUID = -3878604690434965931L;

    @Schema(description = "科室编码")
    private String orgCode;
    @Schema(description = "科室名称")
    private String orgName;
    @Schema(description = "院区编码")
    private String hospitalCode;
    @Schema(description = "院区名称")
    private String hospitalName;
    @Schema(description = "工作组类型")
    private String workGroupTypeCode;
    @Schema(description = "工作组类型名称")
    private String workGroupTypeName;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = StringUtils.trimToNull(orgCode);
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = StringUtils.trimToNull(orgName);
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getWorkGroupTypeCode() {
        return workGroupTypeCode;
    }

    public void setWorkGroupTypeCode(String workGroupTypeCode) {
        this.workGroupTypeCode = workGroupTypeCode;
    }

    public String getWorkGroupTypeName() {
        return workGroupTypeName;
    }

    public void setWorkGroupTypeName(String workGroupTypeName) {
        this.workGroupTypeName = workGroupTypeName;
    }
}