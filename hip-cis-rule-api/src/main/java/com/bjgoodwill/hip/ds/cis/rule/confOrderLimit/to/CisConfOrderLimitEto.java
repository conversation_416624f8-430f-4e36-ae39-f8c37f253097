package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DocNurseTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "医护限制维护")
public class CisConfOrderLimitEto extends BaseEto {

    @Serial
    private static final long serialVersionUID = -8073137044425555425L;

    @Schema(description = "节点名称")
    private String nodeName;
    @Schema(description = "inputCode")
    private String inputCode;
    @Schema(description = "已启用")
    private boolean enabled;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "type")
    private DocNurseTypeEnum type;

    @NotBlank(message = "节点名称不能为空！")
    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = StringUtils.trimToNull(nodeName);
    }

    public String getInputCode() {
        return inputCode;
    }

    public void setInputCode(String inputCode) {
        this.inputCode = StringUtils.trimToNull(inputCode);
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @NotNull(message = "版本不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public DocNurseTypeEnum getType() {
        return type;
    }

    public void setType(DocNurseTypeEnum type) {
        this.type = type;
    }
}