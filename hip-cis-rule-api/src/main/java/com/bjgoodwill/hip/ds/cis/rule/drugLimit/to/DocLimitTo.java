package com.bjgoodwill.hip.ds.cis.rule.drugLimit.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "医生限制")
public class DocLimitTo extends CisDrugLimitTo {

    @Serial
    private static final long serialVersionUID = -7774118293037061623L;

    @Schema(description = "docCode")
    private String docCode;
    @Schema(description = "医生名字")
    private String docName;
    @Schema(description = "人员科室")
    private String personOrgCode;
    @Schema(description = "人员科室名称")
    private String personOrgName;
    @Schema(description = "人员类型编码")
    private String personTypeCode;
    @Schema(description = "人员类型名称")
    private String personTypeName;

    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = docCode;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public String getPersonOrgCode() {
        return personOrgCode;
    }

    public void setPersonOrgCode(String personOrgCode) {
        this.personOrgCode = personOrgCode;
    }

    public String getPersonOrgName() {
        return personOrgName;
    }

    public void setPersonOrgName(String personOrgName) {
        this.personOrgName = personOrgName;
    }

    public String getPersonTypeCode() {
        return personTypeCode;
    }

    public void setPersonTypeCode(String personTypeCode) {
        this.personTypeCode = personTypeCode;
    }

    public String getPersonTypeName() {
        return personTypeName;
    }

    public void setPersonTypeName(String personTypeName) {
        this.personTypeName = personTypeName;
    }
}