package com.bjgoodwill.hip.ds.cis.rule.drugauth.service;

import com.bjgoodwill.hip.business.util.cis.util.DoctAuthCommonQto;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.to.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "医生开立药品权限领域服务", description = "医生开立药品权限领域服务")
public interface DoctDrugAuthorityService {

    @Operation(summary = "P0根据医生编码查询医生权限信息")
    @GetMapping("/{staff-id:.+}")
    DoctDrugAuthorityTo getDoctDrugAuthorityByStaffId(@PathVariable("staff-id") String staffId);

    @Operation(summary = "根据手术诊断编码编码查询医生权限可做手术级别")
    @GetMapping("/getOperationAuthority")
    void getOperationAuthority(@RequestParam("operationDiagnosisCode") String operationDiagnosisCode, @RequestParam("doctorCode") String doctorCode);

    @Operation(summary = "P0根据条件查询医生开立权限说明")
    @PostMapping("/doctdrugauthoritymsg/query")
    List<DoctCommitOrderMsgTo> getDoctDrugAuthorityMsg(@RequestBody @Valid List<DoctCommitDrugAuthorityMsgQto> qtoList);

    @Operation(summary = "根据ID,修改权限数据")
    @PutMapping("/{staff-id:.+}")
    void updateDoctorDrugAuthority(@PathVariable("staff-id") String staffId, @RequestBody DoctDrugAuthorityEto doctDrugAuthorityEto);

    @Operation(summary = "根据ID,删除权限数据")
    @DeleteMapping("/{staff-id:.+}")
    void deleteDoctorDrugAuthority(@PathVariable("staff-id") String staffId);

    @Operation(summary = "新增权限数据")
    @PostMapping("/DoctDrugAuthority")
    void createDoctDrugAuthority(@RequestBody DoctDrugAuthorityNto doctDrugAuthorityNto);

    @Operation(summary = "根据医生编码查询医生权限信息")
    @GetMapping("/DoctDrugAuthority/query")
    List<DoctDrugAuthorityTo> queryDoctDrugAuthorityList(@RequestParam List<String> staffCodes);

    @Operation(summary = "P0根据条件查询项目有效性说明")
    @PostMapping("/serviceitemstatusmsg/query")
    List<DoctCommitOrderMsgTo> getServiceItemStatusMsg(@RequestBody @Valid List<DoctCommitValidityMsgQto> qtoList);

    @Operation(summary = "根据条件查询有无诊断说明")
    @PostMapping("/cisipddiagnosemsg/query")
    List<DoctCommitOrderMsgTo> getCisIpdDiagnoseMsg(@RequestBody @Valid DoctCommitDiagnoseMsgQto msgQto);

    /*@Operation(summary = "根据条件查询重复说明")
    @GetMapping("/cisipdrepeatmsg/query")
    List<DoctCommitOrderMsgTo> getCisIpdRepeatMsg(@RequestParam @Valid DoctCommitRepeatMsgQto msgQto);*/

    @Operation(summary = "P0根据条件查询科室限制说明")
    @PostMapping("/cisipdorglimitmsg/query")
    List<DoctCommitOrderMsgTo> getOrgLimitMsg(@RequestBody @Valid List<DoctCommitOrgLimitMsgQto> qtoList);


    @Operation(summary = "P0申请单校验入口")
    @PostMapping("/doctdrugauthoritymsg/apply")
    List<DoctAuthCommonQto> verifyDoctDrugAuthority(@RequestBody @Valid List<DoctAuthCommonQto> qtoList);

    @Operation(summary = "根据医生编码查询医生抗菌药权限")
    @GetMapping("/{staff-id:.+}/antiTrainingLevel")
    Boolean verifyAntiTraining(@PathVariable("staff-id") String staffId, @RequestParam String antiTrainingLevel);


    @Operation(summary = "根据医生编码查询医生多种抗菌药，肿瘤药权限")
    @GetMapping("/{staff-id:.+}/mul/drugLevel")
    CisVerifyTo verifyMulDrugLevel(@PathVariable("staff-id") String staffId, @RequestBody CisVerifyQto verifyQto);


    @Operation(summary = "P0根据医生编码查询医生权限信息")
    @GetMapping("/dept/{dept-code:.+}")
    List<DoctDrugAuthorityTo> getDoctDrugAuthorityByDeptCode(@PathVariable("dept-code") String deptCde);
}
