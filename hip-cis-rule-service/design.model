{"replaceJavaCode": false, "packageName": "com.bjgoodwill.hip.ds.cis.rule", "shortName": "cis_rule", "entities": [{"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "19b95225-9c35-410f-a987-ac81a385b6cd", "name": "CisFlowNode", "table": "cis_flow_node", "comment": "医嘱节点", "subModule": "cisItemFlow", "properties": [{"id": "f1f7b17b-7835-4ac8-b9f4-78724bcf90ca", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "SNOWFLAKE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "81ee4995-412e-439b-af76-918547808122", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "type": "Basic", "name": "nodeName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "node_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "节点名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "be79b958-d3ad-40d3-81ed-c4e8c2a118cb", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "type": "Basic", "name": "methodName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "method_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "方法名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "4edbd2d3-1314-468f-911a-6969d0b6f1ff", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "type": "Basic", "name": "methodClassName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "method_class_name", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "方法类名", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "1d0ccc25-8409-44fc-b5ae-088697cdda8f", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "type": "Basic", "name": "sequence", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "sequence", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "顺序", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "92bca08a-eb98-4bcb-a52c-48ced064045e", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "type": "Basic", "name": "isFixed", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_fixed", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是否固定", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "14b1e0a1-319d-47be-9de0-cd01525f0316", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "type": "Enabled", "name": "enabled", "javaType": "boolean_", "enumName": null, "idGenerationType": null, "column": "enabled", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "已启用", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": true}, {"id": "c8ad76bc-2a13-4e84-9fa3-b8733bd735fb", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "type": "Basic", "name": "isCanCancel", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_can_cancel", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是否可以作废", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "6aee6b57-ae05-48be-bdb7-caed06c73dda", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "type": "Basic", "name": "isCanRefund", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_can_refund", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是否可以退费", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "52cd259a-0b1a-4cee-8d45-701f0eb7bcc4", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "type": "CreatedStaff", "name": "createdStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff", "length": 64, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "fcba38e0-ff7a-4afc-b864-fcae9e1d09f9", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "name": "CisFlowNodeSub", "table": "cis_flow_node_sub", "comment": "医嘱节点从表", "subModule": "cisItemFlow", "properties": [{"id": "02ca430d-d3cb-4c8b-baea-b1038f64ac49", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "SNOWFLAKE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "a1df4e1a-a000-42f2-a55f-539ab02556ba", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "type": "Basic", "name": "cisFlowNodeId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_flow_node_id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "医嘱节点标识", "associationType": "COMPOSITION", "associatedEntityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": true}, {"id": "ba225735-2e12-4fe4-b456-cf7c7b418843", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "type": "Basic", "name": "cisSystemType", "javaType": "Enum", "enumName": "SystemTypeEnum", "idGenerationType": null, "column": "cis_system_type", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱类型", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "53f087b8-c1a8-4951-9e55-b3dc3937c31c", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "type": "Basic", "name": "cisOrderStatus", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_order_status", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱状态", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "ae4a6f82-382f-4b77-82d9-9df2ffd28f6d", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "type": "Basic", "name": "cisApplyStatus", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_apply_status", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "申请单状态", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "25e16657-02da-42d0-a9ee-91a692f57014", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "type": "Basic", "name": "cisSubApplyStatus", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_sub_apply_status", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "申请单从表状态", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "51ddd58a-c0c8-47e5-a260-02d94664b962", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "type": "Basic", "name": "isBilling", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_billing", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是否计费", "associationType": "ASSOCIATION", "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "5c1d1fdd-3d74-4f86-bae4-9f77fe5d6bc2", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "type": "Basic", "name": "isSendPda", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_send_pda", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是发送pda", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "ac48c395-934c-4ce4-ba2e-d515f0455798", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "type": "Enabled", "name": "enabled", "javaType": "boolean_", "enumName": null, "idGenerationType": null, "column": "enabled", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "已启用", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": true}, {"id": "63cf69bf-8ccf-497d-a773-6f214bf410ea", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "type": "CreatedStaff", "name": "createdStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff", "length": 64, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "3d28cf14-ae03-4cef-9680-4dcbfa07fab4", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "name": "NodeRule", "table": "node_rule", "comment": "节点规则", "subModule": "cisItemFlow", "properties": [{"id": "24a13836-4717-4a89-9207-d2e17bc0ccaa", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "SNOWFLAKE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "200b48ea-5ae6-43ab-bf03-bbf4b18bce96", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "type": "Basic", "name": "cisFlowNodeSubId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_flow_node_sub_id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "医嘱节点从表标识", "associationType": "COMPOSITION", "associatedEntityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": true}, {"id": "41b24c22-6ab6-4dba-a667-2baf756b08c5", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "type": "Basic", "name": "sequence", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "sequence", "length": null, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "顺序", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "9e447bc7-54fe-4377-89ca-64218a60378e", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "type": "Basic", "name": "ruleMethodName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "rule_method_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "限制方法路径", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "ba3516ac-72ec-4dde-a2de-b0b52d05c367", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "type": "Basic", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "rule_method_path", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "限制方法路径", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "317904f7-7db6-4a4f-85ae-07ade5494357", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "type": "Basic", "name": "remark", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "remark", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "限制方法说明", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "a6e68b1d-b4f1-4275-8441-54acac651ffd", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "type": "Basic", "name": "parameter", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "parameter", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "参数", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "714806de-3fef-42e0-953a-b964feac6c76", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "type": "Enabled", "name": "enabled", "javaType": "boolean_", "enumName": null, "idGenerationType": null, "column": "enabled", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "已启用", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": true}, {"id": "cd321fa6-6e1e-4fee-9a62-99446e65786c", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "type": "Basic", "name": "isMulTypesUse", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "is_mul_types_use", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "多医嘱项目一起调用", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "0f35b726-f261-400b-ac06-d64f54abc0da", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "type": "CreatedStaff", "name": "createdStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff", "length": 64, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "08593987-d209-4a37-97a1-f6b66c07f291", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}]}], "services": [{"id": "1a523fd7-2a72-4120-ac95-01caf83285e6", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "superServiceId": null, "comment": "医嘱节点领域服务", "pxyEntities": [{"id": "1cd25c02-ea96-4dce-ae52-5d14edd3ac3d", "serviceId": "1a523fd7-2a72-4120-ac95-01caf83285e6", "entityId": "19b95225-9c35-410f-a987-ac81a385b6cd", "findAll": true, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}, {"id": "3fbee1e8-6167-484a-928b-d903c36f9a3d", "serviceId": "1a523fd7-2a72-4120-ac95-01caf83285e6", "entityId": "80165e4a-4f98-4f02-9511-50fc1491d2f4", "findAll": false, "findOne": true, "create": true, "update": true, "delete": false, "enableAndDisable": true}, {"id": "46a67fc4-a2ab-4518-92f5-7d2df5f4dc95", "serviceId": "1a523fd7-2a72-4120-ac95-01caf83285e6", "entityId": "6d85399a-b4e2-4b22-b0d3-a45a5178a8d3", "findAll": false, "findOne": true, "create": true, "update": true, "delete": false, "enableAndDisable": true}]}]}