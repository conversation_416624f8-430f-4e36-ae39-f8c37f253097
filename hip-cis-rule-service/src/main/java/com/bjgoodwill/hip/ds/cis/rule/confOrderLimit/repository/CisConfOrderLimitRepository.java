package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.repository;

import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.entity.CisConfOrderLimit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.repository.CisConfOrderLimitRepository")
public interface CisConfOrderLimitRepository extends JpaRepository<CisConfOrderLimit, String>, JpaSpecificationExecutor<CisConfOrderLimit> {

    boolean existsByNodeCode(String nodeCode);

    boolean existsByNodeCodeAndIdNot(String nodeCode, String id);

}