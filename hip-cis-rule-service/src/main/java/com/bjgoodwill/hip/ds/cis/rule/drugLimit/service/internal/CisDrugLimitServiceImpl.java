package com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity.CisDrugLimit;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity.DocLimit;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity.OrgLimit;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.CisDrugLimitService;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.internal.assembler.CisDrugLimitAssembler;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.*;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.CisDrugLimitService")
@RequestMapping(value = "/api/rule/drugLimit/drugLimit", produces = "application/json; charset=utf-8")
public class CisDrugLimitServiceImpl implements CisDrugLimitService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisDrugLimitTo> getCisDrugLimits(CisDrugLimitQto cisDrugLimitQto) {
        return CisDrugLimitAssembler.toTos(CisDrugLimit.getCisDrugLimits(cisDrugLimitQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisDrugLimitTo> getCisDrugLimitPage(CisDrugLimitQto cisDrugLimitQto) {
        Page<CisDrugLimit> page = CisDrugLimit.getCisDrugLimitPage(cisDrugLimitQto);
        Page<CisDrugLimitTo> result = page.map(CisDrugLimitAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisDrugLimitTo getCisDrugLimitById(String id) {
        return CisDrugLimitAssembler.toTo(CisDrugLimit.getCisDrugLimitById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisDrugLimitTo createCisDrugLimit(CisDrugLimitNto cisDrugLimitNto) {
        CisDrugLimit cisDrugLimit = CisDrugLimit.newInstanceByNto(cisDrugLimitNto);
		cisDrugLimit = cisDrugLimit.create(cisDrugLimitNto);
		return CisDrugLimitAssembler.toTo(cisDrugLimit);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisDrugLimit(String id, CisDrugLimitEto cisDrugLimitEto) {
        Optional<CisDrugLimit> cisDrugLimitOptional = CisDrugLimit.getCisDrugLimitById(id);
		cisDrugLimitOptional.ifPresent(cisDrugLimit -> cisDrugLimit.update(cisDrugLimitEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisDrugLimit(String id) {
        Optional<CisDrugLimit> cisDrugLimitOptional = CisDrugLimit.getCisDrugLimitById(id);
		cisDrugLimitOptional.ifPresent(cisDrugLimit -> cisDrugLimit.delete());
    }

    //region 医嘱药品开立校验。
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void JudgeDrugLimit(JudgeDrugDto judgeDrugDto) {
       List<CisDrugLimit> cisDrugLimits = CisDrugLimit.findCisDrugLimitsByDrugCodeInAndDeletedFalse(judgeDrugDto.getDrugCodes());
       if (CollectionUtils.isEmpty(cisDrugLimits)) {
           return;
       }

       List<String> docDrugName = new ArrayList<>();
       checkDoctorLimit(cisDrugLimits, judgeDrugDto, docDrugName);
       BusinessAssert.notEmpty(docDrugName, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0006, "当前医生", docDrugName);


       List<String> orgDrugName = new ArrayList<>();
       checkDepartmentLimit(cisDrugLimits, judgeDrugDto, orgDrugName);
       BusinessAssert.notEmpty(orgDrugName, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0006, "当前科室", orgDrugName);
    }


    private void checkDoctorLimit(List<CisDrugLimit> cisDrugLimits, JudgeDrugDto judgeDrugDto, List<String> docDrugName) {
       for (CisDrugLimit limit : cisDrugLimits) {
           if (limit instanceof DocLimit) {
               DocLimit docLimit = (DocLimit) limit;
               if (!docLimit.getDocCode().equals(judgeDrugDto.getDocCode())) {
                   docDrugName.add(docLimit.getDrugName());
               }
           }
       }
    }

    private void checkDepartmentLimit(List<CisDrugLimit> cisDrugLimits, JudgeDrugDto judgeDrugDto, List<String> orgDrugName) {
       for (CisDrugLimit limit : cisDrugLimits) {
           if (limit instanceof OrgLimit) {
               OrgLimit orgLimit = (OrgLimit) limit;
               if (!orgLimit.getOrgCode().equals(judgeDrugDto.getDeptCode())) {
                   orgDrugName.add(orgLimit.getDrugName());
               }
           }
       }
    }
    //endregion



    @InitBinder
	public void initBinder(WebDataBinder binder) {
	}
}