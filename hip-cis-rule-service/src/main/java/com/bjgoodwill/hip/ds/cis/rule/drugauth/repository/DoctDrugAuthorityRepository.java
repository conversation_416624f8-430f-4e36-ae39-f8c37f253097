package com.bjgoodwill.hip.ds.cis.rule.drugauth.repository;

import com.bjgoodwill.hip.ds.cis.rule.drugauth.entity.DoctDrugAuthority;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository("com.bjgoodwill.hip.ds.cis.rule.drugauth.repository.DoctDrugAuthorityRepository")
public interface DoctDrugAuthorityRepository extends JpaRepository<DoctDrugAuthority, String>, JpaSpecificationExecutor<DoctDrugAuthority> {
    List<DoctDrugAuthority> findDoctDrugAuthoritiesByStaffIdInAndDeletedFalse(List<String> strings);

    Optional<DoctDrugAuthority> findByStaffId(String staffId);
}