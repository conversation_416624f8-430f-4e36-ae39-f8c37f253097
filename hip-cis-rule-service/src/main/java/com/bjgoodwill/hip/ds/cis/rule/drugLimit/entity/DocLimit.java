package com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.repository.DocLimitRepository;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "医生限制")
@DiscriminatorValue("02")
public class DocLimit extends CisDrugLimit {

    // 医生编码
    private String docCode;
    // 医生名字
    private String docName;
    // 人员科室
    private String personOrgCode;
    // 人员科室名称
    private String personOrgName;
    // 人员类型编码
    private String personTypeCode;
    // 人员类型名称
    private String personTypeName;

    public static Optional<DocLimit> getDocLimitById(String id) {
        return dao().findById(id);
    }

    public static List<DocLimit> getDocLimits(DocLimitQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static List<DocLimit> getDocLimitsByDrugCodes(List<String> drugCodes) {
        return dao().findDocLimitByDrugCodeInAndDeletedFalse(drugCodes);
    }

    public static Page<DocLimit> getDocLimitPage(DocLimitQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<DocLimit> getSpecification(DocLimitQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getDrugCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugCode"), qto.getDrugCode()));
            }
            if (StringUtils.isNotBlank(qto.getDrugName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugName"), qto.getDrugName()));
            }
            if (StringUtils.isNotBlank(qto.getDocCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("docCode"), qto.getDocCode()));
            }
            if (StringUtils.isNotBlank(qto.getDocName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("docName"), qto.getDocName()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));
            return predicate;
        };
    }

    private static DocLimitRepository dao() {
        return SpringUtil.getBean(DocLimitRepository.class);
    }

    @Comment("")
    @Column(name = "doc_code", nullable = true)
    public String getDocCode() {
        return docCode;
    }

    protected void setDocCode(String docCode) {
        this.docCode = docCode;
    }

    @Comment("医生名字")
    @Column(name = "doc_name", nullable = true)
    public String getDocName() {
        return docName;
    }

    protected void setDocName(String docName) {
        this.docName = docName;
    }

    @Comment("人员科室编码")
    @Column(name = "person_org_code", nullable = true)
    public String getPersonOrgCode() {
        return personOrgCode;
    }

    public void setPersonOrgCode(String personOrgCode) {
        this.personOrgCode = personOrgCode;
    }

    @Comment("人员科室名称")
    @Column(name = "person_org_name", nullable = true)
    public String getPersonOrgName() {
        return personOrgName;
    }

    public void setPersonOrgName(String personOrgName) {
        this.personOrgName = personOrgName;
    }

    @Comment("人员类型编码")
    @Column(name = "person_type_code", nullable = true)
    public String getPersonTypeCode() {
        return personTypeCode;
    }

    public void setPersonTypeCode(String personTypeCode) {
        this.personTypeCode = personTypeCode;
    }

    @Comment("人员类型名称")
    @Column(name = "person_type_name", nullable = true)
    public String getPersonTypeName() {
        return personTypeName;
    }

    public void setPersonTypeName(String personTypeName) {
        this.personTypeName = personTypeName;
    }

    @Override
    public CisDrugLimit create(CisDrugLimitNto cisDrugLimitNto) {
        return create((DocLimitNto) cisDrugLimitNto);
    }

    @Override
    public void update(CisDrugLimitEto cisDrugLimitEto) {
        update((DocLimitEto) cisDrugLimitEto);
    }

    public DocLimit create(DocLimitNto docLimitNto) {
        BusinessAssert.notNull(docLimitNto, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "参数docLimitNto");
        super.create(docLimitNto);

        setDocCode(docLimitNto.getDocCode());
        setDocName(docLimitNto.getDocName());
        setPersonOrgCode(docLimitNto.getPersonOrgCode());
        setPersonOrgName(docLimitNto.getPersonOrgName());
        setPersonTypeCode(docLimitNto.getPersonTypeCode());
        setPersonTypeName(docLimitNto.getPersonTypeName());
        dao().save(this);
        return this;
    }

    public void update(DocLimitEto docLimitEto) {
        super.update(docLimitEto);
        setDocCode(docLimitEto.getDocCode());
        setDocName(docLimitEto.getDocName());
        setPersonOrgCode(docLimitEto.getPersonOrgCode());
        setPersonOrgName(docLimitEto.getPersonOrgName());
        setPersonTypeCode(docLimitEto.getPersonTypeCode());
        setPersonTypeName(docLimitEto.getPersonTypeName());
    }

    public void delete() {
        dao().delete(this);
    }

}
