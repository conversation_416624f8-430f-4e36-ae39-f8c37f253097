package com.bjgoodwill.hip.ds.cis.rule.skin.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.rule.skin.repository.CisSkinLimitRepository;
import com.bjgoodwill.hip.ds.cis.rule.skin.to.CisSkinLimitEto;
import com.bjgoodwill.hip.ds.cis.rule.skin.to.CisSkinLimitNto;
import com.bjgoodwill.hip.ds.cis.rule.skin.to.CisSkinLimitQto;
import com.bjgoodwill.hip.ds.cis.rule.skin.to.CisSkinReplacementNto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "皮试规则")
@Table(name = "cis_skin_limit", indexes = {@Index(name = "idx_cis_skin_limit_drug_code", columnList = "drug_code")}, uniqueConstraints = {})
public class CisSkinLimit {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("药品编码")
    @Column(name = "drug_code", nullable = false)
    private String drugCode;


    @Comment("药品名称")
    @Column(name = "drug_name", nullable = true)
    private String drugName;


    @Comment("成人限制天数")
    @Column(name = "limit_day", nullable = false)
    private Long limitDay;


    @Comment("儿童限制天数")
    @Column(name = "child_limit_day", nullable = false)
    private Long childLimitDay;


    @Comment("科室编码")
    @Column(name = "org_code", nullable = true)
    private String orgCode;


    @Comment("全院标识")
    @Column(name = "hospital_flag", nullable = true)
    private Boolean hospitalFlag;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    @Version
    @Comment("版本")
    @Column(name = "version", nullable = false)
    private Integer version;


    @Comment("已启用")
    @Column(name = "enabled", nullable = false)
    private boolean enabled;

    @Comment("科室名称")
    @Column(name = "org_name", nullable = true)
    private String orgName;

    public static Optional<CisSkinLimit> getCisSkinLimitById(String id) {
        return dao().findById(id);
    }

    public static List<CisSkinLimit> getCisSkinLimitByDrugCode(List<String> drugCodes) {
        return dao().findByDrugCodeAndHospitalFlagAndEnabled(drugCodes);
    }

    public static List<CisSkinLimit> getCisSkinLimits(CisSkinLimitQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisSkinLimit> getCisSkinLimitPage(CisSkinLimitQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisSkinLimit> getSpecification(CisSkinLimitQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getText())) {
                String drugNamePattern = "%" + qto.getText() + "%";
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("drugName"), drugNamePattern));
            }
            if (StringUtils.isNotBlank(qto.getDrugCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugCode"), qto.getDrugCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgCode"), qto.getOrgCode()));
            }
            if (qto.getHospitalFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalFlag"), qto.getHospitalFlag()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("enabled"), true));
            return predicate;
        };
    }

    private static CisSkinLimitRepository dao() {
        return SpringUtil.getBean(CisSkinLimitRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getDrugCode() {
        return drugCode;
    }

    protected void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    public String getDrugName() {
        return drugName;
    }

    protected void setDrugName(String drugName) {
        this.drugName = drugName;
    }

    public Long getLimitDay() {
        return limitDay;
    }

    protected void setLimitDay(Long limitDay) {
        this.limitDay = limitDay;
    }

    public Long getChildLimitDay() {
        return childLimitDay;
    }

    protected void setChildLimitDay(Long childLimitDay) {
        this.childLimitDay = childLimitDay;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public Integer getVersion() {
        return version;
    }

    protected void setVersion(Integer version) {
        this.version = version;
    }

    public boolean isEnabled() {
        return enabled;
    }

    protected void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Boolean getHospitalFlag() {
        return hospitalFlag;
    }

    public void setHospitalFlag(Boolean hospitalFlag) {
        this.hospitalFlag = hospitalFlag;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Transient
    public List<CisSkinReplacement> getCisSkinReplacements() {
        return CisSkinReplacement.getByCisSkinLimitId(getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisSkinLimit other = (CisSkinLimit) obj;
        return Objects.equals(id, other.id);
    }

    public CisSkinLimit create(CisSkinLimitNto cisSkinLimitNto) {
        Assert.notNull(cisSkinLimitNto, "参数cisSkinLimitNto不能为空！");

        setId(cisSkinLimitNto.getId());
        setDrugCode(cisSkinLimitNto.getDrugCode());
        setDrugName(cisSkinLimitNto.getDrugName());
        setLimitDay(cisSkinLimitNto.getLimitDay());
        setChildLimitDay(cisSkinLimitNto.getChildLimitDay());
        setHospitalFlag(cisSkinLimitNto.getHospitalFlag());
        setOrgCode(cisSkinLimitNto.getOrgCode());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setEnabled(true);
        setOrgName(cisSkinLimitNto.getOrgName());
        dao().save(this);
        if (cisSkinLimitNto.getCisSkinReplacements() != null) {
            for (CisSkinReplacementNto cisSkinReplacementNto_ : cisSkinLimitNto.getCisSkinReplacements()) {
                CisSkinReplacement cisSkinReplacement = new CisSkinReplacement();
                cisSkinReplacement.create(getId(), cisSkinReplacementNto_);
            }
        }
        return this;
    }

    public void update(CisSkinLimitEto cisSkinLimitEto) {
        setDrugCode(cisSkinLimitEto.getDrugCode());
        setDrugName(cisSkinLimitEto.getDrugName());
        setLimitDay(cisSkinLimitEto.getLimitDay());
        setChildLimitDay(cisSkinLimitEto.getChildLimitDay());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setEnabled(cisSkinLimitEto.isEnabled());
    }

    public void enable() {
        setEnabled(true);
    }

    public void disable() {
        setEnabled(false);
    }

    public void delete() {
        for (CisSkinReplacement cisSkinReplacement : CisSkinReplacement.getByCisSkinLimitId(getId())) {
            cisSkinReplacement.delete();
        }
        // CisSkinReplacement.deleteByCisSkinLimitId(getId());
        dao().delete(this);
    }

}
