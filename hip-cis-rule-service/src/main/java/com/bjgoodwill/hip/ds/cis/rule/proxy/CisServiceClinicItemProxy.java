package com.bjgoodwill.hip.ds.cis.rule.proxy;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-12-25 14:49
 * @className: CisServiceClinicItemProxy
 * @description:
 **/
@Component
public class CisServiceClinicItemProxy {

    @Autowired
    private ServiceClinicItemTangibleService serviceClinicItemTangibleService;

    public void verifyServiceClinicItem(List<String> serviceItemCodes) {
        List<String> names = serviceClinicItemTangibleService.queryNoValidServiceClinicItemList(serviceItemCodes)
                .stream().toList();

        BusinessAssert.isEmpty(names, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0009, "服务项目", names);
    }
}