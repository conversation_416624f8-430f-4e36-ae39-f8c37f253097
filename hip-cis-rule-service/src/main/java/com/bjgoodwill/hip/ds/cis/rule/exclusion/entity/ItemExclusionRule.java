package com.bjgoodwill.hip.ds.cis.rule.exclusion.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.HospitalModelEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.LimitTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.RuleTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.repository.ItemExclusionRuleRepository;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleEto;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleNto;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "互斥规则维护")
@Table(name = "cis_item_exclusion_rule", indexes = {}, uniqueConstraints = {})
public class ItemExclusionRule {

    // 标识
    private String id;
    // 判定规则编码，如“ForVisit”、“ForTime”
    private RuleTypeEnum ruleType;
    // 收费项目或医嘱项目编码或费用类别编码
    private String itemCode;
    // 收费项目还是医嘱项目还是费用类别，如PRICE(收费项目)、SERVICE(服务项目（包含药品）)、FEECLASS（费用类别）
    private String itemName;
    // 限制次数	限制能开几次
    private Integer ruleDetailTimes;
    // 限制间隔时间(天)	当rule_code 等于“fortime”时填写
    private Integer ruleDetailDays;
    // HospitalModelEnum
    private HospitalModelEnum hospitalModel;
    // Hint—仅提示（可提交成功）Intercept—提示并拦截（提交不成功）Intervene—干预(提交成功系统自行处理费用)
    private LimitTypeEnum limitType;
    // 已启用
    private boolean enabled;
    // 逻辑删除标记
    private boolean deleted;
    // 创建的人员
    private String createdStaff;
    // 创建的人员姓名
    private String createdStaffName;
    // 创建的时间
    private LocalDateTime createdDate;
    // 最后修改的人员
    private String updatedStaff;
    // 最后修改的人员姓名
    private String updatedStaffName;
    // 最后修改的时间
    private LocalDateTime updatedDate;
    // 版本
    private Integer version;

    public static Optional<ItemExclusionRule> getItemExclusionRuleById(String id) {
        return dao().findById(id);
    }

    public static List<ItemExclusionRule> getItemExclusionRules(ItemExclusionRuleQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<ItemExclusionRule> getItemExclusionRulePage(ItemExclusionRuleQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<ItemExclusionRule> getSpecification(ItemExclusionRuleQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("itemCode"), qto.getItemCode()));
            }
            if (StringUtils.isNotBlank(qto.getItemName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("itemName"), qto.getItemName()));
            }
            if (StringUtils.isNotBlank(qto.getItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalModel"), qto.getHospitalModel()));
            }
            if (qto.getLimitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("limitType"), qto.getLimitType()));
            }
            if (qto.getEnabled() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("enabled"), qto.getEnabled()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            return predicate;
        };
    }

    private static ItemExclusionRuleRepository dao() {
        return SpringUtil.getBean(ItemExclusionRuleRepository.class);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Enumerated(EnumType.STRING)
    @Comment("判定规则编码，如“ForVisit”、“ForTime”")
    @Column(name = "rule_type", nullable = true)
    public RuleTypeEnum getRuleType() {
        return ruleType;
    }

    protected void setRuleType(RuleTypeEnum ruleType) {
        this.ruleType = ruleType;
    }

    @Comment("收费项目或医嘱项目编码或费用类别编码")
    @Column(name = "item_code", nullable = false)
    public String getItemCode() {
        return itemCode;
    }

    protected void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    @Comment("收费项目还是医嘱项目还是费用类别，如PRICE(收费项目)、SERVICE(服务项目（包含药品）)、FEECLASS（费用类别）")
    @Column(name = "item_name", nullable = true)
    public String getItemName() {
        return itemName;
    }

    protected void setItemName(String itemName) {
        this.itemName = itemName;
    }

    @Comment("限制次数	限制能开几次")
    @Column(name = "rule_detail_times", nullable = true)
    public Integer getRuleDetailTimes() {
        return ruleDetailTimes;
    }

    protected void setRuleDetailTimes(Integer ruleDetailTimes) {
        this.ruleDetailTimes = ruleDetailTimes;
    }

    @Comment("限制间隔时间(天)	当rule_code 等于“fortime”时填写")
    @Column(name = "rule_detail_days", nullable = true)
    public Integer getRuleDetailDays() {
        return ruleDetailDays;
    }

    protected void setRuleDetailDays(Integer ruleDetailDays) {
        this.ruleDetailDays = ruleDetailDays;
    }

    @Comment("适用范围")
    @Column(name = "hospital_model", nullable = true)
    public HospitalModelEnum getHospitalModel() {
        return hospitalModel;
    }

    public void setHospitalModel(HospitalModelEnum hospitalModel) {
        this.hospitalModel = hospitalModel;
    }

    @Enumerated(EnumType.STRING)
    @Comment("Hint—仅提示（可提交成功）Intercept—提示并拦截（提交不成功）Intervene—干预(提交成功系统自行处理费用)")
    @Column(name = "limit_type", nullable = true)
    public LimitTypeEnum getLimitType() {
        return limitType;
    }

    protected void setLimitType(LimitTypeEnum limitType) {
        this.limitType = limitType;
    }

    @Comment("已启用")
    @Column(name = "enabled", nullable = false)
    public boolean isEnabled() {
        return enabled;
    }

    protected void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Version
    @Comment("版本")
    @Column(name = "version", nullable = false)
    public Integer getVersion() {
        return version;
    }

    protected void setVersion(Integer version) {
        this.version = version;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ItemExclusionRule other = (ItemExclusionRule) obj;
        return Objects.equals(id, other.id);
    }

    public ItemExclusionRule create(ItemExclusionRuleNto itemExclusionRuleNto) {
        BusinessAssert.notNull(itemExclusionRuleNto, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "参数itemExclusionRuleNto");

        setId(itemExclusionRuleNto.getId());
        setRuleType(itemExclusionRuleNto.getRuleType());
        setItemCode(itemExclusionRuleNto.getItemCode());
        setItemName(itemExclusionRuleNto.getItemName());
        setRuleDetailTimes(itemExclusionRuleNto.getRuleDetailTimes());
        setRuleDetailDays(itemExclusionRuleNto.getRuleDetailDays());
        setHospitalModel(itemExclusionRuleNto.getHospitalModel());
        setLimitType(itemExclusionRuleNto.getLimitType());
        setEnabled(true);
        setDeleted(false);
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        dao().save(this);
        return this;
    }

    public void update(ItemExclusionRuleEto itemExclusionRuleEto) {
        setRuleType(itemExclusionRuleEto.getRuleType());
        setItemCode(itemExclusionRuleEto.getItemCode());
        setItemName(itemExclusionRuleEto.getItemName());
        setRuleDetailTimes(itemExclusionRuleEto.getRuleDetailTimes());
        setRuleDetailDays(itemExclusionRuleEto.getRuleDetailDays());
        setHospitalModel(itemExclusionRuleEto.getHospitalModel());
        setLimitType(itemExclusionRuleEto.getLimitType());
        setEnabled(itemExclusionRuleEto.isEnabled());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        setVersion(itemExclusionRuleEto.getVersion());
    }

    public void enable() {
        setEnabled(true);
    }

    public void disable() {
        setEnabled(false);
    }

    public void delete() {
        setDeleted(true);
    }

}
