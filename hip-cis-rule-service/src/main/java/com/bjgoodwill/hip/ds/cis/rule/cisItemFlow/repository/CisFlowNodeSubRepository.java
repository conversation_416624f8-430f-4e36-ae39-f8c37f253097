package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.repository;

import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.entity.CisFlowNodeSub;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.repository.CisFlowNodeSubRepository")
public interface CisFlowNodeSubRepository extends JpaRepository<CisFlowNodeSub, String>, JpaSpecificationExecutor<CisFlowNodeSub> {

    List<CisFlowNodeSub> findByCisFlowNodeId(String cisFlowNodeId);

    Page<CisFlowNodeSub> findByCisFlowNodeId(String cisFlowNodeId, Pageable pageable);

    boolean existsByCisFlowNodeId(String cisFlowNodeId);

    void deleteByCisFlowNodeId(String cisFlowNodeId);

    List<CisFlowNodeSub> findByIsBilling(Boolean isBilling);

    Page<CisFlowNodeSub> findByIsBilling(Boolean isBilling, Pageable pageable);

    boolean existsByIsBilling(Boolean isBilling);

    void deleteByIsBilling(Boolean isBilling);

}