package com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity.CisDrugLimit;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity.DocLimit;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity.OrgLimit;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.CisDrugLimitTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisDrugLimitAssembler {

    public static List<CisDrugLimitTo> toTos(List<CisDrugLimit> cisDrugLimits) {
        return toTos(cisDrugLimits, false);
    }

    public static List<CisDrugLimitTo> toTos(List<CisDrugLimit> cisDrugLimits, boolean withAllParts) {
        BusinessAssert.notNull(cisDrugLimits, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "参数cisDrugLimits");

        List<CisDrugLimitTo> tos = new ArrayList<>();
        for (CisDrugLimit cisDrugLimit : cisDrugLimits)
            tos.add(toTo(cisDrugLimit, withAllParts));
        return tos;
    }

    public static CisDrugLimitTo toTo(CisDrugLimit cisDrugLimit) {
        return toTo(cisDrugLimit, false);
    }

    /**
     * @generated
     */
    public static CisDrugLimitTo toTo(CisDrugLimit cisDrugLimit, boolean withAllParts) {
        if (cisDrugLimit == null)
            return null;
        if (cisDrugLimit instanceof OrgLimit) {
            return OrgLimitAssembler.toTo((OrgLimit) cisDrugLimit, withAllParts);
        }
        if (cisDrugLimit instanceof DocLimit) {
            return DocLimitAssembler.toTo((DocLimit) cisDrugLimit, withAllParts);
        }
        return null;
    }

}