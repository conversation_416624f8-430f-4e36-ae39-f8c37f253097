package com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.repository.OrgLimitRepository;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "null")
@DiscriminatorValue("01")
public class OrgLimit extends CisDrugLimit {

    // 科室编码
    private String orgCode;
    // 科室名称
    private String orgName;
    // 院区编码
    private String hospitalCode;
    // 院区名称
    private String hospitalName;

    @Comment("科室编码")
    @Column(name = "org_code", nullable = true)
    public String getOrgCode() {
    	return orgCode;
    }

    protected void setOrgCode(String orgCode) {
    	this.orgCode = orgCode;
    }

    @Comment("科室名称")
    @Column(name = "org_name", nullable = true)
    public String getOrgName() {
    	return orgName;
    }

    protected void setOrgName(String orgName) {
    	this.orgName = orgName;
    }

    @Comment("院区编码")
    @Column(name = "hospital_code", nullable = true)
    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    @Comment("院区名称")
    @Column(name = "hospital_name", nullable = true)
    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    @Override
    public CisDrugLimit create(CisDrugLimitNto cisDrugLimitNto) {
        return create((OrgLimitNto)cisDrugLimitNto);
    }

    @Override
    public void update(CisDrugLimitEto cisDrugLimitEto) {
        update((OrgLimitEto)cisDrugLimitEto);
    }

    public OrgLimit create(OrgLimitNto orgLimitNto) {
        BusinessAssert.notNull(orgLimitNto, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001,"参数orgLimitNto");
        super.create(orgLimitNto);

        setOrgCode(orgLimitNto.getOrgCode());
        setOrgName(orgLimitNto.getOrgName());
        setHospitalCode(orgLimitNto.getHospitalCode());
        setHospitalName(orgLimitNto.getHospitalName());
        dao().save(this);
        return this;
    }

    public void update(OrgLimitEto orgLimitEto) {
        super.update(orgLimitEto);
        setOrgCode(orgLimitEto.getOrgCode());
        setOrgName(orgLimitEto.getOrgName());
        setHospitalCode(orgLimitEto.getHospitalCode());
        setHospitalName(orgLimitEto.getHospitalName());
    }

    public void delete() {
        dao().delete(this);
    }

    public static Optional<OrgLimit> getOrgLimitById(String id) {
		return dao().findById(id);
	}

	public static List<OrgLimit> getOrgLimits(OrgLimitQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
	}

	public static Page<OrgLimit> getOrgLimitPage(OrgLimitQto qto) {
		
		return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
	}

    public static List<OrgLimit> getOrgLimitByDrugCodes(List<String> drugCodes){
        return dao().findOrgLimitByDrugCodeInAndDeletedFalse(drugCodes);
    }

	/**
	 * @generated
	 */
    private static Specification<OrgLimit> getSpecification(OrgLimitQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
        	if(StringUtils.isNotBlank(qto.getDrugCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugCode"), qto.getDrugCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getDrugName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugName"), qto.getDrugName()));
        	}
            if(StringUtils.isNotBlank(qto.getOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgCode"), qto.getOrgCode()));
            }
            if(StringUtils.isNotBlank(qto.getOrgName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgName"), qto.getOrgName()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));
            return predicate;
        };
    }

    private static OrgLimitRepository dao() {
		return SpringUtil.getBean(OrgLimitRepository.class);
	}

}
