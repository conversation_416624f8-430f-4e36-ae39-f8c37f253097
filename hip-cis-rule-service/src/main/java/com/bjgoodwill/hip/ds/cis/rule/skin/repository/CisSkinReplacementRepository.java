package com.bjgoodwill.hip.ds.cis.rule.skin.repository;

import com.bjgoodwill.hip.ds.cis.rule.skin.entity.CisSkinReplacement;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.rule.skin.repository.CisSkinReplacementRepository")
public interface CisSkinReplacementRepository extends JpaRepository<CisSkinReplacement, String>, JpaSpecificationExecutor<CisSkinReplacement> {

    List<CisSkinReplacement> findByCisSkinLimitId(String cisSkinLimitId);

    Page<CisSkinReplacement> findByCisSkinLimitId(String cisSkinLimitId, Pageable pageable);

    boolean existsByCisSkinLimitId(String cisSkinLimitId);

    void deleteByCisSkinLimitId(String cisSkinLimitId);

}