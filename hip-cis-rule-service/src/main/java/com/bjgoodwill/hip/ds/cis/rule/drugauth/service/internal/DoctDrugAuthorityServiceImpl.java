package com.bjgoodwill.hip.ds.cis.rule.drugauth.service.internal;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.cis.util.DoctAuthCommonQto;
import com.bjgoodwill.hip.business.util.enums.dict.DictCodeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.ds.base.cis.diagnose.dict.service.DiagnoseOperationService;
import com.bjgoodwill.hip.ds.base.cis.diagnose.dict.to.DiagnoseOperationTo;
import com.bjgoodwill.hip.ds.base.cis.diagnose.extension.service.DiagnoseOperationExtensionService;
import com.bjgoodwill.hip.ds.base.cis.diagnose.extension.to.DiagnoseOperationExtensionTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.limitOrg.to.LimitCreateOrgTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.limitOrg.to.LimitExecOrgTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.OperationApplyTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.ServiceClinicItemTo;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.OrgLimitService;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.OrgLimitQto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.OrgLimitTo;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.entity.DoctDrugAuthority;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.service.DoctDrugAuthorityService;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.service.internal.assembler.DoctDrugAuthorityAssembler;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.to.*;
import com.bjgoodwill.hip.ds.cis.rule.verify.OrderHandleManager;
import com.bjgoodwill.hip.ds.drug.goods.service.DrugGoodsService;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsWestTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RestController("com.bjgoodwill.hip.ds.cis.rule.drugauth.service.DoctDrugAuthorityService")
@RequestMapping(value = "/api/rule/drugauth/doctDrugAuthority", produces = "application/json; charset=utf-8")
public class DoctDrugAuthorityServiceImpl implements DoctDrugAuthorityService {


    @Resource
    private DiagnoseOperationService diagnoseOperationService;

    @Resource
    private DiagnoseOperationExtensionService diagnoseOperationExtensionService;

    @Resource
    private DrugGoodsService drugGoodsService;

    @Resource
    private ServiceClinicItemTangibleService serviceClinicItemTangibleService;

    @Resource
    private OrgLimitService orgLimitService;

    @Resource
    private DictElementService dictElementService;

    public static boolean isNumeric(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("\\d+");
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public DoctDrugAuthorityTo getDoctDrugAuthorityByStaffId(String staffId) {
        DoctDrugAuthorityQto qto = new DoctDrugAuthorityQto();
        qto.setStaffId(staffId);
        List<DoctDrugAuthorityTo> doctDrugAuthorityToList = DoctDrugAuthorityAssembler.toTos(DoctDrugAuthority.getDoctDrugAuthorities(qto));
        return CollectionUtils.isEmpty(doctDrugAuthorityToList) ? null : doctDrugAuthorityToList.get(0);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public void getOperationAuthority(String operationDiagnosisCode, String doctorCode) {
        BusinessAssert.hasText(doctorCode, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "医生编码");
        DoctDrugAuthorityQto qto = new DoctDrugAuthorityQto();
        qto.setStaffId(doctorCode);
        Optional<DoctDrugAuthorityTo> doctDrugAuthorityOp = DoctDrugAuthorityAssembler.toTos(DoctDrugAuthority.getDoctDrugAuthorities(qto)).stream().findFirst();
        if (doctDrugAuthorityOp.isPresent()) {
            BusinessAssert.isTrue(!StringUtils.isEmpty(doctDrugAuthorityOp.get().getCanSurgeryLevel()), CisRuleBusinessErrorEnum.BUS_CIS_RULE_0007, "手术权限不足");
        }
        BusinessAssert.hasText(operationDiagnosisCode, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "手术诊断编码");
        //查询手术等级
        DiagnoseOperationTo diagnoseOperationTo = diagnoseOperationService.getDiagnoseOperationByCode(operationDiagnosisCode);
        if (diagnoseOperationTo != null) {
            DiagnoseOperationExtensionTo extensionTo = diagnoseOperationExtensionService.getDiagnoseOperationExtensionByDiagnoseId(diagnoseOperationTo.getId());
            if (extensionTo != null) {
                Integer operationLevel = isNumeric(extensionTo.getOperationLevel().getId()) ? Integer.valueOf(extensionTo.getOperationLevel().getId()) : 1;
                Integer authorityLevel = StringUtils.isNotEmpty(doctDrugAuthorityOp.get().getCanSurgeryLevel()) ? Integer.valueOf(doctDrugAuthorityOp.get().getCanSurgeryLevel()) : 1;
                BusinessAssert.isTrue(authorityLevel >= operationLevel, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0007, HIPLoginUtil.getLoginUserName(),
                        operationLevel + "手术");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<DoctCommitOrderMsgTo> getDoctDrugAuthorityMsg(List<DoctCommitDrugAuthorityMsgQto> qtoList) {
        if (CollectionUtils.isEmpty(qtoList)) return Collections.emptyList();
        var doctDrugAuthorityQto = new DoctDrugAuthorityQto();
        doctDrugAuthorityQto.setStaffId(HIPLoginUtil.getStaffId());
        List<DoctDrugAuthority> doctDrugAuthorities = DoctDrugAuthority.getDoctDrugAuthorities(doctDrugAuthorityQto);
        if (doctDrugAuthorities.isEmpty()) return Collections.emptyList();

        var doctDrugAuthorityOne = doctDrugAuthorities.get(0);
        return qtoList.stream()
                .map(qto -> {
                    //TODO 自带药不校验
                    List<String> msgs = judgeAuthAndSetMsg(qto, doctDrugAuthorityOne);
                    return CollectionUtils.isEmpty(msgs) ? null : new DoctCommitOrderMsgTo(qto.getId(), msgs);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    private List<String> judgeAuthAndSetMsg(DoctCommitDrugAuthorityMsgQto qto, DoctDrugAuthority doctDrugAuthority) {
        Set<String> msgSet = new HashSet<>();
        if (!doctDrugAuthority.getCanPresc()) msgSet.add("无开立处方权限");
        switch (qto.getOrderClass()) {
            case EDRUG -> {
                //orderServiceCode 逗号分隔，先拆分，再查询
                Stream.of(qto.getOrderServiceCode().split(","))
                        .forEach(code -> getDrugAuthorityMsgSet(code, doctDrugAuthority, msgSet));
            }
            case CDRUG -> {
                if (doctDrugAuthority.getChineseHerbFlag() == null
                        || !doctDrugAuthority.getChineseHerbFlag())
                    msgSet.add("无开立中草药权限");
            }
            default -> {
            }
        }
        return new ArrayList<>(msgSet);
    }

    /**
     * @param code
     * @param doctDrugAuthority
     * @param msgSet
     * @Description 查询药品权限说明
     * <AUTHOR>
     * @date 2024/10/25 15:11
     */
    private void getDrugAuthorityMsgSet(String code, DoctDrugAuthority doctDrugAuthority, Set<String> msgSet) {
        Optional.ofNullable((DrugGoodsWestTo) drugGoodsService.getDrugGoodsById(code))
                .ifPresentOrElse(drugGoodsWestTo -> {
                    checkPermission(msgSet, "无开立中成药权限", () -> !Boolean.TRUE.equals(doctDrugAuthority.getChinesePatentFlag()));
                    checkPermission(msgSet, "无开立抗菌药权限", () -> Boolean.TRUE.equals(drugGoodsWestTo.getAntibacterialFlag()) && !Boolean.TRUE.equals(doctDrugAuthority.getAntiTrainingFlag()));
                    checkPermission(msgSet, "无开立贵重药品权限", () -> Boolean.TRUE.equals(drugGoodsWestTo.getPreciousFlag()) && !Boolean.TRUE.equals(doctDrugAuthority.getCanValuableMedicineFlag()));
                    checkPermission(msgSet, "无开立肿瘤药权限", () -> Boolean.TRUE.equals(drugGoodsWestTo.getAntineoplasticFlag()) && !Boolean.TRUE.equals(doctDrugAuthority.getCanAntineoplasticFlag()));

                    List<String> toxiProperties = Arrays.stream(Optional.ofNullable(drugGoodsWestTo.getToxiProperty()).orElse("").split(","))
                            .filter(s -> !s.trim().isEmpty()).toList();
                    if (toxiProperties.isEmpty()) {
                        msgSet.add("药品信息药理属性编码为空");
                        return;
                    }
                    String authorityNames = Optional.ofNullable(doctDrugAuthority.getToxiPropertyNames()).orElse("");
                    if (authorityNames.trim().isEmpty()) {
                        msgSet.add("当前用户药理属性权限为空");
                        return;
                    }
                    Set<String> authoritySet = Collections.unmodifiableSet(Arrays.stream(authorityNames.split(",")).collect(Collectors.toSet()));
                    // 医生的药理属性权限
                    for (String toxiProperty : toxiProperties) {
                        if (!authoritySet.contains(toxiProperty)) {
                            try {
                                String toxiPropertyName = dictElementService.getCustomDictElement(DictCodeEnum.毒理属性.getCode(), toxiProperty).getElementName();
                                msgSet.add("无开立" + toxiPropertyName + "权限");
                            } catch (IllegalArgumentException e) {
                                msgSet.add("无效的药理属性编码: " + toxiProperty);
                            }
                        }
                    }
                }, () -> {
                    msgSet.add("药品信息为空");
                });
    }

    private void checkPermission(Set<String> msgSet, String message, BooleanSupplier condition) {
        if (condition.getAsBoolean()) {
            msgSet.add(message);
        }
    }

    /**
     * @param
     * @Description 抗菌药权限说明-旧的，已作废
     * <AUTHOR>
     * @date 2024/10/25 15:12
     */
    /*private boolean isAntimicrobials(DrugGoodsWestTo drugGoodsWestTo) {
        if (drugGoodsWestTo != null
                && StringUtils.isNotBlank(drugGoodsWestTo.getActionType())
                && drugGoodsWestTo.getActionType().length() >= 4
                && "0101".equals(drugGoodsWestTo.getActionType().substring(0, 4))) {
            return true;
        }
        return false;
    }*/
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateDoctorDrugAuthority(String staffId, DoctDrugAuthorityEto doctDrugAuthorityEto) {
        Optional<DoctDrugAuthority> doctDrugAuthority = DoctDrugAuthority.getDoctDrugAuthorityByStaffId(staffId);
        doctDrugAuthority.ifPresent(p -> {
            doctDrugAuthority.get().update(doctDrugAuthorityEto);
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteDoctorDrugAuthority(String staffId) {
        Optional<DoctDrugAuthority> doctDrugAuthority = DoctDrugAuthority.getDoctDrugAuthorityByStaffId(staffId);
        doctDrugAuthority.ifPresent(p -> {
            doctDrugAuthority.get().delete();
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void createDoctDrugAuthority(DoctDrugAuthorityNto doctDrugAuthorityNto) {
        String staffId = doctDrugAuthorityNto.getStaffId();
        BusinessAssert.hasText(staffId, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "医生编码");

        DoctDrugAuthorityQto qto = new DoctDrugAuthorityQto();
        qto.setStaffId(staffId);

        var items = DoctDrugAuthority.getDoctDrugAuthorities(qto);
        BusinessAssert.noNullElements(items, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0002, "医生权限");

        new DoctDrugAuthority().create(doctDrugAuthorityNto);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<DoctDrugAuthorityTo> queryDoctDrugAuthorityList(List<String> staffCodes) {
        BusinessAssert.notEmpty(staffCodes, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "医生编码");

        return DoctDrugAuthorityAssembler.toTos(DoctDrugAuthority.findByStaffIds(staffCodes));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<DoctCommitOrderMsgTo> getServiceItemStatusMsg(List<DoctCommitValidityMsgQto> qtoList) {
        if (CollectionUtils.isEmpty(qtoList)) return Collections.emptyList();

        return qtoList.stream()
                .map(qto -> {
                    Set<String> msgSet = new HashSet<>();
                    String[] orderServiceCodes = qto.getOrderServiceCode().split(",");

                    if (SystemTypeEnum.CDRUG.equals(qto.getOrderClass()) || SystemTypeEnum.EDRUG.equals(qto.getOrderClass())) {
                        Arrays.stream(orderServiceCodes)
                                .filter(code -> drugGoodsService.getDrugGoodsById(code) == null)
                                .forEach(code -> msgSet.add("项目无效"));
                    } else {
//                        Arrays.stream(orderServiceCodes)
//                                .map(code -> serviceClinicItemTangibleService.getServiceClinicItemByCode(code))
//                                .filter(Objects::nonNull)
//                                .filter(serviceClinicItem -> !serviceClinicItem.isEnabled())
//                                .forEach(serviceClinicItem -> msgSet.add("项目无效"));
                        //获取参数中无效的医嘱项目
                        serviceClinicItemTangibleService.queryNoValidServiceClinicItemList(Arrays.asList(orderServiceCodes))
                                .stream().forEach(serviceClinicItem -> msgSet.add("项目无效"));

                    }

                    return CollectionUtils.isEmpty(msgSet) ? null : new DoctCommitOrderMsgTo(qto.getId(), new ArrayList<>(msgSet));
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<DoctCommitOrderMsgTo> getCisIpdDiagnoseMsg(DoctCommitDiagnoseMsgQto diagnoseMsgQto) {
        if (diagnoseMsgQto == null
                || CollectionUtils.isEmpty(diagnoseMsgQto.getDiagnoseMsgList())
                || CollectionUtils.isEmpty(diagnoseMsgQto.getIpdDiagnoseQtoList())) return Collections.emptyList();
        //需要查询诊断的提交医嘱列表
        List<DoctCommitDiagnoseMsgQto.DiagnoseMsgQto> qtoList = diagnoseMsgQto.getDiagnoseMsgList();
        //当前患者住院诊断列表
        List<DoctCommitDiagnoseMsgQto.IpdDiagnoseQto> ipdDiagnoseQtoList = diagnoseMsgQto.getIpdDiagnoseQtoList();
        List<DoctCommitOrderMsgTo> msgTos = new ArrayList<>();
        Map<String, List<DoctCommitDiagnoseMsgQto.IpdDiagnoseQto>> diagnosesMap = new HashMap<>();
        List<DoctCommitDiagnoseMsgQto.IpdDiagnoseQto> diagnoseToList = diagnosesMap.computeIfAbsent(qtoList.get(0).getVisitCode(), key -> ipdDiagnoseQtoList);
        for (DoctCommitDiagnoseMsgQto.DiagnoseMsgQto qto : qtoList) {
            switch (qto.getOrderClass()) {
                case EDRUG -> {
                    if (CollectionUtils.isEmpty(diagnoseToList))
                        msgTos.add(new DoctCommitOrderMsgTo(qto.getId(), Collections.singletonList("无诊断")));
                }
                case CDRUG -> {
                    if (CollectionUtils.isEmpty(diagnoseToList)
                            || diagnoseToList.stream()
                            .noneMatch(cisIpdDiagnoseTo -> DiagnosisEnum.TCM.equals(cisIpdDiagnoseTo.getDiagnosisClass())))
                        msgTos.add(new DoctCommitOrderMsgTo(qto.getId(), Collections.singletonList("无中医诊断")));

                }
                case OUTHOSPITAL -> {
                    if (CollectionUtils.isEmpty(diagnoseToList)
                            || diagnoseToList.stream().noneMatch(cisIpdDiagnoseTo -> DiagnosisTypeEnum.C.equals(cisIpdDiagnoseTo.getDiagnosisType())
                            && cisIpdDiagnoseTo.getChiefFlag() != null && cisIpdDiagnoseTo.getChiefFlag()))
                        msgTos.add(new DoctCommitOrderMsgTo(qto.getId(), Collections.singletonList("无出院诊断为主诊断")));

                }
                default -> {
                }
            }
        }
        return msgTos;
    }

    /*@Override
    public List<DoctCommitOrderMsgTo> getCisIpdRepeatMsg(DoctCommitRepeatMsgQto qto) {
        if (qto == null
                || CollectionUtils.isEmpty(qto.getRepeatMsgList())
                || CollectionUtils.isEmpty(qto.getValidOrderList())) return Collections.emptyList();
        //需要查询重复的提交医嘱列表
        List<DoctCommitRepeatMsgQto.RepeatMsgQto> repeatMsgList = qto.getRepeatMsgList();
        //全部有效的医嘱列表
        List<DoctCommitRepeatMsgQto.ValidOrderQto> orders = qto.getValidOrderList();
        List<DoctCommitOrderMsgTo> msgTos = new ArrayList<>();
        repeatMsgList.forEach(repeatMsgQto -> {
            String message = getRepeatMessage(orders, repeatMsgQto);
            if (StringUtils.isNotBlank(message))
                msgTos.add(new DoctCommitOrderMsgTo(repeatMsgQto.getId(), Collections.singletonList(message)));

        });
        return msgTos;
    }*/

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<DoctCommitOrderMsgTo> getOrgLimitMsg(List<DoctCommitOrgLimitMsgQto> qtoList) {
        if (CollectionUtils.isEmpty(qtoList)) return Collections.emptyList();
        return qtoList.stream()
                .map(msgQto -> {
                    Set<String> msgSet = Stream.of(msgQto.getOrderServiceCode().split(","))
                            .map(code -> getOrgLimitMessages(code, msgQto))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toCollection(HashSet::new));
                    return CollectionUtils.isEmpty(msgSet) ? null : new DoctCommitOrderMsgTo(msgQto.getId(), new ArrayList<>(msgSet));
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * @param orderServiceCode
     * @param msgQto
     * @return String
     * @Description 获取科室限制说明
     * <AUTHOR>
     * @date 2024/10/25 15:24
     */
    private String getOrgLimitMessages(String orderServiceCode, DoctCommitOrgLimitMsgQto msgQto) {
        switch (msgQto.getOrderClass()) {
            case CDRUG, EDRUG -> {
                //TODO 自带药不校验
                OrgLimitQto orgLimitQto = new OrgLimitQto();
                orgLimitQto.setOrgCode(msgQto.getCreateOrgCode());
                orgLimitQto.setDrugCode(msgQto.getOrderServiceCode());
                List<OrgLimitTo> orgLimits = orgLimitService.getOrgLimits(orgLimitQto);
                if (!CollectionUtils.isEmpty(orgLimits) &&
                        orgLimits.stream().noneMatch(orgLimitTo -> orgLimitTo.getOrgCode().equals(msgQto.getCreateOrgCode())))
                    return "无法在当前科室开立";

            }
            case SKIN, MANAGEMENT, MATERIAL, TREATMENT, ENTRUST, INPUTBLOODAPPLY, BLOOD -> {
                return getServiceItemCreateOrgLimitMsg(orderServiceCode, msgQto);
            }
            case SPCOBS, DGIMG, PALG -> {
                //开立科室限制
                String createOrgLimitMsg = getServiceItemCreateOrgLimitMsg(orderServiceCode, msgQto);
                if (StringUtils.isNotBlank(createOrgLimitMsg)) return createOrgLimitMsg;
                //执行科室限制
                return getServiceItemExecOrgLimitMsg(orderServiceCode, msgQto);
            }
            default -> {
            }
        }
        return null;
    }

    /**
     * @param serviceItemCode
     * @param msgQto
     * @return String
     * @Description 获取服务项目开立科室限制说明
     * <AUTHOR>
     * @date 2024/10/25 15:24
     */
    private String getServiceItemCreateOrgLimitMsg(String serviceItemCode, DoctCommitOrgLimitMsgQto msgQto) {
        ServiceClinicItemTo item = serviceClinicItemTangibleService.getServiceClinicItemByCode(serviceItemCode);
        if (item == null || !item.isEnabled()) {
            return "项目无效";
        }
        List<LimitCreateOrgTo> limitCreateOrgToList = item.getLimitCreateOrgToList();
        if (!CollectionUtils.isEmpty(limitCreateOrgToList)
                && limitCreateOrgToList.stream().noneMatch(limitCreateOrgTo -> limitCreateOrgTo.getOrgCode().equals(msgQto.getCreateOrgCode())))
            return "无法在当前科室开立";
        return null;
    }

    /**
     * @param serviceItemCode
     * @param msgQto
     * @return String
     * @Description 获取服务项目执行科室限制说明
     * <AUTHOR>
     * @date 2024/10/25 15:25
     */
    private String getServiceItemExecOrgLimitMsg(String serviceItemCode, DoctCommitOrgLimitMsgQto msgQto) {
        ServiceClinicItemTo item = serviceClinicItemTangibleService.getServiceClinicItemByCode(serviceItemCode);
        if (item == null || !item.isEnabled()) {
            return "项目无效";
        }
        List<LimitExecOrgTo> limitExecOrgToList = item.getLimitExecOrgToList();
        if (!CollectionUtils.isEmpty(limitExecOrgToList)
                && limitExecOrgToList.stream().noneMatch(limitExecOrgTo -> limitExecOrgTo.getOrgCode().equals(msgQto.getExecuteOrgCode()))) {
            return "无法在当前执行科室执行";
        }
        return null;
    }


    /**
     * 该方法用于验证医生的药品授权等
     * 它覆盖了父类的方法，并且是一个事务性方法，确保在出现异常时回滚事务
     *
     * @param qtoList 一个包含待验证的医生授权信息的列表
     * @return 返回一个经过验证处理后的医生授权信息列表
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<DoctAuthCommonQto> verifyDoctDrugAuthority(List<DoctAuthCommonQto> qtoList) {
        // 获取OrderHandleManager实例，用于处理订单相关的逻辑
        OrderHandleManager orderHandleManager = SpringUtil.getBean(OrderHandleManager.class);
        // 调用OrderHandleManager的execute方法，处理并返回验证后的医生授权信息列表
        return orderHandleManager.execute(qtoList);
    }

    /**
     * 验证医生的抗菌药物培训级别
     * 此方法用于检查医生是否已经完成特定级别的抗菌药物培训
     * 它首先根据员工ID获取医生的药物权限信息，然后检查医生是否已经完成了抗菌药物培训
     * 如果医生已经完成培训，方法将进一步验证培训级别是否包含在医生的培训水平中
     *
     * @param staffId           医生的员工ID，用于获取医生的药物权限信息
     * @param antiTrainingLevel 需要验证的抗菌药物培训级别
     * @return 如果医生的培训水平中包含指定的培训级别，则返回true；否则返回false
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public Boolean verifyAntiTraining(String staffId, String antiTrainingLevel) {
        // 根据员工ID获取医生的药物权限信息
        DoctDrugAuthorityTo doctDrugAuthorityTo = getDoctDrugAuthorityByStaffId(staffId);
        // 确保医生的药物权限信息不为空
        BusinessAssert.notNull(doctDrugAuthorityTo, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "医生权限");

        // 检查医生是否已经完成了抗菌药物培训
        if (!doctDrugAuthorityTo.getAntiTrainingFlag()) {
            return false;
        }

        // 获取医生的抗菌药物培训级别
        String level = doctDrugAuthorityTo.getAntiTrainingLevel();

        // 确保培训级别不为空
        BusinessAssert.hasText(level, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0008, HIPLoginUtil.getLoginUserName(), "抗菌药");

        // 将医生的培训级别分割成列表
        List<String> levels = Arrays.asList(level.split(","));

        // 验证医生的培训级别是否包含所需的培训级别
        return levels.contains(antiTrainingLevel);
    }

    /**
     * 重写验证多种药物使用级别的方法
     * 该方法主要用于验证给定药物是否符合当前医生的用药权限
     * 使用事务管理，任何异常均回滚，且只读
     *
     * @param staffId   医生标识
     * @param verifyQto 包含待验证药物信息的对象
     * @return 返回验证结果对象
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisVerifyTo verifyMulDrugLevel(String staffId, CisVerifyQto verifyQto) {
        // 获取医生的药物使用权限信息
        DoctDrugAuthorityTo doctDrugAuthorityTo = getDoctDrugAuthorityTo(staffId);
        // 初始化用于存储验证结果的映射
        Map<String, String> map = new HashMap<>();

        // 如果有待验证的药物商品信息，则进行抗菌药物和抗肿瘤药物的验证
        if (!CollectionUtils.isEmpty(verifyQto.getDrugGoodsTos())) {
            map.putAll(verifyAntibacterial(doctDrugAuthorityTo, verifyQto.getDrugGoodsTos()));
            map.putAll(verifyAntineoplastic(doctDrugAuthorityTo, verifyQto.getDrugGoodsTos()));
        }

        // 如果有待验证的临床服务项目信息，则进行手术级别的验证
        if (!CollectionUtils.isEmpty(verifyQto.getServiceClinicItemTos())) {
            map.putAll(verifyOperation(doctDrugAuthorityTo, verifyQto.getServiceClinicItemTos()));
        }

        // 根据验证结果构建并返回验证结果对象
        return new CisVerifyTo(map);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<DoctDrugAuthorityTo> getDoctDrugAuthorityByDeptCode(String deptCde) {
        DoctDrugAuthorityQto qto = new DoctDrugAuthorityQto();
        qto.setAdministrativeDept(deptCde);
        return DoctDrugAuthorityAssembler.toTos(DoctDrugAuthority.getDoctDrugAuthorities(qto));
    }


    /**
     * 校验抗菌药物的使用权限
     * 此方法旨在根据医生的抗菌药物培训情况和药物的抗菌等级，筛选出医生有权使用的抗菌药物
     * 如果医生未完成抗菌药物培训或未维护抗菌药等级，或者药物的抗菌等级不在医生的培训范围内，将返回相应的提示信息
     *
     * @param doctDrugAuthorityTo 包含医生抗菌药物培训信息的对象
     * @param drugGoodsTos        药物列表，用于筛选抗菌药物
     * @return 返回一个映射，键为药物代码，值为医生是否可以使用该药物的提示信息
     */
    private Map<String, String> verifyAntibacterial(DoctDrugAuthorityTo doctDrugAuthorityTo, List<DrugGoodsWestTo> drugGoodsTos) {
        // 过滤出所有标记为抗菌药物的药物
        List<DrugGoodsWestTo> antibacterials = drugGoodsTos.stream()
                .filter(p -> p.getAntibacterialFlag()).toList();
        // 如果没有抗菌药物，则直接返回空映射
        if (CollectionUtils.isEmpty(antibacterials)) {
            return new HashMap<>();
        }

        // 如果医生未完成抗菌药物培训，则所有抗菌药物都返回“未完成抗菌药物培训”的提示
        if (Boolean.FALSE.equals(doctDrugAuthorityTo.getAntiTrainingFlag())) {
            return antibacterials.stream()
                    .collect(Collectors.toMap(DrugGoodsWestTo::getDrugGoodsCode, p -> "未完成抗菌药物培训"));
        }

        // 获取医生的抗菌药物培训级别
        String level = StringUtils.isBlank(doctDrugAuthorityTo.getAntiTrainingLevel()) ?
                StringUtils.EMPTY : doctDrugAuthorityTo.getAntiTrainingLevel();

        // 将医生的培训级别分割成列表
        List<String> levels = Arrays.asList(level.split(","));
        // 过滤出医生培训级别不包含的抗菌药物，并返回相应的提示信息
        return antibacterials.stream().filter(p -> !levels.contains(p.getAntineoplasticLevel()))
                .collect(Collectors.toMap(DrugGoodsWestTo::getDrugGoodsCode, p -> "医师没有该药品等级"));
    }

    /**
     * 校验抗癌药物的使用权限
     * 此方法用于检查医生是否有权限开具特定的抗癌药物，基于医生的培训和授权级别
     *
     * @param doctDrugAuthorityTo 包含医生药物使用权限信息的对象
     * @param drugGoodsTos        西药商品列表，用于检查抗癌药物
     * @return 返回一个映射，键为药物代码，值为未通过校验的原因如果列表为空或医生有权开具所有药物，则返回空映射
     */
    private Map<String, String> verifyAntineoplastic(DoctDrugAuthorityTo doctDrugAuthorityTo, List<DrugGoodsWestTo> drugGoodsTos) {
        // 过滤出所有抗癌药物
        List<DrugGoodsWestTo> antineoplastic = drugGoodsTos.stream()
                .filter(p -> p.getAntineoplasticFlag()).toList();
        // 如果没有抗癌药物，则返回空映射
        if (CollectionUtils.isEmpty(antineoplastic)) {
            return new HashMap<>();
        }
        // 如果医生没有权限开具抗癌药物，则返回所有抗癌药物及其原因
        if (Boolean.FALSE.equals(doctDrugAuthorityTo.getCanAntineoplasticFlag())) {
            return antineoplastic.stream()
                    .collect(Collectors.toMap(DrugGoodsWestTo::getDrugGoodsCode, p -> "未完成抗菌药物培训"));
        }
        // 获取医生的抗癌药物使用级别
        String level = StringUtils.isBlank(doctDrugAuthorityTo.getAntineoplasticLevel()) ?
                StringUtils.EMPTY : doctDrugAuthorityTo.getAntineoplasticLevel();

        // 将医生的培训级别分割成列表
        List<String> levels = Arrays.asList(level.split(","));
        // 过滤出医生没有权限开具的抗癌药物，并返回相应的映射
        return antineoplastic.stream().filter(p -> !levels.contains(p.getAntineoplasticLevel()))
                .collect(Collectors.toMap(DrugGoodsWestTo::getDrugGoodsCode, p -> "医师没有该药品等级"));

    }

    /**
     * 验证医生是否有权限进行特定的手术操作
     * 此方法通过比较医生的手术权限级别和手术操作所需的级别来确定医生是否可以进行手术
     *
     * @param doctDrugAuthorityTo  包含医生抗菌药物使用权限信息的对象
     * @param serviceClinicItemTos 一组包含各种医疗服务项目的对象，包括手术操作
     * @return 返回一个映射，键为服务项目代码，值为医生是否可以进行该项目的提示信息
     */
    private Map<String, String> verifyOperation(DoctDrugAuthorityTo doctDrugAuthorityTo, List<ServiceClinicItemTo> serviceClinicItemTos) {
        // 从服务诊所项目列表中过滤出所有手术操作申请
        List<OperationApplyTo> operationApply = serviceClinicItemTos.stream()
                .filter(OperationApplyTo.class::isInstance)
                .map(OperationApplyTo.class::cast)
                .toList();

        // 获取医生的抗菌药物培训级别
        String level = StringUtils.isBlank(doctDrugAuthorityTo.getCanSurgeryLevel())
                ? StringUtils.EMPTY : doctDrugAuthorityTo.getCanSurgeryLevel();

        // 将医生的培训级别分割成列表
        List<String> levels = Arrays.asList(level.split(","));
        // 过滤出医生培训级别不包含的抗菌药物，并返回相应的提示信息
        return operationApply.stream()
                .filter(p -> !levels.contains(p.getCisOperationLevel().getCode()))
                .collect(Collectors.toMap(OperationApplyTo::getServiceItemCode, p -> "医师没有该药品等级"));

    }

    private DoctDrugAuthorityTo getDoctDrugAuthorityTo(String staffId) {
        // 根据员工ID获取医生的药物权限信息
        DoctDrugAuthorityTo doctDrugAuthorityTo = getDoctDrugAuthorityByStaffId(staffId);
        // 确保医生的药物权限信息不为空
        BusinessAssert.notNull(doctDrugAuthorityTo, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "医生权限");
        return doctDrugAuthorityTo;
    }

}
