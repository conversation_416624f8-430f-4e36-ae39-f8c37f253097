package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.entity.CisConfOrderLimitRule;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to.CisConfOrderLimitRuleTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisConfOrderLimitRuleAssembler {

    public static List<CisConfOrderLimitRuleTo> toTos(List<CisConfOrderLimitRule> cisConfOrderLimitRules) {
        return toTos(cisConfOrderLimitRules, false);
    }

    public static List<CisConfOrderLimitRuleTo> toTos(List<CisConfOrderLimitRule> cisConfOrderLimitRules, boolean withAllParts) {
        BusinessAssert.notNull(cisConfOrderLimitRules, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "参数cisConfOrderLimitRules");

        List<CisConfOrderLimitRuleTo> tos = new ArrayList<>();
        for (CisConfOrderLimitRule cisConfOrderLimitRule : cisConfOrderLimitRules)
            tos.add(toTo(cisConfOrderLimitRule, withAllParts));
        return tos;
    }

    public static CisConfOrderLimitRuleTo toTo(CisConfOrderLimitRule cisConfOrderLimitRule) {
        return toTo(cisConfOrderLimitRule, false);
    }

    /**
     * @generated
     */
    public static CisConfOrderLimitRuleTo toTo(CisConfOrderLimitRule cisConfOrderLimitRule, boolean withAllParts) {
        if (cisConfOrderLimitRule == null)
            return null;
        CisConfOrderLimitRuleTo to = new CisConfOrderLimitRuleTo();
        to.setId(cisConfOrderLimitRule.getId());
        to.setCisConfOrderLimitId(cisConfOrderLimitRule.getCisConfOrderLimitId());
        to.setRuleName(cisConfOrderLimitRule.getRuleName());
        to.setRuleType(cisConfOrderLimitRule.getRuleType());
        to.setRuleRegular(cisConfOrderLimitRule.getRuleRegular());
        to.setVersion(cisConfOrderLimitRule.getVersion());
        to.setEnabled(cisConfOrderLimitRule.isEnabled());
        to.setCreatedStaff(cisConfOrderLimitRule.getCreatedStaff());
        to.setCreatedStaffName(cisConfOrderLimitRule.getCreatedStaffName());
        to.setCreatedDate(cisConfOrderLimitRule.getCreatedDate());
        to.setUpdatedStaff(cisConfOrderLimitRule.getUpdatedStaff());
        to.setUpdatedStaffName(cisConfOrderLimitRule.getUpdatedStaffName());
        to.setUpdatedDate(cisConfOrderLimitRule.getUpdatedDate());

        if (withAllParts) {
        }
        return to;
    }

}