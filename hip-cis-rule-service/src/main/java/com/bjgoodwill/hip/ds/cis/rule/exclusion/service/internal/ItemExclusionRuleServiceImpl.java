package com.bjgoodwill.hip.ds.cis.rule.exclusion.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.entity.ItemExclusionRule;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.service.ItemExclusionRuleService;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.service.internal.assembler.ItemExclusionRuleAssembler;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleEto;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleNto;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleQto;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.rule.exclusion.service.ItemExclusionRuleService")
@RequestMapping(value = "/api/rule/exclusion/itemExclusionRule", produces = "application/json; charset=utf-8")
public class ItemExclusionRuleServiceImpl implements ItemExclusionRuleService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<ItemExclusionRuleTo> getItemExclusionRules(ItemExclusionRuleQto itemExclusionRuleQto) {
        return ItemExclusionRuleAssembler.toTos(ItemExclusionRule.getItemExclusionRules(itemExclusionRuleQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<ItemExclusionRuleTo> getItemExclusionRulePage(ItemExclusionRuleQto itemExclusionRuleQto) {
        Page<ItemExclusionRule> page = ItemExclusionRule.getItemExclusionRulePage(itemExclusionRuleQto);
        Page<ItemExclusionRuleTo> result = page.map(ItemExclusionRuleAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public ItemExclusionRuleTo getItemExclusionRuleById(String id) {
        return ItemExclusionRuleAssembler.toTo(ItemExclusionRule.getItemExclusionRuleById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public ItemExclusionRuleTo createItemExclusionRule(ItemExclusionRuleNto itemExclusionRuleNto) {
        ItemExclusionRule itemExclusionRule = new ItemExclusionRule();
        itemExclusionRule = itemExclusionRule.create(itemExclusionRuleNto);
        return ItemExclusionRuleAssembler.toTo(itemExclusionRule);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateItemExclusionRule(String id, ItemExclusionRuleEto itemExclusionRuleEto) {
        Optional<ItemExclusionRule> itemExclusionRuleOptional = ItemExclusionRule.getItemExclusionRuleById(id);
        itemExclusionRuleOptional.ifPresent(itemExclusionRule -> itemExclusionRule.update(itemExclusionRuleEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void enableItemExclusionRule(String id) {
        Optional<ItemExclusionRule> itemExclusionRuleOptional = ItemExclusionRule.getItemExclusionRuleById(id);
        itemExclusionRuleOptional.ifPresent(itemExclusionRule -> itemExclusionRule.enable());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void disableItemExclusionRule(String id) {
        Optional<ItemExclusionRule> itemExclusionRuleOptional = ItemExclusionRule.getItemExclusionRuleById(id);
        itemExclusionRuleOptional.ifPresent(itemExclusionRule -> itemExclusionRule.disable());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteItemExclusionRule(String id) {
        Optional<ItemExclusionRule> itemExclusionRuleOptional = ItemExclusionRule.getItemExclusionRuleById(id);
        itemExclusionRuleOptional.ifPresent(itemExclusionRule -> itemExclusionRule.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}