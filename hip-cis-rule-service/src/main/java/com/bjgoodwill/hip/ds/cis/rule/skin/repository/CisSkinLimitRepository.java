package com.bjgoodwill.hip.ds.cis.rule.skin.repository;

import com.bjgoodwill.hip.ds.cis.rule.skin.entity.CisSkinLimit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.rule.skin.repository.CisSkinLimitRepository")
public interface CisSkinLimitRepository extends JpaRepository<CisSkinLimit, String>, JpaSpecificationExecutor<CisSkinLimit> {

    boolean existsByDrugCode(String drugCode);

    boolean existsByDrugCodeAndIdNot(String drugCode, String id);

    @Query(value = "select a from CisSkinLimit a where a.hospitalFlag = true and a.enabled = true and a.drugCode in (?1) ")
    List<CisSkinLimit> findByDrugCodeAndHospitalFlagAndEnabled(List<String> drugCodes);
}