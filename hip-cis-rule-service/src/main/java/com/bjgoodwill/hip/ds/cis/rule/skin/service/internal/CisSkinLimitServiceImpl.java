package com.bjgoodwill.hip.ds.cis.rule.skin.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.service.CisAntimicrobialsSkinExecReportService;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.skin.entity.CisSkinLimit;
import com.bjgoodwill.hip.ds.cis.rule.skin.entity.CisSkinReplacement;
import com.bjgoodwill.hip.ds.cis.rule.skin.service.CisSkinLimitService;
import com.bjgoodwill.hip.ds.cis.rule.skin.service.internal.assembler.CisSkinLimitAssembler;
import com.bjgoodwill.hip.ds.cis.rule.skin.service.internal.assembler.CisSkinReplacementAssembler;
import com.bjgoodwill.hip.ds.cis.rule.skin.to.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.rule.skin.service.CisSkinLimitService")
@RequestMapping(value = "api/rule/skin/cisSkinLimit", produces = "application/json; charset=utf-8")
public class CisSkinLimitServiceImpl implements CisSkinLimitService {

    @Autowired
    private CisAntimicrobialsSkinExecReportService cisAntimicrobialsSkinExecReportService;

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisSkinLimitTo> getCisSkinLimits(CisSkinLimitQto cisSkinLimitQto) {
        return CisSkinLimitAssembler.toTos(CisSkinLimit.getCisSkinLimits(cisSkinLimitQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisSkinLimitTo> getCisSkinLimitPage(CisSkinLimitQto cisSkinLimitQto) {
        Page<CisSkinLimit> page = CisSkinLimit.getCisSkinLimitPage(cisSkinLimitQto);
        Page<CisSkinLimitTo> result = page.map(CisSkinLimitAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisSkinLimitTo getCisSkinLimitById(String id) {
        return CisSkinLimitAssembler.toTo(CisSkinLimit.getCisSkinLimitById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisSkinLimitTo createCisSkinLimit(CisSkinLimitNto cisSkinLimitNto) {
        CisSkinLimitQto qto = new CisSkinLimitQto();
        qto.setDrugCode(cisSkinLimitNto.getDrugCode());
        qto.setOrgCode(cisSkinLimitNto.getOrgCode());
        qto.setHospitalFlag(cisSkinLimitNto.getHospitalFlag());
        List<CisSkinLimit> check = CisSkinLimit.getCisSkinLimits(qto);
        BusinessAssert.isEmpty(check, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0007, "已经维护该药品的皮试规则，请重新选择。");
        CisSkinLimit cisSkinLimit = new CisSkinLimit();
        cisSkinLimit = cisSkinLimit.create(cisSkinLimitNto);
        return CisSkinLimitAssembler.toTo(cisSkinLimit);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void batchCreateCisSkinLimit(List<CisSkinLimitNto> cisSkinLimitNtos) {
        BusinessAssert.notEmpty(cisSkinLimitNtos, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "cisSkinLimitNtos");
        cisSkinLimitNtos.forEach(a -> new CisSkinLimit().create(a));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisSkinLimit(String id, CisSkinLimitEto cisSkinLimitEto) {
        Optional<CisSkinLimit> cisSkinLimitOptional = CisSkinLimit.getCisSkinLimitById(id);
        cisSkinLimitOptional.ifPresent(cisSkinLimit -> cisSkinLimit.update(cisSkinLimitEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisSkinLimit(String id) {
        Optional<CisSkinLimit> cisSkinLimitOptional = CisSkinLimit.getCisSkinLimitById(id);
        cisSkinLimitOptional.ifPresent(cisSkinLimit -> cisSkinLimit.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisSkinReplacementTo> getCisSkinReplacements(String cisSkinLimitId, CisSkinReplacementQto cisSkinReplacementQto) {
        return CisSkinReplacementAssembler.toTos(CisSkinReplacement.getCisSkinReplacements(cisSkinLimitId, cisSkinReplacementQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisSkinReplacementTo> getCisSkinReplacementPage(String cisSkinLimitId, CisSkinReplacementQto cisSkinReplacementQto) {
        Page<CisSkinReplacement> page = CisSkinReplacement.getCisSkinReplacementPage(cisSkinLimitId, cisSkinReplacementQto);
        Page<CisSkinReplacementTo> result = page.map(CisSkinReplacementAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisSkinReplacementTo createCisSkinReplacement(String cisSkinLimitId, CisSkinReplacementNto cisSkinReplacementNto) {
        CisSkinReplacement cisSkinReplacement = new CisSkinReplacement();
        cisSkinReplacement = cisSkinReplacement.create(cisSkinLimitId, cisSkinReplacementNto);
        return CisSkinReplacementAssembler.toTo(cisSkinReplacement);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void createCisSkinReplacementBatch(String cisSkinLimitId, List<CisSkinReplacementNto> cisSkinReplacementNtos) {
        BusinessAssert.notEmpty(cisSkinReplacementNtos, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "cisSkinReplacementNtos");
        cisSkinReplacementNtos.forEach(a -> createCisSkinReplacement(cisSkinLimitId, a));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisSkinReplacement(String id, CisSkinReplacementEto cisSkinReplacementEto) {
        Optional<CisSkinReplacement> cisSkinReplacementOptional = CisSkinReplacement.getCisSkinReplacementById(id);
        cisSkinReplacementOptional.ifPresent(cisSkinReplacement -> cisSkinReplacement.update(cisSkinReplacementEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisSkinReplacement(String id) {
        Optional<CisSkinReplacement> cisSkinReplacementOptional = CisSkinReplacement.getCisSkinReplacementById(id);
        cisSkinReplacementOptional.ifPresent(cisSkinReplacement -> cisSkinReplacement.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisSkinReplacementByCisSkinLimitId(String cisSkinLimitId) {
        BusinessAssert.hasText(cisSkinLimitId, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "cisSkinLimitId");
        CisSkinReplacement.deleteByCisSkinLimitId(cisSkinLimitId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisSkinLimitByDrugCode(List<String> drugCodes) {
        List<CisSkinLimit> cisSkinLimits = CisSkinLimit.getCisSkinLimitByDrugCode(drugCodes);
        cisSkinLimits.forEach(cisSkinLimit -> cisSkinLimit.delete());
    }

    /**
     * 重写获取皮肤限制替代代码的方法
     * 该方法用于根据药物代码、就诊代码和是否为儿童判断是否可以开具特定药物
     *
     * @param drugCode  药物代码
     * @param visitCode 就诊代码
     * @param childFlag 是否为儿童的标志
     * @return 如果不能开具药物，则返回空字符串，否则返回替代代码
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisSkinReplacementResult getCisSkinLimitReplacementCode(String drugCode, String visitCode, Boolean childFlag, String orgCode) {
        // 确保就诊代码有文本，如果没有，则抛出异常
        BusinessAssert.hasText(visitCode, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "患者流水号");
        BusinessAssert.hasText(orgCode, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "orgCode");

        // 创建查询传输对象并设置参数
        CisSkinLimitQto qto = new CisSkinLimitQto();
        qto.setDrugCode(drugCode);
        qto.setEnabled(true);
        // 根据查询条件获取皮肤限制对象
        CisSkinLimit cisSkinLimit = CisSkinLimit.getCisSkinLimits(qto).stream()
                .filter(v -> StringUtils.isEmpty(v.getOrgCode()) || orgCode.equals(v.getOrgCode()))
                .sorted(Comparator.comparing((CisSkinLimit a) -> !orgCode.equals(a.getOrgCode()))
                        .thenComparing(c -> !c.getHospitalFlag()))
                .findFirst().orElse(null);

        // 如果没有找到皮肤限制对象，则返回空字符串
        if (cisSkinLimit == null) {
            return new CisSkinReplacementResult(true);
        }

        // 检查 childFlag 是否为 null
        childFlag = childFlag == null ? false : childFlag;

        // 根据是否为儿童，获取对应的限制天数
        long limitDays = childFlag ? cisSkinLimit.getChildLimitDay() : cisSkinLimit.getLimitDay();

        // 获取所有替代代码
        List<CisSkinReplacement> skinReplacements = CisSkinReplacement.getByCisSkinLimitId(cisSkinLimit.getId());
        if (CollectionUtils.isEmpty(skinReplacements)) {
            return new CisSkinReplacementResult(true);
        }

        List<String> codes = skinReplacements
                .stream().map(p -> p.getReplacementCode()).toList();
        // 检查在限制天数内是否有执行计划日期在今天的报告
        Boolean anyMatch = cisAntimicrobialsSkinExecReportService.findreportsByVisitCode(visitCode)
                .stream()
                .filter(v -> codes.contains(v.getServiceItemCode()))
                .anyMatch(o -> o.getExecPlanDate().isAfter(LocalDateUtil.now().minusDays(limitDays)));

        // 如果有匹配的报告，则返回空字符串
        if (anyMatch) {
            return new CisSkinReplacementResult(false);
        }

        // 如果没有匹配的报告，则返回空字符串
        return new CisSkinReplacementResult(true,
                CisSkinReplacementAssembler.toTos(skinReplacements.stream().filter(CisSkinReplacement::getDefaultFlag).toList()));

    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}