package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.entity.CisConfOrderLimitDetail;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to.CisConfOrderLimitDetailTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisConfOrderLimitDetailAssembler {

    public static List<CisConfOrderLimitDetailTo> toTos(List<CisConfOrderLimitDetail> cisConfOrderLimitDetails) {
		return toTos(cisConfOrderLimitDetails, false);
	}

	public static List<CisConfOrderLimitDetailTo> toTos(List<CisConfOrderLimitDetail> cisConfOrderLimitDetails, boolean withAllParts) {
		BusinessAssert.notNull(cisConfOrderLimitDetails, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001,"参数cisConfOrderLimitDetails");

		List<CisConfOrderLimitDetailTo> tos = new ArrayList<>();
		for (CisConfOrderLimitDetail cisConfOrderLimitDetail : cisConfOrderLimitDetails)
			tos.add(toTo(cisConfOrderLimitDetail, withAllParts));
		return tos;
	}

	public static CisConfOrderLimitDetailTo toTo(CisConfOrderLimitDetail cisConfOrderLimitDetail) {
		return toTo(cisConfOrderLimitDetail, false);
	}

	/**
	 * @generated
	 */
	public static CisConfOrderLimitDetailTo toTo(CisConfOrderLimitDetail cisConfOrderLimitDetail, boolean withAllParts) {
		if (cisConfOrderLimitDetail == null)
			return null;
		CisConfOrderLimitDetailTo to = new CisConfOrderLimitDetailTo();
        to.setId(cisConfOrderLimitDetail.getId());
        to.setCisConfOrderLimitId(cisConfOrderLimitDetail.getCisConfOrderLimitId());
        to.setItemCode(cisConfOrderLimitDetail.getItemCode());
        to.setItemName(cisConfOrderLimitDetail.getItemName());
        to.setVersion(cisConfOrderLimitDetail.getVersion());
        to.setCreatedStaff(cisConfOrderLimitDetail.getCreatedStaff());
        to.setCreatedStaffName(cisConfOrderLimitDetail.getCreatedStaffName());
        to.setCreatedDate(cisConfOrderLimitDetail.getCreatedDate());
        to.setUpdatedStaff(cisConfOrderLimitDetail.getUpdatedStaff());
		to.setUpdatedStaffName(cisConfOrderLimitDetail.getUpdatedStaffName());
        to.setUpdatedDate(cisConfOrderLimitDetail.getUpdatedDate());
        to.setEnabled(cisConfOrderLimitDetail.isEnabled());
        to.setDetailType(cisConfOrderLimitDetail.getDetailType());

		if (withAllParts) {
		}
		return to;
	}

}