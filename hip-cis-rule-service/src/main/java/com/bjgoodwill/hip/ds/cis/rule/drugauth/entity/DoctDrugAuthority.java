package com.bjgoodwill.hip.ds.cis.rule.drugauth.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.repository.DoctDrugAuthorityRepository;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.to.DoctDrugAuthorityEto;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.to.DoctDrugAuthorityNto;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.to.DoctDrugAuthorityQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "医生开立药品权限")
@Table(name = "cis_doct_drug_authority", indexes = {@Index(name = "doct_drug_authority_staff_id", columnList = "staff_id")}, uniqueConstraints = {})
public class DoctDrugAuthority {

    // 标识
    private String id;
    // 员工工号
    private String staffId;
    // 所属科室
    private String administrativeDept;
    // 中草药权限
    private Boolean chineseHerbFlag;
    // 中成药权限
    private Boolean chinesePatentFlag;
    // 处方权限
    private Boolean canPresc;
    // 医师资格证"
    private Boolean physicianCertificate;
    // 可做手术等级
    private String canSurgeryLevel;
    // 远程会诊权限
    private Boolean isRemoteConsultation;
    // 抗菌药使用培训标识
    private Boolean antiTrainingFlag;
    // 员工维护增加抗菌药物分级管理权限：1、限制使用级、2、非限制使用级、3、特殊使用级
    private String antiTrainingLevel;
    //药理属性名称
    private String toxiPropertyNames;
    // 贵重药权限
    private Boolean canValuableMedicineFlag;
    // 抗肿瘤药权限
    private Boolean canAntineoplasticFlag;
    // 抗肿瘤药权限等级
    private String antineoplasticLevel;
    // 逻辑删除标记
    private boolean deleted;
    // 创建的人员
    private String createdStaff;
    // 创建的人员姓名
    private String createdStaffName;
    // 创建的时间
    private LocalDateTime createdDate;
    // 最后修改的人员
    private String updatedStaff;
    // 最后修改的人员姓名
    private String updatedStaffName;
    // 最后修改的时间
    private LocalDateTime updatedDate;

    private Integer version;
    // 员工名称
    private String staffName;
    // 所属科室名称
    private String administrativeDeptName;

    public static Optional<DoctDrugAuthority> getDoctDrugAuthorityById(String id) {
        return dao().findById(id);
    }

    public static Optional<DoctDrugAuthority> getDoctDrugAuthorityByStaffId(String staffId) {
        return dao().findByStaffId(staffId);
    }

    public static List<DoctDrugAuthority> getDoctDrugAuthorities(DoctDrugAuthorityQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<DoctDrugAuthority> getDoctDrugAuthorityPage(DoctDrugAuthorityQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<DoctDrugAuthority> findByStaffIds(List<String> staffIds) {
        return Lists.partition(staffIds, 100).stream().map(list -> dao().findDoctDrugAuthoritiesByStaffIdInAndDeletedFalse(list))
                .flatMap(List::stream).toList();
    }

    /**
     * @generated
     */
    private static Specification<DoctDrugAuthority> getSpecification(DoctDrugAuthorityQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getStaffId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("staffId"), qto.getStaffId()));
            }
            if (StringUtils.isNotBlank(qto.getAdministrativeDept())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("administrativeDept"), qto.getAdministrativeDept()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            return predicate;
        };
    }

    private static DoctDrugAuthorityRepository dao() {
        return SpringUtil.getBean(DoctDrugAuthorityRepository.class);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("员工工号")
    @Column(name = "staff_id", nullable = false)
    public String getStaffId() {
        return staffId;
    }

    protected void setStaffId(String staffId) {
        this.staffId = staffId;
    }

    @Comment("所属科室")
    @Column(name = "administrative_dept", nullable = false)
    public String getAdministrativeDept() {
        return administrativeDept;
    }

    public void setAdministrativeDept(String administrativeDept) {
        this.administrativeDept = administrativeDept;
    }

    @Comment("中草药权限")
    @Column(name = "chinese_herb_flag", nullable = true)
    public Boolean getChineseHerbFlag() {
        return chineseHerbFlag;
    }

    protected void setChineseHerbFlag(Boolean chineseHerbFlag) {
        this.chineseHerbFlag = chineseHerbFlag;
    }

    @Comment("中成药权限")
    @Column(name = "chinese_patent_flag", nullable = true)
    public Boolean getChinesePatentFlag() {
        return chinesePatentFlag;
    }

    protected void setChinesePatentFlag(Boolean chinesePatentFlag) {
        this.chinesePatentFlag = chinesePatentFlag;
    }

    @Comment("处方权限")
    @Column(name = "can_presc", nullable = true)
    public Boolean getCanPresc() {
        return canPresc;
    }

    protected void setCanPresc(Boolean canPresc) {
        this.canPresc = canPresc;
    }

    @Comment("医师资格证")
    @Column(name = "physician_certificate", nullable = true)
    public Boolean getPhysicianCertificate() {
        return physicianCertificate;
    }

    public void setPhysicianCertificate(Boolean physicianCertificate) {
        this.physicianCertificate = physicianCertificate;
    }

    @Comment("可做手术等级")
    @Column(name = "can_surgery_level", nullable = true)
    public String getCanSurgeryLevel() {
        return canSurgeryLevel;
    }

    protected void setCanSurgeryLevel(String canSurgeryLevel) {
        this.canSurgeryLevel = canSurgeryLevel;
    }

    @Comment("远程会诊权限")
    @Column(name = "is_remote_consultation", nullable = true)
    public Boolean getIsRemoteConsultation() {
        return isRemoteConsultation;
    }

    protected void setIsRemoteConsultation(Boolean isRemoteConsultation) {
        this.isRemoteConsultation = isRemoteConsultation;
    }

    @Comment("抗菌药使用培训标识")
    @Column(name = "anti_training_flag", nullable = true)
    public Boolean getAntiTrainingFlag() {
        return antiTrainingFlag;
    }

    protected void setAntiTrainingFlag(Boolean antiTrainingFlag) {
        this.antiTrainingFlag = antiTrainingFlag;
    }

    @Comment("员工维护增加抗菌药物分级管理权限：1、限制使用级、2、非限制使用级、3、特殊使用级")
    @Column(name = "anti_training_level", nullable = true)
    public String getAntiTrainingLevel() {
        return antiTrainingLevel;
    }

    protected void setAntiTrainingLevel(String antiTrainingLevel) {
        this.antiTrainingLevel = antiTrainingLevel;
    }

    @Comment("麻药精神权限")
    @Column(name = "toxi_property_names", nullable = true)
    public String getToxiPropertyNames() {
        return toxiPropertyNames;
    }

    public void setToxiPropertyNames(String toxiPropertyNames) {
        this.toxiPropertyNames = toxiPropertyNames;
    }

    @Comment("贵重药权限")
    @Column(name = "can_valuable_medicine_flag", nullable = true)
    public Boolean getCanValuableMedicineFlag() {
        return canValuableMedicineFlag;
    }

    protected void setCanValuableMedicineFlag(Boolean canValuableMedicineFlag) {
        this.canValuableMedicineFlag = canValuableMedicineFlag;
    }

    @Comment("抗肿瘤药权限")
    @Column(name = "can_antineoplastic_flag", nullable = true)
    public Boolean getCanAntineoplasticFlag() {
        return canAntineoplasticFlag;
    }

    protected void setCanAntineoplasticFlag(Boolean canAntineoplasticFlag) {
        this.canAntineoplasticFlag = canAntineoplasticFlag;
    }

    @Comment("抗肿瘤药权限等级")
    @Column(name = "antineoplastic_level", nullable = true)
    public String getAntineoplasticLevel() {
        return antineoplasticLevel;
    }

    public void setAntineoplasticLevel(String antineoplasticLevel) {
        this.antineoplasticLevel = antineoplasticLevel;
    }

    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Version
    @Comment("版本")
    @Column(name = "version", nullable = true)
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
    @Comment("员工名称")
    @Column(name = "staff_name", nullable = true)
    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }
    @Comment("所属科室名称")
    @Column(name = "administrative_dept_name", nullable = true)
    public String getAdministrativeDeptName() {
        return administrativeDeptName;
    }

    public void setAdministrativeDeptName(String administrativeDeptName) {
        this.administrativeDeptName = administrativeDeptName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        DoctDrugAuthority other = (DoctDrugAuthority) obj;
        return Objects.equals(id, other.id);
    }

    public DoctDrugAuthority create(DoctDrugAuthorityNto doctDrugAuthorityNto) {
        BusinessAssert.notNull(doctDrugAuthorityNto, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "参数doctDrugAuthorityNto");

        setId(doctDrugAuthorityNto.getId());
        setStaffId(doctDrugAuthorityNto.getStaffId());
        setAdministrativeDept(doctDrugAuthorityNto.getAdministrativeDept());
        setChineseHerbFlag(doctDrugAuthorityNto.getChineseHerbFlag());
        setChinesePatentFlag(doctDrugAuthorityNto.getChinesePatentFlag());
        setCanPresc(doctDrugAuthorityNto.getCanPresc());
        setPhysicianCertificate(doctDrugAuthorityNto.getPhysicianCertificate());
        setCanSurgeryLevel(doctDrugAuthorityNto.getCanSurgeryLevel());
        setIsRemoteConsultation(doctDrugAuthorityNto.getIsRemoteConsultation());
        setAntiTrainingFlag(doctDrugAuthorityNto.getAntiTrainingFlag());
        setAntiTrainingLevel(doctDrugAuthorityNto.getAntiTrainingLevel());
        setToxiPropertyNames(doctDrugAuthorityNto.getToxiPropertyNames());
        setCanValuableMedicineFlag(doctDrugAuthorityNto.getCanValuableMedicineFlag());
        setCanAntineoplasticFlag(doctDrugAuthorityNto.getCanAntineoplasticFlag());
        setAntineoplasticLevel(doctDrugAuthorityNto.getAntineoplasticLevel());
        setDeleted(false);
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateTime.now());
        setStaffName(doctDrugAuthorityNto.getStaffName());
        setAdministrativeDeptName(doctDrugAuthorityNto.getAdministrativeDeptName());
        dao().save(this);
        return this;
    }

    public void update(DoctDrugAuthorityEto doctDrugAuthorityEto) {
        BusinessAssert.isTrue(doctDrugAuthorityEto.getVersion().equals(getVersion()), CisRuleBusinessErrorEnum.BUS_CIS_RULE_0003, "");

        setAdministrativeDept(doctDrugAuthorityEto.getAdministrativeDept());
        setChineseHerbFlag(doctDrugAuthorityEto.getChineseHerbFlag());
        setChinesePatentFlag(doctDrugAuthorityEto.getChinesePatentFlag());
        setCanPresc(doctDrugAuthorityEto.getCanPresc());
        setPhysicianCertificate(doctDrugAuthorityEto.getPhysicianCertificate());
        setCanSurgeryLevel(doctDrugAuthorityEto.getCanSurgeryLevel());
        setIsRemoteConsultation(doctDrugAuthorityEto.getIsRemoteConsultation());
        setAntiTrainingFlag(doctDrugAuthorityEto.getAntiTrainingFlag());
        setAntiTrainingLevel(doctDrugAuthorityEto.getAntiTrainingLevel());
        setAntineoplasticLevel(doctDrugAuthorityEto.getAntineoplasticLevel());
        setToxiPropertyNames(doctDrugAuthorityEto.getToxiPropertyNames());
        setCanValuableMedicineFlag(doctDrugAuthorityEto.getCanValuableMedicineFlag());
        setCanAntineoplasticFlag(doctDrugAuthorityEto.getCanAntineoplasticFlag());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        setAdministrativeDeptName(doctDrugAuthorityEto.getAdministrativeDeptName());
    }

    public void delete() {
        setDeleted(true);
    }

}
