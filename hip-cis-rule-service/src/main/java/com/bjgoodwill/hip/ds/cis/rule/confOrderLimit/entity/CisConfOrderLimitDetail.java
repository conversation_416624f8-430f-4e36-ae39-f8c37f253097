package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DetailTypeEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.repository.CisConfOrderLimitDetailRepository;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to.CisConfOrderLimitDetailEto;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to.CisConfOrderLimitDetailNto;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to.CisConfOrderLimitDetailQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "医护限制明细")
@Table(name = "cis_conf_order_limit_detail", indexes = {@Index(name = "idx_cis_conf_order_limit_detail_conf_order_limit_id", columnList = "cisConfOrderLimitId")}, uniqueConstraints = {})
public class CisConfOrderLimitDetail {

    // 标识
    private String id;
    // 医护限制维护标识
    private String cisConfOrderLimitId;
    //
    private String itemCode;
    //
    private String itemName;
    // 版本
    private Integer version;
    // 创建的人员
    private String createdStaff;
    // 创建的时间
    private LocalDateTime createdDate;
    // 最后修改的人员
    private String updatedStaff;
    // 最后修改的时间
    private LocalDateTime updatedDate;
    // 已启用
    private boolean enabled;
    //
    private DetailTypeEnum detailType;
    // 创建的人员
    private String createdStaffName;
    // 最后修改的人员
    private String updatedStaffName;

    public static Optional<CisConfOrderLimitDetail> getCisConfOrderLimitDetailById(String id) {
        return dao().findById(id);
    }

    public static List<CisConfOrderLimitDetail> getCisConfOrderLimitDetails(String cisConfOrderLimitId, CisConfOrderLimitDetailQto qto) {
        if (cisConfOrderLimitId != null) {
            qto.setCisConfOrderLimitId(cisConfOrderLimitId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisConfOrderLimitDetail> getCisConfOrderLimitDetailPage(String cisConfOrderLimitId, CisConfOrderLimitDetailQto qto) {

        if (cisConfOrderLimitId != null) {
            qto.setCisConfOrderLimitId(cisConfOrderLimitId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisConfOrderLimitDetail> getByCisConfOrderLimitId(String cisConfOrderLimitId) {
        return dao().findByCisConfOrderLimitId(cisConfOrderLimitId);
    }

    public static void deleteByCisConfOrderLimitId(String cisConfOrderLimitId) {
        dao().deleteByCisConfOrderLimitId(cisConfOrderLimitId);
    }

    /**
     * @generated
     */
    private static Specification<CisConfOrderLimitDetail> getSpecification(CisConfOrderLimitDetailQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getCisConfOrderLimitId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisConfOrderLimitId"), qto.getCisConfOrderLimitId()));
            }
            if (qto.getEnabled() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("enabled"), qto.getEnabled()));
            }
            if (qto.getDetailType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("detailType"), qto.getDetailType()));
            }
            return predicate;
        };
    }

    private static CisConfOrderLimitDetailRepository dao() {
        return SpringUtil.getBean(CisConfOrderLimitDetailRepository.class);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("医护限制维护标识")
    @Column(name = "cis_conf_order_limit_id", nullable = false, length = 50)
    public String getCisConfOrderLimitId() {
        return cisConfOrderLimitId;
    }

    protected void setCisConfOrderLimitId(String cisConfOrderLimitId) {
        this.cisConfOrderLimitId = cisConfOrderLimitId;
    }

    @Comment("项目编码")
    @Column(name = "item_code", nullable = false)
    public String getItemCode() {
        return itemCode;
    }

    protected void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    @Comment("项目名称")
    @Column(name = "item_name", nullable = true)
    public String getItemName() {
        return itemName;
    }

    protected void setItemName(String itemName) {
        this.itemName = itemName;
    }

    @Version
    @Comment("版本")
    @Column(name = "version", nullable = false)
    public Integer getVersion() {
        return version;
    }

    protected void setVersion(Integer version) {
        this.version = version;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Comment("已启用")
    @Column(name = "enabled", nullable = false)
    public boolean isEnabled() {
        return enabled;
    }

    protected void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Enumerated(EnumType.STRING)
    @Comment("明细类型")
    @Column(name = "detail_type", nullable = true)
    public DetailTypeEnum getDetailType() {
        return detailType;
    }

    protected void setDetailType(DetailTypeEnum detailType) {
        this.detailType = detailType;
    }

    @Comment("创建的人员名称")
    @Column(name = "created_staff_name", nullable = true)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("最后修改的人员名称")
    @Column(name = "updated_staff_name", nullable = true)
    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Transient
    public CisConfOrderLimit getCisConfOrderLimit() {
        return CisConfOrderLimit.getCisConfOrderLimitById(getCisConfOrderLimitId()).orElse(null);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisConfOrderLimitDetail other = (CisConfOrderLimitDetail) obj;
        return Objects.equals(id, other.id);
    }

    public CisConfOrderLimitDetail create(String cisConfOrderLimitId, CisConfOrderLimitDetailNto cisConfOrderLimitDetailNto) {
        BusinessAssert.notNull(cisConfOrderLimitDetailNto, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "参数cisConfOrderLimitDetailNto");

        setCisConfOrderLimitId(cisConfOrderLimitId);

        setId(cisConfOrderLimitDetailNto.getId());
        setItemCode(cisConfOrderLimitDetailNto.getItemCode());
        setItemName(cisConfOrderLimitDetailNto.getItemName());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        setEnabled(true);
        setDetailType(cisConfOrderLimitDetailNto.getDetailType());
        dao().save(this);
        return this;
    }

    public void update(CisConfOrderLimitDetailEto cisConfOrderLimitDetailEto) {
        setItemCode(cisConfOrderLimitDetailEto.getItemCode());
        setItemName(cisConfOrderLimitDetailEto.getItemName());
        setVersion(cisConfOrderLimitDetailEto.getVersion());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        setEnabled(cisConfOrderLimitDetailEto.isEnabled());
        setDetailType(cisConfOrderLimitDetailEto.getDetailType());
    }

    public void enable() {
        setEnabled(true);
    }

    public void disable() {
        setEnabled(false);
    }

    public void delete() {
        dao().delete(this);
    }

}
