package com.bjgoodwill.hip.ds.cis.rule.drugLimit.repository;

import com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity.DocLimit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.rule.drugLimit.repository.DocLimitRepository")
public interface DocLimitRepository extends JpaRepository<DocLimit, String>, JpaSpecificationExecutor<DocLimit> {

    List<DocLimit> findDocLimitByDrugCodeInAndDeletedFalse(List<String> drugCodes);
}