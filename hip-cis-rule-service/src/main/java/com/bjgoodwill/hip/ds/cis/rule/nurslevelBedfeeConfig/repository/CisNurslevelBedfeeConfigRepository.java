package com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.repository;

import com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.entity.CisNurslevelBedfeeConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.repository.CisNurslevelBedfeeConfigRepository")
public interface CisNurslevelBedfeeConfigRepository extends JpaRepository<CisNurslevelBedfeeConfig, String>, JpaSpecificationExecutor<CisNurslevelBedfeeConfig> {

}