package com.bjgoodwill.hip.ds.cis.rule.drugLimit.repository;

import com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity.OrgLimit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.rule.drugLimit.repository.OrgLimitRepository")
public interface OrgLimitRepository extends JpaRepository<OrgLimit, String>, JpaSpecificationExecutor<OrgLimit> {
    List<OrgLimit> findOrgLimitByDrugCodeInAndDeletedFalse(List<String> drugCodes);
}