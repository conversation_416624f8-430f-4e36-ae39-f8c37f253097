package com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.entity.CisNurslevelBedfeeConfig;
import com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.service.internal.assembler.CisNurslevelBedfeeConfigAssembler;
import com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.to.CisNurslevelBedfeeConfigEto;
import com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.to.CisNurslevelBedfeeConfigNto;
import com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.to.CisNurslevelBedfeeConfigQto;
import com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.to.CisNurslevelBedfeeConfigTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.service.CisNurslevelBedfeeConfigService")
@RequestMapping(value = "/api/rule/nurslevelBedfeeConfig/cisNurslevelBedfeeConfig", produces = "application/json; charset=utf-8")
public class CisNurslevelBedfeeConfigServiceImpl implements CisNurslevelBedfeeConfigService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisNurslevelBedfeeConfigTo> getCisNurslevelBedfeeConfigs(CisNurslevelBedfeeConfigQto cisNurslevelBedfeeConfigQto) {
        return CisNurslevelBedfeeConfigAssembler.toTos(CisNurslevelBedfeeConfig.getCisNurslevelBedfeeConfigs(cisNurslevelBedfeeConfigQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisNurslevelBedfeeConfigTo> getCisNurslevelBedfeeConfigPage(CisNurslevelBedfeeConfigQto cisNurslevelBedfeeConfigQto) {
        Page<CisNurslevelBedfeeConfig> page = CisNurslevelBedfeeConfig.getCisNurslevelBedfeeConfigPage(cisNurslevelBedfeeConfigQto);
        Page<CisNurslevelBedfeeConfigTo> result = page.map(CisNurslevelBedfeeConfigAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisNurslevelBedfeeConfigTo getCisNurslevelBedfeeConfigById(String id) {
        return CisNurslevelBedfeeConfigAssembler.toTo(CisNurslevelBedfeeConfig.getCisNurslevelBedfeeConfigById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisNurslevelBedfeeConfigTo createCisNurslevelBedfeeConfig(CisNurslevelBedfeeConfigNto cisNurslevelBedfeeConfigNto) {
        CisNurslevelBedfeeConfig cisNurslevelBedfeeConfig = new CisNurslevelBedfeeConfig();
        cisNurslevelBedfeeConfig = cisNurslevelBedfeeConfig.create(cisNurslevelBedfeeConfigNto);
        return CisNurslevelBedfeeConfigAssembler.toTo(cisNurslevelBedfeeConfig);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisNurslevelBedfeeConfig(String id, CisNurslevelBedfeeConfigEto cisNurslevelBedfeeConfigEto) {
        Optional<CisNurslevelBedfeeConfig> cisNurslevelBedfeeConfigOptional = CisNurslevelBedfeeConfig.getCisNurslevelBedfeeConfigById(id);
        cisNurslevelBedfeeConfigOptional.ifPresent(cisNurslevelBedfeeConfig -> cisNurslevelBedfeeConfig.update(cisNurslevelBedfeeConfigEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisNurslevelBedfeeConfig(String id) {
        Optional<CisNurslevelBedfeeConfig> cisNurslevelBedfeeConfigOptional = CisNurslevelBedfeeConfig.getCisNurslevelBedfeeConfigById(id);
        cisNurslevelBedfeeConfigOptional.ifPresent(cisNurslevelBedfeeConfig -> cisNurslevelBedfeeConfig.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}