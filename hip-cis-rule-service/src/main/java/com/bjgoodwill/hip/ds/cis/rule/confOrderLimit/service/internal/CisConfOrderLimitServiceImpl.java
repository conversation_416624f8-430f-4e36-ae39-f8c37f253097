package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DetailTypeEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DocNurseTypeEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.RuleTypeEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.entity.CisConfOrderLimit;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.entity.CisConfOrderLimitDetail;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.entity.CisConfOrderLimitRule;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service.CisConfOrderLimitService;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service.internal.assembler.CisConfOrderLimitAssembler;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service.internal.assembler.CisConfOrderLimitDetailAssembler;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service.internal.assembler.CisConfOrderLimitRuleAssembler;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RestController("com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service.CisConfOrderLimitService")
@RequestMapping(value = "/api/rule/confOrderLimit/cisConfOrderLimit", produces = "application/json; charset=utf-8")
public class CisConfOrderLimitServiceImpl implements CisConfOrderLimitService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisConfOrderLimitTo> getCisConfOrderLimits(CisConfOrderLimitQto cisConfOrderLimitQto) {
        return CisConfOrderLimitAssembler.toTos(CisConfOrderLimit.getCisConfOrderLimits(cisConfOrderLimitQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisConfOrderLimitTo> getCisConfOrderLimitPage(CisConfOrderLimitQto cisConfOrderLimitQto) {
        Page<CisConfOrderLimit> page = CisConfOrderLimit.getCisConfOrderLimitPage(cisConfOrderLimitQto);
        Page<CisConfOrderLimitTo> result = page.map(CisConfOrderLimitAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisConfOrderLimitTo createCisConfOrderLimit(CisConfOrderLimitNto cisConfOrderLimitNto) {
        CisConfOrderLimit cisConfOrderLimit = new CisConfOrderLimit();
		cisConfOrderLimit = cisConfOrderLimit.create(cisConfOrderLimitNto);
		return CisConfOrderLimitAssembler.toTo(cisConfOrderLimit);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisConfOrderLimit(String id, CisConfOrderLimitEto cisConfOrderLimitEto) {
        Optional<CisConfOrderLimit> cisConfOrderLimitOptional = CisConfOrderLimit.getCisConfOrderLimitById(id);
		cisConfOrderLimitOptional.ifPresent(cisConfOrderLimit -> cisConfOrderLimit.update(cisConfOrderLimitEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void enableCisConfOrderLimit(String id) {
        Optional<CisConfOrderLimit> cisConfOrderLimitOptional = CisConfOrderLimit.getCisConfOrderLimitById(id);
		cisConfOrderLimitOptional.ifPresent(cisConfOrderLimit -> cisConfOrderLimit.enable());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void disableCisConfOrderLimit(String id) {
        Optional<CisConfOrderLimit> cisConfOrderLimitOptional = CisConfOrderLimit.getCisConfOrderLimitById(id);
		cisConfOrderLimitOptional.ifPresent(cisConfOrderLimit -> cisConfOrderLimit.disable());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisConfOrderLimitRuleTo> getCisConfOrderLimitRules(String cisConfOrderLimitId, CisConfOrderLimitRuleQto cisConfOrderLimitRuleQto) {
        return CisConfOrderLimitRuleAssembler.toTos(CisConfOrderLimitRule.getCisConfOrderLimitRules(cisConfOrderLimitId, cisConfOrderLimitRuleQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisConfOrderLimitRuleTo> getCisConfOrderLimitRulePage(String cisConfOrderLimitId, CisConfOrderLimitRuleQto cisConfOrderLimitRuleQto) {
        Page<CisConfOrderLimitRule> page = CisConfOrderLimitRule.getCisConfOrderLimitRulePage(cisConfOrderLimitId, cisConfOrderLimitRuleQto);
        Page<CisConfOrderLimitRuleTo> result = page.map(CisConfOrderLimitRuleAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisConfOrderLimitRuleTo getCisConfOrderLimitRuleById(String id) {
        return CisConfOrderLimitRuleAssembler.toTo(CisConfOrderLimitRule.getCisConfOrderLimitRuleById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisConfOrderLimitRuleTo createCisConfOrderLimitRule(String cisConfOrderLimitId, CisConfOrderLimitRuleNto cisConfOrderLimitRuleNto) {

        CisConfOrderLimit cisConfOrderLimit = CisConfOrderLimit.getCisConfOrderLimitById(cisConfOrderLimitId).orElse(null);
        BusinessAssert.notNull(cisConfOrderLimit, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "医护限制项目");

        CisConfOrderLimitRule cisConfOrderLimitRule = new CisConfOrderLimitRule();
		cisConfOrderLimitRule = cisConfOrderLimitRule.create(cisConfOrderLimitId, cisConfOrderLimitRuleNto);
		return CisConfOrderLimitRuleAssembler.toTo(cisConfOrderLimitRule);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisConfOrderLimitRule(String id, CisConfOrderLimitRuleEto cisConfOrderLimitRuleEto) {
        Optional<CisConfOrderLimitRule> cisConfOrderLimitRuleOptional = CisConfOrderLimitRule.getCisConfOrderLimitRuleById(id);
		cisConfOrderLimitRuleOptional.ifPresent(cisConfOrderLimitRule -> cisConfOrderLimitRule.update(cisConfOrderLimitRuleEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void enableCisConfOrderLimitRule(String id) {
        Optional<CisConfOrderLimitRule> cisConfOrderLimitRuleOptional = CisConfOrderLimitRule.getCisConfOrderLimitRuleById(id);
		cisConfOrderLimitRuleOptional.ifPresent(cisConfOrderLimitRule -> cisConfOrderLimitRule.enable());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void disableCisConfOrderLimitRule(String id) {
        Optional<CisConfOrderLimitRule> cisConfOrderLimitRuleOptional = CisConfOrderLimitRule.getCisConfOrderLimitRuleById(id);
		cisConfOrderLimitRuleOptional.ifPresent(cisConfOrderLimitRule -> cisConfOrderLimitRule.disable());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisConfOrderLimitDetailTo> getCisConfOrderLimitDetails(String cisConfOrderLimitRuleId, CisConfOrderLimitDetailQto cisConfOrderLimitDetailQto) {
        return CisConfOrderLimitDetailAssembler.toTos(CisConfOrderLimitDetail.getCisConfOrderLimitDetails(cisConfOrderLimitRuleId, cisConfOrderLimitDetailQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisConfOrderLimitDetailTo> getCisConfOrderLimitDetailPage(String cisConfOrderLimitRuleId, CisConfOrderLimitDetailQto cisConfOrderLimitDetailQto) {
        Page<CisConfOrderLimitDetail> page = CisConfOrderLimitDetail.getCisConfOrderLimitDetailPage(cisConfOrderLimitRuleId, cisConfOrderLimitDetailQto);
        Page<CisConfOrderLimitDetailTo> result = page.map(CisConfOrderLimitDetailAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisConfOrderLimitDetailTo createCisConfOrderLimitDetail(String cisConfOrderLimitId, CisConfOrderLimitDetailNto cisConfOrderLimitDetailNto) {
        CisConfOrderLimit cisConfOrderLimit = CisConfOrderLimit.getCisConfOrderLimitById(cisConfOrderLimitId).orElse(null);
        BusinessAssert.notNull(cisConfOrderLimit, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "医护限制项目");

        if(DocNurseTypeEnum.NURSE.equals(cisConfOrderLimit.getType())){
            DetailTypeEnum[] enableDetailTypeEnum = new DetailTypeEnum[]{DetailTypeEnum.DIAGNOSIS};
            BusinessAssert.isTrue(!Arrays.asList(enableDetailTypeEnum).
                    contains(cisConfOrderLimitDetailNto.getDetailType()),
                    CisRuleBusinessErrorEnum.BUS_CIS_RULE_0005, cisConfOrderLimitDetailNto.getDetailType().getName());
        }

        CisConfOrderLimitDetail cisConfOrderLimitDetail = new CisConfOrderLimitDetail();
		cisConfOrderLimitDetail = cisConfOrderLimitDetail.create(cisConfOrderLimitId, cisConfOrderLimitDetailNto);
		return CisConfOrderLimitDetailAssembler.toTo(cisConfOrderLimitDetail);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisConfOrderLimitDetail(String id, CisConfOrderLimitDetailEto cisConfOrderLimitDetailEto) {
        Optional<CisConfOrderLimitDetail> cisConfOrderLimitDetailOptional = CisConfOrderLimitDetail.getCisConfOrderLimitDetailById(id);
		cisConfOrderLimitDetailOptional.ifPresent(cisConfOrderLimitDetail -> cisConfOrderLimitDetail.update(cisConfOrderLimitDetailEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void enableCisConfOrderLimitDetail(String id) {
        Optional<CisConfOrderLimitDetail> cisConfOrderLimitDetailOptional = CisConfOrderLimitDetail.getCisConfOrderLimitDetailById(id);
		cisConfOrderLimitDetailOptional.ifPresent(cisConfOrderLimitDetail -> cisConfOrderLimitDetail.enable());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void disableCisConfOrderLimitDetail(String id) {
        Optional<CisConfOrderLimitDetail> cisConfOrderLimitDetailOptional = CisConfOrderLimitDetail.getCisConfOrderLimitDetailById(id);
		cisConfOrderLimitDetailOptional.ifPresent(cisConfOrderLimitDetail -> cisConfOrderLimitDetail.disable());
    }

    @InitBinder
	public void initBinder(WebDataBinder binder) {
	}

    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void checkDocDetials(List<CheckCisConfOrderLimitQto> qtos){
        qtos.stream().forEach(p->checkDocDetial(p));
    }

    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void checkDocDetial(CheckCisConfOrderLimitQto checkCisConfOrderLimitQto){
        CisConfOrderLimitQto qto = new CisConfOrderLimitQto();
        qto.setType(checkCisConfOrderLimitQto.getDocNurseType());
        List<CisConfOrderLimit> limits = CisConfOrderLimit.getCisConfOrderLimits(qto);

        if(CollectionUtils.isEmpty(limits)){
            return;
        }

        limits.forEach(limit -> checkDetail(limit, checkCisConfOrderLimitQto.getDetailType(), checkCisConfOrderLimitQto.getSex(),
                checkCisConfOrderLimitQto.getBirthday(), checkCisConfOrderLimitQto.getCodes()));
    }

    private void checkDetail(CisConfOrderLimit cisConfOrderLimit, DetailTypeEnum detailType, String sex, LocalDateTime birthday, List<String> codes){

        var details = cisConfOrderLimit.getCisConfOrderLimitDetails().stream().filter(o->o.isEnabled()
                && detailType.equals(o.getDetailType())).toList();
        if(CollectionUtils.isEmpty(details)){
            return;
        }
        Map<String, String> map = details.stream().collect(Collectors.toMap(CisConfOrderLimitDetail::getItemCode, CisConfOrderLimitDetail::getItemName));

        var rules =  cisConfOrderLimit.getCisConfOrderLimitRules().stream().filter(o->o.isEnabled()).collect(Collectors.toList());

        BiFunction<String, CisConfOrderLimitRule, Boolean> func = (s1, s2) -> {
            Pattern pattern = Pattern.compile(s2.getRuleRegular());
            Matcher matcher = pattern.matcher(s1);
            return matcher.matches();
        };

        CisConfOrderLimitRule rule1 =
        rules.stream().filter(p->
                        StringUtils.isEmpty(sex) ||
                        RuleTypeEnum.SEX.equals(p.getRuleType()) &&
                        func.apply(sex, p)).findFirst().orElse(null);


        CisConfOrderLimitRule rule2 =rules.stream().filter(p->{
            if(birthday == null){
                return true;
            }
            long age = ChronoUnit.YEARS.between(birthday, LocalDateTime.now());
            String ageStr = String.valueOf(age);
            return RuleTypeEnum.AGE.equals(p.getRuleType()) &&
                    func.apply(ageStr, p);
        }).findFirst().orElse(null);

        if(rule1 == null && rule2 == null){
            return;
        }

        var result = details.stream().map(CisConfOrderLimitDetail::getItemCode).filter(codes::contains).findFirst();
        BusinessAssert.isTrue(!result.isPresent(), CisRuleBusinessErrorEnum.BUS_CIS_RULE_0006, map.get(result.get()),cisConfOrderLimit.getNodeName());
    }


    //region 护士校对医嘱过滤
    //获取护士限制
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisConfOrderLimitTo> getNurseLimit(){
        CisConfOrderLimitQto qto = new CisConfOrderLimitQto();
        qto.setType(DocNurseTypeEnum.NURSE);

        return CisConfOrderLimitAssembler.toTos(CisConfOrderLimit.getCisConfOrderLimits(qto),true);
    }
    //endregion


}


