package com.bjgoodwill.hip.ds.cis.rule.verify;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.cis.util.DoctAuthCommonQto;
import com.bjgoodwill.hip.business.util.enums.dict.DictCodeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.ds.base.cis.diagnose.extension.enmus.CisOperationLevel;
import com.bjgoodwill.hip.ds.base.cis.diagnose.extension.service.DiagnoseOperationExtensionService;
import com.bjgoodwill.hip.ds.base.cis.diagnose.extension.to.DiagnoseOperationExtensionTo;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.service.DoctDrugAuthorityService;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.to.DoctDrugAuthorityTo;
import com.bjgoodwill.hip.ds.drug.goods.service.DrugGoodsService;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-12-16 14:00
 * @className: DoctAuthorityHandleInterceptService
 * @description: 医师权限校验
 **/
@Order(1)
@Component
public class DoctAuthorityHandleInterceptService extends AbstractOrderHandle {

    @Autowired
    private DoctDrugAuthorityService doctDrugAuthorityService;

    @Autowired
    private DiagnoseOperationExtensionService diagnoseOperationExtensionService;

    @Autowired
    private DrugGoodsService drugGoodsService;

    private DoctDrugAuthorityTo doctDrugAuthorityTo = null;

    @Autowired
    private DictElementService dictElementService;


    /**
     * 核实订单信息
     * 此方法用于验证和处理一批订单信息，通过将订单按类别分组并应用相应的验证逻辑
     *
     * @param qtos 一个包含多个DoctAuthCommonQto对象的列表，代表待验证的订单信息
     */
    @Override
    public List<DoctAuthCommonQto> verifyOrders(List<DoctAuthCommonQto> qtos) {

        // 检查传入的订单列表是否为空，如果为空则直接返回，不进行后续处理
        if (CollectionUtils.isEmpty(qtos)) {
            return qtos;
        }

        doctDrugAuthorityTo = doctDrugAuthorityService.getDoctDrugAuthorityByStaffId(HIPLoginUtil.getStaffId());
        BusinessAssert.notNull(doctDrugAuthorityTo, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, HIPLoginUtil.getLoginName() + ":医师权限");

        //region 手术走越级审批，从应用层进行判断。
        // 将订单列表按订单类别分组，便于后续根据不同类别应用特定的验证逻辑
//        Map<SystemTypeEnum, List<DoctAuthCommonQto>> map = qtos.stream().collect(Collectors.groupingBy(DoctAuthCommonQto::getOrderClass));
        Map<SystemTypeEnum, List<DoctAuthCommonQto>> map = qtos.stream()
                .filter(p -> !SystemTypeEnum.OPERATIONAPPLY.equals(p.getOrderClass()))
                .collect(Collectors.groupingBy(DoctAuthCommonQto::getOrderClass));
        //endregion

        // 遍历分组后的订单地图，对每个类别的订单应用对应的验证逻辑
        map.forEach((k, v) -> {
            // 根据订单类别获取对应的验证逻辑消费者
            Consumer<List<DoctAuthCommonQto>> consumer = verifyMap.get(k);
            // 如果找到了对应的验证逻辑消费者，则应用该逻辑验证当前类别的订单列表
            if (consumer != null) {
                consumer.accept(v);
            }
        });
        return qtos;
    }


    private Map<SystemTypeEnum, Consumer<List<DoctAuthCommonQto>>> verifyMap = Map.of(
            SystemTypeEnum.CDRUG, this::verifyCdrug,
            SystemTypeEnum.EDRUG, this::verifyEdrg,
            SystemTypeEnum.OPERATIONAPPLY, this::verifyOperation
    );


    /**
     * 核实医生开具草药的权限
     * 此方法检查当前医生是否有权限开具草药，以确保符合业务规则
     *
     * @param qtos 包含医生开具信息的列表，用于后续可能的处理或验证
     */
    private void verifyCdrug(List<DoctAuthCommonQto> qtos) {
        // 确保当前登录医生有权限开具草药，否则抛出业务异常
        BusinessAssert.isTrue((doctDrugAuthorityTo.getChineseHerbFlag() != null && doctDrugAuthorityTo.getChineseHerbFlag()),
                CisRuleBusinessErrorEnum.BUS_CIS_RULE_00011, HIPLoginUtil.getLoginUserName() + "开立草药");
    }


    /**
     * 验证医生是否有权开立特定的成药
     * 此方法首先确保医生有处方权，然后检查医生是否有权限开立给定列表中的成药
     * 它通过比较医生的权限和药品的药理属性来确定医生是否有权开立这些药品
     *
     * @param qtos 包含药品信息的查询传输对象列表，用于验证医生的开立权限
     */
    private void verifyEdrg(List<DoctAuthCommonQto> qtos) {
        // 确保医生有处方权
        BusinessAssert.isTrue((doctDrugAuthorityTo.getCanPresc() != null && doctDrugAuthorityTo.getCanPresc()),
                CisRuleBusinessErrorEnum.BUS_CIS_RULE_00011, HIPLoginUtil.getLoginUserName() + "开立成药");

        List<String> drugCodes = qtos.stream().map(DoctAuthCommonQto::getServiceItemCode).distinct().toList();
        List<DrugGoodsTo> drugGoodsTos = drugGoodsService.getDrugGoodsByIds(drugCodes);

        // 提取所有药品的药理属性名称，并确保唯一性
        Set<String> toxiPropertyNames = drugGoodsTos.stream().map(DrugGoodsTo::getToxiPropertyName).collect(Collectors.toSet());

        // 将医生的药理属性权限转换为标准格式，以便后续比较
        List<String> permissions = Arrays.stream(this.doctDrugAuthorityTo.getToxiPropertyNames().split(","))
                .map(p -> dictElementService.getCustomDictElement(DictCodeEnum.毒理属性.getCode(),p).getElementName()).toList();

        // 检查是否有药品的药理属性是医生没有权限的
        List<String> noPermissions = toxiPropertyNames.stream().filter(p -> !permissions.contains(p)).toList();


        // 如果存在医生没有权限的药理属性，抛出异常
        BusinessAssert.isEmpty(noPermissions, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0008, HIPLoginUtil.getLoginUserName(), noPermissions);
    }

    /**
     * 验证手术等级维护操作的合法性
     * 此方法主要负责检查给定的服务项目代码是否都有对应的手术等级，并且这些手术等级是在允许的范围内
     *
     * @param qtos 一个包含服务项目代码的列表，用于查询和验证手术等级
     */
    private void verifyOperation(List<DoctAuthCommonQto> qtos) {
        // 确保手术等级字段有值，否则抛出业务异常
        BusinessAssert.hasText(doctDrugAuthorityTo.getCanSurgeryLevel(), CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, HIPLoginUtil.getLoginUserName() + "手术等级维护");

        // 提取并去重服务项目代码
        List<String> operationServiceCodes = qtos.stream().map(DoctAuthCommonQto::getServiceItemCode).distinct().toList();
        // 根据服务项目代码获取诊断手术扩展信息列表
        List<DiagnoseOperationExtensionTo> diagnoseOperationExtensionTos = diagnoseOperationExtensionService.getDiagnoseOperationExtensionInExtCodes(operationServiceCodes);

        // 提取所有已存在的手术扩展代码
        List<String> existCode = diagnoseOperationExtensionTos.stream().map(DiagnoseOperationExtensionTo::getOperationExtCode).toList();

        // 检查是否存在不存在的手术服务代码
        Optional<String> notExistCode = operationServiceCodes.stream().filter(p -> !existCode.contains(p)).findFirst();
        // 如果存在不存在的手术服务代码，抛出业务异常
        BusinessAssert.isTrue(notExistCode.isPresent(), CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, notExistCode.get() + "手术等级维护");

        // 将允许的手术等级字符串转换为枚举列表
        List<CisOperationLevel> operationLevels = Arrays.stream(doctDrugAuthorityTo.getCanSurgeryLevel().split(","))
                .toList().stream().map(CisOperationLevel::valueOf).toList();

        // 检查是否存在手术等级不在允许范围内的诊断手术扩展信息
        Optional<DiagnoseOperationExtensionTo> notExistLevel = diagnoseOperationExtensionTos.stream().filter(p -> !operationLevels.contains(p.getOperationLevel())).findFirst();
        // 如果存在手术等级不在允许范围内的诊断手术扩展信息，抛出业务异常
        BusinessAssert.isTrue(notExistLevel.isEmpty(), CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, notExistLevel.get().getOperationExtName() + "手术等级维护");
    }
}