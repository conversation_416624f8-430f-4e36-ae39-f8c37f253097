package com.bjgoodwill.hip.ds.cis.rule.skin.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.rule.skin.entity.CisSkinLimit;
import com.bjgoodwill.hip.ds.cis.rule.skin.to.CisSkinLimitTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisSkinLimitAssembler {

    public static List<CisSkinLimitTo> toTos(List<CisSkinLimit> cisSkinLimits) {
        return toTos(cisSkinLimits, true);
    }

    public static List<CisSkinLimitTo> toTos(List<CisSkinLimit> cisSkinLimits, boolean withAllParts) {
        Assert.notNull(cisSkinLimits, "参数cisSkinLimits不能为空！");

        List<CisSkinLimitTo> tos = new ArrayList<>();
        for (CisSkinLimit cisSkinLimit : cisSkinLimits)
            tos.add(toTo(cisSkinLimit, withAllParts));
        return tos;
    }

    public static CisSkinLimitTo toTo(CisSkinLimit cisSkinLimit) {
        return toTo(cisSkinLimit, false);
    }

    /**
     * @generated
     */
    public static CisSkinLimitTo toTo(CisSkinLimit cisSkinLimit, boolean withAllParts) {
        if (cisSkinLimit == null)
            return null;
        CisSkinLimitTo to = new CisSkinLimitTo();
        to.setId(cisSkinLimit.getId());
        to.setDrugCode(cisSkinLimit.getDrugCode());
        to.setDrugName(cisSkinLimit.getDrugName());
        to.setLimitDay(cisSkinLimit.getLimitDay());
        to.setChildLimitDay(cisSkinLimit.getChildLimitDay());
        to.setCreatedDate(cisSkinLimit.getCreatedDate());
        to.setCreatedStaff(cisSkinLimit.getCreatedStaff());
        to.setCreatedStaffName(cisSkinLimit.getCreatedStaffName());
        to.setUpdatedDate(cisSkinLimit.getUpdatedDate());
        to.setUpdatedStaff(cisSkinLimit.getUpdatedStaff());
        to.setUpdatedStaffName(cisSkinLimit.getUpdatedStaffName());
        to.setVersion(cisSkinLimit.getVersion());
        to.setEnabled(cisSkinLimit.isEnabled());
        to.setOrgCode(cisSkinLimit.getOrgCode());
        to.setHospitalFlag(cisSkinLimit.getHospitalFlag());
        to.setOrgName(cisSkinLimit.getOrgName());

        if (withAllParts) {
            to.setCisSkinReplacements(CisSkinReplacementAssembler.toTos(cisSkinLimit.getCisSkinReplacements()));
        }
        return to;
    }

}