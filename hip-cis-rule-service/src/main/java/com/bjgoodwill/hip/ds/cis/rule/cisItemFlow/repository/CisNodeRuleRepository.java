package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.repository;

import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.entity.CisNodeRule;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.repository.NodeRuleRepository")
public interface CisNodeRuleRepository extends JpaRepository<CisNodeRule, String>, JpaSpecificationExecutor<CisNodeRule> {

    boolean existsBySequence(Double sequence);

    boolean existsBySequenceAndIdNot(Double sequence, String id);

    List<CisNodeRule> findByCisFlowNodeSubId(String cisFlowNodeSubId);

    Page<CisNodeRule> findByCisFlowNodeSubId(String cisFlowNodeSubId, Pageable pageable);

    boolean existsByCisFlowNodeSubId(String cisFlowNodeSubId);

    void deleteByCisFlowNodeSubId(String cisFlowNodeSubId);

}