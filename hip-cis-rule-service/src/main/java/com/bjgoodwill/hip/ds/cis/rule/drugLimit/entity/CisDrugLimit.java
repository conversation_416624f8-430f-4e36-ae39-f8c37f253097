package com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.repository.CisDrugLimitRepository;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.CisDrugLimitEto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.CisDrugLimitNto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.CisDrugLimitQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "药品限制")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", discriminatorType = DiscriminatorType.STRING, length = 20)
@Table(name = "cis_drug_limit", indexes = {@Index(name = "idx_cis_drug_limit_drug_code", columnList = "drugCode")}, uniqueConstraints = {})
public abstract class CisDrugLimit {

    // 标识
    private String id;
    // 药品编码
    private String drugCode;
    // 药品名称
    private String drugName;
    // 逻辑删除标记
    private boolean deleted;
    // 创建的人员
    private String createdStaff;
    // 创建的人员姓名
    private String createdStaffName;
    // 创建的时间
    private LocalDateTime createdDate;
    // 最后修改的人员
    private String updatedStaff;
    // 最后修改的人员姓名
    private String updatedStaffName;
    // 最后修改的时间
    private LocalDateTime updatedDate;

    private Integer version;
    // 工作组类型
    private String workGroupTypeCode;
    // 工作组类型名称
    private String workGroupTypeName;

	@Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
    	return id;
    }

    protected void setId(String id) {
    	this.id = id;
    }

    @Comment("药品编码")
    @Column(name = "drug_code", nullable = false)
    public String getDrugCode() {
    	return drugCode;
    }

    protected void setDrugCode(String drugCode) {
    	this.drugCode = drugCode;
    }

    @Comment("药品名称")
    @Column(name = "drug_name", nullable = false)
    public String getDrugName() {
    	return drugName;
    }

    protected void setDrugName(String drugName) {
    	this.drugName = drugName;
    }

    @Comment("工作组类型编码")
    @Column(name = "work_group_type_code", nullable = true)
    public String getWorkGroupTypeCode() {
        return workGroupTypeCode;
    }

    public void setWorkGroupTypeCode(String workGroupTypeCode) {
        this.workGroupTypeCode = workGroupTypeCode;
    }
    @Comment("工作组类型名称")
    @Column(name = "work_group_type_name", nullable = true)
    public String getWorkGroupTypeName() {
        return workGroupTypeName;
    }

    public void setWorkGroupTypeName(String workGroupTypeName) {
        this.workGroupTypeName = workGroupTypeName;
    }

    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    public boolean isDeleted() {
    	return deleted;
    }

    protected void setDeleted(boolean deleted) {
    	this.deleted = deleted;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
    	return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    @Version
    @Comment("版本")
    @Column(name = "version", nullable = false)
    public Integer getVersion() {
    	return version;
    }

	protected void setVersion(Integer version) {
    	this.version = version;
    }

	@Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisDrugLimit other = (CisDrugLimit) obj;
		return Objects.equals(id, other.id);
	}

    public CisDrugLimit create(CisDrugLimitNto cisDrugLimitNto) {
        BusinessAssert.notNull(cisDrugLimitNto, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001,"参数cisDrugLimitNto");
        BusinessAssert.isNull(getCisDrugLimitById(cisDrugLimitNto.getId()).orElse(null)
                , CisRuleBusinessErrorEnum.BUS_CIS_RULE_0002
                ,"药品限制！");
        setId(cisDrugLimitNto.getId());
        setDrugCode(cisDrugLimitNto.getDrugCode());
        setDrugName(cisDrugLimitNto.getDrugName());
        setDeleted(false);
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        setWorkGroupTypeCode(cisDrugLimitNto.getWorkGroupTypeCode());
        setWorkGroupTypeName(cisDrugLimitNto.getWorkGroupTypeName());
        return this;
    }

    public void update(CisDrugLimitEto cisDrugLimitEto) {
        setDrugCode(cisDrugLimitEto.getDrugCode());
        setDrugName(cisDrugLimitEto.getDrugName());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        setWorkGroupTypeCode(cisDrugLimitEto.getWorkGroupTypeCode());
        setWorkGroupTypeName(cisDrugLimitEto.getWorkGroupTypeName());
    }

    public void delete() {
        setDeleted(true);
    }

    public static Optional<CisDrugLimit> getCisDrugLimitById(String id) {
		return dao().findById(id);
	}

	public static List<CisDrugLimit> getCisDrugLimits(CisDrugLimitQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
	}

	public static Page<CisDrugLimit> getCisDrugLimitPage(CisDrugLimitQto qto) {
		
		return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
	}


	public static CisDrugLimit newInstanceByNto(CisDrugLimitNto cisDrugLimitNto) {
		try {
			return (CisDrugLimit) Class.forName("com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity."
					+ StringUtils.removeEnd(cisDrugLimitNto.getClass().getSimpleName(), "Nto")).getConstructor().newInstance();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * @generated
	 */
    private static Specification<CisDrugLimit> getSpecification(CisDrugLimitQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
        	if(StringUtils.isNotBlank(qto.getDrugCode())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugCode"), qto.getDrugCode()));
        	}
        	if(StringUtils.isNotBlank(qto.getDrugName())) {
				predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugName"), qto.getDrugName()));
        	}
    		predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));
	
            return predicate;
        };
    }

    private static CisDrugLimitRepository dao() {
		return SpringUtil.getBean(CisDrugLimitRepository.class);
	}


    public static List<CisDrugLimit> findCisDrugLimitsByDrugCodeInAndDeletedFalse(List<String> drugCodes) {
    	return dao().findCisDrugLimitsByDrugCodeInAndDeletedFalse(drugCodes);
    }
}
