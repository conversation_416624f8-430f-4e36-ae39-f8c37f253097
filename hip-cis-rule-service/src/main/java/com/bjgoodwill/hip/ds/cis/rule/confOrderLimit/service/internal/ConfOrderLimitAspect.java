package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service.internal;

import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to.CheckCisConfOrderLimitQto;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-08-12 16:35
 * @className: confOrderLimitAspect
 * @description:
 **/
@Aspect
@Component
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class ConfOrderLimitAspect {
    @Autowired
    private CisConfOrderLimitServiceImpl cisConfOrderLimitServiceImpl;

    @Pointcut("@annotation(com.bjgoodwill.hip.business.util.cis.validation.ConOrderLimit)")
    public void confOrderLimit() {
    }

    @Before(value = "confOrderLimit()")
    public void logAspect(JoinPoint joinPoint) {
        System.out.println(joinPoint.getSignature().getName() + "收到的参数为" + Arrays.toString(joinPoint.getArgs()));
        Object[] args = joinPoint.getArgs();
        if (args[0] != null) {
            cisConfOrderLimitServiceImpl.checkDocDetials((List<CheckCisConfOrderLimitQto>) args[0]);
        }
    }
}