package com.bjgoodwill.hip.ds.cis.rule.drugauth.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.entity.DoctDrugAuthority;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.to.DoctDrugAuthorityTo;

import java.util.ArrayList;
import java.util.List;

public abstract class DoctDrugAuthorityAssembler {

    public static List<DoctDrugAuthorityTo> toTos(List<DoctDrugAuthority> doctDrugAuthoritys) {
        return toTos(doctDrugAuthoritys, false);
    }

    public static List<DoctDrugAuthorityTo> toTos(List<DoctDrugAuthority> doctDrugAuthoritys, boolean withAllParts) {
        BusinessAssert.notNull(doctDrugAuthoritys, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "参数doctDrugAuthoritys");

        List<DoctDrugAuthorityTo> tos = new ArrayList<>();
        for (DoctDrugAuthority doctDrugAuthority : doctDrugAuthoritys)
            tos.add(toTo(doctDrugAuthority, withAllParts));
        return tos;
    }

    public static DoctDrugAuthorityTo toTo(DoctDrugAuthority doctDrugAuthority) {
        return toTo(doctDrugAuthority, false);
    }

    /**
     * @generated
     */
    public static DoctDrugAuthorityTo toTo(DoctDrugAuthority doctDrugAuthority, boolean withAllParts) {
        if (doctDrugAuthority == null)
            return null;
        DoctDrugAuthorityTo to = new DoctDrugAuthorityTo();
        to.setId(doctDrugAuthority.getId());
        to.setStaffId(doctDrugAuthority.getStaffId());
        to.setChineseHerbFlag(doctDrugAuthority.getChineseHerbFlag());
        to.setChinesePatentFlag(doctDrugAuthority.getChinesePatentFlag());
        to.setCanPresc(doctDrugAuthority.getCanPresc());
        to.setCanSurgeryLevel(doctDrugAuthority.getCanSurgeryLevel());
        to.setIsRemoteConsultation(doctDrugAuthority.getIsRemoteConsultation());
        to.setAntiTrainingFlag(doctDrugAuthority.getAntiTrainingFlag());
        to.setAntiTrainingLevel(doctDrugAuthority.getAntiTrainingLevel());
        to.setToxiPropertyNames(doctDrugAuthority.getToxiPropertyNames());
        to.setCanValuableMedicineFlag(doctDrugAuthority.getCanValuableMedicineFlag());
        to.setCanAntineoplasticFlag(doctDrugAuthority.getCanAntineoplasticFlag());
        to.setAntineoplasticLevel(doctDrugAuthority.getAntineoplasticLevel());
        to.setDeleted(doctDrugAuthority.isDeleted());
        to.setCreatedStaff(doctDrugAuthority.getCreatedStaff());
        to.setCreatedStaffName(doctDrugAuthority.getCreatedStaffName());
        to.setCreatedDate(doctDrugAuthority.getCreatedDate());
        to.setUpdatedStaff(doctDrugAuthority.getUpdatedStaff());
        to.setUpdatedStaffName(doctDrugAuthority.getUpdatedStaffName());
        to.setUpdatedDate(doctDrugAuthority.getUpdatedDate());
        to.setStaffName(doctDrugAuthority.getStaffName());
        to.setAdministrativeDept(doctDrugAuthority.getAdministrativeDept());
        to.setAdministrativeDeptName(doctDrugAuthority.getAdministrativeDeptName());
        to.setPhysicianCertificate(doctDrugAuthority.getPhysicianCertificate());
        to.setVersion(doctDrugAuthority.getVersion());
        if (withAllParts) {
        }
        return to;
    }

}