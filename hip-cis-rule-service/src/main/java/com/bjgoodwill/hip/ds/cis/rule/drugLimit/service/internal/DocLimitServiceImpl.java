package com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity.DocLimit;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.DocLimitService;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.internal.assembler.DocLimitAssembler;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.DocLimitEto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.DocLimitNto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.DocLimitQto;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.DocLimitTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.DocLimitService")
@RequestMapping(value = "/api/rule/drugLimit/docLimit", produces = "application/json; charset=utf-8")
public class DocLimitServiceImpl implements DocLimitService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<DocLimitTo> getDocLimits(DocLimitQto docLimitQto) {
        return DocLimitAssembler.toTos(DocLimit.getDocLimits(docLimitQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<DocLimitTo> getDocLimitPage(DocLimitQto docLimitQto) {
        Page<DocLimit> page = DocLimit.getDocLimitPage(docLimitQto);
        Page<DocLimitTo> result = page.map(DocLimitAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public DocLimitTo getDocLimitById(String id) {
        return DocLimitAssembler.toTo(DocLimit.getDocLimitById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public DocLimitTo createDocLimit(DocLimitNto docLimitNto) {
        DocLimitQto qto = new DocLimitQto();
        qto.setDocCode(docLimitNto.getDocCode());
        qto.setDrugCode(docLimitNto.getDrugCode());
        List<DocLimit> docLimits = DocLimit.getDocLimits(qto);
        BusinessAssert.isEmpty(docLimits, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0007, "该人员已经存在药品限制");
        DocLimit docLimit = new DocLimit();
		docLimit = docLimit.create(docLimitNto);
		return DocLimitAssembler.toTo(docLimit);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateDocLimit(String id, DocLimitEto docLimitEto) {
        DocLimitQto qto = new DocLimitQto();
        qto.setDocCode(docLimitEto.getDocCode());
        qto.setDrugCode(docLimitEto.getDrugCode());
        List<DocLimit> docLimits = DocLimit.getDocLimits(qto).stream().filter(docLimit -> !docLimit.getId().equals(id)).toList();
        BusinessAssert.isEmpty(docLimits, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0007, "该人员已经存在药品限制");
        Optional<DocLimit> docLimitOptional = DocLimit.getDocLimitById(id);
		docLimitOptional.ifPresent(docLimit -> docLimit.update(docLimitEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteDocLimit(String id) {
        Optional<DocLimit> docLimitOptional = DocLimit.getDocLimitById(id);
		docLimitOptional.ifPresent(docLimit -> docLimit.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<DocLimitTo> getDocLimitByDrugCodes(List<String> drugCodes) {
        return  DocLimitAssembler.toTos(DocLimit.getDocLimitsByDrugCodes(drugCodes));
    }


    @InitBinder
	public void initBinder(WebDataBinder binder) {
	}



}