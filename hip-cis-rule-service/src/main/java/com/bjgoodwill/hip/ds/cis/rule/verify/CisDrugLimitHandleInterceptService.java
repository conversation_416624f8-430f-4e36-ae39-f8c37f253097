package com.bjgoodwill.hip.ds.cis.rule.verify;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.cis.util.DoctAuthCommonQto;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.DocLimitService;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.OrgLimitService;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.DocLimitTo;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.OrgLimitTo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-12-16 14:02
 * @className: CisDrugLimitHandleInterceptService
 * @description: 药品限制
 **/
@Order(2)
@Component
public class CisDrugLimitHandleInterceptService extends AbstractOrderHandle {

    @Autowired
    private DocLimitService docLimitService;

    @Autowired
    private OrgLimitService orgLimitService;

    /**
     * 核实订单信息
     * 此方法主要用于核实和处理一批订单的药物限制条件，包括机构限制和医生限制
     * @param qtos 一个包含多个订单信息的列表
     * @return 返回处理后的订单列表，如果订单列表为空或所有订单都不需要核实，则直接返回原始列表
     */
    @Override
    public List<DoctAuthCommonQto> verifyOrders(List<DoctAuthCommonQto> qtos) {

        // 检查输入的订单列表是否为空，如果为空，则直接返回
        if(CollectionUtils.isEmpty(qtos)){
            return qtos;
        }

        // 提取需要核实的药物编码，仅考虑订单类别为电子药物的订单，并去重
        Set<String> drugCodes = qtos.stream().filter(qto -> qto.getOrderClass().equals(SystemTypeEnum.EDRUG))
                .map(DoctAuthCommonQto::getServiceItemCode).collect(Collectors.toSet());

        // 如果没有需要核实的药物编码，则直接返回订单列表
        if(CollectionUtils.isEmpty(drugCodes)){
            return qtos;
        }

        // 获取机构编码，用于后续的机构限制检查
        String orgCode = qtos.get(0).getOrgCode();

        // 根据药物编码获取机构限制信息
        List<OrgLimitTo> orgLimitTos = orgLimitService.getOrgLimitByDrugCodes(drugCodes.stream().toList());

        // 检查机构的药物限制，并获取需要进一步检查的药物编码列表
        List<String> needCheckDrugCodes = checkOrgDrugLimit(orgLimitTos, orgCode);

        // 根据需要进一步检查的药物编码获取医生限制信息
        List<DocLimitTo> docLimitTos = docLimitService.getDocLimitByDrugCodes(needCheckDrugCodes);

        // 检查医生的药物限制，这里使用了HIPLoginUtil.getStaffId()来获取当前登录的员工ID
        checkDocDrugLimit(docLimitTos, HIPLoginUtil.getStaffId());

        // 最后返回处理后的订单列表
        return qtos;
    }


    /**
     * 根据机构限制信息列表和机构代码，检查并返回不受限的药品代码列表
     * 此方法首先将输入的机构限制信息列表转换为一个映射，其中药品代码为键，对应的机构限制信息列表为值
     * 然后，它过滤出那些在指定机构代码下没有限制的药品代码，并返回这些药品代码的列表
     *
     * @param orgLimitTos 机构限制信息列表，包含各个机构的药品限制信息
     * @param orgCode 机构代码，用于标识需要检查的机构
     * @return 返回一个字符串列表，包含不受限的药品代码
     */
    private List<String> checkOrgDrugLimit(List<OrgLimitTo> orgLimitTos, String orgCode) {
        // 将机构限制信息列表转换为映射，按药品代码分组
        Map<String, List<OrgLimitTo>> orgLimitMap = orgLimitTos.stream()
                .collect(Collectors.groupingBy(OrgLimitTo::getDrugCode));

        // 过滤出在指定机构代码下没有限制的药品代码，并返回这些药品代码的列表
        return orgLimitMap.entrySet().stream()
                .filter(entry -> entry.getValue().stream().noneMatch(limit -> orgCode.equals(limit.getOrgCode())))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 检查医生药品限制
     * 该方法用于验证当前医生是否有权限使用特定的药品，通过比较医生代码和药品限制中的医生代码来确定
     *
     * @param docLimitTos 包含药品限制信息的列表，用于检查医生是否有限制使用某些药品
     * @param docCode 当前登录医生的代码，用于识别医生身份
     */
    private void checkDocDrugLimit(List<DocLimitTo> docLimitTos, String docCode) {
        // 将药品限制信息按药品代码分组，以便后续检查
        Map<String, List<DocLimitTo>> docLimitMap = docLimitTos.stream()
                .collect(Collectors.groupingBy(DocLimitTo::getDrugCode));

        // 遍历分组后的药品限制信息，检查当前医生是否有限制使用某些药品
        for (Map.Entry<String, List<DocLimitTo>> entry : docLimitMap.entrySet()) {
            List<DocLimitTo> limits = entry.getValue();
            // 检查当前医生是否在限制使用的药品列表中
            boolean hasLimit = limits.stream().anyMatch(limit -> docCode.equals(limit.getDocCode()));
            String drugName = limits.get(0).getDrugName();
            // 如果当前医生在限制使用的药品列表中，则抛出异常，提示医生无权使用该药品
            BusinessAssert.isTrue(hasLimit, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0007,"医生【" + HIPLoginUtil.getLoginUserName() + "】"
            ,drugName + "【" + entry.getKey() + "】");
        }
    }
}