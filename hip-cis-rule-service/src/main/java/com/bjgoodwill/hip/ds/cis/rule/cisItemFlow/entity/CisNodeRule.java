package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.repository.CisNodeRuleRepository;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to.CisNodeRuleEto;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to.CisNodeRuleNto;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to.CisNodeRuleQto;
import com.bjgoodwill.hip.jpa.core.SnowflakeIdGenerator;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "节点规则")
@Table(name = "cis_node_rule", indexes = {@Index(name = "IDX_cisFlowNodeSubId", columnList = "cisFlowNodeSubId")}, uniqueConstraints = {})
public class CisNodeRule {

    // 标识
    private String id;
    // 医嘱节点从表标识
    private String cisFlowNodeSubId;
    // 顺序
    private Double sequence;
    // 限制方法路径
    private String ruleMethodName;
    // 限制方法路径
    private String ruleMethodPath;
    // 限制方法说明
    private String remark;
    // 参数
    private String parameter;
    // 已启用
    private boolean enabled;
    // 多医嘱项目一起调用
    private String isMulTypesUse;
    // 创建的人员
    private String createdStaff;
    // 创建的人员名称
    private String createdStaffName;
    // 创建的时间
    private LocalDateTime createdDate;
    // 最后修改的时间
    private LocalDateTime updatedDate;

    // 根据ID获取节点规则记录
    // 参数id：节点规则的ID
    // 返回值：返回找到的节点规则实例，如果未找到则返回Optional.empty()
    public static Optional<CisNodeRule> getNodeRuleById(String id) {
        return dao().findById(id);
    }

    // 根据流程节点子ID列表获取节点规则列表
    // 参数cisFlowNodeSubIds：流程节点子ID列表
    // 返回值：返回找到的节点规则列表
    public static List<CisNodeRule> getCisNodeRulesByCisFlowNodeIds(List<String> cisFlowNodeSubIds) {
        return dao().findAll(getSpecificationByIds(cisFlowNodeSubIds));
    }

    // 创建根据流程节点子ID列表查询节点规则的规格说明
    // 参数ids：流程节点子ID列表
    // 返回值：返回查询规格说明
    private static Specification<CisNodeRule> getSpecificationByIds(List<String> ids) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            CriteriaBuilder.In<Object> in = criteriaBuilder.in(root.get("cisFlowNodeSubId"));
            ids.forEach(in::value);
            predicate = criteriaBuilder.and(criteriaBuilder.and(in));
            return predicate;
        };
    }

    // 根据条件获取节点规则列表
    // 参数cisFlowNodeSubId：流程节点子ID
    // 参数qto：查询传输对象，包含查询条件
    // 返回值：返回找到的节点规则列表
    public static List<CisNodeRule> getNodeRules(String cisFlowNodeSubId, CisNodeRuleQto qto) {
        if (cisFlowNodeSubId != null) {
            qto.setCisFlowNodeSubId(cisFlowNodeSubId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    // 根据条件分页获取节点规则列表
    // 参数cisFlowNodeSubId：流程节点子ID
    // 参数qto：查询传输对象，包含查询条件
    // 返回值：返回分页的节点规则列表
    public static Page<CisNodeRule> getNodeRulePage(String cisFlowNodeSubId, CisNodeRuleQto qto) {
        if (cisFlowNodeSubId != null) {
            qto.setCisFlowNodeSubId(cisFlowNodeSubId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    // 根据流程节点子ID获取节点规则列表
    // 参数cisFlowNodeSubId：流程节点子ID
    // 返回值：返回找到的节点规则列表
    public static List<CisNodeRule> getByCisFlowNodeSubId(String cisFlowNodeSubId) {
        return dao().findByCisFlowNodeSubId(cisFlowNodeSubId);
    }

    // 根据流程节点子ID删除节点规则记录
    // 参数cisFlowNodeSubId：流程节点子ID
    public static void deleteByCisFlowNodeSubId(String cisFlowNodeSubId) {
        dao().deleteByCisFlowNodeSubId(cisFlowNodeSubId);
    }

    /**
     * 创建根据条件查询节点规则的规格说明
     * 参数qto：查询传输对象，包含查询条件
     * 返回值：返回查询规格说明
     */
    private static Specification<CisNodeRule> getSpecification(CisNodeRuleQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getCisFlowNodeSubId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisFlowNodeSubId"), qto.getCisFlowNodeSubId()));
            }
            if (qto.getSequence() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sequence"), qto.getSequence()));
            }
            if (StringUtils.isNotBlank(qto.getRuleMethodName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("ruleMethodName"), qto.getRuleMethodName()));
            }
            if (StringUtils.isNotBlank(qto.getRuleMethodPath())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("ruleMethodPath"), qto.getRuleMethodPath()));
            }
            if (StringUtils.isNotBlank(qto.getRemark())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("remark"), qto.getRemark()));
            }
            if (StringUtils.isNotBlank(qto.getParameter())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("parameter"), qto.getParameter()));
            }
            if (qto.getEnabled() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("enabled"), qto.getEnabled()));
            }
            if (StringUtils.isNotBlank(qto.getIsMulTypesUse())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isMulTypesUse"), qto.getIsMulTypesUse()));
            }
            return predicate;
        };
    }

    // 获取CisNodeRuleRepository实例
    // 返回值：返回CisNodeRuleRepository实例
    private static CisNodeRuleRepository dao() {
        return SpringUtil.getBean(CisNodeRuleRepository.class);
    }

    @Id
    @GeneratedValue(generator = "snowflake_generator")
    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("医嘱节点从表标识")
    @Column(name = "cis_flow_node_sub_id", nullable = false, length = 50)
    public String getCisFlowNodeSubId() {
        return cisFlowNodeSubId;
    }

    protected void setCisFlowNodeSubId(String cisFlowNodeSubId) {
        this.cisFlowNodeSubId = cisFlowNodeSubId;
    }

    @Comment("顺序")
    @Column(name = "sequence", nullable = false, unique = true)
    public Double getSequence() {
        return sequence;
    }

    protected void setSequence(Double sequence) {
        this.sequence = sequence;
    }

    @Comment("限制方法路径")
    @Column(name = "rule_method_name", nullable = true)
    public String getRuleMethodName() {
        return ruleMethodName;
    }

    protected void setRuleMethodName(String ruleMethodName) {
        this.ruleMethodName = ruleMethodName;
    }

    @Comment("限制方法路径")
    @Column(name = "rule_method_path", nullable = true)
    public String getRuleMethodPath() {
        return ruleMethodPath;
    }

    protected void setRuleMethodPath(String ruleMethodPath) {
        this.ruleMethodPath = ruleMethodPath;
    }

    @Comment("限制方法说明")
    @Column(name = "remark", nullable = true)
    public String getRemark() {
        return remark;
    }

    protected void setRemark(String remark) {
        this.remark = remark;
    }

    @Comment("参数")
    @Column(name = "parameter", nullable = true)
    public String getParameter() {
        return parameter;
    }

    protected void setParameter(String parameter) {
        this.parameter = parameter;
    }

    @Comment("已启用")
    @Column(name = "enabled", nullable = false)
    public boolean isEnabled() {
        return enabled;
    }

    protected void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Comment("多医嘱项目一起调用")
    @Column(name = "is_mul_types_use", nullable = true)
    public String getIsMulTypesUse() {
        return isMulTypesUse;
    }

    protected void setIsMulTypesUse(String isMulTypesUse) {
        this.isMulTypesUse = isMulTypesUse;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建人员名称")
    @Column(name = "created_staff_name", nullable = true)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Transient
    public CisFlowNodeSub getCisFlowNodeSub() {
        return CisFlowNodeSub.getCisFlowNodeSubById(getCisFlowNodeSubId()).orElse(null);
    }

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisNodeRule other = (CisNodeRule) obj;
        return Objects.equals(id, other.id);
    }

    // 创建节点规则记录
    // 参数cisFlowNodeSubId：流程节点子ID
    // 参数nodeRuleNto：节点规则传输对象，包含要创建的节点规则的信息
    // 返回值：返回创建的节点规则实例
    public CisNodeRule create(String cisFlowNodeSubId, CisNodeRuleNto nodeRuleNto) {
        // 校验nodeRuleNto参数不能为空
        BusinessAssert.notNull(nodeRuleNto, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "nodeRuleNto");
        // 校验序列号不能重复
        BusinessAssert.isTrue(!dao().existsBySequence(nodeRuleNto.getSequence()), CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, nodeRuleNto.getSequence());

        setCisFlowNodeSubId(cisFlowNodeSubId);

        setSequence(nodeRuleNto.getSequence());
        setRuleMethodName(nodeRuleNto.getRuleMethodName());
        setRuleMethodPath(nodeRuleNto.getRuleMethodPath());
        setRemark(nodeRuleNto.getRemark());
        setParameter(nodeRuleNto.getParameter());
        setEnabled(true);
        setIsMulTypesUse(nodeRuleNto.getIsMulTypesUse());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        setUpdatedDate(LocalDateUtil.now());
        dao().save(this);
        return this;
    }

    // 更新节点规则记录
    // 参数nodeRuleEto：节点规则扩展传输对象，包含要更新的节点规则的信息
    public void update(CisNodeRuleEto nodeRuleEto) {
        // 校验序列号不能重复
        BusinessAssert.isTrue(!dao().existsBySequence(nodeRuleEto.getSequence()), CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, nodeRuleEto.getSequence());

        setSequence(nodeRuleEto.getSequence());
        setRuleMethodName(nodeRuleEto.getRuleMethodName());
        setRuleMethodPath(nodeRuleEto.getRuleMethodPath());
        setRemark(nodeRuleEto.getRemark());
        setParameter(nodeRuleEto.getParameter());
        setEnabled(nodeRuleEto.isEnabled());
        setIsMulTypesUse(nodeRuleEto.getIsMulTypesUse());
        setUpdatedDate(LocalDateUtil.now());
    }

    // 启用节点规则
    public void enable() {
        setEnabled(true);
    }

    // 禁用节点规则
    public void disable() {
        setEnabled(false);
    }

    // 删除节点规则记录
    public void delete() {
        dao().delete(this);
    }
}

