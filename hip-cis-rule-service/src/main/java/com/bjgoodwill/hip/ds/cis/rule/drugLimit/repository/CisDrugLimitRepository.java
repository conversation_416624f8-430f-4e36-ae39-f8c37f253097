package com.bjgoodwill.hip.ds.cis.rule.drugLimit.repository;

import com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity.CisDrugLimit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.rule.drugLimit.repository.CisDrugLimitRepository")
public interface CisDrugLimitRepository extends JpaRepository<CisDrugLimit, String>, JpaSpecificationExecutor<CisDrugLimit> {
    List<CisDrugLimit> findCisDrugLimitsByDrugCodeInAndDeletedFalse(List<String> drugCodes);
}