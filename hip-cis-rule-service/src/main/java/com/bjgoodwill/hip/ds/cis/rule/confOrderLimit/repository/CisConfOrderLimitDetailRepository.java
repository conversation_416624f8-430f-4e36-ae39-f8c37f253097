package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.repository;

import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.entity.CisConfOrderLimitDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.repository.CisConfOrderLimitDetailRepository")
public interface CisConfOrderLimitDetailRepository extends JpaRepository<CisConfOrderLimitDetail, String>, JpaSpecificationExecutor<CisConfOrderLimitDetail> {

    List<CisConfOrderLimitDetail> findByCisConfOrderLimitId(String cisConfOrderLimitId);

    //	Page<CisConfOrderLimitDetail> findByCisConfOrderLimitId(String cisConfOrderLimitId, Pageable pageable);
//	boolean existsByCisConfOrderLimitId(String cisConfOrderLimitId);
    void deleteByCisConfOrderLimitId(String cisConfOrderLimitId);
//	List<CisConfOrderLimitDetail> findByCisConfOrderLimitRuleId(String cisConfOrderLimitRuleId);
//    Page<CisConfOrderLimitDetail> findByCisConfOrderLimitRuleId(String cisConfOrderLimitRuleId, Pageable pageable);
//    boolean existsByCisConfOrderLimitRuleId(String cisConfOrderLimitRuleId);
//    void deleteByCisConfOrderLimitRuleId(String cisConfOrderLimitRuleId);

}