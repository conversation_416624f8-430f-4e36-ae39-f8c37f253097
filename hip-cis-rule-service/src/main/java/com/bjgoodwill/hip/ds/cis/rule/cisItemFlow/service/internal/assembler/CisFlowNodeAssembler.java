package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.entity.CisFlowNode;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to.CisFlowNodeTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisFlowNodeAssembler {

    public static List<CisFlowNodeTo> toTos(List<CisFlowNode> cisFlowNodes) {
        return toTos(cisFlowNodes, false);
    }

    public static List<CisFlowNodeTo> toTos(List<CisFlowNode> cisFlowNodes, boolean withAllParts) {
        BusinessAssert.notNull(cisFlowNodes, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "参数cisFlowNodes");

        List<CisFlowNodeTo> tos = new ArrayList<>();
        for (CisFlowNode cisFlowNode : cisFlowNodes)
            tos.add(toTo(cisFlowNode, withAllParts));
        return tos;
    }

    public static CisFlowNodeTo toTo(CisFlowNode cisFlowNode) {
        return toTo(cisFlowNode, false);
    }

    /**
     * @generated
     */
    public static CisFlowNodeTo toTo(CisFlowNode cisFlowNode, boolean withAllParts) {
        if (cisFlowNode == null)
            return null;
        CisFlowNodeTo to = new CisFlowNodeTo();
        to.setId(cisFlowNode.getId());
        to.setNodeName(cisFlowNode.getNodeName());
        to.setMethodName(cisFlowNode.getMethodName());
        to.setMethodClassName(cisFlowNode.getMethodClassName());
        to.setSequence(cisFlowNode.getSequence());
        to.setIsFixed(cisFlowNode.getIsFixed());
        to.setEnabled(cisFlowNode.isEnabled());
        to.setIsCanCancel(cisFlowNode.getIsCanCancel());
        to.setIsCanRefund(cisFlowNode.getIsCanRefund());
        to.setCreatedStaff(cisFlowNode.getCreatedStaff());
        to.setCreatedStaffName(cisFlowNode.getCreatedStaffName());
        to.setCreatedDate(cisFlowNode.getCreatedDate());

        if (withAllParts) {
            to.setCisFlowNodeSubs(CisFlowNodeSubAssembler.toTos(cisFlowNode.getCisFlowNodeSubs()));
        }
        return to;
    }

}