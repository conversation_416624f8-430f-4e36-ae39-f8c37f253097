package com.bjgoodwill.hip.ds.cis.rule.exclusion.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.entity.ItemExclusionRule;
import com.bjgoodwill.hip.ds.cis.rule.exclusion.to.ItemExclusionRuleTo;

import java.util.ArrayList;
import java.util.List;

public abstract class ItemExclusionRuleAssembler {

    public static List<ItemExclusionRuleTo> toTos(List<ItemExclusionRule> itemExclusionRules) {
		return toTos(itemExclusionRules, false);
	}

	public static List<ItemExclusionRuleTo> toTos(List<ItemExclusionRule> itemExclusionRules, boolean withAllParts) {
		BusinessAssert.notNull(itemExclusionRules, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001,"参数itemExclusionRules");

		List<ItemExclusionRuleTo> tos = new ArrayList<>();
		for (ItemExclusionRule itemExclusionRule : itemExclusionRules)
			tos.add(toTo(itemExclusionRule, withAllParts));
		return tos;
	}

	public static ItemExclusionRuleTo toTo(ItemExclusionRule itemExclusionRule) {
		return toTo(itemExclusionRule, false);
	}

	/**
	 * @generated
	 */
	public static ItemExclusionRuleTo toTo(ItemExclusionRule itemExclusionRule, boolean withAllParts) {
		if (itemExclusionRule == null)
			return null;
		ItemExclusionRuleTo to = new ItemExclusionRuleTo();
        to.setId(itemExclusionRule.getId());
        to.setRuleType(itemExclusionRule.getRuleType());
        to.setItemCode(itemExclusionRule.getItemCode());
        to.setItemName(itemExclusionRule.getItemName());
        to.setRuleDetailTimes(itemExclusionRule.getRuleDetailTimes());
        to.setRuleDetailDays(itemExclusionRule.getRuleDetailDays());
        to.setHospitalModel(itemExclusionRule.getHospitalModel());
        to.setLimitType(itemExclusionRule.getLimitType());
        to.setEnabled(itemExclusionRule.isEnabled());
        to.setDeleted(itemExclusionRule.isDeleted());
        to.setCreatedStaff(itemExclusionRule.getCreatedStaff());
        to.setCreatedStaffName(itemExclusionRule.getCreatedStaffName());
        to.setCreatedDate(itemExclusionRule.getCreatedDate());
        to.setUpdatedStaff(itemExclusionRule.getUpdatedStaff());
        to.setUpdatedStaffName(itemExclusionRule.getUpdatedStaffName());
        to.setUpdatedDate(itemExclusionRule.getUpdatedDate());
        to.setVersion(itemExclusionRule.getVersion());

		if (withAllParts) {
		}
		return to;
	}

}