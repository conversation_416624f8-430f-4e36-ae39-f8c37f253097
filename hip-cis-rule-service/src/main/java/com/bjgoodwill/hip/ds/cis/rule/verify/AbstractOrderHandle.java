package com.bjgoodwill.hip.ds.cis.rule.verify;

import com.bjgoodwill.hip.business.util.cis.util.DoctAuthCommonQto;

import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-12-16 14:26
 * @className: AbstractOrderHandle
 * @description:
 **/
public abstract class AbstractOrderHandle {

    /**
     * 责任链，下一个链接节点
     */
    private AbstractOrderHandle next;

    /**
     * 执行入口
     *
     * @param qtos
     * @return
     */
    public List<DoctAuthCommonQto> execute(List<DoctAuthCommonQto> qtos) {
        qtos = verifyOrders(qtos);
        // 判断是否还有下个责任链节点，没有的话，说明已经是最后一个节点
        if (getNext() != null) {
            getNext().execute(qtos);
        }
        return qtos;
    }

    /**
     * 对参数进行处理
     *
     * @param
     * @return
     */
    public abstract List<DoctAuthCommonQto> verifyOrders(List<DoctAuthCommonQto> qtos);


    public AbstractOrderHandle getNext() {
        return next;
    }

    public void setNext(AbstractOrderHandle next) {
        this.next = next;
    }
}