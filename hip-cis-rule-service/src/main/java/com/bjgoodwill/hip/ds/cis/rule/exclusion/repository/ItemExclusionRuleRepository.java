package com.bjgoodwill.hip.ds.cis.rule.exclusion.repository;

import com.bjgoodwill.hip.ds.cis.rule.exclusion.entity.ItemExclusionRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.rule.exclusion.repository.ItemExclusionRuleRepository")
public interface ItemExclusionRuleRepository extends JpaRepository<ItemExclusionRule, String>, JpaSpecificationExecutor<ItemExclusionRule> {

}