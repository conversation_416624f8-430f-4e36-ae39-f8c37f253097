package com.bjgoodwill.hip.ds.cis.rule.drugLimit.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.entity.OrgLimit;
import com.bjgoodwill.hip.ds.cis.rule.drugLimit.to.OrgLimitTo;

import java.util.ArrayList;
import java.util.List;

public abstract class OrgLimitAssembler {

    public static List<OrgLimitTo> toTos(List<OrgLimit> orgLimits) {
        return toTos(orgLimits, false);
    }

    public static List<OrgLimitTo> toTos(List<OrgLimit> orgLimits, boolean withAllParts) {
        BusinessAssert.notNull(orgLimits, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "参数orgLimits");

        List<OrgLimitTo> tos = new ArrayList<>();
        for (OrgLimit orgLimit : orgLimits)
            tos.add(toTo(orgLimit, withAllParts));
        return tos;
    }

    public static OrgLimitTo toTo(OrgLimit orgLimit) {
        return toTo(orgLimit, false);
    }

    /**
     * @generated
     */
    public static OrgLimitTo toTo(OrgLimit orgLimit, boolean withAllParts) {
        if (orgLimit == null)
            return null;
        OrgLimitTo to = new OrgLimitTo();
        to.setId(orgLimit.getId());
        to.setDrugCode(orgLimit.getDrugCode());
        to.setDrugName(orgLimit.getDrugName());
        to.setDeleted(orgLimit.isDeleted());
        to.setCreatedStaff(orgLimit.getCreatedStaff());
        to.setCreatedStaffName(orgLimit.getCreatedStaffName());
        to.setCreatedDate(orgLimit.getCreatedDate());
        to.setUpdatedStaff(orgLimit.getUpdatedStaff());
        to.setUpdatedStaffName(orgLimit.getUpdatedStaffName());
        to.setUpdatedDate(orgLimit.getUpdatedDate());
        to.setOrgCode(orgLimit.getOrgCode());
        to.setOrgName(orgLimit.getOrgName());
        to.setHospitalCode(orgLimit.getHospitalCode());
        to.setHospitalName(orgLimit.getHospitalName());
        to.setWorkGroupTypeCode(orgLimit.getWorkGroupTypeCode());
        to.setWorkGroupTypeName(orgLimit.getWorkGroupTypeName());

        if (withAllParts) {
        }
        return to;
    }

}