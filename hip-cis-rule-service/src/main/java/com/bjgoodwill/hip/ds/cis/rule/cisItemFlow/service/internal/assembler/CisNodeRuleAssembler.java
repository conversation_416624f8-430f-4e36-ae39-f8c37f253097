package com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.entity.CisNodeRule;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.to.CisNodeRuleTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisNodeRuleAssembler {

    public static List<CisNodeRuleTo> toTos(List<CisNodeRule> nodeRules) {
        return toTos(nodeRules, false);
    }

    public static List<CisNodeRuleTo> toTos(List<CisNodeRule> nodeRules, boolean withAllParts) {
        BusinessAssert.notNull(nodeRules, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "参数nodeRules");

        List<CisNodeRuleTo> tos = new ArrayList<>();
        for (CisNodeRule nodeRule : nodeRules)
            tos.add(toTo(nodeRule, withAllParts));
        return tos;
    }

    public static CisNodeRuleTo toTo(CisNodeRule nodeRule) {
        return toTo(nodeRule, false);
    }

    /**
     * @generated
     */
    public static CisNodeRuleTo toTo(CisNodeRule nodeRule, boolean withAllParts) {
        if (nodeRule == null)
            return null;
        CisNodeRuleTo to = new CisNodeRuleTo();
        to.setId(nodeRule.getId());
        to.setCisFlowNodeSubId(nodeRule.getCisFlowNodeSubId());
        to.setSequence(nodeRule.getSequence());
        to.setRuleMethodName(nodeRule.getRuleMethodName());
        to.setRuleMethodPath(nodeRule.getRuleMethodPath());
        to.setRemark(nodeRule.getRemark());
        to.setParameter(nodeRule.getParameter());
        to.setEnabled(nodeRule.isEnabled());
        to.setIsMulTypesUse(nodeRule.getIsMulTypesUse());
        to.setCreatedStaff(nodeRule.getCreatedStaff());
        to.setCreatedStaffName(nodeRule.getCreatedStaffName());
        to.setCreatedDate(nodeRule.getCreatedDate());

        if (withAllParts) {
        }
        return to;
    }

}