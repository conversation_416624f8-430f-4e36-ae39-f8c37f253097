package com.bjgoodwill.hip.ds.cis.rule.verify;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.util.DoctAuthCommonQto;
import com.bjgoodwill.hip.ds.cis.rule.proxy.EconServicePriceServiceProxy;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-12-25 14:43
 * @className: EconServicePriceHandleInterceptService
 * @description:
 **/
@Order(4)
@Component
public class EconServicePriceHandleInterceptService extends AbstractOrderHandle{

    private EconServicePriceServiceProxy econServicePriceServiceProxy;

    public EconServicePriceServiceProxy getEconServicePriceServiceProxy() {
        if(econServicePriceServiceProxy == null){
            econServicePriceServiceProxy = SpringUtil.getBean(EconServicePriceServiceProxy.class);
        }
        return econServicePriceServiceProxy;
    }

    @Override
    public List<DoctAuthCommonQto> verifyOrders(List<DoctAuthCommonQto> qtos) {

        if(CollectionUtils.isEmpty(qtos)){
            return qtos;
        }
        Map<String, String> map = qtos.stream()
                .flatMap(p -> {
                    Map<String, String> priceMap = p.getPriceMap();
                    return priceMap != null ? priceMap.entrySet().stream() : Stream.empty();
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing // 处理键冲突：保留第一个遇到的值
                ));
        if (map.isEmpty()) return qtos;
        getEconServicePriceServiceProxy().verifyPriceValid(map);
        return qtos;
    }
}