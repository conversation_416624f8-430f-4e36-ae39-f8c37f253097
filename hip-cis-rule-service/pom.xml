<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
        <groupId>com.bjgoodwill.hip</groupId>
        <artifactId>hip-parent</artifactId>
        <version>5.0-SNAPSHOT</version>
    </parent>

    <artifactId>hip-cis-rule-service</artifactId>
	<version>5.0-SNAPSHOT</version>
	<name>hip-cis-rule-service</name>

	<properties>
        <hip_cis-rule_api.version>5.0-SNAPSHOT</hip_cis-rule_api.version>
		<hip-drug-goods-api.version>5.0-SNAPSHOT</hip-drug-goods-api.version>
		<hip-base-cis-diagnose-api.version>5.0-SNAPSHOT</hip-base-cis-diagnose-api.version>
		<hip-base-cis-medicineitem-api.version>5.0-SNAPSHOT</hip-base-cis-medicineitem-api.version>
		<hip-cis-cdr-api.version>5.0-SNAPSHOT</hip-cis-cdr-api.version>
		<hip-econ-price-api.version>5.0-SNAPSHOT</hip-econ-price-api.version>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
	</properties>

	<dependencies>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-jpa</artifactId>
        </dependency>
		<dependency>
			<groupId>com.bjgoodwill.hip</groupId>
			<artifactId>hip-cis-rule-api</artifactId>
			<version>${hip_cis-rule_api.version}</version>
		</dependency>
		<dependency>
			<groupId>com.bjgoodwill.hip</groupId>
			<artifactId>hip-business-util</artifactId>
			<version>5.0-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-goods-api</artifactId>
			<version>${hip-drug-goods-api.version}</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>com.bjgoodwill.hip</groupId>
			<artifactId>hip-base-cis-diagnose-api</artifactId>
			<version>${hip-base-cis-diagnose-api.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>21.0</version>
		</dependency>
		<dependency>
			<groupId>com.bjgoodwill.hip</groupId>
			<artifactId>hip-base-cis-medicineitem-api</artifactId>
			<version>${hip-base-cis-medicineitem-api.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.bjgoodwill.hip</groupId>
			<artifactId>hip-cis-cdr-api</artifactId>
			<version>${hip-cis-cdr-api.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.bjgoodwill.hip</groupId>
			<artifactId>hip-common</artifactId>
		</dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-price-api</artifactId>
            <version>${hip-econ-price-api.version}</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>com.bjgoodwill.hip</groupId>
			<artifactId>hip-common</artifactId>
		</dependency>
	</dependencies>

</project>
