    /**
     * 根据CisOrderExecPlanTollQto构建查询条件
     * @param qto 查询条件对象
     * @return Specification查询规范
     */
    private static Specification<CisOrderExecPlan> getSpecification(CisOrderExecPlanTollQto qto) { 
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            
            // 参数校验
            if (qto == null) {
                return predicate;
            }
            
            // 患者主索引查询条件（必填字段）
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            
            // 开始时间查询条件（必填字段）
            if (qto.getStartDate() != null) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.greaterThanOrEqualTo(root.get("createdDate"), qto.getStartDate()));
            }
            
            // 结束时间查询条件（必填字段）
            if (qto.getEndDate() != null) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.lessThanOrEqualTo(root.get("createdDate"), qto.getEndDate()));
            }
            
            // 是否收费查询条件（可选字段）
            if (qto.getCharge() != null) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.equal(root.get("isCharge"), qto.getCharge()));
            }
            
            // 就诊类型查询条件（可选字段）
            // 注意：CisOrderExecPlan表中没有直接的visitType字段
            // 如果需要根据就诊类型过滤，需要通过关联查询实现
            if (qto.getVisitType() != null) {
                // 这里可以根据业务需求添加相应的查询逻辑
                // 例如：通过visitCode关联患者表或就诊表来获取就诊类型
                // 暂时注释掉，具体实现需要根据数据库表结构调整
                // predicate = criteriaBuilder.and(predicate, 
                //     criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            
            return predicate;
        };
    }

    /**
     * 根据CisOrderExecPlanTollQto查询执行单列表
     * @param qto 查询条件对象
     * @return 执行单列表
     */
    public static List<CisOrderExecPlan> getCisOrderExecPlansByTollQto(CisOrderExecPlanTollQto qto) {
        // 参数校验
        BusinessAssert.notNull(qto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "查询条件不能为空");
        BusinessAssert.notBlank(qto.getPatMiCode(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "患者主索引不能为空");
        BusinessAssert.notNull(qto.getStartDate(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "开始时间不能为空");
        BusinessAssert.notNull(qto.getEndDate(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "结束时间不能为空");
        
        // 时间范围校验
        BusinessAssert.isTrue(qto.getStartDate().isBefore(qto.getEndDate()) || qto.getStartDate().isEqual(qto.getEndDate()), 
            CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "开始时间不能晚于结束时间");
        
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    /**
     * 根据CisOrderExecPlanTollQto分页查询执行单
     * @param qto 查询条件对象
     * @return 执行单分页结果
     */
    public static Page<CisOrderExecPlan> getCisOrderExecPlanPageByTollQto(CisOrderExecPlanTollQto qto) {
        // 参数校验
        BusinessAssert.notNull(qto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "查询条件不能为空");
        BusinessAssert.notBlank(qto.getPatMiCode(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "患者主索引不能为空");
        BusinessAssert.notNull(qto.getStartDate(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "开始时间不能为空");
        BusinessAssert.notNull(qto.getEndDate(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "结束时间不能为空");
        
        // 时间范围校验
        BusinessAssert.isTrue(qto.getStartDate().isBefore(qto.getEndDate()) || qto.getStartDate().isEqual(qto.getEndDate()), 
            CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "开始时间不能晚于结束时间");
        
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * 增强版的getSpecification方法，支持更多查询条件
     * @param qto 查询条件对象
     * @return Specification查询规范
     */
    private static Specification<CisOrderExecPlan> getSpecificationEnhanced(CisOrderExecPlanTollQto qto) { 
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            
            // 参数校验
            if (qto == null) {
                return predicate;
            }
            
            // 患者主索引查询条件
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            
            // 时间范围查询 - 使用创建时间
            if (qto.getStartDate() != null && qto.getEndDate() != null) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.between(root.get("createdDate"), qto.getStartDate(), qto.getEndDate()));
            } else if (qto.getStartDate() != null) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.greaterThanOrEqualTo(root.get("createdDate"), qto.getStartDate()));
            } else if (qto.getEndDate() != null) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.lessThanOrEqualTo(root.get("createdDate"), qto.getEndDate()));
            }
            
            // 是否收费查询条件
            if (qto.getCharge() != null) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.equal(root.get("isCharge"), qto.getCharge()));
            }
            
            // 就诊类型查询条件（如果需要实现，可以通过以下方式）
            if (qto.getVisitType() != null) {
                // 方案1：如果CisOrderExecPlan表中有visitType字段
                // predicate = criteriaBuilder.and(predicate, 
                //     criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
                
                // 方案2：通过关联查询实现（需要根据实际表结构调整）
                // Join<CisOrderExecPlan, Visit> visitJoin = root.join("visit", JoinType.LEFT);
                // predicate = criteriaBuilder.and(predicate, 
                //     criteriaBuilder.equal(visitJoin.get("visitType"), qto.getVisitType()));
            }
            
            // 添加默认的状态过滤（排除已删除的记录）
            predicate = criteriaBuilder.and(predicate, 
                criteriaBuilder.notEqual(root.get("statusCode"), CisStatusEnum.OBSOLETE));
            
            return predicate;
        };
    }

    /**
     * 使用增强版查询方法
     * @param qto 查询条件对象
     * @return 执行单列表
     */
    public static List<CisOrderExecPlan> getCisOrderExecPlansByTollQtoEnhanced(CisOrderExecPlanTollQto qto) {
        // 参数校验
        BusinessAssert.notNull(qto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "查询条件不能为空");
        
        return dao().findAll(getSpecificationEnhanced(qto), JpaUtils.getSort(qto));
    }
