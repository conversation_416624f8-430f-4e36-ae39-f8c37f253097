package com.bjgoodwill.hip.ds.cis.rc.rcCard.to.to;

import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.RcCardQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "食源性疾病-患者病例信息")
public class CisRcFbdPatCaseQto extends RcCardQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1080558480862992087L;

    @Schema(description = "病历编码")
    private String fbdCode;
    @Schema(description = "就诊科室编码")
    private String orgCode;
    @Schema(description = "就诊科室名称")
    private String orgName;
    @Schema(description = "就诊时间")
    private LocalDateTime seeDate;
    @Schema(description = "是否复诊:1是，0否")
    private Boolean repeatFlag;
    @Schema(description = "是否住院:1是，0否")
    private Boolean inHospFlag;
    @Schema(description = "就诊前是否使用抗生素：1是，0否")
    private Boolean antibioticFlag;
    @Schema(description = "抗生素名称")
    private String antibioticName;
    @Schema(description = "接诊医生")
    private String seeDoc;
    @Schema(description = "接诊医生姓名")
    private String seeDocName;


    public String getFbdCode() {
        return fbdCode;
    }

    public void setFbdCode(String fbdCode) {
        this.fbdCode = fbdCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public LocalDateTime getSeeDate() {
        return seeDate;
    }

    public void setSeeDate(LocalDateTime seeDate) {
        this.seeDate = seeDate;
    }

    public Boolean getRepeatFlag() {
        return repeatFlag;
    }

    public void setRepeatFlag(Boolean repeatFlag) {
        this.repeatFlag = repeatFlag;
    }

    public Boolean getInHospFlag() {
        return inHospFlag;
    }

    public void setInHospFlag(Boolean inHospFlag) {
        this.inHospFlag = inHospFlag;
    }

    public Boolean getAntibioticFlag() {
        return antibioticFlag;
    }

    public void setAntibioticFlag(Boolean antibioticFlag) {
        this.antibioticFlag = antibioticFlag;
    }

    public String getAntibioticName() {
        return antibioticName;
    }

    public void setAntibioticName(String antibioticName) {
        this.antibioticName = antibioticName;
    }

    public String getSeeDoc() {
        return seeDoc;
    }

    public void setSeeDoc(String seeDoc) {
        this.seeDoc = seeDoc;
    }

    public String getSeeDocName() {
        return seeDocName;
    }

    public void setSeeDocName(String seeDocName) {
        this.seeDocName = seeDocName;
    }
}