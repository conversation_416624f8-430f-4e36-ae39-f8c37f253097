package com.bjgoodwill.hip.ds.cis.rc.rcCard.enmus;

public enum PatStatusEnum {
    //    treatment治疗，death死亡
    TREATMENT("TREATMENT", "治疗"),
    DEATH("DEATH", "死亡");

    private String code;
    private String name;

    PatStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}