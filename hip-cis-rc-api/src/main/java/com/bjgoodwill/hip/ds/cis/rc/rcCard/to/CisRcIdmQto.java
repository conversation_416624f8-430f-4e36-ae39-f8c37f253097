package com.bjgoodwill.hip.ds.cis.rc.rcCard.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "传染病报卡")
public class CisRcIdmQto extends RcCardQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6746991312387751111L;

    @Schema(description = "退卡处理 打回：1；打回后重新提交：0；")
    private Boolean backSolveFlag;


    public Boolean getBackSolveFlag() {
        return backSolveFlag;
    }

    public void setBackSolveFlag(Boolean backSolveFlag) {
        this.backSolveFlag = backSolveFlag;
    }
}