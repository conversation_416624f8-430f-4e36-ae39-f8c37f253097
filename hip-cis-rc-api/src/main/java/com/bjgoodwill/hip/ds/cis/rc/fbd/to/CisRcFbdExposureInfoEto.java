package com.bjgoodwill.hip.ds.cis.rc.fbd.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "食源性疾病-暴露信息")
public class CisRcFbdExposureInfoEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -3438189895976786426L;

    @Schema(description = "病历编码")
    private String fbdCode;
    @Schema(description = "食品名称")
    private String foodName;
    @Schema(description = "食品分类：字典fbd_foodclass")
    private String foodType;
    @Schema(description = "加工或包装方式 字典fbd_foodpack	")
    private String foodPack;
    @Schema(description = "食品品牌")
    private String foodBrand;
    @Schema(description = "生产厂家")
    private String manuFirmName;
    @Schema(description = "进食场所分类编码字典fbd_eatplacetype")
    private String eatPlaceType;
    @Schema(description = "进食场所编码字典fbd_eatplacetype")
    private String eatPlaceCode;
    @Schema(description = "进食地域0境内，1境外")
    private Boolean eatRegionFlag;
    @Schema(description = "进食地点-省	字典administrativedivesions")
    private String eatProvince;
    @Schema(description = "进食地点-市	administrativedivesions")
    private String eatCity;
    @Schema(description = "进食地点-区	administrativedivesions")
    private String eatCounty;
    @Schema(description = "进食地点-详细地址")
    private String eatVillage;
    @Schema(description = "进食人数")
    private Integer eatNum;
    @Schema(description = "进食时间")
    private LocalDateTime eatDate;
    @Schema(description = "购买场所类型编码 字典fbd_placetype	")
    private String purcPlaceType;
    @Schema(description = "购买场所编码")
    private String purcPlaceCode;
    @Schema(description = "购买地域0境内，1境外")
    private Boolean purcRegion;
    @Schema(description = "购买地点-省	字典administrativedivesions")
    private String purcProvince;
    @Schema(description = "购买地点-市	administrativedivesions")
    private String purcCity;
    @Schema(description = "购买地点-区	administrativedivesions")
    private String purcCounty;
    @Schema(description = "购买地点-详细地址	")
    private String purcVillage;
    @Schema(description = "其他人是否发病: 0否，1是，2未知")
    private Boolean otherPatFlag;

    @NotBlank(message = "病历编码不能为空！")
    public String getFbdCode() {
        return fbdCode;
    }

    public void setFbdCode(String fbdCode) {
        this.fbdCode = StringUtils.trimToNull(fbdCode);
    }

    public String getFoodName() {
        return foodName;
    }

    public void setFoodName(String foodName) {
        this.foodName = StringUtils.trimToNull(foodName);
    }

    public String getFoodType() {
        return foodType;
    }

    public void setFoodType(String foodType) {
        this.foodType = StringUtils.trimToNull(foodType);
    }

    public String getFoodPack() {
        return foodPack;
    }

    public void setFoodPack(String foodPack) {
        this.foodPack = StringUtils.trimToNull(foodPack);
    }

    public String getFoodBrand() {
        return foodBrand;
    }

    public void setFoodBrand(String foodBrand) {
        this.foodBrand = StringUtils.trimToNull(foodBrand);
    }

    public String getManuFirmName() {
        return manuFirmName;
    }

    public void setManuFirmName(String manuFirmName) {
        this.manuFirmName = StringUtils.trimToNull(manuFirmName);
    }

    public String getEatPlaceType() {
        return eatPlaceType;
    }

    public void setEatPlaceType(String eatPlaceType) {
        this.eatPlaceType = StringUtils.trimToNull(eatPlaceType);
    }

    public String getEatPlaceCode() {
        return eatPlaceCode;
    }

    public void setEatPlaceCode(String eatPlaceCode) {
        this.eatPlaceCode = StringUtils.trimToNull(eatPlaceCode);
    }

    public Boolean getEatRegionFlag() {
        return eatRegionFlag;
    }

    public void setEatRegionFlag(Boolean eatRegionFlag) {
        this.eatRegionFlag = eatRegionFlag;
    }

    public String getEatProvince() {
        return eatProvince;
    }

    public void setEatProvince(String eatProvince) {
        this.eatProvince = StringUtils.trimToNull(eatProvince);
    }

    public String getEatCity() {
        return eatCity;
    }

    public void setEatCity(String eatCity) {
        this.eatCity = StringUtils.trimToNull(eatCity);
    }

    public String getEatCounty() {
        return eatCounty;
    }

    public void setEatCounty(String eatCounty) {
        this.eatCounty = StringUtils.trimToNull(eatCounty);
    }

    public String getEatVillage() {
        return eatVillage;
    }

    public void setEatVillage(String eatVillage) {
        this.eatVillage = StringUtils.trimToNull(eatVillage);
    }

    public Integer getEatNum() {
        return eatNum;
    }

    public void setEatNum(Integer eatNum) {
        this.eatNum = eatNum;
    }

    public LocalDateTime getEatDate() {
        return eatDate;
    }

    public void setEatDate(LocalDateTime eatDate) {
        this.eatDate = eatDate;
    }

    public String getPurcPlaceType() {
        return purcPlaceType;
    }

    public void setPurcPlaceType(String purcPlaceType) {
        this.purcPlaceType = StringUtils.trimToNull(purcPlaceType);
    }

    public String getPurcPlaceCode() {
        return purcPlaceCode;
    }

    public void setPurcPlaceCode(String purcPlaceCode) {
        this.purcPlaceCode = StringUtils.trimToNull(purcPlaceCode);
    }

    public Boolean getPurcRegion() {
        return purcRegion;
    }

    public void setPurcRegion(Boolean purcRegion) {
        this.purcRegion = purcRegion;
    }

    public String getPurcProvince() {
        return purcProvince;
    }

    public void setPurcProvince(String purcProvince) {
        this.purcProvince = StringUtils.trimToNull(purcProvince);
    }

    public String getPurcCity() {
        return purcCity;
    }

    public void setPurcCity(String purcCity) {
        this.purcCity = StringUtils.trimToNull(purcCity);
    }

    public String getPurcCounty() {
        return purcCounty;
    }

    public void setPurcCounty(String purcCounty) {
        this.purcCounty = StringUtils.trimToNull(purcCounty);
    }

    public String getPurcVillage() {
        return purcVillage;
    }

    public void setPurcVillage(String purcVillage) {
        this.purcVillage = StringUtils.trimToNull(purcVillage);
    }

    public Boolean getOtherPatFlag() {
        return otherPatFlag;
    }

    public void setOtherPatFlag(Boolean otherPatFlag) {
        this.otherPatFlag = otherPatFlag;
    }
}