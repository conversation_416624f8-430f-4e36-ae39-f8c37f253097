package com.bjgoodwill.hip.ds.cis.rc.fbd.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "食源性疾病-病例检查")
public class CisRcFbdCaseExaminationNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1289699039666866205L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "病历编码")
    private String fbdCode;
    @Schema(description = "病例检查类型：ps主要症状与体征（字典fbd_symptomsign）、")
    private String ceType;
    @Schema(description = "编码")
    private String ceCode;
    @Schema(description = "值")
    private String ceValue;
    @Schema(description = "备注")
    private String remark;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "病历编码不能为空！")
    public String getFbdCode() {
        return fbdCode;
    }

    public void setFbdCode(String fbdCode) {
        this.fbdCode = StringUtils.trimToNull(fbdCode);
    }

    public String getCeType() {
        return ceType;
    }

    public void setCeType(String ceType) {
        this.ceType = StringUtils.trimToNull(ceType);
    }

    public String getCeCode() {
        return ceCode;
    }

    public void setCeCode(String ceCode) {
        this.ceCode = StringUtils.trimToNull(ceCode);
    }

    public String getCeValue() {
        return ceValue;
    }

    public void setCeValue(String ceValue) {
        this.ceValue = StringUtils.trimToNull(ceValue);
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = StringUtils.trimToNull(remark);
    }
}