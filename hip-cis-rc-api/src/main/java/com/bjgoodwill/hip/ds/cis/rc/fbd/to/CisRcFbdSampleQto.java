package com.bjgoodwill.hip.ds.cis.rc.fbd.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "食源性疾病-标本采集")
public class CisRcFbdSampleQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4748311029788942453L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "病历编码")
    private String fbdCode;
    @Schema(description = "申请单号")
    private String applyCode;
    @Schema(description = "标本编号")
    private String sampleNo;
    @Schema(description = "采样量")
    private Integer sampleSize;
    @Schema(description = "样本单位 字典fbd_sampleunit	")
    private String sampleUnit;
    @Schema(description = "采样日期")
    private LocalDateTime sampleDate;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getFbdCode() {
        return fbdCode;
    }

    public void setFbdCode(String fbdCode) {
        this.fbdCode = fbdCode;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public Integer getSampleSize() {
        return sampleSize;
    }

    public void setSampleSize(Integer sampleSize) {
        this.sampleSize = sampleSize;
    }

    public String getSampleUnit() {
        return sampleUnit;
    }

    public void setSampleUnit(String sampleUnit) {
        this.sampleUnit = sampleUnit;
    }

    public LocalDateTime getSampleDate() {
        return sampleDate;
    }

    public void setSampleDate(LocalDateTime sampleDate) {
        this.sampleDate = sampleDate;
    }
}