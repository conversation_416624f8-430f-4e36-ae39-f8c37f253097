package com.bjgoodwill.hip.ds.cis.rc.rcCard.to;

import com.bjgoodwill.hip.ds.cis.rc.rcCard.enmus.InstallmentEnum;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.enmus.PatStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "(恶性肿瘤报卡)")
public class CisRcMalignantTumorTo extends RcCardTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -189273179692296746L;

    @Schema(description = "患者现状：treatment治疗，death死亡")
    private PatStatusEnum patStatusEnum;
    @Schema(description = "诊断级别：1省级，2市级，3县级	")
    private String diseaseLevel;
    @Schema(description = "诊断单位")
    private String diseaseUnit;
    @Schema(description = "icd-10编码")
    private String diseaseCode;
    @Schema(description = "icd-10名称")
    private String diseaseName;
    @Schema(description = "icd-0编码 ")
    private String diseaseDCode;
    @Schema(description = "icd-0名称	")
    private String diseaseDName;
    @Schema(description = "患者知情标识	0否 ；1 是")
    private Boolean patKnowFlag;
    @Schema(description = "病理学类型	")
    private String pathologicalType;
    @Schema(description = "病理号")
    private String pathologicalNo;
    @Schema(description = "确认时期别t")
    private String confirmT;
    @Schema(description = "确认时期别n")
    private String confirm_n;
    @Schema(description = "确认时期别m")
    private String confirmM;
    @Schema(description = "分期：1:0-1;2:2；3:3；4:4；5:无法判定；")
    private InstallmentEnum installmentEnum;
    @Schema(description = "诊断部位：left左侧；right右侧；double双侧；unknown不详；")
    private String diseaseParts;
    @Schema(description = "诊断依据")
    private String diseaseBasis;
    @Schema(description = "诊断依据名称")
    private String diseaseBasisName;
    @Schema(description = "订正诊断编码")
    private String lastDiagnosisCode;
    @Schema(description = "订正诊断名称")
    private String lastDiagnosisName;
    @Schema(description = "原诊断日期")
    private LocalDateTime lastDiagnosisDate;
    @Schema(description = "订正人")
    private String lastUpdateUser;

    public PatStatusEnum getPatStatusEnum() {
        return patStatusEnum;
    }

    public void setPatStatusEnum(PatStatusEnum patStatusEnum) {
        this.patStatusEnum = patStatusEnum;
    }

    public String getDiseaseLevel() {
        return diseaseLevel;
    }

    public void setDiseaseLevel(String diseaseLevel) {
        this.diseaseLevel = diseaseLevel;
    }

    public String getDiseaseUnit() {
        return diseaseUnit;
    }

    public void setDiseaseUnit(String diseaseUnit) {
        this.diseaseUnit = diseaseUnit;
    }

    public String getDiseaseCode() {
        return diseaseCode;
    }

    public void setDiseaseCode(String diseaseCode) {
        this.diseaseCode = diseaseCode;
    }

    public String getDiseaseName() {
        return diseaseName;
    }

    public void setDiseaseName(String diseaseName) {
        this.diseaseName = diseaseName;
    }

    public String getDiseaseDCode() {
        return diseaseDCode;
    }

    public void setDiseaseDCode(String diseaseDCode) {
        this.diseaseDCode = diseaseDCode;
    }

    public String getDiseaseDName() {
        return diseaseDName;
    }

    public void setDiseaseDName(String diseaseDName) {
        this.diseaseDName = diseaseDName;
    }

    public Boolean getPatKnowFlag() {
        return patKnowFlag;
    }

    public void setPatKnowFlag(Boolean patKnowFlag) {
        this.patKnowFlag = patKnowFlag;
    }

    public String getPathologicalType() {
        return pathologicalType;
    }

    public void setPathologicalType(String pathologicalType) {
        this.pathologicalType = pathologicalType;
    }

    public String getPathologicalNo() {
        return pathologicalNo;
    }

    public void setPathologicalNo(String pathologicalNo) {
        this.pathologicalNo = pathologicalNo;
    }

    public String getConfirmT() {
        return confirmT;
    }

    public void setConfirmT(String confirmT) {
        this.confirmT = confirmT;
    }

    public String getConfirm_n() {
        return confirm_n;
    }

    public void setConfirm_n(String confirm_n) {
        this.confirm_n = confirm_n;
    }

    public String getConfirmM() {
        return confirmM;
    }

    public void setConfirmM(String confirmM) {
        this.confirmM = confirmM;
    }

    public InstallmentEnum getInstallmentEnum() {
        return installmentEnum;
    }

    public void setInstallmentEnum(InstallmentEnum installmentEnum) {
        this.installmentEnum = installmentEnum;
    }

    public String getDiseaseParts() {
        return diseaseParts;
    }

    public void setDiseaseParts(String diseaseParts) {
        this.diseaseParts = diseaseParts;
    }

    public String getDiseaseBasis() {
        return diseaseBasis;
    }

    public void setDiseaseBasis(String diseaseBasis) {
        this.diseaseBasis = diseaseBasis;
    }

    public String getDiseaseBasisName() {
        return diseaseBasisName;
    }

    public void setDiseaseBasisName(String diseaseBasisName) {
        this.diseaseBasisName = diseaseBasisName;
    }

    public String getLastDiagnosisCode() {
        return lastDiagnosisCode;
    }

    public void setLastDiagnosisCode(String lastDiagnosisCode) {
        this.lastDiagnosisCode = lastDiagnosisCode;
    }

    public String getLastDiagnosisName() {
        return lastDiagnosisName;
    }

    public void setLastDiagnosisName(String lastDiagnosisName) {
        this.lastDiagnosisName = lastDiagnosisName;
    }

    public LocalDateTime getLastDiagnosisDate() {
        return lastDiagnosisDate;
    }

    public void setLastDiagnosisDate(LocalDateTime lastDiagnosisDate) {
        this.lastDiagnosisDate = lastDiagnosisDate;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

}