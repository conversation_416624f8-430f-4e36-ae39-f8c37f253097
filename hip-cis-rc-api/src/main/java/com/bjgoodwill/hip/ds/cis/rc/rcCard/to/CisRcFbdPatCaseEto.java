package com.bjgoodwill.hip.ds.cis.rc.rcCard.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "食源性疾病-患者病例信息")
public class CisRcFbdPatCaseEto extends RcCardEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4800570952043203268L;

    @Schema(description = "病历编码")
    private String fbdCode;
    @Schema(description = "就诊科室编码")
    private String orgCode;
    @Schema(description = "就诊科室名称")
    private String orgName;
    @Schema(description = "就诊时间")
    private LocalDateTime seeDate;
    @Schema(description = "是否复诊:1是，0否")
    private Boolean repeatFlag;
    @Schema(description = "是否住院:1是，0否")
    private Boolean inHospFlag;
    @Schema(description = "就诊前是否使用抗生素：1是，0否")
    private Boolean antibioticFlag;
    @Schema(description = "抗生素名称")
    private String antibioticName;
    @Schema(description = "接诊医生")
    private String seeDoc;
    @Schema(description = "接诊医生姓名")
    private String seeDocName;

    public String getFbdCode() {
        return fbdCode;
    }

    public void setFbdCode(String fbdCode) {
        this.fbdCode = StringUtils.trimToNull(fbdCode);
    }

    @NotBlank(message = "就诊科室编码不能为空！")
    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = StringUtils.trimToNull(orgCode);
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = StringUtils.trimToNull(orgName);
    }

    public LocalDateTime getSeeDate() {
        return seeDate;
    }

    public void setSeeDate(LocalDateTime seeDate) {
        this.seeDate = seeDate;
    }

    public Boolean getRepeatFlag() {
        return repeatFlag;
    }

    public void setRepeatFlag(Boolean repeatFlag) {
        this.repeatFlag = repeatFlag;
    }

    public Boolean getInHospFlag() {
        return inHospFlag;
    }

    public void setInHospFlag(Boolean inHospFlag) {
        this.inHospFlag = inHospFlag;
    }

    public Boolean getAntibioticFlag() {
        return antibioticFlag;
    }

    public void setAntibioticFlag(Boolean antibioticFlag) {
        this.antibioticFlag = antibioticFlag;
    }

    public String getAntibioticName() {
        return antibioticName;
    }

    public void setAntibioticName(String antibioticName) {
        this.antibioticName = StringUtils.trimToNull(antibioticName);
    }

    public String getSeeDoc() {
        return seeDoc;
    }

    public void setSeeDoc(String seeDoc) {
        this.seeDoc = StringUtils.trimToNull(seeDoc);
    }

    public String getSeeDocName() {
        return seeDocName;
    }

    public void setSeeDocName(String seeDocName) {
        this.seeDocName = StringUtils.trimToNull(seeDocName);
    }
}