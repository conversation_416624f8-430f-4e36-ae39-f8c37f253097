package com.bjgoodwill.hip.ds.cis.rc.record.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "报卡执行记录")
public class CisRcRecordEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -8419016432601930446L;

    @Schema(description = "内容")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = StringUtils.trimToNull(content);
    }
}