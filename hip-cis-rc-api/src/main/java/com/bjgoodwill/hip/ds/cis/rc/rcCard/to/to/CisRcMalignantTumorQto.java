package com.bjgoodwill.hip.ds.cis.rc.rcCard.to.to;

import com.bjgoodwill.hip.ds.cis.rc.rcCard.enmus.InstallmentEnum;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.RcCardQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "(恶性肿瘤报卡)")
public class CisRcMalignantTumorQto extends RcCardQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1908335126909443745L;

    @Schema(description = "icd-10编码")
    private String diseaseCode;
    @Schema(description = "icd-10名称")
    private String diseaseName;
    @Schema(description = "icd-0编码 ")
    private String diseaseDCode;
    @Schema(description = "icd-0名称	")
    private String diseaseDName;
    @Schema(description = "患者知情标识	0否 ；1 是")
    private Boolean patKnowFlag;
    @Schema(description = "分期：1:0-1;2:2；3:3；4:4；5:无法判定；")
    private InstallmentEnum installmentEnum;


    public String getDiseaseCode() {
        return diseaseCode;
    }

    public void setDiseaseCode(String diseaseCode) {
        this.diseaseCode = diseaseCode;
    }

    public String getDiseaseName() {
        return diseaseName;
    }

    public void setDiseaseName(String diseaseName) {
        this.diseaseName = diseaseName;
    }

    public String getDiseaseDCode() {
        return diseaseDCode;
    }

    public void setDiseaseDCode(String diseaseDCode) {
        this.diseaseDCode = diseaseDCode;
    }

    public String getDiseaseDName() {
        return diseaseDName;
    }

    public void setDiseaseDName(String diseaseDName) {
        this.diseaseDName = diseaseDName;
    }

    public Boolean getPatKnowFlag() {
        return patKnowFlag;
    }

    public void setPatKnowFlag(Boolean patKnowFlag) {
        this.patKnowFlag = patKnowFlag;
    }

    public InstallmentEnum getInstallmentEnum() {
        return installmentEnum;
    }

    public void setInstallmentEnum(InstallmentEnum installmentEnum) {
        this.installmentEnum = installmentEnum;
    }
}