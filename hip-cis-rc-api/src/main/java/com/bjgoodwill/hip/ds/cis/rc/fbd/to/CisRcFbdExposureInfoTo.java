package com.bjgoodwill.hip.ds.cis.rc.fbd.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "食源性疾病-暴露信息")
public class CisRcFbdExposureInfoTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1288382310489683607L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "病历编码")
    private String fbdCode;
    @Schema(description = "食品名称")
    private String foodName;
    @Schema(description = "食品分类：字典fbd_foodclass")
    private String foodType;
    @Schema(description = "加工或包装方式 字典fbd_foodpack	")
    private String foodPack;
    @Schema(description = "食品品牌")
    private String foodBrand;
    @Schema(description = "生产厂家")
    private String manuFirmName;
    @Schema(description = "进食场所分类编码字典fbd_eatplacetype")
    private String eatPlaceType;
    @Schema(description = "进食场所编码字典fbd_eatplacetype")
    private String eatPlaceCode;
    @Schema(description = "进食地域0境内，1境外")
    private Boolean eatRegionFlag;
    @Schema(description = "进食地点-省	字典administrativedivesions")
    private String eatProvince;
    @Schema(description = "进食地点-市	administrativedivesions")
    private String eatCity;
    @Schema(description = "进食地点-区	administrativedivesions")
    private String eatCounty;
    @Schema(description = "进食地点-详细地址")
    private String eatVillage;
    @Schema(description = "进食人数")
    private Integer eatNum;
    @Schema(description = "进食时间")
    private LocalDateTime eatDate;
    @Schema(description = "购买场所类型编码 字典fbd_placetype	")
    private String purcPlaceType;
    @Schema(description = "购买场所编码")
    private String purcPlaceCode;
    @Schema(description = "购买地域0境内，1境外")
    private Boolean purcRegion;
    @Schema(description = "购买地点-省	字典administrativedivesions")
    private String purcProvince;
    @Schema(description = "购买地点-市	administrativedivesions")
    private String purcCity;
    @Schema(description = "购买地点-区	administrativedivesions")
    private String purcCounty;
    @Schema(description = "购买地点-详细地址	")
    private String purcVillage;
    @Schema(description = "其他人是否发病: 0否，1是，2未知")
    private Boolean otherPatFlag;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "逻辑删除标记")
    private boolean deleted;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFbdCode() {
        return fbdCode;
    }

    public void setFbdCode(String fbdCode) {
        this.fbdCode = fbdCode;
    }

    public String getFoodName() {
        return foodName;
    }

    public void setFoodName(String foodName) {
        this.foodName = foodName;
    }

    public String getFoodType() {
        return foodType;
    }

    public void setFoodType(String foodType) {
        this.foodType = foodType;
    }

    public String getFoodPack() {
        return foodPack;
    }

    public void setFoodPack(String foodPack) {
        this.foodPack = foodPack;
    }

    public String getFoodBrand() {
        return foodBrand;
    }

    public void setFoodBrand(String foodBrand) {
        this.foodBrand = foodBrand;
    }

    public String getManuFirmName() {
        return manuFirmName;
    }

    public void setManuFirmName(String manuFirmName) {
        this.manuFirmName = manuFirmName;
    }

    public String getEatPlaceType() {
        return eatPlaceType;
    }

    public void setEatPlaceType(String eatPlaceType) {
        this.eatPlaceType = eatPlaceType;
    }

    public String getEatPlaceCode() {
        return eatPlaceCode;
    }

    public void setEatPlaceCode(String eatPlaceCode) {
        this.eatPlaceCode = eatPlaceCode;
    }

    public Boolean getEatRegionFlag() {
        return eatRegionFlag;
    }

    public void setEatRegionFlag(Boolean eatRegionFlag) {
        this.eatRegionFlag = eatRegionFlag;
    }

    public String getEatProvince() {
        return eatProvince;
    }

    public void setEatProvince(String eatProvince) {
        this.eatProvince = eatProvince;
    }

    public String getEatCity() {
        return eatCity;
    }

    public void setEatCity(String eatCity) {
        this.eatCity = eatCity;
    }

    public String getEatCounty() {
        return eatCounty;
    }

    public void setEatCounty(String eatCounty) {
        this.eatCounty = eatCounty;
    }

    public String getEatVillage() {
        return eatVillage;
    }

    public void setEatVillage(String eatVillage) {
        this.eatVillage = eatVillage;
    }

    public Integer getEatNum() {
        return eatNum;
    }

    public void setEatNum(Integer eatNum) {
        this.eatNum = eatNum;
    }

    public LocalDateTime getEatDate() {
        return eatDate;
    }

    public void setEatDate(LocalDateTime eatDate) {
        this.eatDate = eatDate;
    }

    public String getPurcPlaceType() {
        return purcPlaceType;
    }

    public void setPurcPlaceType(String purcPlaceType) {
        this.purcPlaceType = purcPlaceType;
    }

    public String getPurcPlaceCode() {
        return purcPlaceCode;
    }

    public void setPurcPlaceCode(String purcPlaceCode) {
        this.purcPlaceCode = purcPlaceCode;
    }

    public Boolean getPurcRegion() {
        return purcRegion;
    }

    public void setPurcRegion(Boolean purcRegion) {
        this.purcRegion = purcRegion;
    }

    public String getPurcProvince() {
        return purcProvince;
    }

    public void setPurcProvince(String purcProvince) {
        this.purcProvince = purcProvince;
    }

    public String getPurcCity() {
        return purcCity;
    }

    public void setPurcCity(String purcCity) {
        this.purcCity = purcCity;
    }

    public String getPurcCounty() {
        return purcCounty;
    }

    public void setPurcCounty(String purcCounty) {
        this.purcCounty = purcCounty;
    }

    public String getPurcVillage() {
        return purcVillage;
    }

    public void setPurcVillage(String purcVillage) {
        this.purcVillage = purcVillage;
    }

    public Boolean getOtherPatFlag() {
        return otherPatFlag;
    }

    public void setOtherPatFlag(Boolean otherPatFlag) {
        this.otherPatFlag = otherPatFlag;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisRcFbdExposureInfoTo other = (CisRcFbdExposureInfoTo) obj;
        return Objects.equals(id, other.id);
    }
}