package com.bjgoodwill.hip.ds.cis.rc.hiv.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "传染病上报艾滋病性病附卡")
public class CisRcIdmHivTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -5663460969440428197L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "传染病报卡标识")
    private String cisRcIdmId;
    @Schema(description = "接触史（编码|编码）")
    private String contact;
    @Schema(description = "接触史其他项 存汉字说明")
    private String contactOther;
    @Schema(description = "性病使【有；无；不详】")
    private String stdtDisease;
    @Schema(description = "感染途径 字典IdmInfection")
    private String infection;
    @Schema(description = "感染途径其他项 存汉字说明")
    private String infectionOther;
    @Schema(description = "样本来源 字典IdmSampleSource")
    private String sampleSource;
    @Schema(description = "样本来源其他项，存汉字说明")
    private String sampleOther;
    @Schema(description = "实验室检测结论【确证检测阳性；替代策略检测阳性】")
    private String detectionConclusion;
    @Schema(description = "检测阳性日期")
    private LocalDateTime detectionDate;
    @Schema(description = "检测单位")
    private String detectionUnit;
    @Schema(description = "检测单位名称")
    private String detectionUnitName;
    @Schema(description = "艾滋病确认日期")
    private LocalDateTime hivConfirmDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCisRcIdmId() {
        return cisRcIdmId;
    }

    public void setCisRcIdmId(String cisRcIdmId) {
        this.cisRcIdmId = cisRcIdmId;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getContactOther() {
        return contactOther;
    }

    public void setContactOther(String contactOther) {
        this.contactOther = contactOther;
    }

    public String getStdtDisease() {
        return stdtDisease;
    }

    public void setStdtDisease(String stdtDisease) {
        this.stdtDisease = stdtDisease;
    }

    public String getInfection() {
        return infection;
    }

    public void setInfection(String infection) {
        this.infection = infection;
    }

    public String getInfectionOther() {
        return infectionOther;
    }

    public void setInfectionOther(String infectionOther) {
        this.infectionOther = infectionOther;
    }

    public String getSampleSource() {
        return sampleSource;
    }

    public void setSampleSource(String sampleSource) {
        this.sampleSource = sampleSource;
    }

    public String getSampleOther() {
        return sampleOther;
    }

    public void setSampleOther(String sampleOther) {
        this.sampleOther = sampleOther;
    }

    public String getDetectionConclusion() {
        return detectionConclusion;
    }

    public void setDetectionConclusion(String detectionConclusion) {
        this.detectionConclusion = detectionConclusion;
    }

    public LocalDateTime getDetectionDate() {
        return detectionDate;
    }

    public void setDetectionDate(LocalDateTime detectionDate) {
        this.detectionDate = detectionDate;
    }

    public String getDetectionUnit() {
        return detectionUnit;
    }

    public void setDetectionUnit(String detectionUnit) {
        this.detectionUnit = detectionUnit;
    }

    public String getDetectionUnitName() {
        return detectionUnitName;
    }

    public void setDetectionUnitName(String detectionUnitName) {
        this.detectionUnitName = detectionUnitName;
    }

    public LocalDateTime getHivConfirmDate() {
        return hivConfirmDate;
    }

    public void setHivConfirmDate(LocalDateTime hivConfirmDate) {
        this.hivConfirmDate = hivConfirmDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisRcIdmHivTo other = (CisRcIdmHivTo) obj;
        return Objects.equals(id, other.id);
    }
}