package com.bjgoodwill.hip.ds.cis.rc.rcCard.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "报卡父类")
public class RcCardQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -216877693554536123L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "卡片编号")
    private String reportNo;
    @Schema(description = "患者类型")
    private VisitTypeEnum visitType;
    @Schema(description = "主索引编码")
    private String patMiCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String name;
    @Schema(description = "患儿家长姓名")
    private String parentsName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "学历	字典recordofformalschooling")
    private String education;
    @Schema(description = "填表人")
    private String reportUser;
    @Schema(description = "填表人姓名")
    private String reportUserName;
    @Schema(description = "是否上传[是，否]")
    private Boolean uploadFlag;
    @Schema(description = "网报返回报卡编码")
    private String uploadReportNo;
    @Schema(description = "医院编码")
    private String hospitalCode;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentsName() {
        return parentsName;
    }

    public void setParentsName(String parentsName) {
        this.parentsName = parentsName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getReportUser() {
        return reportUser;
    }

    public void setReportUser(String reportUser) {
        this.reportUser = reportUser;
    }

    public String getReportUserName() {
        return reportUserName;
    }

    public void setReportUserName(String reportUserName) {
        this.reportUserName = reportUserName;
    }

    public Boolean getUploadFlag() {
        return uploadFlag;
    }

    public void setUploadFlag(Boolean uploadFlag) {
        this.uploadFlag = uploadFlag;
    }

    public String getUploadReportNo() {
        return uploadReportNo;
    }

    public void setUploadReportNo(String uploadReportNo) {
        this.uploadReportNo = uploadReportNo;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

}