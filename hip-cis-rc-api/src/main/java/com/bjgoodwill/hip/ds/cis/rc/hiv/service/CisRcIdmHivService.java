package com.bjgoodwill.hip.ds.cis.rc.hiv.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivEto;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivNto;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivQto;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "传染病上报艾滋病性病附卡领域服务", description = "传染病上报艾滋病性病附卡领域服务")
public interface CisRcIdmHivService {

    @Operation(summary = "根据查询条件对传染病上报艾滋病性病附卡进行查询。")
    @GetMapping("/cisRcIdmHivs")
    List<CisRcIdmHivTo> getCisRcIdmHivs(@ParameterObject @SpringQueryMap CisRcIdmHivQto cisRcIdmHivQto);

    @Operation(summary = "根据查询条件对传染病上报艾滋病性病附卡进行分页查询。")
    @GetMapping("/cisRcIdmHivs/pages")
    GridResultSet<CisRcIdmHivTo> getCisRcIdmHivPage(@ParameterObject @SpringQueryMap CisRcIdmHivQto cisRcIdmHivQto);

    @Operation(summary = "根据唯一标识返回传染病上报艾滋病性病附卡。")
    @GetMapping("/cisRcIdmHivs/{id:.+}")
    CisRcIdmHivTo getCisRcIdmHivById(@PathVariable("id") String id);

    @Operation(summary = "创建传染病上报艾滋病性病附卡。")
    @PostMapping("/cisRcIdmHivs")
    CisRcIdmHivTo createCisRcIdmHiv(@RequestBody @Valid CisRcIdmHivNto cisRcIdmHivNto);

    @Operation(summary = "根据唯一标识修改传染病上报艾滋病性病附卡。")
    @PutMapping("/cisRcIdmHivs/{id:.+}")
    void updateCisRcIdmHiv(@PathVariable("id") String id, @RequestBody @Valid CisRcIdmHivEto cisRcIdmHivEto);

    @Operation(summary = "根据唯一标识删除传染病上报艾滋病性病附卡。")
    @DeleteMapping("/cisRcIdmHivs/{id:.+}")
    void deleteCisRcIdmHiv(@PathVariable("id") String id);

}