package com.bjgoodwill.hip.ds.cis.rc.rcCard.to.to;

import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.RcCardTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "传染病报卡")
public class CisRcIdmTo extends RcCardTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -3495586829946889619L;

    @Schema(description = "是否首诊 0否 1 是")
    private Boolean fristDiagnoseFlag;
    @Schema(description = "病例分类1")
    private String caseClass1;
    @Schema(description = "病例分类2")
    private String caseClass2;
    @Schema(description = "有无附卡 0无 1有")
    private Boolean additionFlag;

    public Boolean getFristDiagnoseFlag() {
        return fristDiagnoseFlag;
    }

    public void setFristDiagnoseFlag(Boolean fristDiagnoseFlag) {
        this.fristDiagnoseFlag = fristDiagnoseFlag;
    }

    public String getCaseClass1() {
        return caseClass1;
    }

    public void setCaseClass1(String caseClass1) {
        this.caseClass1 = caseClass1;
    }

    public String getCaseClass2() {
        return caseClass2;
    }

    public void setCaseClass2(String caseClass2) {
        this.caseClass2 = caseClass2;
    }

    public Boolean getAdditionFlag() {
        return additionFlag;
    }

    public void setAdditionFlag(Boolean additionFlag) {
        this.additionFlag = additionFlag;
    }

}