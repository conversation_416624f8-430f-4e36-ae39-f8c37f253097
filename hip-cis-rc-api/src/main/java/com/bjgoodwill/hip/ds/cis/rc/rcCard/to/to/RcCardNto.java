package com.bjgoodwill.hip.ds.cis.rc.rcCard.to.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@Schema(description = "报卡父类")
public abstract class RcCardNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2652062380803651210L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "卡片编号")
    private String reportNo;
    @Schema(description = "患者类型")
    private VisitTypeEnum visitType;
    @Schema(description = "主索引编码")
    private String patMiCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者姓名")
    private String name;
    @Schema(description = "患儿家长姓名")
    private String parentsName;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "实足年龄")
    private Float age;
    @Schema(description = "年龄单位")
    private String ageUnit;
    @Schema(description = "身份证号")
    private String idNo;
    @Schema(description = "工作单位")
    private String companyName;
    @Schema(description = "联系电话")
    private String contactTel;
    @Schema(description = "病人来源 字典IdmAREA本县区、本市其它县区、本省其它地市、其它省、港澳台、外籍")
    private String homeArea;
    @Schema(description = "省")
    private String province;
    @Schema(description = "市")
    private String city;
    @Schema(description = "县（区）")
    private String county;
    @Schema(description = "乡（镇、街道）")
    private String homeJd;
    @Schema(description = "村")
    private String village;
    @Schema(description = "门牌号")
    private String houseNo;
    @Schema(description = "居住省	administrativedivesions")
    private String czProvince;
    @Schema(description = "居住市	administrativedivesions")
    private String czCity;
    @Schema(description = "居住县（区）	administrativedivesions")
    private String czCounty;
    @Schema(description = "居住乡（镇、街道）	administrativedivesions")
    private String czHomeJd;
    @Schema(description = "民族	字典nation")
    private String nation;
    @Schema(description = "详细地址")
    private String czVillage;
    @Schema(description = "居住门牌号")
    private String czHouseNo;
    @Schema(description = "婚姻	字典maritalstatus")
    private String maritalStatus;
    @Schema(description = "学历	字典recordofformalschooling")
    private String education;
    @Schema(description = "患者职业")
    private String work;
    @Schema(description = "监护人姓名")
    private String guardianName;
    @Schema(description = "发病日期")
    private LocalDateTime infectDate;
    @Schema(description = "诊断日期")
    private LocalDateTime diagnosisDate;
    @Schema(description = "死亡日期")
    private LocalDateTime deadDate;
    @Schema(description = "死亡原因")
    private String deadWhy;
    @Schema(description = "疾病分类编码 字典：IdmDiseaseClass")
    private String diseaseClass;
    @Schema(description = "疾病编码 字典IdmDisease")
    private String diseaseCode;
    @Schema(description = "报告单位类别：Medical医疗机构；Management经营企业；Produce生产企业；Personal个人；Other其它")
    private String reportUnitCategory;
    @Schema(description = "报告单位")
    private String reportUnit;
    @Schema(description = "报告单位联系电话")
    private String unitTel;
    @Schema(description = "填表人")
    private String reportUser;
    @Schema(description = "填表人姓名")
    private String reportUserName;
    @Schema(description = "填报科室编码")
    private String orgCode;
    @Schema(description = "填报科室名称")
    private String orgName;
    @Schema(description = "报告区县	administrativedivesions")
    private String reportCounty;
    @Schema(description = "备注")
    private String remarks;
    @Schema(description = "医院编码")
    private String hospitalCode;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "卡片编号不能为空！")
    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = StringUtils.trimToNull(reportNo);
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    @NotBlank(message = "主索引编码不能为空！")
    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = StringUtils.trimToNull(patMiCode);
    }

    @NotBlank(message = "就诊流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimToNull(name);
    }

    public String getParentsName() {
        return parentsName;
    }

    public void setParentsName(String parentsName) {
        this.parentsName = StringUtils.trimToNull(parentsName);
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = StringUtils.trimToNull(sex);
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public Float getAge() {
        return age;
    }

    public void setAge(Float age) {
        this.age = age;
    }

    public String getAgeUnit() {
        return ageUnit;
    }

    public void setAgeUnit(String ageUnit) {
        this.ageUnit = StringUtils.trimToNull(ageUnit);
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = StringUtils.trimToNull(idNo);
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = StringUtils.trimToNull(companyName);
    }

    @Size(max = 24, message = "联系电话长度不能超过24个字符！")
    public String getContactTel() {
        return contactTel;
    }

    public void setContactTel(String contactTel) {
        this.contactTel = StringUtils.trimToNull(contactTel);
    }

    public String getHomeArea() {
        return homeArea;
    }

    public void setHomeArea(String homeArea) {
        this.homeArea = StringUtils.trimToNull(homeArea);
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = StringUtils.trimToNull(province);
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = StringUtils.trimToNull(city);
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = StringUtils.trimToNull(county);
    }

    public String getHomeJd() {
        return homeJd;
    }

    public void setHomeJd(String homeJd) {
        this.homeJd = StringUtils.trimToNull(homeJd);
    }

    public String getVillage() {
        return village;
    }

    public void setVillage(String village) {
        this.village = StringUtils.trimToNull(village);
    }

    public String getHouseNo() {
        return houseNo;
    }

    public void setHouseNo(String houseNo) {
        this.houseNo = StringUtils.trimToNull(houseNo);
    }

    public String getCzProvince() {
        return czProvince;
    }

    public void setCzProvince(String czProvince) {
        this.czProvince = StringUtils.trimToNull(czProvince);
    }

    public String getCzCity() {
        return czCity;
    }

    public void setCzCity(String czCity) {
        this.czCity = StringUtils.trimToNull(czCity);
    }

    public String getCzCounty() {
        return czCounty;
    }

    public void setCzCounty(String czCounty) {
        this.czCounty = StringUtils.trimToNull(czCounty);
    }

    public String getCzHomeJd() {
        return czHomeJd;
    }

    public void setCzHomeJd(String czHomeJd) {
        this.czHomeJd = StringUtils.trimToNull(czHomeJd);
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = StringUtils.trimToNull(nation);
    }

    public String getCzVillage() {
        return czVillage;
    }

    public void setCzVillage(String czVillage) {
        this.czVillage = StringUtils.trimToNull(czVillage);
    }

    public String getCzHouseNo() {
        return czHouseNo;
    }

    public void setCzHouseNo(String czHouseNo) {
        this.czHouseNo = StringUtils.trimToNull(czHouseNo);
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = StringUtils.trimToNull(maritalStatus);
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = StringUtils.trimToNull(education);
    }

    public String getWork() {
        return work;
    }

    public void setWork(String work) {
        this.work = StringUtils.trimToNull(work);
    }

    public String getGuardianName() {
        return guardianName;
    }

    public void setGuardianName(String guardianName) {
        this.guardianName = StringUtils.trimToNull(guardianName);
    }

    public LocalDateTime getInfectDate() {
        return infectDate;
    }

    public void setInfectDate(LocalDateTime infectDate) {
        this.infectDate = infectDate;
    }

    public LocalDateTime getDiagnosisDate() {
        return diagnosisDate;
    }

    public void setDiagnosisDate(LocalDateTime diagnosisDate) {
        this.diagnosisDate = diagnosisDate;
    }

    public LocalDateTime getDeadDate() {
        return deadDate;
    }

    public void setDeadDate(LocalDateTime deadDate) {
        this.deadDate = deadDate;
    }

    public String getDeadWhy() {
        return deadWhy;
    }

    public void setDeadWhy(String deadWhy) {
        this.deadWhy = StringUtils.trimToNull(deadWhy);
    }

    public String getDiseaseClass() {
        return diseaseClass;
    }

    public void setDiseaseClass(String diseaseClass) {
        this.diseaseClass = StringUtils.trimToNull(diseaseClass);
    }

    public String getDiseaseCode() {
        return diseaseCode;
    }

    public void setDiseaseCode(String diseaseCode) {
        this.diseaseCode = StringUtils.trimToNull(diseaseCode);
    }

    public String getReportUnitCategory() {
        return reportUnitCategory;
    }

    public void setReportUnitCategory(String reportUnitCategory) {
        this.reportUnitCategory = StringUtils.trimToNull(reportUnitCategory);
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = StringUtils.trimToNull(reportUnit);
    }

    public String getUnitTel() {
        return unitTel;
    }

    public void setUnitTel(String unitTel) {
        this.unitTel = StringUtils.trimToNull(unitTel);
    }

    public String getReportUser() {
        return reportUser;
    }

    public void setReportUser(String reportUser) {
        this.reportUser = StringUtils.trimToNull(reportUser);
    }

    public String getReportUserName() {
        return reportUserName;
    }

    public void setReportUserName(String reportUserName) {
        this.reportUserName = StringUtils.trimToNull(reportUserName);
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = StringUtils.trimToNull(orgCode);
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = StringUtils.trimToNull(orgName);
    }

    public String getReportCounty() {
        return reportCounty;
    }

    public void setReportCounty(String reportCounty) {
        this.reportCounty = StringUtils.trimToNull(reportCounty);
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = StringUtils.trimToNull(remarks);
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = StringUtils.trimToNull(hospitalCode);
    }

    public String getMinimal_class() {
        return "." + this.getClass().getSimpleName();
    }
}