package com.bjgoodwill.hip.ds.cis.rc.fbd.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdCaseExaminationEto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdCaseExaminationNto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdCaseExaminationQto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdCaseExaminationTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "食源性疾病-病例检查领域服务", description = "食源性疾病-病例检查领域服务")
public interface CisRcFbdCaseExaminationService {

    @Operation(summary = "根据查询条件对食源性疾病-病例检查进行查询。")
    @GetMapping("/cisRcFbdCaseExaminations")
    List<CisRcFbdCaseExaminationTo> getCisRcFbdCaseExaminations(@ParameterObject @SpringQueryMap CisRcFbdCaseExaminationQto cisRcFbdCaseExaminationQto);

    @Operation(summary = "根据查询条件对食源性疾病-病例检查进行分页查询。")
    @GetMapping("/cisRcFbdCaseExaminations/pages")
    GridResultSet<CisRcFbdCaseExaminationTo> getCisRcFbdCaseExaminationPage(@ParameterObject @SpringQueryMap CisRcFbdCaseExaminationQto cisRcFbdCaseExaminationQto);

    @Operation(summary = "根据唯一标识返回食源性疾病-病例检查。")
    @GetMapping("/cisRcFbdCaseExaminations/{id:.+}")
    CisRcFbdCaseExaminationTo getCisRcFbdCaseExaminationById(@PathVariable("id") String id);

    @Operation(summary = "创建食源性疾病-病例检查。")
    @PostMapping("/cisRcFbdCaseExaminations")
    CisRcFbdCaseExaminationTo createCisRcFbdCaseExamination(@RequestBody @Valid CisRcFbdCaseExaminationNto cisRcFbdCaseExaminationNto);

    @Operation(summary = "根据唯一标识修改食源性疾病-病例检查。")
    @PutMapping("/cisRcFbdCaseExaminations/{id:.+}")
    void updateCisRcFbdCaseExamination(@PathVariable("id") String id, @RequestBody @Valid CisRcFbdCaseExaminationEto cisRcFbdCaseExaminationEto);

    @Operation(summary = "根据唯一标识删除食源性疾病-病例检查。")
    @DeleteMapping("/cisRcFbdCaseExaminations/{id:.+}")
    void deleteCisRcFbdCaseExamination(@PathVariable("id") String id);

}