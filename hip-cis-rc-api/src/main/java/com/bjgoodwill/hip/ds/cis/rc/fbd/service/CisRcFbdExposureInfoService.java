package com.bjgoodwill.hip.ds.cis.rc.fbd.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoEto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoNto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoQto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "食源性疾病-暴露信息领域服务", description = "食源性疾病-暴露信息领域服务")
public interface CisRcFbdExposureInfoService {

    @Operation(summary = "根据查询条件对食源性疾病-暴露信息进行查询。")
    @GetMapping("/cisRcFbdExposureInfoes")
    List<CisRcFbdExposureInfoTo> getCisRcFbdExposureInfoes(@ParameterObject @SpringQueryMap CisRcFbdExposureInfoQto cisRcFbdExposureInfoQto);

    @Operation(summary = "根据查询条件对食源性疾病-暴露信息进行分页查询。")
    @GetMapping("/cisRcFbdExposureInfoes/pages")
    GridResultSet<CisRcFbdExposureInfoTo> getCisRcFbdExposureInfoPage(@ParameterObject @SpringQueryMap CisRcFbdExposureInfoQto cisRcFbdExposureInfoQto);

    @Operation(summary = "根据唯一标识返回食源性疾病-暴露信息。")
    @GetMapping("/cisRcFbdExposureInfoes/{id:.+}")
    CisRcFbdExposureInfoTo getCisRcFbdExposureInfoById(@PathVariable("id") String id);

    @Operation(summary = "创建食源性疾病-暴露信息。")
    @PostMapping("/cisRcFbdExposureInfoes")
    CisRcFbdExposureInfoTo createCisRcFbdExposureInfo(@RequestBody @Valid CisRcFbdExposureInfoNto cisRcFbdExposureInfoNto);

    @Operation(summary = "根据唯一标识修改食源性疾病-暴露信息。")
    @PutMapping("/cisRcFbdExposureInfoes/{id:.+}")
    void updateCisRcFbdExposureInfo(@PathVariable("id") String id, @RequestBody @Valid CisRcFbdExposureInfoEto cisRcFbdExposureInfoEto);

    @Operation(summary = "根据唯一标识删除食源性疾病-暴露信息。")
    @DeleteMapping("/cisRcFbdExposureInfoes/{id:.+}")
    void deleteCisRcFbdExposureInfo(@PathVariable("id") String id);

}