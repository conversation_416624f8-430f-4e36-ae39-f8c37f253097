package com.bjgoodwill.hip.ds.cis.rc.record.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rc.record.to.CisRcRecordEto;
import com.bjgoodwill.hip.ds.cis.rc.record.to.CisRcRecordNto;
import com.bjgoodwill.hip.ds.cis.rc.record.to.CisRcRecordQto;
import com.bjgoodwill.hip.ds.cis.rc.record.to.CisRcRecordTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "报卡执行记录领域服务", description = "报卡执行记录领域服务")
public interface CisRcRecordService {

    @Operation(summary = "根据查询条件对报卡执行记录进行查询。")
    @GetMapping("/cisRcRecords")
    List<CisRcRecordTo> getCisRcRecords(@ParameterObject @SpringQueryMap CisRcRecordQto cisRcRecordQto);

    @Operation(summary = "根据查询条件对报卡执行记录进行分页查询。")
    @GetMapping("/cisRcRecords/pages")
    GridResultSet<CisRcRecordTo> getCisRcRecordPage(@ParameterObject @SpringQueryMap CisRcRecordQto cisRcRecordQto);

    @Operation(summary = "根据唯一标识返回报卡执行记录。")
    @GetMapping("/cisRcRecords/{id:.+}")
    CisRcRecordTo getCisRcRecordById(@PathVariable("id") String id);

    @Operation(summary = "创建报卡执行记录。")
    @PostMapping("/cisRcRecords")
    CisRcRecordTo createCisRcRecord(@RequestBody @Valid CisRcRecordNto cisRcRecordNto);

    @Operation(summary = "根据唯一标识修改报卡执行记录。")
    @PutMapping("/cisRcRecords/{id:.+}")
    void updateCisRcRecord(@PathVariable("id") String id, @RequestBody @Valid CisRcRecordEto cisRcRecordEto);

    @Operation(summary = "根据唯一标识删除报卡执行记录。")
    @DeleteMapping("/cisRcRecords/{id:.+}")
    void deleteCisRcRecord(@PathVariable("id") String id);

}