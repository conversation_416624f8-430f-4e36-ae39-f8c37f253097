package com.bjgoodwill.hip.ds.cis.rc.record.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.RecordTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "报卡执行记录")
public class CisRcRecordQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -9104784835592725101L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "报卡父类标识")
    private String rcCardId;
    @Schema(description = "操作类型")
    private RecordTypeEnum recordType;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getRcCardId() {
        return rcCardId;
    }

    public void setRcCardId(String rcCardId) {
        this.rcCardId = rcCardId;
    }

    public RecordTypeEnum getRecordType() {
        return recordType;
    }

    public void setRecordType(RecordTypeEnum recordType) {
        this.recordType = recordType;
    }
}