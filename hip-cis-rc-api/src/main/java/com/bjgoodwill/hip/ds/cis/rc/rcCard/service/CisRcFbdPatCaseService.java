package com.bjgoodwill.hip.ds.cis.rc.rcCard.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcFbdPatCaseEto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcFbdPatCaseNto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcFbdPatCaseQto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcFbdPatCaseTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "食源性疾病-患者病例信息领域服务", description = "食源性疾病-患者病例信息领域服务")
public interface CisRcFbdPatCaseService {

    @Operation(summary = "根据查询条件对食源性疾病-患者病例信息进行查询。")
    @GetMapping("/cisRcFbdPatCases")
    List<CisRcFbdPatCaseTo> getCisRcFbdPatCases(@ParameterObject @SpringQueryMap CisRcFbdPatCaseQto cisRcFbdPatCaseQto);

    @Operation(summary = "根据查询条件对食源性疾病-患者病例信息进行分页查询。")
    @GetMapping("/cisRcFbdPatCases/pages")
    GridResultSet<CisRcFbdPatCaseTo> getCisRcFbdPatCasePage(@ParameterObject @SpringQueryMap CisRcFbdPatCaseQto cisRcFbdPatCaseQto);

    @Operation(summary = "根据唯一标识返回食源性疾病-患者病例信息。")
    @GetMapping("/cisRcFbdPatCases/{id:.+}")
    CisRcFbdPatCaseTo getCisRcFbdPatCaseById(@PathVariable("id") String id);

    @Operation(summary = "创建食源性疾病-患者病例信息。")
    @PostMapping("/cisRcFbdPatCases")
    CisRcFbdPatCaseTo createCisRcFbdPatCase(@RequestBody @Valid CisRcFbdPatCaseNto cisRcFbdPatCaseNto);

    @Operation(summary = "根据唯一标识修改食源性疾病-患者病例信息。")
    @PutMapping("/cisRcFbdPatCases/{id:.+}")
    void updateCisRcFbdPatCase(@PathVariable("id") String id, @RequestBody @Valid CisRcFbdPatCaseEto cisRcFbdPatCaseEto);

    @Operation(summary = "根据唯一标识删除食源性疾病-患者病例信息。")
    @DeleteMapping("/cisRcFbdPatCases/{id:.+}")
    void deleteCisRcFbdPatCase(@PathVariable("id") String id);

}