package com.bjgoodwill.hip.ds.cis.rc.fbd.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "食源性疾病-标本采集")
public class CisRcFbdSampleEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -7738930747919302349L;

    @Schema(description = "病历编码")
    private String fbdCode;
    @Schema(description = "申请单号")
    private String applyCode;
    @Schema(description = "标本类型 字典fbdSampletype	")
    private String sampleType;
    @Schema(description = "标本编号")
    private String sampleNo;
    @Schema(description = "采样量")
    private Integer sampleSize;
    @Schema(description = "样本单位 字典fbd_sampleunit	")
    private String sampleUnit;
    @Schema(description = "采样日期")
    private LocalDateTime sampleDate;
    @Schema(description = "备注")
    private String remark;

    @NotBlank(message = "病历编码不能为空！")
    @Size(max = 64, message = "病历编码长度不能超过64个字符！")
    public String getFbdCode() {
        return fbdCode;
    }

    public void setFbdCode(String fbdCode) {
        this.fbdCode = StringUtils.trimToNull(fbdCode);
    }

    @Size(max = 64, message = "申请单号长度不能超过64个字符！")
    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = StringUtils.trimToNull(applyCode);
    }

    @Size(max = 16, message = "标本类型 字典fbdSampletype	长度不能超过16个字符！")
    public String getSampleType() {
        return sampleType;
    }

    public void setSampleType(String sampleType) {
        this.sampleType = StringUtils.trimToNull(sampleType);
    }

    @Size(max = 64, message = "标本编号长度不能超过64个字符！")
    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = StringUtils.trimToNull(sampleNo);
    }

    public Integer getSampleSize() {
        return sampleSize;
    }

    public void setSampleSize(Integer sampleSize) {
        this.sampleSize = sampleSize;
    }

    @Size(max = 16, message = "样本单位 字典fbd_sampleunit	长度不能超过16个字符！")
    public String getSampleUnit() {
        return sampleUnit;
    }

    public void setSampleUnit(String sampleUnit) {
        this.sampleUnit = StringUtils.trimToNull(sampleUnit);
    }

    public LocalDateTime getSampleDate() {
        return sampleDate;
    }

    public void setSampleDate(LocalDateTime sampleDate) {
        this.sampleDate = sampleDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = StringUtils.trimToNull(remark);
    }
}