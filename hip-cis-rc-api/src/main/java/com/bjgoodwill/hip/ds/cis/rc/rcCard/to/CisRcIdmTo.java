package com.bjgoodwill.hip.ds.cis.rc.rcCard.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "传染病报卡")
public class CisRcIdmTo extends RcCardTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -4190903657405802943L;

    @Schema(description = "是否首诊 0否 1 是")
    private Boolean fristDiagnoseFlag;
    @Schema(description = "病例分类1")
    private String caseClass1;
    @Schema(description = "病例分类2")
    private String caseClass2;
    @Schema(description = "有无附卡 0无 1有")
    private Boolean additionFlag;
    @Schema(description = "订正病名")
    private String lastDiseaseName;
    @Schema(description = "订正人")
    private String lastUpdateUser;
    @Schema(description = "订正日期")
    private LocalDateTime lastUpdateDate;
    @Schema(description = "退卡处理 打回：1；打回后重新提交：0；")
    private Boolean backSolveFlag;
    @Schema(description = "退卡原因")
    private String backRemarks;

    public Boolean getFristDiagnoseFlag() {
        return fristDiagnoseFlag;
    }

    public void setFristDiagnoseFlag(Boolean fristDiagnoseFlag) {
        this.fristDiagnoseFlag = fristDiagnoseFlag;
    }

    public String getCaseClass1() {
        return caseClass1;
    }

    public void setCaseClass1(String caseClass1) {
        this.caseClass1 = caseClass1;
    }

    public String getCaseClass2() {
        return caseClass2;
    }

    public void setCaseClass2(String caseClass2) {
        this.caseClass2 = caseClass2;
    }

    public Boolean getAdditionFlag() {
        return additionFlag;
    }

    public void setAdditionFlag(Boolean additionFlag) {
        this.additionFlag = additionFlag;
    }

    public String getLastDiseaseName() {
        return lastDiseaseName;
    }

    public void setLastDiseaseName(String lastDiseaseName) {
        this.lastDiseaseName = lastDiseaseName;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public LocalDateTime getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(LocalDateTime lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Boolean getBackSolveFlag() {
        return backSolveFlag;
    }

    public void setBackSolveFlag(Boolean backSolveFlag) {
        this.backSolveFlag = backSolveFlag;
    }

    public String getBackRemarks() {
        return backRemarks;
    }

    public void setBackRemarks(String backRemarks) {
        this.backRemarks = backRemarks;
    }

}