package com.bjgoodwill.hip.ds.cis.rc.rcCard.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcIdmEto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcIdmNto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcIdmQto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcIdmTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "传染病报卡领域服务", description = "传染病报卡领域服务")
public interface CisRcIdmService {

    @Operation(summary = "根据查询条件对传染病报卡进行查询。")
    @GetMapping("/cisRcIdms")
    List<CisRcIdmTo> getCisRcIdms(@ParameterObject @SpringQueryMap CisRcIdmQto cisRcIdmQto);

    @Operation(summary = "根据查询条件对传染病报卡进行分页查询。")
    @GetMapping("/cisRcIdms/pages")
    GridResultSet<CisRcIdmTo> getCisRcIdmPage(@ParameterObject @SpringQueryMap CisRcIdmQto cisRcIdmQto);

    @Operation(summary = "根据唯一标识返回传染病报卡。")
    @GetMapping("/cisRcIdms/{id:.+}")
    CisRcIdmTo getCisRcIdmById(@PathVariable("id") String id);

    @Operation(summary = "创建传染病报卡。")
    @PostMapping("/cisRcIdms")
    CisRcIdmTo createCisRcIdm(@RequestBody @Valid CisRcIdmNto cisRcIdmNto);

    @Operation(summary = "根据唯一标识修改传染病报卡。")
    @PutMapping("/cisRcIdms/{id:.+}")
    void updateCisRcIdm(@PathVariable("id") String id, @RequestBody @Valid CisRcIdmEto cisRcIdmEto);

    @Operation(summary = "根据唯一标识删除传染病报卡。")
    @DeleteMapping("/cisRcIdms/{id:.+}")
    void deleteCisRcIdm(@PathVariable("id") String id);

}