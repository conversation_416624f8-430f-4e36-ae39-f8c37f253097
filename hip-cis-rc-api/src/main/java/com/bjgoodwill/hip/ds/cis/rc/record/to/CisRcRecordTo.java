package com.bjgoodwill.hip.ds.cis.rc.record.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.RecordTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "报卡执行记录")
public class CisRcRecordTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -5436158977368288783L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "报卡父类标识")
    private String rcCardId;
    @Schema(description = "操作类型")
    private RecordTypeEnum recordType;
    @Schema(description = "内容")
    private String content;
    @Schema(description = "订正诊断编码")
    private String lastDiagnosisCode;
    @Schema(description = "订正诊断名称")
    private String lastDiagnosisName;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "逻辑删除标记")
    private boolean deleted;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRcCardId() {
        return rcCardId;
    }

    public void setRcCardId(String rcCardId) {
        this.rcCardId = rcCardId;
    }

    public RecordTypeEnum getRecordType() {
        return recordType;
    }

    public void setRecordType(RecordTypeEnum recordType) {
        this.recordType = recordType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getLastDiagnosisCode() {
        return lastDiagnosisCode;
    }

    public void setLastDiagnosisCode(String lastDiagnosisCode) {
        this.lastDiagnosisCode = lastDiagnosisCode;
    }

    public String getLastDiagnosisName() {
        return lastDiagnosisName;
    }

    public void setLastDiagnosisName(String lastDiagnosisName) {
        this.lastDiagnosisName = lastDiagnosisName;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisRcRecordTo other = (CisRcRecordTo) obj;
        return Objects.equals(id, other.id);
    }
}