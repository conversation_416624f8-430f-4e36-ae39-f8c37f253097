package com.bjgoodwill.hip.ds.cis.rc.fbd.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "食源性疾病-病例检查")
public class CisRcFbdCaseExaminationQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -8774046251628559049L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "病历编码")
    private String fbdCode;
    @Schema(description = "病例检查类型：ps主要症状与体征（字典fbd_symptomsign）、")
    private String ceType;
    @Schema(description = "编码")
    private String ceCode;
    @Schema(description = "值")
    private String ceValue;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getFbdCode() {
        return fbdCode;
    }

    public void setFbdCode(String fbdCode) {
        this.fbdCode = fbdCode;
    }

    public String getCeType() {
        return ceType;
    }

    public void setCeType(String ceType) {
        this.ceType = ceType;
    }

    public String getCeCode() {
        return ceCode;
    }

    public void setCeCode(String ceCode) {
        this.ceCode = ceCode;
    }

    public String getCeValue() {
        return ceValue;
    }

    public void setCeValue(String ceValue) {
        this.ceValue = ceValue;
    }
}