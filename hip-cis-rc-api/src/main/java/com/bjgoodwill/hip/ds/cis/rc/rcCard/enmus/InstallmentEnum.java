package com.bjgoodwill.hip.ds.cis.rc.rcCard.enmus;

public enum InstallmentEnum {

    ONE_INSTALLMENT("1", "0-1"),
    TWO_INSTALLMENT("2", "2"),
    THREE_INSTALLMENT("3", "3"),
    FOUR_INSTALLMENT("4", "4"),
    FIVE_INSTALLMENT("5", "无法判定");

    private String code;
    private String name;

    private InstallmentEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}