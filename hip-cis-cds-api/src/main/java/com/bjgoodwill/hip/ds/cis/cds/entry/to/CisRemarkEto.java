package com.bjgoodwill.hip.ds.cis.cds.entry.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.ds.cis.cds.enmus.EntryTempSubTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "医生词条")
public class CisRemarkEto extends CisEntryTempEto {

    @Serial
    private static final long serialVersionUID = -7484197229960115435L;

    @Schema(description = "医嘱项目")
    private SystemTypeEnum systemType;
    @Schema(description = "子类型")
    private EntryTempSubTypeEnum subType;

    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }

    public EntryTempSubTypeEnum getSubType() {
        return subType;
    }

    public void setSubType(EntryTempSubTypeEnum subType) {
        this.subType = subType;
    }
}