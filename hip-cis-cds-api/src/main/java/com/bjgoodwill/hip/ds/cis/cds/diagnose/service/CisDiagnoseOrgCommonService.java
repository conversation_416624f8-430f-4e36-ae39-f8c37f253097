package com.bjgoodwill.hip.ds.cis.cds.diagnose.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseOrgCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseOrgCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseOrgCommonTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "科室常用诊断领域服务", description = "科室常用诊断领域服务")
public interface CisDiagnoseOrgCommonService extends CisDiagnoseCommonService {

    @Operation(summary = "根据查询条件对科室常用诊断进行查询。")
    @GetMapping("/cisDiagnoseOrgCommons")
    List<CisDiagnoseOrgCommonTo> getCisDiagnoseOrgCommons(@ParameterObject @SpringQueryMap CisDiagnoseOrgCommonQto cisDiagnoseOrgCommonQto);

    @Operation(summary = "根据查询条件对科室常用诊断进行分页查询。")
    @GetMapping("/cisDiagnoseOrgCommons/pages")
    GridResultSet<CisDiagnoseOrgCommonTo> getCisDiagnoseOrgCommonPage(@ParameterObject @SpringQueryMap CisDiagnoseOrgCommonQto cisDiagnoseOrgCommonQto);

    @Operation(summary = "根据唯一标识返回科室常用诊断。")
    @GetMapping("/cisDiagnoseOrgCommons/{id:.+}")
    CisDiagnoseOrgCommonTo getCisDiagnoseOrgCommonById(@PathVariable("id") String id);

    @Operation(summary = "创建科室常用诊断。")
    @PostMapping("/cisDiagnoseOrgCommons")
    CisDiagnoseOrgCommonTo createCisDiagnoseOrgCommon(@RequestBody @Valid CisDiagnoseOrgCommonNto cisDiagnoseOrgCommonNto);

    @Operation(summary = "取消科室常用。")
    @DeleteMapping("/cisDiagnoseOrgCommons/{id:.+}")
    void cancelCisDiagnoseOrgCommon(@PathVariable("id") String id);


//    @Operation(summary = "根据唯一标识修改科室常用诊断。")
//    @PutMapping("/cisDiagnoseOrgCommons/{id:.+}")
//    void updateCisDiagnoseOrgCommon(@PathVariable("id") String id, @RequestBody @Valid CisDiagnoseOrgCommonEto cisDiagnoseOrgCommonEto);


    @Operation(summary = "根据历史诊断修复常用诊断数据")
    @PostMapping("/cisDiagnoseOrgCommons/autostatistics")
    void autostatisticsOrgDiagnose(@RequestBody @NotNull List<CisDiagnoseOrgCommonNto> ntos);
}