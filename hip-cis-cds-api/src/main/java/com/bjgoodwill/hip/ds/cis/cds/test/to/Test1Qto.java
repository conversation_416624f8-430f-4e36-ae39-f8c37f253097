package com.bjgoodwill.hip.ds.cis.cds.test.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "Test1")
public class Test1Qto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -1759426621373099180L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "test2_id")
    private String test2_id;
    @Schema(description = "已启用")
    private Boolean enabled;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getTest2_id() {
        return test2_id;
    }

    public void setTest2_id(String test2_id) {
        this.test2_id = test2_id;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}