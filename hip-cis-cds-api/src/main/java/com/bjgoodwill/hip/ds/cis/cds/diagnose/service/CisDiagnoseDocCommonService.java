package com.bjgoodwill.hip.ds.cis.cds.diagnose.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseDocCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseDocCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseDocCommonTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "个人常用诊断领域服务", description = "个人常用诊断领域服务")
public interface CisDiagnoseDocCommonService extends CisDiagnoseCommonService {

    @Operation(summary = "根据查询条件对个人常用诊断进行查询。")
    @GetMapping("/cisDiagnoseDocCommons")
    List<CisDiagnoseDocCommonTo> getCisDiagnoseDocCommons(@ParameterObject @SpringQueryMap CisDiagnoseDocCommonQto cisDiagnoseDocCommonQto);

    @Operation(summary = "根据查询条件对个人常用诊断进行分页查询。")
    @GetMapping("/cisDiagnoseDocCommons/pages")
    GridResultSet<CisDiagnoseDocCommonTo> getCisDiagnoseDocCommonPage(@ParameterObject @SpringQueryMap CisDiagnoseDocCommonQto cisDiagnoseDocCommonQto);

    @Operation(summary = "根据唯一标识返回个人常用诊断。")
    @GetMapping("/cisDiagnoseDocCommons/{id:.+}")
    CisDiagnoseDocCommonTo getCisDiagnoseDocCommonById(@PathVariable("id") String id);

    @Operation(summary = "创建个人常用诊断。")
    @PostMapping("/cisDiagnoseDocCommons")
    CisDiagnoseDocCommonTo createCisDiagnoseDocCommon(@RequestBody @Valid CisDiagnoseDocCommonNto cisDiagnoseDocCommonNto);

    @Operation(summary = "取消个人常用。")
    @DeleteMapping("/cisDiagnoseDocCommons/{id:.+}")
    void cancelCisDiagnoseDocCommon(@PathVariable("id") String id);

//    @Operation(summary = "根据唯一标识修改个人常用诊断。")
//    @PutMapping("/cisDiagnoseDocCommons/{id:.+}")
//    void updateCisDiagnoseDocCommon(@PathVariable("id") String id, @RequestBody @Valid CisDiagnoseDocCommonEto cisDiagnoseDocCommonEto);

    @Operation(summary = "根据历史诊断修复常用诊断数据")
    @PostMapping("/cisDiagnoseDocCommons/autostatistics")
    void autostatisticsDocDiagnose(@RequestBody @NotNull List<CisDiagnoseDocCommonNto> ntos);

}