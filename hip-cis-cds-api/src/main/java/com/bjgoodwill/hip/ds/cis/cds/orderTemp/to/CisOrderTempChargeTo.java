package com.bjgoodwill.hip.ds.cis.cds.orderTemp.to;

import com.bjgoodwill.hip.business.util.econ.enums.SystemItemClassEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "查询组套明细费用表出参")
public class CisOrderTempChargeTo implements Serializable {

	@Serial
    private static final long serialVersionUID = -950287312883753374L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "tempDetailId")
    private String tempDetailId;
    @Schema(description = "收费项目编码")
    private String priceItemCode;
    @Schema(description = "收费项目名称")
    private String priceItemName;
    @Schema(description = "系统项目分类")
    private SystemItemClassEnum systemItemClass;
    @Schema(description = "固定费用")
    private Boolean isFixed;
    @Schema(description = "规格")
    private String packageSpec;
    @Schema(description = "单价")
    private BigDecimal price;
    @Schema(description = "单位")
    private String unit;
    @Schema(description = "单位名称")
    private String unitName;
    @Schema(description = "数量")
    private Double num;
    @Schema(description = "金额")
    private BigDecimal chageAmount;
    @Schema(description = "特限符合标识:1符合,0不符合")
    private Boolean limitConformFlag;
    @Schema(description = "SPD高值耗材唯一码")
    private String barCode;
    @Schema(description = "执行科室编码")
    private String executeOrgCode;
    @Schema(description = "执行科室名称")
    private String executeOrgName;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;

    public String getId() {
    	return id;
    }

    public void setId(String id) {
    	this.id = id;
    }

    public String getTempDetailId() {
    	return tempDetailId;
    }

    public void setTempDetailId(String tempDetailId) {
    	this.tempDetailId = tempDetailId;
    }

    public String getPriceItemCode() {
    	return priceItemCode;
    }

    public void setPriceItemCode(String priceItemCode) {
    	this.priceItemCode = priceItemCode;
    }

    public String getPriceItemName() {
    	return priceItemName;
    }

    public void setPriceItemName(String priceItemName) {
    	this.priceItemName = priceItemName;
    }

    public SystemItemClassEnum getSystemItemClass() {
        return systemItemClass;
    }

    public void setSystemItemClass(SystemItemClassEnum systemItemClass) {
        this.systemItemClass = systemItemClass;
    }

    public Boolean getIsFixed() {
    	return isFixed;
    }

    public void setIsFixed(Boolean isFixed) {
    	this.isFixed = isFixed;
    }

    public String getPackageSpec() {
    	return packageSpec;
    }

    public void setPackageSpec(String packageSpec) {
    	this.packageSpec = packageSpec;
    }

    public BigDecimal getPrice() {
    	return price;
    }

    public void setPrice(BigDecimal price) {
    	this.price = price;
    }

    public String getUnit() {
    	return unit;
    }

    public void setUnit(String unit) {
    	this.unit = unit;
    }

    public String getUnitName() {
    	return unitName;
    }

    public void setUnitName(String unitName) {
    	this.unitName = unitName;
    }

    public Double getNum() {
    	return num;
    }

    public void setNum(Double num) {
    	this.num = num;
    }

    public BigDecimal getChageAmount() {
    	return chageAmount;
    }

    public void setChageAmount(BigDecimal chageAmount) {
    	this.chageAmount = chageAmount;
    }

    public Boolean getLimitConformFlag() {
    	return limitConformFlag;
    }

    public void setLimitConformFlag(Boolean limitConformFlag) {
    	this.limitConformFlag = limitConformFlag;
    }

    public String getBarCode() {
    	return barCode;
    }

    public void setBarCode(String barCode) {
    	this.barCode = barCode;
    }

    public String getExecuteOrgCode() {
    	return executeOrgCode;
    }

    public void setExecuteOrgCode(String executeOrgCode) {
    	this.executeOrgCode = executeOrgCode;
    }

    public String getExecuteOrgName() {
    	return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
    	this.executeOrgName = executeOrgName;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CisOrderTempChargeTo other = (CisOrderTempChargeTo) obj;
		return Objects.equals(id, other.id);
	}
}