package com.bjgoodwill.hip.ds.cis.cds.test.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Schema(description = "Test1")
public class Test1To implements Serializable {

    @Serial
    private static final long serialVersionUID = -199999570374059782L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "test2_id")
    private String test2_id;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "已启用")
    private boolean enabled;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTest2_id() {
        return test2_id;
    }

    public void setTest2_id(String test2_id) {
        this.test2_id = test2_id;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        Test1To other = (Test1To) obj;
        return Objects.equals(id, other.id);
    }
}