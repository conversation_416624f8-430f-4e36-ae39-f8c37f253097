package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "科室药品开立常用用法和频次信息统计")
public class CisDrugUsageFreqCommonQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -1494695845684703286L;

    @Schema(description = "模糊查询文本")
    private String text;

    @Schema(description = "开立科室编码")
    private String deptCode;

    @Schema(description = "药品编码")
    private String drugCode;

    @Schema(description = "用法编码")
    private String usageCode;

    @Schema(description = "频次编码")
    private String frequencyCode;

    @Schema(description = "次数")
    private Integer num;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    public String getUsageCode() {
        return usageCode;
    }

    public void setUsageCode(String usageCode) {
        this.usageCode = usageCode;
    }

    public String getFrequencyCode() {
        return frequencyCode;
    }

    public void setFrequencyCode(String frequencyCode) {
        this.frequencyCode = frequencyCode;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }
}