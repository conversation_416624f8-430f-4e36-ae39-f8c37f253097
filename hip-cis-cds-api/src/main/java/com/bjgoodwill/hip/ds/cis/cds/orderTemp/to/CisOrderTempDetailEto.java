package com.bjgoodwill.hip.ds.cis.cds.orderTemp.to;


import com.bjgoodwill.hip.business.util.cis.common.enums.CisUnilateralEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "组套明细")
public class CisOrderTempDetailEto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -8130861655134583926L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "组套标识")
    private String cisOrderTempId;
    @Schema(description = "orderClass")
    private SystemTypeEnum orderClass;
    @Schema(description = "服务项目/药品编码")
    private String orderServiceCode;
    @Schema(description = "服务项目/药品名称")
    private String orderServiceName;
    @Schema(description = "医嘱名称")
    private String orderName;
    @Schema(description = "医嘱类型（临/长）")
    private OrderTypeEnum orderType;
    @Schema(description = "用法")
    private String usage;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "频次")
    private String frequency;
    @Schema(description = "频次名称")
    private String frequencyName;
    @Schema(description = "每次剂量")
    private Double dosage;
    @Schema(description = "剂量单位")
    private String dosageUnit;
    @Schema(description = "剂量单位名称")
    private String dosageUnitName;
    @Schema(description = "草药煎法")
    private String madeMethod;
    @Schema(description = "草药煎法名称")
    private String madeMethodName;
    @Schema(description = "付数")
    private Integer doseNum;
    @Schema(description = "疗程")
    private Double treatmentCourse;
    @Schema(description = "疗程单位")
    private String treatmentCourseUnit;
    @Schema(description = "疗程单位名称")
    private String treatmentCourseUnitName;
    @Schema(description = "开立数量")
    private Double num;
    @Schema(description = "开立单位")
    private String unit;
    @Schema(description = "开立单位名称")
    private String unitName;
    @Schema(description = "包装总量")
    private Double packageNum;
    @Schema(description = "包装单位")
    private String packageUnit;
    @Schema(description = "包装单位名称")
    private String packageUnitName;
    @Schema(description = "每次持续时间")
    private Double continueTime;
    @Schema(description = "每次持续单位")
    private String continueUnit;
    @Schema(description = "是否皮试及皮试结果")
    private String skinTest;
    @Schema(description = "带药方式")
    private SbadmWayEnum sbadmWay;
    @Schema(description = "部位")
    private String humanOrgans;
    @Schema(description = "部位名称")
    private String humanOrgansName;
    @Schema(description = "标本")
    private String speciman;
    @Schema(description = "标本名称")
    private String specimanName;
    @Schema(description = "入路")
    private String approach;
    @Schema(description = "入路名称")
    private String approachName;
    @Schema(description = "实验方法")
    private String method;
    @Schema(description = "实验方法名称")
    private String methodName;
    @Schema(description = "辅助器械")
    private String instrument;
    @Schema(description = "辅助器械名称")
    private String instrumentName;
    @Schema(description = "范围")
    private String range;
    @Schema(description = "范围名称")
    private String rangeName;
    @Schema(description = "基本操作")
    private String operation;
    @Schema(description = "基本操作名称")
    private String operationName;
    @Schema(description = "方位")
    private String direction;
    @Schema(description = "方位名称")
    private String directionName;
    @Schema(description = "层数")
    private String layer;
    @Schema(description = "层数名称")
    private String layerName;
    @Schema(description = "执行科室")
    private String executeOrgCode;
    @Schema(description = "执行科室名称")
    private String executeOrgName;
    @Schema(description = "医嘱补充说明")
    private String remark;
    @Schema(description = "优先标记")
    private Boolean priorityFlag;
    @Schema(description = "是否必选")
    private Boolean requiredFlag;
    @Schema(description = "频次点")
    private String freqTimepoint;
    @Schema(description = "序号")
    private Double sortNo;
    @Schema(description = "草药一付包数")
    private Integer cdrugPackNum;
    @Schema(description = "草药每包毫升数")
    private Integer cdrugPackMl;
    @Schema(description = "具体执行地点")
    private String realExecLocation;
    @Schema(description = "滴速")
    private String dripSpeed;
    @Schema(description = "滴速单位")
    private String dripSpeedUnit;
    @Schema(description = "滴速单位名称")
    private String dripSpeedUnitName;
    @Schema(description = "穴位")
    private String acupoint;
    @Schema(description = "穴位名称")
    private String acupointName;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "加工费编码")
    private String processedPriceCode;
    @NotBlank(message = "扩展编码不能为空！")
    @Schema(description = "扩展编码")
    private String extTypeCode;
    @Schema(description = "单侧标记，左，右，双侧")
    private CisUnilateralEnum unilateralFlag;
    @Schema(description = "草药属性（DrugHerbsProEnum）")
    private String herbsProCode;

    @NotBlank(message = "标识不能为空！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Size(max = 50, message = "组套标识长度不能超过50个字符！")
    public String getCisOrderTempId() {
        return cisOrderTempId;
    }

    public void setCisOrderTempId(String cisOrderTempId) {
        this.cisOrderTempId = StringUtils.trimToNull(cisOrderTempId);
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    @NotBlank(message = "服务项目/药品编码不能为空！")
    public String getOrderServiceCode() {
        return orderServiceCode;
    }

    public void setOrderServiceCode(String orderServiceCode) {
        this.orderServiceCode = StringUtils.trimToNull(orderServiceCode);
    }

    public String getOrderServiceName() {
        return orderServiceName;
    }

    public void setOrderServiceName(String orderServiceName) {
        this.orderServiceName = orderServiceName;
    }

    @NotBlank(message = "医嘱名称不能为空！")
    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = StringUtils.trimToNull(orderName);
    }

    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderTypeEnum orderType) {
        this.orderType = orderType;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = StringUtils.trimToNull(usage);
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = StringUtils.trimToNull(frequency);
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = StringUtils.trimToNull(dosageUnit);
    }

    public String getMadeMethod() {
        return madeMethod;
    }

    public void setMadeMethod(String madeMethod) {
        this.madeMethod = StringUtils.trimToNull(madeMethod);
    }

    public Integer getDoseNum() {
        return doseNum;
    }

    public void setDoseNum(Integer doseNum) {
        this.doseNum = doseNum;
    }

    public Double getTreatmentCourse() {
        return treatmentCourse;
    }

    public void setTreatmentCourse(Double treatmentCourse) {
        this.treatmentCourse = treatmentCourse;
    }

    public String getTreatmentCourseUnit() {
        return treatmentCourseUnit;
    }

    public void setTreatmentCourseUnit(String treatmentCourseUnit) {
        this.treatmentCourseUnit = StringUtils.trimToNull(treatmentCourseUnit);
    }

    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = StringUtils.trimToNull(unit);
    }

    public Double getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    public String getPackageUnit() {
        return packageUnit;
    }

    public void setPackageUnit(String packageUnit) {
        this.packageUnit = StringUtils.trimToNull(packageUnit);
    }

    public Double getContinueTime() {
        return continueTime;
    }

    public void setContinueTime(Double continueTime) {
        this.continueTime = continueTime;
    }

    public String getContinueUnit() {
        return continueUnit;
    }

    public void setContinueUnit(String continueUnit) {
        this.continueUnit = StringUtils.trimToNull(continueUnit);
    }

    public String getSkinTest() {
        return skinTest;
    }

    public void setSkinTest(String skinTest) {
        this.skinTest = StringUtils.trimToNull(skinTest);
    }

    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = StringUtils.trimToNull(humanOrgans);
    }

    public String getSpeciman() {
        return speciman;
    }

    public void setSpeciman(String speciman) {
        this.speciman = StringUtils.trimToNull(speciman);
    }

    public String getApproach() {
        return approach;
    }

    public void setApproach(String approach) {
        this.approach = StringUtils.trimToNull(approach);
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = StringUtils.trimToNull(method);
    }

    public String getInstrument() {
        return instrument;
    }

    public void setInstrument(String instrument) {
        this.instrument = StringUtils.trimToNull(instrument);
    }

    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = StringUtils.trimToNull(range);
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = StringUtils.trimToNull(operation);
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = StringUtils.trimToNull(direction);
    }

    public String getLayer() {
        return layer;
    }

    public void setLayer(String layer) {
        this.layer = StringUtils.trimToNull(layer);
    }

    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    public void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = StringUtils.trimToNull(executeOrgCode);
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = StringUtils.trimToNull(remark);
    }

    public Boolean getPriorityFlag() {
        return priorityFlag;
    }

    public void setPriorityFlag(Boolean priorityFlag) {
        this.priorityFlag = priorityFlag;
    }

    public Boolean getRequiredFlag() {
        return requiredFlag;
    }

    public void setRequiredFlag(Boolean requiredFlag) {
        this.requiredFlag = requiredFlag;
    }

    public String getFreqTimepoint() {
        return freqTimepoint;
    }

    public void setFreqTimepoint(String freqTimepoint) {
        this.freqTimepoint = StringUtils.trimToNull(freqTimepoint);
    }

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    public Integer getCdrugPackNum() {
        return cdrugPackNum;
    }

    public void setCdrugPackNum(Integer cdrugPackNum) {
        this.cdrugPackNum = cdrugPackNum;
    }

    public Integer getCdrugPackMl() {
        return cdrugPackMl;
    }

    public void setCdrugPackMl(Integer cdrugPackMl) {
        this.cdrugPackMl = cdrugPackMl;
    }

    public String getRealExecLocation() {
        return realExecLocation;
    }

    public void setRealExecLocation(String realExecLocation) {
        this.realExecLocation = StringUtils.trimToNull(realExecLocation);
    }

    public String getDripSpeed() {
        return dripSpeed;
    }

    public void setDripSpeed(String dripSpeed) {
        this.dripSpeed = StringUtils.trimToNull(dripSpeed);
    }

    public String getDripSpeedUnit() {
        return dripSpeedUnit;
    }

    public void setDripSpeedUnit(String dripSpeedUnit) {
        this.dripSpeedUnit = StringUtils.trimToNull(dripSpeedUnit);
    }

    public String getAcupoint() {
        return acupoint;
    }

    public void setAcupoint(String acupoint) {
        this.acupoint = StringUtils.trimToNull(acupoint);
    }

    public String getAcupointName() {
        return acupointName;
    }

    public void setAcupointName(String acupointName) {
        this.acupointName = StringUtils.trimToNull(acupointName);
    }

    @NotNull(message = "版本不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    public String getMadeMethodName() {
        return madeMethodName;
    }

    public void setMadeMethodName(String madeMethodName) {
        this.madeMethodName = madeMethodName;
    }

    public String getTreatmentCourseUnitName() {
        return treatmentCourseUnitName;
    }

    public void setTreatmentCourseUnitName(String treatmentCourseUnitName) {
        this.treatmentCourseUnitName = treatmentCourseUnitName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPackageUnitName() {
        return packageUnitName;
    }

    public void setPackageUnitName(String packageUnitName) {
        this.packageUnitName = packageUnitName;
    }

    public String getHumanOrgansName() {
        return humanOrgansName;
    }

    public void setHumanOrgansName(String humanOrgansName) {
        this.humanOrgansName = humanOrgansName;
    }

    public String getSpecimanName() {
        return specimanName;
    }

    public void setSpecimanName(String specimanName) {
        this.specimanName = specimanName;
    }

    public String getApproachName() {
        return approachName;
    }

    public void setApproachName(String approachName) {
        this.approachName = approachName;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getInstrumentName() {
        return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }

    public String getRangeName() {
        return rangeName;
    }

    public void setRangeName(String rangeName) {
        this.rangeName = rangeName;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getDirectionName() {
        return directionName;
    }

    public void setDirectionName(String directionName) {
        this.directionName = directionName;
    }

    public String getLayerName() {
        return layerName;
    }

    public void setLayerName(String layerName) {
        this.layerName = layerName;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    public String getDripSpeedUnitName() {
        return dripSpeedUnitName;
    }

    public void setDripSpeedUnitName(String dripSpeedUnitName) {
        this.dripSpeedUnitName = dripSpeedUnitName;
    }

    public String getExtTypeCode() {
        return extTypeCode;
    }

    public void setExtTypeCode(String extTypeCode) {
        this.extTypeCode = extTypeCode;
    }

    public String getProcessedPriceCode() {
        return processedPriceCode;
    }

    public void setProcessedPriceCode(String processedPriceCode) {
        this.processedPriceCode = processedPriceCode;
    }

    public CisUnilateralEnum getUnilateralFlag() {
        return unilateralFlag;
    }

    public void setUnilateralFlag(CisUnilateralEnum unilateralFlag) {
        this.unilateralFlag = unilateralFlag;
    }

    public String getHerbsProCode() {
        return herbsProCode;
    }

    public void setHerbsProCode(String herbsProCode) {
        this.herbsProCode = herbsProCode;
    }
}