package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "药品用法频次同步")
public class CisDrugUsageFreqSyncNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -417794536568076133L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "开立科室编码")
    private String deptCode;
    @Schema(description = "药品编码")
    private String drugCode;
    @Schema(description = "用法编码")
    private String usageCode;
    @Schema(description = "频次编码")
    private String frequencyCode;
    @Schema(description = "剂量")
    private String dosage;
    @Schema(description = "剂量单位")
    private String dosageUnit;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = StringUtils.trimToNull(deptCode);
    }

    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = StringUtils.trimToNull(drugCode);
    }

    public String getUsageCode() {
        return usageCode;
    }

    public void setUsageCode(String usageCode) {
        this.usageCode = StringUtils.trimToNull(usageCode);
    }

    public String getFrequencyCode() {
        return frequencyCode;
    }

    public void setFrequencyCode(String frequencyCode) {
        this.frequencyCode = StringUtils.trimToNull(frequencyCode);
    }

    public String getDosage() {
        return dosage;
    }

    public void setDosage(String dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }
}