package com.bjgoodwill.hip.ds.cis.cds.orderTemp.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import com.bjgoodwill.hip.ds.cis.cds.enmus.TempRangeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "组套")
public class CisOrderTempQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -1642193946215500564L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "tempType")
    private VisitTypeEnum tempType;
    @Schema(description = "tempRange")
    private TempRangeEnum tempRange;
    @Schema(description = "拼音码")
    private String inputPy;
    @Schema(description = "父级主键")
    private String parentId;
    @Schema(description = "是否文件夹")
    private Boolean groupFlag;
    @Schema(description = "hospitalCode")
    private String hospitalCode;
    @Schema(description = "机构编码")
    private String orgCode;
    @Schema(description = "医生编码")
    private String doctorCode;
    @Schema(description = "院区编码")
    private String hospitalArea;
    @Schema(description = "组套类型")
    private SystemTypeEnum systemType;
    @Schema(description = "行政科室编码")
    private String deptCode;
    @Schema(description = "工作组类型")
    private String workGroupType;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public VisitTypeEnum getTempType() {
        return tempType;
    }

    public void setTempType(VisitTypeEnum tempType) {
        this.tempType = tempType;
    }

    public TempRangeEnum getTempRange() {
        return tempRange;
    }

    public void setTempRange(TempRangeEnum tempRange) {
        this.tempRange = tempRange;
    }

    public String getInputPy() {
        return inputPy;
    }

    public void setInputPy(String inputPy) {
        this.inputPy = inputPy;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Boolean getGroupFlag() {
        return groupFlag;
    }

    public void setGroupFlag(Boolean groupFlag) {
        this.groupFlag = groupFlag;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getDoctorCode() {
        return doctorCode;
    }

    public void setDoctorCode(String doctorCode) {
        this.doctorCode = doctorCode;
    }

    public String getHospitalArea() {
        return hospitalArea;
    }

    public void setHospitalArea(String hospitalArea) {
        this.hospitalArea = hospitalArea;
    }

    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getWorkGroupType() {
        return workGroupType;
    }

    public void setWorkGroupType(String workGroupType) {
        this.workGroupType = workGroupType;
    }
}