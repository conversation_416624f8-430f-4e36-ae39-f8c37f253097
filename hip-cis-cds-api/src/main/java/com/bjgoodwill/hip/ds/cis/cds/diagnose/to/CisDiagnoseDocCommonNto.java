package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "个人常用诊断")
public class CisDiagnoseDocCommonNto extends CisDiagnoseCommonNto {

    @Serial
    private static final long serialVersionUID = -8268164825519616256L;

    @Schema(description = "医生编码")
    private String docCode;

    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = StringUtils.trimToNull(docCode);
    }
}