package com.bjgoodwill.hip.ds.cis.cds.orderTemp.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "组套领域服务", description = "组套领域服务")
public interface CisOrderTempService {

    @Operation(summary = "P0根据查询条件对组套进行查询。")
    @GetMapping("/cisOrderTemps/temp/list")
    List<CisOrderTempTo> getCisOrderTemps(@ParameterObject @SpringQueryMap CisOrderTempQto cisOrderTempQto);

    @Operation(summary = "根据查询条件对组套进行分页查询。")
    @GetMapping("/cisOrderTemps/temp/pages")
    GridResultSet<CisOrderTempTo> getCisOrderTempPage(@ParameterObject @SpringQueryMap CisOrderTempQto cisOrderTempQto);

    @Operation(summary = "P0根据唯一标识返回组套。")
    @GetMapping("/cisOrderTemps/temp/get/{id:.+}")
    CisOrderTempTo getCisOrderTempById(@PathVariable("id") String id);

    @Operation(summary = "P0创建组套。")
    @PostMapping("/cisOrderTemps/temp/add")
    CisOrderTempTo createCisOrderTemp(@RequestBody @Valid CisOrderTempNto cisOrderTempNto);

    @Operation(summary = "P0根据唯一标识修改组套。")
    @PutMapping("/cisOrderTemps/temp/update/{id:.+}")
    void updateCisOrderTemp(@PathVariable("id") String id, @RequestBody @Valid CisOrderTempEto cisOrderTempEto);

    @Operation(summary = "P0根据唯一标识删除组套。")
    @DeleteMapping("/cisOrderTemps/temp/delete/{id:.+}")
    void deleteCisOrderTemp(@PathVariable("id") String id);

    @Operation(summary = "P0根据唯一标识修改组套明细。")
    @PutMapping("/cisOrderTemps/{id:.+}/detail/{detailId:.+}")
    void updateCisOrderTempDetail(@PathVariable("id") String id, @PathVariable("detailId") String detailId, @RequestBody @Valid CisOrderTempDetailEto cisOrderTempDetailEto);

    @Operation(summary = "P0根据唯一标识删除组套明细。")
    @DeleteMapping("/cisOrderTemps/{id:.+}/detail/delete/{detailId:.+}")
    void deleteCisOrderTempDetail(@PathVariable("id") String id, @PathVariable("detailId") String detailId);

    @Operation(summary = "P0根据唯一标识批量删除组套明细。")
    @DeleteMapping("/cisOrderTemps/{id:.+}/detail/deletebatch")
    void deleteBatchCisOrderTempDetail(@PathVariable("id") String id, @RequestParam("detailId") List<String> detailIds);

    @Operation(summary = "P0新增明细。")
    @PostMapping("/cisOrderTemps/{id:.+}/detail/add")
    CisOrderTempTo addCisOrderTempDetail(@PathVariable("id") String id, @RequestBody @Valid List<CisOrderTempDetailNto> cisOrderTempDetailNtos);

    @Operation(summary = "P0批量修改组套明细。")
    @PutMapping("/cisOrderTemps/detail/update")
    void updateCisOrderTempDetails(@RequestBody @Valid List<CisOrderTempDetailEto> cisOrderTempDetailEtos);

    @Operation(summary = "P0根据查询条件对组套进行查询,返回树结构,按tab页。")
    @GetMapping("/cisOrderTempsTree/list")
    List<CisOrderTempTo> getCisOrderTempsTree(@ParameterObject @SpringQueryMap CisOrderTempQto cisOrderTempQto);

    @Operation(summary = "P0根据查询条件对组套进行查询,返回全部树结构。")
    @GetMapping("/cisOrderTempsTree/list/all")
    List<CisOrderTempTo> getCisOrderTempsTreeAll(@ParameterObject @SpringQueryMap CisOrderTempQto cisOrderTempQto);

    @Operation(summary = "P0根据查询条件对组套模板明细进行查询。")
    @GetMapping("/cisOrderTemps/details/list")
    List<CisOrderTempDetailTo> getCisOrderTempDetails(@ParameterObject @SpringQueryMap CisOrderTempDetailQto cisOrderTempDetailQto);

    @Operation(summary = "P0根据ids查询组套明细。")
    @GetMapping("/cisOrderTemps/details/list/ids")
    List<CisOrderTempDetailTo> getCisOrderTempDetailsByIds(@RequestParam("ids") List<String> ids);

    @Operation(summary = "P0根据组套id对组套明细进行查询。")
    @GetMapping("/cisOrderTemps/{tempId:.+}/details")
    List<CisOrderTempDetailTo> getCisOrderTempDetaisByTempId(@PathVariable("tempId") String tempId);

    @Operation(summary = "查询当前组套下组套明细最大序号。")
    @GetMapping("/cisOrderTemps/details/getMaxSrotNo")
    Double getCisOrderTempDetailMaxSrotNo(@RequestParam("orderTempId") String orderTempId);

    @Operation(summary = "P0组套排序。")
    @PutMapping("/cisOrderTemps/temp/sort")
    void updateCisOrderTempSort(@RequestBody @Valid List<String> idList);

    @Operation(summary = "P0改变路径。")
    @PutMapping("/cisOrderTemps/temp/changepath")
    void updateCisOrderTempPath(@RequestBody @Valid CisOrderTempEto.OrderTempChangePathEto changePathEto);

}