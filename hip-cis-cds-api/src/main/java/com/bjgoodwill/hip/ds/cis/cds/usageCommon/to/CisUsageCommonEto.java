package com.bjgoodwill.hip.ds.cis.cds.usageCommon.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "科室常用用法")
public class CisUsageCommonEto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -2110633888675121503L;

    @Schema(description = "用法类型")
    private String usageType;
    @Schema(description = "用法编码")
    private String usageCode;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "科室编码")
    private String orgCode;
    @Schema(description = "序号")
    private String usageNo;
    @Schema(description = "已启用")
    private boolean enabled;

    public String getUsageType() {
        return usageType;
    }

    public void setUsageType(String usageType) {
        this.usageType = StringUtils.trimToNull(usageType);
    }

    public String getUsageCode() {
        return usageCode;
    }

    public void setUsageCode(String usageCode) {
        this.usageCode = StringUtils.trimToNull(usageCode);
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = StringUtils.trimToNull(usageName);
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = StringUtils.trimToNull(orgCode);
    }

    public String getUsageNo() {
        return usageNo;
    }

    public void setUsageNo(String usageNo) {
        this.usageNo = StringUtils.trimToNull(usageNo);
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}