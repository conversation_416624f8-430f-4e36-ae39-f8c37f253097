package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.time.LocalDateTime;

@Schema(description = "同步患者诊断基础数据")
public class CisDiagnoseBaseDataNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -8401222024387969006L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "诊断编码")
    private String diagnoseCode;
    @Schema(description = "诊断名称")
    private String diagnoseName;
    @Schema(description = "科室编码")
    private String orgCode;
    @Schema(description = "医生编码")
    private String docCode;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "诊断编码不能为空！")
    public String getDiagnoseCode() {
        return diagnoseCode;
    }

    public void setDiagnoseCode(String diagnoseCode) {
        this.diagnoseCode = StringUtils.trimToNull(diagnoseCode);
    }

    public String getDiagnoseName() {
        return diagnoseName;
    }

    public void setDiagnoseName(String diagnoseName) {
        this.diagnoseName = StringUtils.trimToNull(diagnoseName);
    }

    @NotBlank(message = "科室编码不能为空！")
    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = StringUtils.trimToNull(orgCode);
    }

    @NotBlank(message = "医生编码不能为空！")
    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = StringUtils.trimToNull(docCode);
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
}