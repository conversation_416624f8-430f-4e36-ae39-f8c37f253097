package com.bjgoodwill.hip.ds.cis.cds.diagnose.service;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;

@Tag(name = "常用诊断领域服务", description = "常用诊断领域服务")
public interface CisDiagnoseCommonService {

    @Operation(summary = "根据唯一标识启用常用诊断。")
    @PutMapping("/cisDiagnoseCommons/{id}/enable")
    void enableCisDiagnoseCommon(@PathVariable("id") String id);

    @Operation(summary = "根据唯一标识禁用常用诊断。")
    @PutMapping("/cisDiagnoseCommons/{id}/disable")
    void disableCisDiagnoseCommon(@PathVariable("id") String id);

}