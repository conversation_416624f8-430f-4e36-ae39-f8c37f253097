package com.bjgoodwill.hip.ds.cis.cds.test.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "Test1")
public class Test1Eto implements Serializable {

    @Serial
    private static final long serialVersionUID = -7168722855692826459L;

    @Schema(description = "test2_id")
    private String test2_id;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "已启用")
    private boolean enabled;

    public String getTest2_id() {
        return test2_id;
    }

    public void setTest2_id(String test2_id) {
        this.test2_id = StringUtils.trimToNull(test2_id);
    }

    @NotNull(message = "版本不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}