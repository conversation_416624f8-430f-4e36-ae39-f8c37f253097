package com.bjgoodwill.hip.ds.cis.cds.entry.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.ds.cis.cds.enmus.EntryTempSubTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "医生词条")
public class CisRemarkQto extends CisEntryTempQto {

    @Serial
    private static final long serialVersionUID = -3281790337493287756L;

    @Schema(description = "医嘱项目")
    private SystemTypeEnum systemType;
    @Schema(description = "工作组号")
    private String groupNumber;
    @Schema(description = "子类型")
    private EntryTempSubTypeEnum subType;


    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }

    public String getGroupNumber() {
        return groupNumber;
    }

    public void setGroupNumber(String groupNumber) {
        this.groupNumber = groupNumber;
    }

    public EntryTempSubTypeEnum getSubType() {
        return subType;
    }

    public void setSubType(EntryTempSubTypeEnum subType) {
        this.subType = subType;
    }
}