package com.bjgoodwill.hip.ds.cis.cds.orderTemp.to;

import com.bjgoodwill.hip.business.util.econ.enums.SystemItemClassEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "新增组套明细费用表入参")
public class CisOrderTempChargeNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2466417368535259209L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "tempDetailId")
    private String tempDetailId;
    @Schema(description = "收费项目编码")
    private String priceItemCode;
    @Schema(description = "收费项目名称")
    private String priceItemName;
    @Schema(description = "固定费用")
    private Boolean isFixed;
    @Schema(description = "规格")
    private String packageSpec;
    @Schema(description = "单价")
    private BigDecimal price;
    @Schema(description = "单位")
    private String unit;
    @Schema(description = "单位名称")
    private String unitName;
    @Schema(description = "数量")
    private Double num;
    @Schema(description = "金额")
    private BigDecimal chageAmount;
    @Schema(description = "特限符合标识:1符合,0不符合")
    private Boolean limitConformFlag;
    @Schema(description = "SPD高值耗材唯一码")
    private String barCode;
    @Schema(description = "执行科室编码")
    private String executeOrgCode;
    @Schema(description = "执行科室名称")
    private String executeOrgName;
    @Schema(description = "系统项目分类")
    private SystemItemClassEnum systemItemClass;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "tempDetailId不能为空！")
    public String getTempDetailId() {
        return tempDetailId;
    }

    public void setTempDetailId(String tempDetailId) {
        this.tempDetailId = StringUtils.trimToNull(tempDetailId);
    }

    @NotBlank(message = "收费项目编码不能为空！")
    public String getPriceItemCode() {
        return priceItemCode;
    }

    public void setPriceItemCode(String priceItemCode) {
        this.priceItemCode = StringUtils.trimToNull(priceItemCode);
    }

    public String getPriceItemName() {
        return priceItemName;
    }

    public void setPriceItemName(String priceItemName) {
        this.priceItemName = StringUtils.trimToNull(priceItemName);
    }

    @NotNull(message = "固定费用不能为空！")
    public Boolean getIsFixed() {
        return isFixed;
    }

    public void setIsFixed(Boolean isFixed) {
        this.isFixed = isFixed;
    }

    public String getPackageSpec() {
        return packageSpec;
    }

    public void setPackageSpec(String packageSpec) {
        this.packageSpec = StringUtils.trimToNull(packageSpec);
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = StringUtils.trimToNull(unit);
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = StringUtils.trimToNull(unitName);
    }

    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public BigDecimal getChageAmount() {
        return chageAmount;
    }

    public void setChageAmount(BigDecimal chageAmount) {
        this.chageAmount = chageAmount;
    }

    @NotNull(message = "特限符合标识:1符合,0不符合不能为空！")
    public Boolean getLimitConformFlag() {
        return limitConformFlag;
    }

    public void setLimitConformFlag(Boolean limitConformFlag) {
        this.limitConformFlag = limitConformFlag;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = StringUtils.trimToNull(barCode);
    }

    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    public void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = StringUtils.trimToNull(executeOrgCode);
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = StringUtils.trimToNull(executeOrgName);
    }

    @NotNull(message = "系统项目分类不能为空！")
    public SystemItemClassEnum getSystemItemClass() {
        return systemItemClass;
    }

    public void setSystemItemClass(SystemItemClassEnum systemItemClass) {
        this.systemItemClass = systemItemClass;
    }
}