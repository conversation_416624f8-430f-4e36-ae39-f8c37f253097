package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "常用诊断")
public class CisDiagnoseCommonQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -8400201094509383893L;

    @Schema(description = "模糊查询文本")
    private String text;

    @Schema(description = "人工维护的")
    private Boolean isFix;

    @Schema(description = "已启用")
    private Boolean enabled;

    private DiagnosisEnum diagnosisClass;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Boolean getIsFix() {
        return isFix;
    }

    public void setIsFix(Boolean isFix) {
        this.isFix = isFix;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public DiagnosisEnum getDiagnosisClass() {
        return diagnosisClass;
    }

    public void setDiagnosisClass(DiagnosisEnum diagnosisClass) {
        this.diagnosisClass = diagnosisClass;
    }
}