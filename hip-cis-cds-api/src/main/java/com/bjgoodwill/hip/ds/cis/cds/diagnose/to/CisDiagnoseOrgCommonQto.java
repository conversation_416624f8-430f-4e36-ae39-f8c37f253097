package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.io.Serial;

@Schema(description = "科室常用诊断")
public class CisDiagnoseOrgCommonQto extends CisDiagnoseCommonQto {

    @Serial
    private static final long serialVersionUID = -8423300895988385276L;

    @Schema(description = "科室编码")
    @NotBlank(message = "科室编码不能为空！")
    private String orgCode;


    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }
}