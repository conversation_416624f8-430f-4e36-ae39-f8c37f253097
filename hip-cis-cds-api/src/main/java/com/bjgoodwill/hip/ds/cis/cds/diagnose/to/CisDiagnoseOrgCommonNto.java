package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "科室常用诊断")
public class CisDiagnoseOrgCommonNto extends CisDiagnoseCommonNto {

    @Serial
    private static final long serialVersionUID = -3593379018273517223L;

    @Schema(description = "科室编码")
    private String orgCode;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = StringUtils.trimToNull(orgCode);
    }
}