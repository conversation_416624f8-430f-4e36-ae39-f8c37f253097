package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "同步患者诊断基础数据")
public class CisDiagnoseBaseDataQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -4464061933913251103L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "诊断编码")
    private String diagnoseCode;
    @Schema(description = "科室编码")
    private String orgCode;
    @Schema(description = "医生编码")
    private String docCode;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getDiagnoseCode() {
        return diagnoseCode;
    }

    public void setDiagnoseCode(String diagnoseCode) {
        this.diagnoseCode = diagnoseCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = docCode;
    }
}