package com.bjgoodwill.hip.ds.cis.cds.test.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "Test2")
public class Test2Qto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -2032824339402906908L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "已启用")
    private Boolean enabled;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}