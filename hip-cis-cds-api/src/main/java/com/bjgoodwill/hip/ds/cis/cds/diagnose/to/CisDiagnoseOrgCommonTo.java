package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "科室常用诊断")
public class CisDiagnoseOrgCommonTo extends CisDiagnoseCommonTo {

    @Serial
    private static final long serialVersionUID = -4808602417680475125L;

    @Schema(description = "科室编码")
    private String orgCode;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

}