package com.bjgoodwill.hip.ds.cis.cds.order.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonEto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "常用医嘱领域服务", description = "常用医嘱领域服务")
public interface CisOrderCommonService {

    @Operation(summary = "P0根据查询条件对常用医嘱进行查询。")
    @GetMapping("/cisOrderCommons")
    List<CisOrderCommonTo> getCisOrderCommons(@ParameterObject @SpringQueryMap CisOrderCommonQto cisOrderCommonQto);

    @Operation(summary = "根据查询条件对常用医嘱进行分页查询。")
    @GetMapping("/cisOrderCommons/pages")
    GridResultSet<CisOrderCommonTo> getCisOrderCommonPage(@ParameterObject @SpringQueryMap CisOrderCommonQto cisOrderCommonQto);

    @Operation(summary = "根据唯一标识返回常用医嘱。")
    @GetMapping("/cisOrderCommons/{id:.+}")
    CisOrderCommonTo getCisOrderCommonById(@PathVariable("id") String id);

    @Operation(summary = "创建常用医嘱。")
    @PostMapping("/cisOrderCommons")
    CisOrderCommonTo createCisOrderCommon(@RequestBody @Valid CisOrderCommonNto cisOrderCommonNto);

    @Operation(summary = "根据唯一标识修改常用医嘱。")
    @PutMapping("/cisOrderCommons/{id:.+}")
    void updateCisOrderCommon(@PathVariable("id") String id, @RequestBody @Valid CisOrderCommonEto cisOrderCommonEto);

    @Operation(summary = "根据唯一标识删除常用医嘱。")
    @DeleteMapping("/cisOrderCommons/{id:.+}")
    void deleteCisOrderCommon(@PathVariable("id") String id);

    @Operation(summary = "P0保存常用医嘱")
    @PostMapping("/cisOrderCommons/saveOrderCommons")
    void saveOrderCommons(@RequestBody @Valid List<CisOrderCommonNto> cisOrderCommonNtos);

    @Operation(summary = "根据医生科室查询权重最高的医嘱。")
    @GetMapping("/cisOrderCommons/top-weighted")
    CisOrderCommonTo getCisOrderCommonTop(@RequestParam("docCode") String docCode, @RequestParam("orgCode") String orgCode,
                                          @RequestParam(value = "rule", required = false) String rule);
}