package com.bjgoodwill.hip.ds.cis.cds.orderTemp.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "查询组套明细费用表入参")
public class CisOrderTempChargeQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -3652161663628773526L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "tempDetailId")
    private String tempDetailId;
    @Schema(description = "收费项目编码")
    private String priceItemCode;
    @Schema(description = "收费项目名称")
    private String priceItemName;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getTempDetailId() {
        return tempDetailId;
    }

    public void setTempDetailId(String tempDetailId) {
        this.tempDetailId = tempDetailId;
    }

    public String getPriceItemCode() {
        return priceItemCode;
    }

    public void setPriceItemCode(String priceItemCode) {
        this.priceItemCode = priceItemCode;
    }

    public String getPriceItemName() {
        return priceItemName;
    }

    public void setPriceItemName(String priceItemName) {
        this.priceItemName = priceItemName;
    }
}