package com.bjgoodwill.hip.ds.cis.cds.order.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderRecordNto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderRecordQto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderRecordTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Tag(name = "常用医嘱记录日志领域服务", description = "常用医嘱记录日志领域服务")
public interface CisOrderRecordService {

    @Operation(summary = "根据查询条件对常用医嘱记录日志进行查询。")
    @GetMapping("/cisOrderRecords")
    List<CisOrderRecordTo> getCisOrderRecords(@ParameterObject @SpringQueryMap CisOrderRecordQto cisOrderRecordQto);

    @Operation(summary = "根据查询条件对常用医嘱记录日志进行分页查询。")
    @GetMapping("/cisOrderRecords/pages")
    GridResultSet<CisOrderRecordTo> getCisOrderRecordPage(@ParameterObject @SpringQueryMap CisOrderRecordQto cisOrderRecordQto);

    @Operation(summary = "创建常用医嘱记录日志。")
    @PostMapping("/cisOrderRecords")
    CisOrderRecordTo createCisOrderRecord(@RequestBody @Valid CisOrderRecordNto cisOrderRecordNto);

}