package com.bjgoodwill.hip.ds.cis.cds.entry.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisPharmacyEto;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisPharmacyNto;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisPharmacyQto;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisPharmacyTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "医生药房领域服务", description = "医生药房领域服务")
public interface CisPharmacyService {

    @Operation(summary = "P0根据查询条件对医生药房进行查询。")
    @GetMapping("/cisPharmacies")
    List<CisPharmacyTo> getCisPharmacies(@ParameterObject @SpringQueryMap CisPharmacyQto cisPharmacyQto);

    @Operation(summary = "根据查询条件对医生药房进行分页查询。")
    @GetMapping("/cisPharmacies/pages")
    GridResultSet<CisPharmacyTo> getCisPharmacyPage(@ParameterObject @SpringQueryMap CisPharmacyQto cisPharmacyQto);

    @Operation(summary = "根据唯一标识返回医生药房。")
    @GetMapping("/cisPharmacies/{id:.+}")
    CisPharmacyTo getCisPharmacyById(@PathVariable("id") String id);

    @Operation(summary = "创建医生药房。")
    @PostMapping("/cisPharmacies")
    CisPharmacyTo createCisPharmacy(@RequestBody @Valid CisPharmacyNto cisPharmacyNto);

    @Operation(summary = "根据唯一标识修改医生药房。")
    @PutMapping("/cisPharmacies/{id:.+}")
    void updateCisPharmacy(@PathVariable("id") String id, @RequestBody @Valid CisPharmacyEto cisPharmacyEto);

}