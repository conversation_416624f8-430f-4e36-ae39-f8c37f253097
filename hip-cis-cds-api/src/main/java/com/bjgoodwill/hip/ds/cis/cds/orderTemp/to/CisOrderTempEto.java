package com.bjgoodwill.hip.ds.cis.cds.orderTemp.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.bjgoodwill.hip.ds.cis.cds.enmus.TempRangeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "组套")
public class CisOrderTempEto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -3928190570464562369L;

    @Schema(description = "orderTempName")
    private String orderTempName;
    @Schema(description = "tempType")
    private VisitTypeEnum tempType;
    @Schema(description = "tempRange")
    private TempRangeEnum tempRange;
    @Schema(description = "拼音码")
    private String inputPy;
    @Schema(description = "助记码")
    private String mnemonicCode;
    @Schema(description = "备注说明")
    private String tempRemark;
    @Schema(description = "父级主键")
    private String parentId;
    @Schema(description = "是否文件夹")
    private Boolean groupFlag;
    @Schema(description = "机构编码")
    private String orgCode;
    @Schema(description = "机构名称")
    private String orgName;
    @Schema(description = "医生编码")
    private String doctorCode;
    @Schema(description = "医生名称")
    private String doctorName;
    @Schema(description = "院区编码")
    private String hospitalArea;
    @Schema(description = "院区名称")
    private String hospitalAreaName;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "序号")
    private Integer sortNo;
    @Schema(description = "新增明细集合")
    private List<CisOrderTempDetailNto> detailNtos;
    @Schema(description = "修改明细Eto集合")
    private List<CisOrderTempDetailEto> detailEtos;
    @Schema(description = "删除明细id集合")
    private List<String> deleteDetailIds;

    @NotBlank(message = "orderTempName不能为空！")
    public String getOrderTempName() {
        return orderTempName;
    }

    public void setOrderTempName(String orderTempName) {
        this.orderTempName = StringUtils.trimToNull(orderTempName);
    }

    @NotNull(message = "tempType不能为空！")
    public VisitTypeEnum getTempType() {
        return tempType;
    }

    public void setTempType(VisitTypeEnum tempType) {
        this.tempType = tempType;
    }

    public TempRangeEnum getTempRange() {
        return tempRange;
    }

    public void setTempRange(TempRangeEnum tempRange) {
        this.tempRange = tempRange;
    }

    public String getInputPy() {
        return inputPy;
    }

    public void setInputPy(String inputPy) {
        this.inputPy = StringUtils.trimToNull(inputPy);
    }

    public String getMnemonicCode() {
        return mnemonicCode;
    }

    public void setMnemonicCode(String mnemonicCode) {
        this.mnemonicCode = StringUtils.trimToNull(mnemonicCode);
    }

    public String getTempRemark() {
        return tempRemark;
    }

    public void setTempRemark(String tempRemark) {
        this.tempRemark = StringUtils.trimToNull(tempRemark);
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = StringUtils.trimToNull(parentId);
    }

    public Boolean getGroupFlag() {
        return groupFlag;
    }

    public void setGroupFlag(Boolean groupFlag) {
        this.groupFlag = groupFlag;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = StringUtils.trimToNull(orgCode);
    }

    public String getDoctorCode() {
        return doctorCode;
    }

    public void setDoctorCode(String doctorCode) {
        this.doctorCode = StringUtils.trimToNull(doctorCode);
    }

    public String getHospitalArea() {
        return hospitalArea;
    }

    public void setHospitalArea(String hospitalArea) {
        this.hospitalArea = StringUtils.trimToNull(hospitalArea);
    }

    @NotNull(message = "版本不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public String getHospitalAreaName() {
        return hospitalAreaName;
    }

    public void setHospitalAreaName(String hospitalAreaName) {
        this.hospitalAreaName = hospitalAreaName;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public List<CisOrderTempDetailNto> getDetailNtos() {
        return detailNtos;
    }

    public void setDetailNtos(List<CisOrderTempDetailNto> detailNtos) {
        this.detailNtos = detailNtos;
    }

    public List<CisOrderTempDetailEto> getDetailEtos() {
        return detailEtos;
    }

    public void setDetailEtos(List<CisOrderTempDetailEto> detailEtos) {
        this.detailEtos = detailEtos;
    }

    public List<String> getDeleteDetailIds() {
        return deleteDetailIds;
    }

    public void setDeleteDetailIds(List<String> deleteDetailIds) {
        this.deleteDetailIds = deleteDetailIds;
    }

    @Schema(description = "组套修改路径入参Eto")
    public static class OrderTempChangePathEto implements Serializable {
        @Serial
        private static final long serialVersionUID = -3853406459364958819L;

        @Schema(description = "组套id")
        private String id;
        @Schema(description = "新的父节点id")
        private String newParentId;
        @Schema(description = "组套范围")
        private TempRangeEnum tempRange;
        @Schema(description = "机构编码")
        private String orgCode;
        @Schema(description = "机构名称")
        private String orgName;
        @Schema(description = "行政科室编码")
        private String deptCode;
        @Schema(description = "行政科室名称")
        private String deptName;
        @Schema(description = "工作组类型")
        private String workGroupType;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getNewParentId() {
            return newParentId;
        }

        public void setNewParentId(String newParentId) {
            this.newParentId = newParentId;
        }

        public TempRangeEnum getTempRange() {
            return tempRange;
        }

        public void setTempRange(TempRangeEnum tempRange) {
            this.tempRange = tempRange;
        }

        public String getOrgCode() {
            return orgCode;
        }

        public void setOrgCode(String orgCode) {
            this.orgCode = orgCode;
        }

        public String getOrgName() {
            return orgName;
        }

        public void setOrgName(String orgName) {
            this.orgName = orgName;
        }

        public String getDeptCode() {
            return deptCode;
        }

        public void setDeptCode(String deptCode) {
            this.deptCode = deptCode;
        }

        public String getDeptName() {
            return deptName;
        }

        public void setDeptName(String deptName) {
            this.deptName = deptName;
        }
        public String getWorkGroupType() {
            return workGroupType;
        }

        public void setWorkGroupType(String workGroupType) {
            this.workGroupType = workGroupType;
        }
    }
}