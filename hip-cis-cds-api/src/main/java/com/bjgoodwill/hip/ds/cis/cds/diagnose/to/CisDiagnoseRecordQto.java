package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "常用诊断自动同步记录")
public class CisDiagnoseRecordQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -7646150490154372253L;

    @Schema(description = "模糊查询文本")
    private String text;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}