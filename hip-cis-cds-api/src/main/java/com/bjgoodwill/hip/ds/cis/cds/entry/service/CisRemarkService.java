package com.bjgoodwill.hip.ds.cis.cds.entry.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisRemarkEto;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisRemarkNto;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisRemarkQto;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisRemarkTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "医生词条领域服务", description = "医生词条领域服务")
public interface CisRemarkService {

    @Operation(summary = "P0根据查询条件对医生词条进行查询。")
    @GetMapping("/cisRemarks")
    List<CisRemarkTo> getCisRemarks(@ParameterObject @SpringQueryMap CisRemarkQto cisRemarkQto);

    @Operation(summary = "根据查询条件对医生词条进行分页查询。")
    @GetMapping("/cisRemarks/pages")
    GridResultSet<CisRemarkTo> getCisRemarkPage(@ParameterObject @SpringQueryMap CisRemarkQto cisRemarkQto);

    @Operation(summary = "根据唯一标识返回医生词条。")
    @GetMapping("/cisRemarks/{id:.+}")
    CisRemarkTo getCisRemarkById(@PathVariable("id") String id);

    @Operation(summary = "创建医生词条。")
    @PostMapping("/cisRemarks")
    CisRemarkTo createCisRemark(@RequestBody @Valid CisRemarkNto cisRemarkNto);

    @Operation(summary = "根据唯一标识修改医生词条。")
    @PutMapping("/cisRemarks/{id:.+}")
    void updateCisRemark(@PathVariable("id") String id, @RequestBody @Valid CisRemarkEto cisRemarkEto);

    @Operation(summary = "根据唯一标识删除医生词条。")
    @DeleteMapping("/cisRemarks/{id:.+}")
    void deleteCisRemark(@PathVariable("id") String id);

}