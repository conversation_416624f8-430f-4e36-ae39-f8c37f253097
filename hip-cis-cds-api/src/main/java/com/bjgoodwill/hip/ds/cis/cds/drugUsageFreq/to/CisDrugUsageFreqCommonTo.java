package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "科室药品开立常用用法和频次信息统计")
public class CisDrugUsageFreqCommonTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -8731652024549838895L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "开立科室编码")
    private String deptCode;
    @Schema(description = "药品编码")
    private String drugCode;
    @Schema(description = "用法编码")
    private String usageCode;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "频次编码")
    private String frequencyCode;
    @Schema(description = "频次名称")
    private String frequencyName;
    @Schema(description = "剂量")
    private String dosage;
    @Schema(description = "剂量单位")
    private String dosageUnit;
    @Schema(description = "剂量单位名称")
    private String dosageUnitName;
    @Schema(description = "次数")
    private Integer num;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    public String getUsageCode() {
        return usageCode;
    }

    public void setUsageCode(String usageCode) {
        this.usageCode = usageCode;
    }

    public String getFrequencyCode() {
        return frequencyCode;
    }

    public void setFrequencyCode(String frequencyCode) {
        this.frequencyCode = frequencyCode;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getDosage() {
        return dosage;
    }

    public void setDosage(String dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisDrugUsageFreqCommonTo other = (CisDrugUsageFreqCommonTo) obj;
        return Objects.equals(id, other.id);
    }
}