package com.bjgoodwill.hip.ds.cis.cds.test.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "Test2")
public class Test2Eto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1218914055410988086L;

    @Schema(description = "已启用")
    private boolean enabled;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}