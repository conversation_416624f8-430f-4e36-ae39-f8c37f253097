package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@Schema(description = "常用诊断")
public abstract class CisDiagnoseCommonEto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -602907595202264750L;

    @Schema(description = "诊断名称")
    private String diagnoseName;

    @Schema(description = "权重")
    private Double integral;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "已启用")
    private boolean enabled;

    public String getDiagnoseName() {
        return diagnoseName;
    }

    public void setDiagnoseName(String diagnoseName) {
        this.diagnoseName = StringUtils.trimToNull(diagnoseName);
    }

    public Double getIntegral() {
        return integral;
    }

    public void setIntegral(Double integral) {
        this.integral = integral;
    }

    @NotNull(message = "版本不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getMinimal_class() {
        return "." + this.getClass().getSimpleName();
    }
}