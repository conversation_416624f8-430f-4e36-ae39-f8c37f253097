package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqCommonEto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqCommonTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "科室药品开立常用用法和频次信息统计领域服务", description = "科室药品开立常用用法和频次信息统计领域服务")
public interface CisDrugUsageFreqCommonService {
    @Operation(summary = "P0根据药品编码查询该药品权重最高的用法频次。")
    @GetMapping("/cisDrugUsageFreqs/{deptCode}/drugs/{drugCode}/top-weighted")
    CisDrugUsageFreqCommonTo getDrugUsageFreqTop(@PathVariable("deptCode") String deptCode, @PathVariable("drugCode") String drugCode, @RequestParam(value = "rule", required = false) String rule);


    @Operation(summary = "统计科室药品开立常用用法和频次。")
    @PostMapping("/cisDrugUsageFreqs/statistics")
    void statistics();

    @Operation(summary = "根据查询条件对科室药品开立常用用法和频次信息统计进行查询。")
    @GetMapping("/cisDrugUsageFreqCommons")
    List<CisDrugUsageFreqCommonTo> getCisDrugUsageFreqCommons(@ParameterObject @SpringQueryMap CisDrugUsageFreqCommonQto cisDrugUsageFreqCommonQto);

    @Operation(summary = "根据查询条件对科室药品开立常用用法和频次信息统计进行分页查询。")
    @GetMapping("/cisDrugUsageFreqCommons/pages")
    GridResultSet<CisDrugUsageFreqCommonTo> getCisDrugUsageFreqCommonPage(@ParameterObject @SpringQueryMap CisDrugUsageFreqCommonQto cisDrugUsageFreqCommonQto);

    @Operation(summary = "根据唯一标识返回科室药品开立常用用法和频次信息统计。")
    @GetMapping("/cisDrugUsageFreqCommons/{id:.+}")
    CisDrugUsageFreqCommonTo getCisDrugUsageFreqCommonById(@PathVariable("id") String id);

    @Operation(summary = "创建科室药品开立常用用法和频次信息统计。")
    @PostMapping("/cisDrugUsageFreqCommons")
    CisDrugUsageFreqCommonTo createCisDrugUsageFreqCommon(@RequestBody @Valid CisDrugUsageFreqCommonNto cisDrugUsageFreqCommonNto);

    @Operation(summary = "根据唯一标识修改科室药品开立常用用法和频次信息统计。")
    @PutMapping("/cisDrugUsageFreqCommons/{id:.+}")
    void updateCisDrugUsageFreqCommon(@PathVariable("id") String id, @RequestBody @Valid CisDrugUsageFreqCommonEto cisDrugUsageFreqCommonEto);

    @Operation(summary = "根据唯一标识删除科室药品开立常用用法和频次信息统计。")
    @DeleteMapping("/cisDrugUsageFreqCommons/{id:.+}")
    void deleteCisDrugUsageFreqCommon(@PathVariable("id") String id);

    @Operation(summary = "P0保存科室药品开立常用用法、频次。")
    @PostMapping("/cisDrugUsageFreqCommons/createBatch")
    void createCisDrugUsageFreqCommonBatch(@RequestBody @Valid List<CisDrugUsageFreqCommonNto> list);
}