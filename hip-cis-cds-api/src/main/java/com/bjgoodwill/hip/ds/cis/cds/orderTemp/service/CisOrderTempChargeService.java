package com.bjgoodwill.hip.ds.cis.cds.orderTemp.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeEto;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeNto;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeQto;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "组套明细费用表领域服务", description = "组套明细费用表领域服务")
public interface CisOrderTempChargeService {
    @Operation(summary = "根据查询条件对组套明细费用表进行查询。")
    @GetMapping("/cisOrderTempCharges")
    List<CisOrderTempChargeTo> getCisOrderTempCharges(@ParameterObject @SpringQueryMap CisOrderTempChargeQto cisOrderTempChargeQto);

    @Operation(summary = "根据查询条件对组套明细费用表进行分页查询。")
    @GetMapping("/cisOrderTempCharges/pages")
    GridResultSet<CisOrderTempChargeTo> getCisOrderTempChargePage(@ParameterObject @SpringQueryMap CisOrderTempChargeQto cisOrderTempChargeQto);

    @Operation(summary = "根据唯一标识返回组套明细费用表。")
    @GetMapping("/cisOrderTempCharges/get/{id:.+}")
    CisOrderTempChargeTo getCisOrderTempChargeById(@PathVariable("id") String id);

    @Operation(summary = "根据组套明细ids对组套明细费用进行查询。")
    @GetMapping("/cisOrderTempCharges/list/detailIds")
    List<CisOrderTempChargeTo> listByTempDetailIds(@RequestParam List<String> tempDetailIds);

    @Operation(summary = "创建组套明细费用表。")
    @PostMapping("/cisOrderTempCharges/create")
    void createCisOrderTempCharge(@RequestBody @Valid CisOrderTempChargeNto cisOrderTempChargeNto);

    @Operation(summary = "批量创建组套明细费用表。")
    @PostMapping("/cisOrderTempCharges/batchcreate")
    void batchCreateCisOrderTempCharge(@RequestBody @Valid List<CisOrderTempChargeNto> cisOrderTempChargeNtos);

    @Operation(summary = "根据唯一标识修改组套明细费用表。")
    @PutMapping("/cisOrderTempCharges/update/{id:.+}")
    void updateCisOrderTempCharge(@PathVariable("id") String id, @RequestBody @Valid CisOrderTempChargeEto cisOrderTempChargeEto);

    @Operation(summary = "根据唯一标识删除组套明细费用表。")
    @DeleteMapping("/cisOrderTempCharges/delete/{id:.+}")
    void deleteCisOrderTempCharge(@PathVariable("id") String id);
}