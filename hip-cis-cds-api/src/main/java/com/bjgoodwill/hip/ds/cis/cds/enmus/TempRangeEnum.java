package com.bjgoodwill.hip.ds.cis.cds.enmus;

public enum TempRangeEnum {
    HOSPITAL("HOSPITAL", "全院"),
    DEPT("DEPT", "科室"),
    DOC("DOC", "个人"),
    PRESCRIPTION("PRESCRIPTION", "协定处方");

    private String code;
    private String name;

    TempRangeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TempRangeEnum getValue(String code) {
        for (TempRangeEnum value : TempRangeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}