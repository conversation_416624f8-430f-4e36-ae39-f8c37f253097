package com.bjgoodwill.hip.ds.cis.cds.enmus;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;

public enum EntryTempEnum {
    ENTRY("1", "词条"),
    DRUGSTORE("2", "药房");

    private String code;
    private String name;

    EntryTempEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SystemTypeEnum getValue(String code) {
        for (SystemTypeEnum value : SystemTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}