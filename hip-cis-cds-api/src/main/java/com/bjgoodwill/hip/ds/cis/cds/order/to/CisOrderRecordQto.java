package com.bjgoodwill.hip.ds.cis.cds.order.to;

import com.bjgoodwill.hip.business.util.common.to.BaseTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "常用医嘱记录日志")
public class CisOrderRecordQto extends BaseTo {

    @Serial
    private static final long serialVersionUID = -7391379637822172206L;

    @Schema(description = "模糊查询文本")
    private String text;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}