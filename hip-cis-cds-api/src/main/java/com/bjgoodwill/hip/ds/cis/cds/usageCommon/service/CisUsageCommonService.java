package com.bjgoodwill.hip.ds.cis.cds.usageCommon.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonEto;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "科室常用用法领域服务", description = "科室常用用法领域服务")
public interface CisUsageCommonService {

    @Operation(summary = "P0根据查询条件对科室常用用法进行查询。")
    @GetMapping("/cisUsageCommons")
    List<CisUsageCommonTo> getCisUsageCommons(@ParameterObject @SpringQueryMap CisUsageCommonQto cisUsageCommonQto);

    @Operation(summary = "根据查询条件对科室常用用法进行分页查询。")
    @GetMapping("/cisUsageCommons/pages")
    GridResultSet<CisUsageCommonTo> getCisUsageCommonPage(@ParameterObject @SpringQueryMap CisUsageCommonQto cisUsageCommonQto);

    @Operation(summary = "根据唯一标识返回科室常用用法。")
    @GetMapping("/cisUsageCommons/{id:.+}")
    CisUsageCommonTo getCisUsageCommonById(@PathVariable("id") String id);

    @Operation(summary = "创建科室常用用法。")
    @PostMapping("/cisUsageCommons")
    CisUsageCommonTo createCisUsageCommon(@RequestBody @Valid CisUsageCommonNto cisUsageCommonNto);

    @Operation(summary = "根据唯一标识修改科室常用用法。")
    @PutMapping("/cisUsageCommons/{id:.+}")
    void updateCisUsageCommon(@PathVariable("id") String id, @RequestBody @Valid CisUsageCommonEto cisUsageCommonEto);

    @Operation(summary = "根据唯一标识删除科室常用用法。")
    @DeleteMapping("/cisUsageCommons/{id:.+}")
    void deleteCisUsageCommon(@PathVariable("id") String id);

    @Operation(summary = "统计科室常用用法")
    @PostMapping("/cisUsageCommons/saveUsageCommons")
    void saveCisUsageCommons();
}