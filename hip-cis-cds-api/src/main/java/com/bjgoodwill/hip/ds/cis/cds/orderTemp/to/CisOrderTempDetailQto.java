package com.bjgoodwill.hip.ds.cis.cds.orderTemp.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "组套明细")
public class CisOrderTempDetailQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -8012512228563511906L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "组套标识")
    private String cisOrderTempId;
    @Schema(description = "组套明细组序号")
    private String groupNo;
    @Schema(description = "orderClass")
    private SystemTypeEnum orderClass;
    @Schema(description = "服务项目/药品编码")
    private String orderServiceCode;
    @Schema(description = "医嘱类型（临/长）")
    private OrderTypeEnum orderType;
    @Schema(description = "带药方式")
    private SbadmWayEnum sbadmWay;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getCisOrderTempId() {
        return cisOrderTempId;
    }

    public void setCisOrderTempId(String cisOrderTempId) {
        this.cisOrderTempId = cisOrderTempId;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    public String getOrderServiceCode() {
        return orderServiceCode;
    }

    public void setOrderServiceCode(String orderServiceCode) {
        this.orderServiceCode = orderServiceCode;
    }

    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderTypeEnum orderType) {
        this.orderType = orderType;
    }

    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }
}