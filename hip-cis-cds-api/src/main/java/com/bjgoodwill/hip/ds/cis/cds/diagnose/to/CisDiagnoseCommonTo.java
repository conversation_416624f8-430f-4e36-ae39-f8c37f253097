package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisEnum;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@Schema(description = "常用诊断")
public abstract class CisDiagnoseCommonTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -3059596320963064381L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "诊断编码")
    private String diagnoseCode;
    @Schema(description = "诊断名称")
    private String diagnoseName;
    @Schema(description = "诊断前缀")
    private String prefix;

    @Schema(description = "诊断后缀")
    private String suffix;

    @Schema(description = "中，西医 诊断")
    private DiagnosisEnum diagnosisClass;

    @Schema(description = "权重")
    private Long integral;
    @Schema(description = "人工维护的")
    private Boolean isFix;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "已启用")
    private boolean enabled;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDiagnoseCode() {
        return diagnoseCode;
    }

    public void setDiagnoseCode(String diagnoseCode) {
        this.diagnoseCode = diagnoseCode;
    }

    public String getDiagnoseName() {
        return diagnoseName;
    }

    public void setDiagnoseName(String diagnoseName) {
        this.diagnoseName = diagnoseName;
    }

    public Long getIntegral() {
        return integral;
    }

    public void setIntegral(Long integral) {
        this.integral = integral;
    }

    public Boolean getIsFix() {
        return isFix;
    }

    public void setIsFix(Boolean isFix) {
        this.isFix = isFix;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public DiagnosisEnum getDiagnosisClass() {
        return diagnosisClass;
    }

    public void setDiagnosisClass(DiagnosisEnum diagnosisClass) {
        this.diagnosisClass = diagnosisClass;
    }

    public String getMinimal_class() {
        return "." + this.getClass().getSimpleName();
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisDiagnoseCommonTo other = (CisDiagnoseCommonTo) obj;
        return Objects.equals(id, other.id);
    }
}