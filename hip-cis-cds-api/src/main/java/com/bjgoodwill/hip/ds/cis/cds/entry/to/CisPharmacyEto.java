package com.bjgoodwill.hip.ds.cis.cds.entry.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;

@Schema(description = "医生药房")
public class CisPharmacyEto extends CisEntryTempEto {

    @Serial
    private static final long serialVersionUID = -3767259592531204812L;

    @Schema(description = "版本")
    private Integer version;

    @NotNull(message = "版本不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}