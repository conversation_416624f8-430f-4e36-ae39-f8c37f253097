package com.bjgoodwill.hip.ds.cis.cds.entry.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@Schema(description = "医生个性存储（词条，药房）")
public abstract class CisEntryTempNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -8059815289460124982L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "内容")
    private String content;
    @Schema(description = "医生编码")
    private String doctorCode;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "内容不能为空！")
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = StringUtils.trimToNull(content);
    }

    @NotBlank(message = "医生编码不能为空！")
    public String getDoctorCode() {
        return doctorCode;
    }

    public void setDoctorCode(String doctorCode) {
        this.doctorCode = StringUtils.trimToNull(doctorCode);
    }

    public String getMinimal_class() {
        return "." + this.getClass().getSimpleName();
    }
}