package com.bjgoodwill.hip.ds.cis.cds.test.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Schema(description = "Test2")
public class Test2To implements Serializable {

    @Serial
    private static final long serialVersionUID = -7890704831516118533L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "已启用")
    private boolean enabled;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        Test2To other = (Test2To) obj;
        return Objects.equals(id, other.id);
    }
}