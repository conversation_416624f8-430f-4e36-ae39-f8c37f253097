package com.bjgoodwill.hip.ds.cis.cds.enmus;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;

public enum RecordTypeEnum {
    DIAGNOSIS("DIAGNOSIS", "诊断"),
    USAGEFREQ("USAGEFREQ", "常用用法频次"),
    USAGECOMMON("USAGECOMMON", "常用用法");

    private String code;
    private String name;

    RecordTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SystemTypeEnum getValue(String code) {
        for (SystemTypeEnum value : SystemTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}