package com.bjgoodwill.hip.ds.cis.cds.order.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "常用医嘱")
public class CisOrderCommonTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -8062164579105279540L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "权重")
    private Long integral;
    @Schema(description = "医嘱编码")
    private String serviceItemCode;
    @Schema(description = "医嘱名称")
    private String serviceItemName;
    @Schema(description = "医生编码")
    private String docCode;
    @Schema(description = "医生名称")
    private String docName;
    @Schema(description = "科室编码")
    private String orgCode;
    @Schema(description = "科室名称")
    private String orgName;
    @Schema(description = "执行科室")
    private String executiveOrg;
    @Schema(description = "执行科室名称")
    private String executiveOrgName;
    @Schema(description = "医嘱类型")
    private SystemTypeEnum systemType;
    @Schema(description = "部位")
    private String position;
    @Schema(description = "部位名称")
    private String positionName;
    @Schema(description = "辅助器械")
    private String assistiveDevices;
    @Schema(description = "辅助器械名称")
    private String assistiveDevicesName;
    @Schema(description = "范围")
    private String range;
    @Schema(description = "范围名称")
    private String rangeName;
    @Schema(description = "基本操作")
    private String basicOperation;
    @Schema(description = "基本操作名称")
    private String basicOperationName;
    @Schema(description = "方位")
    private String azimuth;
    @Schema(description = "方位名称")
    private String azimuthName;
    @Schema(description = "层数")
    private String layers;
    @Schema(description = "层数名称")
    private String layersName;
    @Schema(description = "标本")
    private String speciman;
    @Schema(description = "标本名称")
    private String specimanName;
    @Schema(description = "实验方法")
    private String experimentalMethods;
    @Schema(description = "实验方法名称")
    private String experimentalMethodsName;
    @Schema(description = "入路")
    private String approach;
    @Schema(description = "入路名称")
    private String approachName;

    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getIntegral() {
        return integral;
    }

    public void setIntegral(Long integral) {
        this.integral = integral;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = docCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getExecutiveOrg() {
        return executiveOrg;
    }

    public void setExecutiveOrg(String executiveOrg) {
        this.executiveOrg = executiveOrg;
    }

    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getAssistiveDevices() {
        return assistiveDevices;
    }

    public void setAssistiveDevices(String assistiveDevices) {
        this.assistiveDevices = assistiveDevices;
    }

    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = range;
    }

    public String getBasicOperation() {
        return basicOperation;
    }

    public void setBasicOperation(String basicOperation) {
        this.basicOperation = basicOperation;
    }

    public String getAzimuth() {
        return azimuth;
    }

    public void setAzimuth(String azimuth) {
        this.azimuth = azimuth;
    }

    public String getLayers() {
        return layers;
    }

    public void setLayers(String layers) {
        this.layers = layers;
    }

    public String getSpeciman() {
        return speciman;
    }

    public void setSpeciman(String speciman) {
        this.speciman = speciman;
    }

    public String getExperimentalMethods() {
        return experimentalMethods;
    }

    public void setExperimentalMethods(String experimentalMethods) {
        this.experimentalMethods = experimentalMethods;
    }

    public String getApproach() {
        return approach;
    }

    public void setApproach(String approach) {
        this.approach = approach;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getExecutiveOrgName() {
        return executiveOrgName;
    }

    public void setExecutiveOrgName(String executiveOrgName) {
        this.executiveOrgName = executiveOrgName;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getAssistiveDevicesName() {
        return assistiveDevicesName;
    }

    public void setAssistiveDevicesName(String assistiveDevicesName) {
        this.assistiveDevicesName = assistiveDevicesName;
    }

    public String getRangeName() {
        return rangeName;
    }

    public void setRangeName(String rangeName) {
        this.rangeName = rangeName;
    }

    public String getBasicOperationName() {
        return basicOperationName;
    }

    public void setBasicOperationName(String basicOperationName) {
        this.basicOperationName = basicOperationName;
    }

    public String getAzimuthName() {
        return azimuthName;
    }

    public void setAzimuthName(String azimuthName) {
        this.azimuthName = azimuthName;
    }

    public String getLayersName() {
        return layersName;
    }

    public void setLayersName(String layersName) {
        this.layersName = layersName;
    }

    public String getSpecimanName() {
        return specimanName;
    }

    public void setSpecimanName(String specimanName) {
        this.specimanName = specimanName;
    }

    public String getExperimentalMethodsName() {
        return experimentalMethodsName;
    }

    public void setExperimentalMethodsName(String experimentalMethodsName) {
        this.experimentalMethodsName = experimentalMethodsName;
    }

    public String getApproachName() {
        return approachName;
    }

    public void setApproachName(String approachName) {
        this.approachName = approachName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOrderCommonTo other = (CisOrderCommonTo) obj;
        return Objects.equals(id, other.id);
    }
}