package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "个人常用诊断")
public class CisDiagnoseDocCommonTo extends CisDiagnoseCommonTo {

    @Serial
    private static final long serialVersionUID = -995659020183740812L;

    @Schema(description = "医生编码")
    private String docCode;

    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = docCode;
    }

}