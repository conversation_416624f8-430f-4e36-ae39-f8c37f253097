package com.bjgoodwill.hip.ds.cis.cds.test.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "Test1")
public class Test1Nto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1848764625733738243L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "test2_id")
    private String test2_id;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    public String getTest2_id() {
        return test2_id;
    }

    public void setTest2_id(String test2_id) {
        this.test2_id = StringUtils.trimToNull(test2_id);
    }
}