package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

@Schema(description = "科室药品开立常用用法和频次信息统计")
public class CisDrugUsageFreqCommonEto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -2213189416960455006L;

    @Schema(description = "开立科室编码")
    private String deptCode;
    @Schema(description = "药品编码")
    private String drugCode;
    @Schema(description = "用法编码")
    private String usageCode;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "频次编码")
    private String frequencyCode;
    @Schema(description = "频次名称")
    private String frequencyName;
    @Schema(description = "剂量")
    private String dosage;
    @Schema(description = "剂量单位")
    private String dosageUnit;
    @Schema(description = "剂量单位名称")
    private String dosageUnitName;
    @Schema(description = "次数")
    private Integer num;

    @NotBlank(message = "开立科室编码不能为空！")
    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = StringUtils.trimToNull(deptCode);
    }

    @NotBlank(message = "药品编码不能为空！")
    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = StringUtils.trimToNull(drugCode);
    }

    @NotBlank(message = "用法编码不能为空！")
    public String getUsageCode() {
        return usageCode;
    }

    public void setUsageCode(String usageCode) {
        this.usageCode = StringUtils.trimToNull(usageCode);
    }

    @NotBlank(message = "频次编码不能为空！")
    public String getFrequencyCode() {
        return frequencyCode;
    }

    public void setFrequencyCode(String frequencyCode) {
        this.frequencyCode = StringUtils.trimToNull(frequencyCode);
    }

    @NotNull(message = "次数不能为空！")
    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getDosage() {
        return dosage;
    }

    public void setDosage(String dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }
}