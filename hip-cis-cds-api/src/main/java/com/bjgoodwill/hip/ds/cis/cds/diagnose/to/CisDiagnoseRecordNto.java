package com.bjgoodwill.hip.ds.cis.cds.diagnose.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.bjgoodwill.hip.ds.cis.cds.enmus.RecordTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.time.LocalDateTime;

@Schema(description = "常用诊断自动同步记录")
public class CisDiagnoseRecordNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -4187087134011662053L;

    //    @Schema(description = "标识")
//    private String id;
    @Schema(description = "本次同步时间")
    private LocalDateTime recordDateTime;
    @Schema(description = "同步类型")
    private RecordTypeEnum recordType;

//    @NotBlank(message = "标识不能为空！")
//    @Size(max = 50, message = "标识长度不能超过50个字符！")
//    public String getId() {
//        return id;
//    }
//
//    public void setId(String id) {
//        this.id = StringUtils.trimToNull(id);
//    }

    public LocalDateTime getRecordDateTime() {
        return recordDateTime;
    }

    public void setRecordDateTime(LocalDateTime recordDateTime) {
        this.recordDateTime = recordDateTime;
    }

    public RecordTypeEnum getRecordType() {
        return recordType;
    }

    public void setRecordType(RecordTypeEnum recordType) {
        this.recordType = recordType;
    }
}