package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqSyncEto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqSyncNto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqSyncQto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqSyncTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "药品用法频次同步领域服务", description = "药品用法频次同步领域服务")
public interface CisDrugUsageFreqSyncService {

    @Operation(summary = "根据查询条件对药品用法频次同步进行查询。")
    @GetMapping("/cisDrugUsageFreqSyncs")
    List<CisDrugUsageFreqSyncTo> getCisDrugUsageFreqSyncs(@ParameterObject @SpringQueryMap CisDrugUsageFreqSyncQto cisDrugUsageFreqSyncQto);

    @Operation(summary = "根据查询条件对药品用法频次同步进行分页查询。")
    @GetMapping("/cisDrugUsageFreqSyncs/pages")
    GridResultSet<CisDrugUsageFreqSyncTo> getCisDrugUsageFreqSyncPage(@ParameterObject @SpringQueryMap CisDrugUsageFreqSyncQto cisDrugUsageFreqSyncQto);

    @Operation(summary = "根据唯一标识返回药品用法频次同步。")
    @GetMapping("/cisDrugUsageFreqSyncs/{id:.+}")
    CisDrugUsageFreqSyncTo getCisDrugUsageFreqSyncById(@PathVariable("id") String id);

    @Operation(summary = "创建药品用法频次同步。")
    @PostMapping("/cisDrugUsageFreqSyncs")
    CisDrugUsageFreqSyncTo createCisDrugUsageFreqSync(@RequestBody @Valid CisDrugUsageFreqSyncNto cisDrugUsageFreqSyncNto);

    @Operation(summary = "根据唯一标识修改药品用法频次同步。")
    @PutMapping("/cisDrugUsageFreqSyncs/{id:.+}")
    void updateCisDrugUsageFreqSync(@PathVariable("id") String id, @RequestBody @Valid CisDrugUsageFreqSyncEto cisDrugUsageFreqSyncEto);

    @Operation(summary = "根据唯一标识删除药品用法频次同步。")
    @DeleteMapping("/cisDrugUsageFreqSyncs/{id:.+}")
    void deleteCisDrugUsageFreqSync(@PathVariable("id") String id);

    @Operation(summary = "根据开立时间查询申请单和明细数据。")
    @GetMapping("/cisDrugUsageFreqSyncs/byCreatedDate")
    List<CisDrugUsageFreqSyncTo> getCisDrugByCreateDate(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDateTime date);

    @Operation(summary = "批量保存。")
    @PostMapping("/cisDrugUsageFreqSyncs/createBatch")
    void createBatch(@RequestBody @Valid List<CisDrugUsageFreqSyncNto> cisDrugUsageFreqSyncNtos);
}