package com.bjgoodwill.hip.ds.cis.cds.orderTemp.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.bjgoodwill.hip.ds.cis.cds.enmus.TempRangeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.util.List;

@Schema(description = "组套")
public class CisOrderTempNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -4019706135207835592L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "orderTempName")
    private String orderTempName;
    @Schema(description = "tempType")
    private VisitTypeEnum tempType;
    @Schema(description = "tempRange")
    private TempRangeEnum tempRange;
    @Schema(description = "拼音码")
    private String inputPy;
    @Schema(description = "助记码")
    private String mnemonicCode;
    @Schema(description = "备注说明")
    private String tempRemark;
    @Schema(description = "父级主键")
    private String parentId;
    @Schema(description = "是否文件夹")
    private Boolean groupFlag;
    @Schema(description = "hospitalCode")
    private String hospitalCode;
    @Schema(description = "医院名称")
    private String hospitalName;
    @Schema(description = "机构编码")
    private String orgCode;
    @Schema(description = "机构名称")
    private String orgName;
    @Schema(description = "医生编码")
    private String doctorCode;
    @Schema(description = "医生名称")
    private String doctorName;
    @Schema(description = "院区编码")
    private String hospitalArea;
    @Schema(description = "院区名称")
    private String hospitalAreaName;
    @Schema(description = "组套明细")
    private List<CisOrderTempDetailNto> cisOrderTempDetailNtos;
    @Schema(description = "类型（暂时只有中草药使用）")
    private SystemTypeEnum systemType;
    @Schema(description = "序号")
    private Integer sortNo;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "orderTempName不能为空！")
    public String getOrderTempName() {
        return orderTempName;
    }

    public void setOrderTempName(String orderTempName) {
        this.orderTempName = StringUtils.trimToNull(orderTempName);
    }

    @NotNull(message = "tempType不能为空！")
    public VisitTypeEnum getTempType() {
        return tempType;
    }

    public void setTempType(VisitTypeEnum tempType) {
        this.tempType = tempType;
    }

    @NotNull(message = "tempRange不能为空！")
    public TempRangeEnum getTempRange() {
        return tempRange;
    }

    public void setTempRange(TempRangeEnum tempRange) {
        this.tempRange = tempRange;
    }

    public String getInputPy() {
        return inputPy;
    }

    public void setInputPy(String inputPy) {
        this.inputPy = StringUtils.trimToNull(inputPy);
    }

    public String getMnemonicCode() {
        return mnemonicCode;
    }

    public void setMnemonicCode(String mnemonicCode) {
        this.mnemonicCode = StringUtils.trimToNull(mnemonicCode);
    }

    public String getTempRemark() {
        return tempRemark;
    }

    public void setTempRemark(String tempRemark) {
        this.tempRemark = StringUtils.trimToNull(tempRemark);
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = StringUtils.trimToNull(parentId);
    }

    @NotNull(message = "groupFlag不能为空！")
    public Boolean getGroupFlag() {
        return groupFlag;
    }

    public void setGroupFlag(Boolean groupFlag) {
        this.groupFlag = groupFlag;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = StringUtils.trimToNull(hospitalCode);
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = StringUtils.trimToNull(orgCode);
    }

    public String getDoctorCode() {
        return doctorCode;
    }

    public void setDoctorCode(String doctorCode) {
        this.doctorCode = StringUtils.trimToNull(doctorCode);
    }

    public String getHospitalArea() {
        return hospitalArea;
    }

    public void setHospitalArea(String hospitalArea) {
        this.hospitalArea = StringUtils.trimToNull(hospitalArea);
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public String getHospitalAreaName() {
        return hospitalAreaName;
    }

    public void setHospitalAreaName(String hospitalAreaName) {
        this.hospitalAreaName = hospitalAreaName;
    }

    public List<CisOrderTempDetailNto> getCisOrderTempDetailNtos() {
        return cisOrderTempDetailNtos;
    }

    public void setCisOrderTempDetailNtos(List<CisOrderTempDetailNto> cisOrderTempDetailNtos) {
        this.cisOrderTempDetailNtos = cisOrderTempDetailNtos;
    }

    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }
}