package com.bjgoodwill.hip.ds.cis.cds.usageCommon.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;

@Schema(description = "科室常用用法")
public class CisUsageCommonQto extends BaseQto {

    @Serial
    private static final long serialVersionUID = -1885204610001193488L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "用法类型")
    private String usageType;
    @Schema(description = "用法编码")
    private String usageCode;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "科室编码")
    private String orgCode;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getUsageType() {
        return usageType;
    }

    public void setUsageType(String usageType) {
        this.usageType = usageType;
    }

    public String getUsageCode() {
        return usageCode;
    }

    public void setUsageCode(String usageCode) {
        this.usageCode = usageCode;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }
}