package com.bjgoodwill.hip.ds.cis.mtcpoe.order.repository;

import com.bjgoodwill.hip.ds.cis.mtcpoe.order.entity.CisMtOrderExecLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.mtcpoe.order.repository.CisMtOrderExecLogRepository")
public interface CisMtOrderExecLogRepository extends JpaRepository<CisMtOrderExecLog, String>, JpaSpecificationExecutor<CisMtOrderExecLog> {

    List<CisMtOrderExecLog> findByOrderId(String orderId);

    Page<CisMtOrderExecLog> findByOrderId(String orderId, Pageable pageable);

    boolean existsByOrderId(String orderId);

    void deleteByOrderId(String orderId);

}