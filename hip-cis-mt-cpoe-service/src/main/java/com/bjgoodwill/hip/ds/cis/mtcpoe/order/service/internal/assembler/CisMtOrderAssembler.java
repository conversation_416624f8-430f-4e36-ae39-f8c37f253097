package com.bjgoodwill.hip.ds.cis.mtcpoe.order.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.mtcpoe.order.entity.CisMtOrder;
import com.bjgoodwill.hip.ds.cis.mtcpoe.order.to.CisMtOrderTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisMtOrderAssembler {

    public static List<CisMtOrderTo> toTos(List<CisMtOrder> cisMtOrders) {
        return toTos(cisMtOrders, false);
    }

    public static List<CisMtOrderTo> toTos(List<CisMtOrder> cisMtOrders, boolean withAllParts) {
        Assert.notNull(cisMtOrders, "参数cisMtOrders不能为空！");

        List<CisMtOrderTo> tos = new ArrayList<>();
        for (CisMtOrder cisMtOrder : cisMtOrders)
            tos.add(toTo(cisMtOrder, withAllParts));
        return tos;
    }

    public static CisMtOrderTo toTo(CisMtOrder cisMtOrder) {
        return toTo(cisMtOrder, false);
    }

    /**
     * @generated
     */
    public static CisMtOrderTo toTo(CisMtOrder cisMtOrder, boolean withAllParts) {
        if (cisMtOrder == null)
            return null;
        CisMtOrderTo to = new CisMtOrderTo();
        to.setId(cisMtOrder.getId());
        to.setSApplyId(cisMtOrder.getSApplyId());
        to.setSortNo(cisMtOrder.getSortNo());
        to.setPatMiCode(cisMtOrder.getPatMiCode());
        to.setVisitCode(cisMtOrder.getVisitCode());
        to.setOrderServiceCode(cisMtOrder.getOrderServiceCode());
        to.setOrderContent(cisMtOrder.getOrderContent());
        to.setOrderClass(cisMtOrder.getOrderClass());
        to.setApplyCode(cisMtOrder.getApplyCode());
        to.setExecuteOrgCode(cisMtOrder.getExecuteOrgCode());
        to.setExecuteOrgName(cisMtOrder.getExecuteOrgName());
        to.setVersion(cisMtOrder.getVersion());
        to.setDeleted(cisMtOrder.isDeleted());
        to.setCreatedStaff(cisMtOrder.getCreatedStaff());
        to.setCreatedStaffName(cisMtOrder.getCreatedStaffName());
        to.setCreatedDate(cisMtOrder.getCreatedDate());
        to.setCreateOrgCode(cisMtOrder.getCreateOrgCode());
        to.setReMark(cisMtOrder.getReMark());
        to.setStatusCode(cisMtOrder.getStatusCode());
        to.setReceiveOrgCode(cisMtOrder.getReceiveOrgCode());
        to.setLimitConformFlag(cisMtOrder.getLimitConformFlag());
        to.setHospitalCode(cisMtOrder.getHospitalCode());
        to.setUpdatedDate(cisMtOrder.getUpdatedDate());
        to.setUpdatedStaff(cisMtOrder.getUpdatedStaff());
        to.setUpdatedStaffName(cisMtOrder.getUpdatedStaffName());
        to.setSkinFlag(cisMtOrder.getSkinFlag());
        to.setSbadmWay(cisMtOrder.getSbadmWay());
        to.setSkinType(cisMtOrder.getSkinType());

        if (withAllParts) {
        }
        return to;
    }

}