package com.bjgoodwill.hip.ds.cis.mtcpoe.order.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.ExecLogEnum;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.mtcpoe.order.repository.CisMtOrderExecLogRepository;
import com.bjgoodwill.hip.ds.cis.mtcpoe.order.to.CisMtOrderExecLogEto;
import com.bjgoodwill.hip.ds.cis.mtcpoe.order.to.CisMtOrderExecLogNto;
import com.bjgoodwill.hip.ds.cis.mtcpoe.order.to.CisMtOrderExecLogQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "医技医嘱日志")
@Table(name = "cis_mt_order_exec_log", indexes = {}, uniqueConstraints = {})
public class CisMtOrderExecLog {

    // 标识
    private String id;
    // 医技医嘱标识
    private String orderId;
    // 主索引
    private String patMiCode;
    // 流水号
    private String visitCode;
    // 医嘱序号
    private Double orderNo;
    // 医嘱名称
    private String orderName;
    // 操作类型
    private ExecLogEnum execLogType;
    // 逻辑删除标记
    private boolean deleted;
    // 创建的人员
    private String createdStaff;
    // 创建的人员姓名
    private String createdStaffName;
    // 创建的时间
    private LocalDateTime createdDate;
    // 最后修改的时间
    private LocalDateTime updatedDate;
    // 备注
    private String reMark;

    public static Optional<CisMtOrderExecLog> getCisMtOrderExecLogById(String id) {
        return dao().findById(id);
    }

    public static List<CisMtOrderExecLog> getCisMtOrderExecLogs(String orderId, CisMtOrderExecLogQto qto) {
        if (orderId != null) {
            qto.setOrderId(orderId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisMtOrderExecLog> getCisMtOrderExecLogPage(String orderId, CisMtOrderExecLogQto qto) {

        if (orderId != null) {
            qto.setOrderId(orderId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisMtOrderExecLog> getByOrderId(String orderId) {
        return dao().findByOrderId(orderId);
    }

    public static void deleteByOrderId(String orderId) {
        dao().deleteByOrderId(orderId);
    }

    /**
     * @generated
     */
    private static Specification<CisMtOrderExecLog> getSpecification(CisMtOrderExecLogQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getOrderId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderId"), qto.getOrderId()));
            }
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (qto.getOrderNo() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderNo"), qto.getOrderNo()));
            }
            if (qto.getExecLogType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("execLogType"), qto.getExecLogType()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            return predicate;
        };
    }

    private static CisMtOrderExecLogRepository dao() {
        return SpringUtil.getBean(CisMtOrderExecLogRepository.class);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("医技医嘱标识")
    @Column(name = "order_id", nullable = false, length = 50)
    public String getOrderId() {
        return orderId;
    }

    protected void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Comment("主索引")
    @Column(name = "pat_mi_code", nullable = true)
    public String getPatMiCode() {
        return patMiCode;
    }

    protected void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    @Comment("流水号")
    @Column(name = "visit_code", nullable = false)
    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    @Comment("医嘱序号")
    @Column(name = "order_no", nullable = true)
    public Double getOrderNo() {
        return orderNo;
    }

    protected void setOrderNo(Double orderNo) {
        this.orderNo = orderNo;
    }

    @Comment("医嘱名称")
    @Column(name = "order_name", nullable = true)
    public String getOrderName() {
        return orderName;
    }

    protected void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    @Enumerated(EnumType.STRING)
    @Comment("操作类型")
    @Column(name = "exec_log_type", nullable = true)
    public ExecLogEnum getExecLogType() {
        return execLogType;
    }

    protected void setExecLogType(ExecLogEnum execLogType) {
        this.execLogType = execLogType;
    }

    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Comment("备注")
    @Column(name = "re_mark", nullable = true)
    public String getReMark() {
        return reMark;
    }

    protected void setReMark(String reMark) {
        this.reMark = reMark;
    }

    @Transient
    public CisMtOrder getCisMtOrder() {
        return CisMtOrder.getCisMtOrderById(getOrderId()).orElse(null);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisMtOrderExecLog other = (CisMtOrderExecLog) obj;
        return Objects.equals(id, other.id);
    }

    public CisMtOrderExecLog create(String orderId, CisMtOrderExecLogNto cisMtOrderExecLogNto) {
        Assert.notNull(cisMtOrderExecLogNto, "参数cisMtOrderExecLogNto不能为空！");

        setOrderId(orderId);

        setId(cisMtOrderExecLogNto.getId());
        setPatMiCode(cisMtOrderExecLogNto.getPatMiCode());
        setVisitCode(cisMtOrderExecLogNto.getVisitCode());
        setOrderNo(cisMtOrderExecLogNto.getOrderNo());
        setOrderName(cisMtOrderExecLogNto.getOrderName());
        setExecLogType(cisMtOrderExecLogNto.getExecLogType());
        setDeleted(false);
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        setReMark(cisMtOrderExecLogNto.getReMark());
        dao().save(this);
        return this;
    }

    public void update(CisMtOrderExecLogEto cisMtOrderExecLogEto) {
        setExecLogType(cisMtOrderExecLogEto.getExecLogType());
        setUpdatedDate(LocalDateUtil.now());
    }

    public void delete() {
        setDeleted(true);
    }

}
