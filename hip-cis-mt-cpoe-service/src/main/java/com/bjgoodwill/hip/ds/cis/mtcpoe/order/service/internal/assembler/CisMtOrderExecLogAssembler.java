package com.bjgoodwill.hip.ds.cis.mtcpoe.order.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.mtcpoe.order.entity.CisMtOrderExecLog;
import com.bjgoodwill.hip.ds.cis.mtcpoe.order.to.CisMtOrderExecLogTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisMtOrderExecLogAssembler {

    public static List<CisMtOrderExecLogTo> toTos(List<CisMtOrderExecLog> cisMtOrderExecLogs) {
        return toTos(cisMtOrderExecLogs, false);
    }

    public static List<CisMtOrderExecLogTo> toTos(List<CisMtOrderExecLog> cisMtOrderExecLogs, boolean withAllParts) {
        Assert.notNull(cisMtOrderExecLogs, "参数cisMtOrderExecLogs不能为空！");

        List<CisMtOrderExecLogTo> tos = new ArrayList<>();
        for (CisMtOrderExecLog cisMtOrderExecLog : cisMtOrderExecLogs)
            tos.add(toTo(cisMtOrderExecLog, withAllParts));
        return tos;
    }

    public static CisMtOrderExecLogTo toTo(CisMtOrderExecLog cisMtOrderExecLog) {
        return toTo(cisMtOrderExecLog, false);
    }

    /**
     * @generated
     */
    public static CisMtOrderExecLogTo toTo(CisMtOrderExecLog cisMtOrderExecLog, boolean withAllParts) {
        if (cisMtOrderExecLog == null)
            return null;
        CisMtOrderExecLogTo to = new CisMtOrderExecLogTo();
        to.setId(cisMtOrderExecLog.getId());
        to.setOrderId(cisMtOrderExecLog.getOrderId());
        to.setPatMiCode(cisMtOrderExecLog.getPatMiCode());
        to.setVisitCode(cisMtOrderExecLog.getVisitCode());
        to.setOrderNo(cisMtOrderExecLog.getOrderNo());
        to.setOrderName(cisMtOrderExecLog.getOrderName());
        to.setExecLogType(cisMtOrderExecLog.getExecLogType());
        to.setDeleted(cisMtOrderExecLog.isDeleted());
        to.setCreatedStaff(cisMtOrderExecLog.getCreatedStaff());
        to.setCreatedStaffName(cisMtOrderExecLog.getCreatedStaffName());
        to.setCreatedDate(cisMtOrderExecLog.getCreatedDate());
        to.setUpdatedDate(cisMtOrderExecLog.getUpdatedDate());
        to.setReMark(cisMtOrderExecLog.getReMark());

        if (withAllParts) {
        }
        return to;
    }

}