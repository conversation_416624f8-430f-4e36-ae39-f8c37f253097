package com.bjgoodwill.hip.ds.cis.cds.orderTemp.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-cds.name}", url = "${hip.domains.cis-cds.url}", path = "/api/orderTemp/orderTemp/cisOrderTempCharge", contextId = "com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.CisOrderTempChargeServiceFeign")
public interface CisOrderTempChargeServiceFeign extends CisOrderTempChargeService {

}