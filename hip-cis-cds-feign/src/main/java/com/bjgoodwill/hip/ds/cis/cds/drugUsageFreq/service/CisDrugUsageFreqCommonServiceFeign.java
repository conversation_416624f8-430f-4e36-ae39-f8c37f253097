package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-cds.name}", url = "${hip.domains.cis-cds.url}", path = "/api/cds/drugUsageFreq/cisDrugUsageFreqCommon", contextId = "com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.service.CisDrugUsageFreqCommonServiceFeign")
public interface CisDrugUsageFreqCommonServiceFeign extends CisDrugUsageFreqCommonService {

}