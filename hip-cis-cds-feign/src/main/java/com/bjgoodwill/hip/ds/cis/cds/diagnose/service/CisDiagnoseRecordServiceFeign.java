package com.bjgoodwill.hip.ds.cis.cds.diagnose.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cis-cds.name}", url = "${hip.domains.cis-cds.url}", path = "/api/cds/diagnose/cisDiagnoseRecord", contextId = "com.bjgoodwill.hip.ds.cis.cds.diagnose.service.CisDiagnoseRecordServiceFeign")
public interface CisDiagnoseRecordServiceFeign extends CisDiagnoseRecordService {

}