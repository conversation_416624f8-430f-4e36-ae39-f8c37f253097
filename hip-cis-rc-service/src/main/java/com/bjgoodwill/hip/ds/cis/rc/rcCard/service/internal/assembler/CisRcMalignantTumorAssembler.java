package com.bjgoodwill.hip.ds.cis.rc.rcCard.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.rc.rcCard.entity.CisRcMalignantTumor;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcMalignantTumorTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisRcMalignantTumorAssembler {

    public static List<CisRcMalignantTumorTo> toTos(List<CisRcMalignantTumor> cisRcMalignantTumors) {
        return toTos(cisRcMalignantTumors, false);
    }

    public static List<CisRcMalignantTumorTo> toTos(List<CisRcMalignantTumor> cisRcMalignantTumors, boolean withAllParts) {
        Assert.notNull(cisRcMalignantTumors, "参数cisRcMalignantTumors不能为空！");

        List<CisRcMalignantTumorTo> tos = new ArrayList<>();
        for (CisRcMalignantTumor cisRcMalignantTumor : cisRcMalignantTumors)
            tos.add(toTo(cisRcMalignantTumor, withAllParts));
        return tos;
    }

    public static CisRcMalignantTumorTo toTo(CisRcMalignantTumor cisRcMalignantTumor) {
        return toTo(cisRcMalignantTumor, false);
    }

    /**
     * @generated
     */
    public static CisRcMalignantTumorTo toTo(CisRcMalignantTumor cisRcMalignantTumor, boolean withAllParts) {
        if (cisRcMalignantTumor == null)
            return null;
        CisRcMalignantTumorTo to = new CisRcMalignantTumorTo();
        to.setId(cisRcMalignantTumor.getId());
        to.setReportNo(cisRcMalignantTumor.getReportNo());
        to.setVisitType(cisRcMalignantTumor.getVisitType());
        to.setPatMiCode(cisRcMalignantTumor.getPatMiCode());
        to.setVisitCode(cisRcMalignantTumor.getVisitCode());
        to.setName(cisRcMalignantTumor.getName());
        to.setParentsName(cisRcMalignantTumor.getParentsName());
        to.setSex(cisRcMalignantTumor.getSex());
        to.setBirthDate(cisRcMalignantTumor.getBirthDate());
        to.setAge(cisRcMalignantTumor.getAge());
        to.setAgeUnit(cisRcMalignantTumor.getAgeUnit());
        to.setIdNo(cisRcMalignantTumor.getIdNo());
        to.setCompanyName(cisRcMalignantTumor.getCompanyName());
        to.setContactTel(cisRcMalignantTumor.getContactTel());
        to.setHomeArea(cisRcMalignantTumor.getHomeArea());
        to.setProvince(cisRcMalignantTumor.getProvince());
        to.setCity(cisRcMalignantTumor.getCity());
        to.setCounty(cisRcMalignantTumor.getCounty());
        to.setHomeJd(cisRcMalignantTumor.getHomeJd());
        to.setVillage(cisRcMalignantTumor.getVillage());
        to.setHouseNo(cisRcMalignantTumor.getHouseNo());
        to.setCzProvince(cisRcMalignantTumor.getCzProvince());
        to.setCzCity(cisRcMalignantTumor.getCzCity());
        to.setCzCounty(cisRcMalignantTumor.getCzCounty());
        to.setCzHomeJd(cisRcMalignantTumor.getCzHomeJd());
        to.setNation(cisRcMalignantTumor.getNation());
        to.setCzVillage(cisRcMalignantTumor.getCzVillage());
        to.setCzHouseNo(cisRcMalignantTumor.getCzHouseNo());
        to.setMaritalStatus(cisRcMalignantTumor.getMaritalStatus());
        to.setEducation(cisRcMalignantTumor.getEducation());
        to.setWork(cisRcMalignantTumor.getWork());
        to.setGuardianName(cisRcMalignantTumor.getGuardianName());
        to.setInfectDate(cisRcMalignantTumor.getInfectDate());
        to.setDiagnosisDate(cisRcMalignantTumor.getDiagnosisDate());
        to.setDeadDate(cisRcMalignantTumor.getDeadDate());
        to.setDeadWhy(cisRcMalignantTumor.getDeadWhy());
        to.setDiseaseClass(cisRcMalignantTumor.getDiseaseClass());
        to.setDiseaseCode(cisRcMalignantTumor.getDiseaseCode());
        to.setReportUnitCategory(cisRcMalignantTumor.getReportUnitCategory());
        to.setReportUnit(cisRcMalignantTumor.getReportUnit());
        to.setUnitTel(cisRcMalignantTumor.getUnitTel());
        to.setReportUser(cisRcMalignantTumor.getReportUser());
        to.setReportUserName(cisRcMalignantTumor.getReportUserName());
        to.setOrgCode(cisRcMalignantTumor.getOrgCode());
        to.setOrgName(cisRcMalignantTumor.getOrgName());
        to.setReportCounty(cisRcMalignantTumor.getReportCounty());
        to.setRemarks(cisRcMalignantTumor.getRemarks());
        to.setUploadReportNo(cisRcMalignantTumor.getUploadReportNo());
        to.setHospitalCode(cisRcMalignantTumor.getHospitalCode());
        to.setCreatedStaff(cisRcMalignantTumor.getCreatedStaff());
        to.setUpdatedStaffName(cisRcMalignantTumor.getUpdatedStaffName());
        to.setCreatedDate(cisRcMalignantTumor.getCreatedDate());
        to.setUpdatedDate(cisRcMalignantTumor.getUpdatedDate());
        to.setPatStatusEnum(cisRcMalignantTumor.getPatStatusEnum());
        to.setDiseaseLevel(cisRcMalignantTumor.getDiseaseLevel());
        to.setDiseaseUnit(cisRcMalignantTumor.getDiseaseUnit());
        to.setDiseaseCode(cisRcMalignantTumor.getDiseaseCode());
        to.setDiseaseName(cisRcMalignantTumor.getDiseaseName());
        to.setDiseaseDCode(cisRcMalignantTumor.getDiseaseDCode());
        to.setDiseaseDName(cisRcMalignantTumor.getDiseaseDName());
        to.setPatKnowFlag(cisRcMalignantTumor.getPatKnowFlag());
        to.setPathologicalType(cisRcMalignantTumor.getPathologicalType());
        to.setPathologicalNo(cisRcMalignantTumor.getPathologicalNo());
        to.setConfirmT(cisRcMalignantTumor.getConfirmT());
        to.setConfirm_n(cisRcMalignantTumor.getConfirm_n());
        to.setConfirmM(cisRcMalignantTumor.getConfirmM());
        to.setInstallmentEnum(cisRcMalignantTumor.getInstallmentEnum());
        to.setDiseaseParts(cisRcMalignantTumor.getDiseaseParts());
        to.setDiseaseBasis(cisRcMalignantTumor.getDiseaseBasis());
        to.setDiseaseBasisName(cisRcMalignantTumor.getDiseaseBasisName());
        to.setLastDiagnosisDate(cisRcMalignantTumor.getLastDiagnosisDate());

        if (withAllParts) {
        }
        return to;
    }

}