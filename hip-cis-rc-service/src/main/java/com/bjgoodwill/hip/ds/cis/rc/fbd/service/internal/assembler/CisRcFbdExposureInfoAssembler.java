package com.bjgoodwill.hip.ds.cis.rc.fbd.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.rc.fbd.entity.CisRcFbdExposureInfo;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisRcFbdExposureInfoAssembler {

    public static List<CisRcFbdExposureInfoTo> toTos(List<CisRcFbdExposureInfo> cisRcFbdExposureInfos) {
        return toTos(cisRcFbdExposureInfos, false);
    }

    public static List<CisRcFbdExposureInfoTo> toTos(List<CisRcFbdExposureInfo> cisRcFbdExposureInfos, boolean withAllParts) {
        Assert.notNull(cisRcFbdExposureInfos, "参数cisRcFbdExposureInfos不能为空！");

        List<CisRcFbdExposureInfoTo> tos = new ArrayList<>();
        for (CisRcFbdExposureInfo cisRcFbdExposureInfo : cisRcFbdExposureInfos)
            tos.add(toTo(cisRcFbdExposureInfo, withAllParts));
        return tos;
    }

    public static CisRcFbdExposureInfoTo toTo(CisRcFbdExposureInfo cisRcFbdExposureInfo) {
        return toTo(cisRcFbdExposureInfo, false);
    }

    /**
     * @generated
     */
    public static CisRcFbdExposureInfoTo toTo(CisRcFbdExposureInfo cisRcFbdExposureInfo, boolean withAllParts) {
        if (cisRcFbdExposureInfo == null)
            return null;
        CisRcFbdExposureInfoTo to = new CisRcFbdExposureInfoTo();
        to.setId(cisRcFbdExposureInfo.getId());
        to.setFbdCode(cisRcFbdExposureInfo.getFbdCode());
        to.setFoodName(cisRcFbdExposureInfo.getFoodName());
        to.setFoodType(cisRcFbdExposureInfo.getFoodType());
        to.setFoodPack(cisRcFbdExposureInfo.getFoodPack());
        to.setFoodBrand(cisRcFbdExposureInfo.getFoodBrand());
        to.setManuFirmName(cisRcFbdExposureInfo.getManuFirmName());
        to.setEatPlaceType(cisRcFbdExposureInfo.getEatPlaceType());
        to.setEatPlaceCode(cisRcFbdExposureInfo.getEatPlaceCode());
        to.setEatRegionFlag(cisRcFbdExposureInfo.getEatRegionFlag());
        to.setEatProvince(cisRcFbdExposureInfo.getEatProvince());
        to.setEatCity(cisRcFbdExposureInfo.getEatCity());
        to.setEatCounty(cisRcFbdExposureInfo.getEatCounty());
        to.setEatVillage(cisRcFbdExposureInfo.getEatVillage());
        to.setEatNum(cisRcFbdExposureInfo.getEatNum());
        to.setEatDate(cisRcFbdExposureInfo.getEatDate());
        to.setPurcPlaceType(cisRcFbdExposureInfo.getPurcPlaceType());
        to.setPurcPlaceCode(cisRcFbdExposureInfo.getPurcPlaceCode());
        to.setPurcRegion(cisRcFbdExposureInfo.getPurcRegion());
        to.setPurcProvince(cisRcFbdExposureInfo.getPurcProvince());
        to.setPurcCity(cisRcFbdExposureInfo.getPurcCity());
        to.setPurcCounty(cisRcFbdExposureInfo.getPurcCounty());
        to.setPurcVillage(cisRcFbdExposureInfo.getPurcVillage());
        to.setOtherPatFlag(cisRcFbdExposureInfo.getOtherPatFlag());
        to.setCreatedStaff(cisRcFbdExposureInfo.getCreatedStaff());
        to.setCreatedStaffName(cisRcFbdExposureInfo.getCreatedStaffName());
        to.setCreatedDate(cisRcFbdExposureInfo.getCreatedDate());
        to.setUpdatedStaff(cisRcFbdExposureInfo.getUpdatedStaff());
        to.setUpdatedStaffName(cisRcFbdExposureInfo.getUpdatedStaffName());
        to.setUpdatedDate(cisRcFbdExposureInfo.getUpdatedDate());
        to.setDeleted(cisRcFbdExposureInfo.isDeleted());

        if (withAllParts) {
        }
        return to;
    }

}