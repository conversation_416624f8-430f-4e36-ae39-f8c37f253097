package com.bjgoodwill.hip.ds.cis.rc.fbd.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rc.fbd.entity.CisRcFbdSample;
import com.bjgoodwill.hip.ds.cis.rc.fbd.service.CisRcFbdSampleService;
import com.bjgoodwill.hip.ds.cis.rc.fbd.service.internal.assembler.CisRcFbdSampleAssembler;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdSampleEto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdSampleNto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdSampleQto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdSampleTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.rc.fbd.service.CisRcFbdSampleService")
@RequestMapping(value = "/api/cisrc/fbd/cisRcFbdSample", produces = "application/json; charset=utf-8")
public class CisRcFbdSampleServiceImpl implements CisRcFbdSampleService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisRcFbdSampleTo> getCisRcFbdSamples(CisRcFbdSampleQto cisRcFbdSampleQto) {
        return CisRcFbdSampleAssembler.toTos(CisRcFbdSample.getCisRcFbdSamples(cisRcFbdSampleQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisRcFbdSampleTo> getCisRcFbdSamplePage(CisRcFbdSampleQto cisRcFbdSampleQto) {
        Page<CisRcFbdSample> page = CisRcFbdSample.getCisRcFbdSamplePage(cisRcFbdSampleQto);
        Page<CisRcFbdSampleTo> result = page.map(CisRcFbdSampleAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisRcFbdSampleTo getCisRcFbdSampleById(String id) {
        return CisRcFbdSampleAssembler.toTo(CisRcFbdSample.getCisRcFbdSampleById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisRcFbdSampleTo createCisRcFbdSample(CisRcFbdSampleNto cisRcFbdSampleNto) {
        CisRcFbdSample cisRcFbdSample = new CisRcFbdSample();
        cisRcFbdSample = cisRcFbdSample.create(cisRcFbdSampleNto);
        return CisRcFbdSampleAssembler.toTo(cisRcFbdSample);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisRcFbdSample(String id, CisRcFbdSampleEto cisRcFbdSampleEto) {
        Optional<CisRcFbdSample> cisRcFbdSampleOptional = CisRcFbdSample.getCisRcFbdSampleById(id);
        cisRcFbdSampleOptional.ifPresent(cisRcFbdSample -> cisRcFbdSample.update(cisRcFbdSampleEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisRcFbdSample(String id) {
        Optional<CisRcFbdSample> cisRcFbdSampleOptional = CisRcFbdSample.getCisRcFbdSampleById(id);
        cisRcFbdSampleOptional.ifPresent(cisRcFbdSample -> cisRcFbdSample.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}