package com.bjgoodwill.hip.ds.cis.rc.fbd.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.rc.fbd.repository.CisRcFbdCaseExaminationRepository;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdCaseExaminationEto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdCaseExaminationNto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdCaseExaminationQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "食源性疾病-病例检查")
@Table(name = "cis_rc_fbd_case_examination", indexes = {@Index(name = "idx_cis_rc_fbd_case_examination_fbd_code", columnList = "fbd_code")}, uniqueConstraints = {})
public class CisRcFbdCaseExamination {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("病历编码")
    @Column(name = "fbd_code", nullable = false)
    private String fbdCode;


    @Comment("病例检查类型：ps主要症状与体征（字典fbd_symptomsign）、")
    @Column(name = "ce_type", nullable = true)
    private String ceType;


    @Comment("编码")
    @Column(name = "ce_code", nullable = true)
    private String ceCode;


    @Comment("值")
    @Column(name = "ce_value", nullable = true)
    private String ceValue;


    @Comment("备注")
    @Column(name = "remark", nullable = true)
    private String remark;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    private boolean deleted;

    public static Optional<CisRcFbdCaseExamination> getCisRcFbdCaseExaminationById(String id) {
        return dao().findById(id);
    }

    public static List<CisRcFbdCaseExamination> getCisRcFbdCaseExaminations(CisRcFbdCaseExaminationQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisRcFbdCaseExamination> getCisRcFbdCaseExaminationPage(CisRcFbdCaseExaminationQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisRcFbdCaseExamination> getSpecification(CisRcFbdCaseExaminationQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getFbdCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("fbdCode"), qto.getFbdCode()));
            }
            if (StringUtils.isNotBlank(qto.getCeType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("ceType"), qto.getCeType()));
            }
            if (StringUtils.isNotBlank(qto.getCeCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("ceCode"), qto.getCeCode()));
            }
            if (StringUtils.isNotBlank(qto.getCeValue())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("ceValue"), qto.getCeValue()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            return predicate;
        };
    }

    private static CisRcFbdCaseExaminationRepository dao() {
        return SpringUtil.getBean(CisRcFbdCaseExaminationRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getFbdCode() {
        return fbdCode;
    }

    protected void setFbdCode(String fbdCode) {
        this.fbdCode = fbdCode;
    }

    public String getCeType() {
        return ceType;
    }

    protected void setCeType(String ceType) {
        this.ceType = ceType;
    }

    public String getCeCode() {
        return ceCode;
    }

    protected void setCeCode(String ceCode) {
        this.ceCode = ceCode;
    }

    public String getCeValue() {
        return ceValue;
    }

    protected void setCeValue(String ceValue) {
        this.ceValue = ceValue;
    }

    public String getRemark() {
        return remark;
    }

    protected void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisRcFbdCaseExamination other = (CisRcFbdCaseExamination) obj;
        return Objects.equals(id, other.id);
    }

    public CisRcFbdCaseExamination create(CisRcFbdCaseExaminationNto cisRcFbdCaseExaminationNto) {
        Assert.notNull(cisRcFbdCaseExaminationNto, "参数cisRcFbdCaseExaminationNto不能为空！");

        setId(cisRcFbdCaseExaminationNto.getId());
        setFbdCode(cisRcFbdCaseExaminationNto.getFbdCode());
        setCeType(cisRcFbdCaseExaminationNto.getCeType());
        setCeCode(cisRcFbdCaseExaminationNto.getCeCode());
        setCeValue(cisRcFbdCaseExaminationNto.getCeValue());
        setRemark(cisRcFbdCaseExaminationNto.getRemark());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        setDeleted(false);
        dao().save(this);
        return this;
    }

    public void update(CisRcFbdCaseExaminationEto cisRcFbdCaseExaminationEto) {
        setFbdCode(cisRcFbdCaseExaminationEto.getFbdCode());
        setCeType(cisRcFbdCaseExaminationEto.getCeType());
        setCeCode(cisRcFbdCaseExaminationEto.getCeCode());
        setCeValue(cisRcFbdCaseExaminationEto.getCeValue());
        setRemark(cisRcFbdCaseExaminationEto.getRemark());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
    }

    public void delete() {
        setDeleted(true);
    }

}
