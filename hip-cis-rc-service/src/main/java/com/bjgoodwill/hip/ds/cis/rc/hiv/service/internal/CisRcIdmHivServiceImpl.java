package com.bjgoodwill.hip.ds.cis.rc.hiv.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rc.hiv.entity.CisRcIdmHiv;
import com.bjgoodwill.hip.ds.cis.rc.hiv.service.CisRcIdmHivService;
import com.bjgoodwill.hip.ds.cis.rc.hiv.service.internal.assembler.CisRcIdmHivAssembler;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivEto;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivNto;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivQto;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.rc.hiv.service.CisRcIdmHivService")
@RequestMapping(value = "/api/cisrc/hiv/cisRcIdmHiv", produces = "application/json; charset=utf-8")
public class CisRcIdmHivServiceImpl implements CisRcIdmHivService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisRcIdmHivTo> getCisRcIdmHivs(CisRcIdmHivQto cisRcIdmHivQto) {
        return CisRcIdmHivAssembler.toTos(CisRcIdmHiv.getCisRcIdmHivs(cisRcIdmHivQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisRcIdmHivTo> getCisRcIdmHivPage(CisRcIdmHivQto cisRcIdmHivQto) {
        Page<CisRcIdmHiv> page = CisRcIdmHiv.getCisRcIdmHivPage(cisRcIdmHivQto);
        Page<CisRcIdmHivTo> result = page.map(CisRcIdmHivAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisRcIdmHivTo getCisRcIdmHivById(String id) {
        return CisRcIdmHivAssembler.toTo(CisRcIdmHiv.getCisRcIdmHivById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisRcIdmHivTo createCisRcIdmHiv(CisRcIdmHivNto cisRcIdmHivNto) {
        CisRcIdmHiv cisRcIdmHiv = new CisRcIdmHiv();
        cisRcIdmHiv = cisRcIdmHiv.create(cisRcIdmHivNto);
        return CisRcIdmHivAssembler.toTo(cisRcIdmHiv);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisRcIdmHiv(String id, CisRcIdmHivEto cisRcIdmHivEto) {
        Optional<CisRcIdmHiv> cisRcIdmHivOptional = CisRcIdmHiv.getCisRcIdmHivById(id);
        cisRcIdmHivOptional.ifPresent(cisRcIdmHiv -> cisRcIdmHiv.update(cisRcIdmHivEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisRcIdmHiv(String id) {
        Optional<CisRcIdmHiv> cisRcIdmHivOptional = CisRcIdmHiv.getCisRcIdmHivById(id);
        cisRcIdmHivOptional.ifPresent(cisRcIdmHiv -> cisRcIdmHiv.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}