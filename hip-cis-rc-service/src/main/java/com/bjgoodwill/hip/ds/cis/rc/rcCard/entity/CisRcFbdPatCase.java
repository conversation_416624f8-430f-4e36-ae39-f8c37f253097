package com.bjgoodwill.hip.ds.cis.rc.rcCard.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.repository.CisRcFbdPatCaseRepository;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "食源性疾病-患者病例信息")
@Table(name = "cis_rc_fbd_pat_case", indexes = {@Index(name = "idx_cis_rc_fbd_pat_case_report_no", columnList = "report_no"),
        @Index(name = "idx_cis_rc_fbd_pat_case_visit_code", columnList = "visit_code"),
        @Index(name = "idx_cis_rc_fbd_pat_case_fbd_code", columnList = "fbd_code")}, uniqueConstraints = {})
public class CisRcFbdPatCase extends RcCard {

    @Comment("病历编码")
    @Column(name = "fbd_code", nullable = true)
    private String fbdCode;


    @Comment("就诊科室编码")
    @Column(name = "org_code", nullable = false)
    private String orgCode;


    @Comment("就诊科室名称")
    @Column(name = "org_name", nullable = true)
    private String orgName;


    @Comment("就诊时间")
    @Column(name = "see_date", nullable = true)
    private LocalDateTime seeDate;


    @Comment("是否复诊:1是，0否")
    @Column(name = "repeat_flag", nullable = true)
    private Boolean repeatFlag;


    @Comment("是否住院:1是，0否")
    @Column(name = "in_hosp_flag", nullable = true)
    private Boolean inHospFlag;


    @Comment("就诊前是否使用抗生素：1是，0否")
    @Column(name = "antibiotic_flag", nullable = true)
    private Boolean antibioticFlag;


    @Comment("抗生素名称")
    @Column(name = "antibiotic_name", nullable = true)
    private String antibioticName;


    @Comment("接诊医生")
    @Column(name = "see_doc", nullable = true)
    private String seeDoc;


    @Comment("接诊医生姓名")
    @Column(name = "see_doc_name", nullable = true)
    private String seeDocName;

    public static Optional<CisRcFbdPatCase> getCisRcFbdPatCaseById(String id) {
        return dao().findById(id);
    }

    public static List<CisRcFbdPatCase> getCisRcFbdPatCases(CisRcFbdPatCaseQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisRcFbdPatCase> getCisRcFbdPatCasePage(CisRcFbdPatCaseQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisRcFbdPatCase> getSpecification(CisRcFbdPatCaseQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getReportNo())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportNo"), qto.getReportNo()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("name"), qto.getName()));
            }
            if (StringUtils.isNotBlank(qto.getParentsName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("parentsName"), qto.getParentsName()));
            }
            if (StringUtils.isNotBlank(qto.getSex())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
            }
            if (qto.getBirthDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
            }
            if (StringUtils.isNotBlank(qto.getEducation())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("education"), qto.getEducation()));
            }
            if (StringUtils.isNotBlank(qto.getReportUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUser"), qto.getReportUser()));
            }
            if (StringUtils.isNotBlank(qto.getReportUserName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUserName"), qto.getReportUserName()));
            }
            if (StringUtils.isNotBlank(qto.getUploadReportNo())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("uploadReportNo"), qto.getUploadReportNo()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getFbdCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("fbdCode"), qto.getFbdCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgCode"), qto.getOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrgName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgName"), qto.getOrgName()));
            }
            if (qto.getSeeDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("seeDate"), LocalDateUtil.beginOfDay(qto.getSeeDate()), LocalDateUtil.endOfDay(qto.getSeeDate())));
            }
            if (qto.getRepeatFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("repeatFlag"), qto.getRepeatFlag()));
            }
            if (qto.getInHospFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("inHospFlag"), qto.getInHospFlag()));
            }
            if (qto.getAntibioticFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("antibioticFlag"), qto.getAntibioticFlag()));
            }
            if (StringUtils.isNotBlank(qto.getAntibioticName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("antibioticName"), qto.getAntibioticName()));
            }
            if (StringUtils.isNotBlank(qto.getSeeDoc())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("seeDoc"), qto.getSeeDoc()));
            }
            if (StringUtils.isNotBlank(qto.getSeeDocName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("seeDocName"), qto.getSeeDocName()));
            }
            return predicate;
        };
    }

    private static CisRcFbdPatCaseRepository dao() {
        return SpringUtil.getBean(CisRcFbdPatCaseRepository.class);
    }

    public String getFbdCode() {
        return fbdCode;
    }

    protected void setFbdCode(String fbdCode) {
        this.fbdCode = fbdCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    protected void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    protected void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public LocalDateTime getSeeDate() {
        return seeDate;
    }

    protected void setSeeDate(LocalDateTime seeDate) {
        this.seeDate = seeDate;
    }

    public Boolean getRepeatFlag() {
        return repeatFlag;
    }

    protected void setRepeatFlag(Boolean repeatFlag) {
        this.repeatFlag = repeatFlag;
    }

    public Boolean getInHospFlag() {
        return inHospFlag;
    }

    protected void setInHospFlag(Boolean inHospFlag) {
        this.inHospFlag = inHospFlag;
    }

    public Boolean getAntibioticFlag() {
        return antibioticFlag;
    }

    protected void setAntibioticFlag(Boolean antibioticFlag) {
        this.antibioticFlag = antibioticFlag;
    }

    public String getAntibioticName() {
        return antibioticName;
    }

    protected void setAntibioticName(String antibioticName) {
        this.antibioticName = antibioticName;
    }

    public String getSeeDoc() {
        return seeDoc;
    }

    protected void setSeeDoc(String seeDoc) {
        this.seeDoc = seeDoc;
    }

    public String getSeeDocName() {
        return seeDocName;
    }

    protected void setSeeDocName(String seeDocName) {
        this.seeDocName = seeDocName;
    }

    @Override
    public RcCard create(RcCardNto rcCardNto) {
        return create((CisRcFbdPatCaseNto) rcCardNto);
    }

    @Override
    public void update(RcCardEto rcCardEto) {
        update((CisRcFbdPatCaseEto) rcCardEto);
    }

    public CisRcFbdPatCase create(CisRcFbdPatCaseNto cisRcFbdPatCaseNto) {
        Assert.notNull(cisRcFbdPatCaseNto, "参数cisRcFbdPatCaseNto不能为空！");
        super.create(cisRcFbdPatCaseNto);

        setFbdCode(cisRcFbdPatCaseNto.getFbdCode());
        setOrgCode(cisRcFbdPatCaseNto.getOrgCode());
        setOrgName(cisRcFbdPatCaseNto.getOrgName());
        setSeeDate(cisRcFbdPatCaseNto.getSeeDate());
        setRepeatFlag(cisRcFbdPatCaseNto.getRepeatFlag());
        setInHospFlag(cisRcFbdPatCaseNto.getInHospFlag());
        setAntibioticFlag(cisRcFbdPatCaseNto.getAntibioticFlag());
        setAntibioticName(cisRcFbdPatCaseNto.getAntibioticName());
        setSeeDoc(cisRcFbdPatCaseNto.getSeeDoc());
        setSeeDocName(cisRcFbdPatCaseNto.getSeeDocName());
        dao().save(this);
        return this;
    }

    public void update(CisRcFbdPatCaseEto cisRcFbdPatCaseEto) {
        super.update(cisRcFbdPatCaseEto);
        setFbdCode(cisRcFbdPatCaseEto.getFbdCode());
        setOrgCode(cisRcFbdPatCaseEto.getOrgCode());
        setOrgName(cisRcFbdPatCaseEto.getOrgName());
        setSeeDate(cisRcFbdPatCaseEto.getSeeDate());
        setRepeatFlag(cisRcFbdPatCaseEto.getRepeatFlag());
        setInHospFlag(cisRcFbdPatCaseEto.getInHospFlag());
        setAntibioticFlag(cisRcFbdPatCaseEto.getAntibioticFlag());
        setAntibioticName(cisRcFbdPatCaseEto.getAntibioticName());
        setSeeDoc(cisRcFbdPatCaseEto.getSeeDoc());
        setSeeDocName(cisRcFbdPatCaseEto.getSeeDocName());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
