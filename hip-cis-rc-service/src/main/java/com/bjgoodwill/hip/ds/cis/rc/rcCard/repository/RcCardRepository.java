package com.bjgoodwill.hip.ds.cis.rc.rcCard.repository;

import com.bjgoodwill.hip.ds.cis.rc.rcCard.entity.RcCard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.rc.rcCard.repository.RcCardRepository")
public interface RcCardRepository extends JpaRepository<RcCard, String>, JpaSpecificationExecutor<RcCard> {

    boolean existsByReportNo(String reportNo);

    boolean existsByReportNoAndIdNot(String reportNo, String id);

}