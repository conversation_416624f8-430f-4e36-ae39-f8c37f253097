package com.bjgoodwill.hip.ds.cis.rc.fbd.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.rc.fbd.repository.CisRcFbdExposureInfoRepository;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoEto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoNto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "食源性疾病-暴露信息")
@Table(name = "cis_rc_fbd_exposure_info", indexes = {@Index(name = "idx_cis_rc_fbd_exposure_info_fbd_code", columnList = "fbd_code")}, uniqueConstraints = {})
public class CisRcFbdExposureInfo {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("病历编码")
    @Column(name = "fbd_code", nullable = false)
    private String fbdCode;


    @Comment("食品名称")
    @Column(name = "food_name", nullable = true)
    private String foodName;


    @Comment("食品分类：字典fbd_foodclass")
    @Column(name = "food_type", nullable = true)
    private String foodType;


    @Comment("加工或包装方式 字典fbd_foodpack	")
    @Column(name = "food_pack", nullable = true)
    private String foodPack;


    @Comment("食品品牌")
    @Column(name = "food_brand", nullable = true)
    private String foodBrand;


    @Comment("生产厂家")
    @Column(name = "manu_firm_name", nullable = true)
    private String manuFirmName;


    @Comment("进食场所分类编码字典fbd_eatplacetype")
    @Column(name = "eat_place_type", nullable = true)
    private String eatPlaceType;


    @Comment("进食场所编码字典fbd_eatplacetype")
    @Column(name = "eat_place_code", nullable = true)
    private String eatPlaceCode;


    @Comment("进食地域0境内，1境外")
    @Column(name = "eat_region_flag", nullable = true)
    private Boolean eatRegionFlag;


    @Comment("进食地点-省	字典administrativedivesions")
    @Column(name = "eat_province", nullable = true)
    private String eatProvince;


    @Comment("进食地点-市	administrativedivesions")
    @Column(name = "eat_city", nullable = true)
    private String eatCity;


    @Comment("进食地点-区	administrativedivesions")
    @Column(name = "eat_county", nullable = true)
    private String eatCounty;


    @Comment("进食地点-详细地址")
    @Column(name = "eat_village", nullable = true)
    private String eatVillage;


    @Comment("进食人数")
    @Column(name = "eat_num", nullable = true)
    private Integer eatNum;


    @Comment("进食时间")
    @Column(name = "eat_date", nullable = true)
    private LocalDateTime eatDate;


    @Comment("购买场所类型编码 字典fbd_placetype	")
    @Column(name = "purc_place_type", nullable = true)
    private String purcPlaceType;


    @Comment("购买场所编码")
    @Column(name = "purc_place_code", nullable = true)
    private String purcPlaceCode;


    @Comment("购买地域0境内，1境外")
    @Column(name = "purc_region", nullable = true)
    private Boolean purcRegion;


    @Comment("购买地点-省	字典administrativedivesions")
    @Column(name = "purc_province", nullable = true)
    private String purcProvince;


    @Comment("购买地点-市	administrativedivesions")
    @Column(name = "purc_city", nullable = true)
    private String purcCity;


    @Comment("购买地点-区	administrativedivesions")
    @Column(name = "purc_county", nullable = true)
    private String purcCounty;


    @Comment("购买地点-详细地址	")
    @Column(name = "purc_village", nullable = true)
    private String purcVillage;


    @Comment("其他人是否发病: 0否，1是，2未知")
    @Column(name = "other_pat_flag", nullable = true)
    private Boolean otherPatFlag;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    private boolean deleted;

    public static Optional<CisRcFbdExposureInfo> getCisRcFbdExposureInfoById(String id) {
        return dao().findById(id);
    }

    public static List<CisRcFbdExposureInfo> getCisRcFbdExposureInfoes(CisRcFbdExposureInfoQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisRcFbdExposureInfo> getCisRcFbdExposureInfoPage(CisRcFbdExposureInfoQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisRcFbdExposureInfo> getSpecification(CisRcFbdExposureInfoQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getFbdCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("fbdCode"), qto.getFbdCode()));
            }
            if (StringUtils.isNotBlank(qto.getFoodName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("foodName"), qto.getFoodName()));
            }
            if (StringUtils.isNotBlank(qto.getFoodType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("foodType"), qto.getFoodType()));
            }
            if (StringUtils.isNotBlank(qto.getFoodPack())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("foodPack"), qto.getFoodPack()));
            }
            if (StringUtils.isNotBlank(qto.getFoodBrand())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("foodBrand"), qto.getFoodBrand()));
            }
            if (StringUtils.isNotBlank(qto.getManuFirmName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("manuFirmName"), qto.getManuFirmName()));
            }
            if (StringUtils.isNotBlank(qto.getEatPlaceType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eatPlaceType"), qto.getEatPlaceType()));
            }
            if (StringUtils.isNotBlank(qto.getEatPlaceCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eatPlaceCode"), qto.getEatPlaceCode()));
            }
            if (qto.getEatRegionFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eatRegionFlag"), qto.getEatRegionFlag()));
            }
            if (StringUtils.isNotBlank(qto.getEatProvince())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eatProvince"), qto.getEatProvince()));
            }
            if (StringUtils.isNotBlank(qto.getEatCity())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eatCity"), qto.getEatCity()));
            }
            if (StringUtils.isNotBlank(qto.getEatCounty())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eatCounty"), qto.getEatCounty()));
            }
            if (StringUtils.isNotBlank(qto.getEatVillage())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eatVillage"), qto.getEatVillage()));
            }
            if (qto.getEatNum() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("eatNum"), qto.getEatNum()));
            }
            if (qto.getEatDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("eatDate"), LocalDateUtil.beginOfDay(qto.getEatDate()), LocalDateUtil.endOfDay(qto.getEatDate())));
            }
            if (StringUtils.isNotBlank(qto.getPurcPlaceType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("purcPlaceType"), qto.getPurcPlaceType()));
            }
            if (StringUtils.isNotBlank(qto.getPurcPlaceCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("purcPlaceCode"), qto.getPurcPlaceCode()));
            }
            if (qto.getPurcRegion() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("purcRegion"), qto.getPurcRegion()));
            }
            if (StringUtils.isNotBlank(qto.getPurcProvince())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("purcProvince"), qto.getPurcProvince()));
            }
            if (StringUtils.isNotBlank(qto.getPurcCity())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("purcCity"), qto.getPurcCity()));
            }
            if (StringUtils.isNotBlank(qto.getPurcCounty())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("purcCounty"), qto.getPurcCounty()));
            }
            if (StringUtils.isNotBlank(qto.getPurcVillage())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("purcVillage"), qto.getPurcVillage()));
            }
            if (qto.getOtherPatFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("otherPatFlag"), qto.getOtherPatFlag()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            return predicate;
        };
    }

    private static CisRcFbdExposureInfoRepository dao() {
        return SpringUtil.getBean(CisRcFbdExposureInfoRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getFbdCode() {
        return fbdCode;
    }

    protected void setFbdCode(String fbdCode) {
        this.fbdCode = fbdCode;
    }

    public String getFoodName() {
        return foodName;
    }

    protected void setFoodName(String foodName) {
        this.foodName = foodName;
    }

    public String getFoodType() {
        return foodType;
    }

    protected void setFoodType(String foodType) {
        this.foodType = foodType;
    }

    public String getFoodPack() {
        return foodPack;
    }

    protected void setFoodPack(String foodPack) {
        this.foodPack = foodPack;
    }

    public String getFoodBrand() {
        return foodBrand;
    }

    protected void setFoodBrand(String foodBrand) {
        this.foodBrand = foodBrand;
    }

    public String getManuFirmName() {
        return manuFirmName;
    }

    protected void setManuFirmName(String manuFirmName) {
        this.manuFirmName = manuFirmName;
    }

    public String getEatPlaceType() {
        return eatPlaceType;
    }

    protected void setEatPlaceType(String eatPlaceType) {
        this.eatPlaceType = eatPlaceType;
    }

    public String getEatPlaceCode() {
        return eatPlaceCode;
    }

    protected void setEatPlaceCode(String eatPlaceCode) {
        this.eatPlaceCode = eatPlaceCode;
    }

    public Boolean getEatRegionFlag() {
        return eatRegionFlag;
    }

    protected void setEatRegionFlag(Boolean eatRegionFlag) {
        this.eatRegionFlag = eatRegionFlag;
    }

    public String getEatProvince() {
        return eatProvince;
    }

    protected void setEatProvince(String eatProvince) {
        this.eatProvince = eatProvince;
    }

    public String getEatCity() {
        return eatCity;
    }

    protected void setEatCity(String eatCity) {
        this.eatCity = eatCity;
    }

    public String getEatCounty() {
        return eatCounty;
    }

    protected void setEatCounty(String eatCounty) {
        this.eatCounty = eatCounty;
    }

    public String getEatVillage() {
        return eatVillage;
    }

    protected void setEatVillage(String eatVillage) {
        this.eatVillage = eatVillage;
    }

    public Integer getEatNum() {
        return eatNum;
    }

    protected void setEatNum(Integer eatNum) {
        this.eatNum = eatNum;
    }

    public LocalDateTime getEatDate() {
        return eatDate;
    }

    protected void setEatDate(LocalDateTime eatDate) {
        this.eatDate = eatDate;
    }

    public String getPurcPlaceType() {
        return purcPlaceType;
    }

    protected void setPurcPlaceType(String purcPlaceType) {
        this.purcPlaceType = purcPlaceType;
    }

    public String getPurcPlaceCode() {
        return purcPlaceCode;
    }

    protected void setPurcPlaceCode(String purcPlaceCode) {
        this.purcPlaceCode = purcPlaceCode;
    }

    public Boolean getPurcRegion() {
        return purcRegion;
    }

    protected void setPurcRegion(Boolean purcRegion) {
        this.purcRegion = purcRegion;
    }

    public String getPurcProvince() {
        return purcProvince;
    }

    protected void setPurcProvince(String purcProvince) {
        this.purcProvince = purcProvince;
    }

    public String getPurcCity() {
        return purcCity;
    }

    protected void setPurcCity(String purcCity) {
        this.purcCity = purcCity;
    }

    public String getPurcCounty() {
        return purcCounty;
    }

    protected void setPurcCounty(String purcCounty) {
        this.purcCounty = purcCounty;
    }

    public String getPurcVillage() {
        return purcVillage;
    }

    protected void setPurcVillage(String purcVillage) {
        this.purcVillage = purcVillage;
    }

    public Boolean getOtherPatFlag() {
        return otherPatFlag;
    }

    protected void setOtherPatFlag(Boolean otherPatFlag) {
        this.otherPatFlag = otherPatFlag;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisRcFbdExposureInfo other = (CisRcFbdExposureInfo) obj;
        return Objects.equals(id, other.id);
    }

    public CisRcFbdExposureInfo create(CisRcFbdExposureInfoNto cisRcFbdExposureInfoNto) {
        Assert.notNull(cisRcFbdExposureInfoNto, "参数cisRcFbdExposureInfoNto不能为空！");

        setId(cisRcFbdExposureInfoNto.getId());
        setFbdCode(cisRcFbdExposureInfoNto.getFbdCode());
        setFoodName(cisRcFbdExposureInfoNto.getFoodName());
        setFoodType(cisRcFbdExposureInfoNto.getFoodType());
        setFoodPack(cisRcFbdExposureInfoNto.getFoodPack());
        setFoodBrand(cisRcFbdExposureInfoNto.getFoodBrand());
        setManuFirmName(cisRcFbdExposureInfoNto.getManuFirmName());
        setEatPlaceType(cisRcFbdExposureInfoNto.getEatPlaceType());
        setEatPlaceCode(cisRcFbdExposureInfoNto.getEatPlaceCode());
        setEatRegionFlag(cisRcFbdExposureInfoNto.getEatRegionFlag());
        setEatProvince(cisRcFbdExposureInfoNto.getEatProvince());
        setEatCity(cisRcFbdExposureInfoNto.getEatCity());
        setEatCounty(cisRcFbdExposureInfoNto.getEatCounty());
        setEatVillage(cisRcFbdExposureInfoNto.getEatVillage());
        setEatNum(cisRcFbdExposureInfoNto.getEatNum());
        setEatDate(cisRcFbdExposureInfoNto.getEatDate());
        setPurcPlaceType(cisRcFbdExposureInfoNto.getPurcPlaceType());
        setPurcPlaceCode(cisRcFbdExposureInfoNto.getPurcPlaceCode());
        setPurcRegion(cisRcFbdExposureInfoNto.getPurcRegion());
        setPurcProvince(cisRcFbdExposureInfoNto.getPurcProvince());
        setPurcCity(cisRcFbdExposureInfoNto.getPurcCity());
        setPurcCounty(cisRcFbdExposureInfoNto.getPurcCounty());
        setPurcVillage(cisRcFbdExposureInfoNto.getPurcVillage());
        setOtherPatFlag(cisRcFbdExposureInfoNto.getOtherPatFlag());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        setDeleted(false);
        dao().save(this);
        return this;
    }

    public void update(CisRcFbdExposureInfoEto cisRcFbdExposureInfoEto) {
        setFbdCode(cisRcFbdExposureInfoEto.getFbdCode());
        setFoodName(cisRcFbdExposureInfoEto.getFoodName());
        setFoodType(cisRcFbdExposureInfoEto.getFoodType());
        setFoodPack(cisRcFbdExposureInfoEto.getFoodPack());
        setFoodBrand(cisRcFbdExposureInfoEto.getFoodBrand());
        setManuFirmName(cisRcFbdExposureInfoEto.getManuFirmName());
        setEatPlaceType(cisRcFbdExposureInfoEto.getEatPlaceType());
        setEatPlaceCode(cisRcFbdExposureInfoEto.getEatPlaceCode());
        setEatRegionFlag(cisRcFbdExposureInfoEto.getEatRegionFlag());
        setEatProvince(cisRcFbdExposureInfoEto.getEatProvince());
        setEatCity(cisRcFbdExposureInfoEto.getEatCity());
        setEatCounty(cisRcFbdExposureInfoEto.getEatCounty());
        setEatVillage(cisRcFbdExposureInfoEto.getEatVillage());
        setEatNum(cisRcFbdExposureInfoEto.getEatNum());
        setEatDate(cisRcFbdExposureInfoEto.getEatDate());
        setPurcPlaceType(cisRcFbdExposureInfoEto.getPurcPlaceType());
        setPurcPlaceCode(cisRcFbdExposureInfoEto.getPurcPlaceCode());
        setPurcRegion(cisRcFbdExposureInfoEto.getPurcRegion());
        setPurcProvince(cisRcFbdExposureInfoEto.getPurcProvince());
        setPurcCity(cisRcFbdExposureInfoEto.getPurcCity());
        setPurcCounty(cisRcFbdExposureInfoEto.getPurcCounty());
        setPurcVillage(cisRcFbdExposureInfoEto.getPurcVillage());
        setOtherPatFlag(cisRcFbdExposureInfoEto.getOtherPatFlag());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
    }

    public void delete() {
        setDeleted(true);
    }

}
