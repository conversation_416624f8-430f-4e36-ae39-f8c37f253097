package com.bjgoodwill.hip.ds.cis.rc.rcCard.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.rc.rcCard.entity.CisRcFbdPatCase;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcFbdPatCaseTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisRcFbdPatCaseAssembler {

    public static List<CisRcFbdPatCaseTo> toTos(List<CisRcFbdPatCase> cisRcFbdPatCases) {
        return toTos(cisRcFbdPatCases, false);
    }

    public static List<CisRcFbdPatCaseTo> toTos(List<CisRcFbdPatCase> cisRcFbdPatCases, boolean withAllParts) {
        Assert.notNull(cisRcFbdPatCases, "参数cisRcFbdPatCases不能为空！");

        List<CisRcFbdPatCaseTo> tos = new ArrayList<>();
        for (CisRcFbdPatCase cisRcFbdPatCase : cisRcFbdPatCases)
            tos.add(toTo(cisRcFbdPatCase, withAllParts));
        return tos;
    }

    public static CisRcFbdPatCaseTo toTo(CisRcFbdPatCase cisRcFbdPatCase) {
        return toTo(cisRcFbdPatCase, false);
    }

    /**
     * @generated
     */
    public static CisRcFbdPatCaseTo toTo(CisRcFbdPatCase cisRcFbdPatCase, boolean withAllParts) {
        if (cisRcFbdPatCase == null)
            return null;
        CisRcFbdPatCaseTo to = new CisRcFbdPatCaseTo();
        to.setId(cisRcFbdPatCase.getId());
        to.setReportNo(cisRcFbdPatCase.getReportNo());
        to.setVisitType(cisRcFbdPatCase.getVisitType());
        to.setPatMiCode(cisRcFbdPatCase.getPatMiCode());
        to.setVisitCode(cisRcFbdPatCase.getVisitCode());
        to.setName(cisRcFbdPatCase.getName());
        to.setParentsName(cisRcFbdPatCase.getParentsName());
        to.setSex(cisRcFbdPatCase.getSex());
        to.setBirthDate(cisRcFbdPatCase.getBirthDate());
        to.setAge(cisRcFbdPatCase.getAge());
        to.setAgeUnit(cisRcFbdPatCase.getAgeUnit());
        to.setIdNo(cisRcFbdPatCase.getIdNo());
        to.setCompanyName(cisRcFbdPatCase.getCompanyName());
        to.setContactTel(cisRcFbdPatCase.getContactTel());
        to.setHomeArea(cisRcFbdPatCase.getHomeArea());
        to.setProvince(cisRcFbdPatCase.getProvince());
        to.setCity(cisRcFbdPatCase.getCity());
        to.setCounty(cisRcFbdPatCase.getCounty());
        to.setHomeJd(cisRcFbdPatCase.getHomeJd());
        to.setVillage(cisRcFbdPatCase.getVillage());
        to.setHouseNo(cisRcFbdPatCase.getHouseNo());
        to.setCzProvince(cisRcFbdPatCase.getCzProvince());
        to.setCzCity(cisRcFbdPatCase.getCzCity());
        to.setCzCounty(cisRcFbdPatCase.getCzCounty());
        to.setCzHomeJd(cisRcFbdPatCase.getCzHomeJd());
        to.setNation(cisRcFbdPatCase.getNation());
        to.setCzVillage(cisRcFbdPatCase.getCzVillage());
        to.setCzHouseNo(cisRcFbdPatCase.getCzHouseNo());
        to.setMaritalStatus(cisRcFbdPatCase.getMaritalStatus());
        to.setEducation(cisRcFbdPatCase.getEducation());
        to.setWork(cisRcFbdPatCase.getWork());
        to.setGuardianName(cisRcFbdPatCase.getGuardianName());
        to.setInfectDate(cisRcFbdPatCase.getInfectDate());
        to.setDiagnosisDate(cisRcFbdPatCase.getDiagnosisDate());
        to.setDeadDate(cisRcFbdPatCase.getDeadDate());
        to.setDeadWhy(cisRcFbdPatCase.getDeadWhy());
        to.setDiseaseClass(cisRcFbdPatCase.getDiseaseClass());
        to.setDiseaseCode(cisRcFbdPatCase.getDiseaseCode());
        to.setReportUnitCategory(cisRcFbdPatCase.getReportUnitCategory());
        to.setReportUnit(cisRcFbdPatCase.getReportUnit());
        to.setUnitTel(cisRcFbdPatCase.getUnitTel());
        to.setReportUser(cisRcFbdPatCase.getReportUser());
        to.setReportUserName(cisRcFbdPatCase.getReportUserName());
        to.setOrgCode(cisRcFbdPatCase.getOrgCode());
        to.setOrgName(cisRcFbdPatCase.getOrgName());
        to.setReportCounty(cisRcFbdPatCase.getReportCounty());
        to.setRemarks(cisRcFbdPatCase.getRemarks());
        to.setUploadReportNo(cisRcFbdPatCase.getUploadReportNo());
        to.setHospitalCode(cisRcFbdPatCase.getHospitalCode());
        to.setCreatedStaff(cisRcFbdPatCase.getCreatedStaff());
        to.setUpdatedStaffName(cisRcFbdPatCase.getUpdatedStaffName());
        to.setCreatedDate(cisRcFbdPatCase.getCreatedDate());
        to.setUpdatedDate(cisRcFbdPatCase.getUpdatedDate());
        to.setFbdCode(cisRcFbdPatCase.getFbdCode());
        to.setOrgCode(cisRcFbdPatCase.getOrgCode());
        to.setOrgName(cisRcFbdPatCase.getOrgName());
        to.setSeeDate(cisRcFbdPatCase.getSeeDate());
        to.setRepeatFlag(cisRcFbdPatCase.getRepeatFlag());
        to.setInHospFlag(cisRcFbdPatCase.getInHospFlag());
        to.setAntibioticFlag(cisRcFbdPatCase.getAntibioticFlag());
        to.setAntibioticName(cisRcFbdPatCase.getAntibioticName());
        to.setSeeDoc(cisRcFbdPatCase.getSeeDoc());
        to.setSeeDocName(cisRcFbdPatCase.getSeeDocName());

        if (withAllParts) {
        }
        return to;
    }

}