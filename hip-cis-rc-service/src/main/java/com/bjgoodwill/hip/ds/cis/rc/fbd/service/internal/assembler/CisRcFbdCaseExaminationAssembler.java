package com.bjgoodwill.hip.ds.cis.rc.fbd.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.rc.fbd.entity.CisRcFbdCaseExamination;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdCaseExaminationTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisRcFbdCaseExaminationAssembler {

    public static List<CisRcFbdCaseExaminationTo> toTos(List<CisRcFbdCaseExamination> cisRcFbdCaseExaminations) {
        return toTos(cisRcFbdCaseExaminations, false);
    }

    public static List<CisRcFbdCaseExaminationTo> toTos(List<CisRcFbdCaseExamination> cisRcFbdCaseExaminations, boolean withAllParts) {
        Assert.notNull(cisRcFbdCaseExaminations, "参数cisRcFbdCaseExaminations不能为空！");

        List<CisRcFbdCaseExaminationTo> tos = new ArrayList<>();
        for (CisRcFbdCaseExamination cisRcFbdCaseExamination : cisRcFbdCaseExaminations)
            tos.add(toTo(cisRcFbdCaseExamination, withAllParts));
        return tos;
    }

    public static CisRcFbdCaseExaminationTo toTo(CisRcFbdCaseExamination cisRcFbdCaseExamination) {
        return toTo(cisRcFbdCaseExamination, false);
    }

    /**
     * @generated
     */
    public static CisRcFbdCaseExaminationTo toTo(CisRcFbdCaseExamination cisRcFbdCaseExamination, boolean withAllParts) {
        if (cisRcFbdCaseExamination == null)
            return null;
        CisRcFbdCaseExaminationTo to = new CisRcFbdCaseExaminationTo();
        to.setId(cisRcFbdCaseExamination.getId());
        to.setFbdCode(cisRcFbdCaseExamination.getFbdCode());
        to.setCeType(cisRcFbdCaseExamination.getCeType());
        to.setCeCode(cisRcFbdCaseExamination.getCeCode());
        to.setCeValue(cisRcFbdCaseExamination.getCeValue());
        to.setRemark(cisRcFbdCaseExamination.getRemark());
        to.setCreatedStaff(cisRcFbdCaseExamination.getCreatedStaff());
        to.setCreatedStaffName(cisRcFbdCaseExamination.getCreatedStaffName());
        to.setCreatedDate(cisRcFbdCaseExamination.getCreatedDate());
        to.setUpdatedStaff(cisRcFbdCaseExamination.getUpdatedStaff());
        to.setUpdatedStaffName(cisRcFbdCaseExamination.getUpdatedStaffName());
        to.setUpdatedDate(cisRcFbdCaseExamination.getUpdatedDate());
        to.setDeleted(cisRcFbdCaseExamination.isDeleted());

        if (withAllParts) {
        }
        return to;
    }

}