package com.bjgoodwill.hip.ds.cis.rc.hiv.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.rc.hiv.entity.CisRcIdmHiv;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisRcIdmHivAssembler {

    public static List<CisRcIdmHivTo> toTos(List<CisRcIdmHiv> cisRcIdmHivs) {
        return toTos(cisRcIdmHivs, false);
    }

    public static List<CisRcIdmHivTo> toTos(List<CisRcIdmHiv> cisRcIdmHivs, boolean withAllParts) {
        Assert.notNull(cisRcIdmHivs, "参数cisRcIdmHivs不能为空！");

        List<CisRcIdmHivTo> tos = new ArrayList<>();
        for (CisRcIdmHiv cisRcIdmHiv : cisRcIdmHivs)
            tos.add(toTo(cisRcIdmHiv, withAllParts));
        return tos;
    }

    public static CisRcIdmHivTo toTo(CisRcIdmHiv cisRcIdmHiv) {
        return toTo(cisRcIdmHiv, false);
    }

    /**
     * @generated
     */
    public static CisRcIdmHivTo toTo(CisRcIdmHiv cisRcIdmHiv, boolean withAllParts) {
        if (cisRcIdmHiv == null)
            return null;
        CisRcIdmHivTo to = new CisRcIdmHivTo();
        to.setId(cisRcIdmHiv.getId());
        to.setCisRcIdmId(cisRcIdmHiv.getCisRcIdmId());
        to.setContact(cisRcIdmHiv.getContact());
        to.setContactOther(cisRcIdmHiv.getContactOther());
        to.setStdtDisease(cisRcIdmHiv.getStdtDisease());
        to.setInfection(cisRcIdmHiv.getInfection());
        to.setInfectionOther(cisRcIdmHiv.getInfectionOther());
        to.setSampleSource(cisRcIdmHiv.getSampleSource());
        to.setSampleOther(cisRcIdmHiv.getSampleOther());
        to.setDetectionConclusion(cisRcIdmHiv.getDetectionConclusion());
        to.setDetectionDate(cisRcIdmHiv.getDetectionDate());
        to.setDetectionUnit(cisRcIdmHiv.getDetectionUnit());
        to.setDetectionUnitName(cisRcIdmHiv.getDetectionUnitName());
        to.setHivConfirmDate(cisRcIdmHiv.getHivConfirmDate());

        if (withAllParts) {
        }
        return to;
    }

}