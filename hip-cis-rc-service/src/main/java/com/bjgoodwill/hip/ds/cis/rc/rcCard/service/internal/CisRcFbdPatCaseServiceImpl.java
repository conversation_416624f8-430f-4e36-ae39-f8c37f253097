package com.bjgoodwill.hip.ds.cis.rc.rcCard.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.entity.CisRcFbdPatCase;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.service.CisRcFbdPatCaseService;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.service.internal.assembler.CisRcFbdPatCaseAssembler;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcFbdPatCaseEto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcFbdPatCaseNto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcFbdPatCaseQto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcFbdPatCaseTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.rc.rcCard.service.CisRcFbdPatCaseService")
@RequestMapping(value = "/api/cisrc/rcCard/cisRcFbdPatCase", produces = "application/json; charset=utf-8")
public class CisRcFbdPatCaseServiceImpl implements CisRcFbdPatCaseService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisRcFbdPatCaseTo> getCisRcFbdPatCases(CisRcFbdPatCaseQto cisRcFbdPatCaseQto) {
        return CisRcFbdPatCaseAssembler.toTos(CisRcFbdPatCase.getCisRcFbdPatCases(cisRcFbdPatCaseQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisRcFbdPatCaseTo> getCisRcFbdPatCasePage(CisRcFbdPatCaseQto cisRcFbdPatCaseQto) {
        Page<CisRcFbdPatCase> page = CisRcFbdPatCase.getCisRcFbdPatCasePage(cisRcFbdPatCaseQto);
        Page<CisRcFbdPatCaseTo> result = page.map(CisRcFbdPatCaseAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisRcFbdPatCaseTo getCisRcFbdPatCaseById(String id) {
        return CisRcFbdPatCaseAssembler.toTo(CisRcFbdPatCase.getCisRcFbdPatCaseById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisRcFbdPatCaseTo createCisRcFbdPatCase(CisRcFbdPatCaseNto cisRcFbdPatCaseNto) {
        CisRcFbdPatCase cisRcFbdPatCase = new CisRcFbdPatCase();
        cisRcFbdPatCase = cisRcFbdPatCase.create(cisRcFbdPatCaseNto);
        return CisRcFbdPatCaseAssembler.toTo(cisRcFbdPatCase);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisRcFbdPatCase(String id, CisRcFbdPatCaseEto cisRcFbdPatCaseEto) {
        Optional<CisRcFbdPatCase> cisRcFbdPatCaseOptional = CisRcFbdPatCase.getCisRcFbdPatCaseById(id);
        cisRcFbdPatCaseOptional.ifPresent(cisRcFbdPatCase -> cisRcFbdPatCase.update(cisRcFbdPatCaseEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisRcFbdPatCase(String id) {
        Optional<CisRcFbdPatCase> cisRcFbdPatCaseOptional = CisRcFbdPatCase.getCisRcFbdPatCaseById(id);
        cisRcFbdPatCaseOptional.ifPresent(cisRcFbdPatCase -> cisRcFbdPatCase.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}