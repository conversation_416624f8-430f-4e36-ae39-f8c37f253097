package com.bjgoodwill.hip.ds.cis.rc.fbd.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rc.fbd.entity.CisRcFbdExposureInfo;
import com.bjgoodwill.hip.ds.cis.rc.fbd.service.CisRcFbdExposureInfoService;
import com.bjgoodwill.hip.ds.cis.rc.fbd.service.internal.assembler.CisRcFbdExposureInfoAssembler;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoEto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoNto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoQto;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdExposureInfoTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.rc.fbd.service.CisRcFbdExposureInfoService")
@RequestMapping(value = "/api/cisrc/fbd/cisRcFbdExposureInfo", produces = "application/json; charset=utf-8")
public class CisRcFbdExposureInfoServiceImpl implements CisRcFbdExposureInfoService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisRcFbdExposureInfoTo> getCisRcFbdExposureInfoes(CisRcFbdExposureInfoQto cisRcFbdExposureInfoQto) {
        return CisRcFbdExposureInfoAssembler.toTos(CisRcFbdExposureInfo.getCisRcFbdExposureInfoes(cisRcFbdExposureInfoQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisRcFbdExposureInfoTo> getCisRcFbdExposureInfoPage(CisRcFbdExposureInfoQto cisRcFbdExposureInfoQto) {
        Page<CisRcFbdExposureInfo> page = CisRcFbdExposureInfo.getCisRcFbdExposureInfoPage(cisRcFbdExposureInfoQto);
        Page<CisRcFbdExposureInfoTo> result = page.map(CisRcFbdExposureInfoAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisRcFbdExposureInfoTo getCisRcFbdExposureInfoById(String id) {
        return CisRcFbdExposureInfoAssembler.toTo(CisRcFbdExposureInfo.getCisRcFbdExposureInfoById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisRcFbdExposureInfoTo createCisRcFbdExposureInfo(CisRcFbdExposureInfoNto cisRcFbdExposureInfoNto) {
        CisRcFbdExposureInfo cisRcFbdExposureInfo = new CisRcFbdExposureInfo();
        cisRcFbdExposureInfo = cisRcFbdExposureInfo.create(cisRcFbdExposureInfoNto);
        return CisRcFbdExposureInfoAssembler.toTo(cisRcFbdExposureInfo);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisRcFbdExposureInfo(String id, CisRcFbdExposureInfoEto cisRcFbdExposureInfoEto) {
        Optional<CisRcFbdExposureInfo> cisRcFbdExposureInfoOptional = CisRcFbdExposureInfo.getCisRcFbdExposureInfoById(id);
        cisRcFbdExposureInfoOptional.ifPresent(cisRcFbdExposureInfo -> cisRcFbdExposureInfo.update(cisRcFbdExposureInfoEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisRcFbdExposureInfo(String id) {
        Optional<CisRcFbdExposureInfo> cisRcFbdExposureInfoOptional = CisRcFbdExposureInfo.getCisRcFbdExposureInfoById(id);
        cisRcFbdExposureInfoOptional.ifPresent(cisRcFbdExposureInfo -> cisRcFbdExposureInfo.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}