package com.bjgoodwill.hip.ds.cis.rc.rcCard.repository;

import com.bjgoodwill.hip.ds.cis.rc.rcCard.entity.CisRcFbdPatCase;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.rc.rcCard.repository.CisRcFbdPatCaseRepository")
public interface CisRcFbdPatCaseRepository extends JpaRepository<CisRcFbdPatCase, String>, JpaSpecificationExecutor<CisRcFbdPatCase> {

}