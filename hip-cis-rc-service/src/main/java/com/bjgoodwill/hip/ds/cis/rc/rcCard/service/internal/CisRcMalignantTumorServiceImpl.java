package com.bjgoodwill.hip.ds.cis.rc.rcCard.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.entity.CisRcMalignantTumor;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.service.CisRcMalignantTumorService;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.service.internal.assembler.CisRcMalignantTumorAssembler;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcMalignantTumorEto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcMalignantTumorNto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcMalignantTumorQto;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.CisRcMalignantTumorTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.rc.rcCard.service.CisRcMalignantTumorService")
@RequestMapping(value = "/api/cisrc/rcCard/cisRcMalignantTumor", produces = "application/json; charset=utf-8")
public class CisRcMalignantTumorServiceImpl implements CisRcMalignantTumorService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisRcMalignantTumorTo> getCisRcMalignantTumors(CisRcMalignantTumorQto cisRcMalignantTumorQto) {
        return CisRcMalignantTumorAssembler.toTos(CisRcMalignantTumor.getCisRcMalignantTumors(cisRcMalignantTumorQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisRcMalignantTumorTo> getCisRcMalignantTumorPage(CisRcMalignantTumorQto cisRcMalignantTumorQto) {
        Page<CisRcMalignantTumor> page = CisRcMalignantTumor.getCisRcMalignantTumorPage(cisRcMalignantTumorQto);
        Page<CisRcMalignantTumorTo> result = page.map(CisRcMalignantTumorAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisRcMalignantTumorTo getCisRcMalignantTumorById(String id) {
        return CisRcMalignantTumorAssembler.toTo(CisRcMalignantTumor.getCisRcMalignantTumorById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisRcMalignantTumorTo createCisRcMalignantTumor(CisRcMalignantTumorNto cisRcMalignantTumorNto) {
        CisRcMalignantTumor cisRcMalignantTumor = new CisRcMalignantTumor();
        cisRcMalignantTumor = cisRcMalignantTumor.create(cisRcMalignantTumorNto);
        return CisRcMalignantTumorAssembler.toTo(cisRcMalignantTumor);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisRcMalignantTumor(String id, CisRcMalignantTumorEto cisRcMalignantTumorEto) {
        Optional<CisRcMalignantTumor> cisRcMalignantTumorOptional = CisRcMalignantTumor.getCisRcMalignantTumorById(id);
        cisRcMalignantTumorOptional.ifPresent(cisRcMalignantTumor -> cisRcMalignantTumor.update(cisRcMalignantTumorEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisRcMalignantTumor(String id) {
        Optional<CisRcMalignantTumor> cisRcMalignantTumorOptional = CisRcMalignantTumor.getCisRcMalignantTumorById(id);
        cisRcMalignantTumorOptional.ifPresent(cisRcMalignantTumor -> cisRcMalignantTumor.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}