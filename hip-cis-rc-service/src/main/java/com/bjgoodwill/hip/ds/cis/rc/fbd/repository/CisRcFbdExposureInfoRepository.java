package com.bjgoodwill.hip.ds.cis.rc.fbd.repository;

import com.bjgoodwill.hip.ds.cis.rc.fbd.entity.CisRcFbdExposureInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.rc.fbd.repository.CisRcFbdExposureInfoRepository")
public interface CisRcFbdExposureInfoRepository extends JpaRepository<CisRcFbdExposureInfo, String>, JpaSpecificationExecutor<CisRcFbdExposureInfo> {

}