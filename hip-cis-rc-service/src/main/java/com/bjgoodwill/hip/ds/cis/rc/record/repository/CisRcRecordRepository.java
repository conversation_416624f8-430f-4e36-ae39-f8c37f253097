package com.bjgoodwill.hip.ds.cis.rc.record.repository;

import com.bjgoodwill.hip.ds.cis.rc.record.entity.CisRcRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.rc.record.repository.CisRcRecordRepository")
public interface CisRcRecordRepository extends JpaRepository<CisRcRecord, String>, JpaSpecificationExecutor<CisRcRecord> {

    List<CisRcRecord> findByRcCardId(String rcCardId);

    Page<CisRcRecord> findByRcCardId(String rcCardId, Pageable pageable);

    boolean existsByRcCardId(String rcCardId);

    void deleteByRcCardId(String rcCardId);

}