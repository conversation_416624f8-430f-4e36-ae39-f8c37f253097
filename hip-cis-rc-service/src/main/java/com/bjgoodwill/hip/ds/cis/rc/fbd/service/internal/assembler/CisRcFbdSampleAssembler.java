package com.bjgoodwill.hip.ds.cis.rc.fbd.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.rc.fbd.entity.CisRcFbdSample;
import com.bjgoodwill.hip.ds.cis.rc.fbd.to.CisRcFbdSampleTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisRcFbdSampleAssembler {

    public static List<CisRcFbdSampleTo> toTos(List<CisRcFbdSample> cisRcFbdSamples) {
        return toTos(cisRcFbdSamples, false);
    }

    public static List<CisRcFbdSampleTo> toTos(List<CisRcFbdSample> cisRcFbdSamples, boolean withAllParts) {
        Assert.notNull(cisRcFbdSamples, "参数cisRcFbdSamples不能为空！");

        List<CisRcFbdSampleTo> tos = new ArrayList<>();
        for (CisRcFbdSample cisRcFbdSample : cisRcFbdSamples)
            tos.add(toTo(cisRcFbdSample, withAllParts));
        return tos;
    }

    public static CisRcFbdSampleTo toTo(CisRcFbdSample cisRcFbdSample) {
        return toTo(cisRcFbdSample, false);
    }

    /**
     * @generated
     */
    public static CisRcFbdSampleTo toTo(CisRcFbdSample cisRcFbdSample, boolean withAllParts) {
        if (cisRcFbdSample == null)
            return null;
        CisRcFbdSampleTo to = new CisRcFbdSampleTo();
        to.setId(cisRcFbdSample.getId());
        to.setFbdCode(cisRcFbdSample.getFbdCode());
        to.setApplyCode(cisRcFbdSample.getApplyCode());
        to.setSampleType(cisRcFbdSample.getSampleType());
        to.setSampleNo(cisRcFbdSample.getSampleNo());
        to.setSampleSize(cisRcFbdSample.getSampleSize());
        to.setSampleUnit(cisRcFbdSample.getSampleUnit());
        to.setSampleDate(cisRcFbdSample.getSampleDate());
        to.setRemark(cisRcFbdSample.getRemark());
        to.setCreatedStaff(cisRcFbdSample.getCreatedStaff());
        to.setCreatedStaffName(cisRcFbdSample.getCreatedStaffName());
        to.setCreatedDate(cisRcFbdSample.getCreatedDate());
        to.setUpdatedStaff(cisRcFbdSample.getUpdatedStaff());
        to.setUpdatedStaffName(cisRcFbdSample.getUpdatedStaffName());
        to.setUpdatedDate(cisRcFbdSample.getUpdatedDate());
        to.setDeleted(cisRcFbdSample.isDeleted());

        if (withAllParts) {
        }
        return to;
    }

}