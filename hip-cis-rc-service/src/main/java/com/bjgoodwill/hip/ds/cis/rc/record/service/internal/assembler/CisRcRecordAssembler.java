package com.bjgoodwill.hip.ds.cis.rc.record.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.rc.record.entity.CisRcRecord;
import com.bjgoodwill.hip.ds.cis.rc.record.to.CisRcRecordTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisRcRecordAssembler {

    public static List<CisRcRecordTo> toTos(List<CisRcRecord> cisRcRecords) {
        return toTos(cisRcRecords, false);
    }

    public static List<CisRcRecordTo> toTos(List<CisRcRecord> cisRcRecords, boolean withAllParts) {
        Assert.notNull(cisRcRecords, "参数cisRcRecords不能为空！");

        List<CisRcRecordTo> tos = new ArrayList<>();
        for (CisRcRecord cisRcRecord : cisRcRecords)
            tos.add(toTo(cisRcRecord, withAllParts));
        return tos;
    }

    public static CisRcRecordTo toTo(CisRcRecord cisRcRecord) {
        return toTo(cisRcRecord, false);
    }

    /**
     * @generated
     */
    public static CisRcRecordTo toTo(CisRcRecord cisRcRecord, boolean withAllParts) {
        if (cisRcRecord == null)
            return null;
        CisRcRecordTo to = new CisRcRecordTo();
        to.setId(cisRcRecord.getId());
        to.setRcCardId(cisRcRecord.getRcCardId());
        to.setRecordType(cisRcRecord.getRecordType());
        to.setContent(cisRcRecord.getContent());
        to.setLastDiagnosisCode(cisRcRecord.getLastDiagnosisCode());
        to.setLastDiagnosisName(cisRcRecord.getLastDiagnosisName());
        to.setCreatedStaff(cisRcRecord.getCreatedStaff());
        to.setCreatedStaffName(cisRcRecord.getCreatedStaffName());
        to.setCreatedDate(cisRcRecord.getCreatedDate());
        to.setUpdatedDate(cisRcRecord.getUpdatedDate());
        to.setDeleted(cisRcRecord.isDeleted());

        if (withAllParts) {
        }
        return to;
    }

}