package com.bjgoodwill.hip.ds.cis.rc.hiv.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.rc.hiv.repository.CisRcIdmHivRepository;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivEto;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivNto;
import com.bjgoodwill.hip.ds.cis.rc.hiv.to.CisRcIdmHivQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "传染病上报艾滋病性病附卡")
@Table(name = "cis_rc_idm_hiv", indexes = {@Index(name = "idx_cis_rc_idm_hiv_cis_rc_idm_id", columnList = "cis_rc_idm_id")}, uniqueConstraints = {})
public class CisRcIdmHiv {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("传染病报卡标识")
    @Column(name = "cis_rc_idm_id", nullable = true, length = 50)
    private String cisRcIdmId;


    @Comment("接触史（编码|编码）")
    @Column(name = "contact", nullable = true)
    private String contact;


    @Comment("接触史其他项 存汉字说明")
    @Column(name = "contact_other", nullable = true)
    private String contactOther;


    @Comment("性病使【有；无；不详】")
    @Column(name = "stdt_disease", nullable = true)
    private String stdtDisease;


    @Comment("感染途径 字典IdmInfection")
    @Column(name = "infection", nullable = true)
    private String infection;


    @Comment("感染途径其他项 存汉字说明")
    @Column(name = "infection_other", nullable = true)
    private String infectionOther;


    @Comment("样本来源 字典IdmSampleSource")
    @Column(name = "sample_source", nullable = true)
    private String sampleSource;


    @Comment("样本来源其他项，存汉字说明")
    @Column(name = "sample_other", nullable = true)
    private String sampleOther;


    @Comment("实验室检测结论【确证检测阳性；替代策略检测阳性】")
    @Column(name = "detection_conclusion", nullable = true)
    private String detectionConclusion;


    @Comment("检测阳性日期")
    @Column(name = "detection_date", nullable = true)
    private LocalDateTime detectionDate;


    @Comment("检测单位")
    @Column(name = "detection_unit", nullable = true)
    private String detectionUnit;


    @Comment("检测单位名称")
    @Column(name = "detection_unit_name", nullable = true)
    private String detectionUnitName;


    @Comment("艾滋病确认日期")
    @Column(name = "hiv_confirm_date", nullable = true)
    private LocalDateTime hivConfirmDate;

    public static Optional<CisRcIdmHiv> getCisRcIdmHivById(String id) {
        return dao().findById(id);
    }

    public static List<CisRcIdmHiv> getCisRcIdmHivs(CisRcIdmHivQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisRcIdmHiv> getCisRcIdmHivPage(CisRcIdmHivQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisRcIdmHiv> getByCisRcIdmId(String cisRcIdmId) {
        return dao().findByCisRcIdmId(cisRcIdmId);
    }

    public static void deleteByCisRcIdmId(String cisRcIdmId) {
        dao().deleteByCisRcIdmId(cisRcIdmId);
    }

    /**
     * @generated
     */
    private static Specification<CisRcIdmHiv> getSpecification(CisRcIdmHivQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getCisRcIdmId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisRcIdmId"), qto.getCisRcIdmId()));
            }
            if (qto.getDetectionDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("detectionDate"), LocalDateUtil.beginOfDay(qto.getDetectionDate()), LocalDateUtil.endOfDay(qto.getDetectionDate())));
            }
            if (StringUtils.isNotBlank(qto.getDetectionUnit())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("detectionUnit"), qto.getDetectionUnit()));
            }
            return predicate;
        };
    }

    private static CisRcIdmHivRepository dao() {
        return SpringUtil.getBean(CisRcIdmHivRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getCisRcIdmId() {
        return cisRcIdmId;
    }

    protected void setCisRcIdmId(String cisRcIdmId) {
        this.cisRcIdmId = cisRcIdmId;
    }

    public String getContact() {
        return contact;
    }

    protected void setContact(String contact) {
        this.contact = contact;
    }

    public String getContactOther() {
        return contactOther;
    }

    protected void setContactOther(String contactOther) {
        this.contactOther = contactOther;
    }

    public String getStdtDisease() {
        return stdtDisease;
    }

    protected void setStdtDisease(String stdtDisease) {
        this.stdtDisease = stdtDisease;
    }

    public String getInfection() {
        return infection;
    }

    protected void setInfection(String infection) {
        this.infection = infection;
    }

    public String getInfectionOther() {
        return infectionOther;
    }

    protected void setInfectionOther(String infectionOther) {
        this.infectionOther = infectionOther;
    }

    public String getSampleSource() {
        return sampleSource;
    }

    protected void setSampleSource(String sampleSource) {
        this.sampleSource = sampleSource;
    }

    public String getSampleOther() {
        return sampleOther;
    }

    protected void setSampleOther(String sampleOther) {
        this.sampleOther = sampleOther;
    }

    public String getDetectionConclusion() {
        return detectionConclusion;
    }

    protected void setDetectionConclusion(String detectionConclusion) {
        this.detectionConclusion = detectionConclusion;
    }

    public LocalDateTime getDetectionDate() {
        return detectionDate;
    }

    protected void setDetectionDate(LocalDateTime detectionDate) {
        this.detectionDate = detectionDate;
    }

    public String getDetectionUnit() {
        return detectionUnit;
    }

    protected void setDetectionUnit(String detectionUnit) {
        this.detectionUnit = detectionUnit;
    }

    public String getDetectionUnitName() {
        return detectionUnitName;
    }

    protected void setDetectionUnitName(String detectionUnitName) {
        this.detectionUnitName = detectionUnitName;
    }

    public LocalDateTime getHivConfirmDate() {
        return hivConfirmDate;
    }

    protected void setHivConfirmDate(LocalDateTime hivConfirmDate) {
        this.hivConfirmDate = hivConfirmDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisRcIdmHiv other = (CisRcIdmHiv) obj;
        return Objects.equals(id, other.id);
    }

    public CisRcIdmHiv create(CisRcIdmHivNto cisRcIdmHivNto) {
        Assert.notNull(cisRcIdmHivNto, "参数cisRcIdmHivNto不能为空！");

        setId(cisRcIdmHivNto.getId());
        setCisRcIdmId(cisRcIdmHivNto.getCisRcIdmId());
        setContact(cisRcIdmHivNto.getContact());
        setContactOther(cisRcIdmHivNto.getContactOther());
        setStdtDisease(cisRcIdmHivNto.getStdtDisease());
        setInfection(cisRcIdmHivNto.getInfection());
        setInfectionOther(cisRcIdmHivNto.getInfectionOther());
        setSampleSource(cisRcIdmHivNto.getSampleSource());
        setSampleOther(cisRcIdmHivNto.getSampleOther());
        setDetectionConclusion(cisRcIdmHivNto.getDetectionConclusion());
        setDetectionDate(cisRcIdmHivNto.getDetectionDate());
        setDetectionUnit(cisRcIdmHivNto.getDetectionUnit());
        setDetectionUnitName(cisRcIdmHivNto.getDetectionUnitName());
        setHivConfirmDate(cisRcIdmHivNto.getHivConfirmDate());
        dao().save(this);
        return this;
    }

    public void update(CisRcIdmHivEto cisRcIdmHivEto) {
        setContact(cisRcIdmHivEto.getContact());
        setContactOther(cisRcIdmHivEto.getContactOther());
        setStdtDisease(cisRcIdmHivEto.getStdtDisease());
        setInfection(cisRcIdmHivEto.getInfection());
        setInfectionOther(cisRcIdmHivEto.getInfectionOther());
        setSampleSource(cisRcIdmHivEto.getSampleSource());
        setSampleOther(cisRcIdmHivEto.getSampleOther());
        setDetectionConclusion(cisRcIdmHivEto.getDetectionConclusion());
        setDetectionDate(cisRcIdmHivEto.getDetectionDate());
        setDetectionUnit(cisRcIdmHivEto.getDetectionUnit());
        setDetectionUnitName(cisRcIdmHivEto.getDetectionUnitName());
        setHivConfirmDate(cisRcIdmHivEto.getHivConfirmDate());
    }

    public void delete() {
        dao().delete(this);
    }

}
