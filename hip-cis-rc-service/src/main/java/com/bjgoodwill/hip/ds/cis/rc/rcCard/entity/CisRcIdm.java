package com.bjgoodwill.hip.ds.cis.rc.rcCard.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.rc.hiv.entity.CisRcIdmHiv;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.repository.CisRcIdmRepository;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "传染病报卡")
@Table(name = "cis_rc_idm", indexes = {@Index(name = "idx_cis_rc_idm_report_no_idx", columnList = "report_no"),
        @Index(name = "idx_cis_rc_idm_visit_code", columnList = "visit_code")}, uniqueConstraints = {})
public class CisRcIdm extends RcCard {

    @Comment("是否首诊 0否 1 是")
    @Column(name = "frist_diagnose_flag", nullable = true)
    private Boolean fristDiagnoseFlag;


    @Comment("病例分类1")
    @Column(name = "case_class_1", nullable = true)
    private String caseClass1;


    @Comment("病例分类2")
    @Column(name = "case_class_2", nullable = true)
    private String caseClass2;


    @Comment("有无附卡 0无 1有")
    @Column(name = "addition_flag", nullable = true)
    private Boolean additionFlag;

    public static Optional<CisRcIdm> getCisRcIdmById(String id) {
        return dao().findById(id);
    }

    public static List<CisRcIdm> getCisRcIdms(CisRcIdmQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisRcIdm> getCisRcIdmPage(CisRcIdmQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisRcIdm> getSpecification(CisRcIdmQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getReportNo())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportNo"), qto.getReportNo()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("name"), qto.getName()));
            }
            if (StringUtils.isNotBlank(qto.getParentsName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("parentsName"), qto.getParentsName()));
            }
            if (StringUtils.isNotBlank(qto.getSex())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sex"), qto.getSex()));
            }
            if (qto.getBirthDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("birthDate"), LocalDateUtil.beginOfDay(qto.getBirthDate()), LocalDateUtil.endOfDay(qto.getBirthDate())));
            }
            if (StringUtils.isNotBlank(qto.getEducation())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("education"), qto.getEducation()));
            }
            if (StringUtils.isNotBlank(qto.getReportUser())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUser"), qto.getReportUser()));
            }
            if (StringUtils.isNotBlank(qto.getReportUserName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reportUserName"), qto.getReportUserName()));
            }
            if (StringUtils.isNotBlank(qto.getUploadReportNo())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("uploadReportNo"), qto.getUploadReportNo()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            return predicate;
        };
    }

    private static CisRcIdmRepository dao() {
        return SpringUtil.getBean(CisRcIdmRepository.class);
    }

    public Boolean getFristDiagnoseFlag() {
        return fristDiagnoseFlag;
    }

    protected void setFristDiagnoseFlag(Boolean fristDiagnoseFlag) {
        this.fristDiagnoseFlag = fristDiagnoseFlag;
    }

    public String getCaseClass1() {
        return caseClass1;
    }

    protected void setCaseClass1(String caseClass1) {
        this.caseClass1 = caseClass1;
    }

    public String getCaseClass2() {
        return caseClass2;
    }

    protected void setCaseClass2(String caseClass2) {
        this.caseClass2 = caseClass2;
    }

    public Boolean getAdditionFlag() {
        return additionFlag;
    }

    protected void setAdditionFlag(Boolean additionFlag) {
        this.additionFlag = additionFlag;
    }

    @Override
    public RcCard create(RcCardNto rcCardNto) {
        return create((CisRcIdmNto) rcCardNto);
    }

    @Override
    public void update(RcCardEto rcCardEto) {
        update((CisRcIdmEto) rcCardEto);
    }

    public CisRcIdm create(CisRcIdmNto cisRcIdmNto) {
        Assert.notNull(cisRcIdmNto, "参数cisRcIdmNto不能为空！");
        super.create(cisRcIdmNto);

        setFristDiagnoseFlag(cisRcIdmNto.getFristDiagnoseFlag());
        setCaseClass1(cisRcIdmNto.getCaseClass1());
        setCaseClass2(cisRcIdmNto.getCaseClass2());
        setAdditionFlag(cisRcIdmNto.getAdditionFlag());
        dao().save(this);
        return this;
    }

    public void update(CisRcIdmEto cisRcIdmEto) {
        super.update(cisRcIdmEto);
        setFristDiagnoseFlag(cisRcIdmEto.getFristDiagnoseFlag());
        setCaseClass1(cisRcIdmEto.getCaseClass1());
        setCaseClass2(cisRcIdmEto.getCaseClass2());
        setAdditionFlag(cisRcIdmEto.getAdditionFlag());
    }

    public void delete() {
        super.delete();
        for (CisRcIdmHiv cisRcIdmHiv : CisRcIdmHiv.getByCisRcIdmId(getId())) {
            cisRcIdmHiv.delete();
        }
        // CisRcIdmHiv.deleteByCisRcIdmId(getId());
        dao().delete(this);
    }

}
