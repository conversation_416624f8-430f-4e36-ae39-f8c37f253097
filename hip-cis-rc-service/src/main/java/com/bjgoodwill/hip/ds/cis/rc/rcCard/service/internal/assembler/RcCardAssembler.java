package com.bjgoodwill.hip.ds.cis.rc.rcCard.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.rc.rcCard.entity.CisRcFbdPatCase;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.entity.CisRcIdm;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.entity.CisRcMalignantTumor;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.entity.RcCard;
import com.bjgoodwill.hip.ds.cis.rc.rcCard.to.RcCardTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class RcCardAssembler {

    public static List<RcCardTo> toTos(List<RcCard> rcCards) {
        return toTos(rcCards, false);
    }

    public static List<RcCardTo> toTos(List<RcCard> rcCards, boolean withAllParts) {
        Assert.notNull(rcCards, "参数rcCards不能为空！");

        List<RcCardTo> tos = new ArrayList<>();
        for (RcCard rcCard : rcCards)
            tos.add(toTo(rcCard, withAllParts));
        return tos;
    }

    public static RcCardTo toTo(RcCard rcCard) {
        return toTo(rcCard, false);
    }

    /**
     * @generated
     */
    public static RcCardTo toTo(RcCard rcCard, boolean withAllParts) {
        if (rcCard == null)
            return null;
        if (rcCard instanceof CisRcIdm) {
            return CisRcIdmAssembler.toTo((CisRcIdm) rcCard, withAllParts);
        }
        if (rcCard instanceof CisRcMalignantTumor) {
            return CisRcMalignantTumorAssembler.toTo((CisRcMalignantTumor) rcCard, withAllParts);
        }
        if (rcCard instanceof CisRcFbdPatCase) {
            return CisRcFbdPatCaseAssembler.toTo((CisRcFbdPatCase) rcCard, withAllParts);
        }
        return null;
    }

}