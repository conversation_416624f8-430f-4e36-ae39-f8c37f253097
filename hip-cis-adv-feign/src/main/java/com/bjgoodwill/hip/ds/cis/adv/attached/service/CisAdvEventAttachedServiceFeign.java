package com.bjgoodwill.hip.ds.cis.adv.attached.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path="/api/cisadv/attached/cisAdvEventAttached", contextId = "com.bjgoodwill.hip.ds.cis.adv.attached.service.CisAdvEventAttachedServiceFeign")
public interface CisAdvEventAttachedServiceFeign extends CisAdvEventAttachedService {

}