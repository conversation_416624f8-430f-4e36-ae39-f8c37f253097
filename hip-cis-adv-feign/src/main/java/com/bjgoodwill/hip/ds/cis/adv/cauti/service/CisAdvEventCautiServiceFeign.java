package com.bjgoodwill.hip.ds.cis.adv.cauti.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path="/api/cisadv/cauti/cisAdvEventCauti", contextId = "com.bjgoodwill.hip.ds.cis.adv.cauti.service.CisAdvEventCautiServiceFeign")
public interface CisAdvEventCautiServiceFeign extends CisAdvEventCautiService {

}