package com.bjgoodwill.hip.ds.cis.adv.report.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path="/api/cisadv/report/cisAdvEventReport", contextId = "com.bjgoodwill.hip.ds.cis.adv.report.service.CisAdvEventReportServiceFeign")
public interface CisAdvEventReportServiceFeign extends CisAdvEventReportService {

}