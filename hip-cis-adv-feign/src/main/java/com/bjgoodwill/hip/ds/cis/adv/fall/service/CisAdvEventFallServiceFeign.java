package com.bjgoodwill.hip.ds.cis.adv.fall.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path = "/api/cisadv/fall/cisAdvEventFall", contextId = "com.bjgoodwill.hip.ds.cis.adv.fall.service.CisAdvEventFallServiceFeign")
public interface CisAdvEventFallServiceFeign extends CisAdvEventFallService {

}