package com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path = "/api/cisadv/bloodinfection/cisAdvEventPiccBloodInfection", contextId = "com.bjgoodwill.hip.ds.cis.adv.bloodinfection.service.CisAdvEventPiccBloodInfectionServiceFeign")
public interface CisAdvEventPiccBloodInfectionServiceFeign extends CisAdvEventPiccBloodInfectionService {

}