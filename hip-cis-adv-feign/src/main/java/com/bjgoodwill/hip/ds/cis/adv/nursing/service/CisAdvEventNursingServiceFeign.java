package com.bjgoodwill.hip.ds.cis.adv.nursing.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path="/api/cisadv/nursing/cisAdvEventNursing", contextId = "com.bjgoodwill.hip.ds.cis.adv.nursing.service.CisAdvEventNursingServiceFeign")
public interface CisAdvEventNursingServiceFeign extends CisAdvEventNursingService {

}