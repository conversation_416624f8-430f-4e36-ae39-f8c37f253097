package com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path = "/api/cisadv/sharpInjuries/cisAdvEventSharpInjuries", contextId = "com.bjgoodwill.hip.ds.cis.adv.sharpInjuries.service.CisAdvEventSharpInjuriesServiceFeign")
public interface CisAdvEventSharpInjuriesServiceFeign extends CisAdvEventSharpInjuriesService {

}