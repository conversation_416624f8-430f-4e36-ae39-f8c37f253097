package com.bjgoodwill.hip.ds.cis.adv.pressure.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path="/api/cisadv/pressure/cisAdvEventPressureInjury", contextId = "com.bjgoodwill.hip.ds.cis.adv.pressure.service.CisAdvEventPressureInjuryServiceFeign")
public interface CisAdvEventPressureInjuryServiceFeign extends CisAdvEventPressureInjuryService {

}