package com.bjgoodwill.hip.ds.cis.adv.drugExplain.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path = "/api/cisadv/drugExplain/cisAdvEventDrugExplain", contextId = "com.bjgoodwill.hip.ds.cis.adv.drugExplain.service.CisAdvEventDrugExplainServiceFeign")
public interface CisAdvEventDrugExplainServiceFeign extends CisAdvEventDrugExplainService {

}