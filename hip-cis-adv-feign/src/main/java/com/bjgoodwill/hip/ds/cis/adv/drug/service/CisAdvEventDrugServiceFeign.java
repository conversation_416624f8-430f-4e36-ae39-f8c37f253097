package com.bjgoodwill.hip.ds.cis.adv.drug.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path = "/api/cisadv/drug/cisAdvEventDrug", contextId = "com.bjgoodwill.hip.ds.cis.adv.drug.service.CisAdvEventDrugServiceFeign")
public interface CisAdvEventDrugServiceFeign extends CisAdvEventDrugService {

}