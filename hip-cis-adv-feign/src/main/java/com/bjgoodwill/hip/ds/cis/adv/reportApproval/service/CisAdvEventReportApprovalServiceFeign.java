package com.bjgoodwill.hip.ds.cis.adv.reportApproval.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path = "/api/cisadv/reportApproval/cisAdvEventReportApproval", contextId = "com.bjgoodwill.hip.ds.cis.adv.reportApproval.service.CisAdvEventReportApprovalServiceFeign")
public interface CisAdvEventReportApprovalServiceFeign extends CisAdvEventReportApprovalService {

}