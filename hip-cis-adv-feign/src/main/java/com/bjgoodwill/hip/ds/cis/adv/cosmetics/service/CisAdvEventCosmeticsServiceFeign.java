package com.bjgoodwill.hip.ds.cis.adv.cosmetics.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.cisadv.name}", url = "${hip.domains.cisadv.url}", path="/api/cisadv/cosmetics/cisAdvEventCosmetics", contextId = "com.bjgoodwill.hip.ds.cis.adv.cosmetics.service.CisAdvEventCosmeticsServiceFeign")
public interface CisAdvEventCosmeticsServiceFeign extends CisAdvEventCosmeticsService {

}