package com.bjgoodwill.hip.ds.cis.cdr.plan.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "医嘱执行记录")
public class CisCdrOrderPlanRecordNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -5280260158357519052L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "患者主索引")
    private String patCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "医嘱id")
    private String orderId;
    @Schema(description = "申请单id")
    private String applyId;
    @Schema(description = "医嘱类型")
    private SystemTypeEnum orderClass;
    @Schema(description = "医嘱项目名称医嘱项目名称")
    private String serviceItemName;
    @Schema(description = "执行科室")
    private String executeOrg;
    @Schema(description = "执行人")
    private String executeStaff;
    @Schema(description = "执行时间")
    private LocalDateTime executeDate;
    @Schema(description = "药品编码")
    private String drugCode;
    @Schema(description = "执行科室名称")
    private String executeOrgName;
    @Schema(description = "执行人名称")
    private String executeStaffName;
    @Schema(description = "药品规格")
    private String spec;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "频次名称")
    private String frequencyName;
    @Schema(description = "单次用量")
    private Double dosage;
    @Schema(description = "单次用量单位名称")
    private String dosageUnitName;
    @Schema(description = "抽取的时间")
    private LocalDateTime createdDate;
    @Schema(description = "医嘱执行单明细列表")
    private List<CisCdrOrderPlanDetailRecordNto> cisCdrOrderPlanDetailRecords = new ArrayList<>();

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "患者主索引不能为空！")
    @Size(max = 32, message = "患者主索引长度不能超过32个字符！")
    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = StringUtils.trimToNull(patCode);
    }

    @NotBlank(message = "就诊流水号不能为空！")
    @Size(max = 32, message = "就诊流水号长度不能超过32个字符！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @Size(max = 50, message = "医嘱id长度不能超过50个字符！")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = StringUtils.trimToNull(orderId);
    }

    @NotBlank(message = "申请单id不能为空！")
    @Size(max = 32, message = "申请单id长度不能超过32个字符！")
    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = StringUtils.trimToNull(applyId);
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    @Size(max = 64, message = "医嘱项目名称医嘱项目名称长度不能超过64个字符！")
    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = StringUtils.trimToNull(serviceItemName);
    }

    @NotBlank(message = "执行科室不能为空！")
    public String getExecuteOrg() {
        return executeOrg;
    }

    public void setExecuteOrg(String executeOrg) {
        this.executeOrg = StringUtils.trimToNull(executeOrg);
    }

    @Size(max = 32, message = "执行人长度不能超过32个字符！")
    public String getExecuteStaff() {
        return executeStaff;
    }

    public void setExecuteStaff(String executeStaff) {
        this.executeStaff = StringUtils.trimToNull(executeStaff);
    }

    public LocalDateTime getExecuteDate() {
        return executeDate;
    }

    public void setExecuteDate(LocalDateTime executeDate) {
        this.executeDate = executeDate;
    }

    @Size(max = 100, message = "药品编码长度不能超过100个字符！")
    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = StringUtils.trimToNull(drugCode);
    }

    @NotNull(message = "抽取的时间不能为空！")
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public List<CisCdrOrderPlanDetailRecordNto> getCisCdrOrderPlanDetailRecords() {
        return cisCdrOrderPlanDetailRecords;
    }

    public void setCisCdrOrderPlanDetailRecords(List<CisCdrOrderPlanDetailRecordNto> cisCdrOrderPlanDetailRecords) {
        this.cisCdrOrderPlanDetailRecords = cisCdrOrderPlanDetailRecords;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    public String getExecuteStaffName() {
        return executeStaffName;
    }

    public void setExecuteStaffName(String executeStaffName) {
        this.executeStaffName = executeStaffName;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }
}