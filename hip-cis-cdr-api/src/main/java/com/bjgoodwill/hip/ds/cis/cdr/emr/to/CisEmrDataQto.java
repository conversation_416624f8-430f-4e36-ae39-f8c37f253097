package com.bjgoodwill.hip.ds.cis.cdr.emr.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "病历信息")
public class CisEmrDataQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4759709998169628510L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "上次月经时间")
    private LocalDateTime lastMenstrualPeriod;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public LocalDateTime getLastMenstrualPeriod() {
        return lastMenstrualPeriod;
    }

    public void setLastMenstrualPeriod(LocalDateTime lastMenstrualPeriod) {
        this.lastMenstrualPeriod = lastMenstrualPeriod;
    }
}