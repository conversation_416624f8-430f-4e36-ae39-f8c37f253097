package com.bjgoodwill.hip.ds.cis.cdr.diagnose.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseEto;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseNto;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseQto;
import com.bjgoodwill.hip.ds.cis.cdr.diagnose.to.CisCdrClinicDiagnoseTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "诊断记录领域服务", description = "诊断记录领域服务")
public interface CisCdrClinicDiagnoseService {

    @Operation(summary = "根据查询条件对诊断记录进行查询。")
    @GetMapping("/cisCdrClinicDiagnoses")
    List<CisCdrClinicDiagnoseTo> getCisCdrClinicDiagnoses(@ParameterObject @SpringQueryMap CisCdrClinicDiagnoseQto cisCdrClinicDiagnoseQto);

    @Operation(summary = "根据查询条件对诊断记录进行分页查询。")
    @GetMapping("/cisCdrClinicDiagnoses/pages")
    GridResultSet<CisCdrClinicDiagnoseTo> getCisCdrClinicDiagnosePage(@ParameterObject @SpringQueryMap CisCdrClinicDiagnoseQto cisCdrClinicDiagnoseQto);

    @Operation(summary = "创建诊断记录。")
    @PostMapping("/cisCdrClinicDiagnoses")
    CisCdrClinicDiagnoseTo createCisCdrClinicDiagnose(@RequestBody @Valid CisCdrClinicDiagnoseNto cisCdrClinicDiagnoseNto);

    @Operation(summary = "根据唯一标识修改诊断记录。")
    @PutMapping("/cisCdrClinicDiagnoses/{id:.+}")
    void updateCisCdrClinicDiagnose(@PathVariable("id") String id, @RequestBody @Valid CisCdrClinicDiagnoseEto cisCdrClinicDiagnoseEto);

    @Operation(summary = "根据唯一标识删除诊断记录。")
    @DeleteMapping("/cisCdrClinicDiagnoses/{id:.+}")
    void deleteCisCdrClinicDiagnose(@PathVariable("id") String id);

}