package com.bjgoodwill.hip.ds.cis.cdr.setting.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.setting.to.CisPatSettingEto;
import com.bjgoodwill.hip.ds.cis.cdr.setting.to.CisPatSettingNto;
import com.bjgoodwill.hip.ds.cis.cdr.setting.to.CisPatSettingQto;
import com.bjgoodwill.hip.ds.cis.cdr.setting.to.CisPatSettingTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "患者视图过滤设置领域服务", description = "患者视图过滤设置领域服务")
public interface CisPatSettingService {

    @Operation(summary = "根据查询条件对患者视图过滤设置进行查询。")
    @GetMapping("/cisPatSettings")
    List<CisPatSettingTo> getCisPatSettings(@ParameterObject @SpringQueryMap CisPatSettingQto cisPatSettingQto);

    @Operation(summary = "根据查询条件对患者视图过滤设置进行分页查询。")
    @GetMapping("/cisPatSettings/pages")
    GridResultSet<CisPatSettingTo> getCisPatSettingPage(@ParameterObject @SpringQueryMap CisPatSettingQto cisPatSettingQto);

    @Operation(summary = "根据唯一标识返回患者视图过滤设置。")
    @GetMapping("/cisPatSettings/{id:.+}")
    CisPatSettingTo getCisPatSettingById(@PathVariable("id") String id);

    @Operation(summary = "创建患者视图过滤设置。")
    @PostMapping("/cisPatSettings")
    CisPatSettingTo createCisPatSetting(@RequestBody @Valid CisPatSettingNto cisPatSettingNto);

    @Operation(summary = "根据唯一标识修改患者视图过滤设置。")
    @PutMapping("/cisPatSettings/{id:.+}")
    void updateCisPatSetting(@PathVariable("id") String id, @RequestBody @Valid CisPatSettingEto cisPatSettingEto);

    @Operation(summary = "根据唯一标识删除患者视图过滤设置。")
    @DeleteMapping("/cisPatSettings/{id:.+}")
    void deleteCisPatSetting(@PathVariable("id") String id);

}