package com.bjgoodwill.hip.ds.cis.cdr.critical.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportEto;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportNto;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportQto;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "危急值报告领域服务", description = "危急值报告领域服务")
public interface CriticalValueReportService {

    @Operation(summary = "P0根据查询条件对危急值报告进行查询。")
    @GetMapping("/criticalValueReports")
    List<CriticalValueReportTo> getCriticalValueReports(@ParameterObject @SpringQueryMap CriticalValueReportQto criticalValueReportQto);

    @Operation(summary = "根据查询条件对危急值报告进行分页查询。")
    @GetMapping("/criticalValueReports/pages")
    GridResultSet<CriticalValueReportTo> getCriticalValueReportPage(@ParameterObject @SpringQueryMap CriticalValueReportQto criticalValueReportQto);

    @Operation(summary = "根据唯一标识返回危急值报告。")
    @GetMapping("/criticalValueReports/{id:.+}")
    CriticalValueReportTo getCriticalValueReportById(@PathVariable("id") String id);

    @Operation(summary = "创建危急值报告。")
    @PostMapping("/criticalValueReports")
    CriticalValueReportTo createCriticalValueReport(@RequestBody @Valid CriticalValueReportNto criticalValueReportNto);

    @Operation(summary = "P0批量创建危急值报告。")
    @PostMapping("/criticalValueReports/batch")
    List<CriticalValueReportTo> createCriticalValueReportBatch(@RequestBody @Valid List<CriticalValueReportNto> criticalValueReportNtos);

    @Operation(summary = "根据唯一标识修改危急值报告。")
    @PutMapping("/criticalValueReports/{id:.+}")
    void updateCriticalValueReport(@PathVariable("id") String id, @RequestBody @Valid CriticalValueReportEto criticalValueReportEto);

    @Operation(summary = "根据唯一标识删除危急值报告。")
    @DeleteMapping("/criticalValueReports/{id:.+}")
    void deleteCriticalValueReport(@PathVariable("id") String id);

    @Operation(summary = "根据唯一标识答复。")
    @PutMapping("/criticalValueReports/reply/{id:.+}")
    void replyCriticalValueReport(@PathVariable("id") String id, @RequestBody @Valid CriticalValueReportEto criticalValueReportEto);

}