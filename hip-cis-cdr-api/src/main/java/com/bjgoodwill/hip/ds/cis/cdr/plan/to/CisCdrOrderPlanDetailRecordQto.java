package com.bjgoodwill.hip.ds.cis.cdr.plan.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医嘱执行单明细")
public class CisCdrOrderPlanDetailRecordQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1586897647133385062L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "执行单id")
    private String orderPlanId;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getOrderPlanId() {
        return orderPlanId;
    }

    public void setOrderPlanId(String orderPlanId) {
        this.orderPlanId = orderPlanId;
    }
}