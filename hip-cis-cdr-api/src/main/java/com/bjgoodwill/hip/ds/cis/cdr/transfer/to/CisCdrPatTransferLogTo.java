package com.bjgoodwill.hip.ds.cis.cdr.transfer.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "患者流转日志记录")
public class CisCdrPatTransferLogTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -90066202378569660L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "住院流水号")
    private String visitCode;
    @Schema(description = "患者主索引")
    private String patMiCode;
    @Schema(description = "流转类型:直接存汉字")
    private String transferClass;
    @Schema(description = "流转时间")
    private LocalDateTime transferDate;
    @Schema(description = "变更前机构")
    private String beforeOrg;
    @Schema(description = "变更后机构")
    private String afterOrg;
    @Schema(description = "数据抽取时间")
    private LocalDateTime createdDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getTransferClass() {
        return transferClass;
    }

    public void setTransferClass(String transferClass) {
        this.transferClass = transferClass;
    }

    public LocalDateTime getTransferDate() {
        return transferDate;
    }

    public void setTransferDate(LocalDateTime transferDate) {
        this.transferDate = transferDate;
    }

    public String getBeforeOrg() {
        return beforeOrg;
    }

    public void setBeforeOrg(String beforeOrg) {
        this.beforeOrg = beforeOrg;
    }

    public String getAfterOrg() {
        return afterOrg;
    }

    public void setAfterOrg(String afterOrg) {
        this.afterOrg = afterOrg;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisCdrPatTransferLogTo other = (CisCdrPatTransferLogTo) obj;
        return Objects.equals(id, other.id);
    }
}