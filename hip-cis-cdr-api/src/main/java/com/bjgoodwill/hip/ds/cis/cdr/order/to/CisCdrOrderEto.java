package com.bjgoodwill.hip.ds.cis.cdr.order.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@Schema(description = "Cdr的医嘱表")
public abstract class CisCdrOrderEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4336940244498731896L;

    @Schema(description = "医嘱开始时间")
    private LocalDateTime effectiveLowDate;
    @Schema(description = "作废时间")
    private LocalDateTime cancelDate;
    @Schema(description = "状态")
    private CisStatusEnum statusCode;

    public LocalDateTime getEffectiveLowDate() {
        return effectiveLowDate;
    }

    public void setEffectiveLowDate(LocalDateTime effectiveLowDate) {
        this.effectiveLowDate = effectiveLowDate;
    }

    public LocalDateTime getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(LocalDateTime cancelDate) {
        this.cancelDate = cancelDate;
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public String getMinimal_class() {
        return "." + this.getClass().getSimpleName();
    }
}