package com.bjgoodwill.hip.ds.cis.cdr.plan.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医嘱执行记录")
public class CisCdrOrderPlanRecordQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1161258106516177506L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "患者主索引")
    private String patCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "医嘱id")
    private String orderId;
    @Schema(description = "申请单id")
    private String applyId;
    @Schema(description = "医嘱类型")
    private SystemTypeEnum orderClass;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = patCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }
}