package com.bjgoodwill.hip.ds.cis.cdr.order.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医嘱内容")
public class CisCdrOrderContentNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -7278640254086346557L;

    @Schema(description = "医嘱id")
    private String orderId;
    @Schema(description = "医嘱内容")
    private byte[] orderContent;

    @NotBlank(message = "医嘱id不能为空！")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = StringUtils.trimToNull(orderId);
    }

    public byte[] getOrderContent() {
        return orderContent;
    }

    public void setOrderContent(byte[] orderContent) {
        this.orderContent = orderContent;
    }
}