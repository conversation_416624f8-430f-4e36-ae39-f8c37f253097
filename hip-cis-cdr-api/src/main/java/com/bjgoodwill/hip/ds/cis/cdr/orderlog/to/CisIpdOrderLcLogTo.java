package com.bjgoodwill.hip.ds.cis.cdr.orderlog.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "住院医嘱闭环日志")
public class CisIpdOrderLcLogTo extends CisOrderClLogTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -5442396687755988984L;

    @Schema(description = "住院号")
    private String patCode;
    @Schema(description = "执行单id")
    private String orderSplitId;

    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = patCode;
    }

    public String getOrderSplitId() {
        return orderSplitId;
    }

    public void setOrderSplitId(String orderSplitId) {
        this.orderSplitId = orderSplitId;
    }

}