package com.bjgoodwill.hip.ds.cis.cdr.allergy.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.to.CisPatDrugAllergyEto;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.to.CisPatDrugAllergyNto;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.to.CisPatDrugAllergyQto;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.to.CisPatDrugAllergyTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "患者药品过敏记录领域服务", description = "患者药品过敏记录领域服务")
public interface CisPatDrugAllergyService {

    @Operation(summary = "P0根据查询条件对患者药品过敏记录进行查询。")
    @GetMapping("/cisPatDrugAllergies")
    List<CisPatDrugAllergyTo> getCisPatDrugAllergies(@ParameterObject @SpringQueryMap CisPatDrugAllergyQto cisPatDrugAllergyQto);

    @Operation(summary = "根据查询条件对患者药品过敏记录进行分页查询。")
    @GetMapping("/cisPatDrugAllergies/pages")
    GridResultSet<CisPatDrugAllergyTo> getCisPatDrugAllergyPage(@ParameterObject @SpringQueryMap CisPatDrugAllergyQto cisPatDrugAllergyQto);

    @Operation(summary = "根据唯一标识返回患者药品过敏记录。")
    @GetMapping("/cisPatDrugAllergies/{id:.+}")
    CisPatDrugAllergyTo getCisPatDrugAllergyById(@PathVariable("id") String id);

    @Operation(summary = "创建患者药品过敏记录。")
    @PostMapping("/cisPatDrugAllergies")
    CisPatDrugAllergyTo createCisPatDrugAllergy(@RequestBody @Valid CisPatDrugAllergyNto cisPatDrugAllergyNto);

    @Operation(summary = "根据唯一标识修改患者药品过敏记录。")
    @PutMapping("/cisPatDrugAllergies/{id:.+}")
    void updateCisPatDrugAllergy(@PathVariable("id") String id, @RequestBody @Valid CisPatDrugAllergyEto cisPatDrugAllergyEto);

    @Operation(summary = "根据唯一标识删除患者药品过敏记录。")
    @DeleteMapping("/cisPatDrugAllergies/{id:.+}")
    void deleteCisPatDrugAllergy(@PathVariable("id") String id);

}