package com.bjgoodwill.hip.ds.cis.cdr.order.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医嘱内容")
public class CisCdrOrderContentEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1255874293261049152L;

    @Schema(description = "医嘱内容")
    private byte[] orderContent;

    public byte[] getOrderContent() {
        return orderContent;
    }

    public void setOrderContent(byte[] orderContent) {
        this.orderContent = orderContent;
    }
}