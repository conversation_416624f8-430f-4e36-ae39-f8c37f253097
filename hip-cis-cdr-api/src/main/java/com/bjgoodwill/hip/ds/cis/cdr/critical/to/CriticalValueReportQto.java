package com.bjgoodwill.hip.ds.cis.cdr.critical.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "危急值报告")
public class CriticalValueReportQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6585560968411865001L;

    @Schema(description = "患者类型")
    private VisitTypeEnum visitType;
    @Schema(description = "住院号")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "护理组编码")
    private String inDeptCode;
    @Schema(description = "开单科室编码")
    private String visitOrgCode;
    @Schema(description = "开单医生编码")
    private String doctCode;
    @Schema(description = "申请单号")
    private String applyCode;
    @Schema(description = "服务项目名称")
    private String serviceItemCode;
    @Schema(description = "服务项目名称")
    private String serviceItemName;
    @Schema(description = "危急值上报人")
    private String reportUser;
    @Schema(description = "危急值上报日期")
    private LocalDateTime reportDate;
    @Schema(description = "危急值上报科室")
    private String reportOrgCode;
    @Schema(description = "危急值描述")
    private String criticalValue;
    @Schema(description = "应答科室")
    private String responseOrgCode;
    @Schema(description = "临床答复人")
    private String responseUser;
    @Schema(description = "临床答复时间")
    private LocalDateTime responseDate;
    @Schema(description = "临床答复内容")
    private String responseValue;
    @Schema(description = "医技电话发起人")
    private String telResponseUser;
    @Schema(description = "医技电话发起时间")
    private LocalDateTime telResponseDate;
    @Schema(description = "医技补充说明")
    private String telResponseRemark;
    @Schema(description = "医技电话确认标识")
    private Boolean telReponseFlag;
    @Schema(description = "危急值医嘱Id")
    private String orderId;
    @Schema(description = "病程记录")
    private String courseRecord;
    @Schema(description = "病程记录人")
    private String courseRecordUser;
    @Schema(description = "病程记录时间")
    private LocalDateTime courseRecordDate;
    @Schema(description = "答复状态（未答复、已答复）")
    private Boolean statusCode;
    @Schema(description = "医嘱开立时间")
    private LocalDateTime orderCreatedDate;
    @Schema(description = "采样日期")
    private LocalDateTime samplingDate;
    @Schema(description = "调用方标识")
    private String call;
    @Schema(description = "调用Url")
    private String callUrl;
    @Schema(description = "调用方唯一标识")
    private String crisisId;
    @Schema(description = "报告开始日期")
    private LocalDateTime beginReportDate;
    @Schema(description = "报告结束日期")
    private LocalDateTime endReportDate;

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getInDeptCode() {
        return inDeptCode;
    }

    public void setInDeptCode(String inDeptCode) {
        this.inDeptCode = inDeptCode;
    }

    public String getVisitOrgCode() {
        return visitOrgCode;
    }

    public void setVisitOrgCode(String visitOrgCode) {
        this.visitOrgCode = visitOrgCode;
    }

    public String getDoctCode() {
        return doctCode;
    }

    public void setDoctCode(String doctCode) {
        this.doctCode = doctCode;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getReportUser() {
        return reportUser;
    }

    public void setReportUser(String reportUser) {
        this.reportUser = reportUser;
    }

    public LocalDateTime getReportDate() {
        return reportDate;
    }

    public void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    public String getReportOrgCode() {
        return reportOrgCode;
    }

    public void setReportOrgCode(String reportOrgCode) {
        this.reportOrgCode = reportOrgCode;
    }

    public String getCriticalValue() {
        return criticalValue;
    }

    public void setCriticalValue(String criticalValue) {
        this.criticalValue = criticalValue;
    }

    public String getResponseOrgCode() {
        return responseOrgCode;
    }

    public void setResponseOrgCode(String responseOrgCode) {
        this.responseOrgCode = responseOrgCode;
    }

    public String getResponseUser() {
        return responseUser;
    }

    public void setResponseUser(String responseUser) {
        this.responseUser = responseUser;
    }

    public LocalDateTime getResponseDate() {
        return responseDate;
    }

    public void setResponseDate(LocalDateTime responseDate) {
        this.responseDate = responseDate;
    }

    public String getResponseValue() {
        return responseValue;
    }

    public void setResponseValue(String responseValue) {
        this.responseValue = responseValue;
    }

    public String getTelResponseUser() {
        return telResponseUser;
    }

    public void setTelResponseUser(String telResponseUser) {
        this.telResponseUser = telResponseUser;
    }

    public LocalDateTime getTelResponseDate() {
        return telResponseDate;
    }

    public void setTelResponseDate(LocalDateTime telResponseDate) {
        this.telResponseDate = telResponseDate;
    }

    public String getTelResponseRemark() {
        return telResponseRemark;
    }

    public void setTelResponseRemark(String telResponseRemark) {
        this.telResponseRemark = telResponseRemark;
    }

    public Boolean getTelReponseFlag() {
        return telReponseFlag;
    }

    public void setTelReponseFlag(Boolean telReponseFlag) {
        this.telReponseFlag = telReponseFlag;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCourseRecord() {
        return courseRecord;
    }

    public void setCourseRecord(String courseRecord) {
        this.courseRecord = courseRecord;
    }

    public String getCourseRecordUser() {
        return courseRecordUser;
    }

    public void setCourseRecordUser(String courseRecordUser) {
        this.courseRecordUser = courseRecordUser;
    }

    public LocalDateTime getCourseRecordDate() {
        return courseRecordDate;
    }

    public void setCourseRecordDate(LocalDateTime courseRecordDate) {
        this.courseRecordDate = courseRecordDate;
    }

    public Boolean getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Boolean statusCode) {
        this.statusCode = statusCode;
    }

    public LocalDateTime getOrderCreatedDate() {
        return orderCreatedDate;
    }

    public void setOrderCreatedDate(LocalDateTime orderCreatedDate) {
        this.orderCreatedDate = orderCreatedDate;
    }

    public LocalDateTime getSamplingDate() {
        return samplingDate;
    }

    public void setSamplingDate(LocalDateTime samplingDate) {
        this.samplingDate = samplingDate;
    }

    public String getCall() {
        return call;
    }

    public void setCall(String call) {
        this.call = call;
    }

    public String getCallUrl() {
        return callUrl;
    }

    public void setCallUrl(String callUrl) {
        this.callUrl = callUrl;
    }

    public String getCrisisId() {
        return crisisId;
    }

    public void setCrisisId(String crisisId) {
        this.crisisId = crisisId;
    }

    public LocalDateTime getBeginReportDate() {
        return beginReportDate;
    }

    public void setBeginReportDate(LocalDateTime beginReportDate) {
        this.beginReportDate = beginReportDate;
    }

    public LocalDateTime getEndReportDate() {
        return endReportDate;
    }

    public void setEndReportDate(LocalDateTime endReportDate) {
        this.endReportDate = endReportDate;
    }
}