package com.bjgoodwill.hip.ds.cis.cdr.order.to;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "住院长期医嘱")
public class CisCdrIpdLongOrderNto extends CisCdrOrderNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -8354271561569735479L;

    @Schema(description = "医嘱截止时间")
    private LocalDateTime effectiveHighDate;
    @Schema(description = "停止人")
    private String stopStaff;
    @Schema(description = "停止人姓名")
    private String stopStaffName;
    @Schema(description = "停止时间")
    private LocalDateTime stopDate;
    @Schema(description = "停止校对人")
    private String stopProofStaff;
    @Schema(description = "停止校对人姓名")
    private String stopProofStaffName;
    @Schema(description = "停止校对时间")
    private LocalDateTime stopProofDate;
    @Schema(description = "最后拆分日期")
    private LocalDateTime lastSplitDate;
    @Schema(description = "首日次数")
    private String firstDayTimepoint;

    public LocalDateTime getEffectiveHighDate() {
        return effectiveHighDate;
    }

    public void setEffectiveHighDate(LocalDateTime effectiveHighDate) {
        this.effectiveHighDate = effectiveHighDate;
    }

    public String getStopStaff() {
        return stopStaff;
    }

    public void setStopStaff(String stopStaff) {
        this.stopStaff = StringUtils.trimToNull(stopStaff);
    }

    public String getStopStaffName() {
        return stopStaffName;
    }

    public void setStopStaffName(String stopStaffName) {
        this.stopStaffName = StringUtils.trimToNull(stopStaffName);
    }

    public LocalDateTime getStopDate() {
        return stopDate;
    }

    public void setStopDate(LocalDateTime stopDate) {
        this.stopDate = stopDate;
    }

    public String getStopProofStaff() {
        return stopProofStaff;
    }

    public void setStopProofStaff(String stopProofStaff) {
        this.stopProofStaff = StringUtils.trimToNull(stopProofStaff);
    }

    public String getStopProofStaffName() {
        return stopProofStaffName;
    }

    public void setStopProofStaffName(String stopProofStaffName) {
        this.stopProofStaffName = StringUtils.trimToNull(stopProofStaffName);
    }

    public LocalDateTime getStopProofDate() {
        return stopProofDate;
    }

    public void setStopProofDate(LocalDateTime stopProofDate) {
        this.stopProofDate = stopProofDate;
    }

    public LocalDateTime getLastSplitDate() {
        return lastSplitDate;
    }

    public void setLastSplitDate(LocalDateTime lastSplitDate) {
        this.lastSplitDate = lastSplitDate;
    }

    public String getFirstDayTimepoint() {
        return firstDayTimepoint;
    }

    public void setFirstDayTimepoint(String firstDayTimepoint) {
        this.firstDayTimepoint = StringUtils.trimToNull(firstDayTimepoint);
    }
}