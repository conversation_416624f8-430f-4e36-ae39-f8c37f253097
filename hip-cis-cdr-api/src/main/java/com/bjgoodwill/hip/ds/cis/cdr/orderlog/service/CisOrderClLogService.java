package com.bjgoodwill.hip.ds.cis.cdr.orderlog.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisOrderClLogQto;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisOrderClLogTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@Tag(name = "医嘱全闭环日志领域服务", description = "医嘱全闭环日志领域服务")
public interface CisOrderClLogService {

    @Operation(summary = "根据查询条件对医嘱全闭环日志进行查询。")
    @GetMapping("/cisOrderClLogs")
    List<CisOrderClLogTo> getCisOrderClLogs(@ParameterObject @SpringQueryMap CisOrderClLogQto cisOrderClLogQto);

    @Operation(summary = "根据查询条件对医嘱全闭环日志进行分页查询。")
    @GetMapping("/cisOrderClLogs/pages")
    GridResultSet<CisOrderClLogTo> getCisOrderClLogPage(@ParameterObject @SpringQueryMap CisOrderClLogQto cisOrderClLogQto);

    @Operation(summary = "根据唯一标识删除医嘱全闭环日志。")
    @DeleteMapping("/cisOrderClLogs/{id:.+}")
    void deleteCisOrderClLog(@PathVariable("id") String id);

}