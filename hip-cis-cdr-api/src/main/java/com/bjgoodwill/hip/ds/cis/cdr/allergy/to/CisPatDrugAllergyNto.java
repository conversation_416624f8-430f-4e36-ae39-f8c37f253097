package com.bjgoodwill.hip.ds.cis.cdr.allergy.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.bjgoodwill.hip.ds.cis.cdr.allergy.enmus.RecordTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "患者药品过敏记录")
public class CisPatDrugAllergyNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -5960110665672939897L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "主索引编码")
    private String patMiCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者类型")
    private VisitTypeEnum visitType;
    @Schema(description = "记录类型：ast皮试，adv药品不良事件")
    private RecordTypeEnum recordType;
    @Schema(description = "药理归类：字典pharmacologyClass")
    private String actionType;
    @Schema(description = "商品编码")
    private String drugCode;
    @Schema(description = "商品名称")
    private String drugName;
    @Schema(description = "批号")
    private String batchNo;
    @Schema(description = "医嘱ID或药品不良事件ID")
    private String recordId;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "主索引编码不能为空！")
    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = StringUtils.trimToNull(patMiCode);
    }

    @NotBlank(message = "就诊流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public RecordTypeEnum getRecordType() {
        return recordType;
    }

    public void setRecordType(RecordTypeEnum recordType) {
        this.recordType = recordType;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = StringUtils.trimToNull(actionType);
    }

    @NotBlank(message = "商品编码不能为空！")
    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = StringUtils.trimToNull(drugCode);
    }

    public String getDrugName() {
        return drugName;
    }

    public void setDrugName(String drugName) {
        this.drugName = StringUtils.trimToNull(drugName);
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = StringUtils.trimToNull(batchNo);
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = StringUtils.trimToNull(recordId);
    }
}