package com.bjgoodwill.hip.ds.cis.cdr.plan.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-03-27 15:51
 * @className: CisIpdDrugCdrOrderPlanRecordQto
 * @description: 住院药品执行记录查询传输对象
 **/
public class CisIpdDrugCdrOrderPlanRecordQto extends BaseQto implements Serializable {

    @Schema(description = "就诊流水号")
    private String visitCode;

    @Schema(description = "药品编码")
    private String drugGood;

    @Schema(description = "开始日期")
    private LocalDateTime startDate;

    @Schema(description = "结束日期")
    private LocalDateTime endDate;

    @NotBlank(message = "就诊流水号不能为空")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    @NotBlank(message = "药品编码不能为空")
    public String getDrugGood() {
        return drugGood;
    }

    public void setDrugGood(String drugGood) {
        this.drugGood = drugGood;
    }

    @NotNull(message = "开始日期不能为空")
    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    @NotNull(message = "结束日期不能为空")
    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }
}