package com.bjgoodwill.hip.ds.cis.cdr.setting.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "患者视图过滤设置")
public class CisPatSettingQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4498533339223052292L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "医嘱类型")
    private SystemTypeEnum orderClass;
    @Schema(description = "医生编码")
    private String docCode;
    @Schema(description = "工作组编码")
    private String orgCode;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = docCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }
}