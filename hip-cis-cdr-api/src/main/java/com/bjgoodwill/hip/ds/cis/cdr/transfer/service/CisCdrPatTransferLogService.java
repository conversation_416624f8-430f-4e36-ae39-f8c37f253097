package com.bjgoodwill.hip.ds.cis.cdr.transfer.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.transfer.to.CisCdrPatTransferLogNto;
import com.bjgoodwill.hip.ds.cis.cdr.transfer.to.CisCdrPatTransferLogQto;
import com.bjgoodwill.hip.ds.cis.cdr.transfer.to.CisCdrPatTransferLogTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "患者流转日志记录领域服务", description = "患者流转日志记录领域服务")
public interface CisCdrPatTransferLogService {

    @Operation(summary = "根据查询条件对患者流转日志记录进行查询。")
    @GetMapping("/cisCdrPatTransferLogs")
    List<CisCdrPatTransferLogTo> getCisCdrPatTransferLogs(@ParameterObject @SpringQueryMap CisCdrPatTransferLogQto cisCdrPatTransferLogQto);

    @Operation(summary = "根据查询条件对患者流转日志记录进行分页查询。")
    @GetMapping("/cisCdrPatTransferLogs/pages")
    GridResultSet<CisCdrPatTransferLogTo> getCisCdrPatTransferLogPage(@ParameterObject @SpringQueryMap CisCdrPatTransferLogQto cisCdrPatTransferLogQto);

    @Operation(summary = "创建患者流转日志记录。")
    @PostMapping("/cisCdrPatTransferLogs")
    CisCdrPatTransferLogTo createCisCdrPatTransferLog(@RequestBody @Valid CisCdrPatTransferLogNto cisCdrPatTransferLogNto);

    @Operation(summary = "根据唯一标识删除患者流转日志记录。")
    @DeleteMapping("/cisCdrPatTransferLogs/{id:.+}")
    void deleteCisCdrPatTransferLog(@PathVariable("id") String id);

}