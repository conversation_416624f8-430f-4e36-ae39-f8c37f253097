package com.bjgoodwill.hip.ds.cis.cdr.report.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "报告结果")
public class CisReportResultNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1387504838671507511L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "医嘱id")
    private String orderID;
    @Schema(description = "申请单ID")
    private String applyId;
    @Schema(description = "存储其他系统报告主键，用来取消其他系统报告")
    private String reportId;
    @Schema(description = "主索引编码")
    private String patMiCode;
    @Schema(description = "接诊流水编码")
    private String visitCode;
    @Schema(description = "结果类型 SPCOBS/DGIMG")
    private SystemTypeEnum reportType;
    @Schema(description = "诊疗服务项目")
    private String serviceItemCode;
    @Schema(description = "诊疗服务项目名称")
    private String serviceItemName;
    @Schema(description = "设备类型")
    private String deviceType;
    @Schema(description = "执行科室代码")
    private String execOrgCode;
    @Schema(description = "reportDate")
    private LocalDateTime reportDate;
    @Schema(description = "执行人编码")
    private String execStaff;
    @Schema(description = "执行人")
    private String execStaffName;
    @Schema(description = "医嘱报告明细列表")
    private List<CisReportResultDetailNto> cisReportResultDetails = new ArrayList<>();

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    public String getOrderID() {
        return orderID;
    }

    public void setOrderID(String orderID) {
        this.orderID = StringUtils.trimToNull(orderID);
    }

    @NotBlank(message = "申请单ID不能为空！")
    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = StringUtils.trimToNull(applyId);
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = StringUtils.trimToNull(reportId);
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = StringUtils.trimToNull(patMiCode);
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    public SystemTypeEnum getReportType() {
        return reportType;
    }

    public void setReportType(SystemTypeEnum reportType) {
        this.reportType = reportType;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = StringUtils.trimToNull(serviceItemCode);
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getExecOrgCode() {
        return execOrgCode;
    }

    public void setExecOrgCode(String execOrgCode) {
        this.execOrgCode = StringUtils.trimToNull(execOrgCode);
    }

    public LocalDateTime getReportDate() {
        return reportDate;
    }

    public void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    public String getExecStaff() {
        return execStaff;
    }

    public void setExecStaff(String execStaff) {
        this.execStaff = StringUtils.trimToNull(execStaff);
    }

    public String getExecStaffName() {
        return execStaffName;
    }

    public void setExecStaffName(String execStaffName) {
        this.execStaffName = StringUtils.trimToNull(execStaffName);
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public List<CisReportResultDetailNto> getCisReportResultDetails() {
        return cisReportResultDetails;
    }

    public void setCisReportResultDetails(List<CisReportResultDetailNto> cisReportResultDetails) {
        this.cisReportResultDetails = cisReportResultDetails;
    }
}