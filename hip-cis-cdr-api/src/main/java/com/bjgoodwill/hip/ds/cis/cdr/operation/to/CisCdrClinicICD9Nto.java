package com.bjgoodwill.hip.ds.cis.cdr.operation.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "患者术式记录")
public class CisCdrClinicICD9Nto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2199897523837254186L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者主索引")
    private String patMiCode;
    @Schema(description = "医嘱ID")
    private String orderId;
    @Schema(description = "申请单ID")
    private String applyId;
    @Schema(description = "术式编码")
    private String icd9Code;
    @Schema(description = "术式名称")
    private String icd9Name;
    @Schema(description = "手术名称")
    private String serviceItemName;
    @Schema(description = "手术开始时间")
    private LocalDateTime operationStartDate;
    @Schema(description = "手术结束时间")
    private LocalDateTime operationEndDate;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "就诊流水号不能为空！")
    @Size(max = 32, message = "就诊流水号长度不能超过32个字符！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @NotBlank(message = "患者主索引不能为空！")
    @Size(max = 32, message = "患者主索引长度不能超过32个字符！")
    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = StringUtils.trimToNull(patMiCode);
    }

    @NotBlank(message = "医嘱ID不能为空！")
    @Size(max = 50, message = "医嘱ID长度不能超过50个字符！")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = StringUtils.trimToNull(orderId);
    }

    @NotBlank(message = "申请单ID不能为空！")
    @Size(max = 50, message = "申请单ID长度不能超过50个字符！")
    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = StringUtils.trimToNull(applyId);
    }

    @Size(max = 50, message = "术式编码长度不能超过50个字符！")
    public String getIcd9Code() {
        return icd9Code;
    }

    public void setIcd9Code(String icd9Code) {
        this.icd9Code = StringUtils.trimToNull(icd9Code);
    }

    @Size(max = 100, message = "术式名称长度不能超过100个字符！")
    public String getIcd9Name() {
        return icd9Name;
    }

    public void setIcd9Name(String icd9Name) {
        this.icd9Name = StringUtils.trimToNull(icd9Name);
    }

    @Size(max = 50, message = "手术名称长度不能超过50个字符！")
    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = StringUtils.trimToNull(serviceItemName);
    }

    public LocalDateTime getOperationStartDate() {
        return operationStartDate;
    }

    public void setOperationStartDate(LocalDateTime operationStartDate) {
        this.operationStartDate = operationStartDate;
    }

    public LocalDateTime getOperationEndDate() {
        return operationEndDate;
    }

    public void setOperationEndDate(LocalDateTime operationEndDate) {
        this.operationEndDate = operationEndDate;
    }

    @NotNull(message = "创建的时间不能为空！")
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
}