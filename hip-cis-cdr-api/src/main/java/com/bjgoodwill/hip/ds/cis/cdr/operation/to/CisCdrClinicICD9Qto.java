package com.bjgoodwill.hip.ds.cis.cdr.operation.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "患者术式记录")
public class CisCdrClinicICD9Qto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -57460915741734349L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "患者主索引")
    private String patMiCode;
    @Schema(description = "医嘱ID")
    private String orderId;
    @Schema(description = "申请单ID")
    private String applyId;
    @Schema(description = "手术名称")
    private String serviceItemName;
    @Schema(description = "手术开始时间")
    private LocalDateTime operationStartDate;
    @Schema(description = "手术结束时间")
    private LocalDateTime operationEndDate;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public LocalDateTime getOperationStartDate() {
        return operationStartDate;
    }

    public void setOperationStartDate(LocalDateTime operationStartDate) {
        this.operationStartDate = operationStartDate;
    }

    public LocalDateTime getOperationEndDate() {
        return operationEndDate;
    }

    public void setOperationEndDate(LocalDateTime operationEndDate) {
        this.operationEndDate = operationEndDate;
    }
}