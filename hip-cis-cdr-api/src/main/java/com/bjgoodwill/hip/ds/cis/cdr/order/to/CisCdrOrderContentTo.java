package com.bjgoodwill.hip.ds.cis.cdr.order.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Schema(description = "医嘱内容")
public class CisCdrOrderContentTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -4941194033163762858L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "医嘱id")
    private String orderId;
    @Schema(description = "医嘱内容")
    private byte[] orderContent;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public byte[] getOrderContent() {
        return orderContent;
    }

    public void setOrderContent(byte[] orderContent) {
        this.orderContent = orderContent;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisCdrOrderContentTo other = (CisCdrOrderContentTo) obj;
        return Objects.equals(id, other.id);
    }
}