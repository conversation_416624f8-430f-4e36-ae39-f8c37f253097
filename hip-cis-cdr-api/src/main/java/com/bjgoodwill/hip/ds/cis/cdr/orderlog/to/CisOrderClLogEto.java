package com.bjgoodwill.hip.ds.cis.cdr.orderlog.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@Schema(description = "医嘱全闭环日志")
public abstract class CisOrderClLogEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -929797265667479160L;


    public String getMinimal_class() {
        return "." + this.getClass().getSimpleName();
    }
}