package com.bjgoodwill.hip.ds.cis.cdr.report.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.ReportResultFlagEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

//@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(description = "医嘱报告明细")
public class CisReportResultDetailEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -5758914304232128536L;

    @Schema(description = "标本编码（试管号）")
    private String sampleCode;
    @Schema(description = "标本类型")
    private String sampleType;
    @Schema(description = "诊疗项目编码")
    private String itemCode;
    @Schema(description = "诊疗项目名称")
    private String itemName;
    @Schema(description = "报告项目编码")
    private String subItemCode;
    @Schema(description = "报告项目名称")
    private String subItemName;
    @Schema(description = "单位编码")
    private String unitCode;
    @Schema(description = "单位名称")
    private String unitName;
    @Schema(description = "结果值标识")
    private ReportResultFlagEnum resultFlag;
    @Schema(description = "危机值结果标识")
    private String crisisResultFlag;
    @Schema(description = "补充说明")
    private String remark;

    public String getSampleCode() {
        return sampleCode;
    }

    public void setSampleCode(String sampleCode) {
        this.sampleCode = StringUtils.trimToNull(sampleCode);
    }

    public String getSampleType() {
        return sampleType;
    }

    public void setSampleType(String sampleType) {
        this.sampleType = StringUtils.trimToNull(sampleType);
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = StringUtils.trimToNull(itemCode);
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = StringUtils.trimToNull(itemName);
    }

    public String getSubItemCode() {
        return subItemCode;
    }

    public void setSubItemCode(String subItemCode) {
        this.subItemCode = StringUtils.trimToNull(subItemCode);
    }

    public String getSubItemName() {
        return subItemName;
    }

    public void setSubItemName(String subItemName) {
        this.subItemName = StringUtils.trimToNull(subItemName);
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = StringUtils.trimToNull(unitCode);
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = StringUtils.trimToNull(unitName);
    }

    public ReportResultFlagEnum getResultFlag() {
        return resultFlag;
    }

    public void setResultFlag(ReportResultFlagEnum resultFlag) {
        this.resultFlag = resultFlag;
    }

    public String getCrisisResultFlag() {
        return crisisResultFlag;
    }

    public void setCrisisResultFlag(String crisisResultFlag) {
        this.crisisResultFlag = StringUtils.trimToNull(crisisResultFlag);
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = StringUtils.trimToNull(remark);
    }

    //    public String getMinimal_class() {
//        return "." + this.getClass().getSimpleName();
//    }
    @JsonProperty("@class")
    public String getClassName() {
        return getClass().getName();
    }

}