package com.bjgoodwill.hip.ds.cis.cdr.record.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisIpdDrugCdrOrderPlanRecordQto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicIpdRecordEto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicIpdRecordNto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicIpdRecordTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "住院就诊记录领域服务", description = "住院就诊记录领域服务")
public interface CisCdrClinicIpdRecordService {

//    @Operation(summary = "根据查询条件对住院就诊记录进行查询。")
//    @GetMapping("/cisCdrClinicIpdRecords")
//    List<CisCdrClinicIpdRecordTo> getCisCdrClinicIpdRecords(@ParameterObject @SpringQueryMap CisCdrClinicIpdRecordQto cisCdrClinicIpdRecordQto);
//
//    @Operation(summary = "根据查询条件对住院就诊记录进行分页查询。")
//    @GetMapping("/cisCdrClinicIpdRecords/pages")
//    GridResultSet<CisCdrClinicIpdRecordTo> getCisCdrClinicIpdRecordPage(@ParameterObject @SpringQueryMap CisCdrClinicIpdRecordQto cisCdrClinicIpdRecordQto);

    @Operation(summary = "创建住院就诊记录。")
    @PostMapping("/cisCdrClinicIpdRecords")
    CisCdrClinicIpdRecordTo createCisCdrClinicIpdRecord(@RequestBody @Valid CisCdrClinicIpdRecordNto cisCdrClinicIpdRecordNto);

    @Operation(summary = "根据唯一标识修改住院就诊记录。")
    @PutMapping("/cisCdrClinicIpdRecords/{id:.+}")
    void updateCisCdrClinicIpdRecord(@PathVariable("id") String id, @RequestBody @Valid CisCdrClinicIpdRecordEto cisCdrClinicIpdRecordEto);

    @Operation(summary = "根据查询条件对住院就诊记录进行查询。")
    @GetMapping("/cisCdrClinicIpdRecords")
    List<CisCdrClinicIpdRecordTo> getCisCdrClinicIpdRecords(@ParameterObject @SpringQueryMap CisIpdDrugCdrOrderPlanRecordQto cisIpdDrugCdrOrderPlanRecordQto);

    @Operation(summary = "根据查询条件对住院就诊记录进行分页查询。")
    @GetMapping("/cisCdrClinicIpdRecords/pages")
    GridResultSet<CisCdrClinicIpdRecordTo> getCisCdrClinicIpdRecordPage(@ParameterObject @SpringQueryMap CisIpdDrugCdrOrderPlanRecordQto cisIpdDrugCdrOrderPlanRecordQto);

}