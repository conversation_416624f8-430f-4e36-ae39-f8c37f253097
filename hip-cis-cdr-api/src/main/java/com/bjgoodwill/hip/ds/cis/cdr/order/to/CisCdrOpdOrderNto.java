package com.bjgoodwill.hip.ds.cis.cdr.order.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "临时医嘱")
public class CisCdrOpdOrderNto extends CisCdrOrderNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -9040651148170361470L;

    @Schema(description = "处方号")
    private String prescriptionCode;

    @NotBlank(message = "处方号不能为空！")
    public String getPrescriptionCode() {
        return prescriptionCode;
    }

    public void setPrescriptionCode(String prescriptionCode) {
        this.prescriptionCode = StringUtils.trimToNull(prescriptionCode);
    }
}