package com.bjgoodwill.hip.ds.cis.cdr.plan.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "医嘱执行记录领域服务", description = "医嘱执行记录领域服务")
public interface CisCdrOrderPlanRecordService {

    @Operation(summary = "根据查询条件对医嘱执行记录进行查询。")
    @GetMapping("/cisCdrOrderPlanRecords")
    List<CisCdrOrderPlanRecordTo> getCisCdrOrderPlanRecords(@ParameterObject @SpringQueryMap CisCdrOrderPlanRecordQto cisCdrOrderPlanRecordQto);

    @Operation(summary = "根据查询条件对医嘱执行记录进行分页查询。")
    @GetMapping("/cisCdrOrderPlanRecords/pages")
    GridResultSet<CisCdrOrderPlanRecordTo> getCisCdrOrderPlanRecordPage(@ParameterObject @SpringQueryMap CisCdrOrderPlanRecordQto cisCdrOrderPlanRecordQto);

    @Operation(summary = "创建医嘱执行记录。")
    @PostMapping("/cisCdrOrderPlanRecords")
    CisCdrOrderPlanRecordTo createCisCdrOrderPlanRecord(@RequestBody @Valid CisCdrOrderPlanRecordNto cisCdrOrderPlanRecordNto);

    @Operation(summary = "根据唯一标识删除医嘱执行记录。")
    @DeleteMapping("/cisCdrOrderPlanRecords/{id:.+}")
    void deleteCisCdrOrderPlanRecord(@PathVariable("id") String id);

    @Operation(summary = "根据唯一标识返回医嘱执行单明细。")
    @GetMapping("/cisCdrOrderPlanRecords/xId/cisCdrOrderPlanDetailRecords/{id:.+}")
    CisCdrOrderPlanDetailRecordTo getCisCdrOrderPlanDetailRecordById(@PathVariable("id") String id);

    @Operation(summary = "创建医嘱执行单明细。")
    @PostMapping("/cisCdrOrderPlanRecords/{orderPlanId}/cisCdrOrderPlanDetailRecords")
    CisCdrOrderPlanDetailRecordTo createCisCdrOrderPlanDetailRecord(@PathVariable("orderPlanId") String orderPlanId, @RequestBody @Valid CisCdrOrderPlanDetailRecordNto cisCdrOrderPlanDetailRecordNto);

    @Operation(summary = "根据唯一标识删除医嘱执行单明细。")
    @DeleteMapping("/cisCdrOrderPlanRecords/xId/cisCdrOrderPlanDetailRecords/{id:.+}")
    void deleteCisCdrOrderPlanDetailRecord(@PathVariable("id") String id);

//    @Operation(summary = "创建医嘱执行记录。")
//    @PostMapping("/cisCdrOrderPlanRecords/mul")
//    void createCisCdrOrderPlanRecords(@RequestBody @Valid List<CisCdrOrderPlanRecordNto> cisCdrOrderPlanRecordNtos);

    @Operation(summary = "创建医嘱执行记录。")
    @PostMapping("/cisCdrOrderPlanRecords/mul")
    void createCisCdrOrderPlanRecords(@RequestBody @Valid CisCdrOrderPlanRecordWithDetailNto cisCdrOrderPlanRecordWithDetailNto);

    @Operation(summary = "创建医嘱执行记录。")
    @GetMapping("/cisCdrOrderPlanRecords/lastUpdateTime")
    LocalDateTime getLastUpdateTime();

}