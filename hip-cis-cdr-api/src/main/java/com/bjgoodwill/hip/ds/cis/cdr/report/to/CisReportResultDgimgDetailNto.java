package com.bjgoodwill.hip.ds.cis.cdr.report.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "检查报告明细")
public class CisReportResultDgimgDetailNto extends CisReportResultDetailNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6673519533428647957L;

    @Schema(description = "报告结果")
    private String resultContent;

    @Schema(description = "部位")
    private String humanOrgans;

    @Size(max = 1024, message = "报告结果长度不能超过1,024个字符！")
    public String getResultContent() {
        return resultContent;
    }

    public void setResultContent(String resultContent) {
        this.resultContent = StringUtils.trimToNull(resultContent);
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }
}