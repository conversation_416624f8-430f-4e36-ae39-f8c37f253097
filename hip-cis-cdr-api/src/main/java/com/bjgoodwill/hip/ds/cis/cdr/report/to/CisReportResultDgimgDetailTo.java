package com.bjgoodwill.hip.ds.cis.cdr.report.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "检查报告明细")
public class CisReportResultDgimgDetailTo extends CisReportResultDetailTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -4085811233649213367L;

    @Schema(description = "报告结果")
    private String resultContent;

    @Schema(description = "部位")
    private String humanOrgans;

    public String getResultContent() {
        return resultContent;
    }

    public void setResultContent(String resultContent) {
        this.resultContent = resultContent;
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }
}