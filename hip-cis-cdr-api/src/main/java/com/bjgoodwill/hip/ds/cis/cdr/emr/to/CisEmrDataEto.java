package com.bjgoodwill.hip.ds.cis.cdr.emr.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "病历信息")
public class CisEmrDataEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1393520060163557296L;

    @Schema(description = "主诉")
    private String chiefComplaint;
    @Schema(description = "现病史")
    private String presentIllnessHistory;
    @Schema(description = "既往史")
    private String pastMedicalHistory;
    @Schema(description = "过敏史")
    private String allergicHistory;
    @Schema(description = "个人史")
    private String socialHistory;
    @Schema(description = "家族史")
    private String familyHistory;
    @Schema(description = "治疗方案")
    private String treatmentPlan;
    @Schema(description = "上次月经时间")
    private LocalDateTime lastMenstrualPeriod;
    @Schema(description = "版本")
    private Integer version;

    public String getChiefComplaint() {
        return chiefComplaint;
    }

    public void setChiefComplaint(String chiefComplaint) {
        this.chiefComplaint = StringUtils.trimToNull(chiefComplaint);
    }

    public String getPresentIllnessHistory() {
        return presentIllnessHistory;
    }

    public void setPresentIllnessHistory(String presentIllnessHistory) {
        this.presentIllnessHistory = StringUtils.trimToNull(presentIllnessHistory);
    }

    public String getPastMedicalHistory() {
        return pastMedicalHistory;
    }

    public void setPastMedicalHistory(String pastMedicalHistory) {
        this.pastMedicalHistory = StringUtils.trimToNull(pastMedicalHistory);
    }

    public String getAllergicHistory() {
        return allergicHistory;
    }

    public void setAllergicHistory(String allergicHistory) {
        this.allergicHistory = StringUtils.trimToNull(allergicHistory);
    }

    public String getSocialHistory() {
        return socialHistory;
    }

    public void setSocialHistory(String socialHistory) {
        this.socialHistory = StringUtils.trimToNull(socialHistory);
    }

    public String getFamilyHistory() {
        return familyHistory;
    }

    public void setFamilyHistory(String familyHistory) {
        this.familyHistory = StringUtils.trimToNull(familyHistory);
    }

    public String getTreatmentPlan() {
        return treatmentPlan;
    }

    public void setTreatmentPlan(String treatmentPlan) {
        this.treatmentPlan = StringUtils.trimToNull(treatmentPlan);
    }

    public LocalDateTime getLastMenstrualPeriod() {
        return lastMenstrualPeriod;
    }

    public void setLastMenstrualPeriod(LocalDateTime lastMenstrualPeriod) {
        this.lastMenstrualPeriod = lastMenstrualPeriod;
    }

    @NotNull(message = "版本不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}