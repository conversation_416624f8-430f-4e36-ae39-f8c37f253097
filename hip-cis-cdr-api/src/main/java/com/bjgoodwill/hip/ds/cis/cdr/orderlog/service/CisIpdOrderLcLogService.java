package com.bjgoodwill.hip.ds.cis.cdr.orderlog.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisIpdOrderLcLogEto;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisIpdOrderLcLogNto;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisIpdOrderLcLogQto;
import com.bjgoodwill.hip.ds.cis.cdr.orderlog.to.CisIpdOrderLcLogTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "住院医嘱闭环日志领域服务", description = "住院医嘱闭环日志领域服务")
public interface CisIpdOrderLcLogService {

    @Operation(summary = "根据查询条件对住院医嘱闭环日志进行查询。")
    @GetMapping("/cisIpdOrderLcLogs")
    List<CisIpdOrderLcLogTo> getCisIpdOrderLcLogs(@ParameterObject @SpringQueryMap CisIpdOrderLcLogQto cisIpdOrderLcLogQto);

    @Operation(summary = "根据查询条件对住院医嘱闭环日志进行分页查询。")
    @GetMapping("/cisIpdOrderLcLogs/pages")
    GridResultSet<CisIpdOrderLcLogTo> getCisIpdOrderLcLogPage(@ParameterObject @SpringQueryMap CisIpdOrderLcLogQto cisIpdOrderLcLogQto);

    @Operation(summary = "创建住院医嘱闭环日志。")
    @PostMapping("/cisIpdOrderLcLogs")
    CisIpdOrderLcLogTo createCisIpdOrderLcLog(@RequestBody @Valid CisIpdOrderLcLogNto cisIpdOrderLcLogNto);

    @Operation(summary = "根据唯一标识修改住院医嘱闭环日志。")
    @PutMapping("/cisIpdOrderLcLogs/{id:.+}")
    void updateCisIpdOrderLcLog(@PathVariable("id") String id, @RequestBody @Valid CisIpdOrderLcLogEto cisIpdOrderLcLogEto);

}