package com.bjgoodwill.hip.ds.cis.cdr.order.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SkinTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@Schema(description = "Cdr的医嘱表")
public abstract class CisCdrOrderNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2571098977968051348L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "医嘱序号")
    private String sortNo;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "医嘱编码")
    private String orderServiceCode;
    @Schema(description = "医嘱类型")
    private SystemTypeEnum orderClass;
    @Schema(description = "申请单号")
    private String applyCode;
    @Schema(description = "疗程")
    private long treatmentCourse;
    @Schema(description = "疗程单位")
    private String treatmentCourseUnit;
    @Schema(description = "婴儿标记")
    private Boolean babyFlag;
    @Schema(description = "执行科室编码")
    private String executeOrgCode;
    @Schema(description = "执行科室名称")
    private String executeOrgName;
    @Schema(description = "护理组编码")
    private String deptNurseCode;
    @Schema(description = "护理组名称")
    private String deptNurseName;
    @Schema(description = "医嘱开始时间")
    private LocalDateTime effectiveLowDate;
    @Schema(description = "补录标识,1补录")
    private Boolean repairFlag;
    @Schema(description = "主从父节点")
    private String parentCode;
    @Schema(description = "危急值ID")
    private String criticalId;
    @Schema(description = "协定处方")
    private Boolean prescriptionFlag;
    @Schema(description = "合理用药")
    private String passValue;
    @Schema(description = "第三方")
    private String thirdFlag;
    @Schema(description = "领药科室编码")
    private String receiveOrgCode;
    @Schema(description = "领药科室名称")
    private String receiveOrgName;
    @Schema(description = "用法编码")
    private String usage;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "取药方式")
    private SbadmWayEnum sbadmWay;
    @Schema(description = "皮试标记")
    private Boolean skinFlag;
    @Schema(description = "皮试结果")
    private SkinTypeEnum skinType;
    @Schema(description = "创建医生")
    private String createdStaff;
    @Schema(description = "创建的创建医生姓名")
    private String createdStaffName;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "签发时间")
    private LocalDateTime commitDate;
    @Schema(description = "签发人")
    private String submitStaffId;
    @Schema(description = "签发人姓名")
    private String submitStaffName;
    @Schema(description = "校对人")
    private String proofStaff;
    @Schema(description = "校对人姓名")
    private String proofStaffName;
    @Schema(description = "特显符合标识 1符合 0不符合 null不是特限项目")
    private Boolean limitConformFlag;
    @Schema(description = "医院编码")
    private String hospitalCode;
    @Schema(description = "医院名称")
    private String hospitalName;
    @Schema(description = "作废人")
    private String cancelStaff;
    @Schema(description = "作废人姓名")
    private String cancelStaffName;
    @Schema(description = "作废时间")
    private LocalDateTime cancelDate;
    @Schema(description = "作废原因")
    private String cancelRemark;
    @Schema(description = "备注")
    private String reMark;
    @Schema(description = "状态")
    private CisStatusEnum statusCode;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "医嘱序号不能为空！")
    public String getSortNo() {
        return sortNo;
    }

    public void setSortNo(String sortNo) {
        this.sortNo = StringUtils.trimToNull(sortNo);
    }

    @NotBlank(message = "主索引不能为空！")
    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = StringUtils.trimToNull(patMiCode);
    }

    @NotBlank(message = "流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    public String getOrderServiceCode() {
        return orderServiceCode;
    }

    public void setOrderServiceCode(String orderServiceCode) {
        this.orderServiceCode = StringUtils.trimToNull(orderServiceCode);
    }

    @NotNull(message = "医嘱类型不能为空！")
    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    @NotBlank(message = "申请单号不能为空！")
    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = StringUtils.trimToNull(applyCode);
    }

    public long getTreatmentCourse() {
        return treatmentCourse;
    }

    public void setTreatmentCourse(long treatmentCourse) {
        this.treatmentCourse = treatmentCourse;
    }

    public String getTreatmentCourseUnit() {
        return treatmentCourseUnit;
    }

    public void setTreatmentCourseUnit(String treatmentCourseUnit) {
        this.treatmentCourseUnit = StringUtils.trimToNull(treatmentCourseUnit);
    }

    public Boolean getBabyFlag() {
        return babyFlag;
    }

    public void setBabyFlag(Boolean babyFlag) {
        this.babyFlag = babyFlag;
    }

    @NotBlank(message = "执行科室编码不能为空！")
    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    public void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = StringUtils.trimToNull(executeOrgCode);
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = StringUtils.trimToNull(executeOrgName);
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = StringUtils.trimToNull(deptNurseCode);
    }

    public String getDeptNurseName() {
        return deptNurseName;
    }

    public void setDeptNurseName(String deptNurseName) {
        this.deptNurseName = StringUtils.trimToNull(deptNurseName);
    }

    public LocalDateTime getEffectiveLowDate() {
        return effectiveLowDate;
    }

    public void setEffectiveLowDate(LocalDateTime effectiveLowDate) {
        this.effectiveLowDate = effectiveLowDate;
    }

    public Boolean getRepairFlag() {
        return repairFlag;
    }

    public void setRepairFlag(Boolean repairFlag) {
        this.repairFlag = repairFlag;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = StringUtils.trimToNull(parentCode);
    }

    public String getCriticalId() {
        return criticalId;
    }

    public void setCriticalId(String criticalId) {
        this.criticalId = StringUtils.trimToNull(criticalId);
    }

    public Boolean getPrescriptionFlag() {
        return prescriptionFlag;
    }

    public void setPrescriptionFlag(Boolean prescriptionFlag) {
        this.prescriptionFlag = prescriptionFlag;
    }

    public String getPassValue() {
        return passValue;
    }

    public void setPassValue(String passValue) {
        this.passValue = StringUtils.trimToNull(passValue);
    }

    public String getThirdFlag() {
        return thirdFlag;
    }

    public void setThirdFlag(String thirdFlag) {
        this.thirdFlag = StringUtils.trimToNull(thirdFlag);
    }

    public String getReceiveOrgCode() {
        return receiveOrgCode;
    }

    public void setReceiveOrgCode(String receiveOrgCode) {
        this.receiveOrgCode = StringUtils.trimToNull(receiveOrgCode);
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = StringUtils.trimToNull(receiveOrgName);
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = StringUtils.trimToNull(usage);
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = StringUtils.trimToNull(usageName);
    }

    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }

    public Boolean getSkinFlag() {
        return skinFlag;
    }

    public void setSkinFlag(Boolean skinFlag) {
        this.skinFlag = skinFlag;
    }

    public SkinTypeEnum getSkinType() {
        return skinType;
    }

    public void setSkinType(SkinTypeEnum skinType) {
        this.skinType = skinType;
    }

    @NotBlank(message = "创建医生不能为空！")
    @Size(max = 64, message = "创建医生长度不能超过64个字符！")
    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = StringUtils.trimToNull(createdStaff);
    }

    @Size(max = 64, message = "创建的创建医生姓名长度不能超过64个字符！")
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = StringUtils.trimToNull(createdStaffName);
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getCommitDate() {
        return commitDate;
    }

    public void setCommitDate(LocalDateTime commitDate) {
        this.commitDate = commitDate;
    }

    public String getSubmitStaffId() {
        return submitStaffId;
    }

    public void setSubmitStaffId(String submitStaffId) {
        this.submitStaffId = StringUtils.trimToNull(submitStaffId);
    }

    public String getSubmitStaffName() {
        return submitStaffName;
    }

    public void setSubmitStaffName(String submitStaffName) {
        this.submitStaffName = StringUtils.trimToNull(submitStaffName);
    }

    public String getProofStaff() {
        return proofStaff;
    }

    public void setProofStaff(String proofStaff) {
        this.proofStaff = StringUtils.trimToNull(proofStaff);
    }

    public String getProofStaffName() {
        return proofStaffName;
    }

    public void setProofStaffName(String proofStaffName) {
        this.proofStaffName = StringUtils.trimToNull(proofStaffName);
    }

    public Boolean getLimitConformFlag() {
        return limitConformFlag;
    }

    public void setLimitConformFlag(Boolean limitConformFlag) {
        this.limitConformFlag = limitConformFlag;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = StringUtils.trimToNull(hospitalCode);
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = StringUtils.trimToNull(hospitalName);
    }

    public String getCancelStaff() {
        return cancelStaff;
    }

    public void setCancelStaff(String cancelStaff) {
        this.cancelStaff = StringUtils.trimToNull(cancelStaff);
    }

    public String getCancelStaffName() {
        return cancelStaffName;
    }

    public void setCancelStaffName(String cancelStaffName) {
        this.cancelStaffName = StringUtils.trimToNull(cancelStaffName);
    }

    public LocalDateTime getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(LocalDateTime cancelDate) {
        this.cancelDate = cancelDate;
    }

    public String getCancelRemark() {
        return cancelRemark;
    }

    public void setCancelRemark(String cancelRemark) {
        this.cancelRemark = StringUtils.trimToNull(cancelRemark);
    }

    public String getReMark() {
        return reMark;
    }

    public void setReMark(String reMark) {
        this.reMark = StringUtils.trimToNull(reMark);
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public String getMinimal_class() {
        return "." + this.getClass().getSimpleName();
    }
}