package com.bjgoodwill.hip.ds.cis.cdr.order.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "Cdr的医嘱表")
public class CisCdrOrderQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6744284067936660128L;


    @Schema(description = "分类，0门诊，1临时 2 住院")
    private String orderType;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "患者主索引")
    private String patMiCode;
    @Schema(description = "医嘱类型")
    private SystemTypeEnum orderClass;
    @Schema(description = "查询开始日期")
    private LocalDateTime startDate;
    @Schema(description = "查询结束日期")
    private LocalDateTime endDate;

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }
}