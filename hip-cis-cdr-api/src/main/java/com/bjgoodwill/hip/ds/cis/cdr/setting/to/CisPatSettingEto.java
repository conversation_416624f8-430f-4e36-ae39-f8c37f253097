package com.bjgoodwill.hip.ds.cis.cdr.setting.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "患者视图过滤设置")
public class CisPatSettingEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -8442222463456112994L;

    @Schema(description = "数据内容，逗号分割")
    private String value;
    @Schema(description = "版本")
    private Integer version;

    @Size(max = 100, message = "数据内容，逗号分割长度不能超过100个字符！")
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = StringUtils.trimToNull(value);
    }

    @NotNull(message = "版本不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}