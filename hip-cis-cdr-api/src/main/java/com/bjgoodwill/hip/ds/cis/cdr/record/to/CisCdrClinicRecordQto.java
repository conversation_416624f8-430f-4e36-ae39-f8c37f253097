package com.bjgoodwill.hip.ds.cis.cdr.record.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "就诊记录")
public class CisCdrClinicRecordQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1227775862315658553L;

    @Schema(description = "主索引编码")
    private String patCode;

    @Schema(description = "就诊时间(开始)：取接诊时间/入科时间")
    private LocalDateTime startDate;

    @Schema(description = "就诊时间(截止)：取接诊时间/入科时间")
    private LocalDateTime endDate;

    @NotBlank(message = "主索引编码不能为空")
    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = patCode;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }
}