package com.bjgoodwill.hip.ds.cis.cdr.plan.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "医嘱执行单明细")
public class CisCdrOrderPlanDetailRecordNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -5123899009638131208L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "收费项目名称")
    private String priceItemName;

    @Schema(description = "每次剂量")
    private Double dosage;

    // 剂量单位名称 字典DosageUnit
    @Schema(description = "剂量单位名称 字典DosageUnit")
    private String dosageUnitName;

    @Schema(description = "包装总量")
    private Double packageNum;

    @Schema(description = "包装单位名称 MinUnit/PackageUnit")
    private String packageUnitName;

    @Schema(description = "执行科室")
    private String executeOrg;
    @Schema(description = "数据抽取时间")
    private LocalDateTime createdDate;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "流水号不能为空！")
    @Size(max = 32, message = "流水号长度不能超过32个字符！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @Size(max = 32, message = "收费项目名称长度不能超过32个字符！")
    public String getPriceItemName() {
        return priceItemName;
    }

    public void setPriceItemName(String priceItemName) {
        this.priceItemName = StringUtils.trimToNull(priceItemName);
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    public Double getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    public String getPackageUnitName() {
        return packageUnitName;
    }

    public void setPackageUnitName(String packageUnitName) {
        this.packageUnitName = packageUnitName;
    }

    @Size(max = 32, message = "执行科室长度不能超过32个字符！")
    public String getExecuteOrg() {
        return executeOrg;
    }

    public void setExecuteOrg(String executeOrg) {
        this.executeOrg = StringUtils.trimToNull(executeOrg);
    }

    @NotNull(message = "数据抽取时间不能为空！")
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
}