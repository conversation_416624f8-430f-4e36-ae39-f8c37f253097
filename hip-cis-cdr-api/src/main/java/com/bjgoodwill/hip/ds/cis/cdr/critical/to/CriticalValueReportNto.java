package com.bjgoodwill.hip.ds.cis.cdr.critical.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "危急值报告")
public class CriticalValueReportNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -8528808694010006468L;

    // 标识
    @Schema(description = "标识ID")
    private String id;
    @Schema(description = "患者类型")
    private VisitTypeEnum visitType;
    @Schema(description = "住院号")
    private String inpatientCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "护理组编码")
    private String inDeptCode;
    @Schema(description = "开单科室编码")
    private String visitOrgCode;
    @Schema(description = "开单医生编码")
    private String doctCode;
    @Schema(description = "申请单号")
    private String applyCode;
    @Schema(description = "服务项目名称")
    private String serviceItemCode;
    @Schema(description = "服务项目名称")
    private String serviceItemName;
    @Schema(description = "危急值上报人")
    private String reportUser;
    @Schema(description = "危急值上报日期")
    private LocalDateTime reportDate;
    @Schema(description = "危急值上报科室")
    private String reportOrgCode;
    @Schema(description = "危急值描述")
    private String criticalValue;
    @Schema(description = "医技电话发起人")
    private String telResponseUser;
    @Schema(description = "医技电话发起时间")
    private LocalDateTime telResponseDate;
    @Schema(description = "医技补充说明")
    private String telResponseRemark;
    @Schema(description = "医技电话确认标识")
    private Boolean telReponseFlag;
    @Schema(description = "危急值医嘱Id")
    private String orderId;
    @Schema(description = "病程记录")
    private String courseRecord;
    @Schema(description = "病程记录人")
    private String courseRecordUser;
    @Schema(description = "病程记录时间")
    private LocalDateTime courseRecordDate;
    @Schema(description = "答复状态（未答复、已答复）")
    private Boolean statusCode;
    @Schema(description = "医嘱开立时间")
    private LocalDateTime orderCreatedDate;
    @Schema(description = "采样日期")
    private LocalDateTime samplingDate;
    @Schema(description = "调用方标识")
    private String call;
    @Schema(description = "调用Url")
    private String callUrl;
    @Schema(description = "调用方唯一标识")
    private String crisisId;
    @Schema(description = "上报人姓名")
    private String reportUserName;
    @Schema(description = "上报科室名称")
    private String reportOrgName;

    @NotBlank(message = "标识ID不能为空")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = StringUtils.trimToNull(inpatientCode);
    }

    @NotBlank(message = "就诊流水号不能为空")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    public String getInDeptCode() {
        return inDeptCode;
    }

    public void setInDeptCode(String inDeptCode) {
        this.inDeptCode = StringUtils.trimToNull(inDeptCode);
    }

    public String getVisitOrgCode() {
        return visitOrgCode;
    }

    public void setVisitOrgCode(String visitOrgCode) {
        this.visitOrgCode = StringUtils.trimToNull(visitOrgCode);
    }

    public String getDoctCode() {
        return doctCode;
    }

    public void setDoctCode(String doctCode) {
        this.doctCode = StringUtils.trimToNull(doctCode);
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = StringUtils.trimToNull(applyCode);
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = StringUtils.trimToNull(serviceItemCode);
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = StringUtils.trimToNull(serviceItemName);
    }

    @NotBlank(message = "上报人不能为空")
    public String getReportUser() {
        return reportUser;
    }

    public void setReportUser(String reportUser) {
        this.reportUser = StringUtils.trimToNull(reportUser);
    }

    public LocalDateTime getReportDate() {
        return reportDate;
    }

    public void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    @NotBlank(message = "上报科室不能为空")
    public String getReportOrgCode() {
        return reportOrgCode;
    }

    public void setReportOrgCode(String reportOrgCode) {
        this.reportOrgCode = StringUtils.trimToNull(reportOrgCode);
    }

    public String getCriticalValue() {
        return criticalValue;
    }

    public void setCriticalValue(String criticalValue) {
        this.criticalValue = StringUtils.trimToNull(criticalValue);
    }

    public String getTelResponseUser() {
        return telResponseUser;
    }

    public void setTelResponseUser(String telResponseUser) {
        this.telResponseUser = StringUtils.trimToNull(telResponseUser);
    }

    public LocalDateTime getTelResponseDate() {
        return telResponseDate;
    }

    public void setTelResponseDate(LocalDateTime telResponseDate) {
        this.telResponseDate = telResponseDate;
    }

    public String getTelResponseRemark() {
        return telResponseRemark;
    }

    public void setTelResponseRemark(String telResponseRemark) {
        this.telResponseRemark = StringUtils.trimToNull(telResponseRemark);
    }

    public Boolean getTelReponseFlag() {
        return telReponseFlag;
    }

    public void setTelReponseFlag(Boolean telReponseFlag) {
        this.telReponseFlag = telReponseFlag;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = StringUtils.trimToNull(orderId);
    }

    public String getCourseRecord() {
        return courseRecord;
    }

    public void setCourseRecord(String courseRecord) {
        this.courseRecord = StringUtils.trimToNull(courseRecord);
    }

    public String getCourseRecordUser() {
        return courseRecordUser;
    }

    public void setCourseRecordUser(String courseRecordUser) {
        this.courseRecordUser = StringUtils.trimToNull(courseRecordUser);
    }

    public LocalDateTime getCourseRecordDate() {
        return courseRecordDate;
    }

    public void setCourseRecordDate(LocalDateTime courseRecordDate) {
        this.courseRecordDate = courseRecordDate;
    }

    public Boolean getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Boolean statusCode) {
        this.statusCode = statusCode;
    }

    public LocalDateTime getOrderCreatedDate() {
        return orderCreatedDate;
    }

    public void setOrderCreatedDate(LocalDateTime orderCreatedDate) {
        this.orderCreatedDate = orderCreatedDate;
    }

    public LocalDateTime getSamplingDate() {
        return samplingDate;
    }

    public void setSamplingDate(LocalDateTime samplingDate) {
        this.samplingDate = samplingDate;
    }

    public String getCall() {
        return call;
    }

    public void setCall(String call) {
        this.call = StringUtils.trimToNull(call);
    }

    public String getCallUrl() {
        return callUrl;
    }

    public void setCallUrl(String callUrl) {
        this.callUrl = StringUtils.trimToNull(callUrl);
    }

    public String getCrisisId() {
        return crisisId;
    }

    public void setCrisisId(String crisisId) {
        this.crisisId = StringUtils.trimToNull(crisisId);
    }

    @NotBlank(message = "上报人姓名不能为空")
    public String getReportUserName() {
        return reportUserName;
    }

    public void setReportUserName(String reportUserName) {
        this.reportUserName = reportUserName;
    }

    @NotBlank(message = "上报科室不能为空")
    public String getReportOrgName() {
        return reportOrgName;
    }

    public void setReportOrgName(String reportOrgName) {
        this.reportOrgName = reportOrgName;
    }
}