package com.bjgoodwill.hip.ds.cis.cdr.record.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

//@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(description = "就诊记录")
public abstract class CisCdrClinicRecordNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -5360576545649271693L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "主索引编码")
    private String patCode;
    @Schema(description = "患者姓名")
    private String name;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "证件类型")
    private String cardType;
    @Schema(description = "证件号")
    private String cardCode;
    @Schema(description = "民族")
    private String nation;
    @Schema(description = "国籍")
    private String nationality;
    @Schema(description = "电话")
    private String tel;
    @Schema(description = "籍贯")
    private String nativePlace;
    @Schema(description = "现住址")
    private String liveAddr;
    @Schema(description = "详细现住址")
    private String liveExactAddr;
    @Schema(description = "就诊时间：取接诊时间/入科时间")
    private LocalDateTime clinicDate;
    @Schema(description = "入院科室/取挂号科室")
    private String clinicDept;
    @Schema(description = "就诊医生/住院医生")
    private String clinicStaff;
    @Schema(description = "医生姓名")
    private String clinicStaffName;
    @Schema(description = "抽取数据时间")
    private LocalDateTime createDate;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "就诊流水号不能为空！")
    @Size(max = 32, message = "就诊流水号长度不能超过32个字符！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @NotBlank(message = "主索引编码不能为空！")
    @Size(max = 32, message = "主索引编码长度不能超过32个字符！")
    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = StringUtils.trimToNull(patCode);
    }

    @Size(max = 32, message = "患者姓名长度不能超过32个字符！")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimToNull(name);
    }

    @Size(max = 16, message = "性别长度不能超过16个字符！")
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = StringUtils.trimToNull(sex);
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    @Size(max = 16, message = "证件类型长度不能超过16个字符！")
    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = StringUtils.trimToNull(cardType);
    }

    @Size(max = 32, message = "证件号长度不能超过32个字符！")
    public String getCardCode() {
        return cardCode;
    }

    public void setCardCode(String cardCode) {
        this.cardCode = StringUtils.trimToNull(cardCode);
    }

    @Size(max = 16, message = "民族长度不能超过16个字符！")
    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = StringUtils.trimToNull(nation);
    }

    @Size(max = 16, message = "国籍长度不能超过16个字符！")
    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = StringUtils.trimToNull(nationality);
    }

    @Size(max = 16, message = "电话长度不能超过16个字符！")
    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = StringUtils.trimToNull(tel);
    }

    @Size(max = 32, message = "籍贯长度不能超过32个字符！")
    public String getNativePlace() {
        return nativePlace;
    }

    public void setNativePlace(String nativePlace) {
        this.nativePlace = StringUtils.trimToNull(nativePlace);
    }

    @Size(max = 255, message = "现住址长度不能超过255个字符！")
    public String getLiveAddr() {
        return liveAddr;
    }

    public void setLiveAddr(String liveAddr) {
        this.liveAddr = StringUtils.trimToNull(liveAddr);
    }

    @Size(max = 255, message = "详细现住址长度不能超过255个字符！")
    public String getLiveExactAddr() {
        return liveExactAddr;
    }

    public void setLiveExactAddr(String liveExactAddr) {
        this.liveExactAddr = StringUtils.trimToNull(liveExactAddr);
    }

    public LocalDateTime getClinicDate() {
        return clinicDate;
    }

    public void setClinicDate(LocalDateTime clinicDate) {
        this.clinicDate = clinicDate;
    }

    @Size(max = 64, message = "入院科室/取挂号科室长度不能超过64个字符！")
    public String getClinicDept() {
        return clinicDept;
    }

    public void setClinicDept(String clinicDept) {
        this.clinicDept = StringUtils.trimToNull(clinicDept);
    }

    @Size(max = 32, message = "就诊医生/住院医生长度不能超过32个字符！")
    public String getClinicStaff() {
        return clinicStaff;
    }

    public void setClinicStaff(String clinicStaff) {
        this.clinicStaff = StringUtils.trimToNull(clinicStaff);
    }

    @Size(max = 32, message = "医生姓名长度不能超过32个字符！")
    public String getClinicStaffName() {
        return clinicStaffName;
    }

    public void setClinicStaffName(String clinicStaffName) {
        this.clinicStaffName = StringUtils.trimToNull(clinicStaffName);
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

//    public String getMinimal_class() {
//        return "." + this.getClass().getSimpleName();
//    }
    @JsonProperty("@class")
    public String getClassName() {
        return getClass().getName();
    }
}