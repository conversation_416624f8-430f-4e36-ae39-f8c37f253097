package com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "抗菌药皮试药执行记录")
public class CisAntimicrobialsSkinExecReportQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -583623358046508213L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "患者类型")
    private VisitTypeEnum visitType;
    @Schema(description = "系统类型")
    private SystemTypeEnum systemType;
    @Schema(description = "服务项目编码")
    private String serviceItemCode;
    @Schema(description = "服务项目名称")
    private String serviceItemName;
    @Schema(description = "医嘱号")
    private String orderId;
    @Schema(description = "申请单号")
    private String applyId;
    @Schema(description = "执行记录id")
    private String execPlanId;
    @Schema(description = "执行时间")
    private LocalDateTime execPlanDate;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getExecPlanId() {
        return execPlanId;
    }

    public void setExecPlanId(String execPlanId) {
        this.execPlanId = execPlanId;
    }

    public LocalDateTime getExecPlanDate() {
        return execPlanDate;
    }

    public void setExecPlanDate(LocalDateTime execPlanDate) {
        this.execPlanDate = execPlanDate;
    }
}