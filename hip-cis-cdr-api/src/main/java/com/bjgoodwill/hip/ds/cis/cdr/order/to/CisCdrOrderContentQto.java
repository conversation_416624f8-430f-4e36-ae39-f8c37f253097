package com.bjgoodwill.hip.ds.cis.cdr.order.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医嘱内容")
public class CisCdrOrderContentQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -5621530621249783415L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "医嘱id")
    private String orderId;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}