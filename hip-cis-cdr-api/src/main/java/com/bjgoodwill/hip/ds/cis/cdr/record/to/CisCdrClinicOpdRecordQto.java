package com.bjgoodwill.hip.ds.cis.cdr.record.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "门诊就诊记录")
public class CisCdrClinicOpdRecordQto extends CisCdrClinicRecordQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2758147969986535891L;

    @Schema(description = "急诊标记")
    private Boolean emrFlag;


    public Boolean getEmrFlag() {
        return emrFlag;
    }

    public void setEmrFlag(Boolean emrFlag) {
        this.emrFlag = emrFlag;
    }
}