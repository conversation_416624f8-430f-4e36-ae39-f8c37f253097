package com.bjgoodwill.hip.ds.cis.cdr.orderlog.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "住院医嘱闭环日志")
public class CisIpdOrderLcLogNto extends CisOrderClLogNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2534626436790745713L;

    @Schema(description = "住院号")
    private String patCode;
    @Schema(description = "执行单id")
    private String orderSplitId;

    @Size(max = 50, message = "住院号长度不能超过50个字符！")
    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = StringUtils.trimToNull(patCode);
    }

    @Size(max = 50, message = "执行单id长度不能超过50个字符！")
    public String getOrderSplitId() {
        return orderSplitId;
    }

    public void setOrderSplitId(String orderSplitId) {
        this.orderSplitId = StringUtils.trimToNull(orderSplitId);
    }
}