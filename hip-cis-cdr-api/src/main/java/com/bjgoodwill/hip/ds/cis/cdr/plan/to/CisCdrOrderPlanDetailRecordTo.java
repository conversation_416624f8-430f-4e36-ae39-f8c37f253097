package com.bjgoodwill.hip.ds.cis.cdr.plan.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "医嘱执行单明细")
public class CisCdrOrderPlanDetailRecordTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1498803354367057133L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "执行单id")
    private String orderPlanId;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "每次剂量")
    private Double dosage;

    // 剂量单位名称 字典DosageUnit
    @Schema(description = "剂量单位名称 字典DosageUnit")
    private String dosageUnitName;

    @Schema(description = "包装总量")
    private Double packageNum;

    @Schema(description = "包装单位名称 MinUnit/PackageUnit")
    private String packageUnitName;
    @Schema(description = "收费项目名称")
    private String priceItemName;
    @Schema(description = "执行科室")
    private String executeOrg;
    @Schema(description = "数据抽取时间")
    private LocalDateTime createdDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderPlanId() {
        return orderPlanId;
    }

    public void setOrderPlanId(String orderPlanId) {
        this.orderPlanId = orderPlanId;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPriceItemName() {
        return priceItemName;
    }

    public void setPriceItemName(String priceItemName) {
        this.priceItemName = priceItemName;
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    public Double getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    public String getPackageUnitName() {
        return packageUnitName;
    }

    public void setPackageUnitName(String packageUnitName) {
        this.packageUnitName = packageUnitName;
    }

    public String getExecuteOrg() {
        return executeOrg;
    }

    public void setExecuteOrg(String executeOrg) {
        this.executeOrg = executeOrg;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisCdrOrderPlanDetailRecordTo other = (CisCdrOrderPlanDetailRecordTo) obj;
        return Objects.equals(id, other.id);
    }
}