package com.bjgoodwill.hip.ds.cis.cdr.setting.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "患者视图过滤设置")
public class CisPatSettingNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2333820639918758581L;

    @Schema(description = "医嘱类型")
    private SystemTypeEnum orderClass;
    @Schema(description = "数据内容，逗号分割")
    private String value;
    @Schema(description = "医生编码")
    private String docCode;
    @Schema(description = "工作组编码")
    private String orgCode;

    @NotNull(message = "医嘱类型不能为空！")
    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    @Size(max = 100, message = "数据内容，逗号分割长度不能超过100个字符！")
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = StringUtils.trimToNull(value);
    }

    @Size(max = 50, message = "医生编码长度不能超过50个字符！")
    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = StringUtils.trimToNull(docCode);
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = StringUtils.trimToNull(orgCode);
    }
}