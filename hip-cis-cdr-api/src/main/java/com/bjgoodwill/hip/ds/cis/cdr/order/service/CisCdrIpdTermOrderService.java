package com.bjgoodwill.hip.ds.cis.cdr.order.service;

import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrIpdTermOrderEto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrIpdTermOrderNto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrIpdTermOrderTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Tag(name = "住院临时医嘱领域服务", description = "住院临时医嘱领域服务")
public interface CisCdrIpdTermOrderService {

//    @Operation(summary = "根据查询条件对住院临时医嘱进行查询。")
//    @GetMapping("/cisCdrIpdTermOrders")
//    List<CisCdrIpdTermOrderTo> getCisCdrIpdTermOrders(@ParameterObject @SpringQueryMap CisCdrIpdTermOrderQto cisCdrIpdTermOrderQto);
//
//    @Operation(summary = "根据查询条件对住院临时医嘱进行分页查询。")
//    @GetMapping("/cisCdrIpdTermOrders/pages")
//    GridResultSet<CisCdrIpdTermOrderTo> getCisCdrIpdTermOrderPage(@ParameterObject @SpringQueryMap CisCdrIpdTermOrderQto cisCdrIpdTermOrderQto);

    @Operation(summary = "创建住院临时医嘱。")
    @PostMapping("/cisCdrIpdTermOrders")
    CisCdrIpdTermOrderTo createCisCdrIpdTermOrder(@RequestBody @Valid CisCdrIpdTermOrderNto cisCdrIpdTermOrderNto);

    @Operation(summary = "根据唯一标识修改住院临时医嘱。")
    @PutMapping("/cisCdrIpdTermOrders/{id:.+}")
    void updateCisCdrIpdTermOrder(@PathVariable("id") String id, @RequestBody @Valid CisCdrIpdTermOrderEto cisCdrIpdTermOrderEto);

}