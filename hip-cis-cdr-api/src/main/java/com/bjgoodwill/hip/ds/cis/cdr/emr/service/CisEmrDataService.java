package com.bjgoodwill.hip.ds.cis.cdr.emr.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.emr.to.CisEmrDataEto;
import com.bjgoodwill.hip.ds.cis.cdr.emr.to.CisEmrDataNto;
import com.bjgoodwill.hip.ds.cis.cdr.emr.to.CisEmrDataQto;
import com.bjgoodwill.hip.ds.cis.cdr.emr.to.CisEmrDataTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "病历信息领域服务", description = "病历信息领域服务")
public interface CisEmrDataService {

    @Operation(summary = "P0根据查询条件对病历信息进行查询。")
    @GetMapping("/cisEmrDatas")
    List<CisEmrDataTo> getCisEmrDatas(@ParameterObject @SpringQueryMap CisEmrDataQto cisEmrDataQto);

    @Operation(summary = "根据查询条件对病历信息进行分页查询。")
    @GetMapping("/cisEmrDatas/pages")
    GridResultSet<CisEmrDataTo> getCisEmrDataPage(@ParameterObject @SpringQueryMap CisEmrDataQto cisEmrDataQto);

    @Operation(summary = "根据唯一标识返回病历信息。")
    @GetMapping("/cisEmrDatas/{id:.+}")
    CisEmrDataTo getCisEmrDataById(@PathVariable("id") String id);

    @Operation(summary = "创建病历信息。")
    @PostMapping("/cisEmrDatas")
    CisEmrDataTo createCisEmrData(@RequestBody @Valid CisEmrDataNto cisEmrDataNto);

    @Operation(summary = "根据唯一标识修改病历信息。")
    @PutMapping("/cisEmrDatas/{id:.+}")
    void updateCisEmrData(@PathVariable("id") String id, @RequestBody @Valid CisEmrDataEto cisEmrDataEto);

    @Operation(summary = "根据唯一标识删除病历信息。")
    @DeleteMapping("/cisEmrDatas/{id:.+}")
    void deleteCisEmrData(@PathVariable("id") String id);

}