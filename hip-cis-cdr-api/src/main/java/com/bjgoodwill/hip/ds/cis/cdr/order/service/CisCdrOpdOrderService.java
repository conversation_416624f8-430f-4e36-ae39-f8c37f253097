package com.bjgoodwill.hip.ds.cis.cdr.order.service;

import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOpdOrderEto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOpdOrderNto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOpdOrderTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Tag(name = "临时医嘱领域服务", description = "临时医嘱领域服务")
public interface CisCdrOpdOrderService {

//    @Operation(summary = "根据查询条件对临时医嘱进行查询。")
//    @GetMapping("/cisCdrOpdOrders")
//    List<CisCdrOpdOrderTo> getCisCdrOpdOrders(@ParameterObject @SpringQueryMap CisCdrOpdOrderQto cisCdrOpdOrderQto);
//
//    @Operation(summary = "根据查询条件对临时医嘱进行分页查询。")
//    @GetMapping("/cisCdrOpdOrders/pages")
//    GridResultSet<CisCdrOpdOrderTo> getCisCdrOpdOrderPage(@ParameterObject @SpringQueryMap CisCdrOpdOrderQto cisCdrOpdOrderQto);

    @Operation(summary = "创建临时医嘱。")
    @PostMapping("/cisCdrOpdOrders")
    CisCdrOpdOrderTo createCisCdrOpdOrder(@RequestBody @Valid CisCdrOpdOrderNto cisCdrOpdOrderNto);

    @Operation(summary = "根据唯一标识修改临时医嘱。")
    @PutMapping("/cisCdrOpdOrders/{id:.+}")
    void updateCisCdrOpdOrder(@PathVariable("id") String id, @RequestBody @Valid CisCdrOpdOrderEto cisCdrOpdOrderEto);

}