package com.bjgoodwill.hip.ds.cis.cdr.record.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicRecordEto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicRecordNto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicRecordQto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicRecordTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "就诊记录领域服务", description = "就诊记录领域服务")
public interface CisCdrClinicRecordService {

    @Operation(summary = "根据查询条件对就诊记录进行查询。")
    @GetMapping("/cisCdrClinicRecords")
    List<CisCdrClinicRecordTo> getCisCdrClinicRecords(@ParameterObject @SpringQueryMap CisCdrClinicRecordQto cisCdrClinicRecordQto);

    @Operation(summary = "根据查询条件对就诊记录进行分页查询。")
    @GetMapping("/cisCdrClinicRecords/pages")
    GridResultSet<CisCdrClinicRecordTo> getCisCdrClinicRecordPage(@ParameterObject @SpringQueryMap CisCdrClinicRecordQto cisCdrClinicRecordQto);

    @Operation(summary = "创建就诊记录。")
    @PostMapping("/cisCdrClinicRecords")
    CisCdrClinicRecordTo createCisCdrClinicRecord(@RequestBody @Valid CisCdrClinicRecordNto cisCdrClinicRecordNto);

    @Operation(summary = "根据唯一标识修改就诊记录。")
    @PutMapping("/cisCdrClinicRecords/{id:.+}")
    void updateCisCdrClinicRecord(@PathVariable("id") String id, @RequestBody @Valid CisCdrClinicRecordEto cisCdrClinicRecordEto);

    @Operation(summary = "根据唯一标识删除就诊记录。")
    @DeleteMapping("/cisCdrClinicRecords/{id:.+}")
    void deleteCisCdrClinicRecord(@PathVariable("id") String id);

}