package com.bjgoodwill.hip.ds.cis.cdr.report.to;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "检验报告明细")
public class CisReportResultSpcobsDetailNto extends CisReportResultDetailNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -3435674891973183492L;

    @Schema(description = "标本编码（试管号）")
    private String sampleCode;
    @Schema(description = "标本类型")
    private String sampleType;
    @Schema(description = "检验项目编码")
    private String itemCode;
    @Schema(description = "检验项目名称")
    private String itemName;
    @Schema(description = "结果低值（单值结果）")
    private String resultLow;
    @Schema(description = "结果高值")
    private String resultHigh;
    @Schema(description = "单位编码")
    private String unitCode;
    @Schema(description = "单位名称")
    private String unitName;
    @Schema(description = "参考范围低值")
    private String referenceValueLow;
    @Schema(description = "参考范围高值")
    private String referenceValueHigh;
    @Schema(description = "危机值下限")
    private String crisisLowLimit;
    @Schema(description = "危机值上限")
    private String crisisHighLimit;

    public String getSampleCode() {
        return sampleCode;
    }

    public void setSampleCode(String sampleCode) {
        this.sampleCode = StringUtils.trimToNull(sampleCode);
    }

    public String getSampleType() {
        return sampleType;
    }

    public void setSampleType(String sampleType) {
        this.sampleType = StringUtils.trimToNull(sampleType);
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = StringUtils.trimToNull(itemCode);
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = StringUtils.trimToNull(itemName);
    }

    public String getResultLow() {
        return resultLow;
    }

    public void setResultLow(String resultLow) {
        this.resultLow = StringUtils.trimToNull(resultLow);
    }

    public String getResultHigh() {
        return resultHigh;
    }

    public void setResultHigh(String resultHigh) {
        this.resultHigh = StringUtils.trimToNull(resultHigh);
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = StringUtils.trimToNull(unitCode);
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = StringUtils.trimToNull(unitName);
    }

    public String getReferenceValueLow() {
        return referenceValueLow;
    }

    public void setReferenceValueLow(String referenceValueLow) {
        this.referenceValueLow = StringUtils.trimToNull(referenceValueLow);
    }

    public String getReferenceValueHigh() {
        return referenceValueHigh;
    }

    public void setReferenceValueHigh(String referenceValueHigh) {
        this.referenceValueHigh = StringUtils.trimToNull(referenceValueHigh);
    }

    public String getCrisisLowLimit() {
        return crisisLowLimit;
    }

    public void setCrisisLowLimit(String crisisLowLimit) {
        this.crisisLowLimit = StringUtils.trimToNull(crisisLowLimit);
    }

    public String getCrisisHighLimit() {
        return crisisHighLimit;
    }

    public void setCrisisHighLimit(String crisisHighLimit) {
        this.crisisHighLimit = StringUtils.trimToNull(crisisHighLimit);
    }
}