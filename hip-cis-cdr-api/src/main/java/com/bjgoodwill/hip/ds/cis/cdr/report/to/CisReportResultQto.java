package com.bjgoodwill.hip.ds.cis.cdr.report.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "报告结果")
public class CisReportResultQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -8591449927490421544L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "医嘱id")
    private String orderID;
    @Schema(description = "申请单ID")
    private String applyId;
    @Schema(description = "存储其他系统报告主键，用来取消其他系统报告")
    private String reportId;
    @Schema(description = "主索引编码")
    private String patMiCode;
    @Schema(description = "接诊流水编码")
    private String visitCode;
    @Schema(description = "结果类型 SPCOBS/DGIMG")
    private SystemTypeEnum reportType;
    @Schema(description = "诊疗服务项目")
    private String serviceItemCode;
    @Schema(description = "执行科室代码")
    private String execOrgCode;
    @Schema(description = "reportDate")
    private LocalDateTime reportDate;
    @Schema(description = "执行人编码")
    private String execStaff;
    @Schema(description = "执行人")
    private String execStaffName;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getOrderID() {
        return orderID;
    }

    public void setOrderID(String orderID) {
        this.orderID = orderID;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public SystemTypeEnum getReportType() {
        return reportType;
    }

    public void setReportType(SystemTypeEnum reportType) {
        this.reportType = reportType;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getExecOrgCode() {
        return execOrgCode;
    }

    public void setExecOrgCode(String execOrgCode) {
        this.execOrgCode = execOrgCode;
    }

    public LocalDateTime getReportDate() {
        return reportDate;
    }

    public void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    public String getExecStaff() {
        return execStaff;
    }

    public void setExecStaff(String execStaff) {
        this.execStaff = execStaff;
    }

    public String getExecStaffName() {
        return execStaffName;
    }

    public void setExecStaffName(String execStaffName) {
        this.execStaffName = execStaffName;
    }
}