package com.bjgoodwill.hip.ds.cis.cdr.orderlog.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "住院医嘱闭环日志")
public class CisIpdOrderLcLogQto extends CisOrderClLogQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -7124008589616848084L;

    @Schema(description = "住院号")
    private String patCode;


    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = patCode;
    }
}