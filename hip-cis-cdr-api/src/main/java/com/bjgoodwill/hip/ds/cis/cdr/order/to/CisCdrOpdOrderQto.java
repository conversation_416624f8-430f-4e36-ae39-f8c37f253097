package com.bjgoodwill.hip.ds.cis.cdr.order.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "临时医嘱")
public class CisCdrOpdOrderQto extends CisCdrOrderQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4053040835765924327L;

    @Schema(description = "处方号")
    private String prescriptionCode;


    public String getPrescriptionCode() {
        return prescriptionCode;
    }

    public void setPrescriptionCode(String prescriptionCode) {
        this.prescriptionCode = prescriptionCode;
    }
}