package com.bjgoodwill.hip.ds.cis.cdr.report.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "报告结果")
public class CisReportResultEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4797771245526526796L;

    @Schema(description = "reportDate")
    private LocalDateTime reportDate;
    @Schema(description = "执行人")
    private String execStaffName;

    public LocalDateTime getReportDate() {
        return reportDate;
    }

    public void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    public String getExecStaffName() {
        return execStaffName;
    }

    public void setExecStaffName(String execStaffName) {
        this.execStaffName = StringUtils.trimToNull(execStaffName);
    }
}