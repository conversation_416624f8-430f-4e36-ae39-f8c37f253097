package com.bjgoodwill.hip.ds.cis.cdr.critical.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "危急值报告")
public class CriticalValueReportEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -84574118651528498L;

    @Schema(description = "服务项目名称")
    private String serviceItemCode;
    @Schema(description = "服务项目名称")
    private String serviceItemName;
    @Schema(description = "危急值上报人")
    private String reportUser;
    @Schema(description = "危急值上报日期")
    private LocalDateTime reportDate;
    @Schema(description = "危急值上报科室")
    private String reportOrgCode;
    @Schema(description = "危急值描述")
    private String criticalValue;
    @Schema(description = "答复科室")
    private String responseOrgCode;
    @Schema(description = "答复科室名称")
    private String responseOrgName;
    @Schema(description = "临床答复人")
    private String responseUser;
    @Schema(description = "临床答复人名称")
    private String responseUserName;
    @Schema(description = "临床答复时间")
    private LocalDateTime responseDate;
    @Schema(description = "临床答复内容")
    private String responseValue;
    @Schema(description = "医技电话发起人")
    private String telResponseUser;
    @Schema(description = "医技电话发起时间")
    private LocalDateTime telResponseDate;
    @Schema(description = "医技补充说明")
    private String telResponseRemark;
    @Schema(description = "医技电话确认标识")
    private Boolean telReponseFlag;
    @Schema(description = "危急值医嘱Id")
    private String orderId;
    @Schema(description = "病程记录")
    private String courseRecord;
    @Schema(description = "病程记录人")
    private String courseRecordUser;
    @Schema(description = "病程记录时间")
    private LocalDateTime courseRecordDate;
    @Schema(description = "答复状态（未答复、已答复）")
    private Boolean statusCode;
    @Schema(description = "医嘱开立时间")
    private LocalDateTime orderCreatedDate;
    @Schema(description = "采样日期")
    private LocalDateTime samplingDate;
    @Schema(description = "调用方唯一标识")
    private String crisisId;

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = StringUtils.trimToNull(serviceItemCode);
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = StringUtils.trimToNull(serviceItemName);
    }

    public String getReportUser() {
        return reportUser;
    }

    public void setReportUser(String reportUser) {
        this.reportUser = StringUtils.trimToNull(reportUser);
    }

    public LocalDateTime getReportDate() {
        return reportDate;
    }

    public void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    public String getReportOrgCode() {
        return reportOrgCode;
    }

    public void setReportOrgCode(String reportOrgCode) {
        this.reportOrgCode = StringUtils.trimToNull(reportOrgCode);
    }

    public String getCriticalValue() {
        return criticalValue;
    }

    public void setCriticalValue(String criticalValue) {
        this.criticalValue = StringUtils.trimToNull(criticalValue);
    }

    public String getResponseOrgCode() {
        return responseOrgCode;
    }

    public void setResponseOrgCode(String responseOrgCode) {
        this.responseOrgCode = StringUtils.trimToNull(responseOrgCode);
    }

    public String getResponseUser() {
        return responseUser;
    }

    public void setResponseUser(String responseUser) {
        this.responseUser = StringUtils.trimToNull(responseUser);
    }

    public LocalDateTime getResponseDate() {
        return responseDate;
    }

    public void setResponseDate(LocalDateTime responseDate) {
        this.responseDate = responseDate;
    }

    public String getResponseValue() {
        return responseValue;
    }

    public void setResponseValue(String responseValue) {
        this.responseValue = StringUtils.trimToNull(responseValue);
    }

    public String getTelResponseUser() {
        return telResponseUser;
    }

    public void setTelResponseUser(String telResponseUser) {
        this.telResponseUser = StringUtils.trimToNull(telResponseUser);
    }

    public LocalDateTime getTelResponseDate() {
        return telResponseDate;
    }

    public void setTelResponseDate(LocalDateTime telResponseDate) {
        this.telResponseDate = telResponseDate;
    }

    public String getTelResponseRemark() {
        return telResponseRemark;
    }

    public void setTelResponseRemark(String telResponseRemark) {
        this.telResponseRemark = StringUtils.trimToNull(telResponseRemark);
    }

    public Boolean getTelReponseFlag() {
        return telReponseFlag;
    }

    public void setTelReponseFlag(Boolean telReponseFlag) {
        this.telReponseFlag = telReponseFlag;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = StringUtils.trimToNull(orderId);
    }

    public String getCourseRecord() {
        return courseRecord;
    }

    public void setCourseRecord(String courseRecord) {
        this.courseRecord = StringUtils.trimToNull(courseRecord);
    }

    public String getCourseRecordUser() {
        return courseRecordUser;
    }

    public void setCourseRecordUser(String courseRecordUser) {
        this.courseRecordUser = StringUtils.trimToNull(courseRecordUser);
    }

    public LocalDateTime getCourseRecordDate() {
        return courseRecordDate;
    }

    public void setCourseRecordDate(LocalDateTime courseRecordDate) {
        this.courseRecordDate = courseRecordDate;
    }

    public Boolean getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Boolean statusCode) {
        this.statusCode = statusCode;
    }

    public LocalDateTime getOrderCreatedDate() {
        return orderCreatedDate;
    }

    public void setOrderCreatedDate(LocalDateTime orderCreatedDate) {
        this.orderCreatedDate = orderCreatedDate;
    }

    public LocalDateTime getSamplingDate() {
        return samplingDate;
    }

    public void setSamplingDate(LocalDateTime samplingDate) {
        this.samplingDate = samplingDate;
    }

    public String getCrisisId() {
        return crisisId;
    }

    public void setCrisisId(String crisisId) {
        this.crisisId = StringUtils.trimToNull(crisisId);
    }

    public String getResponseOrgName() {
        return responseOrgName;
    }

    public void setResponseOrgName(String responseOrgName) {
        this.responseOrgName = responseOrgName;
    }

    public String getResponseUserName() {
        return responseUserName;
    }

    public void setResponseUserName(String responseUserName) {
        this.responseUserName = responseUserName;
    }
}