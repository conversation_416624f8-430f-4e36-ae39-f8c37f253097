package com.bjgoodwill.hip.ds.cis.cdr.record.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicOpdRecordEto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicOpdRecordNto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicOpdRecordQto;
import com.bjgoodwill.hip.ds.cis.cdr.record.to.CisCdrClinicOpdRecordTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "门诊就诊记录领域服务", description = "门诊就诊记录领域服务")
public interface CisCdrClinicOpdRecordService {

    @Operation(summary = "根据查询条件对门诊就诊记录进行查询。")
    @GetMapping("/cisCdrClinicOpdRecords")
    List<CisCdrClinicOpdRecordTo> getCisCdrClinicOpdRecords(@ParameterObject @SpringQueryMap CisCdrClinicOpdRecordQto cisCdrClinicOpdRecordQto);

    @Operation(summary = "根据查询条件对门诊就诊记录进行分页查询。")
    @GetMapping("/cisCdrClinicOpdRecords/pages")
    GridResultSet<CisCdrClinicOpdRecordTo> getCisCdrClinicOpdRecordPage(@ParameterObject @SpringQueryMap CisCdrClinicOpdRecordQto cisCdrClinicOpdRecordQto);

    @Operation(summary = "创建门诊就诊记录。")
    @PostMapping("/cisCdrClinicOpdRecords")
    CisCdrClinicOpdRecordTo createCisCdrClinicOpdRecord(@RequestBody @Valid CisCdrClinicOpdRecordNto cisCdrClinicOpdRecordNto);

    @Operation(summary = "根据唯一标识修改门诊就诊记录。")
    @PutMapping("/cisCdrClinicOpdRecords/{id:.+}")
    void updateCisCdrClinicOpdRecord(@PathVariable("id") String id, @RequestBody @Valid CisCdrClinicOpdRecordEto cisCdrClinicOpdRecordEto);

}