package com.bjgoodwill.hip.ds.cis.cdr.order.service;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderQto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "Cdr的医嘱表领域服务", description = "Cdr的医嘱表领域服务")
public interface CisCdrOrderService {

    @Operation(summary = "根据查询条件对Cdr的医嘱表进行查询。")
    @GetMapping("/cisCdrOrders")
    List<CisCdrOrderTo> getCisCdrOrders(@ParameterObject @SpringQueryMap CisCdrOrderQto cisCdrOrderQto);

    @Operation(summary = "根据查询条件对Cdr的医嘱表进行分页查询。")
    @GetMapping("/cisCdrOrders/pages")
    GridResultSet<CisCdrOrderTo> getCisCdrOrderPage(@ParameterObject @SpringQueryMap CisCdrOrderQto cisCdrOrderQto);

    @Operation(summary = "获取患者历史检验医嘱数据。")
    @GetMapping("/cisCdrOrders/patMiCode/{pat-mi-code:.+}/spcobs")
    List<CisCdrOrderTo> getSpcobsByPaMiCode(@PathVariable("pat-mi-code") String patMiCode);

    @Operation(summary = "获取患者历史检查医嘱数据。")
    @GetMapping("/cisCdrOrders/patMiCode/{pat-mi-code:.+}/dgimg")
    List<CisCdrOrderTo> getDgimgByPaMiCode(@PathVariable("pat-mi-code") String patMiCode);

    @Operation(summary = "获取患者就诊医嘱。")
    @GetMapping("/cisCdrOrders/visitCode/{visit-code:.+}")
    List<CisCdrOrderTo> getOrdersByVisitCode(@PathVariable("visit-code") String visitCode, SystemTypeEnum orderCLass);

    @Operation(summary = "P0根据创建时间查询CDR医嘱信息。")
    @GetMapping("/cisCdrOrders/findCisCdrOrderByCreateDate")
    List<CisCdrOrderTo> findCisCdrOrderByCreateDate(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDateTime dateTime,
                                                    @RequestParam(required = false) SystemTypeEnum orderCLass);
}