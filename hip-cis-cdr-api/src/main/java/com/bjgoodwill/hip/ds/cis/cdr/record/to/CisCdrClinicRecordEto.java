package com.bjgoodwill.hip.ds.cis.cdr.record.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

//@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(description = "就诊记录")
public abstract class CisCdrClinicRecordEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -3370967961165597817L;


    //    public String getMinimal_class() {
//        return "." + this.getClass().getSimpleName();
//    }
    @JsonProperty("@class")
    public String getClassName() {
        return getClass().getName();
    }
}