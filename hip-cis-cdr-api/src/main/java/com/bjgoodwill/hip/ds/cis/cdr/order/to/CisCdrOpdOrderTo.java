package com.bjgoodwill.hip.ds.cis.cdr.order.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "临时医嘱")
public class CisCdrOpdOrderTo extends CisCdrOrderTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -7647005076689498053L;

    @Schema(description = "处方号")
    private String prescriptionCode;

    public String getPrescriptionCode() {
        return prescriptionCode;
    }

    public void setPrescriptionCode(String prescriptionCode) {
        this.prescriptionCode = prescriptionCode;
    }

}