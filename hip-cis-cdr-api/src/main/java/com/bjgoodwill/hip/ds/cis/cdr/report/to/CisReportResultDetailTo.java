package com.bjgoodwill.hip.ds.cis.cdr.report.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.ReportResultFlagEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

//@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(description = "医嘱报告明细")
public class CisReportResultDetailTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -6745906921543026182L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "报告结果标识")
    private String cisReportResultId;
    @Schema(description = "报告明细编码,存储其他系统报告明细主键，用来取消其他系统报告")
    private String reportDetailCode;
    @Schema(description = "报告内容")
    private String reportContent;
    @Schema(description = "标本编码（试管号）")
    private String sampleCode;
    @Schema(description = "标本类型")
    private String sampleType;
    @Schema(description = "诊疗项目编码")
    private String itemCode;
    @Schema(description = "诊疗项目名称")
    private String itemName;
    @Schema(description = "报告项目编码")
    private String subItemCode;
    @Schema(description = "报告项目名称")
    private String subItemName;
    @Schema(description = "结果低值（单值结果）")
    private String resultLow;
    @Schema(description = "结果高值")
    private String resultHigh;
    @Schema(description = "单位编码")
    private String unitCode;
    @Schema(description = "单位名称")
    private String unitName;
    @Schema(description = "结果值标识")
    private ReportResultFlagEnum resultFlag;
    @Schema(description = "危机值结果标识")
    private String crisisResultFlag;
    @Schema(description = "补充说明")
    private String remark;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;

    @Schema(description = "参考范围低值")
    private String referenceValueLow;
    @Schema(description = "参考范围高值")
    private String referenceValueHigh;
    @Schema(description = "危机值下限")
    private String crisisLowLimit;
    @Schema(description = "危机值上限")
    private String crisisHighLimit;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCisReportResultId() {
        return cisReportResultId;
    }

    public void setCisReportResultId(String cisReportResultId) {
        this.cisReportResultId = cisReportResultId;
    }

    public String getReportDetailCode() {
        return reportDetailCode;
    }

    public void setReportDetailCode(String reportDetailCode) {
        this.reportDetailCode = reportDetailCode;
    }

    public String getSampleCode() {
        return sampleCode;
    }

    public void setSampleCode(String sampleCode) {
        this.sampleCode = sampleCode;
    }

    public String getSampleType() {
        return sampleType;
    }

    public void setSampleType(String sampleType) {
        this.sampleType = sampleType;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSubItemCode() {
        return subItemCode;
    }

    public void setSubItemCode(String subItemCode) {
        this.subItemCode = subItemCode;
    }

    public String getSubItemName() {
        return subItemName;
    }

    public void setSubItemName(String subItemName) {
        this.subItemName = subItemName;
    }

    public String getResultLow() {
        return resultLow;
    }

    public void setResultLow(String resultLow) {
        this.resultLow = resultLow;
    }

    public String getResultHigh() {
        return resultHigh;
    }

    public void setResultHigh(String resultHigh) {
        this.resultHigh = resultHigh;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public ReportResultFlagEnum getResultFlag() {
        return resultFlag;
    }

    public void setResultFlag(ReportResultFlagEnum resultFlag) {
        this.resultFlag = resultFlag;
    }

    public String getCrisisResultFlag() {
        return crisisResultFlag;
    }

    public void setCrisisResultFlag(String crisisResultFlag) {
        this.crisisResultFlag = crisisResultFlag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

//    public String getMinimal_class() {
//        return "." + this.getClass().getSimpleName();
//    }
    @JsonProperty("@class")
    public String getClassName() {
        return getClass().getName();
    }

    public String getReferenceValueLow() {
        return referenceValueLow;
    }

    public void setReferenceValueLow(String referenceValueLow) {
        this.referenceValueLow = referenceValueLow;
    }

    public String getReferenceValueHigh() {
        return referenceValueHigh;
    }

    public void setReferenceValueHigh(String referenceValueHigh) {
        this.referenceValueHigh = referenceValueHigh;
    }

    public String getCrisisLowLimit() {
        return crisisLowLimit;
    }

    public void setCrisisLowLimit(String crisisLowLimit) {
        this.crisisLowLimit = crisisLowLimit;
    }

    public String getCrisisHighLimit() {
        return crisisHighLimit;
    }

    public void setCrisisHighLimit(String crisisHighLimit) {
        this.crisisHighLimit = crisisHighLimit;
    }

    public String getReportContent() {
        return reportContent;
    }

    public void setReportContent(String reportContent) {
        this.reportContent = reportContent;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisReportResultDetailTo other = (CisReportResultDetailTo) obj;
        return Objects.equals(id, other.id);
    }
}