package com.bjgoodwill.hip.ds.cis.cdr.allergy.enmus;

public enum RecordTypeEnum {
    //    记录类型：AST皮试，ADV药品不良事件
    AST("AST", "皮试"),
    ADV("ADV", "药品不良事件");

    private final String code;

    private final String name;

    RecordTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RecordTypeEnum getValue(String code) {
        for (RecordTypeEnum value : RecordTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}