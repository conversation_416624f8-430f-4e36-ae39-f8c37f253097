package com.bjgoodwill.hip.ds.cis.cdr.report.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.report.to.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "报告结果领域服务", description = "报告结果领域服务")
public interface CisReportResultService {

    @Operation(summary = "P0根据查询条件对报告结果进行查询。")
    @GetMapping("/cisReportResults")
    List<CisReportResultTo> getCisReportResults(@ParameterObject @SpringQueryMap CisReportResultQto cisReportResultQto);

    @Operation(summary = "根据查询条件对报告结果进行分页查询。")
    @GetMapping("/cisReportResults/pages")
    GridResultSet<CisReportResultTo> getCisReportResultPage(@ParameterObject @SpringQueryMap CisReportResultQto cisReportResultQto);

    @Operation(summary = "创建报告结果。")
    @PostMapping("/cisReportResults")
    CisReportResultTo createCisReportResult(@RequestBody @Valid CisReportResultNto cisReportResultNto);

    @Operation(summary = "根据唯一标识修改报告结果。")
    @PutMapping("/cisReportResults/{id:.+}")
    void updateCisReportResult(@PathVariable("id") String id, @RequestBody @Valid CisReportResultEto cisReportResultEto);

    @Operation(summary = "根据唯一标识删除报告结果。")
    @DeleteMapping("/cisReportResults/{id:.+}")
    void deleteCisReportResult(@PathVariable("id") String id);

    @Operation(summary = "根据唯一标识返回医嘱报告明细。")
    @GetMapping("/cisReportResults/xId/cisReportResultDetails/{id:.+}")
    CisReportResultDetailTo getCisReportResultDetailById(@PathVariable("id") String id);

    @Operation(summary = "创建医嘱报告明细。")
    @PostMapping("/cisReportResults/{cisReportResultId}/cisReportResultDetails")
    CisReportResultDetailTo createCisReportResultDetail(@PathVariable("cisReportResultId") String cisReportResultId, @RequestBody @Valid CisReportResultDetailNto cisReportResultDetailNto);

    @Operation(summary = "根据唯一标识修改医嘱报告明细。")
    @PutMapping("/cisReportResults/xId/cisReportResultDetails/{id:.+}")
    void updateCisReportResultDetail(@PathVariable("id") String id, @RequestBody @Valid CisReportResultDetailEto cisReportResultDetailEto);

    @Operation(summary = "P0根据申请单号查看报告和报告明细。")
    @GetMapping("/cisReportResults/{apply-id:.+}")
    CisReportResultTo getCisReportResultByApplyId(@PathVariable("apply-id") String applyId);

    @Operation(summary = "P0查询患者子项数据。PRD_M325_住院医生站_报告查看")
    @GetMapping("/cisReportResults/subItem/{visit-code:.+}")
    List<CisReportResultSpcobsDetailTo> getCisReportResultDetialByVisitCode(@PathVariable("visit-code") String visitCode,
                                                                            @RequestParam String itemCode,
                                                                            @RequestParam LocalDateTime startDate,
                                                                            @RequestParam LocalDateTime endDate);

    @Operation(summary = "P0查询患者子项数据。PRD_M325_住院医生站_报告查看")
    @GetMapping("/cisReportResults/subItem/360/{pat-mi-code:.+}")
    List<CisReportResultSpcobsDetailTo> getCisReportResultDetialByPatMiCode(@PathVariable("pat-mi-code") String patMiCode,
                                                                            @RequestParam String itemCode,
                                                                            @RequestParam LocalDateTime startDate,
                                                                            @RequestParam LocalDateTime endDate);


    @Operation(summary = "P0 查询本次就诊的检验项目")
    @GetMapping("/cisReportResults/spcobs/visitCode/{visit-code:.+}")
    List<CisReportResultSpcobsDetailTo> getSpcobsDetailByVisitCode(@PathVariable("visit-code") String visitCode);

    @Operation(summary = "P0 查询本次就诊的检验项目")
    @GetMapping("/cisReportResults/dgimg/visitCode/{visit-code:.+}")
    List<CisReportResultTo> getDgimgResultsByVisitCode(@PathVariable("visit-code") String visitCode);

    @Operation(summary = "P0 查询单次检验项目明细")
    @GetMapping("/cisReportResults/spcobs/xId/{result-id:.+}")
    List<CisReportResultSpcobsDetailTo> getSpcobsDetailByResultId(@PathVariable("result-id") String resultId);

    @Operation(summary = "P0 查询单次检查项目明细")
    @GetMapping("/cisReportResults/dgimg/xId/{result-id:.+}")
    List<CisReportResultDgimgDetailTo> getDgimgDetailByResultId(@PathVariable("result-id") String resultId);
}