package com.bjgoodwill.hip.ds.cis.cdr.plan.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-04-10 17:51
 * @className: CisCdrOrderPlanRecordWithDetailNto
 * @description:
 **/
public class CisCdrOrderPlanRecordWithDetailNto extends BaseNto implements Serializable {

    private List<CisCdrOrderPlanRecordNto> cisCdrOrderPlanRecordNtos;

    private List<CisCdrOrderPlanDetailRecordNto> cisCdrOrderPlanDetailRecordNtos;

    public List<CisCdrOrderPlanRecordNto> getCisCdrOrderPlanRecordNtos() {
        return cisCdrOrderPlanRecordNtos;
    }

    public void setCisCdrOrderPlanRecordNtos(List<CisCdrOrderPlanRecordNto> cisCdrOrderPlanRecordNtos) {
        this.cisCdrOrderPlanRecordNtos = cisCdrOrderPlanRecordNtos;
    }

    public List<CisCdrOrderPlanDetailRecordNto> getCisCdrOrderPlanDetailRecordNtos() {
        return cisCdrOrderPlanDetailRecordNtos;
    }

    public void setCisCdrOrderPlanDetailRecordNtos(List<CisCdrOrderPlanDetailRecordNto> cisCdrOrderPlanDetailRecordNtos) {
        this.cisCdrOrderPlanDetailRecordNtos = cisCdrOrderPlanDetailRecordNtos;
    }
}