package com.bjgoodwill.hip.ds.cis.cdr.report.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Schema(description = "报告结果")
public class CisReportResultTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -8986089041370760119L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "医嘱id")
    private String orderID;
    @Schema(description = "申请单ID")
    private String applyId;
    @Schema(description = "存储其他系统报告主键，用来取消其他系统报告")
    private String reportId;
    @Schema(description = "主索引编码")
    private String patMiCode;
    @Schema(description = "接诊流水编码")
    private String visitCode;
    @Schema(description = "结果类型 SPCOBS/DGIMG")
    private SystemTypeEnum reportType;
    @Schema(description = "诊疗服务项目")
    private String serviceItemCode;
    @Schema(description = "诊疗服务项目名称")
    private String serviceItemName;
    @Schema(description = "设备类型")
    private String deviceType;
    @Schema(description = "执行科室代码")
    private String execOrgCode;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "reportDate")
    private LocalDateTime reportDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "逻辑删除标记")
    private boolean deleted;
    @Schema(description = "执行人编码")
    private String execStaff;
    @Schema(description = "执行人")
    private String execStaffName;
    @Schema(description = "医嘱报告明细列表")
    private List<CisReportResultDetailTo> cisReportResultDetails;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderID() {
        return orderID;
    }

    public void setOrderID(String orderID) {
        this.orderID = orderID;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public SystemTypeEnum getReportType() {
        return reportType;
    }

    public void setReportType(SystemTypeEnum reportType) {
        this.reportType = reportType;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getExecOrgCode() {
        return execOrgCode;
    }

    public void setExecOrgCode(String execOrgCode) {
        this.execOrgCode = execOrgCode;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getReportDate() {
        return reportDate;
    }

    public void setReportDate(LocalDateTime reportDate) {
        this.reportDate = reportDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public String getExecStaff() {
        return execStaff;
    }

    public void setExecStaff(String execStaff) {
        this.execStaff = execStaff;
    }

    public String getExecStaffName() {
        return execStaffName;
    }

    public void setExecStaffName(String execStaffName) {
        this.execStaffName = execStaffName;
    }

    public List<CisReportResultDetailTo> getCisReportResultDetails() {
        return cisReportResultDetails;
    }

    public void setCisReportResultDetails(List<CisReportResultDetailTo> cisReportResultDetails) {
        this.cisReportResultDetails = cisReportResultDetails;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisReportResultTo other = (CisReportResultTo) obj;
        return Objects.equals(id, other.id);
    }
}