package com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.service;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.to.CisAntimicrobialsSkinExecReportNto;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.to.CisAntimicrobialsSkinExecReportQto;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.to.CisAntimicrobialsSkinExecReportTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "抗菌药皮试药执行记录领域服务", description = "抗菌药皮试药执行记录领域服务")
public interface CisAntimicrobialsSkinExecReportService {

    @Operation(summary = "P0根据查询条件对抗菌药皮试药执行记录进行查询。")
    @GetMapping("/cisAntimicrobialsSkinExecReports")
    List<CisAntimicrobialsSkinExecReportTo> getCisAntimicrobialsSkinExecReports(@ParameterObject @SpringQueryMap CisAntimicrobialsSkinExecReportQto cisAntimicrobialsSkinExecReportQto);

    @Operation(summary = "根据查询条件对抗菌药皮试药执行记录进行分页查询。")
    @GetMapping("/cisAntimicrobialsSkinExecReports/pages")
    GridResultSet<CisAntimicrobialsSkinExecReportTo> getCisAntimicrobialsSkinExecReportPage(@ParameterObject @SpringQueryMap CisAntimicrobialsSkinExecReportQto cisAntimicrobialsSkinExecReportQto);

    @Operation(summary = "根据唯一标识返回抗菌药皮试药执行记录。")
    @GetMapping("/cisAntimicrobialsSkinExecReports/{id:.+}")
    CisAntimicrobialsSkinExecReportTo getCisAntimicrobialsSkinExecReportById(@PathVariable("id") String id);

    @Operation(summary = "创建抗菌药皮试药执行记录。")
    @PostMapping("/cisAntimicrobialsSkinExecReports")
    CisAntimicrobialsSkinExecReportTo createCisAntimicrobialsSkinExecReport(@RequestBody @Valid CisAntimicrobialsSkinExecReportNto cisAntimicrobialsSkinExecReportNto);

    @Operation(summary = "根据唯一标识删除抗菌药皮试药执行记录。")
    @DeleteMapping("/cisAntimicrobialsSkinExecReports/{id:.+}")
    void deleteCisAntimicrobialsSkinExecReport(@PathVariable("id") String id);


    @Operation(summary = "P0批量创建抗菌药皮试药执行记录。")
    @PostMapping("/cisAntimicrobialsSkinExecReports/batch")
    List<CisAntimicrobialsSkinExecReportTo> createCisAntimicrobialsSkinExecReport(@RequestBody @Valid List<CisAntimicrobialsSkinExecReportNto> cisAntimicrobialsSkinExecReportNtos);

    @Operation(summary = "查询患者皮试和抗菌药使用记录。")
    @GetMapping("/cisAntimicrobialsSkinExecReports/{visit-code:.+}")
    List<CisAntimicrobialsSkinExecReportTo> findreportsByVisitCode(@PathVariable("visit-code") String visitCode);
}