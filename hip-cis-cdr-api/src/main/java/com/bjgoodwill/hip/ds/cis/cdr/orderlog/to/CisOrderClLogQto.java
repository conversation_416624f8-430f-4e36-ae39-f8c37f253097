package com.bjgoodwill.hip.ds.cis.cdr.orderlog.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.ExecLogEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医嘱全闭环日志")
public class CisOrderClLogQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -216588118394224877L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "医嘱id")
    private String orderId;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "医嘱序号")
    private Double orderNo;
    @Schema(description = "医嘱名称")
    private String orderName;
    @Schema(description = "操作类型")
    private ExecLogEnum execLogType;
    @Schema(description = "工作组")
    private String orgCode;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public Double getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Double orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public ExecLogEnum getExecLogType() {
        return execLogType;
    }

    public void setExecLogType(ExecLogEnum execLogType) {
        this.execLogType = execLogType;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }
}