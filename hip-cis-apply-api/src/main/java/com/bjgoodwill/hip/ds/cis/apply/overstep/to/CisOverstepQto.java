package com.bjgoodwill.hip.ds.cis.apply.overstep.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CheckTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisOverstepEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;

import java.io.Serial;
import java.io.Serializable;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-03-08 15:06
 * @className: CisOverstepQto
 * @description:
 **/
public class CisOverstepQto extends BaseQto implements Serializable {
    @Serial
    private static final long serialVersionUID = -3950751990336749580L;

    private CheckTypeEnum[] checkType;

    private CisOverstepEnum[] checkSystemType;

    private String createName;

    private String visitOrgCode;

    private String createOrgCode;

    private String deptNurseCode;

    public CheckTypeEnum[] getCheckType() {
        return checkType;
    }

    public void setCheckType(CheckTypeEnum[] checkType) {
        this.checkType = checkType;
    }

    public CisOverstepEnum[] getCheckSystemType() {
        return checkSystemType;
    }

    public void setCheckSystemType(CisOverstepEnum[] checkSystemType) {
        this.checkSystemType = checkSystemType;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getVisitOrgCode() {
        return visitOrgCode;
    }

    public void setVisitOrgCode(String visitOrgCode) {
        this.visitOrgCode = visitOrgCode;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }
}