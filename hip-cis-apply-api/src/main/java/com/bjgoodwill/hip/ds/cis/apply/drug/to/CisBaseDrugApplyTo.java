package com.bjgoodwill.hip.ds.cis.apply.drug.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "药品申请单")
public abstract class CisBaseDrugApplyTo extends CisBaseApplyTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -7719012913376908093L;

    @Schema(description = "用法")
    private String usage;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "频次")
    private String frequency;
    @Schema(description = "频次名称")
    private String frequencyName;
    @Schema(description = "疗程")
    private String treatmentCourse;
    @Schema(description = "疗程单位")
    private String treatmentCourseUnit;
    @Schema(description = "疗程单位名称")
    private String treatmentCourseUnitName;
    @Schema(description = "协定处方")
    private Boolean prescriptionFlag;
    @Schema(description = "领药科室")
    private String receiveOrg;
    @Schema(description = "领药科室名称")
    private String receiveOrgName;
    @Schema(description = "药品申请单明细")
    private List<CisDrugApplyDetailTo> cisDrugApplyDetails;

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public String getTreatmentCourse() {
        return treatmentCourse;
    }

    public void setTreatmentCourse(String treatmentCourse) {
        this.treatmentCourse = treatmentCourse;
    }

    public String getTreatmentCourseUnit() {
        return treatmentCourseUnit;
    }

    public void setTreatmentCourseUnit(String treatmentCourseUnit) {
        this.treatmentCourseUnit = treatmentCourseUnit;
    }

    public Boolean getPrescriptionFlag() {
        return prescriptionFlag;
    }

    public void setPrescriptionFlag(Boolean prescriptionFlag) {
        this.prescriptionFlag = prescriptionFlag;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    public List<CisDrugApplyDetailTo> getCisDrugApplyDetails() {
        return cisDrugApplyDetails;
    }

    public void setCisDrugApplyDetails(List<CisDrugApplyDetailTo> cisDrugApplyDetails) {
        this.cisDrugApplyDetails = cisDrugApplyDetails;
    }

//    private SbadmWayEnum sbadmWay;
//
//    public SbadmWayEnum getSbadmWay() {
//        return sbadmWay;
//    }
//
//    public void setSbadmWay(SbadmWayEnum sbadmWay) {
//        this.sbadmWay = sbadmWay;
//    }

    public String getTreatmentCourseUnitName() {
        return treatmentCourseUnitName;
    }

    public void setTreatmentCourseUnitName(String treatmentCourseUnitName) {
        this.treatmentCourseUnitName = treatmentCourseUnitName;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }
}