package com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.service;

import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to.CisOrderExecLimitEto;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to.CisOrderExecLimitNto;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to.CisOrderExecLimitTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@Tag(name = "执行单第三方状态操作限制领域服务", description = "执行单第三方状态操作限制领域服务")
public interface CisOrderExecLimitService {

    @Operation(summary = "根据唯一标识返回执行单第三方状态操作限制。")
    @GetMapping("/cisOrderExecLimits/{id:.+}")
    CisOrderExecLimitTo getCisOrderExecLimitById(@PathVariable("id") String id);

    @Operation(summary = "创建执行单第三方状态操作限制。")
    @PostMapping("/cisOrderExecLimits")
    CisOrderExecLimitTo createCisOrderExecLimit(@RequestBody @Valid CisOrderExecLimitNto cisOrderExecLimitNto);

    @Operation(summary = "根据唯一标识修改执行单第三方状态操作限制。")
    @PutMapping("/cisOrderExecLimits/{id:.+}")
    void updateCisOrderExecLimit(@PathVariable("id") String id, @RequestBody @Valid CisOrderExecLimitEto cisOrderExecLimitEto);

    @Operation(summary = "根据唯一标识删除执行单第三方状态操作限制。")
    @DeleteMapping("/cisOrderExecLimits/{id:.+}")
    void deleteCisOrderExecLimit(@PathVariable("id") String id);

}