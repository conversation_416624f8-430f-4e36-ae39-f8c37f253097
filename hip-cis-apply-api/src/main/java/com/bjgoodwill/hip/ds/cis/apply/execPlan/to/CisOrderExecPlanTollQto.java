package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: cis-base
 * @author: xdguo
 * @create: 2025-06-16 16:37
 * @className: CisOrderExecPlanTollQto
 * @description:
 **/
@Schema(description = "范围内的执行单查询。")
public class CisOrderExecPlanTollQto extends BaseQto {

    @Schema(description = "开始时间")
    private LocalDateTime startDate;

    @Schema(description = "结束时间")
    private LocalDateTime endDate;

//    @Schema(description = "是否收费")
//    private Boolean isCharge;

    @Schema(description = "就诊类型")
    private VisitTypeEnum visitType;

    @Schema(description = "患者主索引")
    private String patMiCode;

    //    @Schema(description = "结算状态")
//    private SetlStasEnum setlStas;
    @Schema(description = "是否结算")
    private Boolean seltFlag;

    @Schema(description = "执行机构")
    private String execOrgCode;

    @NotNull(message = "开始时间不能为空！")
    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    @NotNull(message = "结束时间不能为空！")
    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }


    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    @NotBlank(message = "主索引不能为空！")
    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public Boolean getSeltFlag() {
        return seltFlag;
    }

    public void setSeltFlag(Boolean seltFlag) {
        this.seltFlag = seltFlag;
    }

    @NotBlank(message = "执行机构不能为空！")
    public String getExecOrgCode() {
        return execOrgCode;
    }

    public void setExecOrgCode(String execOrgCode) {
        this.execOrgCode = execOrgCode;
    }
}