package com.bjgoodwill.hip.ds.cis.apply.spcobs.service;

import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyService;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "检验类申请单领域服务", description = "检验类申请单领域服务")
public interface CisSpcobsApplyService extends CisBaseApplyService {

    @Operation(summary = "P0根据唯一标识返回检验类申请单。")
    @GetMapping("/cisSpcobsApplies/{id:.+}")
    CisSpcobsApplyTo getCisSpcobsApplyById(@PathVariable("id") String id);

    @Operation(summary = "根据唯一标识返回检验申请、明细、费用、诊断等。")
    @GetMapping("/cisSpcobsApplies/getall/{id:.+}")
    CisSpcobsApplyTo getCisSpcobsApplyAllById(@PathVariable("id") String id);

    @Operation(summary = "P0创建检验类申请单。")
    @PostMapping("/cisSpcobsApplies")
    CisSpcobsApplyTo createCisSpcobsApply(@RequestBody @Valid CisSpcobsApplyNto cisSpcobsApplyNto);

    @Operation(summary = "P0根据唯一标识修改检验类申请单。")
    @PutMapping("/cisSpcobsApplies/{id:.+}")
    void updateCisSpcobsApply(@PathVariable("id") String id, @RequestBody @Valid CisSpcobsApplyEto cisSpcobsApplyEto);

    @Operation(summary = "P0根据唯一标识删除检验类申请单。")
    @DeleteMapping("/cisSpcobsApplies/{id:.+}")
    void deleteCisSpcobsApply(@PathVariable("id") String id);

    @Operation(summary = "P0根据唯一标识返回检验申请单明细。")
    @GetMapping("/cisSpcobsApplies/xId/cisSpcobsApplyDetails/{id:.+}")
    CisSpcobsApplyDetailTo getCisSpcobsApplyDetailById(@PathVariable("id") String id);

    @Operation(summary = "P0创建检验申请单明细。")
    @PostMapping("/cisSpcobsApplies/{cisSpcobsApplyId}/cisSpcobsApplyDetails")
    CisSpcobsApplyDetailTo createCisSpcobsApplyDetail(@PathVariable("cisSpcobsApplyId") String cisSpcobsApplyId, @RequestBody @Valid CisSpcobsApplyDetailNto cisSpcobsApplyDetailNto);

    @Operation(summary = "P0根据唯一标识修改检验申请单明细。")
    @PutMapping("/cisSpcobsApplies/xId/cisSpcobsApplyDetails/{id:.+}")
    void updateCisSpcobsApplyDetail(@PathVariable("id") String id, @RequestBody @Valid CisSpcobsApplyDetailEto cisSpcobsApplyDetailEto);

    @Operation(summary = "P0根据唯一标识删除检验申请单明细。")
    @DeleteMapping("/cisSpcobsApplies/xId/cisSpcobsApplyDetails/{id:.+}")
    void deleteCisSpcobsApplyDetail(@PathVariable("id") String id);

    @Operation(summary = "P0根据申请单Id返回检验申请单明细。")
    @GetMapping("/cisSpcobsApplies/{applyId}/cisSpcobsApplyDetails")
    List<CisSpcobsApplyDetailTo> getCisSpcobsApplyDetailByApplyId(@PathVariable("applyId") String applyId);

}