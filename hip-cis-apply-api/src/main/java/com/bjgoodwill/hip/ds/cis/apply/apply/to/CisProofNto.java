package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-09-18 10:21
 * @className: CisProofNto
 * @description: 校对后的执行档保存
 **/
@Schema(description = "校对 执行单 保存")
public class CisProofNto implements Serializable {
    @Serial
    private static final long serialVersionUID = -400522152516694464L;

    private String applyId;
    private String visitCode;
    private List<CisOrderExecPlanNto> execPlanNtos = new ArrayList<>();

    @Schema(description = "申请单ID")
    @NotBlank(message = "申请单ID不能为空！")
    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    @Schema(description = "就诊流水号")
    @NotBlank(message = "就诊流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    //    @NotNull(message = "执行档不能为空！")
    public List<CisOrderExecPlanNto> getExecPlanNtos() {
        return execPlanNtos;
    }

    public void setExecPlanNtos(List<CisOrderExecPlanNto> execPlanNtos) {
        this.execPlanNtos = execPlanNtos;
    }
}