package com.bjgoodwill.hip.ds.cis.apply.detail.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;

import java.io.Serializable;
import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-26 11:21
 * @className: ApplyWithDetialNto
 * @description:
 **/

public class ApplyWithDetialNto<T extends DetailNto> extends CisBaseApplyNto implements Serializable {

    private List<T> details;

    public List<T> getDetails() {
        return details;
    }

    public void setDetails(List<T> details) {
        this.details = details;
    }
}