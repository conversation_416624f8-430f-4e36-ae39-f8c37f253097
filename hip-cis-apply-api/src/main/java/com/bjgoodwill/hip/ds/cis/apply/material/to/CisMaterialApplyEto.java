package com.bjgoodwill.hip.ds.cis.apply.material.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "材料")
public class CisMaterialApplyEto extends CisBaseApplyEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -7914947789508950691L;

    @Schema(description = "高值")
    private Boolean highFlag;
    @Schema(description = "高值编码")
    private String barCode;

    public Boolean getHighFlag() {
        return highFlag;
    }

    public void setHighFlag(Boolean highFlag) {
        this.highFlag = highFlag;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = StringUtils.trimToNull(barCode);
    }
}