package com.bjgoodwill.hip.ds.cis.apply.drug.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "中草药申请单")
public class CisCDrugApplyNto extends CisBaseDrugApplyNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -5351471790999141866L;

    @Schema(description = "付数")
    private String doseNum;
    @Schema(description = "草药煎法;0自煎,1代煎")
    private Integer decoction;
    @Schema(description = "草药一付包数")
    private Integer cdrugPackNum;
    @Schema(description = "草药每包毫升数")
    private Integer cdrugPackMl;
    @Schema(description = "协定处方")
    private Boolean prescriptionFlag;
    @Schema(description = "草药属性（DrugHerbsProEnum）")
    private String herbsProCode;

    @NotBlank(message = "付数不能为空！")
    public String getDoseNum() {
        return doseNum;
    }

    public void setDoseNum(String doseNum) {
        this.doseNum = StringUtils.trimToNull(doseNum);
    }

    public Integer getDecoction() {
        return decoction;
    }

    public void setDecoction(Integer decoction) {
        this.decoction = decoction;
    }

    //@NotNull(message = "草药一付包数不能为空！")
    public Integer getCdrugPackNum() {
        return cdrugPackNum;
    }

    public void setCdrugPackNum(Integer cdrugPackNum) {
        this.cdrugPackNum = cdrugPackNum;
    }

    //@NotNull(message = "草药每包毫升数不能为空！")
    public Integer getCdrugPackMl() {
        return cdrugPackMl;
    }

    public void setCdrugPackMl(Integer cdrugPackMl) {
        this.cdrugPackMl = cdrugPackMl;
    }

    public Boolean getPrescriptionFlag() {
        return prescriptionFlag;
    }

    public void setPrescriptionFlag(Boolean prescriptionFlag) {
        this.prescriptionFlag = prescriptionFlag;
    }

    @NotNull(message = "草药属性不能为空")
    public String getHerbsProCode() {
        return herbsProCode;
    }

    public void setHerbsProCode(String herbsProCode) {
        this.herbsProCode = herbsProCode;
    }
}