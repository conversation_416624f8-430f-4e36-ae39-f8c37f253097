package com.bjgoodwill.hip.ds.cis.apply.drug.to;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "成药申请单")
public class CisEDrugApplyNto extends CisBaseDrugApplyNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -7136668111703431029L;

    @Schema(description = "滴速")
    private String dripSpeed;
    @Schema(description = "滴速单位")
    private String dripSpeedUnit;
    @Schema(description = "滴速单位名称")
    private String dripSpeedUnitName;

    public String getDripSpeed() {
        return dripSpeed;
    }

    public void setDripSpeed(String dripSpeed) {
        this.dripSpeed = dripSpeed;
    }

    public String getDripSpeedUnit() {
        return dripSpeedUnit;
    }

    public void setDripSpeedUnit(String dripSpeedUnit) {
        this.dripSpeedUnit = StringUtils.trimToNull(dripSpeedUnit);
    }

    public String getDripSpeedUnitName() {
        return dripSpeedUnitName;
    }

    public void setDripSpeedUnitName(String dripSpeedUnitName) {
        this.dripSpeedUnitName = dripSpeedUnitName;
    }
}