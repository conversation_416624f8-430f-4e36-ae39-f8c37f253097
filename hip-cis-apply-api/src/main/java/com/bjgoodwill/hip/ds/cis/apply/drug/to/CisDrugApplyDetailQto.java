package com.bjgoodwill.hip.ds.cis.apply.drug.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "药品明细")
public class CisDrugApplyDetailQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2851854419607645723L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "药品申请单标识")
    private String cisBaseDrugApplyId;
    private SbadmWayEnum sbadmWay;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getCisBaseDrugApplyId() {
        return cisBaseDrugApplyId;
    }

    public void setCisBaseDrugApplyId(String cisBaseDrugApplyId) {
        this.cisBaseDrugApplyId = cisBaseDrugApplyId;
    }

    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }
}