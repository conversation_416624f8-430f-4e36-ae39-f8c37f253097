package com.bjgoodwill.hip.ds.cis.apply.drug.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetialNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "药品申请单")
public abstract class CisBaseDrugApplyNto extends ApplyWithDetialNto<CisDrugApplyDetailNto> implements Serializable {

    @Serial
    private static final long serialVersionUID = -3291761474502315310L;

    @Schema(description = "用法")
    private String usage;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "疗程")
    private String treatmentCourse;
    @Schema(description = "疗程单位")
    private String treatmentCourseUnit;
    @Schema(description = "疗程单位名称")
    private String treatmentCourseUnitName;
    @Schema(description = "药房")
    private String receiveOrg;
    @Schema(description = "药房名称")
    private String receiveOrgName;
    @Schema(description = "药品明细")
    private List<CisDrugApplyDetailNto> details;
//    private List<CisDrugApplyDetailNto> cisDrugApplyDetails;
//    private SbadmWayEnum sbadmWay;

    @NotBlank(message = "用法不能为空！")
    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = StringUtils.trimToNull(usage);
    }

//    @NotNull(message = "取药方式不能为空！")
//    public SbadmWayEnum getSbadmWay() {
//        return sbadmWay;
//    }
//
//    public void setSbadmWay(SbadmWayEnum sbadmWay) {
//        this.sbadmWay = sbadmWay;
//    }

    @NotBlank(message = "用法名称不能为空！")
    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = StringUtils.trimToNull(usageName);
    }

    public String getTreatmentCourse() {
        return treatmentCourse;
    }

    public void setTreatmentCourse(String treatmentCourse) {
        this.treatmentCourse = StringUtils.trimToNull(treatmentCourse);
    }

    public String getTreatmentCourseUnit() {
        return treatmentCourseUnit;
    }

    public void setTreatmentCourseUnit(String treatmentCourseUnit) {
        this.treatmentCourseUnit = StringUtils.trimToNull(treatmentCourseUnit);
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = StringUtils.trimToNull(receiveOrg);
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    @Override
    @NotEmpty(message = "明细不能为空！")
    public List<CisDrugApplyDetailNto> getDetails() {
        return details;
    }

    @Override
    public void setDetails(List<CisDrugApplyDetailNto> details) {
        this.details = details;
    }

    //    @NotEmpty(message = "明细不能为空！")
//    public List<CisDrugApplyDetailNto> getCisDrugApplyDetails() {
//        return cisDrugApplyDetails;
//    }
//
//    public void setCisDrugApplyDetails(List<CisDrugApplyDetailNto> cisDrugApplyDetails) {
//        this.cisDrugApplyDetails = cisDrugApplyDetails;
//    }

    public String getTreatmentCourseUnitName() {
        return treatmentCourseUnitName;
    }

    public void setTreatmentCourseUnitName(String treatmentCourseUnitName) {
        this.treatmentCourseUnitName = treatmentCourseUnitName;
    }
}