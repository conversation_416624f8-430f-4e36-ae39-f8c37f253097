package com.bjgoodwill.hip.ds.cis.apply.blood.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetialNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "输血申请单")
public class CisInputBloodApplyNto extends ApplyWithDetialNto<CisBloodComponentNto> implements Serializable {

    @Serial
    private static final long serialVersionUID = -5143717668396246297L;

    @Schema(description = "输血申请单标识")
    private String cisBloodApplyId;
    @Schema(description = "血型")
    private String bloodType;
    @Schema(description = "0-阴性，1-阳性")
    private Integer rh_d;
    @Schema(description = "血液成分")
    private List<CisBloodComponentNto> details;

    @NotBlank(message = "输血申请单标识不能为空！")
    @Size(max = 50, message = "输血申请单标识长度不能超过50个字符！")
    public String getCisBloodApplyId() {
        return cisBloodApplyId;
    }

    public void setCisBloodApplyId(String cisBloodApplyId) {
        this.cisBloodApplyId = StringUtils.trimToNull(cisBloodApplyId);
    }

    public String getBloodType() {
        return bloodType;
    }

    public void setBloodType(String bloodType) {
        this.bloodType = bloodType;
    }

    public Integer getRh_d() {
        return rh_d;
    }

    public void setRh_d(Integer rh_d) {
        this.rh_d = rh_d;
    }

    public List<CisBloodComponentNto> getDetails() {
        return details;
    }

    public void setDetails(List<CisBloodComponentNto> details) {
        this.details = details;
    }
}