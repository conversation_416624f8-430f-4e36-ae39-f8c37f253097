package com.bjgoodwill.hip.ds.cis.apply.charge.enmus;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-23 10:56
 * @className: CisChargeType
 * @description:
 **/
public enum CisChargeTypeEnum {
    DOCT("DOCT", "医生补费"),
    NURSE("NURSE", "护士补费"),
    USAGE("USAGE", "用法补费"),
    DECOCTION("DECOCTION", "煎药费"),
    PRESCRIPTION("PRESCRIPTION", "协方加工费"),
    SERVICEITEM("SERVICEITEM", "医嘱项目"),
    THIRD("THIRD", "第三方");

    private String code;
    private String desc;

    CisChargeTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
