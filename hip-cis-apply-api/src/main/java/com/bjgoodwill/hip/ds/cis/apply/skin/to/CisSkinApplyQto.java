package com.bjgoodwill.hip.ds.cis.apply.skin.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "skin")
public class CisSkinApplyQto extends CisBaseApplyQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -5089325698789214313L;

    @Schema(description = "药房")
    private String receiveOrg;


    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }
}