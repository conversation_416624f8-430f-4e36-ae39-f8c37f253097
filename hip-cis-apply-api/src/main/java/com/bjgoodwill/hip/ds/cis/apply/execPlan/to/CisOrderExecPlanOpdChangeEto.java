package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "门诊修改执行单计费状态")
public class CisOrderExecPlanOpdChangeEto extends BaseEto implements Serializable {

    @Schema(description = "执行单ID")
    private String execPlanId;
    @Schema(description = "计费时间")
    private LocalDateTime chargeDate;
    @Schema(description = "计费人")
    private String chargeStaff;
    @Schema(description = "计费科室")
    private String chargeOrg;
    @Schema(description = "是否计费")
    private Boolean isCharge;

    @NotBlank(message = "执行单ID不能为空！")
    public String getExecPlanId() {
        return execPlanId;
    }

    public void setExecPlanId(String execPlanId) {
        this.execPlanId = execPlanId;
    }

    public LocalDateTime getChargeDate() {
        return chargeDate;
    }

    public void setChargeDate(LocalDateTime chargeDate) {
        this.chargeDate = chargeDate;
    }

    public String getChargeStaff() {
        return chargeStaff;
    }

    public void setChargeStaff(String chargeStaff) {
        this.chargeStaff = chargeStaff;
    }

    public String getChargeOrg() {
        return chargeOrg;
    }

    public void setChargeOrg(String chargeOrg) {
        this.chargeOrg = chargeOrg;
    }

    public Boolean getIsCharge() {
        return isCharge;
    }

    public void setIsCharge(Boolean isCharge) {
        this.isCharge = isCharge;
    }

}