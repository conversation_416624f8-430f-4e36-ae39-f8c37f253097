package com.bjgoodwill.hip.ds.cis.apply.blood.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetailEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "备血申请单")
public class CisBloodApplyEto extends ApplyWithDetailEto<CisBloodComponentEto, CisBloodComponentNto> implements Serializable {

    @Serial
    private static final long serialVersionUID = -4630532879401116719L;

    @Schema(description = "预定输注时间")
    private LocalDateTime preInfusionDate;
    @Schema(description = "临床诊断")
    private String clinicalDiagnosis;
    @Schema(description = "输血指征")
    private String transfusionTrigger;
    @Schema(description = "输血需求 1-常规用血，2-紧急手术，3-手术备血，4-异性输血")
    private String transfusionDemand;
    @Schema(description = "输血目的")
    private String transfusionPurpose;
    @Schema(description = "输血方式 1-异体，2-自体，")
    private String transfusionWay;
    @Schema(description = "输血检测项目 0-未送检，1-已送检")
    private String transfusionDetection;
    @Schema(description = "采血人")
    private String drawBloodUser;
    @Schema(description = "采血人名称")
    private String drawBloodUserName;
    @Schema(description = "血型")
    private String bloodType;
    @Schema(description = "0-阴性，1-阳性")
    private Integer rh_d;
    @Schema(description = "红细胞RBC")
    private Double erythrocyte;
    @Schema(description = "白细胞 WBC 单位x10^9/L")
    private String leukocyte;
    @Schema(description = "血红蛋白 HB")
    private Integer hemoglobin;
    @Schema(description = "血小板 PL")
    private Double thrombocyte;
    @Schema(description = "红细胞压积HCT")
    private Double hematokrit;
    @Schema(description = "谷丙转氨酶ALT")
    private Double glutamicPyruvic;
    @Schema(description = "APTT单位秒 参考值 28.0~43.5")
    private Double aptt;
    @Schema(description = "FIBD单位g/L 参考值 2.00~4.00")
    private Double fibd;
    @Schema(description = "PT单位秒 参考值11.0~15.0")
    private Double pt;
    @Schema(description = "HbsAg乙肝表面抗原 0-阴性，1-阳性")
    private Boolean hbsAg;
    @Schema(description = "HbsAb乙型肝炎表面抗体 0-阴性，1-阳性")
    private Boolean hbsAb;
    @Schema(description = "HbeAg乙型肝炎E抗原 0-阴性，1-阳性")
    private Boolean hbeAg;
    @Schema(description = "HbeAb乙型肝炎E抗体 0-阴性，1-阳性")
    private Boolean hbeAb;
    @Schema(description = "HbcAb乙肝核心抗体 0-阴性，1-阳性")
    private Boolean hbcAb;
    @Schema(description = "HCVAb丙肝病毒抗体 0-阴性，1-阳性")
    private Boolean hcvAb;
    @Schema(description = "TP-Ab梅毒 0-阴性，1-阳性")
    private Boolean tpAb;
    @Schema(description = "HIV(1+2)Ab艾滋病")
    private Boolean hivAb;
    @Schema(description = "注明1-紧急输血标本已取结果")
    private Boolean indicate1;
    @Schema(description = "注明2-因患者(或代理人)拒绝检测,故无数据")
    private Boolean indicate2;
    @Schema(description = "标本留取时间")
    private LocalDateTime specimenRetentionDate;
    @Schema(description = "申请医生签字")
    private String applyUser;
    @Schema(description = "申请医生签字名称")
    private String applyUserName;
    @Schema(description = "申请时间")
    private LocalDateTime applyDate;
    @Schema(description = "申请确认标识：0申请，1通过")
    private Boolean appleType;
    @Schema(description = "此患者仅备血未输血 0-否，1-是")
    private Boolean preBlood;
    @Schema(description = "是否输血史")
    private Boolean transfusionHistory;
    @Schema(description = "是否妊娠史")
    private Boolean pregnancyHistory;
    @Schema(description = "是否过敏史")
    private Boolean allergicHistoryFlag;
    @Schema(description = "上级医师")
    private String seniorPhysician;
    @Schema(description = "上级医师名称")
    private String seniorPhysicianName;
    @Schema(description = "上级医师意见")
    private String seniorPhysicianOpinion;
    @Schema(description = "预定输血血型")
    private String preInfusionBloodType;

    public LocalDateTime getPreInfusionDate() {
        return preInfusionDate;
    }

    public void setPreInfusionDate(LocalDateTime preInfusionDate) {
        this.preInfusionDate = preInfusionDate;
    }

    public String getClinicalDiagnosis() {
        return clinicalDiagnosis;
    }

    public void setClinicalDiagnosis(String clinicalDiagnosis) {
        this.clinicalDiagnosis = StringUtils.trimToNull(clinicalDiagnosis);
    }

    public String getTransfusionTrigger() {
        return transfusionTrigger;
    }

    public void setTransfusionTrigger(String transfusionTrigger) {
        this.transfusionTrigger = StringUtils.trimToNull(transfusionTrigger);
    }

    public String getTransfusionDemand() {
        return transfusionDemand;
    }

    public void setTransfusionDemand(String transfusionDemand) {
        this.transfusionDemand = StringUtils.trimToNull(transfusionDemand);
    }

    public String getTransfusionPurpose() {
        return transfusionPurpose;
    }

    public void setTransfusionPurpose(String transfusionPurpose) {
        this.transfusionPurpose = StringUtils.trimToNull(transfusionPurpose);
    }

    public String getTransfusionWay() {
        return transfusionWay;
    }

    public void setTransfusionWay(String transfusionWay) {
        this.transfusionWay = StringUtils.trimToNull(transfusionWay);
    }

    public String getTransfusionDetection() {
        return transfusionDetection;
    }

    public void setTransfusionDetection(String transfusionDetection) {
        this.transfusionDetection = StringUtils.trimToNull(transfusionDetection);
    }

    public String getDrawBloodUser() {
        return drawBloodUser;
    }

    public void setDrawBloodUser(String drawBloodUser) {
        this.drawBloodUser = StringUtils.trimToNull(drawBloodUser);
    }

    public String getBloodType() {
        return bloodType;
    }

    public void setBloodType(String bloodType) {
        this.bloodType = StringUtils.trimToNull(bloodType);
    }

    public Integer getRh_d() {
        return rh_d;
    }

    public void etRh_d(Integer rh_d) {
        this.rh_d = rh_d;
    }

    public Double getErythrocyte() {
        return erythrocyte;
    }

    public void setErythrocyte(Double erythrocyte) {
        this.erythrocyte = erythrocyte;
    }

    public String getLeukocyte() {
        return leukocyte;
    }

    public void setLeukocyte(String leukocyte) {
        this.leukocyte = StringUtils.trimToNull(leukocyte);
    }

    public Integer getHemoglobin() {
        return hemoglobin;
    }

    public void setHemoglobin(Integer hemoglobin) {
        this.hemoglobin = hemoglobin;
    }

    public Double getThrombocyte() {
        return thrombocyte;
    }

    public void setThrombocyte(Double thrombocyte) {
        this.thrombocyte = thrombocyte;
    }

    public Double getHematokrit() {
        return hematokrit;
    }

    public void setHematokrit(Double hematokrit) {
        this.hematokrit = hematokrit;
    }

    public Double getGlutamicPyruvic() {
        return glutamicPyruvic;
    }

    public void setGlutamicPyruvic(Double glutamicPyruvic) {
        this.glutamicPyruvic = glutamicPyruvic;
    }

    public Double getAptt() {
        return aptt;
    }

    public void setAptt(Double aptt) {
        this.aptt = aptt;
    }

    public Double getFibd() {
        return fibd;
    }

    public void setFibd(Double fibd) {
        this.fibd = fibd;
    }

    public Double getPt() {
        return pt;
    }

    public void setPt(Double pt) {
        this.pt = pt;
    }

    public Boolean getHbsAg() {
        return hbsAg;
    }

    public void setHbsAg(Boolean hbsAg) {
        this.hbsAg = hbsAg;
    }

    public Boolean getHbsAb() {
        return hbsAb;
    }

    public void setHbsAb(Boolean hbsAb) {
        this.hbsAb = hbsAb;
    }

    public Boolean getHbeAg() {
        return hbeAg;
    }

    public void setHbeAg(Boolean hbeAg) {
        this.hbeAg = hbeAg;
    }

    public Boolean getHbeAb() {
        return hbeAb;
    }

    public void setHbeAb(Boolean hbeAb) {
        this.hbeAb = hbeAb;
    }

    public Boolean getHbcAb() {
        return hbcAb;
    }

    public void setHbcAb(Boolean hbcAb) {
        this.hbcAb = hbcAb;
    }

    public Boolean getHcvAb() {
        return hcvAb;
    }

    public void setHcvAb(Boolean hcvAb) {
        this.hcvAb = hcvAb;
    }

    public Boolean getTpAb() {
        return tpAb;
    }

    public void setTpAb(Boolean tpAb) {
        this.tpAb = tpAb;
    }

    public Boolean getHivAb() {
        return hivAb;
    }

    public void setHivAb(Boolean hivAb) {
        this.hivAb = hivAb;
    }

    public Boolean getIndicate1() {
        return indicate1;
    }

    public void setIndicate1(Boolean indicate1) {
        this.indicate1 = indicate1;
    }

    public Boolean getIndicate2() {
        return indicate2;
    }

    public void setIndicate2(Boolean indicate2) {
        this.indicate2 = indicate2;
    }

    public LocalDateTime getSpecimenRetentionDate() {
        return specimenRetentionDate;
    }

    public void setSpecimenRetentionDate(LocalDateTime specimenRetentionDate) {
        this.specimenRetentionDate = specimenRetentionDate;
    }

    public String getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(String applyUser) {
        this.applyUser = StringUtils.trimToNull(applyUser);
    }

    public LocalDateTime getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(LocalDateTime applyDate) {
        this.applyDate = applyDate;
    }

    public Boolean getAppleType() {
        return appleType;
    }

    public void setAppleType(Boolean appleType) {
        this.appleType = appleType;
    }

    public Boolean getPreBlood() {
        return preBlood;
    }

    public void setPreBlood(Boolean preBlood) {
        this.preBlood = preBlood;
    }

    public String getDrawBloodUserName() {
        return drawBloodUserName;
    }

    public void setDrawBloodUserName(String drawBloodUserName) {
        this.drawBloodUserName = drawBloodUserName;
    }

    public String getApplyUserName() {
        return applyUserName;
    }

    public void setApplyUserName(String applyUserName) {
        this.applyUserName = applyUserName;
    }

    public Boolean getTransfusionHistory() {
        return transfusionHistory;
    }

    public void setTransfusionHistory(Boolean transfusionHistory) {
        this.transfusionHistory = transfusionHistory;
    }

    public Boolean getPregnancyHistory() {
        return pregnancyHistory;
    }

    public void setPregnancyHistory(Boolean pregnancyHistory) {
        this.pregnancyHistory = pregnancyHistory;
    }

    public Boolean getAllergicHistoryFlag() {
        return allergicHistoryFlag;
    }

    public void setAllergicHistoryFlag(Boolean allergicHistoryFlag) {
        this.allergicHistoryFlag = allergicHistoryFlag;
    }

    public String getSeniorPhysician() {
        return seniorPhysician;
    }

    public void setSeniorPhysician(String seniorPhysician) {
        this.seniorPhysician = seniorPhysician;
    }

    public String getSeniorPhysicianName() {
        return seniorPhysicianName;
    }

    public void setSeniorPhysicianName(String seniorPhysicianName) {
        this.seniorPhysicianName = seniorPhysicianName;
    }

    public String getSeniorPhysicianOpinion() {
        return seniorPhysicianOpinion;
    }

    public void setSeniorPhysicianOpinion(String seniorPhysicianOpinion) {
        this.seniorPhysicianOpinion = seniorPhysicianOpinion;
    }

    public String getPreInfusionBloodType() {
        return preInfusionBloodType;
    }

    public void setPreInfusionBloodType(String preInfusionBloodType) {
        this.preInfusionBloodType = preInfusionBloodType;
    }
}