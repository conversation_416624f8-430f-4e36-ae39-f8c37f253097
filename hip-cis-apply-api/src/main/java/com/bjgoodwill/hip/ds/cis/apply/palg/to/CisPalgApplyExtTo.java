package com.bjgoodwill.hip.ds.cis.apply.palg.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisPalgExtTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "病理申请单扩展表")
public class CisPalgApplyExtTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -4242855444935207400L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "申请单ID")
    private String applyId;
    @Schema(description = "病理扩展类型")
    private CisPalgExtTypeEnum palgExtType;
    @Schema(description = "手术术式编码")
    private String operationCode;
    @Schema(description = "手术术式名称")
    private String operationName;
    @Schema(description = "手术医生")
    private String operationDoctor;
    @Schema(description = "手术医生名称")
    private String operationDoctorName;
    @Schema(description = "手术时间")
    private LocalDateTime operationDate;
    @Schema(description = "手术室")
    private String operationRoom;
    @Schema(description = "手术室名称")
    private String operationRoomName;
    @Schema(description = "术中快速病理")
    private Boolean intraoperativelyPalg;
    @Schema(description = "手术所见")
    private String operationSeen;
    @Schema(description = "发现日期")
    private LocalDateTime discoveryDate;
    @Schema(description = "肿瘤-部位")
    private String humanOrgans;
    @Schema(description = "肿瘤-大小")
    private String tumorSize;
    @Schema(description = "肿瘤-形状")
    private String tumorShape;
    @Schema(description = "肿瘤-活动度")
    private String tumorMobility;
    @Schema(description = "肿瘤-坚度")
    private String tumorFirmness;
    @Schema(description = "肿瘤-生长速度")
    private String tumorGrowthRate;
    @Schema(description = "转移或可疑的转移")
    private Boolean tumorTransfer;
    @Schema(description = "其他")
    private String tumorOther;
    @Schema(description = "月经史-初经（岁）")
    private Integer firstMenstruation;
    @Schema(description = "月经史-周期（天）")
    private Integer cycle;
    @Schema(description = "月经史-末次月经时间")
    private LocalDateTime lastMenstrualDate;
    @Schema(description = "月经史-绝经标志")
    private Boolean menopauseFlag;
    @Schema(description = "妊娠生产史-怀孕")
    private Integer pregnant;
    @Schema(description = "妊娠生产史-生产")
    private Integer produce;
    @Schema(description = "妊娠生产史-流产")
    private Integer abortion;
    @Schema(description = "妊娠生产史-末次产期")
    private LocalDateTime lastDeliveryDate;
    @Schema(description = "妊娠生产史-治疗经过")
    private String treatmentElapse;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public CisPalgExtTypeEnum getPalgExtType() {
        return palgExtType;
    }

    public void setPalgExtType(CisPalgExtTypeEnum palgExtType) {
        this.palgExtType = palgExtType;
    }

    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getOperationDoctor() {
        return operationDoctor;
    }

    public void setOperationDoctor(String operationDoctor) {
        this.operationDoctor = operationDoctor;
    }

    public String getOperationDoctorName() {
        return operationDoctorName;
    }

    public void setOperationDoctorName(String operationDoctorName) {
        this.operationDoctorName = operationDoctorName;
    }

    public LocalDateTime getOperationDate() {
        return operationDate;
    }

    public void setOperationDate(LocalDateTime operationDate) {
        this.operationDate = operationDate;
    }

    public String getOperationRoom() {
        return operationRoom;
    }

    public void setOperationRoom(String operationRoom) {
        this.operationRoom = operationRoom;
    }

    public String getOperationRoomName() {
        return operationRoomName;
    }

    public void setOperationRoomName(String operationRoomName) {
        this.operationRoomName = operationRoomName;
    }

    public Boolean getIntraoperativelyPalg() {
        return intraoperativelyPalg;
    }

    public void setIntraoperativelyPalg(Boolean intraoperativelyPalg) {
        this.intraoperativelyPalg = intraoperativelyPalg;
    }

    public String getOperationSeen() {
        return operationSeen;
    }

    public void setOperationSeen(String operationSeen) {
        this.operationSeen = operationSeen;
    }

    public LocalDateTime getDiscoveryDate() {
        return discoveryDate;
    }

    public void setDiscoveryDate(LocalDateTime discoveryDate) {
        this.discoveryDate = discoveryDate;
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    public String getTumorSize() {
        return tumorSize;
    }

    public void setTumorSize(String tumorSize) {
        this.tumorSize = tumorSize;
    }

    public String getTumorShape() {
        return tumorShape;
    }

    public void setTumorShape(String tumorShape) {
        this.tumorShape = tumorShape;
    }

    public String getTumorMobility() {
        return tumorMobility;
    }

    public void setTumorMobility(String tumorMobility) {
        this.tumorMobility = tumorMobility;
    }

    public String getTumorFirmness() {
        return tumorFirmness;
    }

    public void setTumorFirmness(String tumorFirmness) {
        this.tumorFirmness = tumorFirmness;
    }

    public String getTumorGrowthRate() {
        return tumorGrowthRate;
    }

    public void setTumorGrowthRate(String tumorGrowthRate) {
        this.tumorGrowthRate = tumorGrowthRate;
    }

    public Boolean getTumorTransfer() {
        return tumorTransfer;
    }

    public void setTumorTransfer(Boolean tumorTransfer) {
        this.tumorTransfer = tumorTransfer;
    }

    public String getTumorOther() {
        return tumorOther;
    }

    public void setTumorOther(String tumorOther) {
        this.tumorOther = tumorOther;
    }

    public Integer getFirstMenstruation() {
        return firstMenstruation;
    }

    public void setFirstMenstruation(Integer firstMenstruation) {
        this.firstMenstruation = firstMenstruation;
    }

    public Integer getCycle() {
        return cycle;
    }

    public void setCycle(Integer cycle) {
        this.cycle = cycle;
    }

    public LocalDateTime getLastMenstrualDate() {
        return lastMenstrualDate;
    }

    public void setLastMenstrualDate(LocalDateTime lastMenstrualDate) {
        this.lastMenstrualDate = lastMenstrualDate;
    }

    public Boolean getMenopauseFlag() {
        return menopauseFlag;
    }

    public void setMenopauseFlag(Boolean menopauseFlag) {
        this.menopauseFlag = menopauseFlag;
    }

    public Integer getPregnant() {
        return pregnant;
    }

    public void setPregnant(Integer pregnant) {
        this.pregnant = pregnant;
    }

    public Integer getProduce() {
        return produce;
    }

    public void setProduce(Integer produce) {
        this.produce = produce;
    }

    public Integer getAbortion() {
        return abortion;
    }

    public void setAbortion(Integer abortion) {
        this.abortion = abortion;
    }

    public LocalDateTime getLastDeliveryDate() {
        return lastDeliveryDate;
    }

    public void setLastDeliveryDate(LocalDateTime lastDeliveryDate) {
        this.lastDeliveryDate = lastDeliveryDate;
    }

    public String getTreatmentElapse() {
        return treatmentElapse;
    }

    public void setTreatmentElapse(String treatmentElapse) {
        this.treatmentElapse = treatmentElapse;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisPalgApplyExtTo other = (CisPalgApplyExtTo) obj;
        return Objects.equals(id, other.id);
    }
}