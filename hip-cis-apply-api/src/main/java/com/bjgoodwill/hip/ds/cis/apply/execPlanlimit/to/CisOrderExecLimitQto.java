package com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to;

import com.bjgoodwill.hip.business.util.common.to.BaseTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "执行单第三方状态操作限制")
public class CisOrderExecLimitQto extends BaseTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -4102066215398766037L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "第三方状态名称")
    private String thirdStatus;
    @Schema(description = "false 允许不执行")
    private Boolean noExecFlag;
    @Schema(description = "false 允许取消执行")
    private Boolean cancelExecFlag;
    @Schema(description = "false 允许退费")
    private String refundsFlag;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getThirdStatus() {
        return thirdStatus;
    }

    public void setThirdStatus(String thirdStatus) {
        this.thirdStatus = thirdStatus;
    }

    public Boolean getNoExecFlag() {
        return noExecFlag;
    }

    public void setNoExecFlag(Boolean noExecFlag) {
        this.noExecFlag = noExecFlag;
    }

    public Boolean getCancelExecFlag() {
        return cancelExecFlag;
    }

    public void setCancelExecFlag(Boolean cancelExecFlag) {
        this.cancelExecFlag = cancelExecFlag;
    }

    public String getRefundsFlag() {
        return refundsFlag;
    }

    public void setRefundsFlag(String refundsFlag) {
        this.refundsFlag = refundsFlag;
    }
}