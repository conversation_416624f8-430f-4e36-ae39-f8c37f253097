package com.bjgoodwill.hip.ds.cis.apply.book.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "申请单预约")
public class ApplyBookQto extends BaseTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -5348954325109155605L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "预约状态，开立为NEW，预约成功为ACTIVE")
    private CisStatusEnum appointsStatus;
    @Schema(description = "预约开始时间")
    private LocalDateTime appointsStartDate;
    @Schema(description = "预约结束时间")
    private String appointsEndDate;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public CisStatusEnum getAppointsStatus() {
        return appointsStatus;
    }

    public void setAppointsStatus(CisStatusEnum appointsStatus) {
        this.appointsStatus = appointsStatus;
    }

    public LocalDateTime getAppointsStartDate() {
        return appointsStartDate;
    }

    public void setAppointsStartDate(LocalDateTime appointsStartDate) {
        this.appointsStartDate = appointsStartDate;
    }

    public String getAppointsEndDate() {
        return appointsEndDate;
    }

    public void setAppointsEndDate(String appointsEndDate) {
        this.appointsEndDate = appointsEndDate;
    }
}