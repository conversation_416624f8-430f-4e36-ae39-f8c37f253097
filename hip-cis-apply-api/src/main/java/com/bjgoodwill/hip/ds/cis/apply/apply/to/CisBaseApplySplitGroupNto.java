package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetialNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-01-16 15:18
 * @className: CisBaseApplySplitGroupNto
 * @description:
 **/
@Schema(description = "申请单拆组")
public class CisBaseApplySplitGroupNto extends BaseNto implements Serializable {
    @Schema(description = "原申请单ID")
    private String sourceApplyId;

    @Schema(description = "拆分出的新申请单")
    private List<ApplyWithDetialNto> details;

    @NotBlank(message = "原申请单ID不能为空！")
    public String getSourceApplyId() {
        return sourceApplyId;
    }

    public void setSourceApplyId(String sourceApplyId) {
        this.sourceApplyId = sourceApplyId;
    }

    @NotNull(message = "明细不能为空！")
    public List<ApplyWithDetialNto> getDetails() {
        return details;
    }

    public void setDetails(List<ApplyWithDetialNto> details) {
        this.details = details;
    }
}
