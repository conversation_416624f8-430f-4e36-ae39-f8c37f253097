package com.bjgoodwill.hip.ds.cis.apply.detail.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-24 17:00
 * @className: DetailNto
 * @description:
 **/
//@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class DetailNto extends BaseNto {

    @Schema(description = "标识")
    private String id;

    @Schema(description = "序号")
    private Double sortNo;
    @Schema(description = "就诊流水号")
    private String visitCode;

    private String serviceItemCode;

    private String extTypeCode;

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    @NotNull(message = "就诊流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    public void setHipId() {
        if (StringUtils.isEmpty(this.getId())) {
            this.setId(HIPIDUtil.getNextIdString());
        }
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getExtTypeCode() {
        return extTypeCode;
    }

    public void setExtTypeCode(String extTypeCode) {
        this.extTypeCode = extTypeCode;
    }

    //    public String getMinimal_class() {
//        return "." + this.getClass().getSimpleName();
//    }
    @JsonProperty("@class")
    public String getClassName() {
        return getClass().getName();
    }
}