package com.bjgoodwill.hip.ds.cis.apply.execPlan.service;

import com.bjgoodwill.hip.business.util.cis.common.BaseDictElementTo;
import com.bjgoodwill.hip.business.util.cis.common.CisOrgCommonNto;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.drug.enums.DrugIpdDataStatusEnum;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-08 17:23
 * @className: CisOrderExecPlanService
 * @description:
 **/
@Tag(name = "执行单领域服务", description = "执行单领域服务")
public interface CisOrderExecPlanService {

    @Operation(summary = "P0申请单执行")
    @PutMapping("/cisOrderExecPlan/exec/{id:.+}")
    void executeCisOrderExecPlan(@PathVariable("id") String id, @RequestBody @Valid CisOrgCommonNto cisOrgCommonNto);

    @Operation(summary = "P0申请单取消执行")
    @PutMapping("/cisOrderExecPlan/cancel/{id:.+}")
    void cancelCisOrderExecPlan(@PathVariable("id") String id, @RequestBody @Valid CisOrgCommonNto cisOrgCommonNto);

    @Operation(summary = "P0申请单批量执行")
    @PutMapping("/cisOrderExecPlan/exec/batch")
    void executeCisOrderExecPlanBatch(@RequestParam(required = false) String deptNurseCode, @RequestParam List<String> execIds, @RequestBody @Valid CisOrgCommonNto cisOrgCommonNto);

    @Operation(summary = "P0申请单批量取消执行")
    @PutMapping("/cisOrderExecPlan/cancelexec/batch")
    void cancelExecuteCisOrderExecPlanBatch(@RequestParam(required = false) String deptNurseCode, @RequestParam List<String> execIds, @RequestBody @Valid CisOrgCommonNto cisOrgCommonNto);

    @Operation(summary = "P0申请单批量不执行")
    @PutMapping("/cisOrderExecPlan/noexec/batch")
    void noExecuteCisOrderExecPlanBatch(@RequestParam(required = false) String deptNurseCode, @RequestParam List<String> execIds, @RequestBody @Valid CisOrgCommonNto cisOrgCommonNto);

    @Operation(summary = "P0申请单批量重新执行")
    @PutMapping("/cisOrderExecPlan/anewexec/batch")
    void anewExecuteCisOrderExecPlanBatch(@RequestParam(required = false) String deptNurseCode, @RequestParam List<String> execIds, @RequestBody @Valid CisOrgCommonNto cisOrgCommonNto);

    @Operation(summary = "P0转科/出院医嘱执行")
    @PutMapping("/cisOrderExecPlan/special/exe")
    String specialExecuteCisOrderExecPlanBatch(@RequestParam("visitCode") String visitCode, @RequestParam("systemType") SystemTypeEnum systemType, @RequestBody @Valid CisOrgCommonNto cisOrgCommonNto);

    @Operation(summary = "P0转科/出院医嘱取消执行")
    @PutMapping("/cisOrderExecPlan/special/cancelExe")
    String specialCancelExecuteCisOrderExecPlanBatch(@RequestParam("visitCode") String visitCode, @RequestParam("systemType") SystemTypeEnum systemType, @RequestBody @Valid CisOrgCommonNto cisOrgCommonNto);

//    @Operation(summary = "执行单执行页面查询。")
//    @GetMapping("/cisOrderExecPlan/query")
//    List<CisOrderExecPlanTo> getExecedCisOrderExecPlan(@RequestBody CisOrderExecPlanNurseQto qto);

    @Operation(summary = "P0执行单补费")
    @PutMapping("/cisOrderExecPlan/{id:.+}/repair-fee")
    CisOrderExecPlanChargeTo repairFeeCisOrderExecPlan(@PathVariable("id") String id, @RequestBody @Valid CisOrderExecPlanChargeNto cisOrderExecPlanChargeNto);

    @Operation(summary = "P0执行单费用明细-批量保存")
    @PutMapping("/cisOrderExecPlan/cisOrderExecPlanCharge/{id:.+}/saves")
    List<CisOrderExecPlanChargeTo> createCisOrderExecPlanChargeBatch(@PathVariable("id") String id, @RequestBody @Valid List<CisOrderExecPlanChargeNto> cisOrderExecPlanChargeNtos);

    @Operation(summary = "P0执行单费用明细-批量修改")
    @PutMapping("/cisOrderExecPlan/cisOrderExecPlanCharge/update")
    List<CisOrderExecPlanChargeTo> updateCisOrderExecPlanChargeBatch(@RequestBody @Valid List<CisOrderExecPlanChargeEto> cisOrderExecPlanChargeEtos);

    @Operation(summary = "P0执行单费用明细删除")
    @DeleteMapping("/cisOrderExecPlan/cisOrderExecPlanCharge/delete/{id:.+}")
    void deleteCisOrderExecPlanCharge(@PathVariable("id") String id);

    @Operation(summary = "P0根据执行id查询执行单费用明细")
    @GetMapping("/cisOrderExecPlan/cisOrderExecPlanCharge/query/execId/{id:.+}")
    List<CisOrderExecPlanChargeTo> findCisOrderExecPlanChargeByExecId(@PathVariable("id") String execId);

    @Operation(summary = "P0根据申请单id查询执行单费用明细")
    @GetMapping("/cisOrderExecPlan/cisOrderExecPlanCharge/query/by-applyIds")
    List<CisOrderExecPlanChargeTo> findCisOrderExecPlanChargeByOrderId(@RequestParam List<String> applyIds);

    @Operation(summary = "P0查询患者未执行的执行单")
    @PutMapping("/cisOrderExecPlan/{visit-code:.+}/noexec")
    List<CisOrderExecPlanTo> findNoExecCisOrderExecPlan(@PathVariable("visit-code") String visitCode);

    @Operation(summary = "P0查询患者执行单根据状态")
    @PutMapping("/cisOrderExecPlan/{visit-code:.+}/by-visitCode-statusCode")
    List<CisOrderExecPlanTo> findExecCisOrderExecPlanByVisitCodeAndStatusCode(@PathVariable("visit-code") String visitCode, @RequestParam("statusCode") CisStatusEnum statusCode);

    @Operation(summary = "P0根据医嘱id查询执行单")
    @GetMapping("/cisOrderExecPlan/query/orderId/{order-id:.+}")
    List<CisOrderExecPlanTo> findCisOrderExecPlanByOrderId(@PathVariable("order-id") String orderId);

    @Operation(summary = "P0根据医嘱id查询执行单")
    @GetMapping("/cisOrderExecPlan/query/ids")
    List<CisOrderExecPlanTo> findCisOrderExecPlanInOrderIds(@RequestParam("ids") List<String> ids);

    @Operation(summary = "P0根据id查询执行单")
    @PostMapping("/cisOrderExecPlan/query/exeIds")
    List<CisOrderExecPlanTo> findCisOrderExecPlanInExeIds(@RequestBody List<String> exeIds);

    @Operation(summary = "P0查询患者未执行的执行单")
    @PutMapping("/cisOrderExecPlan/{visit-code:.+}/nocharge")
    List<CisOrderExecPlanTo> findNoChargeCisOrderExecPlan(@PathVariable("visit-code") String visitCode, @RequestParam Boolean isCharge);

    @Operation(summary = "P0执行单执行页面查询。")
    @GetMapping("/cisOrderExecPlan/query")
    List<CisOrderExecPlanTo> getExecedCisOrderExecPlan(@ParameterObject @SpringQueryMap CisOrderExecPlanNurseQto qto);

    @Operation(summary = "P0修复执行单计费信息。")
    @PutMapping("/cisOrderExecPlan/charge/update")
    void updateCisOrderExecPlanCharge(@RequestBody List<CisOrderExecPlanChangeBackEto> cisOrderExecPlanChangeBackEtos);

    @Operation(summary = "P0根据visitCode查询执行单")
    @GetMapping("/cisOrderExecPlan/query/visitCode/{visitCode:.+}")
    List<CisOrderExecPlanTo> findByVisitCode(@PathVariable("visitCode") String visitCode);


    @Operation(summary = "P0执行单补费,通过执行科室补费，没有相同执行科室的执行单，创建一个新的执行单")
    @PutMapping("/cisOrderExecPlan/repair-fee")
    void repairFeeCisOrderExecPlans(@RequestBody List<CisOrderExecPlanChargeNto> cisOrderExecPlanChargeNtos);

    @Operation(summary = "P0双签")
    @PutMapping("/cisOrderExecPlan/double-sign/{id:.+}")
    CisOrderExecPlanTo DoubleSign(@PathVariable("id") String id, @RequestParam String stStaffA, @RequestParam String stStaffB);

    @Operation(summary = "P0领药单发送查询执行单。")
    @GetMapping("/cisOrderExecPlan/prescription-query")
    List<CisOrderExecPlanTo> getCisOrderExecPlanForPrescription(@RequestParam List<String> visitCodes,
                                                                @RequestParam String deptNurseCode,
                                                                @RequestParam(required = false) String receiveOrg,
                                                                @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime execPlanDate);

    @Operation(summary = "P0患者会诊列表。")
    @GetMapping("/cisOrderExecPlan/consultation")
    List<CisOrderExecPlanTo> findConsultationApply(@RequestParam LocalDateTime dateTime);

    @Operation(summary = "P0更新执行单是否发送标识")
    @PutMapping("/cisOrderExecPlan/exec/updateIsSend")
    void updateCisOrderExecPlanIsSend(@RequestParam List<String> execIds, @RequestParam Boolean isSend);

    @Operation(summary = "查询执行单根据状态")
    @PutMapping("/cisOrderExecPlan/noExes")
    List<CisOrderExecPlanTo> findCisOrderExecPlanNoExes(@RequestParam("statusCode") CisStatusEnum statusCode,
                                                        @RequestParam("beginDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime beginDate,
                                                        @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate);

    @Operation(summary = "P0皮试结果录入")
    @PutMapping("/cisOrderExecPlan/skin-results/input/{id:.+}")
    void skinResultsInput(@PathVariable("id") String id, @RequestBody CisOrderExecPlanEto eto);

    @Operation(summary = "患者费用查询执行单")
    @PutMapping("/cisOrderExecPlan/patient-fee")
    List<CisOrderExecPlanTo> findCisOrderExecPlanPatientFee(@ParameterObject @SpringQueryMap CisOrderExecPlanNurseQto qto);

    @Operation(summary = "P0查询患者医技科室内的执行记录")
    @GetMapping("/cisOrderExecPlan/exec/query")
    List<CisOrderExecPlanGroupTo> findCisOrderExecPlanGroupByExecDeptCode(@ParameterObject @SpringQueryMap CisOrderExecPlanMtQto qto);

    @Operation(summary = "P0根据查询条件患者医技科室内的医嘱执行档进行分页查询。")
    @GetMapping("/cisOrderExecPlan/exec/listpages")
    GridResultSet<CisOrderExecPlanMtTo> findCisOrderExecPlanMtPages(@ParameterObject @SpringQueryMap CisOrderExecPlanMtQto qto);

    @Operation(summary = "P0根据执行科室编码返回当前可用的开方科室")
    @GetMapping("/cisOrderExecPlan/query/createOrg")
    List<BaseDictElementTo> findCreateOrgByExecDeptCode(@ParameterObject @SpringQueryMap CisOrderExecPlanMtQto qto);

    @Operation(summary = "P0根据执行科室，开方科室申请单状态和时间，查询患者列表")
    @GetMapping("/cisOrderExecPlan/query/patient")
    GridResultSet<BaseDictElementTo> findPatientByExecDeptCode(@ParameterObject @SpringQueryMap CisOrderExecPlanMtQto qto);


    @Operation(summary = "P1根据医嘱id,查询出带有煎药费的执行单")
    @GetMapping("/cisOrderExecPlan/decoction/{order-id:.+}")
    List<CisOrderExecPlanTo> findDecoctionCisOrderExecPlanByOrderId(@PathVariable("order-id") String orderId);

    @Operation(summary = "P0修改药品执行记录发药状态")
    @PutMapping("/cisOrderExecPlan/{id:.+}/drugInOutType")
    void updateDrugInoutType(@PathVariable("id") String id, @RequestParam DrugIpdDataStatusEnum drugInoutType, @RequestBody @Valid CisOrgCommonNto cisOrgCommonNto);

    @Operation(summary = "P0批量修改药品执行记录发药状态")
    @PutMapping("/cisOrderExecPlan/mul/drugInOutType")
    void updateMulDrugInoutType(@RequestParam List<String> ids, @RequestParam DrugIpdDataStatusEnum drugInoutType, @RequestBody @Valid CisOrgCommonNto cisOrgCommonNto);

//    @Operation(summary = "P0更新状态发消息接口")
//    @PutMapping("/cisOrderExecPlan/mul/send")
//    void sendMessaging(@RequestBody CisOrderStatueEto cisOrderStatueEto);

    @Operation(summary = "P0执行单执行页面查询他科执行非药品数据。")
    @GetMapping("/cisOrderExecPlan/queryOtherExe")
    List<CisOrderExecPlanTo> getOtherExecedCisOrderExecPlan(@ParameterObject @SpringQueryMap CisOrderExecPlanNurseQto qto);

    @Operation(summary = "P0更新执行单打印标识")
    @PostMapping("/cisOrderExecPlan/exec/updateIsPrint")
    void updateCisOrderExecPlanIsPrint(@RequestBody List<String> execIds);

    @Operation(summary = "P0按照执行单明细平铺返回。")
    @GetMapping("/cisOrderExecPlan/exec/detail/queryCisOrderExecPlanByIds")
    List<CisOrderExecPlanCustomTo> queryCisOrderExecPlanByIds(@RequestParam("ids") List<String> ids);

    @Operation(summary = "P0 获取创建时间大于参数的主单执行单。")
    @GetMapping("/cisOrderExecPlan/queryMainExeWithDate")
    CisOrderExecPlanWithDetialTo queryCisOrderExecPlanByCreateDate(@RequestParam LocalDateTime createDate);

    @Operation(summary = "P0根据医嘱id查询已结算执行单")
    @GetMapping("/cisOrderExecPlan/query/by-orderIds")
    List<CisOrderExecPlanTo> queryCisOrderExecPlanByOrderIds(@RequestParam List<String> orderIds);
}
