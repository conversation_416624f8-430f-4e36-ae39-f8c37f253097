package com.bjgoodwill.hip.ds.cis.apply.diag.to;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "危急值查询申请单诊断")
public class ApplyDiagnosisCustomTo {
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "抽象父类标识")
    private String cisBaseApplyId;
    @Schema(description = "标识")
    private String id;
    @Schema(description = "diagCode")
    private String diagCode;
    @Schema(description = "诊断名称")
    private String diagName;

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getCisBaseApplyId() {
        return cisBaseApplyId;
    }

    public void setCisBaseApplyId(String cisBaseApplyId) {
        this.cisBaseApplyId = cisBaseApplyId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDiagCode() {
        return diagCode;
    }

    public void setDiagCode(String diagCode) {
        this.diagCode = diagCode;
    }

    public String getDiagName() {
        return diagName;
    }

    public void setDiagName(String diagName) {
        this.diagName = diagName;
    }
}