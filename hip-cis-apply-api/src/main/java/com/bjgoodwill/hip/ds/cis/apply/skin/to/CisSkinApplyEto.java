package com.bjgoodwill.hip.ds.cis.apply.skin.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "skin")
public class CisSkinApplyEto extends CisBaseApplyEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -188512053056028029L;

    @Schema(description = "每次剂量")
    private Double dosage;
    @Schema(description = "剂量单位")
    private String dosageUnit;
    @Schema(description = "药房")
    private String receiveOrg;
    @Schema(description = "药房名称")
    private String receiveOrgName;

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = StringUtils.trimToNull(dosageUnit);
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = StringUtils.trimToNull(receiveOrg);
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = StringUtils.trimToNull(receiveOrgName);
    }
}