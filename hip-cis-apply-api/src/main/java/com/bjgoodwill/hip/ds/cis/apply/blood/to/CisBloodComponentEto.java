package com.bjgoodwill.hip.ds.cis.apply.blood.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "血液成分从表")
public class CisBloodComponentEto extends DetailEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4520354108096063795L;
    @Schema(description = "标识ID")
    protected String id;
    @Schema(description = "申请输注血液成分")
    private String bloodComponent;
    @Schema(description = "申请输注血量")
    private Double bloodQuantity;
    @Schema(description = "输注血量单位 默认ml")
    private String bloodQuantityUnit;
    @Schema(description = "医嘱处理标记:1己处理，0未处理")
    private Boolean orderResultFlag;
    @Schema(description = "serviceItemCode")
    private String serviceItemCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBloodComponent() {
        return bloodComponent;
    }

    public void setBloodComponent(String bloodComponent) {
        this.bloodComponent = StringUtils.trimToNull(bloodComponent);
    }

    public Double getBloodQuantity() {
        return bloodQuantity;
    }

    public void setBloodQuantity(Double bloodQuantity) {
        this.bloodQuantity = bloodQuantity;
    }

    public String getBloodQuantityUnit() {
        return bloodQuantityUnit;
    }

    public void setBloodQuantityUnit(String bloodQuantityUnit) {
        this.bloodQuantityUnit = StringUtils.trimToNull(bloodQuantityUnit);
    }

    public Boolean getOrderResultFlag() {
        return orderResultFlag;
    }

    public void setOrderResultFlag(Boolean orderResultFlag) {
        this.orderResultFlag = orderResultFlag;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }
}