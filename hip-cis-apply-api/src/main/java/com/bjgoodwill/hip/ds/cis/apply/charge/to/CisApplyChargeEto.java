package com.bjgoodwill.hip.ds.cis.apply.charge.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import com.bjgoodwill.hip.business.util.econ.enums.SystemItemClassEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "CisApplyCharge")
public class CisApplyChargeEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -8799045564888898714L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "收费项目编码")
    private String priceItemCode;
    @Schema(description = "收费项目名称")
    private String priceItemName;
    @Schema(description = "规格")
    private String packageSpec;
    @Schema(description = "执行科室")
    private String executeOrgCode;
    @Schema(description = "执行科室名称")
    private String executeOrgName;
    @Schema(description = "单价")
    private BigDecimal price;
    @Schema(description = "单位")
    private String unit;
    @Schema(description = "单位名称")
    private String unitName;
    @Schema(description = "数量")
    private Double num;
    @Schema(description = "金额")
    private BigDecimal chageAmount;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "系统项目分类")
    private SystemItemClassEnum systemItemClass;

    @NotBlank(message = "收费项目编码不能为空！")
    public String getPriceItemCode() {
        return priceItemCode;
    }

    public void setPriceItemCode(String priceItemCode) {
        this.priceItemCode = StringUtils.trimToNull(priceItemCode);
    }

    public String getPackageSpec() {
        return packageSpec;
    }

    public void setPackageSpec(String packageSpec) {
        this.packageSpec = StringUtils.trimToNull(packageSpec);
    }

    @NotBlank(message = "执行科室不能为空！")
    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    public void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = StringUtils.trimToNull(executeOrgCode);
    }

    @NotNull(message = "总价不能为空！")
    public BigDecimal getChageAmount() {
        return chageAmount;
    }

    public void setChageAmount(BigDecimal chageAmount) {
        this.chageAmount = chageAmount;
    }

    @NotNull(message = "版本不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getPriceItemName() {
        return priceItemName;
    }

    public void setPriceItemName(String priceItemName) {
        this.priceItemName = priceItemName;
    }

    @NotNull(message = "单价不能为空！")
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @NotBlank(message = "单位不能为空！")
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    @NotNull(message = "数量不能为空！")
    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @NotNull(message = "系统项目分类不能为空！")
    public SystemItemClassEnum getSystemItemClass() {
        return systemItemClass;
    }

    public void setSystemItemClass(SystemItemClassEnum systemItemClass) {
        this.systemItemClass = systemItemClass;
    }
}