package com.bjgoodwill.hip.ds.cis.apply.dgimg.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "检查申请单")
public class CisDgimgApplyTo extends CisBaseApplyTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -5851274141446729342L;

    @Schema(description = "检查注意事项")
    private String precautions;
    @Schema(description = "病历及查体摘要")
    private String medrecordAndExamabstract;
    @Schema(description = "体格及其他检查")
    private String physiqueAndExam;
    @Schema(description = "分类")
    private String dgimgClass;
    @Schema(description = "分类名称")
    private String dgimgClassName;
    @Schema(description = "子分类")
    private String dgimgSubClass;
    @Schema(description = "子分类名称")
    private String dgimgSubClassName;
    @Schema(description = "相关辅检")
    private String auxiliaryInspection;
    @Schema(description = "检查目的")
    private String checkPurpose;
    @Schema(description = "申请单预约标识")
    private String applyBookId;
    @Schema(description = "报告pdf地址")
    private String reportPdfUrl;
    @Schema(description = "既往病理检查结果")
    private String previousPathologicalExamin;

    @Schema(description = "检查申请单明细")
    private List<CisDgimgApplyDetailTo> cisDgimgApplyDetails;

    @Schema(description = "设备类型")
    private String deviceType;

    @Schema(description = "设备类型名称")
    private String deviceTypeName;

    @Schema(description = "是否过敏史")
    private Boolean allergicHistoryFlag;

    @Schema(description = "是否职业病史")
    private Boolean occupationalDiseasesFlag;

    @Schema(description = "临床病史")
    private String clinicalHistory;

    @Schema(description = "是否传染病史")
    private Boolean contagiousDiseaseHistoryFlag;

    @Schema(description = "患者病史")
    private CisMedicalHistoryTo cisMedicalHistoryTo;

    public List<CisDgimgApplyDetailTo> getCisDgimgApplyDetails() {
        return cisDgimgApplyDetails;
    }

    public void setCisDgimgApplyDetails(List<CisDgimgApplyDetailTo> cisDgimgApplyDetails) {
        this.cisDgimgApplyDetails = cisDgimgApplyDetails;
    }

    public String getPrecautions() {
        return precautions;
    }

    public void setPrecautions(String precautions) {
        this.precautions = precautions;
    }

    public String getMedrecordAndExamabstract() {
        return medrecordAndExamabstract;
    }

    public void setMedrecordAndExamabstract(String medrecordAndExamabstract) {
        this.medrecordAndExamabstract = medrecordAndExamabstract;
    }

    public String getPhysiqueAndExam() {
        return physiqueAndExam;
    }

    public void setPhysiqueAndExam(String physiqueAndExam) {
        this.physiqueAndExam = physiqueAndExam;
    }

    public String getDgimgClass() {
        return dgimgClass;
    }

    public void setDgimgClass(String dgimgClass) {
        this.dgimgClass = dgimgClass;
    }

    public String getDgimgSubClass() {
        return dgimgSubClass;
    }

    public void setDgimgSubClass(String dgimgSubClass) {
        this.dgimgSubClass = dgimgSubClass;
    }

    public String getAuxiliaryInspection() {
        return auxiliaryInspection;
    }

    public void setAuxiliaryInspection(String auxiliaryInspection) {
        this.auxiliaryInspection = auxiliaryInspection;
    }

    public String getCheckPurpose() {
        return checkPurpose;
    }

    public void setCheckPurpose(String checkPurpose) {
        this.checkPurpose = checkPurpose;
    }

    public String getApplyBookId() {
        return applyBookId;
    }

    public void setApplyBookId(String applyBookId) {
        this.applyBookId = applyBookId;
    }

    public String getReportPdfUrl() {
        return reportPdfUrl;
    }

    public void setReportPdfUrl(String reportPdfUrl) {
        this.reportPdfUrl = reportPdfUrl;
    }

    public String getPreviousPathologicalExamin() {
        return previousPathologicalExamin;
    }

    public void setPreviousPathologicalExamin(String previousPathologicalExamin) {
        this.previousPathologicalExamin = previousPathologicalExamin;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public Boolean getAllergicHistoryFlag() {
        return allergicHistoryFlag;
    }

    public void setAllergicHistoryFlag(Boolean allergicHistoryFlag) {
        this.allergicHistoryFlag = allergicHistoryFlag;
    }

    public Boolean getOccupationalDiseasesFlag() {
        return occupationalDiseasesFlag;
    }

    public void setOccupationalDiseasesFlag(Boolean occupationalDiseasesFlag) {
        this.occupationalDiseasesFlag = occupationalDiseasesFlag;
    }

    public String getClinicalHistory() {
        return clinicalHistory;
    }

    public void setClinicalHistory(String clinicalHistory) {
        this.clinicalHistory = clinicalHistory;
    }

    public Boolean getContagiousDiseaseHistoryFlag() {
        return contagiousDiseaseHistoryFlag;
    }

    public void setContagiousDiseaseHistoryFlag(Boolean contagiousDiseaseHistoryFlag) {
        this.contagiousDiseaseHistoryFlag = contagiousDiseaseHistoryFlag;
    }

    public String getDgimgClassName() {
        return dgimgClassName;
    }

    public void setDgimgClassName(String dgimgClassName) {
        this.dgimgClassName = dgimgClassName;
    }

    public String getDgimgSubClassName() {
        return dgimgSubClassName;
    }

    public void setDgimgSubClassName(String dgimgSubClassName) {
        this.dgimgSubClassName = dgimgSubClassName;
    }

    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    public CisMedicalHistoryTo getCisMedicalHistoryTo() {
        return cisMedicalHistoryTo;
    }

    public void setCisMedicalHistoryTo(CisMedicalHistoryTo cisMedicalHistoryTo) {
        this.cisMedicalHistoryTo = cisMedicalHistoryTo;
    }
}