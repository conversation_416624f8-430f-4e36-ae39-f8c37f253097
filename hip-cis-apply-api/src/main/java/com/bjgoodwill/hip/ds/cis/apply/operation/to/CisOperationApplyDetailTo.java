package com.bjgoodwill.hip.ds.cis.apply.operation.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-09-29 15:43
 * @className: CisOperationApplyDetailTo
 * @description:
 **/
@Schema(description = "手术术式")
public class CisOperationApplyDetailTo extends DetailTo implements Serializable {
    @Serial
    private static final long serialVersionUID = -3624463504315755393L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "申请单ID")
    private String applyId;
    @Schema(description = "序号")
    private Double sortNo;
    @Schema(description = "手术编码")
    private String operationCode;
    @Schema(description = "手术名称")
    private String operationName;
    @Schema(description = "项目编码")
    private String serviceItemCode;
    @Schema(description = "项目名称")
    private String serviceItemName;
    @Schema(description = "手术级别")
    private String operationLevel;
    @Schema(description = "部位")
    private String humanOrgans;
    @Schema(description = "部位名称")
    private String humanOrgansName;
    @Schema(description = "体位")
    private String decubitus;
    @Schema(description = "体位名称")
    private String decubitusName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getOperationLevel() {
        return operationLevel;
    }

    public void setOperationLevel(String operationLevel) {
        this.operationLevel = operationLevel;
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    public String getHumanOrgansName() {
        return humanOrgansName;
    }

    public void setHumanOrgansName(String humanOrgansName) {
        this.humanOrgansName = humanOrgansName;
    }

    public String getDecubitus() {
        return decubitus;
    }

    public void setDecubitus(String decubitus) {
        this.decubitus = decubitus;
    }

    public String getDecubitusName() {
        return decubitusName;
    }

    public void setDecubitusName(String decubitusName) {
        this.decubitusName = decubitusName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOperationApplyDetailTo other = (CisOperationApplyDetailTo) obj;
        return Objects.equals(id, other.id);
    }


}