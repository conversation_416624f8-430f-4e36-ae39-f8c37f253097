package com.bjgoodwill.hip.ds.cis.apply.operation.service;

import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyService;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "手术申请领域服务", description = "手术申请领域服务")
public interface CisOperationApplyService extends CisBaseApplyService {

    @Operation(summary = "根据唯一标识返回手术申请。")
    @GetMapping("/cisOperationApplies/{id:.+}")
    CisOperationApplyTo getCisOperationApplyById(@PathVariable("id") String id);

    @Operation(summary = "根据唯一标识返回手术申请、明细、费用、诊断、执行计划。")
    @GetMapping("/cisOperationApplies/getall/{id:.+}")
    CisOperationApplyTo getCisOperationApplyAllById(@PathVariable("id") String id);

    @Operation(summary = "创建手术申请。")
    @PostMapping("/cisOperationApplies")
    CisOperationApplyTo createCisOperationApply(@RequestBody @Valid CisOperationApplyNto cisOperationApplyNto);

    @Operation(summary = "P0根据唯一标识修改手术申请。")
    @PutMapping("/cisOperationApplies/{id:.+}")
    void updateCisOperationApply(@PathVariable("id") String id, @RequestBody @Valid CisOperationApplyEto cisOperationApplyEto);

    @Operation(summary = "P0根据唯一标识删除手术申请。")
    @DeleteMapping("/cisOperationApplies/{id:.+}")
    void deleteCisOperationApply(@PathVariable("id") String id);

    @Operation(summary = "P0创建手术术式。")
    @PostMapping("/cisOperationApplies/{applyID}/cisOperationApplyDetails")
    CisOperationApplyDetailTo createCisOperationApplyDetail(@PathVariable("applyID") String applyID, @RequestBody @Valid CisOperationApplyDetailNto cisOperationApplyDetailNto);

    @Operation(summary = "P0根据唯一标识修改创建手术术式。")
    @PutMapping("/cisOperationApplies/xId/cisOperationApplyDetails/{id:.+}")
    void updateCisOperationApplyDetail(@PathVariable("id") String id, @RequestBody @Valid CisOperationApplyDetailEto cisSpcobsApplyDetailEto);

    @Operation(summary = "P0根据唯一标识删除创建手术术式。")
    @DeleteMapping("/cisOperationApplies/xId/cisOperationApplyDetails/{id:.+}")
    void deleteCisOperationApplyDetail(@PathVariable("id") String id);

    @Operation(summary = "P0根据申请单Id返回手术申请单明细。")
    @GetMapping("/cisOperationApplies/{applyId}/cisOperationApplyDetails")
    List<CisOperationApplyDetailTo> getCisOperationApplyDetailByApplyId(@PathVariable("applyId") String applyId);
}