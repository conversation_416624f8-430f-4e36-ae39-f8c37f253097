package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * @program: cis-base
 * @author: xdguo
 * @create: 2025-06-20 10:24
 * @className: CisOrderExecPlanWithChargeEto
 * @description:
 **/
@Schema(description = "未计费医嘱修改费用接口")
public class CisOrderExecPlanWithChargeEto extends BaseEto {

    @Schema(description = "执行单ID")
    private String execPlanId;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "删除费用id")
    private List<String> deleteChargeIds;

    @Schema(description = "删除费用信息")
    private List<CisOrderExecPlanChargeEto> chargeEtos;

    @Schema(description = "新增费用信息")
    private List<CisOrderExecPlanChargeNto> chargeNtos;

    @NotBlank(message = "执行单ID不能为空！")
    public String getExecPlanId() {
        return execPlanId;
    }

    public void setExecPlanId(String execPlanId) {
        this.execPlanId = execPlanId;
    }

    @NotBlank(message = "版本号不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public List<String> getDeleteChargeIds() {
        return deleteChargeIds;
    }

    public void setDeleteChargeIds(List<String> deleteChargeIds) {
        this.deleteChargeIds = deleteChargeIds;
    }

    public List<CisOrderExecPlanChargeEto> getChargeEtos() {
        return chargeEtos;
    }

    public void setChargeEtos(List<CisOrderExecPlanChargeEto> chargeEtos) {
        this.chargeEtos = chargeEtos;
    }

    public List<CisOrderExecPlanChargeNto> getChargeNtos() {
        return chargeNtos;
    }

    public void setChargeNtos(List<CisOrderExecPlanChargeNto> chargeNtos) {
        this.chargeNtos = chargeNtos;
    }
}