package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "治疗申请单")
public class CisTreatmentApplyTo extends CisBaseApplyTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -3037012679083580542L;

    @Schema(description = "频次")
    private String frequency;

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

}