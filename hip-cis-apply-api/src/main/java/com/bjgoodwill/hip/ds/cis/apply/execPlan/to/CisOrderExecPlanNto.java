package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "医嘱执行档")
public class CisOrderExecPlanNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1821023332148014342L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "科室编码")
    private String orgCode;
    @Schema(description = "开发科室编码")
    private String orgName;
    @Schema(description = "护理组号")
    private String deptNurseCode;
    @Schema(description = "医嘱号")
    private String orderId;
    @Schema(description = "医嘱序号")
    private Double sortNo;
    @Schema(description = "医嘱项目编码")
    private String serviceItemCode;
    @Schema(description = "医嘱项目名称")
    private String serviceItemName;
    @Schema(description = "orderClass")
    private SystemTypeEnum orderClass;
    @Schema(description = "领药科室")
    private String receiveOrg;
    @Schema(description = "领药科室名称")
    private String receiveOrgName;
    @Schema(description = "医嘱预计执行时间")
    private LocalDateTime execPlanDate;
    @Schema(description = "开立医生")
    private String heldStaff;
    @Schema(description = "开立医生名称")
    private String heldStaffName;
    @Schema(description = "开方医生所在科室")
    private String createOrgCode;
    @Schema(description = "开方医生所在科室名称")
    private String createOrgName;
    private Double num;
    @Schema(description = "用法")
    private String usage;
    @Schema(description = "用法名称")
    private String usageName;
    private List<CisOrderExecPlanChargeNto> cisOrderExecPlanChargeTos;

    private String execOrgCode;
    private String execOrgName;
    private Boolean mainPlanFlag;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "主索引不能为空！")
    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = StringUtils.trimToNull(patMiCode);
    }

    @NotBlank(message = "就诊流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @NotBlank(message = "科室编码不能为空！")
    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = StringUtils.trimToNull(orgCode);
    }

    @NotBlank(message = "护理组号不能为空！")
    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = StringUtils.trimToNull(deptNurseCode);
    }

    @NotBlank(message = "医嘱号不能为空！")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = StringUtils.trimToNull(orderId);
    }

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = StringUtils.trimToNull(serviceItemCode);
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = StringUtils.trimToNull(serviceItemName);
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = StringUtils.trimToNull(receiveOrg);
    }

    public LocalDateTime getExecPlanDate() {
        return execPlanDate;
    }

    public void setExecPlanDate(LocalDateTime execPlanDate) {
        this.execPlanDate = execPlanDate;
    }

    public String getHeldStaff() {
        return heldStaff;
    }

    public void setHeldStaff(String heldStaff) {
        this.heldStaff = StringUtils.trimToNull(heldStaff);
    }

    @NotBlank(message = "开发医生所在科室不能为空！")
    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = StringUtils.trimToNull(createOrgCode);
    }

    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public List<CisOrderExecPlanChargeNto> getCisOrderExecPlanChargeTos() {
        return cisOrderExecPlanChargeTos;
    }

    public void setCisOrderExecPlanChargeTos(List<CisOrderExecPlanChargeNto> cisOrderExecPlanChargeTos) {
        this.cisOrderExecPlanChargeTos = cisOrderExecPlanChargeTos;
    }

    @NotBlank(message = "执行科室不能为空！")
    public String getExecOrgCode() {
        return execOrgCode;
    }

    public void setExecOrgCode(String execOrgCode) {
        this.execOrgCode = execOrgCode;
    }

    //region 主单标记
    public Boolean getMainPlanFlag() {
        return mainPlanFlag;
    }

    public void setMainPlanFlag(Boolean mainPlanFlag) {
        this.mainPlanFlag = mainPlanFlag;
    }
    //endregion

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public String getHeldStaffName() {
        return heldStaffName;
    }

    public void setHeldStaffName(String heldStaffName) {
        this.heldStaffName = heldStaffName;
    }

    public String getCreateOrgName() {
        return createOrgName;
    }

    public void setCreateOrgName(String createOrgName) {
        this.createOrgName = createOrgName;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getExecOrgName() {
        return execOrgName;
    }

    public void setExecOrgName(String execOrgName) {
        this.execOrgName = execOrgName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}