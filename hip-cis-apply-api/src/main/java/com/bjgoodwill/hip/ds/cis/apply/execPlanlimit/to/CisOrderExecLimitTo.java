package com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Schema(description = "执行单第三方状态操作限制")
public class CisOrderExecLimitTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -2473063570225399779L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "第三方状态名称")
    private String thirdStatus;
    @Schema(description = "false 允许不执行")
    private Boolean noExecFlag;
    @Schema(description = "false 允许取消执行")
    private Boolean cancelExecFlag;
    @Schema(description = "false 允许退费")
    private String refundsFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getThirdStatus() {
        return thirdStatus;
    }

    public void setThirdStatus(String thirdStatus) {
        this.thirdStatus = thirdStatus;
    }

    public Boolean getNoExecFlag() {
        return noExecFlag;
    }

    public void setNoExecFlag(Boolean noExecFlag) {
        this.noExecFlag = noExecFlag;
    }

    public Boolean getCancelExecFlag() {
        return cancelExecFlag;
    }

    public void setCancelExecFlag(Boolean cancelExecFlag) {
        this.cancelExecFlag = cancelExecFlag;
    }

    public String getRefundsFlag() {
        return refundsFlag;
    }

    public void setRefundsFlag(String refundsFlag) {
        this.refundsFlag = refundsFlag;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOrderExecLimitTo other = (CisOrderExecLimitTo) obj;
        return Objects.equals(id, other.id);
    }
}