package com.bjgoodwill.hip.ds.cis.apply.palg.service;

import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyService;
import com.bjgoodwill.hip.ds.cis.apply.detail.service.ApplyWithDetialService;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@Tag(name = "病理申请单领域服务", description = "病理申请单领域服务")
public interface CisPalgApplyService extends CisBaseApplyService, ApplyWithDetialService {

    @Operation(summary = "P0根据唯一标识返回病理申请单。")
    @GetMapping("/cisPalgApplies/{id:.+}")
    CisPalgApplyTo getCisPalgApplyById(@PathVariable("id") String id);

    @Operation(summary = "P0创建病理申请单。")
    @PostMapping("/cisPalgApplies")
    CisPalgApplyTo createCisPalgApply(@RequestBody @Valid CisPalgApplyNto cisPalgApplyNto);

    @Operation(summary = "P0根据唯一标识修改病理申请单。")
    @PutMapping("/cisPalgApplies/{id:.+}")
    void updateCisPalgApply(@PathVariable("id") String id, @RequestBody @Valid CisPalgApplyEto cisPalgApplyEto);

    @Operation(summary = "P0根据唯一标识删除病理申请单。")
    @DeleteMapping("/cisPalgApplies/{id:.+}")
    void deleteCisPalgApply(@PathVariable("id") String id);

}