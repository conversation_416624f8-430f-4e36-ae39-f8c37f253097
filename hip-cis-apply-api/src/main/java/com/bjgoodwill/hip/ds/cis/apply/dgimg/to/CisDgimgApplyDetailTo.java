package com.bjgoodwill.hip.ds.cis.apply.dgimg.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisUnilateralEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Schema(description = "检查申请单明细")
public class CisDgimgApplyDetailTo extends DetailTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1780867723546774679L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "检查申请单标识")
    private String cisDgimgApplyId;
    @Schema(description = "序号")
    private Double no;
    @Schema(description = "检查名称")
    private String dgimgName;
    @Schema(description = "人体系统")
    private String humanSystem;
    @Schema(description = "人体系统名称")
    private String humanSystemName;
    @Schema(description = "部位编码")
    private String humanOrgans;
    @Schema(description = "部位名称")
    private String humanOrgansName;
    @Schema(description = "方法")
    private String method;
    @Schema(description = "方法名称")
    private String methodName;
    @Schema(description = "范围")
    private String range;
    @Schema(description = "范围名称")
    private String rangeName;
    @Schema(description = "方位")
    private String direction;
    @Schema(description = "方位名称")
    private String directionName;
    @Schema(description = "layer")
    private String layer;
    @Schema(description = "层数名称")
    private String layerName;
    @Schema(description = "操作")
    private String operation;
    @Schema(description = "操作名称")
    private String operationName;
    @Schema(description = "要求与目的")
    private String requirementPurpose;
    @Schema(description = "状态")
    private CisStatusEnum statusCode;
    @Schema(description = "检查项目编码")
    private String dgimgCode;
    @Schema(description = "单侧标记：左，右，双侧")
    private CisUnilateralEnum unilateralFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCisDgimgApplyId() {
        return cisDgimgApplyId;
    }

    public void setCisDgimgApplyId(String cisDgimgApplyId) {
        this.cisDgimgApplyId = cisDgimgApplyId;
    }

    public Double getNo() {
        return no;
    }

    public void setNo(Double no) {
        this.no = no;
    }

    public String getDgimgName() {
        return dgimgName;
    }

    public void setDgimgName(String dgimgName) {
        this.dgimgName = dgimgName;
    }

    public String getHumanSystem() {
        return humanSystem;
    }

    public void setHumanSystem(String humanSystem) {
        this.humanSystem = humanSystem;
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = range;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getLayer() {
        return layer;
    }

    public void setLayer(String layer) {
        this.layer = layer;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getRequirementPurpose() {
        return requirementPurpose;
    }

    public void setRequirementPurpose(String requirementPurpose) {
        this.requirementPurpose = requirementPurpose;
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public CisUnilateralEnum getUnilateralFlag() {
        return unilateralFlag;
    }

    public void setUnilateralFlag(CisUnilateralEnum unilateralFlag) {
        this.unilateralFlag = unilateralFlag;
    }

    public String getHumanSystemName() {
        return humanSystemName;
    }

    public void setHumanSystemName(String humanSystemName) {
        this.humanSystemName = humanSystemName;
    }

    public String getHumanOrgansName() {
        return humanOrgansName;
    }

    public void setHumanOrgansName(String humanOrgansName) {
        this.humanOrgansName = humanOrgansName;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getRangeName() {
        return rangeName;
    }

    public void setRangeName(String rangeName) {
        this.rangeName = rangeName;
    }

    public String getDirectionName() {
        return directionName;
    }

    public void setDirectionName(String directionName) {
        this.directionName = directionName;
    }

    public String getLayerName() {
        return layerName;
    }

    public void setLayerName(String layerName) {
        this.layerName = layerName;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisDgimgApplyDetailTo other = (CisDgimgApplyDetailTo) obj;
        return Objects.equals(id, other.id);
    }

    public String getDgimgCode() {
        return dgimgCode;
    }

    public void setDgimgCode(String dgimgCode) {
        this.dgimgCode = dgimgCode;
    }
}