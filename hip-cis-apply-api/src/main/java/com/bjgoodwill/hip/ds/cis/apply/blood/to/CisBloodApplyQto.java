package com.bjgoodwill.hip.ds.cis.apply.blood.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "备血申请单")
public class CisBloodApplyQto extends CisBaseApplyQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1545419770564622718L;


}