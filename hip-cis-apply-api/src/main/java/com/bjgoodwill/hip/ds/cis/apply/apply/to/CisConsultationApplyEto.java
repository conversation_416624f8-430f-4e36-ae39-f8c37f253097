package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "会诊申请单")
public class CisConsultationApplyEto extends CisBaseApplyEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -7581133555538999003L;

    @Schema(description = "会诊科室")
    private String cnsltOrg;
    @Schema(description = "会诊科室名称")
    private String cnsltOrgName;
    //    @Schema(description = "诊断编码")
//    private String diagnosisCode;
    @Schema(description = "会诊医院编码")
    private String cnsltHospitalCode;
    @Schema(description = "会诊医院名称")
    private String cnsltHospitalName;
    @Schema(description = "患者病情摘要")
    private String patRemark;
    @Schema(description = "会诊理由及目的")
    private String reason;
    @Schema(description = "会诊医生")
    private String cnsltDoctor;
    @Schema(description = "会诊医生名称")
    private String cnsltDoctorName;
    @Schema(description = "会诊时间")
    private LocalDateTime cnsltDate;
    @Schema(description = "会诊确认时间")
    private LocalDateTime cnsltConfirmDate;
    @Schema(description = "会诊完成时间")
    private LocalDateTime cnsltCompletionTime;
    @Schema(description = "申请医生电话")
    private String createStaffTel;
    @Schema(description = "执行医院编码")
    private String execHospitalCode;
    @Schema(description = "执行医院名称")
    private String execHospitalName;
    @Schema(description = "会诊意见 ")
    private String cnsltOpinion;

    @NotBlank(message = "会诊科室不能为空！")
    public String getCnsltOrg() {
        return cnsltOrg;
    }

    public void setCnsltOrg(String cnsltOrg) {
        this.cnsltOrg = StringUtils.trimToNull(cnsltOrg);
    }

//    public String getDiagnosisCode() {
//        return diagnosisCode;
//    }
//
//    public void setDiagnosisCode(String diagnosisCode) {
//        this.diagnosisCode = StringUtils.trimToNull(diagnosisCode);
//    }

    public String getCnsltHospitalCode() {
        return cnsltHospitalCode;
    }

    public void setCnsltHospitalCode(String cnsltHospitalCode) {
        this.cnsltHospitalCode = StringUtils.trimToNull(cnsltHospitalCode);
    }

    public String getPatRemark() {
        return patRemark;
    }

    public void setPatRemark(String patRemark) {
        this.patRemark = StringUtils.trimToNull(patRemark);
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = StringUtils.trimToNull(reason);
    }

    public String getCnsltDoctor() {
        return cnsltDoctor;
    }

    public void setCnsltDoctor(String cnsltDoctor) {
        this.cnsltDoctor = StringUtils.trimToNull(cnsltDoctor);
    }

    public LocalDateTime getCnsltDate() {
        return cnsltDate;
    }

    public void setCnsltDate(LocalDateTime cnsltDate) {
        this.cnsltDate = cnsltDate;
    }

    public LocalDateTime getCnsltConfirmDate() {
        return cnsltConfirmDate;
    }

    public void setCnsltConfirmDate(LocalDateTime cnsltConfirmDate) {
        this.cnsltConfirmDate = cnsltConfirmDate;
    }

    public LocalDateTime getCnsltCompletionTime() {
        return cnsltCompletionTime;
    }

    public void setCnsltCompletionTime(LocalDateTime cnsltCompletionTime) {
        this.cnsltCompletionTime = cnsltCompletionTime;
    }

    public String getCreateStaffTel() {
        return createStaffTel;
    }

    public void setCreateStaffTel(String createStaffTel) {
        this.createStaffTel = StringUtils.trimToNull(createStaffTel);
    }

    public String getExecHospitalCode() {
        return execHospitalCode;
    }

    public void setExecHospitalCode(String execHospitalCode) {
        this.execHospitalCode = StringUtils.trimToNull(execHospitalCode);
    }

    public String getCnsltOpinion() {
        return cnsltOpinion;
    }

    public void setCnsltOpinion(String cnsltOpinion) {
        this.cnsltOpinion = cnsltOpinion;
    }

    @NotBlank(message = "会诊科室名称不能为空！")
    public String getCnsltOrgName() {
        return cnsltOrgName;
    }

    public void setCnsltOrgName(String cnsltOrgName) {
        this.cnsltOrgName = cnsltOrgName;
    }

    public String getCnsltHospitalName() {
        return cnsltHospitalName;
    }

    public void setCnsltHospitalName(String cnsltHospitalName) {
        this.cnsltHospitalName = cnsltHospitalName;
    }

    public String getCnsltDoctorName() {
        return cnsltDoctorName;
    }

    public void setCnsltDoctorName(String cnsltDoctorName) {
        this.cnsltDoctorName = cnsltDoctorName;
    }

    public String getExecHospitalName() {
        return execHospitalName;
    }

    public void setExecHospitalName(String execHospitalName) {
        this.execHospitalName = execHospitalName;
    }
}