package com.bjgoodwill.hip.ds.cis.apply.spcobs.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetialNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "检验类申请单")
public class CisSpcobsApplyNto extends ApplyWithDetialNto<CisSpcobsApplyDetailNto> implements Serializable {

    @Serial
    private static final long serialVersionUID = -3569943866528290327L;

    @Schema(description = "检验注意事项")
    private String remark;
    @Schema(description = "检验分类")
    private String spcobsClass;
    @Schema(description = "检验分类名称")
    private String spcobsClassName;
    @Schema(description = "检验申请单明细列表")
//    private CisSpcobsApplyDetailNto detail;
    private List<CisSpcobsApplyDetailNto> details = new ArrayList<>();

    @Schema(description = "标本 字典Speciman")
    private String speciman;
    @Schema(description = "标本名称 字典Speciman")
    private String specimanName;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = StringUtils.trimToNull(remark);
    }

    public String getSpcobsClass() {
        return spcobsClass;
    }

    public void setSpcobsClass(String spcobsClass) {
        this.spcobsClass = StringUtils.trimToNull(spcobsClass);
    }

    @Override
    @NotEmpty(message = "明细不能为空！")
    public List<CisSpcobsApplyDetailNto> getDetails() {
        return details;
    }

    @Override
    public void setDetails(List<CisSpcobsApplyDetailNto> details) {
        this.details = details;
    }

    @NotBlank(message = "标本 字典Speciman不能为空！")
    public String getSpeciman() {
        return speciman;
    }

    public void setSpeciman(String speciman) {
        this.speciman = StringUtils.trimToNull(speciman);
    }

    public String getSpcobsClassName() {
        return spcobsClassName;
    }

    public void setSpcobsClassName(String spcobsClassName) {
        this.spcobsClassName = spcobsClassName;
    }

    public String getSpecimanName() {
        return specimanName;
    }

    public void setSpecimanName(String specimanName) {
        this.specimanName = specimanName;
    }
}