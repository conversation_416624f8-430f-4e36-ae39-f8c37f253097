package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;


import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.drug.enums.DrugIpdDataStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Schema(description = "医嘱执行档")
public class CisOrderExecPlanTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1190103657283437553L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "开立科室编码")
    private String orgCode;
    @Schema(description = "开立科室名称")
    private String orgName;
    @Schema(description = "护理组号")
    private String deptNurseCode;
    @Schema(description = "医嘱号")
    private String orderId;
    @Schema(description = "抽象父类标识")
    private String cisBaseApplyId;
    @Schema(description = "医嘱序号")
    private Double sortNo;
    @Schema(description = "医嘱项目编码")
    private String serviceItemCode;
    @Schema(description = "医嘱项目名称")
    private String serviceItemName;
    @Schema(description = "orderClass")
    private SystemTypeEnum orderClass;
    //    @Schema(description = "是否皮试")
//    private Boolean isSkin;
    @Schema(description = "皮试结果")
    private String skinResult;
    @Schema(description = "领药科室")
    private String receiveOrg;
    @Schema(description = "领药科室名称")
    private String receiveOrgName;
    @Schema(description = "医嘱预计执行时间")
    private LocalDateTime execPlanDate;
    @Schema(description = "执行时间")
    private LocalDateTime execDate;
    @Schema(description = "执行人")
    private String execStaff;
    @Schema(description = "执行人名称")
    private String execStaffName;
    @Schema(description = "是否计费")
    private Boolean isCharge;
    @Schema(description = "计费时间")
    private LocalDateTime chargeDate;
    @Schema(description = "计费人")
    private String chargeStaff;
    @Schema(description = "计费人名称")
    private String chargeStaffName;
    @Schema(description = "计费科室")
    private String chargeOrg;
    @Schema(description = "计费科室名称")
    private String chargeOrgName;
    @Schema(description = "执行单打印标记")
    private Boolean isPrint;
    @Schema(description = "执行单打印时间")
    private LocalDateTime printDate;
    @Schema(description = "执行单打印人")
    private String printStaff;
    @Schema(description = "执行单打印人名称")
    private String printStaffName;
    @Schema(description = "皮试操作时间(区别于结果录入时间)")
    private String skinTestDate;
    @Schema(description = "皮试人1")
    private String stStaffA;
    @Schema(description = "皮试人1名称")
    private String stStaffAName;
    @Schema(description = "皮试人2")
    private String stStaffB;
    @Schema(description = "皮试人2名称")
    private String stStaffBName;
    @Schema(description = "开立医生")
    private String heldStaff;
    @Schema(description = "开立医生名称")
    private String heldStaffName;
    @Schema(description = "开发医生所在科室")
    private String createOrgCode;
    @Schema(description = "开发医生所在科室名称")
    private String createOrgName;
    @Schema(description = "statusCode")
    private CisStatusEnum statusCode;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员名称")
    private String updatedStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "版本")
    private Integer version;
    private List<CisOrderExecPlanChargeTo> cisOrderExecPlanChargeTos;
    private Double num;
    @Schema(description = "用法")
    private String usage;
    @Schema(description = "发药状态")
    private DrugIpdDataStatusEnum drugInoutType;
    @Schema(description = "是否发送")
    private Boolean isSend;
    @Schema(description = "执行科室编码")
    private String execOrgCode;
    @Schema(description = "执行科室名称")
    private String execOrgName;
    @Schema(description = "长期，临时")
    private OrderTypeEnum orderType;
    @Schema(description = "第三方状态")
    private String thridStatus;
    @Schema(description = "药品批号")
    private String batchNo;
    @Schema(description = "执行单模版名称")
    private String orderTempName;
    @Schema(description = "执行单模版ID")
    private String orderTempId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCisBaseApplyId() {
        return cisBaseApplyId;
    }

    public void setCisBaseApplyId(String cisBaseApplyId) {
        this.cisBaseApplyId = cisBaseApplyId;
    }

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

//    public Boolean getIsSkin() {
//        return isSkin;
//    }
//
//    public void setIsSkin(Boolean isSkin) {
//        this.isSkin = isSkin;
//    }

    public String getSkinResult() {
        return skinResult;
    }

    public void setSkinResult(String skinResult) {
        this.skinResult = skinResult;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    public LocalDateTime getExecPlanDate() {
        return execPlanDate;
    }

    public void setExecPlanDate(LocalDateTime execPlanDate) {
        this.execPlanDate = execPlanDate;
    }

    public LocalDateTime getExecDate() {
        return execDate;
    }

    public void setExecDate(LocalDateTime execDate) {
        this.execDate = execDate;
    }

    public String getExecStaff() {
        return execStaff;
    }

    public void setExecStaff(String execStaff) {
        this.execStaff = execStaff;
    }

    public Boolean getIsCharge() {
        return isCharge;
    }

    public void setIsCharge(Boolean isCharge) {
        this.isCharge = isCharge;
    }

    public LocalDateTime getChargeDate() {
        return chargeDate;
    }

    public void setChargeDate(LocalDateTime chargeDate) {
        this.chargeDate = chargeDate;
    }

    public String getChargeStaff() {
        return chargeStaff;
    }

    public void setChargeStaff(String chargeStaff) {
        this.chargeStaff = chargeStaff;
    }

    public String getChargeOrg() {
        return chargeOrg;
    }

    public void setChargeOrg(String chargeOrg) {
        this.chargeOrg = chargeOrg;
    }

    public Boolean getIsPrint() {
        return isPrint;
    }

    public void setIsPrint(Boolean isPrint) {
        this.isPrint = isPrint;
    }

    public LocalDateTime getPrintDate() {
        return printDate;
    }

    public void setPrintDate(LocalDateTime printDate) {
        this.printDate = printDate;
    }

    public String getPrintStaff() {
        return printStaff;
    }

    public void setPrintStaff(String printStaff) {
        this.printStaff = printStaff;
    }

    public String getSkinTestDate() {
        return skinTestDate;
    }

    public void setSkinTestDate(String skinTestDate) {
        this.skinTestDate = skinTestDate;
    }

    public String getStStaffA() {
        return stStaffA;
    }

    public void setStStaffA(String stStaffA) {
        this.stStaffA = stStaffA;
    }

    public String getStStaffB() {
        return stStaffB;
    }

    public void setStStaffB(String stStaffB) {
        this.stStaffB = stStaffB;
    }

    public String getHeldStaff() {
        return heldStaff;
    }

    public void setHeldStaff(String heldStaff) {
        this.heldStaff = heldStaff;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOrderExecPlanTo other = (CisOrderExecPlanTo) obj;
        return Objects.equals(id, other.id);
    }

    public List<CisOrderExecPlanChargeTo> getCisOrderExecPlanChargeTos() {
        return cisOrderExecPlanChargeTos;
    }

    public void setCisOrderExecPlanChargeTos(List<CisOrderExecPlanChargeTo> cisOrderExecPlanChargeTos) {
        this.cisOrderExecPlanChargeTos = cisOrderExecPlanChargeTos;
    }

    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public Boolean getPrint() {
        return isPrint;
    }

    public void setPrint(Boolean print) {
        isPrint = print;
    }

    public DrugIpdDataStatusEnum getDrugInoutType() {
        return drugInoutType;
    }

    public void setDrugInoutType(DrugIpdDataStatusEnum drugInoutType) {
        this.drugInoutType = drugInoutType;
    }

    public Boolean getIsSend() {
        return isSend;
    }

    public void setIsSend(Boolean isSend) {
        isSend = isSend;
    }

    public String getExecOrgCode() {
        return execOrgCode;
    }

    public void setExecOrgCode(String execOrgCode) {
        this.execOrgCode = execOrgCode;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public String getExecStaffName() {
        return execStaffName;
    }

    public void setExecStaffName(String execStaffName) {
        this.execStaffName = execStaffName;
    }

    public String getChargeStaffName() {
        return chargeStaffName;
    }

    public void setChargeStaffName(String chargeStaffName) {
        this.chargeStaffName = chargeStaffName;
    }

    public String getChargeOrgName() {
        return chargeOrgName;
    }

    public void setChargeOrgName(String chargeOrgName) {
        this.chargeOrgName = chargeOrgName;
    }

    public String getPrintStaffName() {
        return printStaffName;
    }

    public void setPrintStaffName(String printStaffName) {
        this.printStaffName = printStaffName;
    }

    public String getStStaffAName() {
        return stStaffAName;
    }

    public void setStStaffAName(String stStaffAName) {
        this.stStaffAName = stStaffAName;
    }

    public String getStStaffBName() {
        return stStaffBName;
    }

    public void setStStaffBName(String stStaffBName) {
        this.stStaffBName = stStaffBName;
    }

    public String getHeldStaffName() {
        return heldStaffName;
    }

    public void setHeldStaffName(String heldStaffName) {
        this.heldStaffName = heldStaffName;
    }

    public String getCreateOrgName() {
        return createOrgName;
    }

    public void setCreateOrgName(String createOrgName) {
        this.createOrgName = createOrgName;
    }

    public String getExecOrgName() {
        return execOrgName;
    }

    public void setExecOrgName(String execOrgName) {
        this.execOrgName = execOrgName;
    }

    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderTypeEnum orderType) {
        this.orderType = orderType;
    }

    public String getThridStatus() {
        return thridStatus;
    }

    public void setThridStatus(String thridStatus) {
        this.thridStatus = thridStatus;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getOrderTempName() {
        return orderTempName;
    }

    public void setOrderTempName(String orderTempName) {
        this.orderTempName = orderTempName;
    }

    public String getOrderTempId() {
        return orderTempId;
    }

    public void setOrderTempId(String orderTempId) {
        this.orderTempId = orderTempId;
    }
}