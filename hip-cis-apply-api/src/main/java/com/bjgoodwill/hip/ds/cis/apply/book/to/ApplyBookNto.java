package com.bjgoodwill.hip.ds.cis.apply.book.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "申请单预约")
public class ApplyBookNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -7613235922151767495L;
    @Schema(description = "标识")
    private String id;
    @Schema(description = "预约开始时间")
    private LocalDateTime appointsStartDate;
    @Schema(description = "预约结束时间")
    private String appointsEndDate;

    @Schema(description = "申请单ID")
    private String applyId;

    @NotNull(message = "预约开始时间不能为空！")
    public LocalDateTime getAppointsStartDate() {
        return appointsStartDate;
    }

    public void setAppointsStartDate(LocalDateTime appointsStartDate) {
        this.appointsStartDate = appointsStartDate;
    }

    public String getAppointsEndDate() {
        return appointsEndDate;
    }

    public void setAppointsEndDate(String appointsEndDate) {
        this.appointsEndDate = StringUtils.trimToNull(appointsEndDate);
    }

    @NotBlank(message = "申请单ID不能为空！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @NotBlank(message = "申请单ID不能为空！")
    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }
}