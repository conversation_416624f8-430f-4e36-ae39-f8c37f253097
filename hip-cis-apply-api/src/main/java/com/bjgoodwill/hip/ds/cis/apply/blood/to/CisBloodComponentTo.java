package com.bjgoodwill.hip.ds.cis.apply.blood.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Schema(description = "血液成分从表")
public class CisBloodComponentTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -4503969681608176520L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "输血申请单标识")
    private String cisBloodApplyId;
    @Schema(description = "申请输注血液成分")
    private String bloodComponent;
    @Schema(description = "申请输注血量")
    private Double bloodQuantity;
    @Schema(description = "输注血量单位 默认ml")
    private String bloodQuantityUnit;
    @Schema(description = "医嘱处理标记:1己处理，0未处理")
    private Boolean orderResultFlag;
    @Schema(description = "serviceItemCode")
    private String serviceItemCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCisBloodApplyId() {
        return cisBloodApplyId;
    }

    public void setCisBloodApplyId(String cisBloodApplyId) {
        this.cisBloodApplyId = cisBloodApplyId;
    }

    public String getBloodComponent() {
        return bloodComponent;
    }

    public void setBloodComponent(String bloodComponent) {
        this.bloodComponent = bloodComponent;
    }

    public Double getBloodQuantity() {
        return bloodQuantity;
    }

    public void setBloodQuantity(Double bloodQuantity) {
        this.bloodQuantity = bloodQuantity;
    }

    public String getBloodQuantityUnit() {
        return bloodQuantityUnit;
    }

    public void setBloodQuantityUnit(String bloodQuantityUnit) {
        this.bloodQuantityUnit = bloodQuantityUnit;
    }

    public Boolean getOrderResultFlag() {
        return orderResultFlag;
    }

    public void setOrderResultFlag(Boolean orderResultFlag) {
        this.orderResultFlag = orderResultFlag;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisBloodComponentTo other = (CisBloodComponentTo) obj;
        return Objects.equals(id, other.id);
    }
}