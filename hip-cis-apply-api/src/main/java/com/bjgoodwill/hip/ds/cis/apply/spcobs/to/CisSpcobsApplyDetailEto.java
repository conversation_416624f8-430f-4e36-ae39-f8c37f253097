package com.bjgoodwill.hip.ds.cis.apply.spcobs.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "检验申请单明细")
public class CisSpcobsApplyDetailEto extends DetailEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4612270209283820994L;

    @Schema(description = "ID")
    private String id;

    @Schema(description = "内部序号")
    private Double no;
    @Schema(description = "检验名称")
    private String spcobsName;

    @Schema(description = "检验项目编码")
    private String spcobsCode;

    public Double getNo() {
        return no;
    }

    public void setNo(Double no) {
        this.no = no;
    }

    //    @NotBlank(message = "检验名称不能为空！")
    public String getSpcobsName() {
        return spcobsName;
    }

    public void setSpcobsName(String spcobsName) {
        this.spcobsName = StringUtils.trimToNull(spcobsName);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    //    @NotBlank(message = "检验项目编码不能为空！")
    public String getSpcobsCode() {
        return spcobsCode;
    }

    public void setSpcobsCode(String spcobsCode) {
        this.spcobsCode = spcobsCode;
    }
}