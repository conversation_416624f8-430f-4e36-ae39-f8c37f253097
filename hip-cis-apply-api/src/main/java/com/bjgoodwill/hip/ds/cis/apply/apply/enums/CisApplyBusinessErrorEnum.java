package com.bjgoodwill.hip.ds.cis.apply.apply.enums;

import com.bjgoodwill.hip.common.exception.BusinessErrorEnum;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-03 14:03
 * @className: CisApplyBusinessErrorEnum
 * @description:
 **/
public enum CisApplyBusinessErrorEnum implements BusinessErrorEnum {
    BUS_CIS_APPLY_0001(" [%s]不可为空！"),
    BUS_CIS_APPLY_0002("[%s]已经存在！"),
    BUS_CIS_APPLY_0003("数据[%s]已被修改,请刷新！"),
    BUS_CIS_APPLY_0004("顺序[%s]已经存在！"),
    BUS_CIS_APPLY_0005("数据[%s]格式不正确！"),
    BUS_CIS_APPLY_0006("未找到[%s]申请单数据！"),
    BUS_CIS_APPLY_0007("[%s]不能为空！"),
    BUS_CIS_APPLY_0008("数据[%s]状态不正确,请刷新后重试！"),
    BUS_CIS_APPLY_0009("[%s]数据不存在！"),
    BUS_CIS_APPLY_00010("临时医嘱%s不允许%s操作！"),
    BUS_CIS_APPLY_00011("[%s]挂起时间不允许超出医嘱开始时候和最后拆分时间！"),
    BUS_CIS_APPLY_00012("[%s]已计费，不允许删除，作废！"),
    BUS_CIS_APPLY_00013("[%s]已经执行！"),
    BUS_CIS_APPLY_00014("[%s]"),
    BUS_CIS_APPLY_00015("[%s]第三方状态限制，不允许[%s]"),
    BUS_CIS_APPLY_00016("[%s]项目[s%]不存在！");


    private final String message;

    CisApplyBusinessErrorEnum(String message) {
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.name();
    }

    @Override
    public String getMessage() {
        return message;
    }
}
