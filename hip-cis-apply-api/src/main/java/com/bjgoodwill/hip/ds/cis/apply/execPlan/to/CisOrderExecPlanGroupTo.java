package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-02-27 20:15
 * @className: CisOrderExecPlanGroupTo
 * @description:
 **/
@Schema(description = "医嘱执行单，流水号汇总")
public class CisOrderExecPlanGroupTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1190103657283437555L;
    private String visitCode;
    private String patMiCode;
    private List<CisBaseApplyTo> cisBaseApplyTos;

    public CisOrderExecPlanGroupTo() {
    }

    public CisOrderExecPlanGroupTo(String visitCode, String patMiCode, List<CisBaseApplyTo> cisBaseApplyTos) {
        this.visitCode = visitCode;
        this.patMiCode = patMiCode;
        this.cisBaseApplyTos = cisBaseApplyTos;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public List<CisBaseApplyTo> getCisBaseApplyTos() {
        return cisBaseApplyTos;
    }

    public void setCisBaseApplyTos(List<CisBaseApplyTo> cisBaseApplyTos) {
        this.cisBaseApplyTos = cisBaseApplyTos;
    }
}