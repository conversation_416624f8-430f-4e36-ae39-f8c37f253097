package com.bjgoodwill.hip.ds.cis.apply.charge.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.bjgoodwill.hip.business.util.econ.enums.SystemItemClassEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "CisApplyCharge")
public class CisApplyChargeNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4014394258513488314L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "医嘱id")
    private String orderId;
    @Schema(description = "患者接诊流水号")
    private String visitCode;
    @Schema(description = "收费项目编码")
    private String priceItemCode;
    @Schema(description = "收费项目名称")
    private String priceItemName;
    @Schema(description = "固定费用")
    private Boolean isFixed;
    @Schema(description = "规格")
    private String packageSpec;
    @Schema(description = "单价")
    private BigDecimal price;
    @Schema(description = "单位")
    private String unit;
    @Schema(description = "单位名称")
    private String unitName;
    @Schema(description = "数量")
    private Double num;
    @Schema(description = "计费时段")
    private String chargeTime;
    @Schema(description = "计费频次")
    private String chargeFrequency;
    @Schema(description = "statusCode")
    private CisStatusEnum statusCode;
    @Schema(description = "执行科室")
    private String executeOrgCode;
    @Schema(description = "执行科室名称")
    private String executeOrgName;
    @Schema(description = "金额")
    private BigDecimal chageAmount;
    @Schema(description = "特限符合标识:1符合,0不符合")
    private Boolean limitConformFlag;
    @Schema(description = "SPD高值耗材唯一码")
    private String barCode;
    private String cisBaseApplyId;
    @Schema(description = "费用类型")
    private CisChargeTypeEnum chargeType;
    @Schema(description = "申请单明细id,针对明细的补费使用")
    private String detailId;
    @Schema(description = "系统项目分类")
    private SystemItemClassEnum systemItemClass;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @NotBlank(message = "医嘱id不能为空！")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = StringUtils.trimToNull(orderId);
    }

    @NotBlank(message = "患者接诊流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @NotBlank(message = "收费项目编码不能为空！")
    public String getPriceItemCode() {
        return priceItemCode;
    }

    public void setPriceItemCode(String priceItemCode) {
        this.priceItemCode = StringUtils.trimToNull(priceItemCode);
    }

    public String getPriceItemName() {
        return priceItemName;
    }

    public void setPriceItemName(String priceItemName) {
        this.priceItemName = StringUtils.trimToNull(priceItemName);
    }

    public Boolean getIsFixed() {
        return isFixed;
    }

    public void setIsFixed(Boolean isFixed) {
        this.isFixed = isFixed;
    }

    public String getPackageSpec() {
        return packageSpec;
    }

    public void setPackageSpec(String packageSpec) {
        this.packageSpec = StringUtils.trimToNull(packageSpec);
    }

    @NotNull(message = "单价不能为空！")
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    //    @NotNull(message = "单位不能为空！")
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = StringUtils.trimToNull(unit);
    }

    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public String getChargeTime() {
        return chargeTime;
    }

    public void setChargeTime(String chargeTime) {
        this.chargeTime = chargeTime;
    }

    public String getChargeFrequency() {
        return chargeFrequency;
    }

    public void setChargeFrequency(String chargeFrequency) {
        this.chargeFrequency = StringUtils.trimToNull(chargeFrequency);
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = (statusCode);
    }

    @NotBlank(message = "执行科室不能为空！")
    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    public void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = StringUtils.trimToNull(executeOrgCode);
    }

    public BigDecimal getChageAmount() {
        return chageAmount;
    }

    public void setChageAmount(BigDecimal chageAmount) {
        this.chageAmount = chageAmount;
    }

    public Boolean getLimitConformFlag() {
        return limitConformFlag;
    }

    public void setLimitConformFlag(Boolean limitConformFlag) {
        this.limitConformFlag = limitConformFlag;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = StringUtils.trimToNull(barCode);
    }

    @NotBlank(message = "申请单id不能为空！")
    public String getCisBaseApplyId() {
        return cisBaseApplyId;
    }

    public void setCisBaseApplyId(String cisBaseApplyId) {
        this.cisBaseApplyId = cisBaseApplyId;
    }

    @NotNull(message = "费用类型不能为空！")
    public CisChargeTypeEnum getChargeType() {
        return chargeType;
    }

    public void setChargeType(CisChargeTypeEnum chargeType) {
        this.chargeType = chargeType;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    @NotNull(message = "系统项目分类不能为空！")
    public SystemItemClassEnum getSystemItemClass() {
        return systemItemClass;
    }

    public void setSystemItemClass(SystemItemClassEnum systemItemClass) {
        this.systemItemClass = systemItemClass;
    }
}