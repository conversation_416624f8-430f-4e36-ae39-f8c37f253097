package com.bjgoodwill.hip.ds.cis.apply.drug.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "成药申请单")
public class CisEDrugApplyTo extends CisBaseDrugApplyTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -8831982198917285068L;

    @Schema(description = "滴速")
    private String dripSpeed;
    @Schema(description = "滴速单位")
    private String dripSpeedUnit;
    @Schema(description = "滴速单位名称")
    private String dripSpeedUnitName;

    public String getDripSpeed() {
        return dripSpeed;
    }

    public void setDripSpeed(String dripSpeed) {
        this.dripSpeed = dripSpeed;
    }

    public String getDripSpeedUnit() {
        return dripSpeedUnit;
    }

    public void setDripSpeedUnit(String dripSpeedUnit) {
        this.dripSpeedUnit = dripSpeedUnit;
    }

    public String getDripSpeedUnitName() {
        return dripSpeedUnitName;
    }

    public void setDripSpeedUnitName(String dripSpeedUnitName) {
        this.dripSpeedUnitName = dripSpeedUnitName;
    }
}