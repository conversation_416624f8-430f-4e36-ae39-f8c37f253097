package com.bjgoodwill.hip.ds.cis.apply.operation.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-09-29 15:43
 * @className: Nto
 * @description:
 **/
@Schema(description = "手术术式")
public class CisOperationApplyDetailNto extends DetailNto {

    @Serial
    private static final long serialVersionUID = -3624463504315755393L;

    @Schema(description = "手术术式,ICD9-CM3编码")
    private String operationCode;
    @Schema(description = "手术术式,ICD9-CM3名称")
    private String operationName;
    @Schema(description = "申请单编码")
    private String applyId;
    @Schema(description = "项目编码")
    private String serviceItemCode;
    @Schema(description = "项目名称")
    private String serviceItemName;
    @Schema(description = "手术级别")
    private String operationLevel;
    @Schema(description = "部位")
    private String humanOrgans;
    @Schema(description = "部位名称")
    private String humanOrgansName;
    @Schema(description = "体位")
    private String decubitus;
    @Schema(description = "体位名称")
    private String decubitusName;

    @NotEmpty(message = "手术编码不能为空！")
    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    @Override
    public String getServiceItemCode() {
        return serviceItemCode;
    }

    @Override
    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getOperationLevel() {
        return operationLevel;
    }

    public void setOperationLevel(String operationLevel) {
        this.operationLevel = StringUtils.trimToNull(operationLevel);
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    public String getHumanOrgansName() {
        return humanOrgansName;
    }

    public void setHumanOrgansName(String humanOrgansName) {
        this.humanOrgansName = humanOrgansName;
    }

    public String getDecubitus() {
        return decubitus;
    }

    public void setDecubitus(String decubitus) {
        this.decubitus = decubitus;
    }

    public String getDecubitusName() {
        return decubitusName;
    }

    public void setDecubitusName(String decubitusName) {
        this.decubitusName = decubitusName;
    }
}