package com.bjgoodwill.hip.ds.cis.apply.drug.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "药品明细")
public class CisDrugApplyDetailEto extends DetailEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4616081224505115182L;

    @Schema(description = "序号")
    private Double sortNo;
    @Schema(description = "药品编码")
    private String drugCode;
    @Schema(description = "药品名称")
    private String drugName;
    @Schema(description = "每次剂量")
    private Double dosage;
    @Schema(description = "剂量单位 字典DosageUnit")
    private String dosageUnit;
    @Schema(description = "剂量单位 字典DosageUnit")
    private String dosageUnitName;
    @Schema(description = "包装总量")
    private Double packageNum;
    @Schema(description = "包装单位 MinUnit/PackageUnit")
    private String packageUnit;
    @Schema(description = "包装单位 MinUnit/PackageUnit")
    private String packageUnitName;
    @Schema(description = "领药科室")
    private String receiveOrg;
    @Schema(description = "领药科室名称")
    private String receiveOrgName;
    private SbadmWayEnum sbadmWay;
    @Schema(description = "抗菌药使用说明:0-预防，1-治疗")
    private Integer antimicrobialsPurpose;
    @Schema(description = "特殊煎法：字典DecoctMethod")
    private String decoctMethodCode;
    @Schema(description = "特殊煎法：字典DecoctMethod")
    private String decoctMethodName;

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    @NotBlank(message = "药品编码不能为空！")
    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = StringUtils.trimToNull(drugCode);
    }

    public String getDrugName() {
        return drugName;
    }

    public void setDrugName(String drugName) {
        this.drugName = StringUtils.trimToNull(drugName);
    }

    @NotNull(message = "每次剂量不能为空！")
    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = StringUtils.trimToNull(dosageUnit);
    }

    public Double getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    public String getPackageUnit() {
        return packageUnit;
    }

    public void setPackageUnit(String packageUnit) {
        this.packageUnit = StringUtils.trimToNull(packageUnit);
    }

    @NotBlank(message = "领药科室不能为空！")
    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = StringUtils.trimToNull(receiveOrg);
    }

    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }

    public Integer getAntimicrobialsPurpose() {
        return antimicrobialsPurpose;
    }

    public void setAntimicrobialsPurpose(Integer antimicrobialsPurpose) {
        this.antimicrobialsPurpose = antimicrobialsPurpose;
    }

    public String getDecoctMethodCode() {
        return decoctMethodCode;
    }

    public void setDecoctMethodCode(String decoctMethodCode) {
        this.decoctMethodCode = decoctMethodCode;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    public String getPackageUnitName() {
        return packageUnitName;
    }

    public void setPackageUnitName(String packageUnitName) {
        this.packageUnitName = packageUnitName;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public String getDecoctMethodName() {
        return decoctMethodName;
    }

    public void setDecoctMethodName(String decoctMethodName) {
        this.decoctMethodName = decoctMethodName;
    }
}