package com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "执行单第三方状态操作限制")
public class CisOrderExecLimitNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2734845579643640452L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "第三方状态名称")
    private String thirdStatus;
    @Schema(description = "false 允许不执行")
    private Boolean noExecFlag;
    @Schema(description = "false 允许取消执行")
    private Boolean cancelExecFlag;
    @Schema(description = "false 允许退费")
    private String refundsFlag;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    public String getThirdStatus() {
        return thirdStatus;
    }

    public void setThirdStatus(String thirdStatus) {
        this.thirdStatus = StringUtils.trimToNull(thirdStatus);
    }

    public Boolean getNoExecFlag() {
        return noExecFlag;
    }

    public void setNoExecFlag(Boolean noExecFlag) {
        this.noExecFlag = noExecFlag;
    }

    public Boolean getCancelExecFlag() {
        return cancelExecFlag;
    }

    public void setCancelExecFlag(Boolean cancelExecFlag) {
        this.cancelExecFlag = cancelExecFlag;
    }

    public String getRefundsFlag() {
        return refundsFlag;
    }

    public void setRefundsFlag(String refundsFlag) {
        this.refundsFlag = StringUtils.trimToNull(refundsFlag);
    }
}