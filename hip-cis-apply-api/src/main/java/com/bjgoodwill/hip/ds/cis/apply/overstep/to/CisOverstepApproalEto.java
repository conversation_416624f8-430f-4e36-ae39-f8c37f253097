package com.bjgoodwill.hip.ds.cis.apply.overstep.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "越级审批")
public class CisOverstepApproalEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4558072031052406698L;

    //    @Schema(description = "申请，通过，不通过；默认申请")
//    private CheckTypeEnum checkType;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "审核意见")
    private String reviewOpinions;
//    @Schema(description = "申请医生姓名")
//    private String applyDocName;
//    @Schema(description = "申请科室名称")
//    private String applyOrgName;
//    @Schema(description = "申请原因")
//    private String applyReason;

//    @NotNull(message = "申请，通过，不通过；默认申请不能为空！")
//    public CheckTypeEnum getCheckType() {
//        return checkType;
//    }
//
//    public void setCheckType(CheckTypeEnum checkType) {
//        this.checkType = checkType;
//    }

    @NotNull(message = "版本不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getReviewOpinions() {
        return reviewOpinions;
    }

    public void setReviewOpinions(String reviewOpinions) {
        this.reviewOpinions = StringUtils.trimToNull(reviewOpinions);
    }

//    public String getApplyDocName() {
//        return applyDocName;
//    }
//
//    public void setApplyDocName(String applyDocName) {
//        this.applyDocName = StringUtils.trimToNull(applyDocName);
//    }
//
//    public String getApplyOrgName() {
//        return applyOrgName;
//    }
//
//    public void setApplyOrgName(String applyOrgName) {
//        this.applyOrgName = StringUtils.trimToNull(applyOrgName);
//    }
//
//    public String getApplyReason() {
//        return applyReason;
//    }
//
//    public void setApplyReason(String applyReason) {
//        this.applyReason = StringUtils.trimToNull(applyReason);
//    }
}