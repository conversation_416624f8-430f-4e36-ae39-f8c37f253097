package com.bjgoodwill.hip.ds.cis.apply.palg.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "病理申请单")
public class CisPalgApplyQto extends CisBaseApplyQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6336365070727537369L;

    @Schema(description = "绝经")
    private Boolean isMenopause;


    public Boolean getIsMenopause() {
        return isMenopause;
    }

    public void setIsMenopause(Boolean isMenopause) {
        this.isMenopause = isMenopause;
    }
}