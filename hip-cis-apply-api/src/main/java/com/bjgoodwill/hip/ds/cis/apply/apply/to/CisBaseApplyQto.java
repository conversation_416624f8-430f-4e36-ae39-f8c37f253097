package com.bjgoodwill.hip.ds.cis.apply.apply.to;


import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "抽象父类")
public class CisBaseApplyQto extends BaseTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -2592335624812406294L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "医嘱编码")
    private String serviceItemCode;
    @Schema(description = "状态")
    private CisStatusEnum statusCode;
    @Schema(description = "IPD住院，OPD门诊")
    private VisitTypeEnum visitType;
    @Schema(description = "护理组编码")
    private String deptNurseCode;
    @Schema(description = "医嘱ID")
    private String orderID;
    @Schema(description = "申请医院编码")
    private String hospitalCode;
    @Schema(description = "处方号")
    private String prescriptionID;
    @Schema(description = "开方人所在科室")
    private String createOrgCode;

    @Schema(description = "医嘱类型")
    private String orderClass;
    @Schema(description = "主键集合")
    private List<String> ids;

    public String getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(String orderClass) {
        this.orderClass = orderClass;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    public String getOrderID() {
        return orderID;
    }

    public void setOrderID(String orderID) {
        this.orderID = orderID;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPrescriptionID() {
        return prescriptionID;
    }

    public void setPrescriptionID(String prescriptionID) {
        this.prescriptionID = prescriptionID;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }
}