package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisChangeDeptApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisChangeDeptApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisChangeDeptApplyTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@Tag(name = "转科申请单领域服务", description = "转科申请单领域服务")
public interface CisChangeDeptApplyService extends CisBaseApplyService {

    @Operation(summary = "P0根据唯一标识返回转科申请单。")
    @GetMapping("/cisChangeDeptApplies/{id:.+}")
    CisChangeDeptApplyTo getCisChangeDeptApplyById(@PathVariable("id") String id);

    @Operation(summary = "P0创建转科申请单。")
    @PostMapping("/cisChangeDeptApplies")
    CisChangeDeptApplyTo createCisChangeDeptApply(@RequestBody @Valid CisChangeDeptApplyNto cisChangeDeptApplyNto);

    @Operation(summary = "P0根据唯一标识修改转科申请单。")
    @PutMapping("/cisChangeDeptApplies/{id:.+}")
    void updateCisChangeDeptApply(@PathVariable("id") String id, @RequestBody @Valid CisChangeDeptApplyEto cisChangeDeptApplyEto);

    @Operation(summary = "P0根据唯一标识删除转科申请单。")
    @DeleteMapping("/cisChangeDeptApplies/{id:.+}")
    void deleteCisChangeDeptApply(@PathVariable("id") String id);

}