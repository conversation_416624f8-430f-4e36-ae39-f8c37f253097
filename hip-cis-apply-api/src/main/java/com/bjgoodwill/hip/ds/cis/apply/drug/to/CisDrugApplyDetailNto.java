package com.bjgoodwill.hip.ds.cis.apply.drug.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "药品明细")
public class CisDrugApplyDetailNto extends DetailNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1629141655239423242L;

//    @Schema(description = "序号")
//    private Double sortNo;
//    @Schema(description = "就诊流水号")
//    private String visitCode;

    @Schema(description = "药品申请单ID")
    private String applyId;

    @Schema(description = "药品编码")
    private String drugCode;
    @Schema(description = "药品名称")
    private String drugName;
    @Schema(description = "每次剂量")
    @DecimalMin(value = "0", message = "每次剂量不能小于0！")
    private Double dosage;
    @Schema(description = "剂量单位 字典DosageUnit")
    private String dosageUnit;
    @Schema(description = "剂量单位 字典DosageUnit")
    private String dosageUnitName;
    @Schema(description = "包装总量")
    @DecimalMin(value = "0", message = "包装总量不能小于0！")
    private Double packageNum;
    @Schema(description = "包装单位 MinUnit/PackageUnit")
    private String packageUnit;
    @Schema(description = "包装单位 MinUnit/PackageUnit")
    private String packageUnitName;
    @Schema(description = "领药科室")
    private String receiveOrg;
    @Schema(description = "领药科室名称")
    private String receiveOrgName;

    //    public Double getSortNo() {
//        return sortNo;
//    }
//
//    public void setSortNo(Double sortNo) {
//        this.sortNo = sortNo;
//    }
    private SbadmWayEnum sbadmWay;
    //    @NotBlank(message = "就诊流水号不能为空！")
//    public String getVisitCode() {
//        return visitCode;
//    }
//
//    public void setVisitCode(String visitCode) {
//        this.visitCode = StringUtils.trimToNull(visitCode);
//    }
    @Schema(description = "是否皮试")
    private Boolean isSkin;
    @Schema(description = "皮试结果")
    private String skinResult;
    @Schema(description = "抗菌药使用说明:0-预防，1-治疗")
    private Integer antimicrobialsPurpose;
    @Schema(description = "特殊煎法：字典DecoctMethod")
    private String decoctMethodCode;
    @Schema(description = "特殊煎法：字典DecoctMethod")
    private String decoctMethodName;

    @NotBlank(message = "药品编码不能为空！")
    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = StringUtils.trimToNull(drugCode);
    }

    public String getDrugName() {
        return drugName;
    }

    public void setDrugName(String drugName) {
        this.drugName = StringUtils.trimToNull(drugName);
    }

    @NotNull(message = "每次剂量不能为空！")
    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = StringUtils.trimToNull(dosageUnit);
    }

    public Double getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    public String getPackageUnit() {
        return packageUnit;
    }

    public void setPackageUnit(String packageUnit) {
        this.packageUnit = StringUtils.trimToNull(packageUnit);
    }

    @NotBlank(message = "领药科室不能为空！")
    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = StringUtils.trimToNull(receiveOrg);
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    @NotNull(message = "带药方式不能为空！")
    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }

    public Boolean getIsSkin() {
        return isSkin;
    }

    public void setIsSkin(Boolean isSkin) {
        this.isSkin = isSkin;
    }

    public String getSkinResult() {
        return skinResult;
    }

    public void setSkinResult(String skinResult) {
        this.skinResult = skinResult;
    }

    public Integer getAntimicrobialsPurpose() {
        return antimicrobialsPurpose;
    }

    public void setAntimicrobialsPurpose(Integer antimicrobialsPurpose) {
        this.antimicrobialsPurpose = antimicrobialsPurpose;
    }

    @Override
    public String getServiceItemCode() {
        return drugCode;
    }

    public String getDecoctMethodCode() {
        return decoctMethodCode;
    }

    public void setDecoctMethodCode(String decoctMethodCode) {
        this.decoctMethodCode = decoctMethodCode;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    public String getPackageUnitName() {
        return packageUnitName;
    }

    public void setPackageUnitName(String packageUnitName) {
        this.packageUnitName = packageUnitName;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public String getDecoctMethodName() {
        return decoctMethodName;
    }

    public void setDecoctMethodName(String decoctMethodName) {
        this.decoctMethodName = decoctMethodName;
    }
}