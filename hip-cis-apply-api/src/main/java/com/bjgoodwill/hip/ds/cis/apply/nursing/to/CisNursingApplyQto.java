package com.bjgoodwill.hip.ds.cis.apply.nursing.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "护理")
public class CisNursingApplyQto extends CisBaseApplyQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -1305240741340318495L;

    @Schema(description = "自动计费")
    private String autoFlag;
    @Schema(description = "互斥")
    private String mutuallyExclusiveFlag;


    public String getAutoFlag() {
        return autoFlag;
    }

    public void setAutoFlag(String autoFlag) {
        this.autoFlag = autoFlag;
    }

    public String getMutuallyExclusiveFlag() {
        return mutuallyExclusiveFlag;
    }

    public void setMutuallyExclusiveFlag(String mutuallyExclusiveFlag) {
        this.mutuallyExclusiveFlag = mutuallyExclusiveFlag;
    }
}