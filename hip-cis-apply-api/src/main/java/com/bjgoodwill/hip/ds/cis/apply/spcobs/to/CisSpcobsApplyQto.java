package com.bjgoodwill.hip.ds.cis.apply.spcobs.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "检验类申请单")
public class CisSpcobsApplyQto extends CisBaseApplyQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6175020385000982915L;


}