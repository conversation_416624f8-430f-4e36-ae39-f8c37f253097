package com.bjgoodwill.hip.ds.cis.apply.apply.to;


import com.bjgoodwill.hip.business.util.cis.common.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "定制TO")
public class CisBaseApplyCustomTo extends CisBaseApplyTo implements Serializable {
    @Serial
    private static final long serialVersionUID = -4631361189968655101L;
    @Schema(description = "申请单ID")
    private String applyid;
    @Schema(description = "序号")
    private Double sortNo;
    @Schema(description = "药品编码")
    private String drugCode;
    @Schema(description = "药品名称")
    private String drugName;
    @Schema(description = "每次剂量")
    private Double dosage;
    @Schema(description = "剂量单位 字典DosageUnit")
    private String dosageUnit;
    @Schema(description = "剂量单位 字典DosageUnit")
    private String dosageUnitName;
    @Schema(description = "包装总量")
    private Double packageNum;
    @Schema(description = "包装单位 MinUnit/PackageUnit")
    private String packageUnit;
    @Schema(description = "包装单位 MinUnit/PackageUnit")
    private String packageUnitName;
    @Schema(description = "领药科室")
    private String receiveOrg;
    @Schema(description = "领药科室名称")
    private String receiveOrgName;
    private SbadmWayEnum sbadmWay;
    @Schema(description = "是否皮试")
    private Boolean isSkin;
    @Schema(description = "皮试结果")
    private String skinResult;
    @Schema(description = "抗菌药使用说明:0-预防，1-治疗")
    private Integer antimicrobialsPurpose;
    @Schema(description = "特殊煎法：字典DecoctMethod")
    private String decoctMethodCode;
    @Schema(description = "特殊煎法：字典DecoctMethod")
    private String decoctMethodName;
    @Schema(description = "用法")
    private String usage;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "频次")
    private String frequency;
    @Schema(description = "频次名称")
    private String frequencyName;
    @Schema(description = "疗程")
    private String treatmentCourse;
    @Schema(description = "疗程单位")
    private String treatmentCourseUnit;
    @Schema(description = "疗程单位名称")
    private String treatmentCourseUnitName;
    @Schema(description = "协定处方")
    private Boolean prescriptionFlag;
    @Schema(description = "滴速")
    private String dripSpeed;
    @Schema(description = "滴速单位")
    private String dripSpeedUnit;
    @Schema(description = "滴速单位名称")
    private String dripSpeedUnitName;
    @Schema(description = "付数")
    private String doseNum;
    @Schema(description = "草药煎法;0自煎,1代煎")
    private Integer decoction;
    @Schema(description = "草药一付包数")
    private Integer cdrugPackNum;
    @Schema(description = "草药每包毫升数")
    private Integer cdrugPackMl;
    @Schema(description = "草药属性（DrugHerbsProEnum）")
    private String herbsProCode;
    @Schema(description = "手术编码")
    private String operationCode;
    @Schema(description = "手术名称")
    private String operationName;
    @Schema(description = "项目编码")
    private String serviceItemCode;
    @Schema(description = "项目名称")
    private String serviceItemName;
    @Schema(description = "手术级别")
    private String operationLevel;
    @Schema(description = "部位")
    private String humanOrgans;
    @Schema(description = "部位名称")
    private String humanOrgansName;
    @Schema(description = "体位")
    private String decubitus;
    @Schema(description = "体位名称")
    private String decubitusName;
    @Schema(description = "标本")
    private String speciman;
    @Schema(description = "标本名称")
    private String specimanName;
    @Schema(description = "离体时间")
    private LocalDateTime outVivoDate;
    @Schema(description = "检验名称")
    private String spcobsName;
    @Schema(description = "检验设备类型 字典SpcobsDeviceType")
    private String deviceType;
    @Schema(description = "检验设备类型 字典SpcobsDeviceType")
    private String deviceTypeName;
    @Schema(description = "试管号")
    private String testTubeId;
    @Schema(description = "方法 字典SpcobsMethod")
    private String method;
    @Schema(description = "方法 字典SpcobsMethod")
    private String methodName;
    @Schema(description = "检验项目编码")
    private String spcobsCode;
    @Schema(description = "检查名称")
    private String dgimgName;
    @Schema(description = "人体系统")
    private String humanSystem;
    @Schema(description = "人体系统名称")
    private String humanSystemName;
    @Schema(description = "范围")
    private String range;
    @Schema(description = "范围名称")
    private String rangeName;
    @Schema(description = "方位")
    private String direction;
    @Schema(description = "方位名称")
    private String directionName;
    @Schema(description = "layer")
    private String layer;
    @Schema(description = "层数名称")
    private String layerName;
    @Schema(description = "要求与目的")
    private String requirementPurpose;
    @Schema(description = "状态")
    private CisStatusEnum statusCode;
    @Schema(description = "检查项目编码")
    private String dgimgCode;
    @Schema(description = "单侧标记：左，右，双侧")
    private CisUnilateralEnum unilateralFlag;
    @Schema(description = "扩展编码")
    private String extTypeCode;
    @Schema(description = "操作")
    private String operation;
    @Schema(description = "长嘱、临嘱")
    private OrderTypeEnum orderType;
    @Schema(description = "医嘱类型")
    private SystemTypeEnum systemType;

    public String getApplyid() {
        return applyid;
    }

    public void setApplyid(String applyid) {
        this.applyid = applyid;
    }

    @Override
    public Double getSortNo() {
        return sortNo;
    }

    @Override
    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    public String getDrugName() {
        return drugName;
    }

    public void setDrugName(String drugName) {
        this.drugName = drugName;
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    public Double getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    public String getPackageUnit() {
        return packageUnit;
    }

    public void setPackageUnit(String packageUnit) {
        this.packageUnit = packageUnit;
    }

    public String getPackageUnitName() {
        return packageUnitName;
    }

    public void setPackageUnitName(String packageUnitName) {
        this.packageUnitName = packageUnitName;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }

    public Boolean getSkin() {
        return isSkin;
    }

    public void setSkin(Boolean skin) {
        isSkin = skin;
    }

    public String getSkinResult() {
        return skinResult;
    }

    public void setSkinResult(String skinResult) {
        this.skinResult = skinResult;
    }

    public Integer getAntimicrobialsPurpose() {
        return antimicrobialsPurpose;
    }

    public void setAntimicrobialsPurpose(Integer antimicrobialsPurpose) {
        this.antimicrobialsPurpose = antimicrobialsPurpose;
    }

    public String getDecoctMethodCode() {
        return decoctMethodCode;
    }

    public void setDecoctMethodCode(String decoctMethodCode) {
        this.decoctMethodCode = decoctMethodCode;
    }

    public String getDecoctMethodName() {
        return decoctMethodName;
    }

    public void setDecoctMethodName(String decoctMethodName) {
        this.decoctMethodName = decoctMethodName;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public String getTreatmentCourse() {
        return treatmentCourse;
    }

    public void setTreatmentCourse(String treatmentCourse) {
        this.treatmentCourse = treatmentCourse;
    }

    public String getTreatmentCourseUnit() {
        return treatmentCourseUnit;
    }

    public void setTreatmentCourseUnit(String treatmentCourseUnit) {
        this.treatmentCourseUnit = treatmentCourseUnit;
    }

    public String getTreatmentCourseUnitName() {
        return treatmentCourseUnitName;
    }

    public void setTreatmentCourseUnitName(String treatmentCourseUnitName) {
        this.treatmentCourseUnitName = treatmentCourseUnitName;
    }

    public Boolean getPrescriptionFlag() {
        return prescriptionFlag;
    }

    public void setPrescriptionFlag(Boolean prescriptionFlag) {
        this.prescriptionFlag = prescriptionFlag;
    }

    public String getDripSpeed() {
        return dripSpeed;
    }

    public void setDripSpeed(String dripSpeed) {
        this.dripSpeed = dripSpeed;
    }

    public String getDripSpeedUnit() {
        return dripSpeedUnit;
    }

    public void setDripSpeedUnit(String dripSpeedUnit) {
        this.dripSpeedUnit = dripSpeedUnit;
    }

    public String getDripSpeedUnitName() {
        return dripSpeedUnitName;
    }

    public void setDripSpeedUnitName(String dripSpeedUnitName) {
        this.dripSpeedUnitName = dripSpeedUnitName;
    }

    public String getDoseNum() {
        return doseNum;
    }

    public void setDoseNum(String doseNum) {
        this.doseNum = doseNum;
    }

    public Integer getDecoction() {
        return decoction;
    }

    public void setDecoction(Integer decoction) {
        this.decoction = decoction;
    }

    public Integer getCdrugPackNum() {
        return cdrugPackNum;
    }

    public void setCdrugPackNum(Integer cdrugPackNum) {
        this.cdrugPackNum = cdrugPackNum;
    }

    public Integer getCdrugPackMl() {
        return cdrugPackMl;
    }

    public void setCdrugPackMl(Integer cdrugPackMl) {
        this.cdrugPackMl = cdrugPackMl;
    }

    public String getHerbsProCode() {
        return herbsProCode;
    }

    public void setHerbsProCode(String herbsProCode) {
        this.herbsProCode = herbsProCode;
    }

    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    @Override
    public String getServiceItemCode() {
        return serviceItemCode;
    }

    @Override
    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    @Override
    public String getServiceItemName() {
        return serviceItemName;
    }

    @Override
    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getOperationLevel() {
        return operationLevel;
    }

    public void setOperationLevel(String operationLevel) {
        this.operationLevel = operationLevel;
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    public String getHumanOrgansName() {
        return humanOrgansName;
    }

    public void setHumanOrgansName(String humanOrgansName) {
        this.humanOrgansName = humanOrgansName;
    }

    public String getDecubitus() {
        return decubitus;
    }

    public void setDecubitus(String decubitus) {
        this.decubitus = decubitus;
    }

    public String getDecubitusName() {
        return decubitusName;
    }

    public void setDecubitusName(String decubitusName) {
        this.decubitusName = decubitusName;
    }

    public String getSpeciman() {
        return speciman;
    }

    public void setSpeciman(String speciman) {
        this.speciman = speciman;
    }

    public String getSpecimanName() {
        return specimanName;
    }

    public void setSpecimanName(String specimanName) {
        this.specimanName = specimanName;
    }

    public LocalDateTime getOutVivoDate() {
        return outVivoDate;
    }

    public void setOutVivoDate(LocalDateTime outVivoDate) {
        this.outVivoDate = outVivoDate;
    }

    public String getSpcobsName() {
        return spcobsName;
    }

    public void setSpcobsName(String spcobsName) {
        this.spcobsName = spcobsName;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    public String getTestTubeId() {
        return testTubeId;
    }

    public void setTestTubeId(String testTubeId) {
        this.testTubeId = testTubeId;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getSpcobsCode() {
        return spcobsCode;
    }

    public void setSpcobsCode(String spcobsCode) {
        this.spcobsCode = spcobsCode;
    }

    public String getDgimgName() {
        return dgimgName;
    }

    public void setDgimgName(String dgimgName) {
        this.dgimgName = dgimgName;
    }

    public String getHumanSystem() {
        return humanSystem;
    }

    public void setHumanSystem(String humanSystem) {
        this.humanSystem = humanSystem;
    }

    public String getHumanSystemName() {
        return humanSystemName;
    }

    public void setHumanSystemName(String humanSystemName) {
        this.humanSystemName = humanSystemName;
    }

    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = range;
    }

    public String getRangeName() {
        return rangeName;
    }

    public void setRangeName(String rangeName) {
        this.rangeName = rangeName;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getDirectionName() {
        return directionName;
    }

    public void setDirectionName(String directionName) {
        this.directionName = directionName;
    }

    public String getLayer() {
        return layer;
    }

    public void setLayer(String layer) {
        this.layer = layer;
    }

    public String getLayerName() {
        return layerName;
    }

    public void setLayerName(String layerName) {
        this.layerName = layerName;
    }

    public String getRequirementPurpose() {
        return requirementPurpose;
    }

    public void setRequirementPurpose(String requirementPurpose) {
        this.requirementPurpose = requirementPurpose;
    }

    @Override
    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    @Override
    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public String getDgimgCode() {
        return dgimgCode;
    }

    public void setDgimgCode(String dgimgCode) {
        this.dgimgCode = dgimgCode;
    }

    public CisUnilateralEnum getUnilateralFlag() {
        return unilateralFlag;
    }

    public void setUnilateralFlag(CisUnilateralEnum unilateralFlag) {
        this.unilateralFlag = unilateralFlag;
    }

    public String getExtTypeCode() {
        return extTypeCode;
    }

    public void setExtTypeCode(String extTypeCode) {
        this.extTypeCode = extTypeCode;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderTypeEnum orderType) {
        this.orderType = orderType;
    }

    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }
}