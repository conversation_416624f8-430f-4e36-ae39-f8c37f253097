package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-15 15:56
 * @className: CisSplitEto
 * @description:
 **/
@Schema(description = "申请单拆分")
public class CisSplitEto implements Serializable {


    @Serial
    private static final long serialVersionUID = -400522152516694469L;

    @Schema(description = "患者流水号")
    @NotBlank(message = "患者流水号！")
    private String visitCode;

    @Schema(description = "医嘱ID")
    @NotBlank(message = "医嘱ID")
    private String orderId;

    @Schema(description = "拆分起始时间--长期医嘱")
    private LocalDateTime beginDate;

    @Schema(description = "拆分截至时间--长期医嘱")
    private LocalDateTime endDate;

    private String firstDayTimepoint;
    private OrderTypeEnum orderTypeEnum;
    private String nurseDeptCode;

    public CisSplitEto() {
    }

    public CisSplitEto(String visitCode, String orderId) {
        setVisitCode(visitCode);
        setOrderId(orderId);
    }

    public CisSplitEto(String visitCode, String orderId, LocalDateTime beginDate, LocalDateTime endDate, String firstDayTimepoint) {
        this(visitCode, orderId);
        setBeginDate(beginDate);
        setEndDate(endDate);
        setFirstDayTimepoint(firstDayTimepoint);
    }

    public CisSplitEto(String visitCode, String orderId, LocalDateTime beginDate, LocalDateTime endDate,
                       String firstDayTimepoint, OrderTypeEnum orderTypeEnum) {
        this(visitCode, orderId, beginDate, endDate, firstDayTimepoint);
        setOrderTypeEnum(orderTypeEnum);
    }

    public String getFirstDayTimepoint() {
        return firstDayTimepoint;
    }

    public void setFirstDayTimepoint(String firstDayTimepoint) {
        this.firstDayTimepoint = firstDayTimepoint;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public LocalDateTime getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDateTime beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }

    public OrderTypeEnum getOrderTypeEnum() {
        return orderTypeEnum;
    }

    public void setOrderTypeEnum(OrderTypeEnum orderTypeEnum) {
        this.orderTypeEnum = orderTypeEnum;
    }

    @NotBlank(message = "护理组号不能为空！")
    public String getNurseDeptCode() {
        return nurseDeptCode;
    }

    public void setNurseDeptCode(String nurseDeptCode) {
        this.nurseDeptCode = nurseDeptCode;
    }
}