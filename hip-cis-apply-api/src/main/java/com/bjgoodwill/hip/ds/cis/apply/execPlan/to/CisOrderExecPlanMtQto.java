package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseTo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-03-01 14:09
 * @className: CisOrderExecPlanMtQto
 * @description:
 **/
@Schema(description = "医技执行查询")
public class CisOrderExecPlanMtQto extends BaseTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -5816949167034278869L;

    @Schema(description = "执行科室")
    private String execDeptCode;
    @Schema(description = "执行单状态")
    private CisStatusEnum[] statusCodes;
    @Schema(description = "开方科室")
    private List<String> createOrgCodes;
    @Schema(description = "患者流水号")
    private String visitCode;
    @Schema(description = "执行起始时间")
    private LocalDateTime beginDate;
    @Schema(description = "执行截止时间")
    private LocalDateTime endDate;

    @NotBlank(message = "执行科室不能为空")
    public String getExecDeptCode() {
        return execDeptCode;
    }

    public void setExecDeptCode(String execDeptCode) {
        this.execDeptCode = execDeptCode;
    }

    public CisStatusEnum[] getStatusCodes() {
        return statusCodes;
    }

    public void setStatusCodes(CisStatusEnum[] statusCodes) {
        this.statusCodes = statusCodes;
    }

    public List<String> getCreateOrgCodes() {
        return createOrgCodes;
    }

    public void setCreateOrgCodes(List<String> createOrgCodes) {
        this.createOrgCodes = createOrgCodes;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public LocalDateTime getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDateTime beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }


}