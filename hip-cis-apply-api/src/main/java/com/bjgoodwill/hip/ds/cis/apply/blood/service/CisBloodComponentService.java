package com.bjgoodwill.hip.ds.cis.apply.blood.service;

import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodComponentEto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodComponentNto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodComponentTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@Tag(name = "血液成分从表领域服务", description = "血液成分从表领域服务")
public interface CisBloodComponentService {

    @Operation(summary = "P0根据唯一标识返回血液成分从表。")
    @GetMapping("/cisBloodComponents/{id:.+}")
    CisBloodComponentTo getCisBloodComponentById(@PathVariable("id") String id);

    @Operation(summary = "P0创建血液成分从表。")
    @PostMapping("/cisBloodComponents")
    CisBloodComponentTo createCisBloodComponent(@RequestBody @Valid CisBloodComponentNto cisBloodComponentNto);

    @Operation(summary = "P0根据唯一标识修改血液成分从表。")
    @PutMapping("/cisBloodComponents/{id:.+}")
    void updateCisBloodComponent(@PathVariable("id") String id, @RequestBody @Valid CisBloodComponentEto cisBloodComponentEto);

    @Operation(summary = "P0根据唯一标识删除血液成分从表。")
    @DeleteMapping("/cisBloodComponents/{id:.+}")
    void deleteCisBloodComponent(@PathVariable("id") String id);

}