package com.bjgoodwill.hip.ds.cis.apply.overstep.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisOverstepEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-03-12 19:42
 * @className: CisOverStepNto
 * @description:
 **/
public class CisOverStepNto extends BaseNto implements Serializable {
    @Serial
    private static final long serialVersionUID = -2092557003546112577L;

    @Schema(description = "审核类型， 抗菌药，手术")
    private CisOverstepEnum checkSystemType;

    @Schema(description = "申请原因")
    private String applyReason;

    @NotNull(message = "越级审批类型枚举不能为空！")
    public CisOverstepEnum getCheckSystemType() {
        return checkSystemType;
    }

    public void setCheckSystemType(CisOverstepEnum checkSystemType) {
        this.checkSystemType = checkSystemType;
    }

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = StringUtils.trimToNull(applyReason);
    }
}