package com.bjgoodwill.hip.ds.cis.apply.palg.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "病理明细")
public class CisPalgApplyDetailTo extends DetailTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -6746861918369457967L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "病理申请单标识")
    private String cisPalgApplyId;
    @Schema(description = "序号")
    private Double sortNo;
    @Schema(description = "部位")
    private String humanOrgans;
    @Schema(description = "标本")
    private String speciman;
    @Schema(description = "数量")
    private Integer num;
    @Schema(description = "离体时间")
    private LocalDateTime outVivoDate;
    @Schema(description = "逻辑删除标记")
    private boolean deleted;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    private String serviceItemCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCisPalgApplyId() {
        return cisPalgApplyId;
    }

    public void setCisPalgApplyId(String cisPalgApplyId) {
        this.cisPalgApplyId = cisPalgApplyId;
    }

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    public String getSpeciman() {
        return speciman;
    }

    public void setSpeciman(String speciman) {
        this.speciman = speciman;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public LocalDateTime getOutVivoDate() {
        return outVivoDate;
    }

    public void setOutVivoDate(LocalDateTime outVivoDate) {
        this.outVivoDate = outVivoDate;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisPalgApplyDetailTo other = (CisPalgApplyDetailTo) obj;
        return Objects.equals(id, other.id);
    }
}