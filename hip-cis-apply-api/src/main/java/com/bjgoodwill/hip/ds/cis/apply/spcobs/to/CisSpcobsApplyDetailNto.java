package com.bjgoodwill.hip.ds.cis.apply.spcobs.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "检验申请单明细")
public class CisSpcobsApplyDetailNto extends DetailNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -3169524959675659472L;

    //    @Schema(description = "标识")
//    private String id;
//    @Schema(description = "就诊流水号")
//    private String visitCode;
//    @Schema(description = "内部序号")
//    private Double no;
    @Schema(description = "检验名称")
    private String spcobsName;
    // 检验项目编码
    @Schema(description = "检验项目编码")
    private String spcobsCode;

    @Schema(description = "检验设备类型 字典SpcobsDeviceType")
    private String deviceType;
    @Schema(description = "检验设备类型 字典SpcobsDeviceType")
    private String deviceTypeName;
    @Schema(description = "试管号")
    private String testTubeId;
    @Schema(description = "方法 字典SpcobsMethod")
    private String method;
    @Schema(description = "方法 字典SpcobsMethod")
    private String methodName;


//    @NotBlank(message = "标识不能为空！")
//    @Size(max = 50, message = "标识长度不能超过50个字符！")
//    public String getId() {
//        return id;
//    }
//
//    public void setId(String id) {
//        this.id = StringUtils.trimToNull(id);
//    }
//
//    @NotBlank(message = "就诊流水号不能为空！")
//    public String getVisitCode() {
//        return visitCode;
//    }
//
//    public void setVisitCode(String visitCode) {
//        this.visitCode = StringUtils.trimToNull(visitCode);
//    }
//
//    public Double getNo() {
//        return no;
//    }
//
//    public void setNo(Double no) {
//        this.no = no;
//    }

    @NotBlank(message = "检验名称不能为空！")
    public String getSpcobsName() {
        return spcobsName;
    }

    public void setSpcobsName(String spcobsName) {
        this.spcobsName = StringUtils.trimToNull(spcobsName);
    }

    @NotBlank(message = "检验项目编码不能为空！")
    public String getSpcobsCode() {
        return spcobsCode;
    }

    public void setSpcobsCode(String spcobsCode) {
        this.spcobsCode = spcobsCode;
    }

    @NotBlank(message = "检验设备类型 字典SpcobsDeviceType不能为空！")
    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = StringUtils.trimToNull(deviceType);
    }

    public String getTestTubeId() {
        return testTubeId;
    }

    public void setTestTubeId(String testTubeId) {
        this.testTubeId = StringUtils.trimToNull(testTubeId);
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = StringUtils.trimToNull(method);
    }

    @Override
    public String getServiceItemCode() {
        return spcobsCode;
    }

    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }
}