package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;


import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "医嘱执行档")
public class CisOrderExecPlanQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -5816949167034278864L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "就诊流水号")
    private String visitCode;
    @Schema(description = "科室编码")
    private String orgCode;
    @Schema(description = "护理组号")
    private String deptNurseCode;
    @Schema(description = "医嘱号")
    private String orderId;
    @Schema(description = "抽象父类标识")
    private String cisBaseApplyId;
    @Schema(description = "orderClass")
    private SystemTypeEnum orderClass;
    @Schema(description = "医嘱预计执行时间")
    private LocalDateTime execPlanDate;
    @Schema(description = "执行时间")
    private String execDate;
    @Schema(description = "statusCode")
    private CisStatusEnum statusCode;
    @Schema(description = "是否计费")
    private Boolean isCharge;
    @Schema(description = "领药科室")
    private String receiveOrg;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCisBaseApplyId() {
        return cisBaseApplyId;
    }

    public void setCisBaseApplyId(String cisBaseApplyId) {
        this.cisBaseApplyId = cisBaseApplyId;
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    public LocalDateTime getExecPlanDate() {
        return execPlanDate;
    }

    public void setExecPlanDate(LocalDateTime execPlanDate) {
        this.execPlanDate = execPlanDate;
    }

    public String getExecDate() {
        return execDate;
    }

    public void setExecDate(String execDate) {
        this.execDate = execDate;
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public Boolean getIsCharge() {
        return isCharge;
    }

    protected void setIsCharge(Boolean isCharge) {
        this.isCharge = isCharge;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }
}