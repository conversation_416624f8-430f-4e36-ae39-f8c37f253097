package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "处置")
public class CisManagementApplyTo extends CisBaseApplyTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -9087143017195024042L;

    @Schema(description = "频次")
    private String frequency;
    @Schema(description = "频次名称")
    private String frequencyName;

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }
}