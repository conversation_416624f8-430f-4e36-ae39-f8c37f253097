package com.bjgoodwill.hip.ds.cis.apply.drug.service;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanCDrugQto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "中草药申请单领域服务", description = "中草药申请单领域服务")
public interface CisCDrugApplyService extends CisBaseDrugApplyService {

    @Operation(summary = "P0根据唯一标识返回中草药申请单。")
    @GetMapping("/cisCDrugApplies/{id:.+}")
    CisCDrugApplyTo getCisCDrugApplyById(@PathVariable("id") String id);

    @Operation(summary = "P0创建中草药申请单。")
    @PostMapping("/cisCDrugApplies")
    CisCDrugApplyTo createCisCDrugApply(@RequestBody @Valid CisCDrugApplyNto cisCDrugApplyNto);

    @Operation(summary = "P0根据唯一标识修改中草药申请单。")
    @PutMapping("/cisCDrugApplies/{id:.+}")
    void updateCisCDrugApply(@PathVariable("id") String id, @RequestBody @Valid CisCDrugApplyEto cisCDrugApplyEto);

    @Operation(summary = "P0根据唯一标识删除中草药申请单。")
    @DeleteMapping("/cisCDrugApplies/{id:.+}")
    void deleteCisCDrugApply(@PathVariable("id") String id);

    @Operation(summary = "P0中草药发药申请。")
    @GetMapping("/cisBaseDrugApplies/cdrug-send")
    List<CisBaseApplyTo> getCDrugSend(@RequestBody @Valid CisOrderExecPlanCDrugQto cisOrderExecPlanDrugQto);

}