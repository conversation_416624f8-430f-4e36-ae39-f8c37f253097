package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-04-10 17:45
 * @className: CisOrderExecPlanWithDetialTo
 * @description:
 **/
@Schema(description = "传输数据给cdr用")
public class CisOrderExecPlanWithDetialTo implements Serializable {

    private List<CisOrderExecPlanTo> cisOrderExecPlanTos;

    private List<DetailTo> detailTos;

    public List<CisOrderExecPlanTo> getCisOrderExecPlanTos() {
        return cisOrderExecPlanTos;
    }

    public void setCisOrderExecPlanTos(List<CisOrderExecPlanTo> cisOrderExecPlanTos) {
        this.cisOrderExecPlanTos = cisOrderExecPlanTos;
    }

    public List<DetailTo> getDetailTos() {
        return detailTos;
    }

    public void setDetailTos(List<DetailTo> detailTos) {
        this.detailTos = detailTos;
    }
}