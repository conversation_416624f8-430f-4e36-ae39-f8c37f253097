package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.io.Serializable;
import java.util.List;

/**
 * @program: cis-base
 * @author: xdguo
 * @create: 2025-05-19 15:14
 * @className: CisBaseApplySplitGroupNewNtoLst
 * @description:
 **/
@Schema(description = "申请单拆组新,全部拆成单支")
public class CisBaseApplySplitGroupNewNtoLst extends BaseNto implements Serializable {

    @Schema(description = "医嘱ID")
    private String orderId;

    @Schema(description = "明细")
    private List<CisBaseApplySplitGroupNewNto> ntos;

    @NotBlank(message = "医嘱ID不能为空！")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @NotEmpty(message = "明细不能为空！")
    public List<CisBaseApplySplitGroupNewNto> getNtos() {
        return ntos;
    }

    public void setNtos(List<CisBaseApplySplitGroupNewNto> ntos) {
        this.ntos = ntos;
    }
}