package com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "患者病史")
public class CisMedicalHistoryTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -6880952719975970695L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "患者流水号")
    private String visitCode;
    @Schema(description = "主诉")
    private String chiefComplaint;
    @Schema(description = "现病史")
    private String presentIllnessHistory;
    @Schema(description = "既往史")
    private String pastMedicalHistory;
    @Schema(description = "过敏史")
    private String allergicHistory;
    @Schema(description = "个人史")
    private String socialHistory;
    @Schema(description = "家族史")
    private String familyHistory;
    @Schema(description = "治疗方案")
    private String treatmentPlan;
    @Schema(description = "创建时间")
    private LocalDateTime createdDate;
    @Schema(description = "创建人员编码")
    private String createdStaff;
    @Schema(description = "创建人员名称")
    private String createdStaffName;
    @Schema(description = "修改时间")
    private LocalDateTime updatedDate;
    @Schema(description = "修改人员编码")
    private String updatedStaff;
    @Schema(description = "修改人员名称")
    private String updatedStaffName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getChiefComplaint() {
        return chiefComplaint;
    }

    public void setChiefComplaint(String chiefComplaint) {
        this.chiefComplaint = chiefComplaint;
    }

    public String getPresentIllnessHistory() {
        return presentIllnessHistory;
    }

    public void setPresentIllnessHistory(String presentIllnessHistory) {
        this.presentIllnessHistory = presentIllnessHistory;
    }

    public String getPastMedicalHistory() {
        return pastMedicalHistory;
    }

    public void setPastMedicalHistory(String pastMedicalHistory) {
        this.pastMedicalHistory = pastMedicalHistory;
    }

    public String getAllergicHistory() {
        return allergicHistory;
    }

    public void setAllergicHistory(String allergicHistory) {
        this.allergicHistory = allergicHistory;
    }

    public String getSocialHistory() {
        return socialHistory;
    }

    public void setSocialHistory(String socialHistory) {
        this.socialHistory = socialHistory;
    }

    public String getFamilyHistory() {
        return familyHistory;
    }

    public void setFamilyHistory(String familyHistory) {
        this.familyHistory = familyHistory;
    }

    public String getTreatmentPlan() {
        return treatmentPlan;
    }

    public void setTreatmentPlan(String treatmentPlan) {
        this.treatmentPlan = treatmentPlan;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisMedicalHistoryTo other = (CisMedicalHistoryTo) obj;
        return Objects.equals(id, other.id);
    }
}