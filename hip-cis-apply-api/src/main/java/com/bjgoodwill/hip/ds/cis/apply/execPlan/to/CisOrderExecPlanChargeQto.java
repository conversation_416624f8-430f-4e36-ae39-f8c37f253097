package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医嘱执行档费用从表")
public class CisOrderExecPlanChargeQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2612074157814754565L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "医嘱执行档标识")
    private String cisOrderExecPlanId;
    @Schema(description = "抽象父类标识")
    private String cisBaseApplyId;
    @Schema(description = "患者接诊流水号")
    private String visitCode;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getCisOrderExecPlanId() {
        return cisOrderExecPlanId;
    }

    public void setCisOrderExecPlanId(String cisOrderExecPlanId) {
        this.cisOrderExecPlanId = cisOrderExecPlanId;
    }

    public String getCisBaseApplyId() {
        return cisBaseApplyId;
    }

    public void setCisBaseApplyId(String cisBaseApplyId) {
        this.cisBaseApplyId = cisBaseApplyId;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }
}