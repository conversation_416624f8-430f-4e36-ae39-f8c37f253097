package com.bjgoodwill.hip.ds.cis.apply.diag.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "申请单诊断")
public class ApplyDiagnosisNto extends BaseNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6765012739929398640L;

    @Schema(description = "抽象父类标识")
    private String cisBaseApplyId;
    @Schema(description = "标识")
    private String id;
    @Schema(description = "diagCode")
    private String diagCode;
    @Schema(description = "诊断名称")
    private String diagName;

    @Size(max = 50, message = "抽象父类标识长度不能超过50个字符！")
    public String getCisBaseApplyId() {
        return cisBaseApplyId;
    }

    public void setCisBaseApplyId(String cisBaseApplyId) {
        this.cisBaseApplyId = StringUtils.trimToNull(cisBaseApplyId);
    }

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    public String getDiagCode() {
        return diagCode;
    }

    public void setDiagCode(String diagCode) {
        this.diagCode = StringUtils.trimToNull(diagCode);
    }

    public String getDiagName() {
        return diagName;
    }

    public void setDiagName(String diagName) {
        this.diagName = StringUtils.trimToNull(diagName);
    }
}