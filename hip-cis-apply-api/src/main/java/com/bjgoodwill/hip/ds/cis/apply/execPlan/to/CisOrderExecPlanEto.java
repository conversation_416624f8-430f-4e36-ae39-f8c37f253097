package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "医嘱执行档")
public class CisOrderExecPlanEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6776465966480452840L;

    @Schema(description = "皮试结果")
    private String skinResult;
    @Schema(description = "statusCode")
    private CisStatusEnum statusCode;
    @Schema(description = "版本")
    private Integer version;
    private Double num;
    @Schema(description = "皮试操作时间(区别于结果录入时间)")
    private String skinTestDate;
    @Schema(description = "皮试人1")
    private String stStaffA;
    @Schema(description = "皮试人1名称")
    private String stStaffAName;
    @Schema(description = "皮试人2")
    private String stStaffB;
    @Schema(description = "皮试人2名称")
    private String stStaffBName;
    @Schema(description = "药品批号")
    private String batchNo;

    public String getSkinResult() {
        return skinResult;
    }

    public void setSkinResult(String skinResult) {
        this.skinResult = StringUtils.trimToNull(skinResult);
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public String getSkinTestDate() {
        return skinTestDate;
    }

    public void setSkinTestDate(String skinTestDate) {
        this.skinTestDate = skinTestDate;
    }

    public String getStStaffA() {
        return stStaffA;
    }

    public void setStStaffA(String stStaffA) {
        this.stStaffA = stStaffA;
    }

    public String getStStaffAName() {
        return stStaffAName;
    }

    public void setStStaffAName(String stStaffAName) {
        this.stStaffAName = stStaffAName;
    }

    public String getStStaffB() {
        return stStaffB;
    }

    public void setStStaffB(String stStaffB) {
        this.stStaffB = stStaffB;
    }

    public String getStStaffBName() {
        return stStaffBName;
    }

    public void setStStaffBName(String stStaffBName) {
        this.stStaffBName = stStaffBName;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }
}