package com.bjgoodwill.hip.ds.cis.apply.spcobs.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Schema(description = "检验申请单明细")
public class CisSpcobsApplyDetailTo extends DetailTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -7336072460583360703L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "检验类申请单标识")
    private String cisSpcobsApplyId;
    @Schema(description = "内部序号")
    private Double no;
    @Schema(description = "检验名称")
    private String spcobsName;
    @Schema(description = "检验设备类型 字典SpcobsDeviceType")
    private String deviceType;
    @Schema(description = "检验设备类型 字典SpcobsDeviceType")
    private String deviceTypeName;
    @Schema(description = "试管号")
    private String testTubeId;
    @Schema(description = "方法 字典SpcobsMethod")
    private String method;
    @Schema(description = "方法 字典SpcobsMethod")
    private String methodName;
    @Schema(description = "标本 字典Speciman")
    private String speciman;
    @Schema(description = "检验项目编码")
    private String spcobsCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCisSpcobsApplyId() {
        return cisSpcobsApplyId;
    }

    public void setCisSpcobsApplyId(String cisSpcobsApplyId) {
        this.cisSpcobsApplyId = cisSpcobsApplyId;
    }

    public Double getNo() {
        return no;
    }

    public void setNo(Double no) {
        this.no = no;
    }

    public String getSpcobsName() {
        return spcobsName;
    }

    public void setSpcobsName(String spcobsName) {
        this.spcobsName = spcobsName;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getTestTubeId() {
        return testTubeId;
    }

    public void setTestTubeId(String testTubeId) {
        this.testTubeId = testTubeId;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getSpeciman() {
        return speciman;
    }

    public void setSpeciman(String speciman) {
        this.speciman = speciman;
    }

    public String getSpcobsCode() {
        return spcobsCode;
    }

    public void setSpcobsCode(String spcobsCode) {
        this.spcobsCode = spcobsCode;
    }

    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisSpcobsApplyDetailTo other = (CisSpcobsApplyDetailTo) obj;
        return Objects.equals(id, other.id);
    }
}