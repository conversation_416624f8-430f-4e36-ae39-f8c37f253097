package com.bjgoodwill.hip.ds.cis.apply.book.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "申请单预约")
public class ApplyBookEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -3949214399221382729L;

    @Schema(description = "预约结束时间")
    private String appointsEndDate;

    public String getAppointsEndDate() {
        return appointsEndDate;
    }

    public void setAppointsEndDate(String appointsEndDate) {
        this.appointsEndDate = StringUtils.trimToNull(appointsEndDate);
    }
}