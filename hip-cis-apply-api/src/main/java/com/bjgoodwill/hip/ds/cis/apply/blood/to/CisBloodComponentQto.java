package com.bjgoodwill.hip.ds.cis.apply.blood.to;

import com.bjgoodwill.hip.business.util.common.to.BaseTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "血液成分从表")
public class CisBloodComponentQto extends BaseTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1531395092970220663L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "输血申请单标识")
    private String cisBloodApplyId;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getCisBloodApplyId() {
        return cisBloodApplyId;
    }

    public void setCisBloodApplyId(String cisBloodApplyId) {
        this.cisBloodApplyId = cisBloodApplyId;
    }
}