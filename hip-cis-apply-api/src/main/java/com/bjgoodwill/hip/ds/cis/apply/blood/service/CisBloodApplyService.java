package com.bjgoodwill.hip.ds.cis.apply.blood.service;

import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyService;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodApplyTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@Tag(name = "备血申请单领域服务", description = "备血申请单领域服务")
public interface CisBloodApplyService extends CisBaseApplyService {

    @Operation(summary = "P0根据唯一标识返回备血申请单。")
    @GetMapping("/cisPreparationBloodApplies/{id:.+}")
    CisBloodApplyTo getCisPreparationBloodApplyById(@PathVariable("id") String id);

    @Operation(summary = "P0创建备血申请单。")
    @PostMapping("/cisPreparationBloodApplies")
    CisBloodApplyTo createCisPreparationBloodApply(@RequestBody @Valid CisBloodApplyNto cisBloodApplyNto);

    @Operation(summary = "P0根据唯一标识修改备血申请单。")
    @PutMapping("/cisPreparationBloodApplies/{id:.+}")
    void updateCisPreparationBloodApply(@PathVariable("id") String id, @RequestBody @Valid CisBloodApplyEto cisBloodApplyEto);

    @Operation(summary = "P0根据唯一标识删除备血申请单。")
    @DeleteMapping("/cisPreparationBloodApplies/{id:.+}")
    void deleteCisPreparationBloodApply(@PathVariable("id") String id);

}