package com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "患者病史")
public class CisMedicalHistoryEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -8537214365061412976L;

    @Schema(description = "主诉")
    private String chiefComplaint;
    @Schema(description = "现病史")
    private String presentIllnessHistory;
    @Schema(description = "既往史")
    private String pastMedicalHistory;
    @Schema(description = "过敏史")
    private String allergicHistory;
    @Schema(description = "个人史")
    private String socialHistory;
    @Schema(description = "家族史")
    private String familyHistory;
    @Schema(description = "治疗方案")
    private String treatmentPlan;

    public String getChiefComplaint() {
        return chiefComplaint;
    }

    public void setChiefComplaint(String chiefComplaint) {
        this.chiefComplaint = StringUtils.trimToNull(chiefComplaint);
    }

    public String getPresentIllnessHistory() {
        return presentIllnessHistory;
    }

    public void setPresentIllnessHistory(String presentIllnessHistory) {
        this.presentIllnessHistory = StringUtils.trimToNull(presentIllnessHistory);
    }

    public String getPastMedicalHistory() {
        return pastMedicalHistory;
    }

    public void setPastMedicalHistory(String pastMedicalHistory) {
        this.pastMedicalHistory = StringUtils.trimToNull(pastMedicalHistory);
    }

    public String getAllergicHistory() {
        return allergicHistory;
    }

    public void setAllergicHistory(String allergicHistory) {
        this.allergicHistory = StringUtils.trimToNull(allergicHistory);
    }

    public String getSocialHistory() {
        return socialHistory;
    }

    public void setSocialHistory(String socialHistory) {
        this.socialHistory = StringUtils.trimToNull(socialHistory);
    }

    public String getFamilyHistory() {
        return familyHistory;
    }

    public void setFamilyHistory(String familyHistory) {
        this.familyHistory = StringUtils.trimToNull(familyHistory);
    }

    public String getTreatmentPlan() {
        return treatmentPlan;
    }

    public void setTreatmentPlan(String treatmentPlan) {
        this.treatmentPlan = StringUtils.trimToNull(treatmentPlan);
    }
}