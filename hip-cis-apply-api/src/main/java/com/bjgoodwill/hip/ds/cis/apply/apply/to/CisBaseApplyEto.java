package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeEto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(description = "抽象父类")
public abstract class CisBaseApplyEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -8183651125993415657L;
    @Schema(description = "申请单诊断")
    protected List<ApplyDiagnosisNto> applyDiagnosisNtos;
    @Schema(description = "医嘱编码")
    private String serviceItemCode;
    @Schema(description = "医嘱名称")
    private String serviceItemName;
    @Schema(description = "是否允许加急标识,1允许0不允许")
    private String isCanPriorityFlag;
    @Schema(description = "状态")
    private CisStatusEnum statusCode;
    @Schema(description = "处方号")
    private String prescriptionID;
    @Schema(description = "备注")
    private String reMark;
    @Schema(description = "重症患者执行时间")
    private LocalDateTime icuExecuteDate;
    @Schema(description = "是否跨院申请项目")
    private Boolean isChargeManager;
    @Schema(description = "版本")
    private Integer version;
    private String frequency;
    private String frequencyName;
    private Double num;
    @Schema(description = "是否隔离")
    private Boolean isOlation;
    @Schema(description = "是否申请")
    private Boolean isApply;
    @Schema(description = "申请单费用List")
    private List<CisApplyChargeEto> cisApplyChargeEtos;
    private List<CisApplyChargeNto> cisApplyChargeNtos;
    private List<String> deleteCisApplyCharge;

    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = StringUtils.trimToNull(serviceItemCode);
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = StringUtils.trimToNull(serviceItemName);
    }

    public String getIsCanPriorityFlag() {
        return isCanPriorityFlag;
    }

    public void setIsCanPriorityFlag(String isCanPriorityFlag) {
        this.isCanPriorityFlag = StringUtils.trimToNull(isCanPriorityFlag);
    }

    @NotNull(message = "状态不能为空！")
    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public String getPrescriptionID() {
        return prescriptionID;
    }

    public void setPrescriptionID(String prescriptionID) {
        this.prescriptionID = StringUtils.trimToNull(prescriptionID);
    }

    public String getReMark() {
        return reMark;
    }

    public void setReMark(String reMark) {
        this.reMark = StringUtils.trimToNull(reMark);
    }

    public LocalDateTime getIcuExecuteDate() {
        return icuExecuteDate;
    }

    public void setIcuExecuteDate(LocalDateTime icuExecuteDate) {
        this.icuExecuteDate = icuExecuteDate;
    }

    public Boolean getIsChargeManager() {
        return isChargeManager;
    }

    public void setIsChargeManager(Boolean isChargeManager) {
        this.isChargeManager = isChargeManager;
    }

    @NotNull(message = "版本不能为空！")
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public List<CisApplyChargeEto> getCisApplyChargeEtos() {
        return cisApplyChargeEtos;
    }

    public void setCisApplyChargeEtos(List<CisApplyChargeEto> cisApplyChargeEtos) {
        this.cisApplyChargeEtos = cisApplyChargeEtos;
    }

    public List<CisApplyChargeNto> getCisApplyChargeNtos() {
        return cisApplyChargeNtos;
    }

    public void setCisApplyChargeNtos(List<CisApplyChargeNto> cisApplyChargeNtos) {
        this.cisApplyChargeNtos = cisApplyChargeNtos;
    }

    public List<String> getDeleteCisApplyCharge() {
        return deleteCisApplyCharge;
    }

    public void setDeleteCisApplyCharge(List<String> deleteCisApplyCharge) {
        this.deleteCisApplyCharge = deleteCisApplyCharge;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public Boolean getIsOlation() {
        return isOlation;
    }

    public void setIsOlation(Boolean isOlation) {
        this.isOlation = isOlation;
    }

    public Boolean getIsApply() {
        return isApply;
    }

    public void setIsApply(Boolean isApply) {
        this.isApply = isApply;
    }

    public List<ApplyDiagnosisNto> getApplyDiagnosisNtos() {
        return applyDiagnosisNtos;
    }

    public void setApplyDiagnosisNtos(List<ApplyDiagnosisNto> applyDiagnosisNtos) {
        this.applyDiagnosisNtos = applyDiagnosisNtos;
    }

    @JsonProperty("@class")
    public String getClassName() {
        return getClass().getName();
    }
}