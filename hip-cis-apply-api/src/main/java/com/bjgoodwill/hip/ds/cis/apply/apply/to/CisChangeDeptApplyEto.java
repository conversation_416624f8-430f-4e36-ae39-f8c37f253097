package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "转科申请单")
public class CisChangeDeptApplyEto extends CisBaseApplyEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -895062026765497668L;

    @Schema(description = "转出科室")
    private String outOrgCode;
    @Schema(description = "转出科室名称")
    private String outOrgName;
    @Schema(description = "转出护理组")
    private String outDeptNurseCode;
    @Schema(description = "转出护理组名称")
    private String outDeptNurseName;
    @Schema(description = "转出医院编码")
    private String outHospitalCode;
    @Schema(description = "转出医院名称")
    private String outHospitalName;
    @Schema(description = "转入科室")
    private String inOrgCode;
    @Schema(description = "转入科室名称")
    private String inOrgName;
    @Schema(description = "转入护理组")
    private String inDeptNurseCode;
    @Schema(description = "转入护理组名称")
    private String inDeptNurseName;
    @Schema(description = "转入医院编码")
    private String inHospitalCode;
    @Schema(description = "转入医院名称")
    private String inHospitalName;

    @NotBlank(message = "转出科室不能为空！")
    public String getOutOrgCode() {
        return outOrgCode;
    }

    public void setOutOrgCode(String outOrgCode) {
        this.outOrgCode = StringUtils.trimToNull(outOrgCode);
    }

    @NotBlank(message = "转出科室名称不能为空！")
    public String getOutOrgName() {
        return outOrgName;
    }

    public void setOutOrgName(String outOrgName) {
        this.outOrgName = outOrgName;
    }

    @NotBlank(message = "转出护理组不能为空！")
    public String getOutDeptNurseCode() {
        return outDeptNurseCode;
    }

    public void setOutDeptNurseCode(String outDeptNurseCode) {
        this.outDeptNurseCode = StringUtils.trimToNull(outDeptNurseCode);
    }

    @NotBlank(message = "转出护理组名称不能为空！")
    public String getOutDeptNurseName() {
        return outDeptNurseName;
    }

    public void setOutDeptNurseName(String outDeptNurseName) {
        this.outDeptNurseName = StringUtils.trimToNull(outDeptNurseName);
    }

    @NotBlank(message = "转出医院编码不能为空！")
    public String getOutHospitalCode() {
        return outHospitalCode;
    }

    public void setOutHospitalCode(String outHospitalCode) {
        this.outHospitalCode = StringUtils.trimToNull(outHospitalCode);
    }

    @NotBlank(message = "转出医院名称不能为空！")
    public String getOutHospitalName() {
        return outHospitalName;
    }

    public void setOutHospitalName(String outHospitalName) {
        this.outHospitalName = StringUtils.trimToNull(outHospitalName);
    }

    @NotBlank(message = "转入科室不能为空！")
    public String getInOrgCode() {
        return inOrgCode;
    }

    public void setInOrgCode(String inOrgCode) {
        this.inOrgCode = StringUtils.trimToNull(inOrgCode);
    }

    @NotBlank(message = "转入科室名称不能为空！")
    public String getInOrgName() {
        return inOrgName;
    }

    public void setInOrgName(String inOrgName) {
        this.inOrgName = StringUtils.trimToNull(inOrgName);
    }

    @NotBlank(message = "转入护理组不能为空！")
    public String getInDeptNurseCode() {
        return inDeptNurseCode;
    }

    public void setInDeptNurseCode(String inDeptNurseCode) {
        this.inDeptNurseCode = StringUtils.trimToNull(inDeptNurseCode);
    }

    @NotBlank(message = "转入护理组名称不能为空！")
    public String getInDeptNurseName() {
        return inDeptNurseName;
    }

    public void setInDeptNurseName(String inDeptNurseName) {
        this.inDeptNurseName = StringUtils.trimToNull(inDeptNurseName);
    }

    @NotBlank(message = "转入医院编码不能为空！")
    public String getInHospitalCode() {
        return inHospitalCode;
    }

    public void setInHospitalCode(String inHospitalCode) {
        this.inHospitalCode = StringUtils.trimToNull(inHospitalCode);
    }

    @NotBlank(message = "转入医院名称不能为空！")
    public String getInHospitalName() {
        return inHospitalName;
    }

    public void setInHospitalName(String inHospitalName) {
        this.inHospitalName = StringUtils.trimToNull(inHospitalName);
    }
}