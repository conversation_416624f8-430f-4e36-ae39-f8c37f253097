package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeTo;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisDrugApplyDetailTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public class CisApplyVerifyTo {

    @Schema(description = "申请单费用明细")
    private List<CisApplyChargeTo> cisApplyChargeToList;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "医嘱编码")
    private String serviceItemCode;
    @Schema(description = "医嘱名称")
    private String serviceItemName;
    @Schema(description = "是否允许加急标识,1允许0不允许")
    private String isCanPriorityFlag;
    @Schema(description = "状态")
    private CisStatusEnum statusCode;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "执行人")
    private String executorStaff;
    @Schema(description = "执行时间")
    private LocalDateTime executorDate;
    @Schema(description = "执行医院编码")
    private String executorHosptialCode;
    @Schema(description = "执行科室编码")
    private String executorOrgCode;
    @Schema(description = "病历及查体摘要标识")
    private String medrecordExamabstractId;
    @Schema(description = "IPD住院，OPD门诊")
    private VisitTypeEnum visitType;
    @Schema(description = "护理组编码")
    private String deptNurseCode;
    @Schema(description = "护理组名称")
    private String deptNurseName;
    @Schema(description = "医嘱ID")
    private String orderID;
    @Schema(description = "申请医院编码")
    private String hospitalCode;
    @Schema(description = "处方号")
    private String prescriptionID;
    @Schema(description = "打印标识：1己打印，0未打印；默认0")
    private Boolean isPrint;
    @Schema(description = "打印人")
    private String printStaff;
    @Schema(description = "打印时间")
    private LocalDate printDate;
    @Schema(description = "备注")
    private String reMark;
    @Schema(description = "重症患者执行时间")
    private LocalDateTime icuExecuteDate;
    @Schema(description = "是否跨院申请项目")
    private Boolean isChargeManager;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "开方人所在科室")
    private String createOrgCode;
    @Schema(description = "排序序号")
    private Double sortNo;
    @Schema(description = "isBaby")
    private Boolean isBaby;
    @Schema(description = "开立科室")
    private String visitOrgCode;
//    // 转科
//    @Schema(description = "转出科室")
//    private String outOrgCode;
//    @Schema(description = "转出护理组")
//    private String outDeptNurseCode;
//    @Schema(description = "转出医院编码")
//    private String outHospitalCode;
//    @Schema(description = "转入科室")
//    private String inOrgCode;
//    @Schema(description = "转入护理组")
//    private String inDeptNurseCode;
//    @Schema(description = "转入医院编码")
//    private String inHospitalCode;
//    //Common
//    @Schema(description = "频次")
//    private String frequency;
//    @Schema(description = "每次剂量")
//    private Double dosage;
//    @Schema(description = "剂量单位")
//    private String dosageUnit;
//    @Schema(description = "包装总量")
//    private Double packageNum;
//    @Schema(description = "包装单位 MinUnit/PackageUnit")
//    private String packageUnit;
//    @Schema(description = "每次持续时间")
//    private Double continueTime;
//    @Schema(description = "每次持续单位 字典TimeUnit")
//    private String continueUnit;
//    @Schema(description = "医嘱类型")
//    private String systemType;
//    // 会诊
//    @Schema(description = "会诊科室")
//    private String cnsltOrg;
//    @Schema(description = "诊断编码")
//    private String diagnosisCode;
//    @Schema(description = "会诊医院编码")
//    private String cnsltHospitalCode;
//    @Schema(description = "患者病情摘要")
//    private String patRemark;
//    @Schema(description = "会诊理由及目的")
//    private String reason;
//    @Schema(description = "会诊意见 ")
//    private String cnsltOpinion;
//    @Schema(description = "会诊医生")
//    private String cnsltDoctor;
//    @Schema(description = "会诊时间")
//    private LocalDateTime cnsltDate;
//    @Schema(description = "会诊确认时间")
//    private LocalDateTime cnsltConfirmDate;
//    @Schema(description = "会诊完成时间")
//    private LocalDateTime cnsltCompletionTime;
//    @Schema(description = "申请医生电话")
//    private String createStaffTel;
//    @Schema(description = "执行医院编码")
//    private String execHospitalCode;
//    // 手术
//    @Schema(description = "RH血型")
//    private String bloodTypeRh;
//    @Schema(description = "手术分类")
//    private String operationType;
//    @Schema(description = "部位")
//    private String humanOrgans;
//    @Schema(description = "体位")
//    private String decubitus;
//    @Schema(description = "是否隔离")
//    private Boolean isOlation;
//    @Schema(description = "术前诊断")
//    private String preoperativeDiagnosis1;
//    @Schema(description = "preoperativeDiagnosis2")
//    private String preoperativeDiagnosis2;
//    @Schema(description = "preoperativeDiagnosis3")
//    private String preoperativeDiagnosis3;
//    @Schema(description = "拟手术名称1")
//    private String operationName1;
//    @Schema(description = "operationName2")
//    private String operationName2;
//    @Schema(description = "operationName3")
//    private String operationName3;
//    @Schema(description = "麻醉类型")
//    private String anaesthesiaType;
//    @Schema(description = "麻醉方式")
//    private String anaesthesiaMode;
//    @Schema(description = " 特殊手术属性")
//    private String operationSpecialAttr;
//    @Schema(description = "拟手术日期")
//    private LocalDateTime operationDate;
//    @Schema(description = "拟预计用时")
//    private Double operationTime;
//    @Schema(description = "手术级别")
//    private String operationLevel;
//    @Schema(description = "切口类型")
//    private String incisionType;
//    @Schema(description = "切口等级 字典WoundGrade")
//    private String incisionLevel;
//    @Schema(description = "合并手术")
//    private Boolean mergeFlag;
//    @Schema(description = "手术审批类型：1常规手术，2急诊手术；默认1")
//    private String apprOperTypc;
//    @Schema(description = "手术医生")
//    private String operationDoctor;
//    @Schema(description = "手术医生科室")
//    private String operationDoctorOrg;
//    @Schema(description = "基本操作")
//    private String operation;
//    @Schema(description = "入路")
//    private String approach;
//    @Schema(description = "辅助器械")
//    private String instrument;
//    // 出院
//    @Schema(description = "出院时间")
//    private LocalDateTime outDate;
//    @Schema(description = "出院方式 字典DischargeWay")
//    private String dischargeDisposition;
//    // 备血
//    @Schema(description = "预定输注时间")
//    private LocalDateTime preInfusionDate;
//    @Schema(description = "临床诊断")
//    private String clinicalDiagnosis;
//    @Schema(description = "输血指征")
//    private String transfusionTrigger;
//    @Schema(description = "输血需求 1-正常，2-紧急，3-大量，4-特殊(TransfusionDemand)")
//    private String transfusionDemand;
//    @Schema(description = "输血目的")
//    private String transfusionPurpose;
//    @Schema(description = "输血方式 1-异体，2-自体，")
//    private String transfusionWay;
//    @Schema(description = "输血检测项目 0-未送检，1-已送检")
//    private String transfusionDetection;
//    @Schema(description = "采血人")
//    private String drawBloodUser;
//    @Schema(description = "血型")
//    private String bloodType;
//    @Schema(description = "0-阴性，1-阳性")
//    private Integer rh_d;
//    @Schema(description = "红细胞RBC")
//    private Double erythrocyte;
//    @Schema(description = "白细胞 WBC 单位x10^9/L")
//    private String leukocyte;
//    @Schema(description = "血红蛋白 HB")
//    private Integer hemoglobin;
//    @Schema(description = "血小板 PL")
//    private Double thrombocyte;
//    @Schema(description = "红细胞压积HCT")
//    private Double hematokrit;
//    @Schema(description = "谷丙转氨酶ALT")
//    private Double glutamic_Pyruvic;
//    @Schema(description = "APTT单位秒 参考值 28.0~43.5")
//    private Double aptt;
//    @Schema(description = "FIBD单位g/L 参考值 2.00~4.00")
//    private Double fibd;
//    @Schema(description = "PT单位秒 参考值11.0~15.0")
//    private Double pt;
//    @Schema(description = "HbsAg乙肝表面抗原 0-阴性，1-阳性")
//    private Boolean hbsAg;
//    @Schema(description = "HbsAb乙型肝炎表面抗体 0-阴性，1-阳性")
//    private Boolean hbsAb;
//    @Schema(description = "HbeAg乙型肝炎E抗原 0-阴性，1-阳性")
//    private Boolean hbeAg;
//    @Schema(description = "HbeAb乙型肝炎E抗体 0-阴性，1-阳性")
//    private Boolean hbeAb;
//    @Schema(description = "HbcAb乙肝核心抗体 0-阴性，1-阳性")
//    private Boolean hbcAb;
//    @Schema(description = "HCVAb丙肝病毒抗体 0-阴性，1-阳性")
//    private Boolean hcvAb;
//    @Schema(description = "TP-Ab梅毒 0-阴性，1-阳性")
//    private Boolean tpAb;
//    @Schema(description = "HIV(1+2)Ab艾滋病")
//    private Boolean hivAb;
//    @Schema(description = "注明1 ")
//    private Boolean indicate1;
//    @Schema(description = "注明2")
//    private Boolean indicate2;
//    @Schema(description = "标本留取时间")
//    private LocalDateTime specimenRetentionDate;
//    @Schema(description = "申请医生签字")
//    private String applyUser;
//    @Schema(description = "申请时间")
//    private LocalDateTime applyDate;
//    @Schema(description = "申请确认标识：0申请，1通过")
//    private Boolean appleType;
//    @Schema(description = "此患者仅备血未输血 0-否，1-是")
//    private Boolean preBlood;

    @Schema(description = "用法")
    private String usage;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "频次")
    private String frequency;
    @Schema(description = "频次名称")
    private String frequencyName;
    @Schema(description = "疗程")
    private String treatmentCourse;
    @Schema(description = "疗程单位")
    private String treatmentCourseUnit;
    @Schema(description = "协定处方")
    private Boolean prescriptionFlag;
    @Schema(description = "领药科室")
    private String receiveOrg;
    @Schema(description = "药品申请单明细")
    private List<CisDrugApplyDetailTo> cisDrugApplyDetails;
    @Schema(description = "滴速")
    private String dripSpeed;
    @Schema(description = "滴速单位")
    private String dripSpeedUnit;
    @Schema(description = "是否皮试")
    private Boolean isSkin;
    @Schema(description = "皮试结果")
    private String skinResult;
    @Schema(description = "抗菌药使用说明:0-预防，1-治疗")
    private Integer antimicrobialsPurpose;
    @Schema(description = "付数")
    private String doseNum;
    @Schema(description = "草药煎法;0自煎,1代煎")
    private Integer decoction;
    @Schema(description = "草药一付包数")
    private Integer cdrugPackNum;
    @Schema(description = "草药每包毫升数")
    private Integer cdrugPackMl;
    @Schema(description = "带药方式")
    private SbadmWayEnum sbadmWay;

    public List<CisApplyChargeTo> getCisApplyChargeToList() {
        return cisApplyChargeToList;
    }

    public void setCisApplyChargeToList(List<CisApplyChargeTo> cisApplyChargeToList) {
        this.cisApplyChargeToList = cisApplyChargeToList;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getIsCanPriorityFlag() {
        return isCanPriorityFlag;
    }

    public void setIsCanPriorityFlag(String isCanPriorityFlag) {
        this.isCanPriorityFlag = isCanPriorityFlag;
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getExecutorStaff() {
        return executorStaff;
    }

    public void setExecutorStaff(String executorStaff) {
        this.executorStaff = executorStaff;
    }

    public LocalDateTime getExecutorDate() {
        return executorDate;
    }

    public void setExecutorDate(LocalDateTime executorDate) {
        this.executorDate = executorDate;
    }

    public String getExecutorHosptialCode() {
        return executorHosptialCode;
    }

    public void setExecutorHosptialCode(String executorHosptialCode) {
        this.executorHosptialCode = executorHosptialCode;
    }

    public String getExecutorOrgCode() {
        return executorOrgCode;
    }

    public void setExecutorOrgCode(String executorOrgCode) {
        this.executorOrgCode = executorOrgCode;
    }

    public String getMedrecordExamabstractId() {
        return medrecordExamabstractId;
    }

    public void setMedrecordExamabstractId(String medrecordExamabstractId) {
        this.medrecordExamabstractId = medrecordExamabstractId;
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    public String getDeptNurseName() {
        return deptNurseName;
    }

    public void setDeptNurseName(String deptNurseName) {
        this.deptNurseName = deptNurseName;
    }

    public String getOrderID() {
        return orderID;
    }

    public void setOrderID(String orderID) {
        this.orderID = orderID;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPrescriptionID() {
        return prescriptionID;
    }

    public void setPrescriptionID(String prescriptionID) {
        this.prescriptionID = prescriptionID;
    }

    public Boolean getPrint() {
        return isPrint;
    }

    public void setPrint(Boolean print) {
        isPrint = print;
    }

    public String getPrintStaff() {
        return printStaff;
    }

    public void setPrintStaff(String printStaff) {
        this.printStaff = printStaff;
    }

    public LocalDate getPrintDate() {
        return printDate;
    }

    public void setPrintDate(LocalDate printDate) {
        this.printDate = printDate;
    }

    public String getReMark() {
        return reMark;
    }

    public void setReMark(String reMark) {
        this.reMark = reMark;
    }

    public LocalDateTime getIcuExecuteDate() {
        return icuExecuteDate;
    }

    public void setIcuExecuteDate(LocalDateTime icuExecuteDate) {
        this.icuExecuteDate = icuExecuteDate;
    }

    public Boolean getChargeManager() {
        return isChargeManager;
    }

    public void setChargeManager(Boolean chargeManager) {
        isChargeManager = chargeManager;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    public Boolean getBaby() {
        return isBaby;
    }

    public void setBaby(Boolean baby) {
        isBaby = baby;
    }

    public String getVisitOrgCode() {
        return visitOrgCode;
    }

    public void setVisitOrgCode(String visitOrgCode) {
        this.visitOrgCode = visitOrgCode;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public String getTreatmentCourse() {
        return treatmentCourse;
    }

    public void setTreatmentCourse(String treatmentCourse) {
        this.treatmentCourse = treatmentCourse;
    }

    public String getTreatmentCourseUnit() {
        return treatmentCourseUnit;
    }

    public void setTreatmentCourseUnit(String treatmentCourseUnit) {
        this.treatmentCourseUnit = treatmentCourseUnit;
    }

    public Boolean getPrescriptionFlag() {
        return prescriptionFlag;
    }

    public void setPrescriptionFlag(Boolean prescriptionFlag) {
        this.prescriptionFlag = prescriptionFlag;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    public List<CisDrugApplyDetailTo> getCisDrugApplyDetails() {
        return cisDrugApplyDetails;
    }

    public void setCisDrugApplyDetails(List<CisDrugApplyDetailTo> cisDrugApplyDetails) {
        this.cisDrugApplyDetails = cisDrugApplyDetails;
    }

    public String getDripSpeed() {
        return dripSpeed;
    }

    public void setDripSpeed(String dripSpeed) {
        this.dripSpeed = dripSpeed;
    }

    public String getDripSpeedUnit() {
        return dripSpeedUnit;
    }

    public void setDripSpeedUnit(String dripSpeedUnit) {
        this.dripSpeedUnit = dripSpeedUnit;
    }

    public Boolean getSkin() {
        return isSkin;
    }

    public void setSkin(Boolean skin) {
        isSkin = skin;
    }

    public String getSkinResult() {
        return skinResult;
    }

    public void setSkinResult(String skinResult) {
        this.skinResult = skinResult;
    }

    public Integer getAntimicrobialsPurpose() {
        return antimicrobialsPurpose;
    }

    public void setAntimicrobialsPurpose(Integer antimicrobialsPurpose) {
        this.antimicrobialsPurpose = antimicrobialsPurpose;
    }

    public String getDoseNum() {
        return doseNum;
    }

    public void setDoseNum(String doseNum) {
        this.doseNum = doseNum;
    }

    public Integer getDecoction() {
        return decoction;
    }

    public void setDecoction(Integer decoction) {
        this.decoction = decoction;
    }

    public Integer getCdrugPackNum() {
        return cdrugPackNum;
    }

    public void setCdrugPackNum(Integer cdrugPackNum) {
        this.cdrugPackNum = cdrugPackNum;
    }

    public Integer getCdrugPackMl() {
        return cdrugPackMl;
    }

    public void setCdrugPackMl(Integer cdrugPackMl) {
        this.cdrugPackMl = cdrugPackMl;
    }

    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }
}
