package com.bjgoodwill.hip.ds.cis.apply.spcobs.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetailEto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "检验类申请单")
public class CisSpcobsApplyEto extends ApplyWithDetailEto<CisSpcobsApplyDetailEto, CisSpcobsApplyDetailNto> implements Serializable {

    @Serial
    private static final long serialVersionUID = -8256881196391501433L;

    @Schema(description = "检验注意事项")
    private String remark;
    @Schema(description = "检验分类")
    private String spcobsClass;
    @Schema(description = "检验分类名称")
    private String spcobsClassName;
    @Schema(description = "标本 字典Speciman")
    private String speciman;
    @Schema(description = "标本名称 字典Speciman")
    private String specimanName;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSpcobsClass() {
        return spcobsClass;
    }

    public void setSpcobsClass(String spcobsClass) {
        this.spcobsClass = spcobsClass;
    }

    public String getSpeciman() {
        return speciman;
    }

    public void setSpeciman(String speciman) {
        this.speciman = speciman;
    }

    public String getSpcobsClassName() {
        return spcobsClassName;
    }

    public void setSpcobsClassName(String spcobsClassName) {
        this.spcobsClassName = spcobsClassName;
    }

    public String getSpecimanName() {
        return specimanName;
    }

    public void setSpecimanName(String specimanName) {
        this.specimanName = specimanName;
    }
}