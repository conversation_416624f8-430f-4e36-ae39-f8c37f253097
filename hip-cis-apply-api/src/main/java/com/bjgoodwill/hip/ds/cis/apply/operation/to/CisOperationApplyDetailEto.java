package com.bjgoodwill.hip.ds.cis.apply.operation.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-09-29 15:44
 * @className: CisOperationApplyDetailEto
 * @description:
 **/
@Schema(description = "手术术式")
public class CisOperationApplyDetailEto extends DetailEto implements Serializable {
    @Schema(description = "id")
    private String id;
    @Schema(description = "手术术式编码,ICD9-CM3编码")
    private String operationCode;
    @Schema(description = "手术术式编码,ICD9-CM3名称")
    private String operationName;
    @Schema(description = "序号")
    private Double sortNo;
    @Schema(description = "手术级别")
    private String operationLevel;
    @Schema(description = "部位")
    private String humanOrgans;
    @Schema(description = "部位名称")
    private String humanOrgansName;
    @Schema(description = "体位")
    private String decubitus;
    @Schema(description = "体位名称")
    private String decubitusName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @NotEmpty(message = "手术编码不能为空！")
    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    public String getOperationLevel() {
        return operationLevel;
    }

    public void setOperationLevel(String operationLevel) {
        this.operationLevel = StringUtils.trimToNull(operationLevel);
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    public String getHumanOrgansName() {
        return humanOrgansName;
    }

    public void setHumanOrgansName(String humanOrgansName) {
        this.humanOrgansName = humanOrgansName;
    }

    public String getDecubitus() {
        return decubitus;
    }

    public void setDecubitus(String decubitus) {
        this.decubitus = decubitus;
    }

    public String getDecubitusName() {
        return decubitusName;
    }

    public void setDecubitusName(String decubitusName) {
        this.decubitusName = decubitusName;
    }
}