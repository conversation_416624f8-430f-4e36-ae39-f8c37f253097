package com.bjgoodwill.hip.ds.cis.apply.nursing.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "护理")
public class CisNursingApplyNto extends CisBaseApplyNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -9134626814871948440L;

    @Schema(description = "自动计费")
    private String autoFlag;
    @Schema(description = "互斥")
    private String mutuallyExclusiveFlag;

    public String getAutoFlag() {
        return autoFlag;
    }

    public void setAutoFlag(String autoFlag) {
        this.autoFlag = StringUtils.trimToNull(autoFlag);
    }

    public String getMutuallyExclusiveFlag() {
        return mutuallyExclusiveFlag;
    }

    public void setMutuallyExclusiveFlag(String mutuallyExclusiveFlag) {
        this.mutuallyExclusiveFlag = StringUtils.trimToNull(mutuallyExclusiveFlag);
    }
}