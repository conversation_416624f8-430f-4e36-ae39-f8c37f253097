package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisCommonEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisCommonNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisCommonTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@Tag(name = "通用申请单领域服务", description = "通用申请单领域服务")
public interface CisCommonService extends CisBaseApplyService {

    @Operation(summary = "P0根据唯一标识返回通用申请单。")
    @GetMapping("/cisCommons/{id:.+}")
    CisCommonTo getCisCommonById(@PathVariable("id") String id);

    @Operation(summary = "P0创建通用申请单。")
    @PostMapping("/cisCommons")
    CisCommonTo createCisCommon(@RequestBody @Valid CisCommonNto cisCommonNto);

    @Operation(summary = "P0根据唯一标识修改通用申请单。")
    @PutMapping("/cisCommons/{id:.+}")
    void updateCisCommon(@PathVariable("id") String id, @RequestBody @Valid CisCommonEto cisCommonEto);

    @Operation(summary = "P0根据唯一标识删除通用申请单。")
    @DeleteMapping("/cisCommons/{id:.+}")
    void deleteCisCommon(@PathVariable("id") String id);

}