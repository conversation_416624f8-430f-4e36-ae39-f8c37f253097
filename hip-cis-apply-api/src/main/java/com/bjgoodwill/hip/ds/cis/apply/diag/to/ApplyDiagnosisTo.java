package com.bjgoodwill.hip.ds.cis.apply.diag.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "申请单诊断")
public class ApplyDiagnosisTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1273298154551420673L;

    @Schema(description = "抽象父类标识")
    private String cisBaseApplyId;
    @Schema(description = "标识")
    private String id;
    @Schema(description = "diagCode")
    private String diagCode;
    @Schema(description = "诊断名称")
    private String diagName;
    @Schema(description = "逻辑删除标记")
    private boolean deleted;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;

    public String getCisBaseApplyId() {
        return cisBaseApplyId;
    }

    public void setCisBaseApplyId(String cisBaseApplyId) {
        this.cisBaseApplyId = cisBaseApplyId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDiagCode() {
        return diagCode;
    }

    public void setDiagCode(String diagCode) {
        this.diagCode = diagCode;
    }

    public String getDiagName() {
        return diagName;
    }

    public void setDiagName(String diagName) {
        this.diagName = diagName;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ApplyDiagnosisTo other = (ApplyDiagnosisTo) obj;
        return Objects.equals(id, other.id);
    }
}