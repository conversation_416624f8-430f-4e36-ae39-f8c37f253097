package com.bjgoodwill.hip.ds.cis.apply.palg.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "病理明细")
public class CisPalgApplyDetailEto extends DetailEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -8910641351701222531L;

    @Schema(description = "ID")
    private String id;
    @Schema(description = "序号")
    private Double sortNo;
    @Schema(description = "部位")
    private String humanOrgans;
    @Schema(description = "标本")
    private String speciman;
    @Schema(description = "数量")
    private Integer num;
    @Schema(description = "离体时间")
    private LocalDateTime outVivoDate;

    private String serviceItemCode;

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    @NotBlank(message = "部位不能为空！")
    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = StringUtils.trimToNull(humanOrgans);
    }

    @NotBlank(message = "标本不能为空！")
    public String getSpeciman() {
        return speciman;
    }

    public void setSpeciman(String speciman) {
        this.speciman = StringUtils.trimToNull(speciman);
    }

    @NotNull(message = "数量不能为空！")
    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public LocalDateTime getOutVivoDate() {
        return outVivoDate;
    }

    public void setOutVivoDate(LocalDateTime outVivoDate) {
        this.outVivoDate = outVivoDate;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }
}