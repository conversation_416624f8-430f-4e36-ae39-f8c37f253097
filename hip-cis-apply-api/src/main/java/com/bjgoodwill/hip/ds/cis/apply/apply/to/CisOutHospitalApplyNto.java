package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "出院申请单")
public class CisOutHospitalApplyNto extends CisBaseApplyNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6915013986406361225L;

    @Schema(description = "出院时间")
    private LocalDateTime outDate;
    @Schema(description = "出院方式 字典DischargeWay")
    private String dischargeDisposition;

    public LocalDateTime getOutDate() {
        return outDate;
    }

    public void setOutDate(LocalDateTime outDate) {
        this.outDate = outDate;
    }

    @NotBlank(message = "出院方式 字典DischargeWay不能为空！")
    public String getDischargeDisposition() {
        return dischargeDisposition;
    }

    public void setDischargeDisposition(String dischargeDisposition) {
        this.dischargeDisposition = StringUtils.trimToNull(dischargeDisposition);
    }
}