package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetailEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.io.Serializable;
import java.util.Map;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-01-17 09:45
 * @className: CisBaseApplyMergeGroupEto
 * @description:
 **/
@Schema(description = "申请单并组")
public class CisBaseApplyMergeGroupEto extends BaseEto implements Serializable {

    @Schema(description = "源申请单ID")
    private String sourceApplyId;

    @Schema(description = "合并的申请单")
    private Map<String, ApplyWithDetailEto> detailEtos;

    @NotBlank(message = "源申请单ID不能为空！")
    public String getSourceApplyId() {
        return sourceApplyId;
    }

    public void setSourceApplyId(String sourceApplyId) {
        this.sourceApplyId = sourceApplyId;
    }

    @NotEmpty(message = "合并的申请单不能为空！")
    public Map<String, ApplyWithDetailEto> getDetailEtos() {
        return detailEtos;
    }

    public void setDetailEtos(Map<String, ApplyWithDetailEto> detailEtos) {
        this.detailEtos = detailEtos;
    }
}