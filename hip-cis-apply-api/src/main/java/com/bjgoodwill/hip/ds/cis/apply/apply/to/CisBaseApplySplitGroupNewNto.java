package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.io.Serializable;

/**
 * @program: cis-base
 * @author: xdguo
 * @create: 2025-05-19 15:07
 * @className: CisBaseApplySplitGroupNewNto
 * @description:
 **/
@Schema(description = "申请单拆组新,全部拆成单支，其中一个医嘱id是原orderId")
public class CisBaseApplySplitGroupNewNto extends BaseNto implements Serializable {

    private String orderId;

    private String detailId;

    private String orderContent;

    private String toxiproperty;

    @NotBlank(message = "医嘱ID不能为空！")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @NotBlank(message = "明细ID不能为空！")
    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    @NotBlank(message = "医嘱内容不能为空！")
    public String getOrderContent() {
        return orderContent;
    }

    public void setOrderContent(String orderContent) {
        this.orderContent = orderContent;
    }

    @NotBlank(message = "药理属性不能为空！")
    public String getToxiproperty() {
        return toxiproperty;
    }

    public void setToxiproperty(String toxiproperty) {
        this.toxiproperty = toxiproperty;
    }
}