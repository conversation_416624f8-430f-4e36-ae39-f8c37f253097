package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-09-19 10:16
 * @className: CisOrderExecPlanChangeChargeEto
 * @description:
 **/
@Schema(description = "修改执行单计费状态")
public class CisOrderExecPlanChangeBackEto extends BaseEto implements Serializable {

    @Schema(description = "执行单ID")
    private String execPlanId;
    @Schema(description = "计费时间")
    private LocalDateTime chargeDate;
    @Schema(description = "计费人")
    private String chargeStaff;
    @Schema(description = "计费科室")
    private String chargeOrg;
    @Schema(description = "是否计费")
    private Boolean isCharge;
    @Schema(description = "发送标识")
    private Boolean isSend;
    @Schema(description = "医嘱ID")
    private String orderId;
    @Schema(description = "工作组编码")
    private String orgCode;
    @Schema(description = "工作组名称")
    private String orgName;

    @NotBlank(message = "执行单ID不能为空！")
    public String getExecPlanId() {
        return execPlanId;
    }

    public void setExecPlanId(String execPlanId) {
        this.execPlanId = execPlanId;
    }

    //    @NotNull(message = "计费时间不能为空！")
    public LocalDateTime getChargeDate() {
        return chargeDate;
    }

    public void setChargeDate(LocalDateTime chargeDate) {
        this.chargeDate = chargeDate;
    }

    //    @NotBlank(message = "计费人不能为空！")
    public String getChargeStaff() {
        return chargeStaff;
    }

    public void setChargeStaff(String chargeStaff) {
        this.chargeStaff = chargeStaff;
    }

    //    @NotNull(message = "计费科室不能为空！")
    public String getChargeOrg() {
        return chargeOrg;
    }

    public void setChargeOrg(String chargeOrg) {
        this.chargeOrg = chargeOrg;
    }

    public Boolean getIsCharge() {
        return isCharge;
    }

    public void setIsCharge(Boolean isCharge) {
        this.isCharge = isCharge;
    }

    //    @NotNull(message = "发送标识不能为空！")
    public Boolean getIsSend() {
        return isSend;
    }

    public void setIsSend(Boolean isSend) {
        this.isSend = isSend;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}