package com.bjgoodwill.hip.ds.cis.apply.overstep.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CheckTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisOverstepEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.business.util.common.to.BaseTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "越级审批")
public class CisOverstepApproalQto extends BaseTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -3950751990336749580L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "医嘱id")
    private String orderId;
    @Schema(description = "申请单ID")
    private String applyId;
    @Schema(description = "就诊类型")
    private VisitTypeEnum visitType;
    @Schema(description = "申请，通过，不通过；默认申请")
    private CheckTypeEnum checkType;
    @Schema(description = "审核类型， 抗菌药，手术")
    private CisOverstepEnum checkSystemType;
    @Schema(description = "主索引")
    private String patMiCode;
    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "审核人")
    private String reviewStaff;
    @Schema(description = "申请医生")
    private String applyDoc;
    @Schema(description = "申请科室")
    private String applyOrgCode;
    @Schema(description = "申请科室名称")
    private String applyOrgName;
    @Schema(description = "申请时间")
    private LocalDateTime applyDateTime;

    @Schema(description = "开方科室")
    private String visitOrgCode;
    @Schema(description = "医生所属科室")
    private String createOrgCode;
    @Schema(description = "护理组")
    private String deptNurseCode;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    public void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    public CheckTypeEnum getCheckType() {
        return checkType;
    }

    public void setCheckType(CheckTypeEnum checkType) {
        this.checkType = checkType;
    }

    public CisOverstepEnum getCheckSystemType() {
        return checkSystemType;
    }

    public void setCheckSystemType(CisOverstepEnum checkSystemType) {
        this.checkSystemType = checkSystemType;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getReviewStaff() {
        return reviewStaff;
    }

    public void setReviewStaff(String reviewStaff) {
        this.reviewStaff = reviewStaff;
    }

    public String getApplyDoc() {
        return applyDoc;
    }

    public void setApplyDoc(String applyDoc) {
        this.applyDoc = applyDoc;
    }

    public String getApplyOrgCode() {
        return applyOrgCode;
    }

    public void setApplyOrgCode(String applyOrgCode) {
        this.applyOrgCode = applyOrgCode;
    }

    public String getApplyOrgName() {
        return applyOrgName;
    }

    public void setApplyOrgName(String applyOrgName) {
        this.applyOrgName = applyOrgName;
    }

    public LocalDateTime getApplyDateTime() {
        return applyDateTime;
    }

    public void setApplyDateTime(LocalDateTime applyDateTime) {
        this.applyDateTime = applyDateTime;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getVisitOrgCode() {
        return visitOrgCode;
    }

    public void setVisitOrgCode(String visitOrgCode) {
        this.visitOrgCode = visitOrgCode;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }
}