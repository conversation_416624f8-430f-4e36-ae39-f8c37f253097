package com.bjgoodwill.hip.ds.cis.apply.apply.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "通用申请单")
public class CisCommonEto extends CisBaseApplyEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -3374247282657053597L;

    @Schema(description = "每次剂量")
    private Double dosage;
    @Schema(description = "每次持续时间")
    private Double continueTime;
    @Schema(description = "每次持续单位 字典TimeUnit")
    private String continueUnit;
    @Schema(description = "每次持续单位名称 字典TimeUnit")
    private String continueUnitName;
    @Schema(description = "医嘱类型")
    private SystemTypeEnum systemType;

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public Double getContinueTime() {
        return continueTime;
    }

    public void setContinueTime(Double continueTime) {
        this.continueTime = continueTime;
    }

    public String getContinueUnit() {
        return continueUnit;
    }

    public void setContinueUnit(String continueUnit) {
        this.continueUnit = StringUtils.trimToNull(continueUnit);
    }

    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }

    public String getContinueUnitName() {
        return continueUnitName;
    }

    public void setContinueUnitName(String continueUnitName) {
        this.continueUnitName = continueUnitName;
    }
}