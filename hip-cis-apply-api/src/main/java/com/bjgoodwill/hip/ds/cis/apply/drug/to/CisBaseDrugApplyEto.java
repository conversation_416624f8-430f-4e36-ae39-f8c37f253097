package com.bjgoodwill.hip.ds.cis.apply.drug.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetailEto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.util.List;

@Schema(description = "药品申请单")
public abstract class CisBaseDrugApplyEto extends ApplyWithDetailEto<CisDrugApplyDetailEto, CisDrugApplyDetailNto> {

    @Serial
    private static final long serialVersionUID = -2401792203212938518L;

    @Schema(description = "用法")
    private String usage;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "疗程")
    private String treatmentCourse;
    @Schema(description = "疗程单位")
    private String treatmentCourseUnit;
    @Schema(description = "疗程单位名称")
    private String treatmentCourseUnitName;
    //    private SbadmWayEnum sbadmWay;
    private List<CisDrugApplyDetailEto> cisDrugApplyDetailEtos;
    private List<CisDrugApplyDetailNto> cisDrugApplyDetailNtos;
    @Schema(description = "药房")
    private String receiveOrg;
    @Schema(description = "药房名称")
    private String receiveOrgName;

    @NotBlank(message = "用法不能为空！")
    public String getUsage() {
        return usage;
    }

//    public SbadmWayEnum getSbadmWay() {
//        return sbadmWay;
//    }
//
//    public void setSbadmWay(SbadmWayEnum sbadmWay) {
//        this.sbadmWay = sbadmWay;
//    }

    public void setUsage(String usage) {
        this.usage = StringUtils.trimToNull(usage);
    }

    public String getTreatmentCourse() {
        return treatmentCourse;
    }

    public void setTreatmentCourse(String treatmentCourse) {
        this.treatmentCourse = StringUtils.trimToNull(treatmentCourse);
    }

    public String getTreatmentCourseUnit() {
        return treatmentCourseUnit;
    }

    public void setTreatmentCourseUnit(String treatmentCourseUnit) {
        this.treatmentCourseUnit = StringUtils.trimToNull(treatmentCourseUnit);
    }

    public List<CisDrugApplyDetailEto> getCisDrugApplyDetailEtos() {
        return cisDrugApplyDetailEtos;
    }

    public void setCisDrugApplyDetailEtos(List<CisDrugApplyDetailEto> cisDrugApplyDetailEtos) {
        this.cisDrugApplyDetailEtos = cisDrugApplyDetailEtos;
    }

    public List<CisDrugApplyDetailNto> getCisDrugApplyDetailNtos() {
        return cisDrugApplyDetailNtos;
    }

    public void setCisDrugApplyDetailNtos(List<CisDrugApplyDetailNto> cisDrugApplyDetailNtos) {
        this.cisDrugApplyDetailNtos = cisDrugApplyDetailNtos;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    public String getTreatmentCourseUnitName() {
        return treatmentCourseUnitName;
    }

    public void setTreatmentCourseUnitName(String treatmentCourseUnitName) {
        this.treatmentCourseUnitName = treatmentCourseUnitName;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }
}