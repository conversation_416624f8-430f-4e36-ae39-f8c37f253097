package com.bjgoodwill.hip.ds.cis.apply.palg.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "病理明细")
public class CisPalgApplyDetailQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -7896191793206892621L;

    @Schema(description = "模糊查询文本")
    private String text;
    @Schema(description = "病理申请单标识")
    private String cisPalgApplyId;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getCisPalgApplyId() {
        return cisPalgApplyId;
    }

    public void setCisPalgApplyId(String cisPalgApplyId) {
        this.cisPalgApplyId = cisPalgApplyId;
    }
}