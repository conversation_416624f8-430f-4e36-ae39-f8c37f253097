package com.bjgoodwill.hip.ds.cis.apply.dgimg.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetailEto;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryNto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "检查申请单")
public class CisDgimgApplyEto extends ApplyWithDetailEto<CisDgimgApplyDetailEto, CisDgimgApplyDetailNto> implements Serializable {

    @Serial
    private static final long serialVersionUID = -6393433257674166752L;

    @Schema(description = "检查注意事项")
    private String precautions;
    @Schema(description = "病历及查体摘要")
    private String medrecordAndExamabstract;
    @Schema(description = "分类")
    private String dgimgClass;
    @Schema(description = "分类名称")
    private String dgimgClassName;
    @Schema(description = "子分类")
    private String dgimgSubClass;
    @Schema(description = "子分类名称")
    private String dgimgSubClassName;
    @Schema(description = "相关辅检")
    private String auxiliaryInspection;
    @Schema(description = "检查目的")
    private String checkPurpose;

    @Schema(description = "是否过敏史")
    private Boolean allergicHistoryFlag;

    @Schema(description = "是否职业病史")
    private Boolean occupationalDiseasesFlag;

    @Schema(description = "临床病史")
    private String clinicalHistory;

    @Schema(description = "是否传染病史")
    private Boolean contagiousDiseaseHistoryFlag;

    private List<CisDgimgApplyDetailEto> cisDgimgApplyDetailEtos;

    private List<CisDgimgApplyDetailNto> cisDgimgApplyDetailNtos;

    @Schema(description = "既往病理检查结果")
    private String previousPathologicalExamin;

    @Schema(description = "申请单诊断")
    private List<ApplyDiagnosisNto> applyDiagnosisNtos;

    @Schema(description = "患者病史")
    private CisMedicalHistoryNto cisMedicalHistoryNto;

    public String getPrecautions() {
        return precautions;
    }

    public void setPrecautions(String precautions) {
        this.precautions = StringUtils.trimToNull(precautions);
    }

    public String getMedrecordAndExamabstract() {
        return medrecordAndExamabstract;
    }

    public void setMedrecordAndExamabstract(String medrecordAndExamabstract) {
        this.medrecordAndExamabstract = StringUtils.trimToNull(medrecordAndExamabstract);
    }

    public String getDgimgClass() {
        return dgimgClass;
    }

    public void setDgimgClass(String dgimgClass) {
        this.dgimgClass = StringUtils.trimToNull(dgimgClass);
    }

    public String getDgimgSubClass() {
        return dgimgSubClass;
    }

    public void setDgimgSubClass(String dgimgSubClass) {
        this.dgimgSubClass = StringUtils.trimToNull(dgimgSubClass);
    }

    public String getAuxiliaryInspection() {
        return auxiliaryInspection;
    }

    public void setAuxiliaryInspection(String auxiliaryInspection) {
        this.auxiliaryInspection = StringUtils.trimToNull(auxiliaryInspection);
    }

    public String getCheckPurpose() {
        return checkPurpose;
    }

    public void setCheckPurpose(String checkPurpose) {
        this.checkPurpose = StringUtils.trimToNull(checkPurpose);
    }

    public List<CisDgimgApplyDetailEto> getCisDgimgApplyDetailEtos() {
        return cisDgimgApplyDetailEtos;
    }

    public void setCisDgimgApplyDetailEtos(List<CisDgimgApplyDetailEto> cisDgimgApplyDetailEtos) {
        this.cisDgimgApplyDetailEtos = cisDgimgApplyDetailEtos;
    }

    public List<CisDgimgApplyDetailNto> getCisDgimgApplyDetailNtos() {
        return cisDgimgApplyDetailNtos;
    }

    public void setCisDgimgApplyDetailNtos(List<CisDgimgApplyDetailNto> cisDgimgApplyDetailNtos) {
        this.cisDgimgApplyDetailNtos = cisDgimgApplyDetailNtos;
    }

    public Boolean getAllergicHistoryFlag() {
        return allergicHistoryFlag;
    }

    public void setAllergicHistoryFlag(Boolean allergicHistoryFlag) {
        this.allergicHistoryFlag = allergicHistoryFlag;
    }

    public Boolean getOccupationalDiseasesFlag() {
        return occupationalDiseasesFlag;
    }

    public void setOccupationalDiseasesFlag(Boolean occupationalDiseasesFlag) {
        this.occupationalDiseasesFlag = occupationalDiseasesFlag;
    }

    public String getClinicalHistory() {
        return clinicalHistory;
    }

    public void setClinicalHistory(String clinicalHistory) {
        this.clinicalHistory = clinicalHistory;
    }

    public Boolean getContagiousDiseaseHistoryFlag() {
        return contagiousDiseaseHistoryFlag;
    }

    public void setContagiousDiseaseHistoryFlag(Boolean contagiousDiseaseHistoryFlag) {
        this.contagiousDiseaseHistoryFlag = contagiousDiseaseHistoryFlag;
    }

    public String getPreviousPathologicalExamin() {
        return previousPathologicalExamin;
    }

    public void setPreviousPathologicalExamin(String previousPathologicalExamin) {
        this.previousPathologicalExamin = previousPathologicalExamin;
    }

    public String getDgimgClassName() {
        return dgimgClassName;
    }

    public void setDgimgClassName(String dgimgClassName) {
        this.dgimgClassName = dgimgClassName;
    }

    public String getDgimgSubClassName() {
        return dgimgSubClassName;
    }

    public void setDgimgSubClassName(String dgimgSubClassName) {
        this.dgimgSubClassName = dgimgSubClassName;
    }

    public List<ApplyDiagnosisNto> getApplyDiagnosisNtos() {
        return applyDiagnosisNtos;
    }

    public void setApplyDiagnosisNtos(List<ApplyDiagnosisNto> applyDiagnosisNtos) {
        this.applyDiagnosisNtos = applyDiagnosisNtos;
    }

    public CisMedicalHistoryNto getCisMedicalHistoryNto() {
        return cisMedicalHistoryNto;
    }

    public void setCisMedicalHistoryNto(CisMedicalHistoryNto cisMedicalHistoryNto) {
        this.cisMedicalHistoryNto = cisMedicalHistoryNto;
    }
}