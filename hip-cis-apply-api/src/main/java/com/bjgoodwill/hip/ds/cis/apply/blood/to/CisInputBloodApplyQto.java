package com.bjgoodwill.hip.ds.cis.apply.blood.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "输血申请单")
public class CisInputBloodApplyQto extends CisBaseApplyQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -6367416149680324535L;


}