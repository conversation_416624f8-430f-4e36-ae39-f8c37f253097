package com.bjgoodwill.hip.ds.cis.apply.skin.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "skin")
public class CisSkinApplyTo extends CisBaseApplyTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1758704030258406513L;

    @Schema(description = "频次")
    private String frequency;
    @Schema(description = "每次剂量")
    private Double dosage;
    @Schema(description = "剂量单位")
    private String dosageUnit;
    @Schema(description = "用法")
    private String usage;
    @Schema(description = "用法名称")
    private String usageName;
    @Schema(description = "药房")
    private String receiveOrg;
    @Schema(description = "药房名称")
    private String receiveOrgName;

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }
}