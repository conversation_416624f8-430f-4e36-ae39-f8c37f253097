package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.business.util.common.to.BaseNto;
import com.bjgoodwill.hip.business.util.econ.enums.SystemItemClassEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "医嘱执行档费用从表")
public class CisOrderExecPlanChargeNto extends BaseNto {

    @Serial
    private static final long serialVersionUID = -3558584132890334029L;

    @Schema(description = "标识")
    private String id;
    //    @Schema(description = "医嘱执行档标识")
//    private String cisOrderExecPlanId;
    @Schema(description = "抽象父类标识")
    private String cisBaseApplyId;
    @Schema(description = "患者接诊流水号")
    private String visitCode;
    @Schema(description = "收费项目编码")
    private String priceItemCode;
    @Schema(description = "收费项目名称")
    private String priceItemName;
    @Schema(description = "包装规格")
    private String packageSpec;
    @Schema(description = "单价")
    private BigDecimal price;
    @Schema(description = "单位 字典Measures")
    private String unit;
    @Schema(description = "单位名称 字典Measures")
    private String unitName;
    @Schema(description = "数量")
    private Double num;
    @Schema(description = "应收")
    private BigDecimal chargeAmount;
    @Schema(description = "是否为固定项")
    private Boolean isFixed;
    @Schema(description = "特限符合标识 1符合；0 不符合")
    private Boolean limitConformFlag;
    @Schema(description = "执行/取药科室")
    private String executeOrgCode;
    @Schema(description = "执行/取药科室名称")
    private String executeOrgName;
    @Schema(description = "费用类型")
    private CisChargeTypeEnum chargeType;
    @Schema(description = "系统项目分类")
    private SystemItemClassEnum systemItemClass;
    @Schema(description = "申请单明细id")
    private String detailId;

    @NotBlank(message = "标识不能为空！")
    @Size(max = 50, message = "标识长度不能超过50个字符！")
    public String getId() {
        return id;
    }

//    @NotBlank(message = "医嘱执行档标识不能为空！")
//    @Size(max = 50, message = "医嘱执行档标识长度不能超过50个字符！")
//    public String getCisOrderExecPlanId() {
//        return cisOrderExecPlanId;
//    }
//
//    public void setCisOrderExecPlanId(String cisOrderExecPlanId) {
//        this.cisOrderExecPlanId = StringUtils.trimToNull(cisOrderExecPlanId);
//    }

    public void setId(String id) {
        this.id = StringUtils.trimToNull(id);
    }

    @Size(max = 50, message = "抽象父类标识长度不能超过50个字符！")
    @NotBlank(message = "申请单ID不能为空！")
    public String getCisBaseApplyId() {
        return cisBaseApplyId;
    }

    public void setCisBaseApplyId(String cisBaseApplyId) {
        this.cisBaseApplyId = StringUtils.trimToNull(cisBaseApplyId);
    }

    @NotBlank(message = "患者接诊流水号不能为空！")
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = StringUtils.trimToNull(visitCode);
    }

    @NotBlank(message = "收费项目编码不能为空！")
    public String getPriceItemCode() {
        return priceItemCode;
    }

    public void setPriceItemCode(String priceItemCode) {
        this.priceItemCode = StringUtils.trimToNull(priceItemCode);
    }

    public String getPriceItemName() {
        return priceItemName;
    }

    public void setPriceItemName(String priceItemName) {
        this.priceItemName = StringUtils.trimToNull(priceItemName);
    }

    public String getPackageSpec() {
        return packageSpec;
    }

    public void setPackageSpec(String packageSpec) {
        this.packageSpec = StringUtils.trimToNull(packageSpec);
    }

    @NotNull(message = "单价不能为空！")
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = StringUtils.trimToNull(unit);
    }

    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public BigDecimal getChargeAmount() {
        return chargeAmount;
    }

    public void setChargeAmount(BigDecimal chargeAmount) {
        this.chargeAmount = chargeAmount;
    }

    public Boolean getIsFixed() {
        return isFixed;
    }

    public void setIsFixed(Boolean isFixed) {
        this.isFixed = isFixed;
    }

    public Boolean getLimitConformFlag() {
        return limitConformFlag;
    }

    public void setLimitConformFlag(Boolean limitConformFlag) {
        this.limitConformFlag = limitConformFlag;
    }

    @NotBlank(message = "执行/取药科室不能为空！")
    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    public void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = StringUtils.trimToNull(executeOrgCode);
    }

    @NotNull(message = "费用类型不能为空！")
    public CisChargeTypeEnum getChargeType() {
        return chargeType;
    }

    public void setChargeType(CisChargeTypeEnum chargeType) {
        this.chargeType = chargeType;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    @NotNull(message = "系统项目分类不能为空！")
    public SystemItemClassEnum getSystemItemClass() {
        return systemItemClass;
    }

    public void setSystemItemClass(SystemItemClassEnum systemItemClass) {
        this.systemItemClass = systemItemClass;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }
}