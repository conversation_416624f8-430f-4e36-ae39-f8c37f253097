package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.*;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeEto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeQto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeTo;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Tag(name = "抽象父类领域服务", description = "抽象父类领域服务")
public interface CisBaseApplyService {

    @Operation(summary = "P0根据查询条件对抽象父类进行查询。")
    @GetMapping("/cisBaseApplies")
    List<CisBaseApplyTo> getCisBaseApplies(@ParameterObject @SpringQueryMap CisBaseApplyQto cisBaseApplyQto);

    @Operation(summary = "根据查询条件对抽象父类进行分页查询。")
    @GetMapping("/cisBaseApplies/pages")
    GridResultSet<CisBaseApplyTo> getCisBaseApplyPage(@ParameterObject @SpringQueryMap CisBaseApplyQto cisBaseApplyQto);

    @Operation(summary = "P0根据唯一标识删除抽象父类。")
    @DeleteMapping("/cisBaseApplies/{id:.+}")
    void deleteCisBaseApply(@PathVariable("id") String id);

    @Operation(summary = "P0根据查询条件对CisApplyCharge进行查询。")
    @PostMapping("/cisBaseApplies/{cisBaseApplyId}/getCisApplyCharges")
    List<CisApplyChargeTo> getCisApplyCharges(@PathVariable("cisBaseApplyId") String cisBaseApplyId, @RequestBody CisApplyChargeQto cisApplyChargeQto);

    @Operation(summary = "P0根据cisBaseApplyIds查询条件对CisApplyCharge进行查询。")
    @PostMapping("/cisBaseApplies/cisApplyCharge/getCisApplyCharges-by-cisBaseApplyIds")
    List<CisApplyChargeTo> getCisApplyChargesByCisBaseApplyIds(@RequestParam("cisBaseApplyIds") List<String> cisBaseApplyIds);

    @Operation(summary = "根据查询条件对CisApplyCharge进行分页查询。")
    @PostMapping("/cisBaseApply/{cisBaseApplyId}/cisApplyCharges/pages")
    GridResultSet<CisApplyChargeTo> getCisApplyChargePage(@PathVariable("cisBaseApplyId") String cisBaseApplyId, @RequestBody CisApplyChargeQto cisApplyChargeQto);

    @Operation(summary = "P0创建CisApplyCharge。")
    @PostMapping("/cisBaseApplies/{cisBaseApplyId}/creatCisApplyCharges")
    CisApplyChargeTo createCisApplyCharge(@PathVariable("cisBaseApplyId") String cisBaseApplyId, @RequestBody @Valid CisApplyChargeNto cisApplyChargeNto);

    @Operation(summary = "根据唯一标识修改CisApplyCharge。")
    @PutMapping("/cisBaseApplies/xId/cisApplyCharges/{id:.+}")
    void updateCisApplyCharge(@PathVariable("id") String id, @RequestBody @Valid CisApplyChargeEto cisApplyChargeEto);

    @Operation(summary = "P0根据唯一标识删除CisApplyCharge。")
    @DeleteMapping("/cisBaseApplies/xId/cisApplyCharges/delete/{id:.+}")
    void deleteCisApplyCharge(@PathVariable("id") String id);

    @Operation(summary = "P0根据唯一标识批量删除CisApplyCharge。")
    @DeleteMapping("/cisBaseApplies/xId/cisApplyCharges/delete-batch")
    void deleteCisApplyChargeByIds(@RequestParam("ids") List<String> ids);

    @Operation(summary = "P0根据Id对CisApplyCharge进行查询。")
    @GetMapping("/cisBaseApplies/cisApplyCharges/{id:.+}")
    CisApplyChargeTo getCisApplyChargeById(@PathVariable("id") String id);

    @Operation(summary = "P0根据查询条件对医嘱执行档进行查询。")
    @PostMapping("/cisBaseApplies/{cisBaseApplyId}/get/cisOrderExecPlans")
    List<CisOrderExecPlanTo> getCisOrderExecPlans(@PathVariable("cisBaseApplyId") String cisBaseApplyId, @RequestBody CisOrderExecPlanQto cisOrderExecPlanQto);

    @Operation(summary = "根据查询条件对医嘱执行档进行分页查询。")
    @PostMapping("/cisBaseApply/{cisBaseApplyId}/cisOrderExecPlans/pages")
    GridResultSet<CisOrderExecPlanTo> getCisOrderExecPlanPage(@PathVariable("cisBaseApplyId") String cisBaseApplyId, @RequestBody CisOrderExecPlanQto cisOrderExecPlanQto);

    @Operation(summary = "P0根据唯一标识返回医嘱执行档。")
    @GetMapping("/cisBaseApplies/xId/cisOrderExecPlans/{id:.+}")
    CisOrderExecPlanTo getCisOrderExecPlanById(@PathVariable("id") String id);

    @Operation(summary = "P0创建医嘱执行档。")
    @PostMapping("/cisBaseApplies/{cisBaseApplyId}/create/cisOrderExecPlans")
    CisOrderExecPlanTo createCisOrderExecPlan(@PathVariable("cisBaseApplyId") String cisBaseApplyId, @RequestBody @Valid CisOrderExecPlanNto cisOrderExecPlanNto);

    @Operation(summary = "P0根据唯一标识修改医嘱执行档。")
    @PutMapping("/cisBaseApplies/xId/update/cisOrderExecPlans/{id:.+}")
    void updateCisOrderExecPlan(@PathVariable("id") String id, @RequestBody @Valid CisOrderExecPlanEto cisOrderExecPlanEto);

    @Operation(summary = "P0根据唯一标识删除医嘱执行档。")
    @DeleteMapping("/cisBaseApplies/xId/delete/cisOrderExecPlans/{id:.+}")
    void deleteCisOrderExecPlan(@PathVariable("id") String id);


    //region 根据患者流水号查询申请单
    @Operation(summary = "P0根据流水号查询申请单。")
    @GetMapping("/cisBaseApplies/visitCode/{visit-code:.+}")
    List<CisBaseApplyTo> getCisBaseApplyByVisitCode(@PathVariable("visit-code") String visitCode);
    //endregion

    //region 申请单校对
    @Operation(summary = "P0申请单校对-临时医嘱拆分")
    @PutMapping("/cisBaseApplies/proof/CisBaseApplys")
    List<CisBaseApplyTo> proofCisBaseApplys(@RequestBody List<CisSplitEto> etos);

    @Operation(summary = "申请单校对-临时医嘱拆分-只拼数据 不保存")
    @PutMapping("/cisBaseApplies/proof/CisBaseApplys/noSave")
    List<CisBaseApplyTo> proofCisBaseApplysNoSave(@RequestBody List<CisSplitEto> etos);

    @Operation(summary = "P0申请单校对-临时医嘱拆分-数据保存")
    @PutMapping("/cisBaseApplies/proof/CisBaseApplys/save")
    void proofCisBaseApplysSave(@RequestBody List<CisBaseApplyNto> etos);

    //region 申请单校对 --废弃
    //    @Operation(summary = "申请单校对2,deptNurseCode 护理组号")
    //    @PutMapping("/cisBaseApplies/proof/CisBaseApplysBatch")
    //    void proofCisBaseApplysBatch(@RequestParam String deptNurseCode, @RequestParam List<String> orderIds);

    //endregion

    //endregion

    //region 拆分
    @Operation(summary = "P0长期医嘱拆分")
    @PutMapping("/cisBaseApplies/split/CisBaseApplys")
    List<CisBaseApplyTo> splitCisBaseApplys(@RequestBody List<CisSplitEto> etos);

    @Operation(summary = "长期医嘱拆分-只拼数据 不保存")
    @PutMapping("/cisBaseApplies/split/CisBaseApplys/noSave")
    List<CisBaseApplyTo> splitCisBaseApplysNoSave(@RequestBody List<CisSplitEto> etos);

    @Operation(summary = "申请单校对-长期医嘱拆分-只拼数据 不保存")
    @PutMapping("/cisBaseApplies/split/CisBaseApplys/proofNoSave")
    List<CisBaseApplyTo> splitCisBaseApplysNoSaveProof(@RequestBody List<CisSplitEto> etos);

    @Operation(summary = "P0申请单校对-长期医嘱拆分-数据保存")
    @PutMapping("/cisBaseApplies/split/CisBaseApplys/proofSave")
    List<CisBaseApplyTo> splitCisBaseApplysSaveProof(@RequestBody List<CisSplitEto> etos);

    @Operation(summary = "P0申请单校对-临时医嘱拆分-数据保存")
    @PutMapping("/cisBaseApplies/split/CisBaseApplys/save")
    void splitCisBaseApplysSave(@RequestBody List<CisBaseApplyNto> ntos);
    //endregion


    @Operation(summary = "P0申请单保存-副本。")
    @PutMapping("/cisBaseApplies/create/CisBaseApplys")
    void createCisBaseApplys(@RequestBody List<CisBaseApplyNto> entitys);

    @Operation(summary = "P0申请单修改-副本。")
    @PutMapping("/cisBaseApplies/update/CisBaseApply/clone")
    CisBaseApplyTo updateCisBaseApplyClone(@RequestBody CisBaseApplyEto entity, @RequestParam String baseApplyId);

    @Operation(summary = "P0申请单提交。")
    @PutMapping("/cisBaseApplies/submit/CisBaseApplys")
    void submitCisBaseApplys(@RequestParam String visitCode, @RequestParam List<String> orderIds);


    @Operation(summary = "门诊申请单提交，拆分")
    @PutMapping("/cisBaseApplies/submit/opd/CisBaseApplys")
    List<CisBaseApplyTo> submitOpdCisBaseApplys(@RequestParam String visitCode, @RequestParam List<String> orderIds);

    @Operation(summary = "P0申请单撤回。")
    @GetMapping("/cisBaseApplies/cisApplyCharges/{id:.+}/withdraw")
    void withdraw(@PathVariable("id") String id);

    @Operation(summary = "P0停止校对，费用查询")
    @GetMapping("/cisBaseApplies/proof/stop/query")
    List<CisBaseApplyTo> getProofStopCisBaseApplyByOrderIds(@ParameterObject @SpringQueryMap List<CisProofQto> qtos);


    @Operation(summary = "P0停止申请单校对查询。--护士站停止校对--弹出框选择之后用")
    @PutMapping("/cisBaseApplies/proof/stop")
    void proofStop(@RequestParam("orderIds") List<String> orderIds);


    @Operation(summary = "P0执行单执行")
    @PutMapping("/cisBaseApplies/orderPlan/exec")
    void execOrderPlan(@RequestBody List<CisProofEto> etos);


    @Operation(summary = "P0执行单取消执行")
    @PutMapping("/cisBaseApplies/orderPlan/cancelPlansExecute")
    void cancelPlansExecute(@RequestBody List<CisProofEto> etos);

    @Operation(summary = "P0执行单不执行")
    @PutMapping("/cisBaseApplies/orderPlan/noExecPlansExecute")
    void noExecPlansExecute(@RequestBody List<CisProofEto> etos);

    @Operation(summary = "P0申请单预停止操作。")
    @PutMapping("/cisBaseApplies/preObsolete/{order-id:.+}")
    void preObsolete(@PathVariable("order-id") String orderid);

    @Operation(summary = "P0作废校对-护士站")
    @PutMapping("/cisBaseApplies/proof/obsolete")
    void proofObsolete(@RequestParam String deptNurseCode, @RequestParam List<String> orderIds);

    @Operation(summary = "P0根据ID对抽象父类进行查询。")
    @GetMapping("/cisBaseApplies/byApplyId/{id:.+}")
    CisBaseApplyTo getCisBaseApplyById(@PathVariable("id") String id);

    @Operation(summary = "P0医嘱的补费")
    @PutMapping("/cisBaseApplies/{order-id:.+}/noSplit/repairFee")
    CisBaseApplyTo addCisApplyCharge(@PathVariable("order-id") String orderid, @RequestBody CisApplyChargeNto cisApplyChargeNto);

    @Operation(summary = "P0 门诊医嘱的补费")
    @PutMapping("/cisBaseApplies/{id:.+}/opd/repairFee")
    CisBaseApplyTo repairOpdFeeCharge(@PathVariable("id") String id, @RequestBody List<CisApplyChargeNto> cisApplyChargeNtos);

    @Operation(summary = "通过医嘱id查询申请单相关--少用")
    @PutMapping("/cisBaseApplies/find")
    List<CisBaseApplyTo> findCisApplyByOrderId(@RequestParam String orderId);


    //    @Operation(summary = "医嘱费用删除")
    //    @PutMapping("/cisBaseApplies/{order-id:.+}/noSplit/remove")
    //    CisBaseApplyTo removeCisApplyCharge(@PathVariable("order-id") String orderid,@RequestParam String chargeId);
    //
    //    @Operation(summary = "医嘱补费对照的补费")
    //    @PutMapping("/cisBaseApplies/{order-id:.+}/noSplit/update")
    //    CisBaseApplyTo updateCisApplyChargeNto(@PathVariable("order-id") String orderid,@RequestBody CisApplyChargeEto cisApplyChargeEto);
    //
    //    @Operation(summary = "申请单补费")
    //    @PutMapping("/cisBaseApplies/{id:.+}/split/repairFee")
    //    CisBaseApplyTo repairCisApplyChargeNto(@PathVariable("id") String id,@RequestBody CisApplyChargeNto cisApplyChargeNto);

    //region 出入转
    //
    //    List<CisBaseApplyTo> findCisBaseApplyExpenseReview(@RequestParam String visitCode);
    //endregion

    //region 保存直接生成申请单NEW 校对 拆分 不产生新的申请单
    @Operation(summary = "P0申请单新增。")
    @PutMapping("/cisBaseApplies/save/apply")
    List<CisBaseApplyTo> saveApplys(@RequestBody List<CisBaseApplyNto> entitys);

    @Operation(summary = "P0申请单新增并提交。")
    @PutMapping("/cisBaseApplies/saveandsubmit/apply")
    List<CisBaseApplyTo> saveAndSubmitApplys(@RequestBody List<CisBaseApplyNto> entitys);


    @Operation(summary = "P0申请单提交。")
    @PutMapping("/cisBaseApplies/submit/apply")
    void submitApplys(@RequestParam String visitCode, @RequestParam List<String> orderIds);

    @Operation(summary = "P0申请单校对-临时医嘱拆分")
    @PutMapping("/cisBaseApplies/proof/apply")
    List<CisBaseApplyTo> proofApplys(@RequestBody List<CisSplitEto> etos);


    @Operation(summary = "P0申请单校对-临时医嘱拆分")
    @PutMapping("/cisBaseApplies/proof/apply/save")
    void saveProofExecPlans(@RequestBody List<CisProofNto> ntos);


    @Operation(summary = "P0申请单校对-长期医嘱拆分")
    @PutMapping("/cisBaseApplies/split/apply/save")
    void saveSplitExecPlans(@RequestBody List<CisSplitNto> ntos);

    @Operation(summary = "P0申请单修改")
    @PutMapping("/cisBaseApplies/update/cisBaseApply")
    CisBaseApplyTo updateCisBaseApply(@RequestBody CisBaseApplyEto entity, @RequestParam String baseApplyId);

    @Operation(summary = "P0申请单回退")
    @PutMapping("/cisBaseApplies/backUp/cisBaseApply")
    void backUpCisBaseApply(@RequestParam List<String> orderIds);

    @Operation(summary = "P0门诊申请单回退")
    @PutMapping("/cisBaseApplies/backUp/cisBaseApplyOpd")
    void backUpCisBaseApplyOpd(@RequestParam List<String> prescriptionIds);

    @Operation(summary = "P0修改门诊申请单的处方号")
    @PutMapping("/cisBaseApplies/update/opd/prescriptionId")
    void updatePrescriptionId(@RequestParam Map<String, String> prescriptionIdMap);
    //endregion

    @Operation(summary = "P0根据医嘱ID修改门诊申请单的处方号")
    @PutMapping("/cisBaseApplies/update/opd/prescriptionId-by-orderIds")
    void updatePrescriptionIdByOrderIds(@RequestBody List<CisBaseApplySplitGroupNewEto> etos);


    //region 医技执行
//    @Operation(summary = "测试。")
//    @GetMapping("/cisBaseApplies/test/{id:.+}")
//    void test(@PathVariable("id") String id);
    //endregion

    @Operation(summary = "P0根据Id对CisApplyCharge进行查询。")
    @GetMapping("/cisBaseApplies/findCisBaseApplyByOrderIds")
    List<CisBaseApplyTo> findCisBaseApplyByOrderIds(@RequestParam List<String> orderIds);

    @Operation(summary = "P0根据创建时间获取检验检查申请信息。")
    @GetMapping("/cisBaseApplies/findCisBaseApplyByCreateDate")
    List<CisBaseApplyTo> findCisBaseApplyByCreateDate(@RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDateTime date);

    @Operation(summary = "查询当前患者是否使用该诊断。")
    @GetMapping("/cisBaseApplies/{visit-code:.+}/queryOrderIdsByDiagCode")
    List<String> queryOrderIdsByDiagCode(@PathVariable("visit-code") String visitCode, @RequestParam("diagCode") String diagCode);

    @Operation(summary = "查询当前患者是否使用该诊断。")
    @GetMapping("/cisBaseApplies/treatmentCode/{treatment-code:.+}/queryOrderIdsByDiagCode")
    List<String> queryOrderIdsByDiagCodeWithTreatmentCode(@PathVariable("treatment-code") String treatmentCode, @RequestParam("diagCode") String diagCode);


    @Operation(summary = "P0根据Id对CisApply以及预约进行查询-护士工作站-检查/检验。")
    @GetMapping("/cisBaseApplies/findCisBaseApplyAndBookingByOrderIds")
    List<CisBaseApplyTo> findCisBaseApplyAndBookingByOrderIds(@RequestParam List<String> orderIds);

    @Operation(summary = "P0根据流水号对CisApply查询-PRD_M325_住院医生站_报告查看")
    @GetMapping("/cisBaseApplies/{visit-code:.+}/report")
    List<CisBaseApplyTo> findCisBaseApplyByVisitCode(@PathVariable("visit-code") String visitCode, @RequestParam("order-class") SystemTypeEnum orderClass);

    @Operation(summary = "P0按照申请单明细平铺返回。")
    @PostMapping("/cisBaseApplies/detailTile/queryCisBaseApplyByOrderIds")
    List<CisBaseApplyCustomTo> queryCisBaseApplyByOrderIds(@RequestBody List<String> orderIds);

    @Operation(summary = "P0根据查询条件患者医技科室内的医嘱执行档进行分页查询。")
    @PostMapping("/cisBaseApply/cisOrderExecPlans/pages")
    GridResultSet<CisOrderExecPlanMtTo> findCisOrderExecPlanMtPages(@ParameterObject @SpringQueryMap CisOrderExecPlanMtQto qto);

    @Operation(summary = "P0根据Id对申请单进行查询。")
    @PostMapping("/cisBaseApply/findCisBaseApplyByApplyIds")
    List<CisBaseApplyTo> findCisBaseApplyByApplyIds(@RequestParam List<String> applyIds);

    @Operation(summary = "获取单次就诊使用的诊断。")
    @GetMapping("/cisBaseApply/{visit-code:.+}/findDiagnosis")
    List<ApplyDiagnosisTo> findCisDiaggnosisByVisitCode(@PathVariable("visit-code") String visitCode);
}