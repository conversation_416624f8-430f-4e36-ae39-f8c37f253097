package com.bjgoodwill.hip.ds.cis.apply.dgimg.to;

import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetialNto;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "检查申请单")
public class CisDgimgApplyNto extends ApplyWithDetialNto<CisDgimgApplyDetailNto> implements Serializable {

    @Serial
    private static final long serialVersionUID = -1919768792319270735L;

    @Schema(description = "检查注意事项")
    private String precautions;
    @Schema(description = "病历及查体摘要")
    private String medrecordAndExamabstract;
    @Schema(description = "体格及其他检查")
    private String physiqueAndExam;
    @Schema(description = "分类")
    private String dgimgClass;
    @Schema(description = "分类名称")
    private String dgimgClassName;
    @Schema(description = "子分类")
    private String dgimgSubClass;
    @Schema(description = "子分类名称")
    private String dgimgSubClassName;
    @Schema(description = "相关辅检")
    private String auxiliaryInspection;
    @Schema(description = "检查目的")
    private String checkPurpose;
    @Schema(description = "申请单预约标识")
    private String applyBookId;
    @Schema(description = "既往病理检查结果")
    private String previousPathologicalExamin;
    @Schema(description = "检查申请单明细")
    private List<CisDgimgApplyDetailNto> details = new ArrayList<>();

    @Schema(description = "设备类型")
    private String deviceType;

    @Schema(description = "设备类型名称")
    private String deviceTypeName;

    @Schema(description = "是否过敏史")
    private Boolean allergicHistoryFlag;

    @Schema(description = "是否职业病史")
    private Boolean occupationalDiseasesFlag;

    @Schema(description = "临床病史")
    private String clinicalHistory;

    @Schema(description = "是否传染病史")
    private Boolean contagiousDiseaseHistoryFlag;

    @Schema(description = "患者病史")
    private CisMedicalHistoryNto cisMedicalHistoryNto;

    public String getPrecautions() {
        return precautions;
    }

    public void setPrecautions(String precautions) {
        this.precautions = StringUtils.trimToNull(precautions);
    }

    @NotBlank(message = "病历及查体摘要不能为空！")
    public String getMedrecordAndExamabstract() {
        return medrecordAndExamabstract;
    }

    public void setMedrecordAndExamabstract(String medrecordAndExamabstract) {
        this.medrecordAndExamabstract = StringUtils.trimToNull(medrecordAndExamabstract);
    }

    public String getPhysiqueAndExam() {
        return physiqueAndExam;
    }

    public void setPhysiqueAndExam(String physiqueAndExam) {
        this.physiqueAndExam = StringUtils.trimToNull(physiqueAndExam);
    }

    public String getDgimgClass() {
        return dgimgClass;
    }

    public void setDgimgClass(String dgimgClass) {
        this.dgimgClass = StringUtils.trimToNull(dgimgClass);
    }

    public String getDgimgSubClass() {
        return dgimgSubClass;
    }

    public void setDgimgSubClass(String dgimgSubClass) {
        this.dgimgSubClass = StringUtils.trimToNull(dgimgSubClass);
    }

    @NotBlank(message = "相关辅检不能为空！")
    public String getAuxiliaryInspection() {
        return auxiliaryInspection;
    }

    public void setAuxiliaryInspection(String auxiliaryInspection) {
        this.auxiliaryInspection = StringUtils.trimToNull(auxiliaryInspection);
    }

    @NotBlank(message = "检查目的不能为空！")
    public String getCheckPurpose() {
        return checkPurpose;
    }

    public void setCheckPurpose(String checkPurpose) {
        this.checkPurpose = StringUtils.trimToNull(checkPurpose);
    }

    @Size(max = 50, message = "申请单预约标识长度不能超过50个字符！")
    public String getApplyBookId() {
        return applyBookId;
    }

    public void setApplyBookId(String applyBookId) {
        this.applyBookId = StringUtils.trimToNull(applyBookId);
    }

    public String getPreviousPathologicalExamin() {
        return previousPathologicalExamin;
    }

    public void setPreviousPathologicalExamin(String previousPathologicalExamin) {
        this.previousPathologicalExamin = StringUtils.trimToNull(previousPathologicalExamin);
    }

    @Override
    @NotEmpty(message = "明细不能为空！")
    public List<CisDgimgApplyDetailNto> getDetails() {
        return details;
    }

    @Override
    public void setDetails(List<CisDgimgApplyDetailNto> details) {
        this.details = details;
    }


    @NotBlank(message = "设备类型不能为空！")
    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }


    public Boolean getAllergicHistoryFlag() {
        return allergicHistoryFlag;
    }

    public void setAllergicHistoryFlag(Boolean allergicHistoryFlag) {
        this.allergicHistoryFlag = allergicHistoryFlag;
    }

    public Boolean getOccupationalDiseasesFlag() {
        return occupationalDiseasesFlag;
    }

    public void setOccupationalDiseasesFlag(Boolean occupationalDiseasesFlag) {
        this.occupationalDiseasesFlag = occupationalDiseasesFlag;
    }

    @NotBlank(message = "临床病史不能为空！")
    public String getClinicalHistory() {
        return clinicalHistory;
    }

    public void setClinicalHistory(String clinicalHistory) {
        this.clinicalHistory = clinicalHistory;
    }

    public Boolean getContagiousDiseaseHistoryFlag() {
        return contagiousDiseaseHistoryFlag;
    }

    public void setContagiousDiseaseHistoryFlag(Boolean contagiousDiseaseHistoryFlag) {
        this.contagiousDiseaseHistoryFlag = contagiousDiseaseHistoryFlag;
    }

    public String getDgimgClassName() {
        return dgimgClassName;
    }

    public void setDgimgClassName(String dgimgClassName) {
        this.dgimgClassName = dgimgClassName;
    }

    @NotBlank(message = "设备类型名称不能为空！")
    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    public String getDgimgSubClassName() {
        return dgimgSubClassName;
    }

    public void setDgimgSubClassName(String dgimgSubClassName) {
        this.dgimgSubClassName = dgimgSubClassName;
    }

    @NotEmpty(message = "申请单诊断不能为空！")
    public List<ApplyDiagnosisNto> getApplyDiagnosisNtos() {
        return applyDiagnosisNtos;
    }

    public CisMedicalHistoryNto getCisMedicalHistoryNto() {
        return cisMedicalHistoryNto;
    }

    public void setCisMedicalHistoryNto(CisMedicalHistoryNto cisMedicalHistoryNto) {
        this.cisMedicalHistoryNto = cisMedicalHistoryNto;
    }
}