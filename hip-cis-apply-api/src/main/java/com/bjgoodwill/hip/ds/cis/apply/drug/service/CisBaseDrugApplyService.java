package com.bjgoodwill.hip.ds.cis.apply.drug.service;

import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyService;
import com.bjgoodwill.hip.ds.cis.apply.detail.service.ApplyWithDetialService;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisBaseDrugApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisBaseDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisBaseDrugApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisDrugApplyDetailTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "药品申请单领域服务", description = "药品申请单领域服务")
public interface CisBaseDrugApplyService extends CisBaseApplyService, ApplyWithDetialService {

    @Operation(summary = "P0根据唯一标识返回药品申请单。")
    @GetMapping("/cisBaseDrugApplies/{id:.+}")
    CisBaseDrugApplyTo getCisBaseDrugApplyById(@PathVariable("id") String id);

    @Operation(summary = "P0创建药品申请单。")
    @PostMapping("/cisBaseDrugApplies")
    CisBaseDrugApplyTo createCisBaseDrugApply(@RequestBody @Valid CisBaseDrugApplyNto cisBaseDrugApplyNto);

    @Operation(summary = "P0根据唯一标识修改药品申请单。")
    @PutMapping("/cisBaseDrugApplies/{id:.+}")
    void updateCisBaseDrugApply(@PathVariable("id") String id, @RequestBody @Valid CisBaseDrugApplyEto cisBaseDrugApplyEto);

    @Operation(summary = "P0根据唯一标识删除药品申请单。")
    @DeleteMapping("/cisBaseDrugApplies/{id:.+}")
    void deleteCisBaseDrugApply(@PathVariable("id") String id);

    @Operation(summary = "P0根据申请单Id返回中草药申请单。")
    @GetMapping("/cisBaseDrugApplies/{applyId}/getCisDrugApplyDetails")
    List<CisDrugApplyDetailTo> getCisDrugApplyByApplyId(@PathVariable("applyId") String applyId);

    @Operation(summary = "P0根据申请单Id集合返回明细。")
    @GetMapping("/cisBaseDrugApplies/applyId/findCisDrugApplyDetails")
    List<CisDrugApplyDetailTo> findCisDrugApplyByApplyIdList(@RequestParam("applyIds") List<String> applyIds);

}