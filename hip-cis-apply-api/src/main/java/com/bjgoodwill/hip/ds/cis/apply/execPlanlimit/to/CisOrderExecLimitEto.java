package com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "执行单第三方状态操作限制")
public class CisOrderExecLimitEto extends BaseEto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2606168225176939385L;

    @Schema(description = "第三方状态名称")
    private String thirdStatus;
    @Schema(description = "false 允许不执行")
    private Boolean noExecFlag;
    @Schema(description = "false 允许取消执行")
    private Boolean cancelExecFlag;
    @Schema(description = "false 允许退费")
    private String refundsFlag;

    public String getThirdStatus() {
        return thirdStatus;
    }

    public void setThirdStatus(String thirdStatus) {
        this.thirdStatus = StringUtils.trimToNull(thirdStatus);
    }

    public Boolean getNoExecFlag() {
        return noExecFlag;
    }

    public void setNoExecFlag(Boolean noExecFlag) {
        this.noExecFlag = noExecFlag;
    }

    public Boolean getCancelExecFlag() {
        return cancelExecFlag;
    }

    public void setCancelExecFlag(Boolean cancelExecFlag) {
        this.cancelExecFlag = cancelExecFlag;
    }

    public String getRefundsFlag() {
        return refundsFlag;
    }

    public void setRefundsFlag(String refundsFlag) {
        this.refundsFlag = StringUtils.trimToNull(refundsFlag);
    }
}