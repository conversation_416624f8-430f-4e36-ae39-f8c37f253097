package com.bjgoodwill.hip.ds.cis.apply.palg.to;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "病理申请单")
public class CisPalgApplyTo extends CisBaseApplyTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -6404045788137348713L;

    @Schema(description = "最后一次月经日期")
    private LocalDateTime lastMenstrualDate;
    @Schema(description = "绝经")
    private Boolean isMenopause;
    @Schema(description = "病人病历(选择所有现时适应的个案)")
    private String medicalRecord;
    @Schema(description = "其他现时适应个案")
    private String ortherQuestions;
    @Schema(description = "过去检查结果")
    private String pastResults;
    @Schema(description = "检查目的")
    private String checkPurpose;
    @Schema(description = "是否传染病")
    private Boolean contagiousDiseaseHistoryFlag;
    @Schema(description = "病理申请单扩展表")
    private CisPalgApplyExtTo palgExt;
    @Schema(description = "病理明细")
    private List<CisPalgApplyDetailTo> details;
    @Schema(description = "临床病史")
    private String clinicalHistory;
    @Schema(description = "临床诊断")
    private String clinicalDiagnosis;
    @Schema(description = "患者病史")
    private CisMedicalHistoryTo cisMedicalHistoryTo;

    public LocalDateTime getLastMenstrualDate() {
        return lastMenstrualDate;
    }

    public void setLastMenstrualDate(LocalDateTime lastMenstrualDate) {
        this.lastMenstrualDate = lastMenstrualDate;
    }

    public Boolean getIsMenopause() {
        return isMenopause;
    }

    public void setIsMenopause(Boolean isMenopause) {
        this.isMenopause = isMenopause;
    }

    public String getMedicalRecord() {
        return medicalRecord;
    }

    public void setMedicalRecord(String medicalRecord) {
        this.medicalRecord = medicalRecord;
    }

    public String getOrtherQuestions() {
        return ortherQuestions;
    }

    public void setOrtherQuestions(String ortherQuestions) {
        this.ortherQuestions = ortherQuestions;
    }

    public String getPastResults() {
        return pastResults;
    }

    public void setPastResults(String pastResults) {
        this.pastResults = pastResults;
    }

    public String getCheckPurpose() {
        return checkPurpose;
    }

    public void setCheckPurpose(String checkPurpose) {
        this.checkPurpose = checkPurpose;
    }

    public Boolean getContagiousDiseaseHistoryFlag() {
        return contagiousDiseaseHistoryFlag;
    }

    public void setContagiousDiseaseHistoryFlag(Boolean contagiousDiseaseHistoryFlag) {
        this.contagiousDiseaseHistoryFlag = contagiousDiseaseHistoryFlag;
    }

    public CisPalgApplyExtTo getPalgExt() {
        return palgExt;
    }

    public void setPalgExt(CisPalgApplyExtTo palgExt) {
        this.palgExt = palgExt;
    }

    public List<CisPalgApplyDetailTo> getDetails() {
        return details;
    }

    public void setDetails(List<CisPalgApplyDetailTo> details) {
        this.details = details;
    }

    public String getClinicalHistory() {
        return clinicalHistory;
    }

    public void setClinicalHistory(String clinicalHistory) {
        this.clinicalHistory = clinicalHistory;
    }

    public String getClinicalDiagnosis() {
        return clinicalDiagnosis;
    }

    public void setClinicalDiagnosis(String clinicalDiagnosis) {
        this.clinicalDiagnosis = clinicalDiagnosis;
    }

    public CisMedicalHistoryTo getCisMedicalHistoryTo() {
        return cisMedicalHistoryTo;
    }

    public void setCisMedicalHistoryTo(CisMedicalHistoryTo cisMedicalHistoryTo) {
        this.cisMedicalHistoryTo = cisMedicalHistoryTo;
    }
}