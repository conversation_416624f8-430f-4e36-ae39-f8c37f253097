package com.bjgoodwill.hip.ds.cis.apply.execPlan.to;

import com.bjgoodwill.hip.business.util.common.to.BaseEto;
import com.bjgoodwill.hip.business.util.econ.enums.SetlStasEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.io.Serializable;

@Schema(description = "修改执行单结算枚举标识")
public class CisOrderExecPlanSetlStasEto extends BaseEto implements Serializable {

    @Schema(description = "执行单ID")
    private String execPlanId;

    @Schema(description = "结算状态SetlStasEnum")
    private SetlStasEnum setlStas;

    @NotBlank(message = "执行单ID不能为空！")
    public String getExecPlanId() {
        return execPlanId;
    }

    public void setExecPlanId(String execPlanId) {
        this.execPlanId = execPlanId;
    }

    public SetlStasEnum getSetlStas() {
        return setlStas;
    }

    public void setSetlStas(SetlStasEnum setlStas) {
        this.setlStas = setlStas;
    }
}