package com.bjgoodwill.hip.ds.cis.apply.apply.to;


import com.bjgoodwill.hip.business.util.cis.util.CisBaseApplyCommonNto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanNto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

//@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(description = "抽象父类")
public abstract class CisBaseApplyNto extends CisBaseApplyCommonNto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2971493602553982141L;
    @Schema(description = "申请单诊断")
    protected List<ApplyDiagnosisNto> applyDiagnosisNtos = new ArrayList<>();
    @Schema(description = "CisApplyCharge列表")
    private List<CisApplyChargeNto> cisApplyCharges = new ArrayList<>();
    @Schema(description = "医嘱执行档列表")
    private List<CisOrderExecPlanNto> cisOrderExecPlans = new ArrayList<>();

    public List<CisApplyChargeNto> getCisApplyCharges() {
        return cisApplyCharges;
    }

    public void setCisApplyCharges(List<CisApplyChargeNto> cisApplyCharges) {
        this.cisApplyCharges = cisApplyCharges;
    }

    public List<CisOrderExecPlanNto> getCisOrderExecPlans() {
        return cisOrderExecPlans;
    }

    public void setCisOrderExecPlans(List<CisOrderExecPlanNto> cisOrderExecPlans) {
        this.cisOrderExecPlans = cisOrderExecPlans;
    }

    public String getAll_class() {
        return this.getClass().getName();
    }

    public List<ApplyDiagnosisNto> getApplyDiagnosisNtos() {
        return applyDiagnosisNtos;
    }

    public void setApplyDiagnosisNtos(List<ApplyDiagnosisNto> applyDiagnosisNtos) {
        this.applyDiagnosisNtos = applyDiagnosisNtos;
    }

    @JsonProperty("@class")
    public String getClassName() {
        return getClass().getName();
    }
}