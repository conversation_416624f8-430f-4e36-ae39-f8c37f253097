package com.bjgoodwill.hip.ds.cis.apply.book.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "申请单预约")
public class ApplyBookTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -2951922900696323187L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "申请单ID")
    private String applyId;

    @Schema(description = "预约状态，开立为NEW，预约成功为ACTIVE")
    private CisStatusEnum appointsStatus;
    @Schema(description = "预约开始时间")
    private LocalDateTime appointsStartDate;
    @Schema(description = "预约结束时间")
    private String appointsEndDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public CisStatusEnum getAppointsStatus() {
        return appointsStatus;
    }

    public void setAppointsStatus(CisStatusEnum appointsStatus) {
        this.appointsStatus = appointsStatus;
    }

    public LocalDateTime getAppointsStartDate() {
        return appointsStartDate;
    }

    public void setAppointsStartDate(LocalDateTime appointsStartDate) {
        this.appointsStartDate = appointsStartDate;
    }

    public String getAppointsEndDate() {
        return appointsEndDate;
    }

    public void setAppointsEndDate(String appointsEndDate) {
        this.appointsEndDate = appointsEndDate;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ApplyBookTo other = (ApplyBookTo) obj;
        return Objects.equals(id, other.id);
    }
}