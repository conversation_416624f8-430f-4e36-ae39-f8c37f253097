package com.bjgoodwill.hip.ds.cis.apply.drug.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Schema(description = "药品明细")
public class CisDrugApplyDetailTo extends DetailTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -3524463504315755393L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "药品申请单标识")
    private String cisBaseDrugApplyId;
    @Schema(description = "序号")
    private Double sortNo;
    @Schema(description = "药品编码")
    private String drugCode;
    @Schema(description = "药品名称")
    private String drugName;
    @Schema(description = "每次剂量")
    private Double dosage;
    @Schema(description = "剂量单位 字典DosageUnit")
    private String dosageUnit;
    @Schema(description = "剂量单位 字典DosageUnit")
    private String dosageUnitName;
    @Schema(description = "包装总量")
    private Double packageNum;
    @Schema(description = "包装单位 MinUnit/PackageUnit")
    private String packageUnit;
    @Schema(description = "包装单位 MinUnit/PackageUnit")
    private String packageUnitName;
    @Schema(description = "领药科室")
    private String receiveOrg;
    @Schema(description = "领药科室名称")
    private String receiveOrgName;
    private SbadmWayEnum sbadmWay;
    //    private DrugInoutTypeEnum drugInoutType;
    @Schema(description = "是否皮试")
    private Boolean isSkin;
    @Schema(description = "皮试结果")
    private String skinResult;
    @Schema(description = "抗菌药使用说明:0-预防，1-治疗")
    private Integer antimicrobialsPurpose;
    @Schema(description = "特殊煎法：字典DecoctMethod")
    private String decoctMethodCode;
    @Schema(description = "特殊煎法：字典DecoctMethod")
    private String decoctMethodName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCisBaseDrugApplyId() {
        return cisBaseDrugApplyId;
    }

    public void setCisBaseDrugApplyId(String cisBaseDrugApplyId) {
        this.cisBaseDrugApplyId = cisBaseDrugApplyId;
    }

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    public String getDrugName() {
        return drugName;
    }

    public void setDrugName(String drugName) {
        this.drugName = drugName;
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    public Double getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    public String getPackageUnit() {
        return packageUnit;
    }

    public void setPackageUnit(String packageUnit) {
        this.packageUnit = packageUnit;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }

    public Boolean getIsSkin() {
        return isSkin;
    }

    public void setIsSkin(Boolean isSkin) {
        this.isSkin = isSkin;
    }

    public String getSkinResult() {
        return skinResult;
    }

    public void setSkinResult(String skinResult) {
        this.skinResult = skinResult;
    }

    public Integer getAntimicrobialsPurpose() {
        return antimicrobialsPurpose;
    }

    public void setAntimicrobialsPurpose(Integer antimicrobialsPurpose) {
        this.antimicrobialsPurpose = antimicrobialsPurpose;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    public String getPackageUnitName() {
        return packageUnitName;
    }

    public void setPackageUnitName(String packageUnitName) {
        this.packageUnitName = packageUnitName;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public String getDecoctMethodName() {
        return decoctMethodName;
    }

    public void setDecoctMethodName(String decoctMethodName) {
        this.decoctMethodName = decoctMethodName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisDrugApplyDetailTo other = (CisDrugApplyDetailTo) obj;
        return Objects.equals(id, other.id);
    }

    public String getDecoctMethodCode() {
        return decoctMethodCode;
    }

    public void setDecoctMethodCode(String decoctMethodCode) {
        this.decoctMethodCode = decoctMethodCode;
    }

    //    public DrugInoutTypeEnum getDrugInoutType() {
//        return drugInoutType;
//    }
//
//    public void setDrugInoutType(DrugInoutTypeEnum drugInoutType) {
//        this.drugInoutType = drugInoutType;
//    }
}