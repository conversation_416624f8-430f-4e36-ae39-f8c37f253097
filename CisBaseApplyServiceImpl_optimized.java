    public CisBaseApplyTo repairOpdFeeCharge(String id, List<CisApplyChargeNto> cisApplyChargeNtos){
        // 参数校验
        BusinessAssert.notBlank(id, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单ID不能为空");
        BusinessAssert.notEmpty(cisApplyChargeNtos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "费用信息不能为空");
        
        // 获取申请单并校验
        CisBaseApply cisBaseApply = CisBaseApply.getCisBaseApplyById(id).orElse(null);
        BusinessAssert.notNull(cisBaseApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单");
        BusinessAssert.isTrue(cisBaseApply.getStatusCode().equals(CisStatusEnum.ACTIVE), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0008, "申请单");

        CisBaseApplyTo to = CisBaseApplyAssembler.toTo(cisBaseApply, true);

        // 如果有一个执行单计费，判断申请单已收费
        boolean tolled = false;
        if (to != null && CollectionUtil.isNotEmpty(to.getCisOrderExecPlans())) {
            tolled = to.getCisOrderExecPlans().stream().anyMatch(p -> p != null && p.getIsCharge());
        }
        
        // 获取执行单费用信息
        List<CisOrderExecPlanChargeNto> orderExecPlanChargeNtos = getOrderExecPlanChargeNtos(id, cisApplyChargeNtos);
        
        if (CollectionUtil.isEmpty(orderExecPlanChargeNtos)) {
            return to; // 如果没有费用信息，直接返回
        }

        if (tolled) {
            // 已收费情况下的处理
            CisOrderExecPlanService execPlanService = SpringUtil.getBean(CisOrderExecPlanService.class);
            execPlanService.repairFeeCisOrderExecPlans(HIPIDUtil.getNextIdString(), orderExecPlanChargeNtos);
        } else {
            // 未收费情况下的处理
            // 补进申请单费用表
            CisApplyCharge cisApplyCharge = new CisApplyCharge();
            for (CisApplyChargeNto chargeNto : cisApplyChargeNtos) {
                if (chargeNto != null) {
                    cisApplyCharge.create(cisBaseApply.getId(), chargeNto, true);
                }
            }
            
            // 补进执行单费用表
            if (CollectionUtil.isNotEmpty(orderExecPlanChargeNtos)) {
                Map<String, List<CisOrderExecPlanChargeNto>> groupedCharges = 
                    orderExecPlanChargeNtos.stream()
                        .collect(Collectors.groupingBy(CisOrderExecPlanChargeNto::getExecuteOrgCode));
                
                for (Map.Entry<String, List<CisOrderExecPlanChargeNto>> entry : groupedCharges.entrySet()) {
                    String key = entry.getKey();
                    List<CisOrderExecPlanChargeNto> value = entry.getValue();
                    
                    if (CollectionUtil.isEmpty(value)) {
                        continue;
                    }
                    
                    CisOrderExecPlanTo cisOrderExecPlanTo = null;
                    if (to != null && CollectionUtil.isNotEmpty(to.getCisOrderExecPlans())) {
                        cisOrderExecPlanTo = to.getCisOrderExecPlans().stream()
                            .filter(p -> p != null && p.getExecOrgCode() != null && p.getExecOrgCode().equals(key))
                            .findFirst().orElse(null);
                    }
                    
                    if (cisOrderExecPlanTo == null) {
                        // 创建新的执行计划
                        createNewExecPlan(cisBaseApply, key, value);
                    } else {
                        // 更新现有执行计划
                        new CisOrderExecPlanCharge().create(to.getId(), value, true);
                    }
                }
            }
        }

        // 返回更新后的申请单信息
        return CisBaseApplyAssembler.toTo(CisBaseApply.getCisBaseApplyById(id).orElse(null), true);
    }
    
    /**
     * 创建新的执行计划
     * @param cisBaseApply 申请单
     * @param execOrgCode 执行机构代码
     * @param chargeList 费用列表
     */
    private void createNewExecPlan(CisBaseApply cisBaseApply, String execOrgCode, List<CisOrderExecPlanChargeNto> chargeList) {
        if (cisBaseApply == null || StringUtils.isEmpty(execOrgCode) || CollectionUtil.isEmpty(chargeList)) {
            return;
        }
        
        CisOrderExecPlanChargeNto firstCharge = chargeList.get(0);
        if (firstCharge == null) {
            return;
        }
        
        CisOrderExecPlanNto cisOrderExecPlanNto = new CisOrderExecPlanNto();
        cisOrderExecPlanNto.setExecOrgCode(execOrgCode);
        cisOrderExecPlanNto.setExecOrgName(firstCharge.getExecuteOrgName());
        cisOrderExecPlanNto.setMainPlanFlag(false);
        cisOrderExecPlanNto.setOrderId(cisBaseApply.getOrderID());
        cisOrderExecPlanNto.setOrderClass(cisBaseApply.getSystemType());
        cisOrderExecPlanNto.setServiceItemCode(cisBaseApply.getServiceItemCode());
        cisOrderExecPlanNto.setServiceItemName(cisBaseApply.getServiceItemName());
        cisOrderExecPlanNto.setVisitCode(cisBaseApply.getVisitCode());
        cisOrderExecPlanNto.setPatMiCode(cisBaseApply.getPatMiCode());
        cisOrderExecPlanNto.setOrgCode(cisBaseApply.getVisitOrgCode());
        cisOrderExecPlanNto.setOrgName(cisBaseApply.getVisitOrgName());
        cisOrderExecPlanNto.setDeptNurseCode(cisBaseApply.getDeptNurseCode());
        cisOrderExecPlanNto.setCisOrderExecPlanChargeTos(chargeList);
        
        CisOrderExecPlan cisOrderExecPlan = new CisOrderExecPlan();
        cisOrderExecPlan.create(cisBaseApply.getId(), cisOrderExecPlanNto, true);
    }
