package com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.rule.name}", url = "${hip.domains.rule.url}", path="/api/rule/confOrderLimit/cisConfOrderLimit", contextId = "com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.service.CisConfOrderLimitServiceFeign")
public interface CisConfOrderLimitServiceFeign extends CisConfOrderLimitService {

}