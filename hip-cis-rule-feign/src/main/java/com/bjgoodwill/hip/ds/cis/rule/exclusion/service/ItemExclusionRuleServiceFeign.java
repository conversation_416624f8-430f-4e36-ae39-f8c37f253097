package com.bjgoodwill.hip.ds.cis.rule.exclusion.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.rule.name}", url = "${hip.domains.rule.url}", path="/api/rule/exclusion/itemExclusionRule", contextId = "com.bjgoodwill.hip.ds.cis.rule.exclusion.service.ItemExclusionRuleServiceFeign")
public interface ItemExclusionRuleServiceFeign extends ItemExclusionRuleService {

}