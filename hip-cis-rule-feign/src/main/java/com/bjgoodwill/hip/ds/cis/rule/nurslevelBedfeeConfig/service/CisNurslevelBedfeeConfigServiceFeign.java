package com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.rule.name}", url = "${hip.domains.rule.url}", path="/api/rule/nurslevelBedfeeConfig/cisNurslevelBedfeeConfig", contextId = "com.bjgoodwill.hip.ds.cis.rule.nurslevelBedfeeConfig.service.CisNurslevelBedfeeConfigServiceFeign")
public interface CisNurslevelBedfeeConfigServiceFeign extends CisNurslevelBedfeeConfigService {

}