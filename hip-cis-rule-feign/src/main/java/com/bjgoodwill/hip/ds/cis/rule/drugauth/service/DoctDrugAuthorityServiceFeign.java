package com.bjgoodwill.hip.ds.cis.rule.drugauth.service;

import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${hip.domains.rule.name}", url = "${hip.domains.rule.url}", path = "/api/rule/drugauth/doctDrugAuthority", contextId = "com.bjgoodwill.hip.ds.cis.rule.drugauth.service.DoctDrugAuthorityServiceFeign")
public interface DoctDrugAuthorityServiceFeign extends DoctDrugAuthorityService {


}