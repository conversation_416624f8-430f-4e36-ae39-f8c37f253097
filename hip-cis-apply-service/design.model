{"replaceJavaCode": false, "packageName": "com.bjgoodwill.hip.ds.cis.apply", "shortName": "apply", "entities": [{"inheritanceRole": "SUPERCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": null, "discriminatorColumn": "orderType", "discriminatorValue": null, "id": "749cb2f5-92af-43e2-8de2-0870896e09d7", "name": "CisBaseApply", "table": "cis_base_apply", "comment": "抽象父类", "subModule": "apply", "properties": [{"id": "3cc205cd-b205-42bd-be7a-c5e580a3bf80", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "2af5dbe0-b526-4115-9a33-e10380283a7b", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "patMiCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "pat_mi_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "主索引", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "b68741dd-5e73-4525-9f5d-539df37423e6", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "visitCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "visit_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "流水号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "ef5e06aa-c8a5-4973-b3a3-4c7e38984b6a", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "serviceItemCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "service_item_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "cce9226c-3f28-4023-b426-56053ece66df", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "serviceItemName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "service_item_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "e6f1b62c-e747-408d-82ae-9bcfa269c33f", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "isCanPriorityFlag", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "is_can_priority_flag", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是否允许加急标识,1允许0不允许", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "1a8d99c4-abc8-4297-a93b-10a7019f7c8d", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "statusCode", "javaType": "Enum", "enumName": "CisStatusEnum", "idGenerationType": null, "column": "status_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "状态", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": true}, {"id": "98aacf08-966a-407c-a649-d7b97eeafb52", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "CreatedStaff", "name": "createdStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff", "length": 64, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "e39d3954-5891-4f13-a81f-caec5e9f76ec", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "f719f9b3-32fa-46d3-a09e-4ac4fa72b228", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "UpdatedStaff", "name": "updatedStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "updated_staff", "length": 64, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "329d7c1e-e690-4237-a89c-a55c57808f88", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "UpdatedDate", "name": "updatedDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "updated_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "b95b4ce3-b15e-4341-8a87-5d90d68f239c", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "executor<PERSON><PERSON><PERSON>", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "executor_staff", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行人", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "c959d86e-706b-445c-b535-5cdb030e766d", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "executorDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "executor_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "25fb4de1-4e49-419e-baac-e89a3a0e15cb", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "executorHosptialCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "executor_hosptial_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行医院编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "5e7a7dd6-c298-4534-96f2-0ece276f6194", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "executorOrgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "executor_org_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行科室编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "884c182b-768d-4bbc-91d1-de46f022a2b3", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "medrecordAndExamabstractId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "medrecord_and_examabstract_id", "length": 50, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "病历及查体摘要标识", "associationType": "ASSOCIATION", "associatedEntityId": "b43ca141-79c4-4016-a3e6-88871a7589d3", "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "084abc32-7c1d-48d6-8065-3e2496397645", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "visitType", "javaType": "Enum", "enumName": "VisitTypeEnum", "idGenerationType": null, "column": "visit_type", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "IPD住院，OPD门诊", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "544786a5-d40a-43e3-81f3-d22ca4923e18", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "deptNurseCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "dept_nurse_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "护理组编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "e4710fc4-d916-4a81-b159-3f3e18c03ec0", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "deptNurseName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "dept_nurse_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "护理组名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "69b0dfaf-453b-463b-b038-1ea787ed4dfe", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "orderID", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "order_id", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱ID", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "b177e417-c8ff-43af-8765-e1908850cf40", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "hospitalCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "hospital_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "申请医院编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "fe78a3f3-dfbe-4719-afb8-46104378b253", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "prescriptionID", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "prescription_id", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "处方号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "e41da8de-8c7a-4ab2-8acf-280ebb5ac96f", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "isPrint", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_print", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "打印标识：1己打印，0未打印；默认0", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "2d8eee8f-c1ee-4acd-92ca-6ad3e953f2a2", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "printStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "print_staff", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "打印人", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "487e6369-39c4-4ed9-92af-2b16528d6b75", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "printDate", "javaType": "LocalDate", "enumName": null, "idGenerationType": null, "column": "print_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "打印时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "4a992681-8cab-48b7-9338-9c1a95c23abd", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "reMark", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "re_mark", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "备注", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "e00e9b88-443c-436a-9bcb-370fb2f8cabf", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "icuExecuteDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "icu_execute_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "重症患者执行时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "38ee19f1-bced-4026-9f12-89a3b1a59c06", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "isChargeManager", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_charge_manager", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是否跨院申请项目", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "1421b206-707e-4144-bdd1-a46cbf3f2641", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Version", "name": "version", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "version", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "版本", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": false}, {"id": "91eec135-bbd3-4af2-9be1-033f90e5ef7b", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "createOrgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "create_org_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "开方人所在科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "8fce1425-c5dc-40af-af5c-c57603cd8052", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "sortNo", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "sort_no", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "排序序号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "a0c980ce-0ac3-4242-a54e-a32cb586d2a3", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "type": "Basic", "name": "isBaby", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_baby", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": "05", "id": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "name": "CisDgimgApply", "table": null, "comment": "检查申请单", "subModule": null, "properties": [{"id": "0a87882c-9e32-4119-8a4a-889d40e4c798", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "type": "Basic", "name": "precautions", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "precautions", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "检查注意事项", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "0e7cabf4-5ff6-4f26-80b5-96c4ea51781c", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "type": "Basic", "name": "medrecordAndExamabstract", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "medrecord_and_examabstract", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "病历及查体摘要", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "a857742b-3b34-4d2d-a143-97f11b856b3e", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "type": "Basic", "name": "clinicalDiagnosis", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "clinical_diagnosis", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "临床诊断", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "5247fc17-2e67-4175-b9fa-06f2863341cc", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "type": "Basic", "name": "physiqueAndExam", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "physique_and_exam", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "体格及其他检查", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "ea6d7111-cb4c-49a5-b33b-2a409fc88c05", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "type": "Basic", "name": "dgimgClass", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "dgimg_class", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "分类", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "a6e35b5c-f20f-4ee7-b042-5bbb357ee56b", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "type": "Basic", "name": "dgimgSubClass", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "dgimg_sub_class", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "子分类", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "db129e12-90c5-4c1b-bb69-c65c95852685", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "type": "Basic", "name": "auxiliaryInspection", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "auxiliary_inspection", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "相关辅检", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "600cd7fe-5287-4836-bc6e-d1ca9ba6a060", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "type": "Basic", "name": "checkPurpose", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "check_purpose", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "检查目的", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "990ec216-cf68-4820-a3e4-a7434e463f96", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "type": "Basic", "name": "applyBookId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "apply_book_id", "length": 50, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "申请单预约标识", "associationType": "ASSOCIATION", "associatedEntityId": "0b98ce45-b435-498c-8762-cb6771d74813", "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "737ae556-158a-4494-8c09-13cdd2017807", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "type": "Basic", "name": "reportPdfUrl", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "report_pdf_url", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "报告pdf地址", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "8a460f39-72e2-459f-ab0d-2fa066bb146e", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "type": "Basic", "name": "previousPathologicalExamin", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "previous_pathological_examin", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "既往病理检查结果", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "0b98ce45-b435-498c-8762-cb6771d74813", "name": "ApplyBook", "table": "apply_book", "comment": "申请单预约", "subModule": "book", "properties": [{"id": "1774d40e-7852-47f5-b168-39529a9848fc", "entityId": "0b98ce45-b435-498c-8762-cb6771d74813", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "SNOWFLAKE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "9ab84625-8342-487c-9af3-a10a5880cdd2", "entityId": "0b98ce45-b435-498c-8762-cb6771d74813", "type": "Basic", "name": "appoints<PERSON><PERSON><PERSON>", "javaType": "Enum", "enumName": "CisStatusEnum", "idGenerationType": null, "column": "appoints_status", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "预约状态，开立为NEW，预约成功为ACTIVE", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": true}, {"id": "21650cb0-4aea-4d21-89ad-c9e9cc1059e5", "entityId": "0b98ce45-b435-498c-8762-cb6771d74813", "type": "Basic", "name": "appointsS<PERSON>tDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "appoints_start_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "预约开始时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "70af869a-15e3-4aa9-a92a-fca949d125a6", "entityId": "0b98ce45-b435-498c-8762-cb6771d74813", "type": "Basic", "name": "appointsEndDate", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "appoints_end_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "预约结束时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "b43ca141-79c4-4016-a3e6-88871a7589d3", "name": "MedrecordAndExamabstract", "table": "medrecord_and_examabstract", "comment": "病历及查体摘要", "subModule": "medrecord", "properties": [{"id": "0de5e1bd-917e-4b0b-8195-0e9ca9aeeae2", "entityId": "b43ca141-79c4-4016-a3e6-88871a7589d3", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "SNOWFLAKE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "cf985432-6112-40aa-a1da-08a94f25cb2e", "entityId": "b43ca141-79c4-4016-a3e6-88871a7589d3", "type": "Basic", "name": "content", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "content", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "内容", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "676ed6f0-8749-4554-9a54-2eba62a203f9", "entityId": "b43ca141-79c4-4016-a3e6-88871a7589d3", "type": "Basic", "name": "isAllergicHistory", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_allergic_history", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "过敏史", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "96b4029f-44a9-44a3-890c-3a38efca95d8", "entityId": "b43ca141-79c4-4016-a3e6-88871a7589d3", "type": "Basic", "name": "isInfectious", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_infectious", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "传染病标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "c5d1b170-fa41-4b44-a8f0-a9a981c52d13", "entityId": "b43ca141-79c4-4016-a3e6-88871a7589d3", "type": "Basic", "name": "isOccupational", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_occupational", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "职业病史标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "6286009d-bb4b-453e-b079-87ef50cbfc01", "entityId": "b43ca141-79c4-4016-a3e6-88871a7589d3", "type": "Basic", "name": "pastHistoryContext", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "past_history_context", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "既往史", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "e555252c-5425-48c1-8824-fc743ebf871a", "entityId": "b43ca141-79c4-4016-a3e6-88871a7589d3", "type": "Basic", "name": "lastMenstrualPeriod", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "last_menstrual_period", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "末次月经时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "44a47882-b327-461c-9fd3-33a8eace75c9", "entityId": "b43ca141-79c4-4016-a3e6-88871a7589d3", "type": "Basic", "name": "transfusionHistory", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "transfusion_history", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "输血史", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": "06", "id": "263e3480-7f0c-4fbd-b856-27eb69d38224", "name": "CisSpcobsApply", "table": null, "comment": "检验类申请单", "subModule": null, "properties": [{"id": "32c59604-8333-498d-a767-b257f713e8de", "entityId": "263e3480-7f0c-4fbd-b856-27eb69d38224", "type": "Basic", "name": "remark", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "remark", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "检验注意事项", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "fbb8e611-9203-4033-ac2b-ac33360c600b", "entityId": "263e3480-7f0c-4fbd-b856-27eb69d38224", "type": "Basic", "name": "spcobsClass", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "spcobs_class", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "检验分类", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "85e93f7f-25dd-4e21-a1b4-2b40cda826e7", "entityId": "263e3480-7f0c-4fbd-b856-27eb69d38224", "type": "Basic", "name": "printBarCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "print_bar_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "条码号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "d6701dde-fdc6-4d2e-9235-5b989d53cb1d", "entityId": "263e3480-7f0c-4fbd-b856-27eb69d38224", "type": "Basic", "name": "barCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "bar_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "lis条码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "4ca6a3c1-e236-40cb-8ab4-9437c63c51a2", "entityId": "263e3480-7f0c-4fbd-b856-27eb69d38224", "type": "Basic", "name": "reportPdfUrl", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "report_pdf_url", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "报告pdf地址", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": "00", "id": "3cf24a04-bcfb-4273-9bae-eba0b9258fe4", "name": "CisCommon", "table": null, "comment": "通用申请单", "subModule": null, "properties": [{"id": "047725e3-6d2e-48fb-8fe4-c93fae4fae71", "entityId": "3cf24a04-bcfb-4273-9bae-eba0b9258fe4", "type": "Basic", "name": "frequency", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "frequency", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "频次", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "0ac6b035-c2d6-40bc-a92e-5431e797de16", "entityId": "3cf24a04-bcfb-4273-9bae-eba0b9258fe4", "type": "Basic", "name": "dosage", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "dosage", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "每次剂量", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "ce4148d5-7f03-4438-844c-8dac68bde51b", "entityId": "3cf24a04-bcfb-4273-9bae-eba0b9258fe4", "type": "Basic", "name": "dosageUnit", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "dosage_unit", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "剂量单位", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "896b71d5-ff26-4134-a9dc-1c74aeb1126b", "entityId": "3cf24a04-bcfb-4273-9bae-eba0b9258fe4", "type": "Basic", "name": "packageNum", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "package_num", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "包装总量", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "aebeeaa2-40fb-448c-a8c6-e54a4e1bf3c0", "entityId": "3cf24a04-bcfb-4273-9bae-eba0b9258fe4", "type": "Basic", "name": "packageUnit", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "package_unit", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "包装单位 MinUnit/PackageUnit", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "ee3ca3b8-01c3-45cb-9523-b38697183fa3", "entityId": "3cf24a04-bcfb-4273-9bae-eba0b9258fe4", "type": "Basic", "name": "continueTime", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "continue_time", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "每次持续时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "1a252bbf-2c7c-4e73-87fb-5f8c24cd55cd", "entityId": "3cf24a04-bcfb-4273-9bae-eba0b9258fe4", "type": "Basic", "name": "continueUnit", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "continue_unit", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "每次持续单位 字典TimeUnit", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "657f17f2-ce33-4ef5-8eb7-8a5b617c6f23", "entityId": "3cf24a04-bcfb-4273-9bae-eba0b9258fe4", "type": "Basic", "name": "systemType", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "system_type", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱类型", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "SUB_SUPERCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": null, "id": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "name": "CisBaseDrugApply", "table": null, "comment": "药品申请单", "subModule": null, "properties": [{"id": "9bb13ccd-857b-4c88-9dc0-76f86b62211b", "entityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "type": "Basic", "name": "usage", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "usage", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "用法", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "e22f3a30-c1fc-4b9e-8a45-61d13b7ddcb2", "entityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "type": "Basic", "name": "usageName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "usage_name", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "用法名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "f19d8960-180b-42d7-b4a2-6c5751b8f89f", "entityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "type": "Basic", "name": "frequency", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "frequency", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "频次", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "4b28dd3a-e142-41da-924e-104438a17bbb", "entityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "type": "Basic", "name": "frequencyName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "frequency_name", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "频次名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "e2a3d1a2-b589-45e3-926e-d2c77a9d1650", "entityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "type": "Basic", "name": "treatmentCourse", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "treatment_course", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "疗程", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "ba87f8e3-684a-4d26-a296-87edf8c97f14", "entityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "type": "Basic", "name": "treatmentCourseUnit", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "treatment_course_unit", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "疗程单位", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "409be2e9-4d73-4755-a8e1-0d2272cf03bb", "entityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "type": "Basic", "name": "prescriptionType", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "prescription_type", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "处方类型", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "705a83ae-00ba-4c31-8661-feac8676d12c", "entityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "type": "Basic", "name": "receiveOrg", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "receive_org", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "领药科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "discriminatorColumn": null, "discriminatorValue": "01", "id": "64653efb-9221-41c7-8711-af7b0763101a", "name": "CisEDrugApply", "table": null, "comment": "成药申请单", "subModule": null, "properties": [{"id": "5808e908-5f1b-4c9f-bf18-52385af8ebaa", "entityId": "64653efb-9221-41c7-8711-af7b0763101a", "type": "Basic", "name": "dripSpeed", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "drip_speed", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "滴速", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "facd27dc-e7ab-4c41-a385-bab812056cc7", "entityId": "64653efb-9221-41c7-8711-af7b0763101a", "type": "Basic", "name": "dripSpeedUnit", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "drip_speed_unit", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "滴速单位", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "75190950-d30e-43ac-ae01-ba2a0f69540a", "entityId": "64653efb-9221-41c7-8711-af7b0763101a", "type": "Basic", "name": "isSkin", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_skin", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是否皮试", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "ce6e3cab-dd33-4685-ab28-f014e5c09f31", "entityId": "64653efb-9221-41c7-8711-af7b0763101a", "type": "Basic", "name": "skinResult", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "skin_result", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "皮试结果", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "a1c3fbc6-8f81-424a-904c-9c24d37121c5", "entityId": "64653efb-9221-41c7-8711-af7b0763101a", "type": "Basic", "name": "antimicrobialsPurpose", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "antimicrobials_purpose", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "抗菌药使用说明:0-预防，1-治疗", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "895df1c4-d76c-48f5-ae8e-69fa9cac3ee2", "entityId": "64653efb-9221-41c7-8711-af7b0763101a", "type": "Basic", "name": "prescriptionType", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "prescription_type", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "处方类型", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "discriminatorColumn": null, "discriminatorValue": "03", "id": "aaa1a630-d5d4-4842-aa38-2cac9b10ef16", "name": "CisCDrugApply", "table": null, "comment": "中草药申请单", "subModule": null, "properties": [{"id": "4e6e79c1-7848-47ef-9ec4-a56660c9ae4f", "entityId": "aaa1a630-d5d4-4842-aa38-2cac9b10ef16", "type": "Basic", "name": "doseNum", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "dose_num", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "付数", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "df4c7d39-6aec-44bf-8f60-a330b029bcde", "entityId": "aaa1a630-d5d4-4842-aa38-2cac9b10ef16", "type": "Basic", "name": "decoction", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "decoction", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "草药煎法;0自煎,1代煎", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "8dce1677-a9d7-44ae-b599-938c315f801e", "entityId": "aaa1a630-d5d4-4842-aa38-2cac9b10ef16", "type": "Basic", "name": "cdrugPackNum", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "cdrug_pack_num", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "草药一付包数", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "14ba2eaf-9158-4b7d-8419-eeb55065312e", "entityId": "aaa1a630-d5d4-4842-aa38-2cac9b10ef16", "type": "Basic", "name": "cdrugPackMl", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "cdrug_pack_ml", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "草药每包毫升数", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": "07", "id": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "name": "CisOperationApply", "table": null, "comment": "手术申请", "subModule": null, "properties": [{"id": "5d94f66c-3569-41b2-9737-ee26ed130e98", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "bloodType", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "blood_type", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "血型", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "422be308-fef9-4289-a186-672c51aa2096", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "bloodTypeRh", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "blood_type_rh", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "RH血型", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "e030b650-05cf-43f4-8eb9-daf721d7d648", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "operationType", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "operation_type", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "手术分类", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "3857b82f-b444-49d9-8fa5-92a58b4a7970", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "humanOrgans", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "human_organs", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "部位", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "b782f898-7ef4-4afb-bc58-6a06a72e562a", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "decubitus", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "decubitus", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "体位", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "1d993a7e-44e6-4fca-a2e4-30bdb2854df7", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "isOlation", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_olation", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是否隔离", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "56609db2-4fa1-4f5c-a17c-ba8dd3ae462a", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "preoperativeDiagnosis1", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "preoperative_diagnosis_1", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "术前诊断", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "7a54215d-829b-4f06-ae90-38bbadaf1609", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "preoperativeDiagnosis2", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "preoperative_diagnosis_2", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "89cc3a83-91fd-4bb4-9169-1d79a74ff619", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "preoperativeDiagnosis3", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "preoperative_diagnosis_3", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "9e7ad541-197e-4592-b5f3-b41819a165a4", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "operationName1", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "operation_name_1", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "拟手术名称1", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "e53e8760-621c-4bea-aaf8-934291a61bd3", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "operationName2", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "operation_name_2", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "7a906812-47a5-4533-b898-4e62f16c0a00", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "operationName3", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "operation_name_3", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "648fa657-34e2-4554-ac4e-e5e6a7a0ab0d", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "anaesthesiaType", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "anaesthesia_type", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "麻醉类型", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "c04ff759-0d9e-4319-b82a-525c1e839d09", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "anaesthesiaMode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "anaesthesia_mode", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "麻醉方式", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "d14e990c-9d32-4904-ba6c-8c2e6f4a0aa5", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "operationSpecialAttr", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "operation_special_attr", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": " 特殊手术属性", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "56188d75-a2b2-4cae-8d98-d86a4b9cab9d", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "operationDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "operation_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "拟手术日期", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "79a99f82-9079-4004-9a5e-2dbd59c507c3", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "operationTime", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "operation_time", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "拟预计用时", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "4cf8e6cc-5952-42cc-bb0d-e84fd742f499", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "operationLevel", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "operation_level", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "手术级别", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "09029974-896c-463f-b219-fbcc12b75cbd", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "incisionType", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "incision_type", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "切口类型", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "07abd9ab-5c4f-4da1-9369-5a3b817721e9", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "incisionLevel", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "incision_level", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "切口等级 字典WoundGrade", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "2f7e9d77-5fb1-48cf-b8f8-d5b4f09b9a7f", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "mergeFlag", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "merge_flag", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "合并手术", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "bd3fd718-74a8-42dd-a7dc-d49a6aae3917", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "apprOperTypc", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "appr_oper_typc", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "手术审批类型：1常规手术，2急诊手术；默认1", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "b6fb71de-73cc-4a07-a13e-749716ef4b4a", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "operationDoctor", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "operation_doctor", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "手术医生", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "6f74b196-d416-46de-8379-f7d47616747a", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "operationDoctorOrg", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "operation_doctor_org", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "手术医生科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "aafa4a3e-39bb-4c1f-842f-7669bf594237", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "operation", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "operation", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "基本操作", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "78eb317c-8bbe-448b-bf23-36f884959700", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "approach", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "approach", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "入路", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "9903f57d-fdaf-4b74-ad0f-009501140ce0", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "type": "Basic", "name": "instrument", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "instrument", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "辅助器械", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": "15", "id": "58a3172d-a857-41e2-aa76-b315333616bf", "name": "CisTreatmentApply", "table": null, "comment": "治疗申请单", "subModule": null, "properties": [{"id": "0f100aab-c1e3-46d2-b261-3b084d69daf2", "entityId": "58a3172d-a857-41e2-aa76-b315333616bf", "type": "Basic", "name": "frequency", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "frequency", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "频次", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": "17", "id": "fa852c29-fdf0-4e1b-910d-f2f58726f75c", "name": "CisChangeDeptApply", "table": null, "comment": "转科申请单", "subModule": null, "properties": [{"id": "22a639cb-5bf0-43a7-96fd-a8a84988e73c", "entityId": "fa852c29-fdf0-4e1b-910d-f2f58726f75c", "type": "Basic", "name": "outOrgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "out_org_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "转出科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "f506c723-bd85-4d36-be11-76c9ca3bc105", "entityId": "fa852c29-fdf0-4e1b-910d-f2f58726f75c", "type": "Basic", "name": "outDeptNurseCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "out_dept_nurse_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "转出护理组", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "e3f316b5-f99e-442a-a7d3-c83c8b91b2ad", "entityId": "fa852c29-fdf0-4e1b-910d-f2f58726f75c", "type": "Basic", "name": "outHospitalCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "out_hospital_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "转出医院编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "e09eef4f-9005-4d80-a05f-ed004359b6c1", "entityId": "fa852c29-fdf0-4e1b-910d-f2f58726f75c", "type": "Basic", "name": "inOrgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "in_org_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "转入科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "5c2a6b14-f975-41e4-b649-81b120f5c129", "entityId": "fa852c29-fdf0-4e1b-910d-f2f58726f75c", "type": "Basic", "name": "inDeptNurseCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "in_dept_nurse_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "转入护理组", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "cda4b11e-2b04-4c27-a130-905ce7ea2c3e", "entityId": "fa852c29-fdf0-4e1b-910d-f2f58726f75c", "type": "Basic", "name": "inHospitalCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "in_hospital_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "转入医院编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": "16", "id": "84a0fcdd-438b-4311-9f43-c02d4cd24a04", "name": "CisOutHospitalApply", "table": null, "comment": "出院申请单", "subModule": null, "properties": [{"id": "0e9e32d8-3a1a-4693-835c-dfba697ac1d8", "entityId": "84a0fcdd-438b-4311-9f43-c02d4cd24a04", "type": "Basic", "name": "outDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "out_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "出院时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "df0808f2-4cbd-4bd0-8a94-d3d2a50752af", "entityId": "84a0fcdd-438b-4311-9f43-c02d4cd24a04", "type": "Basic", "name": "dischargeDisposition", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "discharge_disposition", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "出院方式 字典DischargeWay", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": "23", "id": "93c9c2b0-3527-4612-b3cd-4702995c760f", "name": "CisPalgApply", "table": null, "comment": "病理申请单", "subModule": null, "properties": [{"id": "55ebc51c-cd92-4aaa-aab5-8c05e7dabf6a", "entityId": "93c9c2b0-3527-4612-b3cd-4702995c760f", "type": "Basic", "name": "speciman", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "speciman", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "标本", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": "18", "id": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "name": "CisConsultationApply", "table": null, "comment": "会诊申请单", "subModule": null, "properties": [{"id": "83e258d6-9304-453e-9351-37621315355f", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "cnsltOrg", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cnslt_org", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "会诊科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "7610b0ae-1e2f-405f-9187-47538a37fc15", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "diagnosisCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "diagnosis_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "诊断编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "93f648fb-40f9-4585-93df-edf9864842c8", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "cnsltHospitalCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cnslt_hospital_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "会诊医院编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "287d08a0-d15c-4311-ab14-9ff18f69e296", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "<PERSON><PERSON><PERSON><PERSON>", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "pat_remark", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "患者病情摘要", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "23c42e0c-3fd2-47a5-95a3-e370d53b7d76", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "reason", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "reason", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "会诊理由及目的", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "3e157c93-a023-497c-be4a-fcaecdf0bc71", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "cnsltOpinion", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cnslt_opinion", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "会诊意见 ", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "e06ae5a4-e441-4557-8d75-ffa244c1d7fe", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "cnsltDoctor", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cnslt_doctor", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "会诊医生", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "0ff17efb-def4-4f05-9205-8291c18110e6", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "cnsltDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "cnslt_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "会诊时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "5852d9da-b732-47ec-a603-793e354fbd4b", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "cnsltConfirmDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "cnslt_confirm_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "会诊确认时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "ab7ef60c-b15f-41e4-8880-6b896c943e82", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "cnsltCompletionTime", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "cnslt_completion_time", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "会诊完成时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "9b12b6a8-a756-4f93-a88e-e85b096e1c5b", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "createStaffTel", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "create_staff_tel", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "申请医生电话", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "8a4c6a66-33bb-46b1-9bfb-94e7adef8f87", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "type": "Basic", "name": "execHospitalCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "exec_hospital_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行医院编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": "12", "id": "cf052941-240c-4dd3-880e-497ab57179ba", "name": "CisPreparationBloodApply", "table": null, "comment": "备血申请单", "subModule": null, "properties": [{"id": "521f3c34-63e3-4476-9fd2-4e1dbf917f90", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "preInfusionDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "pre_infusion_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "预定输注时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "cc488467-0034-40c0-b564-15d720ebcc56", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "clinicalDiagnosis", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "clinical_diagnosis", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "临床诊断", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "fdd5111f-855d-4bfb-be0b-f13cc6360378", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "transfusionTrigger", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "transfusion_trigger", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "输血指征", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "b1bce71a-74a3-44ba-b4ec-3870a240a637", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "transfusionDemand", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "transfusion_demand", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "输血需求 1-正常，2-紧急，3-大量，4-特殊(TransfusionDemand)", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "4b904295-6a25-4c74-ae6a-01bf04ee9343", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "transfusionPurpose", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "transfusion_purpose", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "输血目的", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "e41643ad-6565-4db8-b549-df0ecf3e96c0", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "transfusionWay", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "transfusion_way", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "输血方式 1-异体，2-自体，", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "492cdb0f-4c5f-4bb8-91bc-3058f4857b7a", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "transfusionDetection", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "transfusion_detection", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "输血检测项目 0-未送检，1-已送检", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "0af61e12-48c4-4835-b321-6ac7307c6027", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "drawBloodUser", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "draw_blood_user", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "采血人", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "d619f2db-e83a-45a0-a3b4-2f64384d7822", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "bloodType", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "blood_type", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "血型", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "f7a3aa9f-6f04-4033-9704-ef4f7f37ba89", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "rh_d", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "rh____d", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "0-阴性，1-阳性", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "6339ab82-9360-471a-bcac-590fbf645103", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "erythrocyte", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "erythrocyte", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "红细胞RBC", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "13d52738-bf81-440e-9326-2a6bd013b0ae", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "leukocyte", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "leukocyte", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "白细胞 WBC 单位x10^9/L", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "d9f2041d-941d-4d83-828e-ff00ba3f5d5b", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "hemoglobin", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "hemoglobin", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "血红蛋白 HB", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "ab1e8ee6-bda8-473e-b087-6a97c6d5e79f", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "thrombocyte", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "thrombocyte", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "血小板 PL", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "2fadf2e0-2b1c-43f5-a660-7e3ebe6ac6e5", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "hematokrit", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "hematokrit", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "红细胞压积HCT", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "20ec3aad-aa1a-41c0-be94-d7e844e5625e", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "glutamic_Pyruvic", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "glutamic___pyruvic", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "谷丙转氨酶ALT", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "b1ac71db-8ed6-4525-898c-ee93394b4a62", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "aptt", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "aptt", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "APTT单位秒 参考值 28.0~43.5", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "7eba673e-627e-444f-bd52-705ecda55e2f", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "fibd", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "fibd", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "FIBD单位g/L 参考值 2.00~4.00", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "814e3e7a-7faf-4434-a32d-e0927cbc37db", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "pt", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "pt", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "PT单位秒 参考值11.0~15.0", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "b1ebddcc-ebfa-4f4d-8b9d-5b3aed8de1f6", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "hbsAg", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "hbs_ag", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "HbsAg乙肝表面抗原 0-阴性，1-阳性", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "0944ebad-47a3-4e0c-8cae-b8152833a7cc", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "hbsAb", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "hbs_ab", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "HbsAb乙型肝炎表面抗体 0-阴性，1-阳性", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "addeca1b-6757-4811-be30-7bcc8bd9fd1d", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "hbeAg", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "hbe_ag", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "HbeAg乙型肝炎E抗原 0-阴性，1-阳性", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "221443f6-f7d7-4aa9-9405-6c54354cbe4f", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "hbeAb", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "hbe_ab", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "HbeAb乙型肝炎E抗体 0-阴性，1-阳性", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "9b608419-7d5f-4716-a8f3-f7d6cf6e6ad1", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "hbcAb", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "hbc_ab", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "HbcAb乙肝核心抗体 0-阴性，1-阳性", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "5c190072-bf03-4b18-bba0-c8aacbffa2a4", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "hcvAb", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "hcv_ab", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "HCVAb丙肝病毒抗体 0-阴性，1-阳性", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "e3e72de9-a788-4fa2-97a2-993a8a75f23a", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "tpAb", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "tp_ab", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "TP-Ab梅毒 0-阴性，1-阳性", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "8e322abc-df9e-46e2-bab4-17ca6eef490d", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "hivAb", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "hiv_ab", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "HIV(1+2)Ab艾滋病", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "3b50ebdc-c6bc-4f68-ae80-02f63aa746b1", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "indicate1", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "indicate_1", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "注明1 ", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "e6d5279b-e1be-4578-9281-83612293e8cb", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "indicate2", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "indicate_2", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "注明2", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "e60f4724-e801-43b2-96f8-cef34acbacf2", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "specimenRetentionDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "specimen_retention_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "标本留取时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "5a1f1977-1451-4d25-bb8d-ef3ee69783c7", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "applyUser", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "apply_user", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "申请医生签字", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "9177ec53-c7eb-4934-9b14-50e6e36b9f27", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "applyDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "apply_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "申请时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "10155c03-da38-471e-ae56-967fa918e0ae", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "appleType", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "apple_type", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "申请确认标识：0申请，1通过", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "1f224333-e127-492a-babb-d04889d94236", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "type": "Basic", "name": "preBlood", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "pre_blood", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "此患者仅备血未输血 0-否，1-是", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "discriminatorColumn": null, "discriminatorValue": "08", "id": "80e1041c-ebd4-4718-9474-9347925795d3", "name": "CisBloodApply", "table": null, "comment": "输血申请单", "subModule": null, "properties": [{"id": "ede4bab0-e784-47d6-ac8b-0c34fe2340fd", "entityId": "80e1041c-ebd4-4718-9474-9347925795d3", "type": "Basic", "name": "cisBloodApplyId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_blood_apply_id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "输血申请单标识", "associationType": "ASSOCIATION", "associatedEntityId": "80e1041c-ebd4-4718-9474-9347925795d3", "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "1c87f8ae-25e4-4f6e-9f43-458cdd7fb0c6", "name": "CisBloodComponent", "table": "cis_blood_component", "comment": "血液成分从表", "subModule": "blood", "properties": [{"id": "15bfbd76-2a5c-40ee-ac04-86fa64386fb3", "entityId": "1c87f8ae-25e4-4f6e-9f43-458cdd7fb0c6", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "fa9e2211-0639-427d-9d6a-25b19b26c8dd", "entityId": "1c87f8ae-25e4-4f6e-9f43-458cdd7fb0c6", "type": "Basic", "name": "cisBloodApplyId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_blood_apply_id", "length": 50, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "输血申请单标识", "associationType": "ASSOCIATION", "associatedEntityId": "80e1041c-ebd4-4718-9474-9347925795d3", "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "2f7b9fab-5578-4cc7-b953-4f19f2cbb9ac", "entityId": "1c87f8ae-25e4-4f6e-9f43-458cdd7fb0c6", "type": "Basic", "name": "bloodComponent", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "blood_component", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "申请输注血液成分", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "aacffae6-fbf4-40d1-8236-c57656c4a454", "entityId": "1c87f8ae-25e4-4f6e-9f43-458cdd7fb0c6", "type": "Basic", "name": "bloodQuantity", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "blood_quantity", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "申请输注血量", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "46f31b6b-8b4e-4cb8-9689-74f98851af35", "entityId": "1c87f8ae-25e4-4f6e-9f43-458cdd7fb0c6", "type": "Basic", "name": "bloodQuantityUnit", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "blood_quantity_unit", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "输注血量单位 默认ml", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "0c4c7e0b-0765-4ba2-b9e8-491fcfa3e541", "entityId": "1c87f8ae-25e4-4f6e-9f43-458cdd7fb0c6", "type": "Basic", "name": "orderResultFlag", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "order_result_flag", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱处理标记:1己处理，0未处理", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "db784e10-a2e4-4b66-9e01-c149b80fa41b", "name": "CisDgimgApplyDetail", "table": "cis_dgimg_apply_detail", "comment": "检查申请单明细", "subModule": "dgimg", "properties": [{"id": "a8f6b681-1f98-4178-9e44-404e6442e4f5", "entityId": "db784e10-a2e4-4b66-9e01-c149b80fa41b", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "267c397a-7270-4b1e-af7f-039f26f585b4", "entityId": "db784e10-a2e4-4b66-9e01-c149b80fa41b", "type": "Basic", "name": "cisDgimgApplyId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_dgimg_apply_id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "检查申请单标识", "associationType": "ASSOCIATION", "associatedEntityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "name": "CisApplyCharge", "table": "cis_apply_charge", "comment": null, "subModule": "charge", "properties": [{"id": "dc6df155-5ac7-4bf9-8da2-1225f531b6c1", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "eeb3b91f-d6ca-4e5f-aae8-b09c2f0df247", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "cisBaseApplyId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_base_apply_id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "抽象父类标识", "associationType": "COMPOSITION", "associatedEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": true}, {"id": "d21caac0-f78a-4b21-964f-067ef9d17724", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "orderId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "order_id", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "医嘱id", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "4436114a-2bb3-48d8-b744-38a64b0caca8", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "visitCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "visit_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "患者接诊流水号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "5d935b7a-45d9-488d-a0f9-c8cd3a90cc37", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "priceItemCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "price_item_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "收费项目编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "7805ffe7-de66-45db-b3d1-033518952787", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "priceItemName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "price_item_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "收费项目名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "56b04d76-ef04-45d2-a48e-a9a565e17ac6", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "isFixed", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_fixed", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "固定费用", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "ca5e5ea8-5adf-4224-9c04-e67896821625", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "packageSpec", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "package_spec", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "规格", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "b6fd336f-7789-4afd-8fde-c99e15b49a06", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "price", "javaType": "BigDecimal", "enumName": null, "idGenerationType": null, "column": "price", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "单价", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "7d44d6c4-00fc-48ae-8c8c-1d3792984ace", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "unit", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "unit", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "单位", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "9ad4cce7-c59c-4eb0-a94e-f96e0c0e70de", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "num", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "num", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "数量", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "41d49324-87c4-49c4-8c00-a21980e4be52", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "chargeTime", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "charge_time", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "计费时段", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "84ba28be-2ddd-43da-958a-159fb6fb63d4", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "chargeFrequency", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "charge_frequency", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "计费频次", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "c3d3ce43-fc54-4072-b85e-ebb7f9d8dbb1", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "statusCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "status_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "d9546d9b-166d-4a7b-837a-cb0914604986", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "CreatedStaff", "name": "createdStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff", "length": 64, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "e9e12b57-691b-46db-b7a0-7fbd75055bba", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "f3b6f53d-3e76-4186-9877-48201d1917c9", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "UpdatedStaff", "name": "updatedStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "updated_staff", "length": 64, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "99f4f6f0-dbe7-487e-9e3b-9a5853a96d8e", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "UpdatedDate", "name": "updatedDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "updated_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "2675b9ca-6cda-4e56-bf86-913d6369fc8b", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "executeOrgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "execute_org_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "执行科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "75de588e-7ed1-454c-a9ee-1e5fda4be4be", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "chageAmount", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "chage_amount", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "金额", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "728e38d7-b5e6-4985-af65-ba3e68ebf701", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "limitConformFlag", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "limit_conform_flag", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "特限符合标识:1符合,0不符合", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "b185102a-e524-478a-aecd-57e015f39cdf", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Basic", "name": "barCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "bar_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "SPD高值耗材唯一码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "7086abb4-1172-44ef-bd2a-11f995087e1c", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "type": "Version", "name": "version", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "version", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "版本", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "68f85e76-cd92-4257-aca6-61602b5a736f", "name": "CisSpcobsApplyDetail", "table": "cis_spcobs_apply_detail", "comment": "检验申请单明细", "subModule": "spcobs", "properties": [{"id": "5b20c1f7-500e-4d54-92ab-d6a15ac5485b", "entityId": "68f85e76-cd92-4257-aca6-61602b5a736f", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "1d31dfa7-ff2f-4247-880a-13dbb083acf3", "entityId": "68f85e76-cd92-4257-aca6-61602b5a736f", "type": "Basic", "name": "cisSpcobsApplyId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_spcobs_apply_id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "检验类申请单标识", "associationType": "COMPOSITION", "associatedEntityId": "263e3480-7f0c-4fbd-b856-27eb69d38224", "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": true}, {"id": "84d4eaf4-fe1e-4eb9-80f1-a89fc324aa87", "entityId": "68f85e76-cd92-4257-aca6-61602b5a736f", "type": "Basic", "name": "no", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "no", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "内部序号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "dab2c852-5afa-48db-ab5d-c62abe8b0713", "entityId": "68f85e76-cd92-4257-aca6-61602b5a736f", "type": "Basic", "name": "spcobsName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "spcobs_name", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "检验名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "0d52106a-4cbe-4835-bfa9-aecfe42d14be", "entityId": "68f85e76-cd92-4257-aca6-61602b5a736f", "type": "Basic", "name": "deviceType", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "device_type", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "检验设备类型 字典SpcobsDeviceType", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "d3256338-7428-41f9-b6b0-105dae8ac214", "entityId": "68f85e76-cd92-4257-aca6-61602b5a736f", "type": "Basic", "name": "testTubeId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "test_tube_id", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "试管号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "c26173f5-ed1d-4bfa-bfbf-04122c46061a", "entityId": "68f85e76-cd92-4257-aca6-61602b5a736f", "type": "Basic", "name": "method", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "method", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "方法 字典SpcobsMethod", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "a3f8a2cc-0f3f-4ebc-a60f-0e2284e1df04", "entityId": "68f85e76-cd92-4257-aca6-61602b5a736f", "type": "Basic", "name": "speciman", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "speciman", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "标本 字典Speciman", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "name": "CisOrderExecPlan", "table": "cis_order_exec_plan", "comment": "医嘱执行档", "subModule": "execPlan", "properties": [{"id": "a90e783c-57bf-4b3a-a494-cd843b1ac13a", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "4ae8955d-74c9-4005-b92f-5a6f820c6d0c", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "patMiCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "pat_mi_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "主索引", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "79ed2753-5f4b-4c81-b2fb-301542b5c26b", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "visitCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "visit_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "就诊流水号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "1dc944f1-487a-4855-90e6-7a581cd69d72", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "orgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "org_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "科室编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "9e18d458-918c-47df-b08b-bbfe47e630a6", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "deptNurseCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "dept_nurse_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "护理组号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "260fbe89-71f9-4134-aef7-a25c75c2e437", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "orderId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "order_id", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "医嘱号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "e2c38d22-f0b4-4b94-bca7-3dc11b350218", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "cisBaseApplyId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_base_apply_id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "抽象父类标识", "associationType": "COMPOSITION", "associatedEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": true}, {"id": "cb1685a4-d0de-4904-a9dc-ce553b033205", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "sortNo", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "sort_no", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱序号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "d1936298-4de2-4735-b9de-8f599ae8ea42", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "serviceItemCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "service_item_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱项目编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "cfbe181b-825e-4875-a3fe-78b42cb61fcc", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "serviceItemName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "service_item_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱项目名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "dfe1c4c8-fc38-43d4-b895-f1cd851f1c72", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "orderClass", "javaType": "Enum", "enumName": "SystemTypeEnum", "idGenerationType": null, "column": "order_class", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "922a5636-f868-4209-9907-0b9266546e69", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "isSkin", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_skin", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是否皮试", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "2c3746f5-e8dc-489a-9d20-fe99ed9fb421", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "skinResult", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "skin_result", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "皮试结果", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": false}, {"id": "472cc99e-f149-4bd7-bee4-fcc3775cb3a3", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "receiveOrg", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "receive_org", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "领药科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "7dcdd01d-ace8-423d-9878-808328fd4276", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "execPlanDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "exec_plan_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱预计执行时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "f54f622d-85e7-41f3-b5db-aebd8692b9fd", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "execDate", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "exec_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": true}, {"id": "e4378753-8363-44df-884a-d40c9b3975be", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "execStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "exec_staff", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行人", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "453dd04d-066f-4019-8984-8d9e2de45209", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "isCharge", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_charge", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是否计费", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "65244352-10aa-4b6a-a214-5d033cac643e", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "chargeDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "charge_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "计费时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "0933f130-c49e-4040-8948-038ccc3d5ef6", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "chargeStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "charge_staff", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "计费人", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "f3b8f7e9-b09d-4ff2-b3ef-9e215c02f58e", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "chargeOrg", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "charge_org", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "计费科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "45949499-54cb-4509-bcb5-3d68dc8cf32b", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "isPrint", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_print", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行单打印标记", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "47aed2e9-08db-426e-8517-3d3adc73f1a5", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "printDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "print_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行单打印时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "e8bda9ce-976d-4365-9916-4fb594c56636", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "printStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "print_staff", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行单打印人", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "d0a8c6ad-3559-4e1b-86f6-4e1615136c37", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "skinTestDate", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "skin_test_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "皮试操作时间(区别于结果录入时间)", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "960c6479-711e-4ca8-a27e-962c5c1f8eeb", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "stStaffA", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "st_staff_a", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "皮试人1", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "1ccb0995-7926-4f76-8f83-d1132e0112c2", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "stStaffB", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "st_staff_b", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "皮试人2", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "fdfd0c71-4c6e-4a54-bd25-905de29c04c5", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "heldStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "held_staff", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "开立医生", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "dc76ade3-4818-465c-bff7-b25c35a4b3a6", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "createOrgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "create_org_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "开发医生所在科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "b0a82d25-25fa-44fc-88be-2a2fb3ab524f", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Basic", "name": "statusCode", "javaType": "Enum", "enumName": "CisStatusEnum", "idGenerationType": null, "column": "status_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": true}, {"id": "fba3537c-341c-43ee-9db4-e83cdbd532e6", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "CreatedStaff", "name": "createdStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff", "length": 64, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "dead2748-ae76-41ce-9674-6c447c04fb35", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "a12b40c3-8d55-4ea7-aeeb-ae5693a2bb86", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "UpdatedStaff", "name": "updatedStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "updated_staff", "length": 64, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "4e57a1be-712b-465e-a493-7ff764af1246", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "UpdatedDate", "name": "updatedDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "updated_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "b5d27f5a-77c5-40d4-9b56-c152b163a91d", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "type": "Version", "name": "version", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "version", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "版本", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": false}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "name": "CisOrderExecPlanCharge", "table": "cis_order_exec_plan_charge", "comment": "医嘱执行档费用从表", "subModule": "execPlan", "properties": [{"id": "32ecd75c-9e04-48b8-b815-1dda08215bdc", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "0016291f-53a9-4924-8921-0b06c0c2b983", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "cisOrderExecPlanId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_order_exec_plan_id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "医嘱执行档标识", "associationType": "ASSOCIATION", "associatedEntityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "c7b935d2-e207-4d63-be3f-e84a4f4294de", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "cisBaseApplyId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "cis_base_apply_id", "length": 50, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "抽象父类标识", "associationType": "ASSOCIATION", "associatedEntityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "c2a40827-fbce-4258-96b3-ea96e5ad6dc4", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "visitCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "visit_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "患者接诊流水号", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "b3c59aba-badf-491b-ba3f-34b47269b4ac", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "priceItemCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "price_item_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "收费项目编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "169332bf-6840-409a-be53-626b051a4171", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "priceItemName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "price_item_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "收费项目名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "ef8dd0c9-b0f8-4f00-92d6-ff1baf8ac93b", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "packageSpec", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "package_spec", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "包装规格", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "cf6b8d17-56e8-4228-83df-8d30cad6ae06", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "price", "javaType": "BigDecimal", "enumName": null, "idGenerationType": null, "column": "price", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "单价", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "ce5751c8-e404-4cf1-ad88-987fc82f1bd5", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "unit", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "unit", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "单位 字典Measures", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "d1581463-fdaf-4450-9b6f-965718cc0d51", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "num", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "num", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "数量", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "72b1abda-5ba5-4c5e-9e44-62b2720cdfb4", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "chargeAmount", "javaType": "BigDecimal", "enumName": null, "idGenerationType": null, "column": "charge_amount", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "应收", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "c686962c-930a-4688-ac56-04214b25a1ed", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "isFixed", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_fixed", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "是否为固定项", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "b716910f-debd-4bb4-9e52-a2d6cdea6dc7", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "limitConformFlag", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "limit_conform_flag", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "特限符合标识 1符合；0 不符合", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "09b9bc6c-3b8b-4071-a3a5-f58f1ddb7cbe", "entityId": "99f07bbf-7cf6-418b-9b77-58709e66c55a", "type": "Basic", "name": "executeOrgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "execute_org_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "执行/取药科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}]}], "services": [{"id": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "superServiceId": null, "comment": "抽象父类领域服务", "pxyEntities": [{"id": "563347fb-74ae-42c1-b389-6969e68bf325", "serviceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "entityId": "749cb2f5-92af-43e2-8de2-0870896e09d7", "findAll": true, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}, {"id": "4358adee-0edd-42ec-84c4-c971ce0fa783", "serviceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "entityId": "0cc08272-0a76-43fe-9705-2c4e8ea429e8", "findAll": true, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}, {"id": "47e60e62-ec55-4f44-8554-e74ed901886d", "serviceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "entityId": "0be2bbd8-0429-45c7-9e14-96a9c5c81838", "findAll": true, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "0474ecbd-14f5-443e-b781-a0e31c1418ff", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "检查申请单领域服务", "pxyEntities": [{"id": "4655716c-640c-4f36-a413-6d93e6c37bf9", "serviceId": "0474ecbd-14f5-443e-b781-a0e31c1418ff", "entityId": "a57cedd3-82da-4e44-8988-034c7a5a61bb", "findAll": true, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "acaaece7-ca96-43fb-bfc2-510441d84f6e", "entityId": "b43ca141-79c4-4016-a3e6-88871a7589d3", "superServiceId": null, "comment": "病历及查体摘要领域服务", "pxyEntities": [{"id": "771348b7-1f60-4f3c-a77d-7dc79d59bcdb", "serviceId": "acaaece7-ca96-43fb-bfc2-510441d84f6e", "entityId": "b43ca141-79c4-4016-a3e6-88871a7589d3", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "75a97fca-9f90-47ce-9e73-3f95264cbf6c", "entityId": "263e3480-7f0c-4fbd-b856-27eb69d38224", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "检验类申请单领域服务", "pxyEntities": [{"id": "f5bcd2cf-7070-4d67-9188-f4763afa265b", "serviceId": "75a97fca-9f90-47ce-9e73-3f95264cbf6c", "entityId": "263e3480-7f0c-4fbd-b856-27eb69d38224", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}, {"id": "380924bc-90e7-4beb-9c2a-26d676b86474", "serviceId": "75a97fca-9f90-47ce-9e73-3f95264cbf6c", "entityId": "68f85e76-cd92-4257-aca6-61602b5a736f", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "265dd5b2-6fe1-4e1b-8ef0-5161a8f42f92", "entityId": "3cf24a04-bcfb-4273-9bae-eba0b9258fe4", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "通用申请单领域服务", "pxyEntities": [{"id": "4ca43312-c192-452c-b51c-fa96d68c2d1d", "serviceId": "265dd5b2-6fe1-4e1b-8ef0-5161a8f42f92", "entityId": "3cf24a04-bcfb-4273-9bae-eba0b9258fe4", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "290bd981-3f34-4146-b9fb-b0bc78d78ae3", "entityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "药品申请单领域服务", "pxyEntities": [{"id": "c4b009fd-48fb-418c-ae9f-c67ae805c619", "serviceId": "290bd981-3f34-4146-b9fb-b0bc78d78ae3", "entityId": "b9ff5c4d-f547-4b40-9b01-9bc1d1fc6136", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "25097fbf-b8e5-4b0d-a37d-4fe454ffe1e4", "entityId": "64653efb-9221-41c7-8711-af7b0763101a", "superServiceId": "290bd981-3f34-4146-b9fb-b0bc78d78ae3", "comment": "成药申请单领域服务", "pxyEntities": [{"id": "82d1ce12-b468-4543-9985-59de5e731c05", "serviceId": "25097fbf-b8e5-4b0d-a37d-4fe454ffe1e4", "entityId": "64653efb-9221-41c7-8711-af7b0763101a", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "6a42c152-4e71-4156-9f86-36a73d7c1c79", "entityId": "aaa1a630-d5d4-4842-aa38-2cac9b10ef16", "superServiceId": "290bd981-3f34-4146-b9fb-b0bc78d78ae3", "comment": "中草药申请单领域服务", "pxyEntities": [{"id": "daba739e-2459-43f4-b9ac-42a10588554d", "serviceId": "6a42c152-4e71-4156-9f86-36a73d7c1c79", "entityId": "aaa1a630-d5d4-4842-aa38-2cac9b10ef16", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "f165eef2-3567-4d79-8227-c7acc84dcb9a", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "手术申请领域服务", "pxyEntities": [{"id": "b499f043-b5f2-4b51-aa36-b4f9fcd9e89a", "serviceId": "f165eef2-3567-4d79-8227-c7acc84dcb9a", "entityId": "de95b698-20a3-4c4b-bf8c-dd85e6085514", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "b44fde08-7d4a-4938-81d3-2ee49e834b68", "entityId": "58a3172d-a857-41e2-aa76-b315333616bf", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "治疗申请单领域服务", "pxyEntities": [{"id": "3dbd938e-43c1-43b0-8e0f-6d7d45685452", "serviceId": "b44fde08-7d4a-4938-81d3-2ee49e834b68", "entityId": "58a3172d-a857-41e2-aa76-b315333616bf", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "fc26043d-2d13-40e7-8e92-b55b2aa0cc5c", "entityId": "fa852c29-fdf0-4e1b-910d-f2f58726f75c", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "转科申请单领域服务", "pxyEntities": [{"id": "0b373558-1ae8-4207-ac14-5299201cfa1d", "serviceId": "fc26043d-2d13-40e7-8e92-b55b2aa0cc5c", "entityId": "fa852c29-fdf0-4e1b-910d-f2f58726f75c", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "783faa5e-b498-4445-91a9-3796cc491196", "entityId": "84a0fcdd-438b-4311-9f43-c02d4cd24a04", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "出院申请单领域服务", "pxyEntities": [{"id": "eadb8de1-9f3e-43c1-b7fc-69f8da69f9e8", "serviceId": "783faa5e-b498-4445-91a9-3796cc491196", "entityId": "84a0fcdd-438b-4311-9f43-c02d4cd24a04", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "92ab00d9-a69a-445f-97ff-4914f261698c", "entityId": "93c9c2b0-3527-4612-b3cd-4702995c760f", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "病理申请单领域服务", "pxyEntities": [{"id": "08fe0b5e-d625-4c9f-8c51-0952207b16a2", "serviceId": "92ab00d9-a69a-445f-97ff-4914f261698c", "entityId": "93c9c2b0-3527-4612-b3cd-4702995c760f", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "0f9774ba-4069-440d-9c78-ef0108648396", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "会诊申请单领域服务", "pxyEntities": [{"id": "f1f29a42-bb7a-4c3e-9473-4b223496cee0", "serviceId": "0f9774ba-4069-440d-9c78-ef0108648396", "entityId": "7b1749e4-ee7a-45ad-899a-5a951b4cbc8e", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "04bb6a5e-bf6f-4e43-aa1c-244b8f21fb32", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "备血申请单领域服务", "pxyEntities": [{"id": "093a665b-3f2d-4f44-b7b3-9a101aba38b8", "serviceId": "04bb6a5e-bf6f-4e43-aa1c-244b8f21fb32", "entityId": "cf052941-240c-4dd3-880e-497ab57179ba", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "76270698-0515-4994-9b75-6245cca5cf1e", "entityId": "80e1041c-ebd4-4718-9474-9347925795d3", "superServiceId": "7e0330b6-f30f-4f06-9e78-8a9357e3ccc4", "comment": "输血申请单领域服务", "pxyEntities": [{"id": "a190686a-f1f3-4758-9612-d6f2f54e7a59", "serviceId": "76270698-0515-4994-9b75-6245cca5cf1e", "entityId": "80e1041c-ebd4-4718-9474-9347925795d3", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "101bb230-f88f-4482-9ce6-42334f020951", "entityId": "1c87f8ae-25e4-4f6e-9f43-458cdd7fb0c6", "superServiceId": null, "comment": "血液成分从表领域服务", "pxyEntities": [{"id": "67462b1c-d827-40fa-abc7-e701c829f42a", "serviceId": "101bb230-f88f-4482-9ce6-42334f020951", "entityId": "1c87f8ae-25e4-4f6e-9f43-458cdd7fb0c6", "findAll": false, "findOne": true, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}]}