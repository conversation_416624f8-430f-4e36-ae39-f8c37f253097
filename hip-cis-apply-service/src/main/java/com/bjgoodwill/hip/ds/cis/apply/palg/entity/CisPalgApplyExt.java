package com.bjgoodwill.hip.ds.cis.apply.palg.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisPalgExtTypeEnum;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.apply.palg.repository.CisPalgApplyExtRepository;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyExtEto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyExtNto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyExtQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "病理申请单扩展表")
@Table(name = "cis_palg_apply_ext", indexes = {@Index(name = "cis_palg_apply_ext_apply_id", columnList = "apply_id")}, uniqueConstraints = {})
public class CisPalgApplyExt {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("申请单ID")
    @Column(name = "apply_id", nullable = true)
    private String applyId;


    @Enumerated(EnumType.STRING)
    @Comment("病理扩展类型")
    @Column(name = "palg_ext_type", nullable = true)
    private CisPalgExtTypeEnum palgExtType;


    @Comment("手术术式编码")
    @Column(name = "operation_code", nullable = true)
    private String operationCode;


    @Comment("手术术式名称")
    @Column(name = "operation_name", nullable = true)
    private String operationName;


    @Comment("手术医生")
    @Column(name = "operation_doctor", nullable = true)
    private String operationDoctor;


    @Comment("手术医生名称")
    @Column(name = "operation_doctor_name", nullable = true)
    private String operationDoctorName;


    @Comment("手术时间")
    @Column(name = "operation_date", nullable = true)
    private LocalDateTime operationDate;


    @Comment("手术室")
    @Column(name = "operation_room", nullable = true)
    private String operationRoom;


    @Comment("手术室名称")
    @Column(name = "operation_room_name", nullable = true)
    private String operationRoomName;


    @Comment("术中快速病理")
    @Column(name = "intraoperatively_palg", nullable = true)
    private Boolean intraoperativelyPalg;


    @Comment("手术所见")
    @Column(name = "operation_seen", nullable = true)
    private String operationSeen;


    @Comment("发现日期")
    @Column(name = "discovery_date", nullable = true)
    private LocalDateTime discoveryDate;


    @Comment("肿瘤-部位")
    @Column(name = "human_organs", nullable = true)
    private String humanOrgans;


    @Comment("肿瘤-大小")
    @Column(name = "tumor_size", nullable = true)
    private String tumorSize;


    @Comment("肿瘤-形状")
    @Column(name = "tumor_shape", nullable = true)
    private String tumorShape;


    @Comment("肿瘤-活动度")
    @Column(name = "tumor_mobility", nullable = true)
    private String tumorMobility;


    @Comment("肿瘤-坚度")
    @Column(name = "tumor_firmness", nullable = true)
    private String tumorFirmness;


    @Comment("肿瘤-生长速度")
    @Column(name = "tumor_growth_rate", nullable = true)
    private String tumorGrowthRate;


    @Comment("转移或可疑的转移")
    @Column(name = "tumor_transfer", nullable = true)
    private Boolean tumorTransfer;


    @Comment("其他")
    @Column(name = "tumor_other", nullable = true)
    private String tumorOther;


    @Comment("月经史-初经（岁）")
    @Column(name = "first_menstruation", nullable = true)
    private Integer firstMenstruation;


    @Comment("月经史-周期（天）")
    @Column(name = "cycle", nullable = true)
    private Integer cycle;


    @Comment("月经史-末次月经时间")
    @Column(name = "last_menstrual_date", nullable = true)
    private LocalDateTime lastMenstrualDate;


    @Comment("月经史-绝经标志")
    @Column(name = "menopause_flag", nullable = true)
    private Boolean menopauseFlag;


    @Comment("妊娠生产史-怀孕")
    @Column(name = "pregnant", nullable = true)
    private Integer pregnant;


    @Comment("妊娠生产史-生产")
    @Column(name = "produce", nullable = true)
    private Integer produce;


    @Comment("妊娠生产史-流产")
    @Column(name = "abortion", nullable = true)
    private Integer abortion;


    @Comment("妊娠生产史-末次产期")
    @Column(name = "last_delivery_date", nullable = true)
    private LocalDateTime lastDeliveryDate;


    @Comment("妊娠生产史-治疗经过")
    @Column(name = "treatment_elapse", nullable = true)
    private String treatmentElapse;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    public static void deleteByCisPalgApplyId(String cisPalgApplyId) {
        dao().deleteByApplyId(cisPalgApplyId);
    }

    public static Optional<CisPalgApplyExt> getCisPalgApplyExtById(String id) {
        return dao().findById(id);
    }

    public static List<CisPalgApplyExt> getCisPalgApplyExts(CisPalgApplyExtQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisPalgApplyExt> getCisPalgApplyExtPage(CisPalgApplyExtQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisPalgApplyExt> getSpecification(CisPalgApplyExtQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("applyId"), qto.getApplyId()));
            }
            if (qto.getPalgExtType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("palgExtType"), qto.getPalgExtType()));
            }
            if (StringUtils.isNotBlank(qto.getOperationCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("operationCode"), qto.getOperationCode()));
            }
            if (StringUtils.isNotBlank(qto.getOperationName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("operationName"), qto.getOperationName()));
            }
            if (StringUtils.isNotBlank(qto.getOperationDoctor())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("operationDoctor"), qto.getOperationDoctor()));
            }
            if (StringUtils.isNotBlank(qto.getOperationDoctorName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("operationDoctorName"), qto.getOperationDoctorName()));
            }
            if (qto.getOperationDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("operationDate"), LocalDateUtil.beginOfDay(qto.getOperationDate()), LocalDateUtil.endOfDay(qto.getOperationDate())));
            }
            if (StringUtils.isNotBlank(qto.getOperationRoom())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("operationRoom"), qto.getOperationRoom()));
            }
            if (StringUtils.isNotBlank(qto.getOperationRoomName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("operationRoomName"), qto.getOperationRoomName()));
            }
            if (qto.getIntraoperativelyPalg() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("intraoperativelyPalg"), qto.getIntraoperativelyPalg()));
            }
            if (StringUtils.isNotBlank(qto.getOperationSeen())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("operationSeen"), qto.getOperationSeen()));
            }
            if (qto.getDiscoveryDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("discoveryDate"), LocalDateUtil.beginOfDay(qto.getDiscoveryDate()), LocalDateUtil.endOfDay(qto.getDiscoveryDate())));
            }
            if (StringUtils.isNotBlank(qto.getHumanOrgans())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("humanOrgans"), qto.getHumanOrgans()));
            }
            if (StringUtils.isNotBlank(qto.getTumorSize())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("tumorSize"), qto.getTumorSize()));
            }
            if (StringUtils.isNotBlank(qto.getTumorShape())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("tumorShape"), qto.getTumorShape()));
            }
            if (StringUtils.isNotBlank(qto.getTumorMobility())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("tumorMobility"), qto.getTumorMobility()));
            }
            if (StringUtils.isNotBlank(qto.getTumorFirmness())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("tumorFirmness"), qto.getTumorFirmness()));
            }
            if (StringUtils.isNotBlank(qto.getTumorGrowthRate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("tumorGrowthRate"), qto.getTumorGrowthRate()));
            }
            if (qto.getTumorTransfer() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("tumorTransfer"), qto.getTumorTransfer()));
            }
            if (StringUtils.isNotBlank(qto.getTumorOther())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("tumorOther"), qto.getTumorOther()));
            }
            if (qto.getFirstMenstruation() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("firstMenstruation"), qto.getFirstMenstruation()));
            }
            if (qto.getCycle() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cycle"), qto.getCycle()));
            }
            if (qto.getLastMenstrualDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("lastMenstrualDate"), LocalDateUtil.beginOfDay(qto.getLastMenstrualDate()), LocalDateUtil.endOfDay(qto.getLastMenstrualDate())));
            }
            if (qto.getMenopauseFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("menopauseFlag"), qto.getMenopauseFlag()));
            }
            if (qto.getPregnant() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("pregnant"), qto.getPregnant()));
            }
            if (qto.getProduce() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("produce"), qto.getProduce()));
            }
            if (qto.getAbortion() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("abortion"), qto.getAbortion()));
            }
            if (qto.getLastDeliveryDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("lastDeliveryDate"), LocalDateUtil.beginOfDay(qto.getLastDeliveryDate()), LocalDateUtil.endOfDay(qto.getLastDeliveryDate())));
            }
            if (StringUtils.isNotBlank(qto.getTreatmentElapse())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("treatmentElapse"), qto.getTreatmentElapse()));
            }
            return predicate;
        };
    }

    private static CisPalgApplyExtRepository dao() {
        return SpringUtil.getBean(CisPalgApplyExtRepository.class);
    }

    public static CisPalgApplyExt getByCisPalgApplyExtApplyId(String cisPalgApplyId) {
        return dao().findByApplyId(cisPalgApplyId).stream().findFirst().orElse(null);
    }

    public static void deleteByCisPalgApplyExtApplyId(String cisPalgApplyId) {
        dao().deleteByApplyId(cisPalgApplyId);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getApplyId() {
        return applyId;
    }

    protected void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public CisPalgExtTypeEnum getPalgExtType() {
        return palgExtType;
    }

    protected void setPalgExtType(CisPalgExtTypeEnum palgExtType) {
        this.palgExtType = palgExtType;
    }

    public String getOperationCode() {
        return operationCode;
    }

    protected void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public String getOperationName() {
        return operationName;
    }

    protected void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getOperationDoctor() {
        return operationDoctor;
    }

    protected void setOperationDoctor(String operationDoctor) {
        this.operationDoctor = operationDoctor;
    }

    public String getOperationDoctorName() {
        return operationDoctorName;
    }

    protected void setOperationDoctorName(String operationDoctorName) {
        this.operationDoctorName = operationDoctorName;
    }

    public LocalDateTime getOperationDate() {
        return operationDate;
    }

    protected void setOperationDate(LocalDateTime operationDate) {
        this.operationDate = operationDate;
    }

    public String getOperationRoom() {
        return operationRoom;
    }

    protected void setOperationRoom(String operationRoom) {
        this.operationRoom = operationRoom;
    }

    public String getOperationRoomName() {
        return operationRoomName;
    }

    protected void setOperationRoomName(String operationRoomName) {
        this.operationRoomName = operationRoomName;
    }

    public Boolean getIntraoperativelyPalg() {
        return intraoperativelyPalg;
    }

    protected void setIntraoperativelyPalg(Boolean intraoperativelyPalg) {
        this.intraoperativelyPalg = intraoperativelyPalg;
    }

    public String getOperationSeen() {
        return operationSeen;
    }

    protected void setOperationSeen(String operationSeen) {
        this.operationSeen = operationSeen;
    }

    public LocalDateTime getDiscoveryDate() {
        return discoveryDate;
    }

    protected void setDiscoveryDate(LocalDateTime discoveryDate) {
        this.discoveryDate = discoveryDate;
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    protected void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    public String getTumorSize() {
        return tumorSize;
    }

    protected void setTumorSize(String tumorSize) {
        this.tumorSize = tumorSize;
    }

    public String getTumorShape() {
        return tumorShape;
    }

    protected void setTumorShape(String tumorShape) {
        this.tumorShape = tumorShape;
    }

    public String getTumorMobility() {
        return tumorMobility;
    }

    protected void setTumorMobility(String tumorMobility) {
        this.tumorMobility = tumorMobility;
    }

    public String getTumorFirmness() {
        return tumorFirmness;
    }

    protected void setTumorFirmness(String tumorFirmness) {
        this.tumorFirmness = tumorFirmness;
    }

    public String getTumorGrowthRate() {
        return tumorGrowthRate;
    }

    protected void setTumorGrowthRate(String tumorGrowthRate) {
        this.tumorGrowthRate = tumorGrowthRate;
    }

    public Boolean getTumorTransfer() {
        return tumorTransfer;
    }

    protected void setTumorTransfer(Boolean tumorTransfer) {
        this.tumorTransfer = tumorTransfer;
    }

    public String getTumorOther() {
        return tumorOther;
    }

    protected void setTumorOther(String tumorOther) {
        this.tumorOther = tumorOther;
    }

    public Integer getFirstMenstruation() {
        return firstMenstruation;
    }

    protected void setFirstMenstruation(Integer firstMenstruation) {
        this.firstMenstruation = firstMenstruation;
    }

    public Integer getCycle() {
        return cycle;
    }

    protected void setCycle(Integer cycle) {
        this.cycle = cycle;
    }

    public LocalDateTime getLastMenstrualDate() {
        return lastMenstrualDate;
    }

    protected void setLastMenstrualDate(LocalDateTime lastMenstrualDate) {
        this.lastMenstrualDate = lastMenstrualDate;
    }

    public Boolean getMenopauseFlag() {
        return menopauseFlag;
    }

    protected void setMenopauseFlag(Boolean menopauseFlag) {
        this.menopauseFlag = menopauseFlag;
    }

    public Integer getPregnant() {
        return pregnant;
    }

    protected void setPregnant(Integer pregnant) {
        this.pregnant = pregnant;
    }

    public Integer getProduce() {
        return produce;
    }

    protected void setProduce(Integer produce) {
        this.produce = produce;
    }

    public Integer getAbortion() {
        return abortion;
    }

    protected void setAbortion(Integer abortion) {
        this.abortion = abortion;
    }

    public LocalDateTime getLastDeliveryDate() {
        return lastDeliveryDate;
    }

    protected void setLastDeliveryDate(LocalDateTime lastDeliveryDate) {
        this.lastDeliveryDate = lastDeliveryDate;
    }

    public String getTreatmentElapse() {
        return treatmentElapse;
    }

    protected void setTreatmentElapse(String treatmentElapse) {
        this.treatmentElapse = treatmentElapse;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisPalgApplyExt other = (CisPalgApplyExt) obj;
        return Objects.equals(id, other.id);
    }

    public CisPalgApplyExt create(String applyId, CisPalgApplyExtNto cisPalgApplyExtNto, Boolean save) {
        Assert.notNull(cisPalgApplyExtNto, "参数cisPalgApplyExtNto不能为空！");

        setId(cisPalgApplyExtNto.getId());
        setApplyId(applyId);
        setPalgExtType(cisPalgApplyExtNto.getPalgExtType());
        setOperationCode(cisPalgApplyExtNto.getOperationCode());
        setOperationName(cisPalgApplyExtNto.getOperationName());
        setOperationDoctor(cisPalgApplyExtNto.getOperationDoctor());
        setOperationDoctorName(cisPalgApplyExtNto.getOperationDoctorName());
        setOperationDate(cisPalgApplyExtNto.getOperationDate());
        setOperationRoom(cisPalgApplyExtNto.getOperationRoom());
        setOperationRoomName(cisPalgApplyExtNto.getOperationRoomName());
        setIntraoperativelyPalg(cisPalgApplyExtNto.getIntraoperativelyPalg());
        setOperationSeen(cisPalgApplyExtNto.getOperationSeen());
        setDiscoveryDate(cisPalgApplyExtNto.getDiscoveryDate());
        setHumanOrgans(cisPalgApplyExtNto.getHumanOrgans());
        setTumorSize(cisPalgApplyExtNto.getTumorSize());
        setTumorShape(cisPalgApplyExtNto.getTumorShape());
        setTumorMobility(cisPalgApplyExtNto.getTumorMobility());
        setTumorFirmness(cisPalgApplyExtNto.getTumorFirmness());
        setTumorGrowthRate(cisPalgApplyExtNto.getTumorGrowthRate());
        setTumorTransfer(cisPalgApplyExtNto.getTumorTransfer());
        setTumorOther(cisPalgApplyExtNto.getTumorOther());
        setFirstMenstruation(cisPalgApplyExtNto.getFirstMenstruation());
        setCycle(cisPalgApplyExtNto.getCycle());
        setLastMenstrualDate(cisPalgApplyExtNto.getLastMenstrualDate());
        setMenopauseFlag(cisPalgApplyExtNto.getMenopauseFlag());
        setPregnant(cisPalgApplyExtNto.getPregnant());
        setProduce(cisPalgApplyExtNto.getProduce());
        setAbortion(cisPalgApplyExtNto.getAbortion());
        setLastDeliveryDate(cisPalgApplyExtNto.getLastDeliveryDate());
        setTreatmentElapse(cisPalgApplyExtNto.getTreatmentElapse());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        if (save) {
            dao().save(this);
        }
        return this;
    }

    public void update(CisPalgApplyExtEto cisPalgApplyExtEto) {
        setPalgExtType(cisPalgApplyExtEto.getPalgExtType());
        setOperationCode(cisPalgApplyExtEto.getOperationCode());
        setOperationName(cisPalgApplyExtEto.getOperationName());
        setOperationDoctor(cisPalgApplyExtEto.getOperationDoctor());
        setOperationDoctorName(cisPalgApplyExtEto.getOperationDoctorName());
        setOperationDate(cisPalgApplyExtEto.getOperationDate());
        setOperationRoom(cisPalgApplyExtEto.getOperationRoom());
        setOperationRoomName(cisPalgApplyExtEto.getOperationRoomName());
        setIntraoperativelyPalg(cisPalgApplyExtEto.getIntraoperativelyPalg());
        setOperationSeen(cisPalgApplyExtEto.getOperationSeen());
        setDiscoveryDate(cisPalgApplyExtEto.getDiscoveryDate());
        setHumanOrgans(cisPalgApplyExtEto.getHumanOrgans());
        setTumorSize(cisPalgApplyExtEto.getTumorSize());
        setTumorShape(cisPalgApplyExtEto.getTumorShape());
        setTumorMobility(cisPalgApplyExtEto.getTumorMobility());
        setTumorFirmness(cisPalgApplyExtEto.getTumorFirmness());
        setTumorGrowthRate(cisPalgApplyExtEto.getTumorGrowthRate());
        setTumorTransfer(cisPalgApplyExtEto.getTumorTransfer());
        setTumorOther(cisPalgApplyExtEto.getTumorOther());
        setFirstMenstruation(cisPalgApplyExtEto.getFirstMenstruation());
        setCycle(cisPalgApplyExtEto.getCycle());
        setLastMenstrualDate(cisPalgApplyExtEto.getLastMenstrualDate());
        setMenopauseFlag(cisPalgApplyExtEto.getMenopauseFlag());
        setPregnant(cisPalgApplyExtEto.getPregnant());
        setProduce(cisPalgApplyExtEto.getProduce());
        setAbortion(cisPalgApplyExtEto.getAbortion());
        setLastDeliveryDate(cisPalgApplyExtEto.getLastDeliveryDate());
        setTreatmentElapse(cisPalgApplyExtEto.getTreatmentElapse());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }

}
