package com.bjgoodwill.hip.ds.cis.apply.material.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.material.entity.CisMaterialApply;
import com.bjgoodwill.hip.ds.cis.apply.material.to.CisMaterialApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.material.to.CisMaterialApplyTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisMaterialApplyAssembler {

    public static List<CisMaterialApplyTo> toTos(List<CisMaterialApply> cisMaterialApplys) {
        return toTos(cisMaterialApplys, false);
    }

    public static List<CisMaterialApplyTo> toTos(List<CisMaterialApply> cisMaterialApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisMaterialApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisMaterialApplys不能为空！");

        List<CisMaterialApplyTo> tos = new ArrayList<>();
        for (CisMaterialApply cisMaterialApply : cisMaterialApplys)
            tos.add(toTo(cisMaterialApply, withAllParts));
        return tos;
    }

    public static CisMaterialApplyTo toTo(CisMaterialApply cisMaterialApply) {
        return toTo(cisMaterialApply, false);
    }

    /**
     * @generated
     */
    public static CisMaterialApplyTo toTo(CisMaterialApply cisMaterialApply, boolean withAllParts) {
        if (cisMaterialApply == null)
            return null;
        CisMaterialApplyTo to = new CisMaterialApplyTo();
        to.setId(cisMaterialApply.getId());
        to.setPatMiCode(cisMaterialApply.getPatMiCode());
        to.setVisitCode(cisMaterialApply.getVisitCode());
        to.setServiceItemCode(cisMaterialApply.getServiceItemCode());
        to.setServiceItemName(cisMaterialApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisMaterialApply.getIsCanPriorityFlag());
        to.setStatusCode(cisMaterialApply.getStatusCode());
        to.setCreatedStaff(cisMaterialApply.getCreatedStaff());
        to.setCreatedDate(cisMaterialApply.getCreatedDate());
        to.setUpdatedStaff(cisMaterialApply.getUpdatedStaff());
        to.setUpdatedDate(cisMaterialApply.getUpdatedDate());
        to.setExecutorStaff(cisMaterialApply.getExecutorStaff());
        to.setExecutorDate(cisMaterialApply.getExecutorDate());
        to.setExecutorHosptialCode(cisMaterialApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisMaterialApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisMaterialApply.getExecutorOrgName());
        to.setVisitType(cisMaterialApply.getVisitType());
        to.setDeptNurseCode(cisMaterialApply.getDeptNurseCode());
        to.setDeptNurseName(cisMaterialApply.getDeptNurseName());
        to.setOrderID(cisMaterialApply.getOrderID());
        to.setHospitalCode(cisMaterialApply.getHospitalCode());
        to.setPrescriptionID(cisMaterialApply.getPrescriptionID());
        to.setIsPrint(cisMaterialApply.getIsPrint());
        to.setPrintStaff(cisMaterialApply.getPrintStaff());
        to.setPrintDate(cisMaterialApply.getPrintDate());
        to.setReMark(cisMaterialApply.getReMark());
        to.setIcuExecuteDate(cisMaterialApply.getIcuExecuteDate());
        to.setIsChargeManager(cisMaterialApply.getIsChargeManager());
        to.setVersion(cisMaterialApply.getVersion());
        to.setCreateOrgCode(cisMaterialApply.getCreateOrgCode());
        to.setSortNo(cisMaterialApply.getSortNo());
        to.setIsBaby(cisMaterialApply.getIsBaby());
        to.setIsOlation(cisMaterialApply.getIsOlation());
        to.setNum(cisMaterialApply.getNum());
        to.setIsApply(cisMaterialApply.getIsApply());
        to.setVisitOrgCode(cisMaterialApply.getVisitOrgCode());
        to.setVisitOrgName(cisMaterialApply.getVisitOrgName());
        to.setHighFlag(cisMaterialApply.getHighFlag());
        to.setBarCode(cisMaterialApply.getBarCode());

        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisMaterialApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisMaterialApply.getCisOrderExecPlans()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisMaterialApply.getApplyDiagnoses()));
        }
        return to;
    }

    public static CisMaterialApplyNto toNto(CisMaterialApply cisMaterialApply, boolean withAllParts) {
        if (cisMaterialApply == null)
            return null;
        CisMaterialApplyNto to = new CisMaterialApplyNto();
        to.setId(cisMaterialApply.getId());
        to.setPatMiCode(cisMaterialApply.getPatMiCode());
        to.setVisitCode(cisMaterialApply.getVisitCode());
        to.setServiceItemCode(cisMaterialApply.getServiceItemCode());
        to.setServiceItemName(cisMaterialApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisMaterialApply.getIsCanPriorityFlag());
        to.setExecutorOrgCode(cisMaterialApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisMaterialApply.getExecutorOrgName());
        to.setVisitType(cisMaterialApply.getVisitType());
        to.setDeptNurseCode(cisMaterialApply.getDeptNurseCode());
        to.setDeptNurseName(cisMaterialApply.getDeptNurseName());
        to.setOrderID(cisMaterialApply.getOrderID());
        to.setHospitalCode(cisMaterialApply.getHospitalCode());
        to.setPrescriptionID(cisMaterialApply.getPrescriptionID());
        to.setReMark(cisMaterialApply.getReMark());
        to.setIcuExecuteDate(cisMaterialApply.getIcuExecuteDate());
        to.setIsChargeManager(cisMaterialApply.getIsChargeManager());
        to.setCreateOrgCode(cisMaterialApply.getCreateOrgCode());
        to.setSortNo(cisMaterialApply.getSortNo());
        to.setIsBaby(cisMaterialApply.getIsBaby());
        to.setIsOlation(cisMaterialApply.getIsOlation());
        to.setNum(cisMaterialApply.getNum());
        to.setIsApply(cisMaterialApply.getIsApply());
        to.setVisitOrgCode(cisMaterialApply.getVisitOrgCode());
        to.setVisitOrgName(cisMaterialApply.getVisitOrgName());
        to.setHighFlag(cisMaterialApply.getHighFlag());
        to.setBarCode(cisMaterialApply.getBarCode());
        if (withAllParts) {
        }
        return to;
    }

}