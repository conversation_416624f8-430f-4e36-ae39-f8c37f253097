package com.bjgoodwill.hip.ds.cis.apply.operation.service.internal;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.enums.dict.DictCodeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.service.internal.ApplyWithDetialServiceImpl;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailTo;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.operation.entity.CisOperationApply;
import com.bjgoodwill.hip.ds.cis.apply.operation.entity.CisOperationApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.operation.service.CisOperationApplyService;
import com.bjgoodwill.hip.ds.cis.apply.operation.service.internal.assembler.CisOperationApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.operation.service.internal.assembler.CisOperationApplyDetailAssembler;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.*;
import com.bjgoodwill.hip.ds.cis.apply.proxy.DictElementServiceProxy;
import jodd.util.StringUtil;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.*;

@RestController("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisOperationApplyService")
@RequestMapping(value = "/api/apply/apply/cisOperationApply", produces = "application/json; charset=utf-8")
public class CisOperationApplyServiceImpl extends ApplyWithDetialServiceImpl<CisDgimgApplyDetail> implements CisOperationApplyService {

    private Map<String, Map<String, String>> dictOperationApplyDicts;

    private DictElementServiceProxy dictElementServiceProxy() {
        return SpringUtil.getBean(DictElementServiceProxy.class);
    }

    protected Map<String, Map<String, String>> getServiceDicts() {
        if (dictOperationApplyDicts == null) {
            List<String> dictCodes = List.of(new String[]{DictCodeEnum.入路.getCode(), DictCodeEnum.辅助器械.getCode(),
                    DictCodeEnum.部位.getCode(), DictCodeEnum.切口等级.getCode()});
            dictOperationApplyDicts = dictElementServiceProxy().getMuliDicts(dictCodes);
        }
        return dictOperationApplyDicts;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisOperationApplyTo getCisOperationApplyById(String id) {
        return CisOperationApplyAssembler.toTo(CisOperationApply.getCisOperationApplyById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisOperationApplyTo getCisOperationApplyAllById(String id) {
        return CisOperationApplyAssembler.toTo(CisOperationApply.getCisOperationApplyById(id).orElse(null), true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOperationApplyTo createCisOperationApply(CisOperationApplyNto cisOperationApplyNto) {
        CisOperationApply cisOperationApply = new CisOperationApply();
        if (StringUtil.isNotBlank(cisOperationApplyNto.getApproach())) {
            cisOperationApplyNto.setApproachName(getDictName(DictCodeEnum.入路.getCode(), cisOperationApplyNto.getApproach()));
        }
        if (StringUtil.isNotBlank(cisOperationApplyNto.getInstrument())) {
            cisOperationApplyNto.setInstrumentName(getDictName(DictCodeEnum.辅助器械.getCode(), cisOperationApplyNto.getInstrument()));
        }
        if (StringUtil.isNotBlank(cisOperationApplyNto.getIncisionLevel())) {
            cisOperationApplyNto.setIncisionLevelName(getDictName(DictCodeEnum.切口等级.getCode(), cisOperationApplyNto.getIncisionLevel()));
        }
        cisOperationApply = cisOperationApply.create(cisOperationApplyNto, true);
        return CisOperationApplyAssembler.toTo(cisOperationApply);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOperationApply(String id, CisOperationApplyEto cisOperationApplyEto) {
        Optional<CisOperationApply> cisOperationApplyOptional = CisOperationApply.getCisOperationApplyById(id);
        cisOperationApplyOptional.ifPresent(cisOperationApply -> {
            if (StringUtil.isNotBlank(cisOperationApplyEto.getApproach())) {
                cisOperationApplyEto.setApproachName(getDictName(DictCodeEnum.入路.getCode(), cisOperationApplyEto.getApproach()));
            }
            if (StringUtil.isNotBlank(cisOperationApplyEto.getInstrument())) {
                cisOperationApplyEto.setInstrumentName(getDictName(DictCodeEnum.辅助器械.getCode(), cisOperationApplyEto.getInstrument()));
            }
            if (StringUtil.isNotBlank(cisOperationApplyEto.getIncisionLevel())) {
                cisOperationApplyEto.setIncisionLevelName(getDictName(DictCodeEnum.切口等级.getCode(), cisOperationApplyEto.getIncisionLevel()));
            }
            cisOperationApply.update(cisOperationApplyEto);
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOperationApply(String id) {
        Optional<CisOperationApply> cisOperationApplyOptional = CisOperationApply.getCisOperationApplyById(id);
        cisOperationApplyOptional.ifPresent(cisOperationApply -> cisOperationApply.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOperationApplyDetailTo createCisOperationApplyDetail(String applyID, CisOperationApplyDetailNto cisOperationApplyDetailNto) {
        Optional<CisOperationApply> cisOperationApplyOptional = CisOperationApply.getCisOperationApplyById(applyID);
        BusinessAssert.notNull(cisOperationApplyOptional, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0006, applyID);
        CisOperationApplyDetail cisOperationApplyDetail = new CisOperationApplyDetail();
        cisOperationApplyDetail = cisOperationApplyDetail.create(applyID, cisOperationApplyDetailNto, CisStatusEnum.NEW, true);
        return CisOperationApplyDetailAssembler.toTo(cisOperationApplyDetail);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOperationApplyDetail(String id, CisOperationApplyDetailEto cisSpcobsApplyDetailEto) {

    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOperationApplyDetail(String id) {
        Optional<CisOperationApplyDetail> cisOperationApplyDetailOptional = CisOperationApplyDetail.getCisOperationApplyDetailById(id);
        cisOperationApplyDetailOptional.ifPresent(cisOperationApplyDetail -> cisOperationApplyDetail.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisOperationApplyDetailTo> getCisOperationApplyDetailByApplyId(String applyId) {
        List<CisOperationApplyDetail> details = CisOperationApplyDetail.getByCisOperationApplyId(applyId);
        return CollectionUtils.isEmpty(details) ? Collections.emptyList() : CisOperationApplyDetailAssembler.toTos(details);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<DetailTo> queryDetailToByCreateDate(LocalDateTime dateTime) {
        return new ArrayList<>(CisOperationApplyDetailAssembler.toTos(
                new CisOperationApplyDetail().queryDetailsByCreateDate(dateTime)
                        .stream().filter(CisOperationApplyDetail.class::isInstance)
                        .map(CisOperationApplyDetail.class::cast)
                        .toList()));
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }


}