package com.bjgoodwill.hip.ds.cis.apply.charge.repository;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.entity.CisApplyCharge;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.apply.charge.repository.CisApplyChargeRepository")
public interface CisApplyChargeRepository extends JpaRepository<CisApplyCharge, String>, JpaSpecificationExecutor<CisApplyCharge> {

    List<CisApplyCharge> findByApplyIdAndDeletedFalse(String cisBaseApplyId);

    Page<CisApplyCharge> findByApplyId(String cisBaseApplyId, Pageable pageable);

    boolean existsByApplyId(String cisBaseApplyId);

    void deleteByApplyId(String cisBaseApplyId);

    List<CisApplyCharge> findCisApplyChargesByIdInAndDeletedFalse(List<String> ids);

    List<CisApplyCharge> findCisApplyChargesByVisitCodeAndDeletedFalseAndStatusCodeAndApplyIdIn(String visitCode, CisStatusEnum cisStatusEnum, List<String> applyIds);

    List<CisApplyCharge> findCisApplyChargesByApplyIdInAndDeletedFalseAndStatusCode(List<String> applyIds, CisStatusEnum cisStatusEnum);
//    void deleteallByCisBaseApplyIdInAndStatueCodeAndChargeType(List<String> ids, CisStatusEnum cisStatusEnum, CisChargeTypeEnum cisChargeTypeEnum);

    @Modifying
    @Query("delete from CisApplyCharge c where c.applyId in ?1 and c.statusCode = ?2 and c.chargeType = ?3")
    void deleteUsageCharge(List<String> ids, CisStatusEnum cisStatusEnum, CisChargeTypeEnum cisChargeTypeEnum);

    @Modifying
    @Query("update CisApplyCharge c set c.detailId = ?2 where c.id in ?1")
    void updateDetailIdByIdIn(List<String> ids, String detailId);

    @Modifying
    @Query("update CisApplyCharge c set c.applyId = ?2 , c.orderId = ?3,c.updatedStaff = ?4,c.updatedStaffName = ?5 where c.id in ?1")
    void updateApplyIdByIdsIn(List<String> ids, String applyId, String orderId, String updateStaff, String updateName);
}