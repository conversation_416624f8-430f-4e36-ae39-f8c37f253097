package com.bjgoodwill.hip.ds.cis.apply.dgimg.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.service.internal.ApplyWithDetialServiceImpl;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailTo;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApply;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.service.CisDgimgApplyService;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.service.internal.assembler.CisDgimgApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.service.internal.assembler.CisDgimgApplyDetailAssembler;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.*;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisDgimgApplyService")
@RequestMapping(value = "/api/apply/apply/cisDgimgApply", produces = "application/json; charset=utf-8")
public class CisDgimgApplyServiceImpl extends ApplyWithDetialServiceImpl<CisDgimgApplyDetail> implements CisDgimgApplyService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisDgimgApplyTo> getCisDgimgApplies(CisDgimgApplyQto cisDgimgApplyQto) {
        return CisDgimgApplyAssembler.toTos(CisDgimgApply.getCisDgimgApplies(cisDgimgApplyQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisDgimgApplyTo> getCisDgimgApplyPage(CisDgimgApplyQto cisDgimgApplyQto) {
        Page<CisDgimgApply> page = CisDgimgApply.getCisDgimgApplyPage(cisDgimgApplyQto);
        Page<CisDgimgApplyTo> result = page.map(CisDgimgApplyAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisDgimgApplyTo getCisDgimgApplyById(String id) {
        return CisDgimgApplyAssembler.toTo(CisDgimgApply.getCisDgimgApplyById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisDgimgApplyTo getCisDgimgApplyAllById(String id) {
        return CisDgimgApplyAssembler.toTo(CisDgimgApply.getCisDgimgApplyById(id).orElse(null), true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisDgimgApplyTo createCisDgimgApply(CisDgimgApplyNto cisDgimgApplyNto) {
        CisDgimgApply cisDgimgApply = new CisDgimgApply();
        cisDgimgApply = cisDgimgApply.create(cisDgimgApplyNto, true);
        return CisDgimgApplyAssembler.toTo(cisDgimgApply);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisDgimgApplyTo addCisDgimgApply(String id, CisDgimgApplyDetailNto cisDgimgApplyDetailNto) {
        CisDgimgApply cisDgimgApply = CisDgimgApply.getCisDgimgApplyById(id).orElse(null);

        BusinessAssert.notNull(cisDgimgApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单");

        CisDgimgApplyDetail cisDgimgApplyDetail = new CisDgimgApplyDetail();
        cisDgimgApplyDetailNto.setCisDgimgApplyId(cisDgimgApply.getId());
        cisDgimgApplyDetail.create(cisDgimgApplyDetailNto.getCisDgimgApplyId(), cisDgimgApplyDetailNto, cisDgimgApply.getStatusCode());
        return CisDgimgApplyAssembler.toTo(cisDgimgApply, true);
    }


    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisDgimgApply(String id, CisDgimgApplyEto cisDgimgApplyEto) {
        Optional<CisDgimgApply> cisDgimgApplyOptional = CisDgimgApply.getCisDgimgApplyById(id);
        cisDgimgApplyOptional.ifPresent(cisDgimgApply -> cisDgimgApply.update(cisDgimgApplyEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisDgimgApply(String id) {
        Optional<CisDgimgApply> cisDgimgApplyOptional = CisDgimgApply.getCisDgimgApplyById(id);
        cisDgimgApplyOptional.ifPresent(cisDgimgApply -> cisDgimgApply.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisDgimgApplyDetailTo> getCisDgimgApplyDetailByApplyId(String applyId) {
        List<CisDgimgApplyDetail> details = CisDgimgApplyDetail.getByCisDgimgApplyId(applyId);
        return CollectionUtils.isEmpty(details) ? Collections.emptyList() : CisDgimgApplyDetailAssembler.toTos(details);
    }


    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<DetailTo> queryDetailToByCreateDate(LocalDateTime dateTime) {
        return new ArrayList<>(CisDgimgApplyDetailAssembler.toTos(
                new CisDgimgApplyDetail().queryDetailsByCreateDate(dateTime)
                        .stream().filter(CisDgimgApplyDetail.class::isInstance)
                        .map(CisDgimgApplyDetail.class::cast)
                        .toList()));
    }
}