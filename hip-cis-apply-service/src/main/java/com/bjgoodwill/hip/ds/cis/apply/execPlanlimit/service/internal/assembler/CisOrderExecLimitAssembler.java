package com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.entity.CisOrderExecLimit;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to.CisOrderExecLimitTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisOrderExecLimitAssembler {

    public static List<CisOrderExecLimitTo> toTos(List<CisOrderExecLimit> cisOrderExecLimits) {
        return toTos(cisOrderExecLimits, false);
    }

    public static List<CisOrderExecLimitTo> toTos(List<CisOrderExecLimit> cisOrderExecLimits, boolean withAllParts) {
        Assert.notNull(cisOrderExecLimits, "参数cisOrderExecLimits不能为空！");

        List<CisOrderExecLimitTo> tos = new ArrayList<>();
        for (CisOrderExecLimit cisOrderExecLimit : cisOrderExecLimits)
            tos.add(toTo(cisOrderExecLimit, withAllParts));
        return tos;
    }

    public static CisOrderExecLimitTo toTo(CisOrderExecLimit cisOrderExecLimit) {
        return toTo(cisOrderExecLimit, false);
    }

    /**
     * @generated
     */
    public static CisOrderExecLimitTo toTo(CisOrderExecLimit cisOrderExecLimit, boolean withAllParts) {
        if (cisOrderExecLimit == null)
            return null;
        CisOrderExecLimitTo to = new CisOrderExecLimitTo();
        to.setId(cisOrderExecLimit.getId());
        to.setThirdStatus(cisOrderExecLimit.getThirdStatus());
        to.setNoExecFlag(cisOrderExecLimit.getNoExecFlag());
        to.setCancelExecFlag(cisOrderExecLimit.getCancelExecFlag());
        to.setRefundsFlag(cisOrderExecLimit.getRefundsFlag());

        if (withAllParts) {
        }
        return to;
    }

}