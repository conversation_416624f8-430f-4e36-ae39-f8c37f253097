package com.bjgoodwill.hip.ds.cis.apply.spcobs.repository;

import com.bjgoodwill.hip.ds.cis.apply.detail.entity.BaseDetail;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApplyDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.apply.spcobs.repository.CisSpcobsApplyDetailRepository")
public interface CisSpcobsApplyDetailRepository extends JpaRepository<CisSpcobsApplyDetail, String>, JpaSpecificationExecutor<CisSpcobsApplyDetail> {

    List<CisSpcobsApplyDetail> findByApplyId(String cisSpcobsApplyId);

    List<CisSpcobsApplyDetail> findByApplyIdIn(List<String> applyIds);

    Page<CisSpcobsApplyDetail> findByApplyId(String cisSpcobsApplyId, Pageable pageable);

    boolean existsByApplyId(String cisSpcobsApplyId);

    void deleteByApplyId(String cisSpcobsApplyId);

    List<CisSpcobsApplyDetail> findCisSpcobsApplyDetailsByVisitCode(String visitCode);

    @Query("select a from CisSpcobsApplyDetail a where a.createdDate >= :dateTime or :dateTime is null")
    List<BaseDetail> queryDetailsByCreateDate(LocalDateTime dateTime);
}