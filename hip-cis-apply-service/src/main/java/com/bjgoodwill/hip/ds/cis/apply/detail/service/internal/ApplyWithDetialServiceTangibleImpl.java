package com.bjgoodwill.hip.ds.cis.apply.detail.service.internal;

import com.bjgoodwill.hip.ds.cis.apply.detail.service.ApplyWithDetialTangibleService;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailTo;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-09-25 14:07
 * @className: ApplyWithDetialServiceTangibleImpl
 * @description:
 **/
@RestController("com.bjgoodwill.hip.ds.cis.apply.detail.service.ApplyWithDetialService")
@RequestMapping(value = "/api/apply/detail/ApplyWithDetialService", produces = "application/json; charset=utf-8")
@Tag(name = "抽象父类.带明细的申请单", description = "抽象父类.带明细的申请单")
public class ApplyWithDetialServiceTangibleImpl extends ApplyWithDetialServiceImpl implements ApplyWithDetialTangibleService {
    @Override
    public List<DetailTo> queryDetailToByCreateDate(LocalDateTime dateTime) {
        return List.of();
    }
}