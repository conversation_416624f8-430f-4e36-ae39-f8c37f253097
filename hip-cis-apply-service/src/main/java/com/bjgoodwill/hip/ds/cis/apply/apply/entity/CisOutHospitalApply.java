package com.bjgoodwill.hip.ds.cis.apply.apply.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisOutHospitalApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "出院申请单")
@DiscriminatorValue("16")
public class CisOutHospitalApply extends CisBaseApply {

    // 出院时间
    private LocalDateTime outDate;
    // 出院方式 字典DischargeWay
    private String dischargeDisposition;

    public static Optional<CisOutHospitalApply> getCisOutHospitalApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisOutHospitalApply> getCisOutHospitalApplies(CisOutHospitalApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisOutHospitalApply> getCisOutHospitalApplyPage(CisOutHospitalApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisOutHospitalApply> getSpecification(CisOutHospitalApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            return predicate;
        };
    }

    private static CisOutHospitalApplyRepository dao() {
        return SpringUtil.getBean(CisOutHospitalApplyRepository.class);
    }

    @Comment("出院时间")
    @Column(name = "out_date", nullable = true)
    public LocalDateTime getOutDate() {
        return outDate;
    }

    protected void setOutDate(LocalDateTime outDate) {
        this.outDate = outDate;
    }

    @Comment("出院方式 字典DischargeWay")
    @Column(name = "discharge_disposition", nullable = true)
    public String getDischargeDisposition() {
        return dischargeDisposition;
    }

    protected void setDischargeDisposition(String dischargeDisposition) {
        this.dischargeDisposition = dischargeDisposition;
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.OUTHOSPITAL;
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisOutHospitalApplyNto) cisBaseApplyNto, save);
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisOutHospitalApplyEto) cisBaseApplyEto);
    }

    public CisOutHospitalApply create(CisOutHospitalApplyNto cisOutHospitalApplyNto, Boolean save) {
        BusinessAssert.notNull(cisOutHospitalApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisOutHospitalApplyNto不能为空！");
        super.create(cisOutHospitalApplyNto, save);

        BusinessAssert.hasText(cisOutHospitalApplyNto.getServiceItemCode(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "serviceItemCode");
        setServiceItemCode(cisOutHospitalApplyNto.getServiceItemCode());

        setOutDate(cisOutHospitalApplyNto.getOutDate());
        setDischargeDisposition(cisOutHospitalApplyNto.getDischargeDisposition());
        if (save) {
            dao().save(this);
        }
        return this;
    }

    public void update(CisOutHospitalApplyEto cisOutHospitalApplyEto) {
        super.update(cisOutHospitalApplyEto);
        setOutDate(cisOutHospitalApplyEto.getOutDate());
        setDischargeDisposition(cisOutHospitalApplyEto.getDischargeDisposition());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
