package com.bjgoodwill.hip.ds.cis.apply.proxy;

import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.service.CisAntimicrobialsSkinExecReportService;
import com.bjgoodwill.hip.ds.cis.cdr.antimicrobials.to.CisAntimicrobialsSkinExecReportNto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-12-23 15:06
 * @className: CisCdrServiceProxy
 * @description:
 **/
@Component
public class CisCdrServiceProxy {
    @Autowired
    private CisAntimicrobialsSkinExecReportService cisAntimicrobialsSkinExecReportService;

    public void createCisAntimicrobialsSkinExecReport(List<CisAntimicrobialsSkinExecReportNto> ntos) {
        if (!CollectionUtils.isEmpty(ntos)) {
            cisAntimicrobialsSkinExecReportService.createCisAntimicrobialsSkinExecReport(ntos);
        }
    }
}