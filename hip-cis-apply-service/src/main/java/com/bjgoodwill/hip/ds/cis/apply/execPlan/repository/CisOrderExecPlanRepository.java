package com.bjgoodwill.hip.ds.cis.apply.execPlan.repository;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository("com.bjgoodwill.hip.ds.cis.apply.execPlan.repository.CisOrderExecPlanRepository")
public interface CisOrderExecPlanRepository extends JpaRepository<CisOrderExecPlan, String>, JpaSpecificationExecutor<CisOrderExecPlan> {

    List<CisOrderExecPlan> findByCisBaseApplyId(String cisBaseApplyId);

    @Query(value = "SELECT a.cisBaseApplyId, max(a.execPlanDate) as execPlanDate FROM CisOrderExecPlan a WHERE a.visitCode = ?1 and a.execPlanDate >?2 GROUP BY a.cisBaseApplyId ")
    List<Object[]> findByCisBaseApplyIdOrderByExecPlanDateDesc(String visitCode, LocalDateTime endDate);

    Page<CisOrderExecPlan> findByCisBaseApplyId(String cisBaseApplyId, Pageable pageable);

    boolean existsByCisBaseApplyId(String cisBaseApplyId);

    void deleteByCisBaseApplyId(String cisBaseApplyId);

    List<CisOrderExecPlan> findByVisitCode(String visitCode);

    List<CisOrderExecPlan> findCisOrderExecPlansByDeptNurseCodeAndStatusCodeIn(String deptNurseCode, CisStatusEnum[] statusCode);

    List<CisOrderExecPlan> findCisOrderExecPlansByOrderIdIn(List<String> ids);

    Optional<CisOrderExecPlan> findTopByCisBaseApplyIdOrderByExecPlanDateDesc(String cisBaseApplyId);

    @Query(value = "select a from CisOrderExecPlan a where a.orderClass in('EDRUG','CDRUG','MATERIAL','SKIN') and a.deptNurseCode = ?1 and (a.statusCode = ?2 or ?2 is null) and (a.execPlanDate < ?3) ")
    List<CisOrderExecPlan> findExecCisOrderExecPlansDeptNurseCode(String deptNurseCode, CisStatusEnum statusCode, LocalDateTime endDate);

    @Query(value = "select a from CisOrderExecPlan a where a.orderClass in('EDRUG','CDRUG','MATERIAL','SKIN')and a.deptNurseCode = ?1 and (a.statusCode = ?2 or ?2 is null) and a.execPlanDate >= ?3 and a.execPlanDate < ?4 ")
    List<CisOrderExecPlan> findExecCisOrderExecPlansExecutedDeptNurseCode(String deptNurseCode, CisStatusEnum statusCode, LocalDateTime startDate, LocalDateTime endDate);

    @Query(value = "select a from CisOrderExecPlan a where a.execOrgCode = ?1 and (a.statusCode = ?2 or ?2 is null) and (a.execPlanDate < ?3) ")
    List<CisOrderExecPlan> findExecCisOrderExecPlans(String execOrgCode, CisStatusEnum statusCode, LocalDateTime endDate);

    @Query(value = "select a from CisOrderExecPlan a where a.execOrgCode = ?1 and (a.statusCode = ?2 or ?2 is null) and a.execPlanDate >= ?3 and a.execPlanDate < ?4 ")
    List<CisOrderExecPlan> findExecCisOrderExecPlansExecuted(String execOrgCode, CisStatusEnum statusCode, LocalDateTime startDate, LocalDateTime endDate);

    @Query(value = "select a,b.orderType as orderType from CisOrderExecPlan a,CisBaseApply b where a.cisBaseApplyId = b.id and" +
            " a.deptNurseCode = ?1 and a.statusCode = ?2 and  a.execPlanDate < ?3 and (b.orderType = ?4 or ?4 is null ) and (a.receiveOrg = ?5 or ?5 is null ) " +
            " and a.orderClass = ?6 and a.isSend = false ")
    List<CisOrderExecPlan> findExecCisOrderDrugPlans(String deptNurseCode, CisStatusEnum statusCode, LocalDateTime endDate, OrderTypeEnum orderType,
                                                     String receiveOrg, SystemTypeEnum orderClass);

    List<CisOrderExecPlan> findCisOrderExecPlansByVisitCodeAndStatusCode(String visitCode, CisStatusEnum statusCode);

    List<CisOrderExecPlan> findCisOrderExecPlansByVisitCodeAndIsCharge(String visitCode, Boolean isCharge);

    List<CisOrderExecPlan> findByOrderId(String orderId);

    List<CisOrderExecPlan> findCisOrderExecPlansByVisitCodeAndCisBaseApplyIdInAndStatusCode(String vistCode, List<String> cisBaseApplyIds, CisStatusEnum statusCode);

    List<CisOrderExecPlan> findCisOrderExecPlansByIdIn(List<String> ids);

    @Query(value = "select a from CisOrderExecPlan a where a.createdDate > ?1 and (a.statusCode = 'NEW' or a.statusCode = 'EXCUTE') and a.orderClass = 'CONSULTATION' ")
    List<CisOrderExecPlan> findConsultationApply(LocalDateTime dateTime);

    @Query(value = "select a from CisOrderExecPlan a,CisDrugApplyDetail d where a.cisBaseApplyId = d.applyId and a.orderClass = 'EDRUG' and d.antimicrobialsPurpose is not null and a.id in (?1) and (a.skinResult is null or a.skinResult ='FEMININE') ")
    List<CisOrderExecPlan> findExecCisOrderExecPlansAntimicrobialsByIds(List<String> ids);

    @Query(value = "select a from CisOrderExecPlan a where a.orderClass = 'SKIN' and a.id in (?1) and (a.skinResult is null or a.skinResult ='FEMININE') ")
    List<CisOrderExecPlan> findExecCisOrderExecPlansSkinByIds(List<String> ids);

    @Query(value = "select a from CisOrderExecPlan a where a.statusCode = ?1 and a.execPlanDate >= ?2 and a.execPlanDate < ?3 ")
    List<CisOrderExecPlan> findCisOrderExecPlanNoExes(CisStatusEnum statusCode, LocalDateTime beginDate, LocalDateTime endDate);

    @Query(value = "select a from CisOrderExecPlan a,CisBaseApply c where a.cisBaseApplyId = c.id and c.statusCode !=  'VOID' and a.statusCode in ?1 and a.execPlanDate >= ?2 and a.execPlanDate < ?3 and a.execOrgCode = ?4 ")
    List<CisOrderExecPlan> findCisOrderExecPlanWithRange(CisStatusEnum[] statusCode, LocalDateTime beginDate, LocalDateTime endDate, String execCode);

    @Query(value = "select a from CisOrderExecPlan a  where a.mainPlanFlag  and a.createdDate >= :dateTime or :dateTime is null ")
    List<CisOrderExecPlan> findCisOrderExecPlanByCreatedDate(LocalDateTime dateTime);

}