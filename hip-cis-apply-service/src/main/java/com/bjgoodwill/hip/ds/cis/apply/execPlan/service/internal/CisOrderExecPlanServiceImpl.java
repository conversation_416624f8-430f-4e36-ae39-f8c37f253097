package com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal;

import cn.hutool.core.collection.CollUtil;
import com.bjgoodwill.hip.business.util.cis.common.BaseDictElementTo;
import com.bjgoodwill.hip.business.util.cis.common.CisOrgCommonNto;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.business.util.drug.enums.DrugIpdDataStatusEnum;
import com.bjgoodwill.hip.business.util.econ.enums.SetlStasEnum;
import com.bjgoodwill.hip.business.util.mq.to.cis.CisAntimicrobialsSkinExecMqNto;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisDetailQueryService;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.ExecuteService;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.TempprarysplitService;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisBaseApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyQto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApply;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlanCharge;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.CisOrderExecPlanService;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanCustomAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.*;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.entity.CisOrderExecLimit;
import com.bjgoodwill.hip.ds.cis.apply.mq.send.CisApplyMqSend;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApply;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-08 17:28
 * @className: CisOrderExecPlanServiceImpl
 * @description:
 **/
@RestController("com.bjgoodwill.hip.ds.cis.apply.execPlan.service.CisOrderExecPlanService")
@RequestMapping(value = "/api/apply/execPlan/service/CisOrderExecPlanService", produces = "application/json; charset=utf-8")
public class CisOrderExecPlanServiceImpl implements CisOrderExecPlanService {

    @Autowired
    private TempprarysplitService tempprarysplitService;

    @Autowired
    private CisApplyMqSend cisApplyMqSend;

    @Autowired
    private ExecuteService executeService;
    @Autowired
    private DictElementService dictElementService;

    @Autowired
    private CisDetailQueryService cisDetailQueryService;

    //执行
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void executeCisOrderExecPlan(String id, CisOrgCommonNto cisOrgCommonNto) {
        Optional<CisOrderExecPlan> cisOrderExecPlan = CisOrderExecPlan.getCisOrderExecPlanById(id);
        cisOrderExecPlan.ifPresent(p -> {
            // 校验申请单状态
            executeService.verifyStatus(Arrays.asList(p), CisStatusEnum.COMPLETED);
            p.exec();
            if (Boolean.TRUE.equals(p.getMainPlanFlag())) {
                cisApplyMqSend.OrderStatueSend(p, CisStatusEnum.COMPLETED, cisOrgCommonNto);
                // 回写医嘱扩展信息
                executeService.writeIpdOrderExt(p);
            }
        });
        // 调用把皮试药和抗菌药写入cdr
        exexPlansWriteCdr(Arrays.asList(id));
    }

    //取消执行
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void cancelCisOrderExecPlan(String id, CisOrgCommonNto cisOrgCommonNto) {


        Optional<CisOrderExecPlan> cisOrderExecPlan = CisOrderExecPlan.getCisOrderExecPlanById(id);
        cisOrderExecPlan.ifPresent(p -> {
            //region 第三方状态操作判断。
            List<String> thridStatues = CisOrderExecLimit.findCancelExecByExecPlanIds(new ArrayList<>(Collections.singletonList(id)))
                    .stream().map(CisOrderExecLimit::getThirdStatus).toList();
            executeService.verifyStatus(Arrays.asList(p), CisStatusEnum.CANCELEXCUTE);

            //endregion
            p.cancel();
            if (Boolean.TRUE.equals(p.getMainPlanFlag())) {
                cisApplyMqSend.OrderStatueSend(p, CisStatusEnum.PASS, cisOrgCommonNto);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void cancelExecuteCisOrderExecPlanBatch(String deptNurseCode, List<String> execIds, CisOrgCommonNto cisOrgCommonNto) {

        //执行护理计划并获取执行结果
        List<CisOrderExecPlan> items = execCisOrderExecPlan(deptNurseCode, execIds, CisOrderExecPlan::cancel,
                new CisStatusEnum[]{CisStatusEnum.COMPLETED}, new ArrayList<>());
        executeService.verifyStatus(items, CisStatusEnum.CANCELEXCUTE);

        //对执行结果中的主计划发送完成状态通知
        items.stream().filter(CisOrderExecPlan::getMainPlanFlag).forEach(p -> {
            cisApplyMqSend.OrderStatueSend(p, CisStatusEnum.PASS, cisOrgCommonNto);
        });
    }

    //批量执行

    /**
     * 执行护理组的执行计划批量操作。该方法主要用于处理和执行一批特定的护理执行计划。
     * 它首先验证输入的部门护士代码和执行单ID列表，然后根据这些ID过滤出需要执行的护理计划，
     * 最后执行这些计划并发送执行结果。
     *
     * @param deptNurseCode 部门护士代码，用于标识特定的护理组。
     * @param execIds       执行单ID列表，表示需要批量执行的护理计划的标识。
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void executeCisOrderExecPlanBatch(String deptNurseCode, List<String> execIds, CisOrgCommonNto cisOrgCommonNto) {

        //执行护理计划并获取执行结果
        List<CisOrderExecPlan> items = execCisOrderExecPlan(deptNurseCode, execIds, CisOrderExecPlan::exec,
                new CisStatusEnum[]{CisStatusEnum.NEW, CisStatusEnum.CANCELEXCUTE, CisStatusEnum.REJECT}, new ArrayList<>());
        // 校验申请单状态
        executeService.verifyStatus(items, CisStatusEnum.COMPLETED);
        //对执行结果中的主计划发送完成状态通知
        items.stream().filter(CisOrderExecPlan::getMainPlanFlag).forEach(p -> {
            cisApplyMqSend.OrderStatueSend(p, CisStatusEnum.COMPLETED, cisOrgCommonNto);
            // 回写医嘱扩展信息
            executeService.writeIpdOrderExt(p);
        });
        // 调用把皮试药和抗菌药写入cdr
        exexPlansWriteCdr(execIds);
    }

    /*
     * 将抗菌药皮试药写入CDR
     */
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void exexPlansWriteCdr(List<String> execIds) {
        // 查询皮试和抗菌药的执行计划
        List<CisAntimicrobialsSkinExecMqNto> ntos = Stream.concat(
                CisOrderExecPlan.findExecCisOrderExecPlansAntimicrobialsByIds(execIds).stream(),
                CisOrderExecPlan.findExecCisOrderExecPlansSkinByIds(execIds).stream()
        ).map(a -> {
            CisAntimicrobialsSkinExecMqNto nto = new CisAntimicrobialsSkinExecMqNto();
            nto.setId(HIPIDUtil.getNextIdString());
            nto.setVisitCode(a.getVisitCode());
            nto.setPatMiCode(a.getPatMiCode());
            nto.setVisitType(VisitTypeEnum.IPD);
            nto.setSystemType(a.getOrderClass());
            nto.setServiceItemCode(a.getServiceItemCode());
            nto.setServiceItemName(a.getServiceItemName());
            nto.setOrderId(a.getOrderId());
            nto.setApplyId(a.getCisBaseApplyId());
            nto.setExecPlanId(a.getId());
            nto.setExecPlanDate(LocalDateUtil.now());
            return nto;
        }).collect(Collectors.toList());

        ntos.forEach(nto -> cisApplyMqSend.CisAntimicrobialsSkinExecCreateSend(nto));
    }

    /**
     * 根据护士查询条件获取已执行的医嘱执行计划列表
     *
     * @param qto 查询条件对象，包含部门护士代码、状态代码、结束日期和医嘱类型等信息
     * @return 医嘱执行计划的TO列表
     * <p>
     * 此方法首先通过部门护士代码和其他查询参数从数据库中查找已执行的医嘱执行计划
     * 如果找到了一组医嘱执行计划，并且查询条件中包含了访视代码列表，则进一步筛选出访视代码在查询条件列表中的医嘱执行计划
     * 最后，将医嘱执行计划实体列表转换为传输对象（TO）列表以供返回
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> getExecedCisOrderExecPlan(CisOrderExecPlanNurseQto qto) {
        // 查找已执行的医嘱执行计划
        List<CisOrderExecPlan> items = Optional.ofNullable(qto.getStartDate())
                .map(startDate -> Stream.concat(CisOrderExecPlan.findExecCisOrderExecPlansExecuted(qto.getExecOrgCode(), qto.getStatusCode(), startDate, qto.getEndDate()).stream(),
                        CisOrderExecPlan.findExecCisOrderExecPlansExecutedDeptNurseCode(qto.getDeptNurseCode(), qto.getStatusCode(), startDate, qto.getEndDate()).stream())
                ).orElseGet(() -> Stream.concat(CisOrderExecPlan.findExecCisOrderExecPlans(qto.getExecOrgCode(), qto.getStatusCode(), qto.getEndDate()).stream(),
                        CisOrderExecPlan.findExecCisOrderExecPlansDeptNurseCode(qto.getDeptNurseCode(), qto.getStatusCode(), qto.getEndDate()).stream())).toList();

        // 如果医嘱执行计划列表非空且查询条件中包含访视代码列表，则进行筛选
        if (!CollectionUtils.isEmpty(items) && !CollectionUtils.isEmpty(qto.getVisitCode())) {
            // 筛选访视代码在查询条件列表中的医嘱执行计划
            items = items.stream()
                    .filter(cisOrderExecPlan -> qto.getVisitCode().contains(cisOrderExecPlan.getVisitCode()))
                    .toList();
        }
        ;
        items = items.stream().filter(a -> !List.of(SystemTypeEnum.OUTHOSPITAL, SystemTypeEnum.CHANGEDEPT, SystemTypeEnum.PATIENT, SystemTypeEnum.LINE).contains(a.getOrderClass())).toList();
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        //region 查询申请单获取临时，长期属性
        List<String> applyIds = items.stream().map(CisOrderExecPlan::getCisBaseApplyId).distinct().toList();
        List<Object[]> applyList = CisBaseApply.findCisBaseApplyOrderType(applyIds);
        Map<String, OrderTypeEnum> map = applyList.stream()
                .collect(Collectors.toMap(apply -> (String) apply[0], apply -> (OrderTypeEnum) apply[1]));
        //endregion

        // 将医嘱执行计划实体列表转换为传输对象（TO）列表并返回
        List<CisOrderExecPlanTo> tos = CisOrderExecPlanAssembler.toTos(items, true);
        tos.forEach(to -> to.setOrderType(map.get(to.getCisBaseApplyId()) == null ? null : map.get(to.getCisBaseApplyId())));
        if (qto.getOrderType() != null) {
            tos = tos.stream().filter(to -> qto.getOrderType().equals(to.getOrderType())).toList();
        }
        return tos;
    }


    /**
     * 更新执行单计费状态
     *
     * @param cisOrderExecPlanChangeBackEtos 待修改的电路订单执行计划变更回退实体列表
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderExecPlanCharge(List<CisOrderExecPlanChangeBackEto> cisOrderExecPlanChangeBackEtos) {

        // 确保待修改的订单执行计划变更回退实体列表不为空
        BusinessAssert.notEmpty(cisOrderExecPlanChangeBackEtos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "修改数据");

        // 提取订单执行计划ID列表
        List<String> ids = cisOrderExecPlanChangeBackEtos.stream().flatMap(Stream::ofNullable)
                .map(CisOrderExecPlanChangeBackEto::getExecPlanId).toList();

        List<CisOrderExecPlan> items = new ArrayList<>();
        Lists.partition(ids, 100).stream().forEach(p -> items.addAll(CisOrderExecPlan.findExecCisOrderExecPlans(ids)));
        // 根据订单ID查询对应的电路订单执行计划，并将其ID映射为键，对象为值，生成Map
        Map<String, CisOrderExecPlan> map = items
                .stream().collect(Collectors.toMap(CisOrderExecPlan::getId, item -> item));

        // 遍历待修改的订单执行计划变更回退实体列表
        cisOrderExecPlanChangeBackEtos.parallelStream().filter(a -> StringUtil.isNotEmpty(a.getExecPlanId())).forEach(eto -> {
            // 根据执行计划ID获取对应的电路订单执行计划对象
            CisOrderExecPlan item = map.get(eto.getExecPlanId());

            // 确保电路订单执行计划对象不为空
            BusinessAssert.notNull(item, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, eto.getExecPlanId());
            if (eto.getIsSend() != null) {
                BusinessAssert.isFalse(eto.getIsSend().equals(item.getIsSend()), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0008, item.getId());
            }

            // 更新订单执行计划对象的数据
            item.update(eto);
            // 申请领药记录医嘱流程日志
            extractedLogSend(item, eto);
        });
        // 处理整取药用orderId
        List<String> orderIds = cisOrderExecPlanChangeBackEtos.stream().filter(a -> StringUtil.isNotEmpty(a.getOrderId())).map(CisOrderExecPlanChangeBackEto::getOrderId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderIds)) {
            List<CisOrderExecPlan> exePlans = CisOrderExecPlan.findCisOrderExecPlansByOrderIdIn(orderIds);
            Map<String, List<CisOrderExecPlan>> groupedByOrderId = exePlans.stream().collect(Collectors.groupingBy(CisOrderExecPlan::getOrderId));
            cisOrderExecPlanChangeBackEtos.stream().filter(a -> StringUtil.isNotEmpty(a.getOrderId())).forEach(eto -> {
                List<CisOrderExecPlan> item = groupedByOrderId.get(eto.getOrderId());
                // 确保电路订单执行计划对象不为空
                BusinessAssert.notEmpty(item, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, StringUtil.join(item, ","));
                // 更新订单执行计划对象的数据
                item.forEach(c -> c.update(eto));
                // 申请领药记录医嘱流程日志
                extractedLogSend(item.get(0), eto);
            });
        }
    }

    private void extractedLogSend(CisOrderExecPlan item, CisOrderExecPlanChangeBackEto eto) {
        if (List.of(SystemTypeEnum.EDRUG, SystemTypeEnum.CDRUG).contains(item.getOrderClass()) && eto.getIsSend() != null && eto.getIsSend()
                && StringUtil.isNotEmpty(item.getOrgCode())) {
            CisOrgCommonNto nto = new CisOrgCommonNto();
            nto.setOrgCode(eto.getOrgCode());
            nto.setOrgName(eto.getOrgName());
            cisApplyMqSend.ApplyDrugLogSend(item, nto);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findByVisitCode(String visitCode) {
        return CisOrderExecPlanAssembler.toTos(CisOrderExecPlan.findByVisitCode(visitCode), true);
    }


    /**
     * 修复费用相关的CIS订单执行计划
     * <p>
     * 此方法主要用于处理和修复与CIS订单执行计划相关的费用问题通过对列表进行分组和迭代，
     * 对每个订单执行计划进行详细的修复操作
     *
     * @param cisOrderExecPlanChargeNtos 一个包含CisOrderExecPlanChargeNto对象的列表，不能为空
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void repairFeeCisOrderExecPlans(List<CisOrderExecPlanChargeNto> cisOrderExecPlanChargeNtos) {
        // 确保传入的列表不为空，否则抛出异常
        BusinessAssert.notEmpty(cisOrderExecPlanChargeNtos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "费用");

        // 对传入的列表按cisBaseApplyId进行分组，以便后续处理
        cisOrderExecPlanChargeNtos.stream()
                .collect(Collectors.groupingBy(CisOrderExecPlanChargeNto::getCisBaseApplyId))
                .forEach((key, value) -> {
                    // 对分组后的每个列表调用修复方法
                    repairFeeCisOrderExecPlan(value);
                });

    }


    /**
     * 修复费用执行计划中的费用分配
     * 该方法根据执行机构代码对费用列表进行分组，并为每个执行机构创建或更新执行计划
     * 还负责为每个费用项创建对应的执行计划费用记录
     *
     * @param cisOrderExecPlanChargeNtos 费用执行计划费用列表
     */
    private void repairFeeCisOrderExecPlan(List<CisOrderExecPlanChargeNto> cisOrderExecPlanChargeNtos) {
        // 根据执行机构代码对费用列表进行分组
        Map<String, List<CisOrderExecPlanChargeNto>> map =
                cisOrderExecPlanChargeNtos.stream().collect(Collectors.groupingBy(CisOrderExecPlanChargeNto::getExecuteOrgCode));

        // 获取申请单ID
        String applyId = cisOrderExecPlanChargeNtos.get(0).getCisBaseApplyId();
        // 根据申请单ID获取申请单对象
        CisBaseApply cisBaseApply = CisBaseApply.getCisBaseApplyById(applyId).orElse(null);
        // 校验申请单是否存在
        BusinessAssert.isNull(cisBaseApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0006, "费用");
        BusinessAssert.isTrue(!CisStatusEnum.PASS.equals(cisBaseApply.getStatusCode()), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014,
                "接口为校对后补费接口！");

        BusinessAssert.isTrue(OrderTypeEnum.LONG_TERM_ORDER.equals(cisBaseApply.getOrderType()), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014,
                "校对后的长期医嘱，无法通过本方法补费！");

        // 遍历每个执行机构及其对应的费用列表
        map.forEach((key, value) -> {
            // 为当前执行机构创建或更新执行计划，并获取执行计划对象
            CisOrderExecPlan orderExecPlan = tempprarysplitService.createOrderExecPlan(cisBaseApply, LocalDateTime.now(), key, true, null);
            // 为当前执行计划创建费用记录
            value.forEach(eto -> {
                new CisOrderExecPlanCharge().create(orderExecPlan.getId(), eto, true);
            });

        });
    }


    /**
     * 执行单补费
     * <p>
     * 此方法旨在处理特定的执行计划费用问题它首先通过ID获取执行计划，
     * 确保执行计划存在，然后基于提供的费用信息创建新的执行计划费用记录，
     * 并返回更新后的执行计划信息
     *
     * @param id                        执行计划的唯一标识符用于定位特定的执行计划
     * @param cisOrderExecPlanChargeNto 包含费用信息的对象，用于创建新的执行计划费用记录
     * @return 返回更新后的执行计划对象，以传输对象的形式
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOrderExecPlanChargeTo repairFeeCisOrderExecPlan(String id, CisOrderExecPlanChargeNto cisOrderExecPlanChargeNto) {
        // 通过ID获取执行计划，如果不存在，则返回null
        CisOrderExecPlan cisOrderExecPlan = CisOrderExecPlan.getCisOrderExecPlanById(id).orElse(null);
        // 校验执行计划是否为空，如果为空，则抛出异常
        BusinessAssert.notNull(cisOrderExecPlan, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "执行单");
        // 创建新的执行计划费用记录
//        new CisOrderExecPlanCharge().create(id, cisOrderExecPlanChargeNto, true);
        CisOrderExecPlanCharge cisOrderExecPlanCharge = new CisOrderExecPlanCharge();
        // 返回更新后的执行计划信息
        return CisOrderExecPlanChargeAssembler.toTo(cisOrderExecPlanCharge.create(id, cisOrderExecPlanChargeNto, true));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisOrderExecPlanChargeTo> repairFeeCisOrderExecPlans(String id, List<CisOrderExecPlanChargeNto> cisOrderExecPlanChargeNtos) {
        BusinessAssert.notEmpty(cisOrderExecPlanChargeNtos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "cisOrderExecPlanChargeNtos");
        return cisOrderExecPlanChargeNtos.stream()
                .flatMap(Stream::ofNullable)
                .map(nto -> repairFeeCisOrderExecPlan(id, nto)).toList();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisOrderExecPlanChargeTo> createCisOrderExecPlanChargeBatch(String id, List<CisOrderExecPlanChargeNto> cisOrderExecPlanChargeNtos) {
        BusinessAssert.notEmpty(cisOrderExecPlanChargeNtos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "cisOrderExecPlanChargeNtos");
        return cisOrderExecPlanChargeNtos.stream()
                .filter(Objects::nonNull)
                .map(nto -> repairFeeCisOrderExecPlan(id, nto))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisOrderExecPlanChargeTo> updateCisOrderExecPlanChargeBatch(List<CisOrderExecPlanChargeEto> cisOrderExecPlanChargeEtos) {
        BusinessAssert.notEmpty(cisOrderExecPlanChargeEtos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "cisOrderExecPlanChargeEtos");
        List<String> ids = cisOrderExecPlanChargeEtos.stream().flatMap(Stream::ofNullable)
                .map(CisOrderExecPlanChargeEto::getCisOrderExecPlanId).toList();
        List<CisOrderExecPlanCharge> items = CisOrderExecPlanCharge.findByCisOrderExecPlanIdsIn(ids);
        // 根据订单ID查询对应的订单，并将其ID映射为键，对象为值，生成Map
        Map<String, CisOrderExecPlanCharge> map = items.stream().collect(Collectors.toMap(CisOrderExecPlanCharge::getId, item -> item));
        List<CisOrderExecPlanChargeTo> tos = new ArrayList<>();
        // 遍历待修改的订单执行费用变更实体列表
        cisOrderExecPlanChargeEtos.forEach(eto -> {
            // 根据执行计划ID获取对应的电路订单执行计划对象
            CisOrderExecPlanCharge item = map.get(eto.getId());
            // 确保电路订单执行计划对象不为空
            BusinessAssert.notNull(item, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, eto.getId());
            // 更新订单执行计划对象的数据
            item.update(eto);
            // 组装返回to
            tos.add(CisOrderExecPlanChargeAssembler.toTo(item, true));
        });
        return tos;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOrderExecPlanCharge(String id) {
        Optional<CisOrderExecPlanCharge> chargeOptional = CisOrderExecPlanCharge.getCisOrderExecPlanChargeById(id);
        chargeOptional.ifPresent(cisOrderExecPlanCharge -> cisOrderExecPlanCharge.delete());
    }

    //查询患者未执行的执行单。
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisOrderExecPlanTo> findNoExecCisOrderExecPlan(String visitCode) {
        return CisOrderExecPlanAssembler.toTos(CisOrderExecPlan.findNoExecCisOrderExecPlans(visitCode));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findExecCisOrderExecPlanByVisitCodeAndStatusCode(String visitCode, CisStatusEnum statusCode) {
        return CisOrderExecPlanAssembler.toTos(CisOrderExecPlan.findExecCisOrderExecPlanByVisitCodeAndStatusCode(visitCode, statusCode));
    }


    /**
     * 执行护理组的执行计划
     * <p>
     * 该方法主要用于处理护理组的执行计划，根据部门护士代码、执行单ID列表、执行操作的消费者函数、
     * 执行状态码枚举数组以及第三方状态列表来筛选和处理执行计划数据
     *
     * @param deptNurseCode 部门护士代码，用于识别特定的护理组
     * @param execIds       执行单ID列表，用于指定需要处理的执行单
     * @param func          消费者函数，用于处理每个执行计划对象
     * @param statusCode    执行状态码枚举数组，用于筛选特定状态的执行计划
     * @param thridStatues  第三方状态列表，用于进一步筛选执行计划
     * @return 返回处理后的执行计划列表
     * @throws BusinessAssert 如果输入数据不合法，抛出业务异常
     */
    private List<CisOrderExecPlan> execCisOrderExecPlan(String deptNurseCode, List<String> execIds, Consumer<CisOrderExecPlan> func,
                                                        CisStatusEnum[] statusCode, List<String> thridStatues) {
        // 确保执行单列表不为空，否则抛出指定的业务异常
        BusinessAssert.notEmpty(execIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "执行单");

        List<CisOrderExecPlan> cisOrderExecPlans = CisOrderExecPlan.findExecCisOrderExecPlans(execIds)
                .stream().filter(cisOrderExecPlan -> Arrays.asList(statusCode).contains(cisOrderExecPlan.getStatusCode())).toList();
        // 确保查询到的执行单列表不为空，否则抛出指定的业务异常
        BusinessAssert.notEmpty(cisOrderExecPlans, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "执行单");

        // 如果提供了第三方状态列表，进行进一步的数据筛选和校验
        if (CollUtil.isNotEmpty(thridStatues)) {
            // 获取符合第三方状态的执行计划的服务项目名称列表
            List<String> names = cisOrderExecPlans.stream().filter(p -> thridStatues.contains(p.getThridStatus())).map(CisOrderExecPlan::getServiceItemName).toList();
            // 确保名称列表中没有空元素，否则抛出指定的业务异常
            BusinessAssert.noNullElements(names, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00015,
                    names, "操作");
        }

        // 对每个执行计划应用提供的消费者函数进行处理
        cisOrderExecPlans.forEach(func);

        // 返回处理后的执行计划列表
        return cisOrderExecPlans;
    }


    //批量不执行
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void noExecuteCisOrderExecPlanBatch(String deptNurseCode, List<String> execIds, CisOrgCommonNto cisOrgCommonNto) {

        List<String> thridStatues = CisOrderExecLimit.findNoExecByExecPlanIds(execIds).stream().map(CisOrderExecLimit::getThirdStatus).toList();

        var items = execCisOrderExecPlan(deptNurseCode, execIds, CisOrderExecPlan::noExec,
                new CisStatusEnum[]{CisStatusEnum.NEW}, thridStatues);

        items.stream().filter(CisOrderExecPlan::getMainPlanFlag).forEach(p -> {
            cisApplyMqSend.OrderStatueSend(p, CisStatusEnum.PASS, cisOrgCommonNto);
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void anewExecuteCisOrderExecPlanBatch(String deptNurseCode, List<String> execIds, CisOrgCommonNto cisOrgCommonNto) {
        execCisOrderExecPlan(deptNurseCode, execIds, CisOrderExecPlan::anewExec,
                new CisStatusEnum[]{CisStatusEnum.REJECT, CisStatusEnum.CANCELEXCUTE}, new ArrayList<>());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public String specialExecuteCisOrderExecPlanBatch(String visitCode, SystemTypeEnum systemType, CisOrgCommonNto cisOrgCommonNto) {
        BusinessAssert.isTrue(List.of(SystemTypeEnum.OUTHOSPITAL, SystemTypeEnum.CHANGEDEPT).contains(systemType), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数systemType类型不正确！");
        CisBaseApplyQto qto = new CisBaseApplyQto();
        qto.setStatusCode(CisStatusEnum.PASS);
        qto.setVisitCode(visitCode);
        qto.setOrderClass(systemType.getCode());
        CisBaseApply apply = CisBaseApply.getCisBaseApplies(qto).stream().findFirst().orElse(null);

        BusinessAssert.notNull(apply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "未查询到有效数据！");

        CisOrderExecPlan.getByCisBaseApplyId(apply.getId()).stream().forEach(p -> executeCisOrderExecPlan(p.getId(), cisOrgCommonNto));
        return apply.getOrderID();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public String specialCancelExecuteCisOrderExecPlanBatch(String visitCode, SystemTypeEnum systemType, CisOrgCommonNto cisOrgCommonNto) {
        BusinessAssert.isTrue(List.of(SystemTypeEnum.OUTHOSPITAL, SystemTypeEnum.CHANGEDEPT).contains(systemType), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数systemType类型不正确！");
        CisBaseApplyQto qto = new CisBaseApplyQto();
        qto.setStatusCode(CisStatusEnum.COMPLETED);
        qto.setVisitCode(visitCode);
        qto.setOrderClass(systemType.getCode());
        CisBaseApply apply = CisBaseApply.getCisBaseApplies(qto).stream().findFirst().orElse(null);

        BusinessAssert.notNull(apply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "未查询到有效数据！");

        CisOrderExecPlan.getByCisBaseApplyId(apply.getId()).stream().forEach(p -> cancelCisOrderExecPlan(p.getId(), cisOrgCommonNto));
        return apply.getOrderID();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanChargeTo> findCisOrderExecPlanChargeByExecId(String execId) {
        return CisOrderExecPlanChargeAssembler.toTos(CisOrderExecPlanCharge.getByCisOrderExecPlanId(execId));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findCisOrderExecPlanByPlanChargeId(String chargeId) {
        return CisOrderExecPlanCharge.getCisOrderExecPlanChargeById(chargeId)
                .map(charge -> charge.getCisOrderExecPlanId())
                .flatMap(execPlanId -> Optional.ofNullable(CisOrderExecPlan.findExecCisOrderExecPlans(Collections.singletonList(execPlanId))))
                .map(list -> CisOrderExecPlanAssembler.toTos(list, true))
                .orElse(Collections.emptyList());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanChargeTo> findCisOrderExecPlanChargeByOrderId(List<String> applyIds) {
        List<CisOrderExecPlanCharge> planCharges = CisOrderExecPlanCharge.findByCisBaseApplyIdIn(applyIds);
        return CisOrderExecPlanChargeAssembler.toTos(planCharges);
    }

    //
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findCisOrderExecPlanByOrderId(String orderId) {
        return CisOrderExecPlanAssembler.toTos(
                CisOrderExecPlan.findCisOrderExecPlansByOrderIdIn(Arrays.asList(orderId)), true);

    }


    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findCisOrderExecPlanInOrderIds(List<String> orderIds) {
        return CisOrderExecPlanAssembler.toTos(
                CisOrderExecPlan.findCisOrderExecPlansByOrderIdIn(orderIds));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findCisOrderExecPlanInExeIds(List<String> exeIds) {
        return CisOrderExecPlanAssembler.toTos(
                CisOrderExecPlan.findExecCisOrderExecPlans(exeIds), true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisOrderExecPlanTo> findNoChargeCisOrderExecPlan(String visitCode, Boolean isCharge) {
        //findNoChargeCisOrderExecPlans
        return CisOrderExecPlanAssembler.toTos(
                CisOrderExecPlan.findNoChargeCisOrderExecPlans(visitCode, isCharge));
    }


//    public List<CisOrderExecLimit> orderPlanExecLimit(List<CisOrderExecPlan> cisOrderExecPlans){
//        var items = cisOrderExecPlans.stream().filter(p-> StringUtil.isNotBlank(p.getThridStatus())).toList();
//        List<CisOrderExecLimit> limits = CisOrderExecLimit.findAll();
//
//        if(CollectionUtils.isEmpty(limits)){
//            return Collections.emptyList();
//        }
//
//        List<String> limitIds = items.stream().map( CisOrderExecPlan::getThridStatus).distinct().toList();
//
//        return limits.stream().filter(p-> limitIds.contains(p.getThirdStatus())).toList();
//
//    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOrderExecPlanTo DoubleSign(String planId, String stStaffA, String stStaffB) {
        CisOrderExecPlan cisOrderExecPlan = CisOrderExecPlan.getCisOrderExecPlanById(planId).orElse(null);
        if (cisOrderExecPlan != null) {
            cisOrderExecPlan.doubleSign(stStaffA, stStaffB);
        }
        return CisOrderExecPlanAssembler.toTo(cisOrderExecPlan, true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> getCisOrderExecPlanForPrescription(List<String> visitCodes, String deptNurseCode, String receiveOrg, LocalDateTime execPlanDate) {
        List<CisOrderExecPlanTo> cisOrderExecPlanList = CisOrderExecPlanAssembler.toTos(CisOrderExecPlan.getCisOrderExecPlanForPrescription(visitCodes, deptNurseCode, receiveOrg, execPlanDate));
        if (!CollectionUtils.isEmpty(cisOrderExecPlanList)) {
            List<String> cisOrderExecPlanIds = cisOrderExecPlanList.stream().map(CisOrderExecPlanTo::getId).collect(Collectors.toList());
            Map<String, List<CisOrderExecPlanChargeTo>> chargesMap = CisOrderExecPlanChargeAssembler.toTos(CisOrderExecPlanCharge.findByCisOrderExecPlanIdsIn(cisOrderExecPlanIds))
                    .stream().collect(Collectors.groupingBy(CisOrderExecPlanChargeTo::getCisOrderExecPlanId));
            cisOrderExecPlanList.forEach(a -> a.setCisOrderExecPlanChargeTos(chargesMap.getOrDefault(a.getId(), new ArrayList<>())));
        }
        return cisOrderExecPlanList;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findConsultationApply(LocalDateTime dateTime) {
        return CisOrderExecPlanAssembler.toTos(CisOrderExecPlan.findConsultationApply(dateTime));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderExecPlanIsSend(List<String> execIds, Boolean isSend) {
        BusinessAssert.notEmpty(execIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "执行单ID");
        Lists.partition(execIds, 100).stream()
                .flatMap(p -> CisOrderExecPlan.findExecCisOrderExecPlans(p).stream())
                .forEach(p -> {
                    BusinessAssert.isFalse(isSend.equals(p.getIsSend()), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0008, p.getId());
                    p.updateIsSend(isSend);
                });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findCisOrderExecPlanNoExes(CisStatusEnum statusCode, LocalDateTime beginDate, LocalDateTime endDate) {
        return CisOrderExecPlanAssembler.toTos(CisOrderExecPlan.findCisOrderExecPlanNoExes(statusCode, beginDate, endDate));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void skinResultsInput(String id, CisOrderExecPlanEto cisOrderExecPlanEto) {
        Optional<CisOrderExecPlan> cisOrderExecPlanOptional = CisOrderExecPlan.findByOrderPlanId(id);
        cisOrderExecPlanOptional.ifPresent(cisOrderExecPlan -> cisOrderExecPlan.skinResultsInput(cisOrderExecPlanEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findCisOrderExecPlanPatientFee(CisOrderExecPlanNurseQto qto) {
        List<CisOrderExecPlanTo> cisOrderExecPlanList = CisOrderExecPlanAssembler.toTos(CisOrderExecPlan.findCisOrderExecPlanPatientFee(qto.getVisitCode(), qto.getDeptNurseCode(), qto.getStartDate(), qto.getEndDate()));
        cisOrderExecPlanList = cisOrderExecPlanList.stream().filter(a -> !List.of(SystemTypeEnum.OUTHOSPITAL, SystemTypeEnum.CHANGEDEPT, SystemTypeEnum.PATIENT, SystemTypeEnum.LINE).contains(a.getOrderClass())).toList();
        if (!CollectionUtils.isEmpty(cisOrderExecPlanList)) {
            List<String> cisOrderExecPlanIds = cisOrderExecPlanList.stream().map(CisOrderExecPlanTo::getId).collect(Collectors.toList());
            Map<String, List<CisOrderExecPlanChargeTo>> chargesMap = CisOrderExecPlanChargeAssembler.toTos(CisOrderExecPlanCharge.findByCisOrderExecPlanIdsIn(cisOrderExecPlanIds))
                    .stream().collect(Collectors.groupingBy(CisOrderExecPlanChargeTo::getCisOrderExecPlanId));
            cisOrderExecPlanList.forEach(a -> a.setCisOrderExecPlanChargeTos(chargesMap.getOrDefault(a.getId(), new ArrayList<>())));
        }
        return cisOrderExecPlanList;
    }

    /**
     * 根据执行科室代码和订单状态代码查找CIS订单执行计划(带申请单)，并按就诊码分组
     *
     * @param qto
     * @return 返回一个CisOrderExecPlanGroupTo对象列表，每个对象包含一个分组的就诊码和相应的执行计划列表
     * <p>
     * 此方法使用了事务注解，用于确保数据库操作的一致性，并且只读属性提高了查询性能
     * 选择使用stream API进行数据处理，因为它提供了高效且易于理解的集合操作方式
     * 当查询结果为空时，直接返回空列表，避免进行不必要的处理，提高代码效率
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanGroupTo> findCisOrderExecPlanGroupByExecDeptCode(CisOrderExecPlanMtQto qto) {
        // 查询指定状态、时间和执行科室的CIS订单执行计划
        List<CisOrderExecPlanTo> items = getCisOrderExecPlanWithMtQto(qto);
        // 如果查询结果为空，直接返回空列表
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }

        List<String> applyIds = items.stream().map(CisOrderExecPlanTo::getCisBaseApplyId).toList();
        List<CisBaseApplyTo> cisBaseApplyTo = CisBaseApplyAssembler.toTos(CisBaseApply.findCisBaseAppliesByApplyIDIn(applyIds));

        // 如果查询结果为空，直接返回空列表
        if (CollectionUtils.isEmpty(cisBaseApplyTo)) {
            return Collections.emptyList();
        }

        Map<String, List<CisOrderExecPlanTo>> mapExecPlanto =
                items.stream().collect(Collectors.groupingBy(p -> p.getCisBaseApplyId()));
        cisBaseApplyTo.forEach(p -> {
            p.setCisOrderExecPlans(mapExecPlanto.get(p.getId()));
        });

        return cisBaseApplyTo.stream().collect(Collectors.groupingBy(CisBaseApplyTo::getVisitCode))
                .entrySet().stream().map(p ->
                        new CisOrderExecPlanGroupTo(p.getKey(), p.getValue().get(0).getPatMiCode(), p.getValue()))
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisOrderExecPlanMtTo> findCisOrderExecPlanMtPages(CisOrderExecPlanMtQto qto) {
        Page<CisOrderExecPlan> page = CisOrderExecPlan.getCisOrderExecPlanWithRangePages(qto);
        Page<CisOrderExecPlanTo> result = page.map(cisOrderExecPlan -> CisOrderExecPlanAssembler.toTo(cisOrderExecPlan, true));
        List<CisOrderExecPlanMtTo> cisOrderExecPlanMtToList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result.getContent())) {
            result.getContent().forEach(p -> {
                CisOrderExecPlanMtTo cisOrderExecPlanMtTo = HIPBeanUtil.copy(p, CisOrderExecPlanMtTo.class);
                CisBaseApply cisBaseApply = CisBaseApply.getCisBaseApplyById(p.getCisBaseApplyId()).get();
                cisOrderExecPlanMtTo.setCisBaseApplyId(cisBaseApply.getId());
                cisOrderExecPlanMtTo.setCreateDateTime(cisBaseApply.getCreatedDate());
                cisOrderExecPlanMtTo.setCisOrderExecPlanChargeTos(findCisOrderExecPlanChargeByExecId(p.getId()));
                String type = "";
                if (cisBaseApply instanceof CisDgimgApply dgimgApply) {
                    type = dgimgApply.getDeviceTypeName();
                    if (StringUtils.isBlank(dgimgApply.getDeviceTypeName())) {
                        DictElementTo deviceType = dictElementService.getCustomDictElement("DgimgDeviceType", dgimgApply.getDeviceType());
                        if (deviceType != null) {
                            type = deviceType.getElementName();
                        }
                    }
                } else if (cisBaseApply instanceof CisSpcobsApply spcobsApply) {
                    type = spcobsApply.getSpecimanName();
                    if (StringUtils.isBlank(spcobsApply.getSpecimanName())) {
                        DictElementTo speciman = dictElementService.getCustomDictElement("Speciman", spcobsApply.getSpeciman());
                        if (speciman != null) {
                            type = speciman.getElementName();
                        }
                    }
                }
                cisOrderExecPlanMtTo.setType(type);
                cisOrderExecPlanMtToList.add(cisOrderExecPlanMtTo);
            });
        }
        return new GridResultSet<>(cisOrderExecPlanMtToList, page.getNumber(), page.getSize(), page.getTotalElements());
    }


    /**
     * 根据执行科室代码查找创建的组织
     * <p>
     * 该方法利用Java 8的Stream API从CIS订单执行计划中过滤出符合条件的记录，
     * 并将其映射为BaseDictElementTo对象，最终返回一个列表
     *
     * @param qto
     * @return 返回一个BaseDictElementTo对象的列表，每个对象包含符合查询条件的组织代码和名称
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<BaseDictElementTo> findCreateOrgByExecDeptCode(CisOrderExecPlanMtQto qto) {
        // 查询指定状态、时间和执行科室的CIS订单执行计划
        return getCisOrderExecPlanWithMtQto(qto)
                .stream()
                // 将每个CIS订单执行计划映射为BaseDictElementTo对象，包含组织代码和名称
                .map(v ->
                        new BaseDictElementTo(v.getOrgCode(), v.getOrgName())
                ).collect(Collectors.toSet())
                // 将集合转换为列表
                .stream().toList();
    }

    /**
     * 根据执行科室代码查找患者信息
     * 此方法通过查询指定状态、时间和执行科室的CIS订单执行计划，并根据创建科室代码过滤结果，最后返回患者的就诊代码和医疗信息代码
     *
     * @param qto
     * @return 返回一个包含患者基础字典元素的列表，每个元素包含患者的就诊代码和医疗信息代码
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<BaseDictElementTo> findPatientByExecDeptCode(CisOrderExecPlanMtQto qto) {
        // 查询指定状态、时间和执行科室的CIS订单执行计划
        List<CisOrderExecPlanTo> items = getCisOrderExecPlanWithMtQto(qto);
        // 如果查询结果为空，直接返回空列表
        if (CollectionUtils.isEmpty(items)) {
            return new GridResultSet<>();
        }
        // 将查询结果转换为包含患者基础字典元素的列表，并确保列表中没有重复的患者信息
        List<BaseDictElementTo> baseDictElementToList = items.stream().map(p ->
                new BaseDictElementTo(p.getVisitCode(), p.getPatMiCode())
        ).collect(Collectors.toSet()).stream().toList();

        Page<BaseDictElementTo> result = new PageImpl(items);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    private List<CisOrderExecPlanTo> getCisOrderExecPlanWithMtQto(CisOrderExecPlanMtQto qto) {
        // 查询指定状态、时间和执行科室的CIS订单执行计划
        return CisOrderExecPlanAssembler.toTos(
                CisOrderExecPlan.findCisOrderExecPlanWithRange(qto.getStatusCodes(), qto.getBeginDate(), qto.getEndDate(), qto.getExecDeptCode())
                        .stream()
                        .filter(e -> StringUtils.isBlank(qto.getVisitCode()) || e.getVisitCode().equals(qto.getVisitCode()))
                        .filter(p -> CollectionUtils.isEmpty(qto.getCreateOrgCodes()) || qto.getCreateOrgCodes().contains(p.getCreateOrgCode()))
                        .toList());
    }


    /**
     * 根据订单ID查找煎药中心的执行计划
     * 此方法使用事务管理，确保数据一致性
     *
     * @param orderId 订单ID，用于查找执行计划
     * @return 返回一个包含煎药中心执行计划的列表，如果没有找到则返回空列表
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findDecoctionCisOrderExecPlanByOrderId(String orderId) {
        // 创建查询传输对象并设置订单ID
        CisBaseApplyQto qto = new CisBaseApplyQto();
        qto.setOrderID(orderId);

        // 获取与订单ID相关的所有申请记录
        List<CisBaseApply> applyList = CisBaseApply.getCisBaseApplies(qto);
        if (CollectionUtils.isEmpty(applyList)) {
            return Collections.emptyList();
        }

        // 获取第一个申请记录的ID
        String applyId = applyList.get(0).getId();

        // 根据申请记录ID获取所有的执行计划收费项目
        List<CisOrderExecPlanCharge> cisOrderExecPlanCharges = CisOrderExecPlanCharge.getByCisBaseApplyId(applyId);
        if (cisOrderExecPlanCharges == null || cisOrderExecPlanCharges.isEmpty()) {
            return Collections.emptyList();
        }

        // 过滤出煎药中心的收费类型
        List<CisOrderExecPlanCharge> decoctionCharges = cisOrderExecPlanCharges.stream()
                .filter(p -> CisChargeTypeEnum.DECOCTION.equals(p.getChargeType()))
                .toList();

        if (decoctionCharges.isEmpty()) {
            return Collections.emptyList();
        }

        // 提取所有符合条件的执行计划ID
        List<String> execIds = decoctionCharges.stream()
                .map(CisOrderExecPlanCharge::getCisOrderExecPlanId)
                .toList();

        // 转换并返回所有未执行的煎药中心执行计划
        List<CisOrderExecPlanTo> cisOrderExecPlanTos = CisOrderExecPlanAssembler.toTos(
                CisOrderExecPlan.findExecCisOrderExecPlans(execIds).stream()
                        .filter(p -> !CisStatusEnum.COMPLETED.equals(p.getStatusCode()))
                        .toList());
        Map<String, List<CisOrderExecPlanChargeTo>> charges =
                CisOrderExecPlanChargeAssembler.toTos(decoctionCharges)
                        .stream().collect(Collectors.groupingBy(CisOrderExecPlanChargeTo::getCisOrderExecPlanId));
        cisOrderExecPlanTos.forEach(p -> p.setCisOrderExecPlanChargeTos(charges.get(p.getId())));
        return cisOrderExecPlanTos;
    }


    /**
     * 设置药品出入库类型
     *
     * @param id            药品执行计划的ID
     * @param drugInoutType 药品出入库类型枚举
     *                      <p>
     *                      该方法用于根据药品执行计划ID设置药品的出入库类型它首先通过ID获取药品执行计划对象，
     *                      然后通过执行计划中的申请单ID获取申请单对象，确保申请单存在且属于药品系统类型，
     *                      最后设置药品执行计划的出入库类型
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateDrugInoutType(String id, DrugIpdDataStatusEnum drugInoutType, CisOrgCommonNto cisOrgCommonNto) {
        BusinessAssert.notNull(drugInoutType, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "药品出入库类型");

        // 根据ID获取药品执行计划对象，如果不存在则返回null
        CisOrderExecPlan orderExecPlan = CisOrderExecPlan.getCisOrderExecPlanById(id).orElse(null);
        // 断言药品执行计划对象不为null，否则抛出异常
        BusinessAssert.notNull(orderExecPlan, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, id + "执行记录");

        // 获取药品执行计划关联的申请单ID
        String applyId = orderExecPlan.getCisBaseApplyId();
        // 根据申请单ID获取申请单对象，如果不存在则返回null
        CisBaseApply cisBaseApply = CisBaseApply.getCisBaseApplyById(applyId).orElse(null);
        // 断言申请单对象不为null，否则抛出异常
        BusinessAssert.notNull(cisBaseApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, applyId + "申请单");

        // 断言申请单的系统类型为药品系统类型，否则抛出异常
        BusinessAssert.isTrue(SystemTypeEnum.CDRUG.equals(cisBaseApply.getSystemType()) ||
                        SystemTypeEnum.EDRUG.equals(cisBaseApply.getSystemType()),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0005, applyId + "为" + cisBaseApply.getSystemType().getName());

        // 设置药品执行计划的出入库类型
        orderExecPlan.setOrderPlanDrugInoutType(drugInoutType);
        // 记录医嘱日志
        cisApplyMqSend.PharmaciessendLogSend(orderExecPlan, cisOrgCommonNto);
    }

    /**
     * 修改多个药品执行计划的出入库类型
     * <p>
     * 此方法用于批量更新药品执行计划的出入库类型它首先验证输入参数的有效性，
     * 然后根据提供的药品执行计划ID列表，更新每个药品执行计划的出入库类型
     *
     * @param ids           药品执行计划ID列表，用于标识要更新的药品执行计划
     * @param drugInoutType 药品出入库类型，用于更新药品执行计划的出入库状态
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateMulDrugInoutType(List<String> ids, DrugIpdDataStatusEnum drugInoutType, CisOrgCommonNto cisOrgCommonNto) {

        // 验证输入参数ids是否为空，如果为空，则抛出异常
        BusinessAssert.notEmpty(ids, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "入参:ids");
        // 验证输入参数drugInoutType是否为null，如果为null，则抛出异常
        BusinessAssert.notNull(drugInoutType, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "入参：drugInoutType");

        // 根据药品执行计划ID列表查询药品执行计划
        List<CisOrderExecPlan> items = CisOrderExecPlan.findExecCisOrderExecPlans(ids);

        // 验证查询结果是否为null，如果为null，则抛出异常
        BusinessAssert.notNull(items, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "药品执行计划");

        // 提取药品执行计划的订单ID列表
        List<String> orderIds = items.stream().map(CisOrderExecPlan::getOrderId).toList();
        // 验证所有订单的系统类型是否都为药品类型，如果不是，则抛出异常
        BusinessAssert.isTrue(CisBaseApply.findCisBaseAppliesByOrderIDIn(orderIds)
                        .stream().map(p -> p.getSystemType())
                        .allMatch(o -> SystemTypeEnum.CDRUG.equals(o) || SystemTypeEnum.EDRUG.equals(o)),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0005,
                "医嘱类型"
        );

        // 遍历药品执行计划列表，设置每个计划的出入库类型
        items.forEach(p -> {
            p.setOrderPlanDrugInoutType(drugInoutType);
            // 记录医嘱日志
            cisApplyMqSend.PharmaciessendLogSend(p, cisOrgCommonNto);
        });
    }

//    @Override
//    @Transactional(rollbackFor = Throwable.class, readOnly = false)
//    public void sendMessaging(CisOrderStatueEto cisOrderStatueEto) {
//        cisApplyMqSend.OrderStatueSend(cisOrderStatueEto);
//    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> getOtherExecedCisOrderExecPlan(CisOrderExecPlanNurseQto qto) {
        List<CisOrderExecPlan> items = CisOrderExecPlan.getOtherExecedCisOrderExecPlan(qto).stream()
                .filter(p -> !Arrays.asList(SystemTypeEnum.EDRUG, SystemTypeEnum.CDRUG, SystemTypeEnum.SKIN).contains(p.getOrderClass())).toList();
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        //region 查询申请单获取临时，长期属性
        List<String> applyIds = items.stream().map(CisOrderExecPlan::getCisBaseApplyId).distinct().toList();
        List<Object[]> applyList = CisBaseApply.findCisBaseApplyOrderType(applyIds);
        Map<String, OrderTypeEnum> map = applyList.stream()
                .collect(Collectors.toMap(apply -> (String) apply[0], apply -> (OrderTypeEnum) apply[1]));
        // 将医嘱执行计划实体列表转换为传输对象（TO）列表并返回
        List<CisOrderExecPlanTo> tos = CisOrderExecPlanAssembler.toTos(items, true);
        tos.forEach(to -> to.setOrderType(map.get(to.getCisBaseApplyId()) == null ? null : map.get(to.getCisBaseApplyId())));
        return tos.stream().filter(to -> OrderTypeEnum.TEMPORARY_ORDER.equals(to.getOrderType())).toList();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderExecPlanIsPrint(List<String> execIds) {
        BusinessAssert.notEmpty(execIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "执行单ID");
        Lists.partition(execIds, 100).stream()
                .flatMap(p -> CisOrderExecPlan.findExecCisOrderExecPlans(p).stream())
                .forEach(p -> p.updateIsPrint());
    }

    /**
     * 定制给执行单单打印使用
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanCustomTo> queryCisOrderExecPlanByIds(List<String> ids) {
        List<CisOrderExecPlan> execPlans = CisOrderExecPlan.findExecCisOrderExecPlans(ids);
        Map<String, CisOrderExecPlan> execPlanMap = new HashMap<>();
        execPlans.forEach(execPlan -> execPlanMap.put(execPlan.getId(), execPlan));
        // 处理明细数据
        List<String> execPlanIds = execPlans.stream().map(CisOrderExecPlan::getId).toList();
        List<CisOrderExecPlanCustomTo> toList = new ArrayList();
        CisOrderExecPlanCharge.findByCisOrderExecPlanIdsIn(execPlanIds).stream().forEach(detail -> {
            CisOrderExecPlan to = execPlanMap.get(detail.getCisOrderExecPlanId());
            if (to != null) {
                toList.add(CisOrderExecPlanCustomAssembler.toTo(to, detail));
            }
        });
        // 处理无费数据
        List<CisOrderExecPlan> generals = execPlans.stream().filter(a -> !toList.stream().map(CisOrderExecPlanCustomTo::getCisOrderExecPlanId).distinct().toList().contains(a.getId())).toList();
        if (!CollectionUtils.isEmpty(generals)) {
            generals.stream().forEach(to -> toList.add(CisOrderExecPlanCustomAssembler.toTo(to, null)));
        }
        return toList;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisOrderExecPlanWithDetialTo queryCisOrderExecPlanByCreateDate(LocalDateTime createDate) {
        CisOrderExecPlanWithDetialTo to = new CisOrderExecPlanWithDetialTo();
        List<CisOrderExecPlan> execPlans = CisOrderExecPlan.findCisOrderExecPlanByCreatedDate(createDate);
        to.setCisOrderExecPlanTos(CisOrderExecPlanAssembler.toTos(execPlans));

        to.setDetailTos(cisDetailQueryService.queryDetailToByCreateDate(createDate));

        return to;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> queryCisOrderExecPlanByOrderIds(List<String> orderIds) {
        return CisOrderExecPlanAssembler.toTos(
                CisOrderExecPlan.findCisOrderExecPlansByOrderIdIn(orderIds)
                        .stream().filter(a -> Boolean.TRUE.equals(a.getSeltFlag())).toList());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderExecPlanOpdSetlStas(List<CisOrderExecPlanSetlStasEto> cisOrderExecPlanSetlStasEtos) {

        // 确保待修改的订单执行计划变更回退实体列表不为空
        BusinessAssert.notEmpty(cisOrderExecPlanSetlStasEtos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "修改数据");

        // 提取订单执行计划ID列表
        List<String> ids = cisOrderExecPlanSetlStasEtos.stream().flatMap(Stream::ofNullable)
                .map(CisOrderExecPlanSetlStasEto::getExecPlanId).toList();

        List<CisOrderExecPlan> items = new ArrayList<>();
        Lists.partition(ids, 100).stream().forEach(p -> items.addAll(CisOrderExecPlan.findExecCisOrderExecPlans(ids)));
        // 根据订单ID查询对应的电路订单执行计划，并将其ID映射为键，对象为值，生成Map
        Map<String, CisOrderExecPlan> map = items
                .stream().collect(Collectors.toMap(CisOrderExecPlan::getId, item -> item));

        // 遍历待修改的订单执行计划变更回退实体列表
        cisOrderExecPlanSetlStasEtos.forEach(eto -> {
            // 根据执行计划ID获取对应的电路订单执行计划对象
            CisOrderExecPlan item = map.get(eto.getExecPlanId());
            // 确保电路订单执行计划对象不为空
            BusinessAssert.notNull(item, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, eto.getExecPlanId());
            // 更新订单执行计划对象的数据
            item.updateSetlStas(eto);
            // 记录医嘱日志
            paymentLogSend(eto, item);
        });
    }

    private void paymentLogSend(CisOrderExecPlanSetlStasEto eto, CisOrderExecPlan item) {
        if(SetlStasEnum.已结算.equals(eto.getSetlStas()) || SetlStasEnum.已结算取消.equals(eto.getSetlStas())) {
            cisApplyMqSend.opdPaymentLogSend(item, eto);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderExecPlanOpdCharge(List<CisOrderExecPlanOpdChangeEto> cisOrderExecPlanOpdChangeEtos) {

        // 确保待修改的订单执行计划变更回退实体列表不为空
        BusinessAssert.notEmpty(cisOrderExecPlanOpdChangeEtos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "修改数据");

        // 提取订单执行计划ID列表
        List<String> ids = cisOrderExecPlanOpdChangeEtos.stream().flatMap(Stream::ofNullable)
                .map(CisOrderExecPlanOpdChangeEto::getExecPlanId).toList();

        List<CisOrderExecPlan> items = new ArrayList<>();
        Lists.partition(ids, 100).stream().forEach(p -> items.addAll(CisOrderExecPlan.findExecCisOrderExecPlans(ids)));
        // 根据订单ID查询对应的电路订单执行计划，并将其ID映射为键，对象为值，生成Map
        Map<String, CisOrderExecPlan> map = items
                .stream().collect(Collectors.toMap(CisOrderExecPlan::getId, item -> item));

        // 遍历待修改的订单执行计划变更回退实体列表
        cisOrderExecPlanOpdChangeEtos.forEach(eto -> {
            // 根据执行计划ID获取对应的电路订单执行计划对象
            CisOrderExecPlan item = map.get(eto.getExecPlanId());
            // 确保电路订单执行计划对象不为空
            BusinessAssert.notNull(item, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, eto.getExecPlanId());
            // 更新订单执行计划对象的数据
            item.updateOpdChange(eto);
        });
    }

    /**
     * 查询订单执行计划
     *
     * @param qto 查询参数
     * @return 订单执行计划列表
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findCisOrderExecPlans(CisOrderExecPlanTollQto qto) {
        List<CisOrderExecPlan> items = CisOrderExecPlan.findCisOrderExecPlans(qto);
        return CisOrderExecPlanAssembler.toTos(items);
    }

    /**
     * 查询订单执行计划
     *
     * @param applyCode 申请单编号
     * @return 订单执行计划列表
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findCisOrderExecPlansToll(String applyCode) {
        CisOrderExecPlanQto qto = new CisOrderExecPlanQto();
        qto.setCisBaseApplyId(applyCode);
        qto.setSeltFlag(true);
        return CisOrderExecPlanAssembler.toTos(CisOrderExecPlan.getCisOrderExecPlans(applyCode,qto),true);
    }

    /**
     * 开立科室为当前工作组的未收费的执行单和费用明细
     *
     * @param applyCode 申请单编号
     * @param orgCode   组织编码
     * @return 订单执行计划列表
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> findCisOrderExecPlansCurOrg(String applyCode, String orgCode) {
        BusinessAssert.hasText(orgCode, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0007, "当前工作组编码");
        CisOrderExecPlanQto qto = new CisOrderExecPlanQto();
        qto.setCisBaseApplyId(applyCode);
        qto.setOrgCode(orgCode);
        qto.setSeltFlag(false);
        return CisOrderExecPlanAssembler.toTos(CisOrderExecPlan.getCisOrderExecPlans(applyCode,qto),true);
    }

    /**
     * 修改执行单及其费用信息
     * 该方法支持对执行单的费用信息进行新增、修改和删除操作
     *
     * @param eto 执行单费用修改对象，包含执行单ID、版本号、新增费用、修改费用、删除费用ID等信息
     * @return 修改后的执行单信息
     * @throws BusinessAssert 当参数校验失败或业务规则不满足时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOrderExecPlanTo modefyCisOrderExecPlanWithCharge(CisOrderExecPlanWithChargeEto eto) {
        // 1. 参数校验 - 确保输入参数的有效性
        BusinessAssert.notNull(eto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "修改参数不能为空");
        BusinessAssert.hasText(eto.getExecPlanId(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "执行单ID不能为空");
        BusinessAssert.notNull(eto.getVersion(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "版本号不能为空");

        // 2. 查询执行单并进行业务校验
        CisOrderExecPlan cisOrderExecPlan = CisOrderExecPlan.getCisOrderExecPlanById(eto.getExecPlanId()).orElse(null);
        BusinessAssert.notNull(cisOrderExecPlan, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001,
            "执行单不存在：" + eto.getExecPlanId());

        // 版本号校验，防止并发修改冲突
        cisOrderExecPlan.updateVersion(eto.getVersion());

        // 3. 处理新增费用信息
        if (CollectionUtils.isNotEmpty(eto.getChargeNtos())) {
            // 创建新的费用记录
            CisOrderExecPlanCharge cisOrderExecPlanCharge = new CisOrderExecPlanCharge();
            cisOrderExecPlanCharge.create(eto.getExecPlanId(), eto.getChargeNtos(), true);
        }

        // 4. 获取当前执行单的所有费用信息（包括新增的）
        List<CisOrderExecPlanCharge> charges = CisOrderExecPlanCharge.getByCisOrderExecPlanId(eto.getExecPlanId());

        // 5. 处理费用修改信息
        if (CollectionUtils.isNotEmpty(eto.getChargeEtos())) {
            // 构建费用ID到费用对象的映射，便于快速查找
            Map<String, CisOrderExecPlanCharge> chargesMap = charges
                    .stream()
                    .filter(Objects::nonNull) // 过滤空对象，确保数据安全
                    .collect(Collectors.toMap(CisOrderExecPlanCharge::getId, item -> item));

            // 批量更新费用信息
            eto.getChargeEtos().stream()
                .filter(Objects::nonNull) // 过滤空对象，确保数据安全
                .forEach(charge -> updateChargeAmount(charge, chargesMap));
        }

        // 6. 处理费用删除操作
        if (CollectionUtils.isNotEmpty(eto.getDeleteChargeIds())) {
            // 筛选出需要删除的费用记录并执行删除操作
            charges.stream()
                .filter(Objects::nonNull) // 确保费用对象不为空
                .filter(charge -> eto.getDeleteChargeIds().contains(charge.getId())) // 筛选需要删除的费用
                .forEach(charge -> {
                    // 执行删除操作
                    charge.delete();
                });
        }

        // 7. 返回更新后的执行单信息
        return CisOrderExecPlanAssembler.toTo(cisOrderExecPlan, true);
    }

    /**
     * 更新费用金额并保存
     *
     * @param charge 费用对象
     * @param chargesMap 费用映射表
     */
    private void updateChargeAmount(CisOrderExecPlanChargeEto charge, Map<String, CisOrderExecPlanCharge> chargesMap) {
        // 参数校验
        if (charge == null || StringUtils.isBlank(charge.getId())) {
            return;
        }

        // 获取对应的费用记录
        CisOrderExecPlanCharge cisOrderExecPlanCharge = chargesMap.get(charge.getId());
        BusinessAssert.notNull(cisOrderExecPlanCharge, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001,
            "费用记录不存在：" + charge.getId());

        // 计算费用金额（添加空值检查和数值校验）
        Double num = charge.getNum();
        BigDecimal price = cisOrderExecPlanCharge.getPrice();

        if (num != null && price != null && num >= 0 && price.compareTo(BigDecimal.ZERO) >= 0) {
            BigDecimal chargeAmount = price.multiply(BigDecimal.valueOf(num));
            charge.setChargeAmount(chargeAmount);
        } else {
            // 如果数量或价格为空或负数，设置费用金额为0
            charge.setChargeAmount(BigDecimal.ZERO);
        }

        // 更新费用记录
        cisOrderExecPlanCharge.update(charge);
    }


}