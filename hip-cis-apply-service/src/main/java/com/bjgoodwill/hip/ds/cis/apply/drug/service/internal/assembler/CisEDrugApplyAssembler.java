package com.bjgoodwill.hip.ds.cis.apply.drug.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisEDrugApply;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisEDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisEDrugApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;

import java.util.ArrayList;
import java.util.List;

public abstract class CisEDrugApplyAssembler {

    public static List<CisEDrugApplyTo> toTos(List<CisEDrugApply> cisEDrugApplys) {
        return toTos(cisEDrugApplys, false);
    }

    public static List<CisEDrugApplyTo> toTos(List<CisEDrugApply> cisEDrugApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisEDrugApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisEDrugApplys不能为空！");

        List<CisEDrugApplyTo> tos = new ArrayList<>();
        for (CisEDrugApply cisEDrugApply : cisEDrugApplys)
            tos.add(toTo(cisEDrugApply, withAllParts));
        return tos;
    }

    public static CisEDrugApplyTo toTo(CisEDrugApply cisEDrugApply) {
        return toTo(cisEDrugApply, false);
    }

    /**
     * @generated
     */
    public static CisEDrugApplyTo toTo(CisEDrugApply cisEDrugApply, boolean withAllParts) {
        if (cisEDrugApply == null)
            return null;
        CisEDrugApplyTo to = new CisEDrugApplyTo();
        to.setId(cisEDrugApply.getId());
        to.setPatMiCode(cisEDrugApply.getPatMiCode());
        to.setVisitCode(cisEDrugApply.getVisitCode());
        to.setServiceItemCode(cisEDrugApply.getServiceItemCode());
        to.setServiceItemName(cisEDrugApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisEDrugApply.getIsCanPriorityFlag());
        to.setStatusCode(cisEDrugApply.getStatusCode());
        to.setCreatedStaff(cisEDrugApply.getCreatedStaff());
        to.setCreatedDate(cisEDrugApply.getCreatedDate());
        to.setUpdatedStaff(cisEDrugApply.getUpdatedStaff());
        to.setUpdatedDate(cisEDrugApply.getUpdatedDate());
        to.setExecutorStaff(cisEDrugApply.getExecutorStaff());
        to.setExecutorDate(cisEDrugApply.getExecutorDate());
        to.setExecutorHosptialCode(cisEDrugApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisEDrugApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisEDrugApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisEDrugApply.getMedrecordExamabstractId());
        to.setVisitType(cisEDrugApply.getVisitType());
        to.setDeptNurseCode(cisEDrugApply.getDeptNurseCode());
        to.setDeptNurseName(cisEDrugApply.getDeptNurseName());
        to.setOrderID(cisEDrugApply.getOrderID());
        to.setHospitalCode(cisEDrugApply.getHospitalCode());
        to.setPrescriptionID(cisEDrugApply.getPrescriptionID());
        to.setIsPrint(cisEDrugApply.getIsPrint());
        to.setPrintStaff(cisEDrugApply.getPrintStaff());
        to.setPrintDate(cisEDrugApply.getPrintDate());
        to.setReMark(cisEDrugApply.getReMark());
        to.setIcuExecuteDate(cisEDrugApply.getIcuExecuteDate());
        to.setIsChargeManager(cisEDrugApply.getIsChargeManager());
        to.setVersion(cisEDrugApply.getVersion());
        to.setCreateOrgCode(cisEDrugApply.getCreateOrgCode());
        to.setSortNo(cisEDrugApply.getSortNo());
        to.setIsBaby(cisEDrugApply.getIsBaby());
        to.setUsage(cisEDrugApply.getUsage());
        to.setUsageName(cisEDrugApply.getUsageName());
        to.setFrequency(cisEDrugApply.getFrequency());
        to.setFrequencyName(cisEDrugApply.getFrequencyName());
        to.setTreatmentCourse(cisEDrugApply.getTreatmentCourse());
        to.setTreatmentCourseUnit(cisEDrugApply.getTreatmentCourseUnit());
        to.setTreatmentCourseUnitName(cisEDrugApply.getTreatmentCourseUnitName());
        to.setReceiveOrg(cisEDrugApply.getReceiveOrg());
        to.setReceiveOrgName(cisEDrugApply.getReceiveOrgName());
        to.setDripSpeed(cisEDrugApply.getDripSpeed());
        to.setDripSpeedUnit(cisEDrugApply.getDripSpeedUnit());
        to.setDripSpeedUnitName(cisEDrugApply.getDripSpeedUnitName());
        to.setVisitOrgCode(cisEDrugApply.getVisitOrgCode());
        to.setVisitOrgName(cisEDrugApply.getVisitOrgName());
        to.setIsOlation(cisEDrugApply.getIsOlation());
        to.setIsApply(cisEDrugApply.getIsApply());
        to.setNum(cisEDrugApply.getNum());
//        to.setSbadmWay(cisEDrugApply.getSbadmWay());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisEDrugApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisEDrugApply.getCisOrderExecPlans()));
            to.setCisDrugApplyDetails(CisDrugApplyDetailAssembler.toTos(cisEDrugApply.getDetailList()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisEDrugApply.getApplyDiagnoses()));

        }
        return to;
    }


    public static CisEDrugApplyNto toNto(CisEDrugApply cisEDrugApply, boolean withAllParts) {
        if (cisEDrugApply == null)
            return null;
        CisEDrugApplyNto to = new CisEDrugApplyNto();
        to.setId(cisEDrugApply.getId());
        to.setPatMiCode(cisEDrugApply.getPatMiCode());
        to.setVisitCode(cisEDrugApply.getVisitCode());
        to.setServiceItemCode(cisEDrugApply.getServiceItemCode());
        to.setServiceItemName(cisEDrugApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisEDrugApply.getIsCanPriorityFlag());
//        to.setMedrecordExamabstractId(cisEDrugApply.getMedrecordExamabstractId());
        to.setVisitType(cisEDrugApply.getVisitType());
        to.setDeptNurseCode(cisEDrugApply.getDeptNurseCode());
        to.setDeptNurseName(cisEDrugApply.getDeptNurseName());
        to.setOrderID(cisEDrugApply.getOrderID());
        to.setHospitalCode(cisEDrugApply.getHospitalCode());
        to.setPrescriptionID(cisEDrugApply.getPrescriptionID());
//        to.setIsPrint(cisEDrugApply.getIsPrint());
//        to.setPrintStaff(cisEDrugApply.getPrintStaff());
//        to.setPrintDate(cisEDrugApply.getPrintDate());
        to.setReMark(cisEDrugApply.getReMark());
        to.setIcuExecuteDate(cisEDrugApply.getIcuExecuteDate());
        to.setIsChargeManager(cisEDrugApply.getIsChargeManager());
        to.setCreateOrgCode(cisEDrugApply.getCreateOrgCode());
        to.setSortNo(cisEDrugApply.getSortNo());
        to.setIsBaby(cisEDrugApply.getIsBaby());
        to.setUsage(cisEDrugApply.getUsage());
        to.setUsageName(cisEDrugApply.getUsageName());
        to.setFrequency(cisEDrugApply.getFrequency());
        to.setFrequencyName(cisEDrugApply.getFrequencyName());
        to.setTreatmentCourse(cisEDrugApply.getTreatmentCourse());
        to.setTreatmentCourseUnit(cisEDrugApply.getTreatmentCourseUnit());
        to.setTreatmentCourseUnitName(cisEDrugApply.getTreatmentCourseUnitName());
        to.setReceiveOrg(cisEDrugApply.getReceiveOrg());
        to.setReceiveOrgName(cisEDrugApply.getReceiveOrgName());
        to.setDripSpeed(cisEDrugApply.getDripSpeed());
        to.setDripSpeedUnit(cisEDrugApply.getDripSpeedUnit());
        to.setDripSpeedUnitName(cisEDrugApply.getDripSpeedUnitName());
        to.setVisitOrgCode(cisEDrugApply.getVisitOrgCode());
        to.setOrderType(cisEDrugApply.getOrderType());
        to.setVisitOrgName(cisEDrugApply.getVisitOrgName());
        to.setCreateOrgName(cisEDrugApply.getCreateOrgName());
        to.setExecutorOrgCode(cisEDrugApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisEDrugApply.getExecutorOrgName());
        to.setNum(cisEDrugApply.getNum());
        to.setIsOlation(cisEDrugApply.getIsOlation());
        to.setIsApply(cisEDrugApply.getIsApply());
//        to.setSbadmWay(cisEDrugApply.getSbadmWay());
        if (withAllParts) {
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisEDrugApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisEDrugApply.getCisOrderExecPlans()));

        }
        return to;
    }
}