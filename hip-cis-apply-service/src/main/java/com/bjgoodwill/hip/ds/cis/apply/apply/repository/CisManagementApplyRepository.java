package com.bjgoodwill.hip.ds.cis.apply.apply.repository;

import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisManagementApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisManagementApplyRepository")
public interface CisManagementApplyRepository extends JpaRepository<CisManagementApply, String>, JpaSpecificationExecutor<CisManagementApply> {

}