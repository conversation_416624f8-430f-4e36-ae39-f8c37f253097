package com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisManagementApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisManagementApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisManagementApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;

import java.util.ArrayList;
import java.util.List;

public abstract class CisManagementApplyAssembler {

    public static List<CisManagementApplyTo> toTos(List<CisManagementApply> cisManagementApplys) {
        return toTos(cisManagementApplys, false);
    }

    public static List<CisManagementApplyTo> toTos(List<CisManagementApply> cisManagementApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisManagementApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisManagementApplys不能为空！");

        List<CisManagementApplyTo> tos = new ArrayList<>();
        for (CisManagementApply cisManagementApply : cisManagementApplys)
            tos.add(toTo(cisManagementApply, withAllParts));
        return tos;
    }

    public static CisManagementApplyTo toTo(CisManagementApply cisManagementApply) {
        return toTo(cisManagementApply, false);
    }

    /**
     * @generated
     */
    public static CisManagementApplyTo toTo(CisManagementApply cisManagementApply, boolean withAllParts) {
        if (cisManagementApply == null)
            return null;
        CisManagementApplyTo to = new CisManagementApplyTo();
        to.setId(cisManagementApply.getId());
        to.setPatMiCode(cisManagementApply.getPatMiCode());
        to.setVisitCode(cisManagementApply.getVisitCode());
        to.setServiceItemCode(cisManagementApply.getServiceItemCode());
        to.setServiceItemName(cisManagementApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisManagementApply.getIsCanPriorityFlag());
        to.setStatusCode(cisManagementApply.getStatusCode());
        to.setCreatedStaff(cisManagementApply.getCreatedStaff());
        to.setCreatedDate(cisManagementApply.getCreatedDate());
        to.setUpdatedStaff(cisManagementApply.getUpdatedStaff());
        to.setUpdatedDate(cisManagementApply.getUpdatedDate());
        to.setExecutorStaff(cisManagementApply.getExecutorStaff());
        to.setExecutorDate(cisManagementApply.getExecutorDate());
        to.setExecutorHosptialCode(cisManagementApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisManagementApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisManagementApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisManagementApply.getMedrecordExamabstractId());
        to.setVisitType(cisManagementApply.getVisitType());
        to.setDeptNurseCode(cisManagementApply.getDeptNurseCode());
        to.setDeptNurseName(cisManagementApply.getDeptNurseName());
        to.setOrderID(cisManagementApply.getOrderID());
        to.setHospitalCode(cisManagementApply.getHospitalCode());
        to.setPrescriptionID(cisManagementApply.getPrescriptionID());
        to.setIsPrint(cisManagementApply.getIsPrint());
        to.setPrintStaff(cisManagementApply.getPrintStaff());
        to.setPrintDate(cisManagementApply.getPrintDate());
        to.setReMark(cisManagementApply.getReMark());
        to.setIcuExecuteDate(cisManagementApply.getIcuExecuteDate());
        to.setIsChargeManager(cisManagementApply.getIsChargeManager());
        to.setVersion(cisManagementApply.getVersion());
        to.setCreateOrgCode(cisManagementApply.getCreateOrgCode());
        to.setSortNo(cisManagementApply.getSortNo());
        to.setIsBaby(cisManagementApply.getIsBaby());
        to.setIsOlation(cisManagementApply.getIsOlation());
        to.setFrequency(cisManagementApply.getFrequency());
        to.setFrequencyName(cisManagementApply.getFrequencyName());
        to.setNum(cisManagementApply.getNum());
        to.setIsApply(cisManagementApply.getIsApply());
        to.setVisitOrgCode(cisManagementApply.getVisitOrgCode());
        to.setVisitOrgName(cisManagementApply.getVisitOrgName());
//        to.setClinicalDiagnosis(cisManagementApply.getClinicalDiagnosis());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisManagementApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisManagementApply.getCisOrderExecPlans()));
        }
        return to;
    }

    public static CisManagementApplyNto toNto(CisManagementApply cisManagementApply, boolean withAllParts) {
        if (cisManagementApply == null)
            return null;
        CisManagementApplyNto to = new CisManagementApplyNto();
        to.setId(cisManagementApply.getId());
        to.setPatMiCode(cisManagementApply.getPatMiCode());
        to.setVisitCode(cisManagementApply.getVisitCode());
        to.setServiceItemCode(cisManagementApply.getServiceItemCode());
        to.setServiceItemName(cisManagementApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisManagementApply.getIsCanPriorityFlag());
        to.setVisitType(cisManagementApply.getVisitType());
        to.setDeptNurseCode(cisManagementApply.getDeptNurseCode());
        to.setDeptNurseName(cisManagementApply.getDeptNurseName());
        to.setOrderID(cisManagementApply.getOrderID());
        to.setHospitalCode(cisManagementApply.getHospitalCode());
        to.setPrescriptionID(cisManagementApply.getPrescriptionID());
        to.setReMark(cisManagementApply.getReMark());
        to.setIcuExecuteDate(cisManagementApply.getIcuExecuteDate());
        to.setIsChargeManager(cisManagementApply.getIsChargeManager());
        to.setCreateOrgCode(cisManagementApply.getCreateOrgCode());
        to.setSortNo(cisManagementApply.getSortNo());
        to.setIsBaby(cisManagementApply.getIsBaby());
        to.setFrequency(cisManagementApply.getFrequency());
        to.setFrequencyName(cisManagementApply.getFrequencyName());
        to.setVisitOrgCode(cisManagementApply.getVisitOrgCode());
        to.setOrderType(cisManagementApply.getOrderType());
        to.setVisitOrgName(cisManagementApply.getVisitOrgName());
        to.setCreateOrgName(cisManagementApply.getCreateOrgName());
        to.setExecutorOrgCode(cisManagementApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisManagementApply.getExecutorOrgName());
        to.setNum(cisManagementApply.getNum());
        to.setIsOlation(cisManagementApply.getIsOlation());
        to.setIsApply(cisManagementApply.getIsApply());
        if (withAllParts) {
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisManagementApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisManagementApply.getCisOrderExecPlans()));
        }
        return to;
    }

}