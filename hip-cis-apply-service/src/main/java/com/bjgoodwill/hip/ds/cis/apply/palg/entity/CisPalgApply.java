package com.bjgoodwill.hip.ds.cis.apply.palg.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisPalgApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.ApplyWithDetial;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.entity.CisMedicalHistory;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryEto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "病理申请单")
@DiscriminatorValue("23")
public class CisPalgApply extends ApplyWithDetial<CisPalgApplyDetail> {

    // 最后一次月经日期
    private LocalDateTime lastMenstrualDate;
    // 绝经
    private Boolean isMenopause;
    // 病人病历(选择所有现时适应的个案)
    private String medicalRecord;
    // 其他现时适应个案
    private String ortherQuestions;
    // 过去检查结果
    private String pastResults;
    // 临床史
    private String clinicalHistory;
    // 临床诊断
    private String clinicalDiagnosis;

    public static Optional<CisPalgApply> getCisPalgApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisPalgApply> getCisPalgApplies(CisPalgApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisPalgApply> getCisPalgApplyPage(CisPalgApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisPalgApply> getSpecification(CisPalgApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            if (qto.getIsMenopause() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isMenopause"), qto.getIsMenopause()));
            }
            return predicate;
        };
    }

    private static CisPalgApplyRepository dao() {
        return SpringUtil.getBean(CisPalgApplyRepository.class);
    }

    @Comment("最后一次月经日期")
    @Column(name = "last_menstrual_date", nullable = true)
    public LocalDateTime getLastMenstrualDate() {
        return lastMenstrualDate;
    }

    protected void setLastMenstrualDate(LocalDateTime lastMenstrualDate) {
        this.lastMenstrualDate = lastMenstrualDate;
    }

    @Comment("绝经")
    @Column(name = "is_menopause", nullable = true)
    public Boolean getIsMenopause() {
        return isMenopause;
    }

    protected void setIsMenopause(Boolean isMenopause) {
        this.isMenopause = isMenopause;
    }

    @Comment("病人病历(选择所有现时适应的个案)")
    @Column(name = "medical_record", nullable = true)
    public String getMedicalRecord() {
        return medicalRecord;
    }

//    @Override
//    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto) {
//        return create((CisPalgApplyNto)cisBaseApplyNto);
//    }

    protected void setMedicalRecord(String medicalRecord) {
        this.medicalRecord = medicalRecord;
    }

    @Comment("其他现时适应个案")
    @Column(name = "orther_questions", nullable = true)
    public String getOrtherQuestions() {
        return ortherQuestions;
    }

    protected void setOrtherQuestions(String ortherQuestions) {
        this.ortherQuestions = ortherQuestions;
    }

    @Comment("过去检查结果")
    @Column(name = "past_results", nullable = true)
    public String getPastResults() {
        return pastResults;
    }

    protected void setPastResults(String pastResults) {
        this.pastResults = pastResults;
    }

    @Comment("临床病史")
    @Column(name = "Clinical_History", nullable = true)
    public String getClinicalHistory() {
        return clinicalHistory;
    }

    public void setClinicalHistory(String clinicalHistory) {
        this.clinicalHistory = clinicalHistory;
    }

    @Comment("临床诊断")
    @Column(name = "clinical_diagnosis", nullable = true)
    public String getClinicalDiagnosis() {
        return clinicalDiagnosis;
    }

    public void setClinicalDiagnosis(String clinicalDiagnosis) {
        this.clinicalDiagnosis = clinicalDiagnosis;
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.PALG;
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisPalgApplyEto) cisBaseApplyEto);
    }

    @Override
    public CisPalgApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisPalgApplyNto) cisBaseApplyNto, save);
    }

    public CisPalgApply create(CisPalgApplyNto cisPalgApplyNto, Boolean save) {
        BusinessAssert.notNull(cisPalgApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisPalgApplyNto不能为空！");
        super.create(cisPalgApplyNto, save);

        setLastMenstrualDate(cisPalgApplyNto.getLastMenstrualDate());
        setIsMenopause(cisPalgApplyNto.getIsMenopause());
        setMedicalRecord(cisPalgApplyNto.getMedicalRecord());
        setOrtherQuestions(cisPalgApplyNto.getOrtherQuestions());
        setPastResults(cisPalgApplyNto.getPastResults());
        setCheckPurpose(cisPalgApplyNto.getCheckPurpose());
        setContagiousDiseaseHistoryFlag(cisPalgApplyNto.getContagiousDiseaseHistoryFlag());
        setClinicalHistory(cisPalgApplyNto.getClinicalHistory());
        setClinicalDiagnosis(cisPalgApplyNto.getClinicalDiagnosis());
        if (save) {
            dao().save(this);
        }

        if (!CollectionUtils.isEmpty(cisPalgApplyNto.getDetails())) {
            for (CisPalgApplyDetailNto _cisPalgApplyDetailNto : cisPalgApplyNto.getDetails()) {
                CisPalgApplyDetail cisDrugApplyDetail = new CisPalgApplyDetail();
                cisDrugApplyDetail.create(getId(), _cisPalgApplyDetailNto, getStatusCode(), save);
            }
        }

        if (!ObjectUtils.isEmpty(cisPalgApplyNto.getPalgExt())) {
            CisPalgApplyExt cisPalgApplyExt = new CisPalgApplyExt();
            cisPalgApplyExt.create(getId(), cisPalgApplyNto.getPalgExt(), save);
        }

        //患者病史
        if (cisPalgApplyNto.getCisMedicalHistoryNto() != null) {
            CisMedicalHistory cisMedicalHistory = getCisMedicalHistory();
            if (cisMedicalHistory != null) {
                CisMedicalHistoryEto eto = HIPBeanUtil.copy(cisPalgApplyNto.getCisMedicalHistoryNto(), CisMedicalHistoryEto.class);
                cisMedicalHistory.setId(cisPalgApplyNto.getCisMedicalHistoryNto().getId());
                cisMedicalHistory.update(eto);
            } else {
                cisMedicalHistory = new CisMedicalHistory();
                cisMedicalHistory.create(cisPalgApplyNto.getCisMedicalHistoryNto());
            }
        }
        return this;
    }

    public void update(CisPalgApplyEto cisPalgApplyEto) {
        super.update(cisPalgApplyEto);
        setLastMenstrualDate(cisPalgApplyEto.getLastMenstrualDate());
        setMedicalRecord(cisPalgApplyEto.getMedicalRecord());
        setOrtherQuestions(cisPalgApplyEto.getOrtherQuestions());
        setPastResults(cisPalgApplyEto.getPastResults());
        setCheckPurpose(cisPalgApplyEto.getCheckPurpose());
        setContagiousDiseaseHistoryFlag(cisPalgApplyEto.getContagiousDiseaseHistoryFlag());
        setClinicalHistory(cisPalgApplyEto.getClinicalHistory());
        setClinicalDiagnosis(cisPalgApplyEto.getClinicalDiagnosis());
        if (!CollectionUtils.isEmpty(cisPalgApplyEto.getDetailEtos())) {
            cisPalgApplyEto.getDetailEtos().forEach(eto -> {
                Optional<CisPalgApplyDetail> detail = CisPalgApplyDetail.getCisPalgApplyDetailById(eto.getId());
                detail.get().update(eto);
            });
        }

        if (!CollectionUtils.isEmpty(cisPalgApplyEto.getDetailNtos())) {
            cisPalgApplyEto.getDetailNtos().forEach(nto -> {
                CisPalgApplyDetail detail = new CisPalgApplyDetail();
                detail.create(getId(), nto, getStatusCode(), true);
            });
        }

        if (!ObjectUtils.isEmpty(cisPalgApplyEto.getPalgExt())) {
            Optional<CisPalgApplyExt> extOp = CisPalgApplyExt.getCisPalgApplyExtById(cisPalgApplyEto.getPalgExt().getId());
            extOp.ifPresent(ext -> ext.update(cisPalgApplyEto.getPalgExt()));
        }
        //患者病史
        if (cisPalgApplyEto.getCisMedicalHistoryNto() != null) {
            CisMedicalHistory cisMedicalHistory = getCisMedicalHistory();
            if (cisMedicalHistory != null) {
                CisMedicalHistoryEto eto = HIPBeanUtil.copy(cisPalgApplyEto.getCisMedicalHistoryNto(), CisMedicalHistoryEto.class);
                cisMedicalHistory.setId(cisPalgApplyEto.getCisMedicalHistoryNto().getId());
                cisMedicalHistory.update(eto);
            } else {
                cisMedicalHistory = new CisMedicalHistory();
                cisMedicalHistory.create(cisPalgApplyEto.getCisMedicalHistoryNto());
            }
        }
    }

    public void delete() {
        super.delete();

        CisPalgApplyDetail.deleteByCisPalgApplyId(getId());

        CisPalgApplyExt.deleteByCisPalgApplyId(getId());

        dao().delete(this);
    }

    @Override
    @Transient
    public List<CisPalgApplyDetail> getDetailList() {
        return CisPalgApplyDetail.getByCisPalgApplyId(getId());
    }

    @Override
    protected List<String> getSplitCodes(List<CisPalgApplyDetail> applyDetails) {
        return List.of();
    }
}