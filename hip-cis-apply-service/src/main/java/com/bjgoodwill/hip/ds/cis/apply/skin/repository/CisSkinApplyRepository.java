package com.bjgoodwill.hip.ds.cis.apply.skin.repository;

import com.bjgoodwill.hip.ds.cis.apply.skin.entity.CisSkinApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisSkinApplyRepository")
public interface CisSkinApplyRepository extends JpaRepository<CisSkinApply, String>, JpaSpecificationExecutor<CisSkinApply> {

}