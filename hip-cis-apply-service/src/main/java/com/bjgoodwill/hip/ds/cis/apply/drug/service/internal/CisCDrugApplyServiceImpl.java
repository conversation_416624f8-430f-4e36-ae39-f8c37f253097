package com.bjgoodwill.hip.ds.cis.apply.drug.service.internal;

import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisBaseApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeEto;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisCDrugApply;
import com.bjgoodwill.hip.ds.cis.apply.drug.service.CisCDrugApplyService;
import com.bjgoodwill.hip.ds.cis.apply.drug.service.internal.assembler.CisCDrugApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanCDrugQto;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisCDrugApplyService")
@RequestMapping(value = "/api/apply/apply/cisCDrugApply", produces = "application/json; charset=utf-8")
public class CisCDrugApplyServiceImpl extends CisBaseDrugApplyServiceImpl implements CisCDrugApplyService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisCDrugApplyTo getCisCDrugApplyById(String id) {
        return CisCDrugApplyAssembler.toTo(CisCDrugApply.getCisCDrugApplyById(id).orElse(null), true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisCDrugApplyTo createCisCDrugApply(CisCDrugApplyNto cisCDrugApplyNto) {
        CisCDrugApply cisCDrugApply = new CisCDrugApply();
        cisCDrugApply = cisCDrugApply.create(cisCDrugApplyNto, true);
        return CisCDrugApplyAssembler.toTo(cisCDrugApply);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisCDrugApply(String id, CisCDrugApplyEto cisCDrugApplyEto) {
        Optional<CisCDrugApply> cisCDrugApplyOptional = CisCDrugApply.getCisCDrugApplyById(id);
        cisCDrugApplyOptional.ifPresent(cisCDrugApply -> cisCDrugApply.update(cisCDrugApplyEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisCDrugApply(String id) {
        Optional<CisCDrugApply> cisCDrugApplyOptional = CisCDrugApply.getCisCDrugApplyById(id);
        cisCDrugApplyOptional.ifPresent(cisCDrugApply -> cisCDrugApply.delete());
    }

    //集中发送页面，中草药查询
    @Override
    public List<CisBaseApplyTo> getCDrugSend(CisOrderExecPlanCDrugQto cisOrderExecPlanDrugQto) {
        List<CisOrderExecPlan> cisOrderExecPlans = CisOrderExecPlan.findExecCisOrderCDrugPlans(cisOrderExecPlanDrugQto);
        if (CollectionUtils.isEmpty(cisOrderExecPlans)) {
            return Collections.emptyList();
        }
        List<String> orderIds = cisOrderExecPlans.stream().map(p -> p.getOrderId()).distinct().toList();
        List<CisBaseApplyTo> tos = CisBaseApplyAssembler.toTos(CisBaseApply.findCisBaseAppliesByOrderIDIn(orderIds));
        Map<String, List<CisOrderExecPlan>> map = cisOrderExecPlans.stream().collect(Collectors.groupingBy(CisOrderExecPlan::getOrderId));
        tos.forEach(p -> p.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(map.get(p.getOrderID()))));
        return tos;
    }

    @Override
    public void updateCisApplyCharge(String id, CisApplyChargeEto cisApplyChargeEto) {

    }

}