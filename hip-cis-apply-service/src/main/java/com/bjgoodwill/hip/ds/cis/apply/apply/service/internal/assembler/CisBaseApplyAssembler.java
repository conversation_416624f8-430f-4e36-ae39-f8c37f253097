package com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.*;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.blood.entity.CisBloodApply;
import com.bjgoodwill.hip.ds.cis.apply.blood.entity.CisInputBloodApply;
import com.bjgoodwill.hip.ds.cis.apply.blood.service.internal.assembler.CisBloodApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.blood.service.internal.assembler.CisInputBloodApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApply;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.service.internal.assembler.CisDgimgApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisBaseDrugApply;
import com.bjgoodwill.hip.ds.cis.apply.drug.service.internal.assembler.CisBaseDrugApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.nursing.entity.CisNursingApply;
import com.bjgoodwill.hip.ds.cis.apply.nursing.service.internal.assembler.CisNursingApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.operation.entity.CisOperationApply;
import com.bjgoodwill.hip.ds.cis.apply.operation.service.internal.assembler.CisOperationApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.palg.entity.CisPalgApply;
import com.bjgoodwill.hip.ds.cis.apply.palg.service.internal.assembler.CisPalgApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.skin.entity.CisSkinApply;
import com.bjgoodwill.hip.ds.cis.apply.skin.service.internal.assembler.CisSkinApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApply;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.service.internal.assembler.CisSpcobsApplyAssembler;

import java.util.ArrayList;
import java.util.List;

public abstract class CisBaseApplyAssembler {

    public static List<CisBaseApplyTo> toTos(List<CisBaseApply> cisBaseApplys) {
        return toTos(cisBaseApplys, false);
    }

    public static List<CisBaseApplyTo> toTos(List<CisBaseApply> cisBaseApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisBaseApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisBaseApplys不能为空！");

        List<CisBaseApplyTo> tos = new ArrayList<>();
        for (CisBaseApply cisBaseApply : cisBaseApplys)
            tos.add(toTo(cisBaseApply, withAllParts));
        return tos;
    }

    public static CisBaseApplyTo toTo(CisBaseApply cisBaseApply) {
        return toTo(cisBaseApply, false);
    }

    /**
     * @generated
     */
    public static CisBaseApplyTo toTo(CisBaseApply cisBaseApply, boolean withAllParts) {
        if (cisBaseApply == null)
            return null;
        if (cisBaseApply instanceof CisDgimgApply) {
            return CisDgimgApplyAssembler.toTo((CisDgimgApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisSpcobsApply) {
            return CisSpcobsApplyAssembler.toTo((CisSpcobsApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisCommon) {
            return CisCommonAssembler.toTo((CisCommon) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisBaseDrugApply) {
            return CisBaseDrugApplyAssembler.toTo((CisBaseDrugApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisOperationApply) {
            return CisOperationApplyAssembler.toTo((CisOperationApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisTreatmentApply) {
            return CisTreatmentApplyAssembler.toTo((CisTreatmentApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisChangeDeptApply) {
            return CisChangeDeptApplyAssembler.toTo((CisChangeDeptApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisOutHospitalApply) {
            return CisOutHospitalApplyAssembler.toTo((CisOutHospitalApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisPalgApply) {
            return CisPalgApplyAssembler.toTo((CisPalgApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisConsultationApply) {
            return CisConsultationApplyAssembler.toTo((CisConsultationApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisBloodApply) {
            return CisBloodApplyAssembler.toTo((CisBloodApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisInputBloodApply) {
            return CisInputBloodApplyAssembler.toTo((CisInputBloodApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisManagementApply) {
            return CisManagementApplyAssembler.toTo((CisManagementApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisNursingApply) {
            return CisNursingApplyAssembler.toTo((CisNursingApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisSkinApply) {
            return CisSkinApplyAssembler.toTo((CisSkinApply) cisBaseApply, withAllParts);
        }
        return null;
    }


    public static CisBaseApplyNto toNto(CisBaseApply cisBaseApply, boolean withAllParts) {
        if (cisBaseApply == null)
            return null;
        if (cisBaseApply instanceof CisDgimgApply) {
            return CisDgimgApplyAssembler.toNto((CisDgimgApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisSpcobsApply) {
            return CisSpcobsApplyAssembler.toNto((CisSpcobsApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisCommon) {
            return CisCommonAssembler.toNto((CisCommon) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisBaseDrugApply) {
            return CisBaseDrugApplyAssembler.toNto((CisBaseDrugApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisOperationApply) {
            return CisOperationApplyAssembler.toNto((CisOperationApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisTreatmentApply) {
            return CisTreatmentApplyAssembler.toNto((CisTreatmentApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisChangeDeptApply) {
            return CisChangeDeptApplyAssembler.toNto((CisChangeDeptApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisOutHospitalApply) {
            return CisOutHospitalApplyAssembler.toNto((CisOutHospitalApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisPalgApply) {
            return CisPalgApplyAssembler.toNto((CisPalgApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisConsultationApply) {
            return CisConsultationApplyAssembler.toNto((CisConsultationApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisBloodApply) {
            return CisBloodApplyAssembler.toNto((CisBloodApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisInputBloodApply) {
            return CisInputBloodApplyAssembler.toNto((CisInputBloodApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisManagementApply) {
            return CisManagementApplyAssembler.toNto((CisManagementApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisNursingApply) {
            return CisNursingApplyAssembler.toNto((CisNursingApply) cisBaseApply, withAllParts);
        }
        if (cisBaseApply instanceof CisSkinApply) {
            return CisSkinApplyAssembler.toNto((CisSkinApply) cisBaseApply, withAllParts);
        }
        return null;
    }

    public static List<CisBaseApplyNto> toNtos(List<CisBaseApply> cisBaseApplys) {
        return toNtos(cisBaseApplys, false);
    }

    public static List<CisBaseApplyNto> toNtos(List<CisBaseApply> cisBaseApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisBaseApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisBaseApplys不能为空！");

        List<CisBaseApplyNto> tos = new ArrayList<>();
        for (CisBaseApply cisBaseApply : cisBaseApplys)
            tos.add(toNto(cisBaseApply, withAllParts));
        return tos;
    }
}