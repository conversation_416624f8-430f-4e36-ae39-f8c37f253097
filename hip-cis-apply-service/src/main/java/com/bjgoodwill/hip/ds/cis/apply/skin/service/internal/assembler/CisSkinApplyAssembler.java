package com.bjgoodwill.hip.ds.cis.apply.skin.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.skin.entity.CisSkinApply;
import com.bjgoodwill.hip.ds.cis.apply.skin.to.CisSkinApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.skin.to.CisSkinApplyTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisSkinApplyAssembler {

    public static List<CisSkinApplyTo> toTos(List<CisSkinApply> cisSkinApplys) {
        return toTos(cisSkinApplys, false);
    }

    public static List<CisSkinApplyTo> toTos(List<CisSkinApply> cisSkinApplys, boolean withAllParts) {
        Assert.notNull(cisSkinApplys, "参数cisSkinApplys不能为空！");

        List<CisSkinApplyTo> tos = new ArrayList<>();
        for (CisSkinApply cisSkinApply : cisSkinApplys)
            tos.add(toTo(cisSkinApply, withAllParts));
        return tos;
    }

    public static CisSkinApplyTo toTo(CisSkinApply cisSkinApply) {
        return toTo(cisSkinApply, false);
    }

    /**
     * @generated
     */
    public static CisSkinApplyTo toTo(CisSkinApply cisSkinApply, boolean withAllParts) {
        if (cisSkinApply == null)
            return null;
        CisSkinApplyTo to = new CisSkinApplyTo();
        to.setId(cisSkinApply.getId());
        to.setPatMiCode(cisSkinApply.getPatMiCode());
        to.setVisitCode(cisSkinApply.getVisitCode());
        to.setServiceItemCode(cisSkinApply.getServiceItemCode());
        to.setServiceItemName(cisSkinApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisSkinApply.getIsCanPriorityFlag());
        to.setStatusCode(cisSkinApply.getStatusCode());
        to.setCreatedStaff(cisSkinApply.getCreatedStaff());
        to.setCreatedDate(cisSkinApply.getCreatedDate());
        to.setUpdatedStaff(cisSkinApply.getUpdatedStaff());
        to.setUpdatedDate(cisSkinApply.getUpdatedDate());
        to.setExecutorStaff(cisSkinApply.getExecutorStaff());
        to.setExecutorDate(cisSkinApply.getExecutorDate());
        to.setExecutorHosptialCode(cisSkinApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisSkinApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisSkinApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisSkinApply.getMedrecordExamabstractId());
        to.setVisitType(cisSkinApply.getVisitType());
        to.setDeptNurseCode(cisSkinApply.getDeptNurseCode());
        to.setDeptNurseName(cisSkinApply.getDeptNurseName());
        to.setOrderID(cisSkinApply.getOrderID());
        to.setHospitalCode(cisSkinApply.getHospitalCode());
        to.setPrescriptionID(cisSkinApply.getPrescriptionID());
        to.setIsPrint(cisSkinApply.getIsPrint());
        to.setPrintStaff(cisSkinApply.getPrintStaff());
        to.setPrintDate(cisSkinApply.getPrintDate());
        to.setReMark(cisSkinApply.getReMark());
        to.setIcuExecuteDate(cisSkinApply.getIcuExecuteDate());
        to.setIsChargeManager(cisSkinApply.getIsChargeManager());
        to.setVersion(cisSkinApply.getVersion());
        to.setCreateOrgCode(cisSkinApply.getCreateOrgCode());
        to.setSortNo(cisSkinApply.getSortNo());
        to.setIsBaby(cisSkinApply.getIsBaby());
        to.setFrequency(cisSkinApply.getFrequency());
        to.setDosage(cisSkinApply.getDosage());
        to.setDosageUnit(cisSkinApply.getDosageUnit());
        to.setUsage(cisSkinApply.getUsage());
        to.setUsageName(cisSkinApply.getUsageName());
        to.setReceiveOrg(cisSkinApply.getReceiveOrg());
        to.setIsOlation(cisSkinApply.getIsOlation());
        to.setNum(cisSkinApply.getNum());
        to.setIsApply(cisSkinApply.getIsApply());
        to.setVisitOrgCode(cisSkinApply.getVisitOrgCode());
        to.setVisitOrgName(cisSkinApply.getVisitOrgName());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisSkinApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisSkinApply.getCisOrderExecPlans()));
        }
        return to;
    }

    /**
     * @generated
     */
    public static CisSkinApplyNto toNto(CisSkinApply cisSkinApply, boolean withAllParts) {
        if (cisSkinApply == null)
            return null;
        CisSkinApplyNto to = new CisSkinApplyNto();
        to.setId(cisSkinApply.getId());
        to.setPatMiCode(cisSkinApply.getPatMiCode());
        to.setVisitCode(cisSkinApply.getVisitCode());
        to.setServiceItemCode(cisSkinApply.getServiceItemCode());
        to.setServiceItemName(cisSkinApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisSkinApply.getIsCanPriorityFlag());
        to.setExecutorOrgCode(cisSkinApply.getExecutorOrgCode());
//        to.setMedrecordExamabstractId(cisSkinApply.getMedrecordExamabstractId());
        to.setVisitType(cisSkinApply.getVisitType());
        to.setDeptNurseCode(cisSkinApply.getDeptNurseCode());
        to.setDeptNurseName(cisSkinApply.getDeptNurseName());
        to.setOrderID(cisSkinApply.getOrderID());
        to.setHospitalCode(cisSkinApply.getHospitalCode());
        to.setPrescriptionID(cisSkinApply.getPrescriptionID());
        to.setReMark(cisSkinApply.getReMark());
        to.setIcuExecuteDate(cisSkinApply.getIcuExecuteDate());
        to.setIsChargeManager(cisSkinApply.getIsChargeManager());
        to.setCreateOrgCode(cisSkinApply.getCreateOrgCode());
        to.setSortNo(cisSkinApply.getSortNo());
        to.setIsBaby(cisSkinApply.getIsBaby());
        to.setFrequency(cisSkinApply.getFrequency());
        to.setDosage(cisSkinApply.getDosage());
        to.setDosageUnit(cisSkinApply.getDosageUnit());
        to.setUsage(cisSkinApply.getUsage());
        to.setUsageName(cisSkinApply.getUsageName());
        to.setReceiveOrg(cisSkinApply.getReceiveOrg());
        to.setExecutorOrgCode(cisSkinApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisSkinApply.getExecutorOrgName());
        to.setNum(cisSkinApply.getNum());
        to.setIsOlation(cisSkinApply.getIsOlation());
        to.setOrderType(cisSkinApply.getOrderType());
        to.setFrequencyName(cisSkinApply.getFrequencyName());
        to.setVisitOrgCode(cisSkinApply.getVisitOrgCode());
        to.setVisitOrgName(cisSkinApply.getVisitOrgName());
        to.setCreateOrgName(cisSkinApply.getCreateOrgName());
        to.setIsApply(cisSkinApply.getIsApply());
        if (withAllParts) {
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisSkinApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisSkinApply.getCisOrderExecPlans()));
        }
        return to;
    }

}