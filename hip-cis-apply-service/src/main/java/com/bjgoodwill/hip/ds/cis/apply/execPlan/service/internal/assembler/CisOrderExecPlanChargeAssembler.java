package com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlanCharge;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanChargeTo;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public abstract class CisOrderExecPlanChargeAssembler {

    public static List<CisOrderExecPlanChargeTo> toTos(List<CisOrderExecPlanCharge> cisOrderExecPlanCharges) {
        return toTos(cisOrderExecPlanCharges, false);
    }

    public static List<CisOrderExecPlanChargeTo> toTos(List<CisOrderExecPlanCharge> cisOrderExecPlanCharges, boolean withAllParts) {
//        BusinessAssert.notNull(cisOrderExecPlanCharges, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisOrderExecPlanCharges不能为空！");

        List<CisOrderExecPlanChargeTo> tos = new ArrayList<>();
        if (CollectionUtils.isEmpty(cisOrderExecPlanCharges)) return tos;
        for (CisOrderExecPlanCharge cisOrderExecPlanCharge : cisOrderExecPlanCharges)
            tos.add(toTo(cisOrderExecPlanCharge, withAllParts));
        return tos;
    }

    public static CisOrderExecPlanChargeTo toTo(CisOrderExecPlanCharge cisOrderExecPlanCharge) {
        return toTo(cisOrderExecPlanCharge, false);
    }

    /**
     * @generated
     */
    public static CisOrderExecPlanChargeTo toTo(CisOrderExecPlanCharge cisOrderExecPlanCharge, boolean withAllParts) {
        if (cisOrderExecPlanCharge == null)
            return null;
        CisOrderExecPlanChargeTo to = new CisOrderExecPlanChargeTo();
        to.setId(cisOrderExecPlanCharge.getId());
        to.setCisOrderExecPlanId(cisOrderExecPlanCharge.getCisOrderExecPlanId());
        to.setCisBaseApplyId(cisOrderExecPlanCharge.getCisBaseApplyId());
        to.setVisitCode(cisOrderExecPlanCharge.getVisitCode());
        to.setPriceItemCode(cisOrderExecPlanCharge.getPriceItemCode());
        to.setPriceItemName(cisOrderExecPlanCharge.getPriceItemName());
        to.setPackageSpec(cisOrderExecPlanCharge.getPackageSpec());
        to.setPrice(cisOrderExecPlanCharge.getPrice());
        to.setUnit(cisOrderExecPlanCharge.getUnit());
        to.setUnitName(cisOrderExecPlanCharge.getUnitName());
        to.setNum(cisOrderExecPlanCharge.getNum());
        to.setChargeAmount(cisOrderExecPlanCharge.getChargeAmount());
        to.setIsFixed(cisOrderExecPlanCharge.getIsFixed());
        to.setLimitConformFlag(cisOrderExecPlanCharge.getLimitConformFlag());
        to.setExecuteOrgCode(cisOrderExecPlanCharge.getExecuteOrgCode());
        to.setExecuteOrgName(cisOrderExecPlanCharge.getExecuteOrgName());
//        to.setSetlStas(cisOrderExecPlanCharge.getSetlStas());
        to.setChargeType(cisOrderExecPlanCharge.getChargeType());
        to.setSystemItemClass(cisOrderExecPlanCharge.getSystemItemClass());
        to.setDetailId(cisOrderExecPlanCharge.getDetailId());
        if (withAllParts) {
        }
        return to;
    }

}