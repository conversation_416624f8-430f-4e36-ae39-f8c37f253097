package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisProofNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisSplitEto;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo, 临时医嘱校对拆分-按频次
 * @create: 2024-07-09 10:10
 * @className: tempprarysplitService
 * @description:
 **/
@Component
public class TempprarysplitService extends SplitService {


    /**
     * 根据频率获取执行日期数组。
     * 该方法通过解析频率参数，生成从开始日期到结束日期之间符合频率的执行时间数组。
     *
     * @param splitConversion 执行频率，由外部传入，决定了生成执行时间的规则。
     * @return 返回一个LocalDateTime数组，包含所有符合频率的执行时间。
     */
//    protected LocalDateTime[] getExecutionDates(CisSplitConversion splitConversion) {
//        // 通过CisFrequenciesService.getTimes(frequency)获取到符合频率的字符串时间数组
//        // 然后通过stream()和map()将其转换为LocalDateTime类型的数组
//        return CisFrequenciesService.getTimes(splitConversion.getApply().getFrequency()).stream().map(p -> {
//            // 定义日期时间格式化器，用于解析字符串时间
//            DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
//            // 将当前日期与传入的时间字符串拼接，形成完整的LocalDateTime字符串，并进行解析
//            return LocalDateTime.parse(LocalDateUtil.now().toLocalDate().toString() + "T" + p + ":00", formatter);
//        }).toArray(LocalDateTime[]::new);
//    }
    protected LocalDateTime[] getExecutionDates(CisSplitConversion splitConversion) {
        return new LocalDateTime[]{LocalDateTime.now()};
    }

    @Override
    protected CisBaseApply doApply(CisBaseApplyNto applyNto, Boolean save) {
        CisBaseApply apply = CisBaseApply.newInstanceByNto(applyNto);
        apply.setStatusCode(CisStatusEnum.ACTIVE);
        applyNto.setId(applyCodeServiceProxy.getApplyNextCode());
        apply.create(applyNto, save);
        return apply;
    }

    protected CisBaseApply doApplyProof(CisBaseApplyNto applyNto, Boolean save) {
        CisBaseApply apply = CisBaseApply.newInstanceByNto(applyNto);
        apply.setStatusCode(CisStatusEnum.ACTIVE);
        apply.createProof(applyNto, save);
        return apply;
    }

    @Override
    protected List<CisSplitConversion> getCisSplitConversion(List<CisSplitEto> etos, List<CisBaseApplyNto> applies) {
        // 初始化拆分转换对象的列表，用于存储匹配后的结果。
        return applies.stream().map(p -> new CisSplitConversion(p)).toList();
    }

    @Override
    protected List<CisBaseApplyNto> getCisBaseApplyNtos(List<CisBaseApplyNto> applies) {
        if (CollectionUtils.isEmpty(applies)) {
            return applies;
        }
        return applies.stream().filter(p -> OrderTypeEnum.TEMPORARY_ORDER.equals(p.getOrderType())).toList();
    }

    @Override
    protected List<CisBaseApplyNto> getApplyNtos(List<CisSplitEto> etos) {
        List<String> orderids = etos.stream().map(CisSplitEto::getOrderId).collect(Collectors.toList());
        ApplyCreateService applyCreateService = SpringUtil.getBean(ApplyCreateService.class);
        List<CisBaseApplyNto> applies = applyCreateService.getProofApplys(etos.get(0).getNurseDeptCode(), orderids);
        return getCisBaseApplyNtos(applies);
    }


    @Override
    protected void judgeOrderPlan(String visitCode, List<CisProofNto> orderPlans) {
        // 根据访问代码和活动状态获取申请列表
        List<CisBaseApply> applyList = CisBaseApply.findCisBaseAppliesByVisitCodeAndStatusCode(visitCode, CisStatusEnum.ACTIVE);

        // 将申请列表中的申请转换为申请ID列表
        List<String> applyIds = applyList
                .stream().map(CisBaseApply::getId).toList();

        // 断言申请ID列表非空，否则抛出异常
        BusinessAssert.notNull(applyIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0006, visitCode + "校对");

        // 检查订单计划中是否存在至少一个不在申请ID列表中的计划
        BusinessAssert.isTrue(orderPlans.stream().anyMatch(p -> !applyIds.contains(p.getApplyId())),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0003, visitCode + "校对");

        // 过滤申请列表，移除那些在订单计划中已经存在的申请
        applyList = applyList.stream().filter(p -> !orderPlans.stream().anyMatch(q -> q.getApplyId().equals(p.getId()))).toList();

        // 对剩余的申请执行校对操作
        applyList.forEach(p -> p.proof2());
    }


}