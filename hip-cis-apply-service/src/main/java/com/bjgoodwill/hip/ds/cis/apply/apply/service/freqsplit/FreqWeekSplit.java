package com.bjgoodwill.hip.ds.cis.apply.apply.service.freqsplit;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.base.cis.dict.frequency.to.FrequencyTo;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisSplitConversion;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-18 10:59
 * @className: FreqWeekSplit
 * @description:
 **/
public class FreqWeekSplit extends FreqSplit {

    /**
     * 根据指定的起始日期、转换规则和频率，分割日期范围内的时间点。
     * 该方法用于计算从上一个分割点到当前结束日期之间，按照特定频率应生成的时间点列表。
     *
     * @param lastSplitDate   上一个分割点的时间，用于计算下一个分割点的起始位置。
     * @param splitConversion 分割转换规则，包含结束日期和频率等信息。
     * @param frequencyTo     频率转换对象，用于确定时间点的生成频率。
     * @return 返回一个LocalDateTime类型的列表，包含按照指定频率分割出的时间点。
     */
    @Override
    protected List<LocalDateTime> splitDate(LocalDateTime lastSplitDate, CisSplitConversion splitConversion, FrequencyTo frequencyTo) {
        List<LocalDateTime> times = new ArrayList<>();
        int freqTime = frequencyTo.getFreqTime();

        // 计算从lastSplitDate到splitConversion.getEndDate()之间的天数
        long dayNum = (long) ChronoUnit.DAYS.between(lastSplitDate, splitConversion.getEndDate());
        for (int i = 0; i <= dayNum; i++) {
            // 根据频率调整循环增量，以满足不同频率的时间点生成
            i = i * freqTime;
            LocalDateTime time;
            // 根据频率是否为1，采取不同的时间计算方法
            if (freqTime == 1) {
                // 当频率为1时，调用getOneWeedMore方法计算时间点
                time = getOneWeedMore(getTimes(frequencyTo), i, lastSplitDate);
            } else {
                // 当频率不为1时，直接计算每天的23:00作为时间点
                time = getDateTime(lastSplitDate.plusDays(i), "23:00");
            }
            // 如果计算出的时间点不为空，则添加到结果列表中
            if (time != null) {
                times.add(time);
            }
        }

        // 过滤结果列表，移除不在splitConversion.getEndDate()之后且lastSplitDate之前的時間点
        return times.stream().filter(time -> time.isBefore(splitConversion.getEndDate()) &&
                time.isAfter(lastSplitDate)
        ).toList();
    }


    /**
     * 获取未来一周内与给定频率时间点匹配的所有日期。
     * <p>
     * 该方法首先验证输入的频率时间点列表不为空，然后将这些时间点转换为一个整数集合，表示星期几。
     * 接着，它计算从给定日期时间开始，按照指定频率向前或向后推移的日期，并检查这个日期是否与输入的频率时间点匹配。
     * 如果匹配，它将这个日期时间格式化为字符串并返回。然而，当前的实现存在一些问题，导致方法始终返回null。
     *
     * @param freqTimepoints 一个表示星期几的数字列表，1表示星期一，2表示星期二，以此类推，7表示星期日。
     * @param freqTime       频率时间，表示要向前或向后推移的天数。
     * @param dateTime       起始日期时间。
     * @return 匹配的日期时间列表，以字符串格式表示。
     */
    private LocalDateTime getOneWeedMore(List<String> freqTimepoints, int freqTime, LocalDateTime dateTime) {
        // 验证输入的频率时间点列表不为空
        BusinessAssert.notEmpty(freqTimepoints, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "频次点！");

        // 将频率时间点转换为整数集合，表示星期几
        // 将freqTimepoints转换为Set以提高查找效率
        Set<Integer> weekdaysAsSet = new HashSet<>();
        for (String point : freqTimepoints) {
            // 确保每个时间点的格式正确
//            Assert.isTrue(point == null || point.isEmpty() || point.length() > 1, "频次点格式不正确！");
            BusinessAssert.isTrue(point == null || point.isEmpty() || point.length() > 1, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0005,
                    "频次点");
            // 将时间点字符串转换为整数，并验证其范围在1到7之间
            int dayOfWeekValue = Integer.parseInt(point);
//            Assert.isTrue(dayOfWeekValue < 1 || dayOfWeekValue > 7, "周频次应在1~7之间！");
            BusinessAssert.isTrue(dayOfWeekValue < 1 || dayOfWeekValue > 7,
                    CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0005, "周频次应在1~7之间");
            weekdaysAsSet.add(dayOfWeekValue);
        }

        // 计算按照频率推移后的日期时间
        LocalDateTime date = dateTime.plusDays(freqTime);
        // 检查推移后的日期是否与频率时间点匹配
        if (weekdaysAsSet.contains(date.getDayOfWeek().getValue())) {
            return getDateTime(date, "23:00");
        }
        // 方法按设计应返回匹配的日期时间列表，但目前实现返回null
        return null;
    }


}