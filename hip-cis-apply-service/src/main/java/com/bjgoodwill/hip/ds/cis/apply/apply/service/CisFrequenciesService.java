package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.base.cis.dict.frequency.enmus.TimeUnitEnum;
import com.bjgoodwill.hip.ds.base.cis.dict.frequency.to.FrequencyTo;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.freqsplit.FreqDaySplit;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.freqsplit.FreqHourSplit;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.freqsplit.FreqSplit;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.freqsplit.FreqWeekSplit;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-10 09:16
 * @className: CisFrequenciesService
 * @description: 频次服务
 **/
@Component
public class CisFrequenciesService {
    private static Map<String, FrequencyTo> frequenciesMap;

    private static Map<TimeUnitEnum, FreqSplit> timeUnitMap
            = new HashMap<>() {{
        put(TimeUnitEnum.D, new FreqDaySplit());
        put(TimeUnitEnum.H, new FreqHourSplit());
        put(TimeUnitEnum.W, new FreqWeekSplit());
    }};

    //扣除长期的
    public static void setFrequencies(List<FrequencyTo> frequencies) {
        frequenciesMap = frequencies.stream()
                .collect(Collectors.toMap(FrequencyTo::getCode, v -> v));
    }

    //临时医嘱用
    public static List<String> getTimes(String frequencyCode) {
        BusinessAssert.hasText(frequencyCode, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "频次编码！");
        FrequencyTo frequencyTo = frequenciesMap.get(frequencyCode);

        String[] freqTimepoint = frequencyTo.getFreqTimepoint().split("-");
        BusinessAssert.notEmpty(freqTimepoint, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "频次点！");
//        Assert.notEmpty(freqTimepoint, frequencyTo.getCode() + "频次点为空！");

//        Assert.isTrue(freqTimepoint.length == frequencyTo.getFreqTime(), frequencyTo.getCode() + "频次数量和格式对不上");
//        BusinessAssert.isTrue(freqTimepoint.length == frequencyTo.getFreqTime(),
//                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0005, "频次数量和格式");

        return Arrays.stream(freqTimepoint).toList();
    }

    //长期医嘱用
    public static List<LocalDateTime> getTimes(CisSplitConversion splitConversion) {
        //splitConversion 前面校验过了 这里不校验了。

        String frequencyCode = splitConversion.getApply().getFrequency();
        List<String> times = getTimes(frequencyCode);

        //首选 计算出 开始时间和结束时间 间隔的天数
        FrequencyTo frequencyTo = frequenciesMap.get(frequencyCode);
        BusinessAssert.notNull(frequencyTo, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, frequencyCode + "编码的频次");

        //频次单位，小时，日，周
        TimeUnitEnum timeUnit = frequencyTo.getFreqTimeunit();

        return timeUnitMap.get(timeUnit).compute(splitConversion, frequencyTo);
    }

}