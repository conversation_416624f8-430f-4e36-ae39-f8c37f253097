package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.ds.cis.apply.detail.service.internal.ApplyWithDetialServiceImpl;
import com.bjgoodwill.hip.ds.cis.apply.detail.service.internal.ApplyWithDetialServiceTangibleImpl;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailTo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-04-10 16:54
 * @className: CisDetailQueryService
 * @description:
 **/
@Component
public class CisDetailQueryService {
    @Autowired
    private ApplicationContext applicationContext;

    public List<DetailTo> queryDetailToByCreateDate(LocalDateTime dateTime) {
        Map<String, ApplyWithDetialServiceImpl> map = applicationContext.getBeansOfType(ApplyWithDetialServiceImpl.class);
        return map.values().stream()
                .filter(service -> !(service instanceof ApplyWithDetialServiceTangibleImpl))
                .flatMap(service -> service.queryDetailToByCreateDate(dateTime).stream())
                .flatMap(Stream::ofNullable)
                .collect(Collectors.toList());
    }
}