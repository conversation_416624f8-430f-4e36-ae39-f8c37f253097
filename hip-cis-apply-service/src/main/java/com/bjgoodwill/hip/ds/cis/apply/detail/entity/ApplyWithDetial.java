package com.bjgoodwill.hip.ds.cis.apply.detail.entity;

import cn.hutool.core.collection.CollectionUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.charge.entity.CisApplyCharge;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetailEto;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetialNto;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailEto;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailNto;
import com.bjgoodwill.hip.ds.cis.apply.diag.entity.ApplyDiagnosis;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyNto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-24 16:27
 * @className: applyWithDetial
 * @description:
 **/
public abstract class ApplyWithDetial<T extends BaseDetail> extends CisBaseApply {

    public abstract List<T> getDetailList();


    /**
     * 删除详细信息。
     * 该方法用于从列表中删除指定ID的详细信息项，并相应地更新关联的费用信息。
     * 如果所有详细信息都被删除，则进一步删除整个业务实体。
     *
     * @param details   详细信息列表，包含待删除的详细信息项。
     * @param detailids 待删除详细信息项的ID列表。
     */
    public void deleteDetails(List<T> details, List<String> detailids) {
        // 根据待删除ID列表过滤出需要删除的详细信息项
        // 过滤出需要删除的明细项
        List<T> lst = details.stream().filter(p -> detailids.contains(p.getId())).toList();

        // 确保待删除的详细信息项存在，否则抛出业务异常
        BusinessAssert.notNull(lst, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "删除明细！");

        // 检查删除后列表是否为空，如果为空，则调用delete()方法
//        if (lst.size() == details.size()) {
//            delete();
//        }

        // 删除关联的费用信息
        List<CisApplyCharge> charges = getCisApplyCharges().stream().filter(p -> detailids.contains(p.getDetailId())).toList();
        if (!CollectionUtils.isEmpty(charges)) {
            charges.forEach(p -> p.delete());
        }

        // 删除详细信息列表中的详细信息项
        lst.stream().forEach(p -> p.delete());
    }

    //拆组
    @Transactional()
    public CisBaseApply splitGroup(ApplyWithDetialNto nto, List<String> detailids) {
        BusinessAssert.notEmpty(detailids, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "拆分组信息时，详情ID列表不能为空！");

        changeDetialApplyCode(nto.getOrderID(), nto.getId(), detailids);
        List<String> chargeIds = nto.getCisApplyCharges().stream().map(o -> o.getId()).toList();
        // 创建并返回拆分后的CisBaseApply对象

        CisBaseApply create = CisBaseApply.newInstanceByNto(nto);
        create.setStatusCode(CisStatusEnum.NEW);
        CisBaseApply apply = create.create(nto, true);

        CisApplyCharge.updateApplyIdByIdsIn(chargeIds, nto.getId(), nto.getOrderID());
        if (!CollectionUtils.isEmpty(nto.getApplyDiagnosisNtos())) {
            List<String> diagIds = nto.getApplyDiagnosisNtos().stream().map(o -> o.getId()).toList();
            ApplyDiagnosis.updateApplyIdByIds(diagIds, nto.getId());
        }
        return apply;
    }


    protected void changeDetialApplyCode(String newApplyId) {

    }

    protected void changeDetialApplyCode(String newOrderId, String newApplyId, List<String> detailids) {
//        // 获取当前对象中的药物申请详情列表
//        List<T> applyDetails = splitGroupDetial(newApplyId, detailids, false);

//        if (!CollectionUtils.isEmpty(applyDetails)) {
////            List<String> codes = getSplitCodes(applyDetails);
//
//            //加费在明细上的 也要带走
////            List<String> detailIds = applyDetails.stream().map(p -> p.getId()).toList();
////            mergeGroupCharge(newApplyId, codes, detailIds);
//
//            applyDetails.forEach(p -> p.setApplyId(newApplyId));
//        }
    }

    /**
     * 合并组内费用项。
     * 根据申请ID、费用项代码列表和详情ID列表，从现有的费用项列表中筛选出匹配的费用项，并更新它们的申请ID。
     * 此方法主要用于处理费用项合并的场景，确保相同申请下的费用项能够被正确地聚合。
     *
     * @param applyId   申请ID，用于更新费用项的申请ID。
     * @param codes     费用项代码列表，用于筛选费用项。
     * @param detailIds 详情ID列表，用于进一步筛选费用项。
     * @return 返回筛选并更新后的费用项列表。
     */
    private List<CisApplyCharge> mergeGroupCharge(String applyId, List<String> codes, List<String> detailIds) {
        // 获取所有的费用项列表
        List<CisApplyCharge> cisApplyCharges = getCisApplyCharges();
        // 如果费用项列表为空，则直接返回
        if (CollectionUtils.isEmpty(cisApplyCharges)) {
            return cisApplyCharges;
        }

        // 过滤费用项列表，仅保留代码在codes列表中或详情ID在detailIds列表中的费用项
        cisApplyCharges = cisApplyCharges.stream()
                .filter(cisApplyCharge -> codes.contains(cisApplyCharge.getPriceItemCode())
                        || (!CollectionUtils.isEmpty(detailIds) && detailIds.contains(cisApplyCharge.getDetailId()))
                ).toList();
        // 如果过滤后的费用项列表为空，则直接返回
        if (CollectionUtils.isEmpty(cisApplyCharges)) {
            return cisApplyCharges;
        }

        // 更新费用项的申请ID为传入的applyId
        cisApplyCharges.stream().forEach(p -> p.updateApplyId(applyId));

        // 返回更新后的费用项列表
        return cisApplyCharges;
    }


    protected abstract List<String> getSplitCodes(List<T> applyDetails);


    /**
     * 修改申请单明细申请单ID。
     * 如果all参数为真，则检查是否所有详情都属于同一申请，否则只处理列表中的详情。
     *
     * @param applyId   药物申请的ID，用于更新药物申请详情的申请ID。
     * @param detailids 药物申请详情的ID列表，用于筛选需要拆分的详情。
     * @param split     如果为真，则检查是否所有详情都属于同一申请；否则只处理列表中的详情。
     * @return 更新后的药物申请详情列表。
     */
    private List<T> splitGroupDetial(String applyId, List<String> detailids, Boolean split) {
        // 获取所有药物申请详情
        List<T> applyDetails = getDetailList();
        // 如果详情列表为空，则直接返回空列表
        // 检查药物申请详情列表是否为空
        if (CollectionUtils.isEmpty(applyDetails)) {
            return applyDetails;
        }

        // 将详情ID列表转换为HashSet，以提高查找性能
        // 将详情ID列表转换为HashSet以优化查找性能
        Set<String> detailIdSet = new HashSet<>(detailids);
        // 筛选出需要拆分的药物申请详情
        // 筛选出详情ID列表中的匹配项，即需要进行拆分处理的药物申请详情
        List<T> detailList =
                applyDetails.stream().filter(applyDetail -> detailIdSet.contains(applyDetail.getId())).toList();

        // 如果all为真，检查是否所有详情都属于同一申请
        if (split) {
//            Assert.isTrue(applyDetails.size() == detailList.size(), "拆组数量和组内数量一致，无法拆组！");
            BusinessAssert.isTrue(applyDetails.size() == detailList.size(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014,
                    "拆组数量和组内数量一致，无法拆组");
        }

        // 如果筛选后的详情列表为空，则直接返回空列表
        // 检查筛选后的药物申请详情列表是否为空
        if (CollectionUtils.isEmpty(applyDetails)) {
            return applyDetails;
        }
        // 更新筛选出的详情的申请ID
        applyDetails.forEach(o -> o.setApplyId(applyId));
        // 返回更新后的详情列表
        return applyDetails;
    }


    public void mergeGroup(String orderId, String applyId, Map<String, ApplyWithDetailEto> detailEtoMap) {

        List<String> applyCodes = detailEtoMap.keySet().stream().toList();

        CisBaseApply.findCisBaseAppliesByApplyIDIn(applyCodes).stream()
                .filter(ApplyWithDetial.class::isInstance)
                .forEach(p -> {
                    ((ApplyWithDetial<?>) p).mergeDelete(orderId, applyId, detailEtoMap.get(p.getId()));
                });

//        List<String> chargeIds = detailids.stream()
//                .map(p->p.getCisApplyChargeEtos())
//                .flatMap(List::stream)
//                .map(CisApplyChargeEto::getId)
//                .toList();
//        CisApplyCharge.updateApplyIdByIdsIn(chargeIds,applyId);

        //申请单明细id.
//        List<String> detailIds = detailids.stream()
//                .map(v -> v.getDetailEtos())
//                .flatMap(List::stream)
//                .map(p -> ((DetailEto) p).getId())
//                .toList();
//        changeDetialApplyCode(applyId, detailIds);
    }


    public ApplyWithDetial getMergeMain(List<ApplyWithDetial> applyList) {
        //获取明细和费用
        List<BaseDetail> details = applyList.stream().map(p -> p.getDetailList())
                .flatMap(List::stream)
                .filter(BaseDetail.class::isInstance)
                .map(BaseDetail.class::cast)
                .toList();

        //如果有抗菌药 直接把抗菌药扣出来 抗菌药的医嘱作为合并的主医嘱。
        String applyId = getMainId(details, applyList);


        return applyList.stream()
                .filter(p -> p.getId().equals(applyId)).findFirst().orElse(applyList.get(0));
    }

    protected String getMainId(List<BaseDetail> details, List<ApplyWithDetial> applyList) {
        return applyList.get(0).getId();
    }


    public CisBaseApply updateChargeDetailID(String detailId, ApplyWithDetialNto applyNto) {
        return this;
        //后面暂时不用了id,页面拼。
//        List<CisApplyChargeNto> cisApplyCharges = applyNto.getCisApplyCharges();
//        if(CollectionUtils.isEmpty(cisApplyCharges)){
//            return this;
//        }
//        List<String> ids = cisApplyCharges.stream().map(p->p.getId()).toList();
//
//        CisApplyCharge.updateDetailidByIdIn(ids,detailId);
//        return this;
    }


    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((ApplyWithDetialNto) cisBaseApplyNto, save);
    }

    public ApplyWithDetial create(ApplyWithDetialNto applyNto, Boolean save) {
        super.create(applyNto, save);
//        BusinessAssert.notEmpty(applyNto.getDetails(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, applyNto.getOrderID() + "申请单明细");
        if (CollectionUtil.isNotEmpty(applyNto.getDetails())) {
            applyNto.getDetails().stream().filter(DetailNto.class::isInstance)
                    .forEach(p -> ((DetailNto) p).setHipId()
                    );
            String code = getServiceCode(applyNto.getDetails());
            setServiceItemCode(code);
        }
        //草药的太多了 不存了 没啥用。
        if (applyNto instanceof CisCDrugApplyNto) {
            return this;
        }

        return this;
    }

    protected String getServiceCode(List<DetailNto> details) {
        return StringUtils.join(details.stream().filter(DetailNto.class::isInstance)
                .map(p -> (p).getServiceItemCode()).toList().toArray(), ",");
    }

    protected void mergeDelete(String orderId, String newApplyCode, ApplyWithDetailEto eto) {
        if (!CollectionUtils.isEmpty(eto.getCisApplyChargeEtos())) {
            List<String> chargeIds = eto.getCisApplyChargeEtos().stream()
                    .map(p -> p.getId()).toList();
            CisApplyCharge.updateApplyIdByIdsIn(chargeIds, newApplyCode, orderId);
        }

        if (!CollectionUtils.isEmpty(eto.getDetailEtos())) {
            List<String> detailIds = eto.getDetailEtos().stream()
                    .map(p -> ((DetailEto) p).getId()).toList();
            changeDetialApplyCode(orderId, newApplyCode, detailIds);
        }
        this.delete();
    }

    public void mergeDelete(String orderId, String newApplyCode) {

        if (!CollectionUtils.isEmpty(getCisApplyCharges())) {
            List<String> chargeIds = getCisApplyCharges().stream()
                    .map(p -> p.getId()).toList();
            CisApplyCharge.updateApplyIdByIdsIn(chargeIds, newApplyCode, orderId);
        }

        if (!CollectionUtils.isEmpty(getDetailList())) {
            List<String> detailIds = getDetailList().stream()
                    .map(p -> ((BaseDetail) p).getId()).toList();
            changeDetialApplyCode(orderId, newApplyCode, detailIds);
        }
        this.delete();
    }

    public CisBaseApply splitCreate(CisBaseApplyNto nto, BaseDetail baseDetail) {

        if (CollectionUtil.isNotEmpty(getCisApplyCharges())) {
            List<String> chargeIds = getCisApplyCharges().stream()
                    .filter(o -> o.getDetailId().equals(baseDetail.getId()))
                    .map(p -> p.getId()).toList();
            CisApplyCharge.updateApplyIdByIdsIn(chargeIds, nto.getId(), nto.getOrderID());
        }

        List<String> detailIds = Collections.singletonList(baseDetail.getId());
        changeDetialApplyCode(nto.getOrderID(), nto.getId(), detailIds);
//        nto.setServiceItemCode(baseDetail.getServiceItemCode());

        CisBaseApply create = CisBaseApply.newInstanceByNto(nto);
        create.setStatusCode(CisStatusEnum.NEW);
        return create.create(nto, true);
    }

    public String getServiceCodeByEto(List<DetailEto> details) {
        return "";
    }

//    public String getServiceItemCode() {
//        return StringUtils.join(Arrays.stream(getDetailList().stream().map(p -> p.getServiceItemCode()).toArray()), ",");
//    }

    //    public void setApplyDetail(String visitCode, List<CisBaseApplyTo> cisBaseApplyTos) {
//        // 过滤出所有SPCOBS类型的申请对象
//        var spcobsApplyTos = getApplyBaseTos(cisBaseApplyTos);
//        // 如果有SPCOBS类型的申请对象
//        if(!CollectionUtils.isEmpty(spcobsApplyTos)){
//            // 根据访问代码查询SPCOBS申请详细信息，并按申请ID分组
//            Map<String, List<T>> spcobsApplyDetailMap =
//                    findCisApplyDetailsByVisitCode(visitCode).stream()
//                            .collect(Collectors.groupingBy(T::getApplyId));
//
//            // 遍历每个申请ID及其详细的SPCOBS申请信息
//            // 为每个SPCOBS申请填充详细信息。
//            spcobsApplyDetailMap.forEach((applyId, details) -> Optional.ofNullable(spcobsApplyTos.stream()
//                            .filter(applyTo -> applyTo.getId().equals(applyId))
//                            .findFirst()
//                            .orElse(null))
//                    .ifPresent(applyTo ->
//                            // 使用组装器将详细的SPCOBS申请信息转换为合适的格式，并设置到申请对象中
//                            setApplyBaseToDetail(applyTo,details)));
//
//        }
//    }
//
//    protected abstract List<T> findCisApplyDetailsByVisitCode(String vistCode);
//    protected abstract List<CisBaseApplyTo> getApplyBaseTos(List<CisBaseApplyTo> cisBaseApplyTos);
//    protected abstract void setApplyBaseToDetail(CisBaseApplyTo applyBaseTo, List<T> applyDetails);
}