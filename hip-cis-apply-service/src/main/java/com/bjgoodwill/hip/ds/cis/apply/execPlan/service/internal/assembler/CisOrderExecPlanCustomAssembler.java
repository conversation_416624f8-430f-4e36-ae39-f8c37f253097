package com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlanCharge;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanCustomTo;

public abstract class CisOrderExecPlanCustomAssembler {

    /**
     * @generated
     */
    public static <T> CisOrderExecPlanCustomTo toTo(CisOrderExecPlan cisOrderExecPlan, T detail) {
        if (cisOrderExecPlan == null)
            return null;
        CisOrderExecPlanCustomTo to = new CisOrderExecPlanCustomTo();
//        to.setId(cisOrderExecPlan.getId());
        to.setCisOrderExecPlanId(cisOrderExecPlan.getId());
        to.setPatMiCode(cisOrderExecPlan.getPatMiCode());
        to.setVisitCode(cisOrderExecPlan.getVisitCode());
        to.setOrgCode(cisOrderExecPlan.getOrgCode());
        to.setOrgName(cisOrderExecPlan.getOrgName());
        to.setDeptNurseCode(cisOrderExecPlan.getDeptNurseCode());
        to.setOrderId(cisOrderExecPlan.getOrderId());
        to.setCisBaseApplyId(cisOrderExecPlan.getCisBaseApplyId());
        to.setSortNo(cisOrderExecPlan.getSortNo());
        to.setServiceItemCode(cisOrderExecPlan.getServiceItemCode());
        to.setServiceItemName(cisOrderExecPlan.getServiceItemName());
        to.setOrderClass(cisOrderExecPlan.getOrderClass());
        to.setSkinResult(cisOrderExecPlan.getSkinResult());
        to.setReceiveOrg(cisOrderExecPlan.getReceiveOrg());
        to.setReceiveOrgName(cisOrderExecPlan.getReceiveOrgName());
        to.setExecPlanDate(cisOrderExecPlan.getExecPlanDate());
        to.setExecDate(cisOrderExecPlan.getExecDate());
        to.setExecStaff(cisOrderExecPlan.getExecStaff());
        to.setExecStaffName(cisOrderExecPlan.getExecStaffName());
        to.setIsCharge(cisOrderExecPlan.getIsCharge());
        to.setChargeDate(cisOrderExecPlan.getChargeDate());
        to.setChargeStaff(cisOrderExecPlan.getChargeStaff());
        to.setChargeStaffName(cisOrderExecPlan.getChargeStaffName());
        to.setChargeOrg(cisOrderExecPlan.getChargeOrg());
        to.setChargeOrgName(cisOrderExecPlan.getChargeOrgName());
        to.setIsPrint(cisOrderExecPlan.getIsPrint());
        to.setPrintDate(cisOrderExecPlan.getPrintDate());
        to.setPrintStaff(cisOrderExecPlan.getPrintStaff());
        to.setPrintStaffName(cisOrderExecPlan.getPrintStaffName());
        to.setSkinTestDate(cisOrderExecPlan.getSkinTestDate());
        to.setStStaffA(cisOrderExecPlan.getStStaffA());
        to.setStStaffAName(cisOrderExecPlan.getStStaffAName());
        to.setStStaffB(cisOrderExecPlan.getStStaffB());
        to.setStStaffBName(cisOrderExecPlan.getStStaffBName());
        to.setHeldStaff(cisOrderExecPlan.getHeldStaff());
        to.setHeldStaffName(cisOrderExecPlan.getHeldStaffName());
        to.setCreateOrgCode(cisOrderExecPlan.getCreateOrgCode());
        to.setCreateOrgName(cisOrderExecPlan.getCreateOrgName());
        to.setStatusCode(cisOrderExecPlan.getStatusCode());
        to.setCreatedStaff(cisOrderExecPlan.getCreatedStaff());
        to.setCreatedDate(cisOrderExecPlan.getCreatedDate());
        to.setUpdatedStaff(cisOrderExecPlan.getUpdatedStaff());
        to.setUpdatedStaffName(cisOrderExecPlan.getUpdatedStaffName());
        to.setUpdatedDate(cisOrderExecPlan.getUpdatedDate());
        to.setVersion(cisOrderExecPlan.getVersion());
        to.setIsSend(cisOrderExecPlan.getIsSend());
        to.setDrugInoutType(cisOrderExecPlan.getDrugInoutType());
        to.setThridStatus(cisOrderExecPlan.getThridStatus());
        to.setUsage(cisOrderExecPlan.getUsage());
        to.setBatchNo(cisOrderExecPlan.getBatchNo());
//        to.setTreatmentCode(cisOrderExecPlan.getTreatmentCode());
        // 处理明细
        if (detail == null) {
            return to;
        }
        CisOrderExecPlanCharge charge = (CisOrderExecPlanCharge) detail;
        to.setId(charge.getId());
        to.setPriceItemCode(charge.getPriceItemCode());
        to.setPriceItemName(charge.getPriceItemName());
        to.setServiceItemCode(charge.getPriceItemCode());
        to.setServiceItemName(charge.getPriceItemName());
        to.setPackageSpec(charge.getPackageSpec());
        to.setPrice(charge.getPrice());
        to.setUnit(charge.getUnit());
        to.setUnitName(charge.getUnitName());
        to.setNum(charge.getNum());
        to.setChargeAmount(charge.getChargeAmount());
        to.setIsFixed(charge.getIsFixed());
        to.setLimitConformFlag(charge.getLimitConformFlag());
        to.setExecuteOrgCode(charge.getExecuteOrgCode());
        to.setExecuteOrgName(charge.getExecuteOrgName());
        to.setExecOrgCode(charge.getExecuteOrgCode());
        to.setExecOrgName(charge.getExecuteOrgName());
//        to.setSetlStas(charge.getSetlStas());
        to.setChargeType(charge.getChargeType());
        to.setSystemItemClass(charge.getSystemItemClass());
        return to;
    }
}