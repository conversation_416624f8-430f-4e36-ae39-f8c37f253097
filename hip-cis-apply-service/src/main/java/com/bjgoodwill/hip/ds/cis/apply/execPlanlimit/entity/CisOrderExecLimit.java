package com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.repository.CisOrderExecLimitRepository;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to.CisOrderExecLimitEto;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to.CisOrderExecLimitNto;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to.CisOrderExecLimitQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "执行单第三方状态操作限制")
@Table(name = "cis_order_exec_limit", indexes = {}, uniqueConstraints = {})
public class CisOrderExecLimit {

    // 标识
    private String id;
    // 第三方状态名称
    private String thirdStatus;
    // false 允许不执行
    private Boolean noExecFlag;
    // false 允许取消执行
    private Boolean cancelExecFlag;
    // false 允许退费
    private String refundsFlag;

    public static Optional<CisOrderExecLimit> getCisOrderExecLimitById(String id) {
        return dao().findById(id);
    }

    public static List<CisOrderExecLimit> getCisOrderExecLimits(CisOrderExecLimitQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisOrderExecLimit> getCisOrderExecLimitPage(CisOrderExecLimitQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisOrderExecLimit> getSpecification(CisOrderExecLimitQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getThirdStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("thirdStatus"), qto.getThirdStatus()));
            }
            if (qto.getNoExecFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("noExecFlag"), qto.getNoExecFlag()));
            }
            if (qto.getCancelExecFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cancelExecFlag"), qto.getCancelExecFlag()));
            }
            if (StringUtils.isNotBlank(qto.getRefundsFlag())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("refundsFlag"), qto.getRefundsFlag()));
            }
            return predicate;
        };
    }

    private static CisOrderExecLimitRepository dao() {
        return SpringUtil.getBean(CisOrderExecLimitRepository.class);
    }

    public static List<CisOrderExecLimit> findAll() {
        return dao().findAll();
    }

    public static List<CisOrderExecLimit> findNoExecByExecPlanIds(List<String> orderPlanIds) {
        return dao().findNoExecByExecPlanIds(orderPlanIds);
    }

    public static List<CisOrderExecLimit> findCancelExecByExecPlanIds(List<String> orderPlanIds) {
        return dao().findCancelExecByExecPlanIds(orderPlanIds);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("第三方状态名称")
    @Column(name = "third_status", nullable = true)
    public String getThirdStatus() {
        return thirdStatus;
    }

    protected void setThirdStatus(String thirdStatus) {
        this.thirdStatus = thirdStatus;
    }

    @Comment("false 允许不执行")
    @Column(name = "no_exec_flag", nullable = true)
    public Boolean getNoExecFlag() {
        return noExecFlag;
    }

    protected void setNoExecFlag(Boolean noExecFlag) {
        this.noExecFlag = noExecFlag;
    }

    @Comment("false 允许取消执行")
    @Column(name = "cancel_exec_flag", nullable = true)
    public Boolean getCancelExecFlag() {
        return cancelExecFlag;
    }

    protected void setCancelExecFlag(Boolean cancelExecFlag) {
        this.cancelExecFlag = cancelExecFlag;
    }

    @Comment("false 允许退费")
    @Column(name = "refunds_flag", nullable = true)
    public String getRefundsFlag() {
        return refundsFlag;
    }

    protected void setRefundsFlag(String refundsFlag) {
        this.refundsFlag = refundsFlag;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOrderExecLimit other = (CisOrderExecLimit) obj;
        return Objects.equals(id, other.id);
    }

    public CisOrderExecLimit create(CisOrderExecLimitNto cisOrderExecLimitNto) {
        Assert.notNull(cisOrderExecLimitNto, "参数cisOrderExecLimitNto不能为空！");

        setId(cisOrderExecLimitNto.getId());
        setThirdStatus(cisOrderExecLimitNto.getThirdStatus());
        setNoExecFlag(cisOrderExecLimitNto.getNoExecFlag());
        setCancelExecFlag(cisOrderExecLimitNto.getCancelExecFlag());
        setRefundsFlag(cisOrderExecLimitNto.getRefundsFlag());
        dao().save(this);
        return this;
    }

    public void update(CisOrderExecLimitEto cisOrderExecLimitEto) {
        setThirdStatus(cisOrderExecLimitEto.getThirdStatus());
        setNoExecFlag(cisOrderExecLimitEto.getNoExecFlag());
        setCancelExecFlag(cisOrderExecLimitEto.getCancelExecFlag());
        setRefundsFlag(cisOrderExecLimitEto.getRefundsFlag());
    }

    public void delete() {
        dao().delete(this);
    }
}
