package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisBaseApplyCustomAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyCustomTo;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisDrugApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.operation.entity.CisOperationApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.palg.entity.CisPalgApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApplyDetail;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @program: HIP5.0-CIS
 * @author: yanht
 * @create: 2025-3-26
 * @className: CisApplyCustomService
 * @description: 医嘱单打印用
 **/
@Component
public class CisApplyCustomService {

    /**
     * 处理带有明细的申请单
     *
     * @param applys
     * @param applyMap
     * @param applyIds
     */
    public List<CisBaseApplyCustomTo> extractedDetail(List<CisBaseApply> applys, Map<String, CisBaseApply> applyMap, List<String> applyIds) {
        List<CisBaseApplyCustomTo> toList = Collections.synchronizedList(new ArrayList<>());
        if (CollectionUtils.isEmpty(applys) || CollectionUtils.isEmpty(applyIds)) {
            return toList;
        }
        // 创建并行任务
        CompletableFuture<Void> drugTask = CompletableFuture.runAsync(() ->
                extractedDrug(applys, applyMap, applyIds, toList));
        CompletableFuture<Void> dgimgTask = CompletableFuture.runAsync(() ->
                extractedDgimg(applys, applyMap, applyIds, toList));
        CompletableFuture<Void> spcobsTask = CompletableFuture.runAsync(() ->
                extractedSpcobs(applys, applyMap, applyIds, toList));
        CompletableFuture<Void> operationTask = CompletableFuture.runAsync(() ->
                extractedOperationapply(applys, applyMap, applyIds, toList));
        CompletableFuture<Void> palgTask = CompletableFuture.runAsync(() ->
                extractedPalg(applys, applyMap, applyIds, toList));
        // 等待所有任务完成
        CompletableFuture.allOf(drugTask, dgimgTask, spcobsTask, operationTask, palgTask).join();

        return toList;
    }

    /**
     * 药品
     *
     * @param applys
     * @param applyMap
     * @param applyIds
     * @param toList
     */
    private void extractedDrug(List<CisBaseApply> applys, Map<String, CisBaseApply> applyMap, List<String> applyIds, List<CisBaseApplyCustomTo> toList) {
        if (!CollectionUtils.isEmpty(applys.stream().filter(a -> SystemTypeEnum.EDRUG.equals(a.getSystemType())).toList())) {
            CisDrugApplyDetail.getByCisBaseDrugApplyIds(applyIds).stream().forEach(detail -> {
                CisBaseApply to = applyMap.get(detail.getApplyId());
                if (to != null) {
                    toList.add(CisBaseApplyCustomAssembler.toTo(to, detail));
                }
            });
        }
    }

    /**
     * 检查
     *
     * @param applys
     * @param applyMap
     * @param applyIds
     * @param toList
     */
    private void extractedDgimg(List<CisBaseApply> applys, Map<String, CisBaseApply> applyMap, List<String> applyIds, List<CisBaseApplyCustomTo> toList) {
        if (!CollectionUtils.isEmpty(applys.stream().filter(a -> SystemTypeEnum.DGIMG.equals(a.getSystemType())).toList())) {
            CisDgimgApplyDetail.getByCisDgimgApplyIds(applyIds).stream().forEach(detail -> {
                CisBaseApply to = applyMap.get(detail.getApplyId());
                if (to != null) {
                    toList.add(CisBaseApplyCustomAssembler.toTo(to, detail));
                }
            });
        }
    }

    /**
     * 检验
     *
     * @param applys
     * @param applyMap
     * @param applyIds
     * @param toList
     */
    private void extractedSpcobs(List<CisBaseApply> applys, Map<String, CisBaseApply> applyMap, List<String> applyIds, List<CisBaseApplyCustomTo> toList) {
        if (!CollectionUtils.isEmpty(applys.stream().filter(a -> SystemTypeEnum.SPCOBS.equals(a.getSystemType())).toList())) {
            CisSpcobsApplyDetail.getByCisSpcobsApplyIds(applyIds).stream().forEach(detail -> {
                CisBaseApply to = applyMap.get(detail.getApplyId());
                if (to != null) {
                    toList.add(CisBaseApplyCustomAssembler.toTo(to, detail));
                }
            });
        }
    }

    /**
     * 手术
     *
     * @param applys
     * @param applyMap
     * @param applyIds
     * @param toList
     */
    private void extractedOperationapply(List<CisBaseApply> applys, Map<String, CisBaseApply> applyMap, List<String> applyIds, List<CisBaseApplyCustomTo> toList) {
        if (!CollectionUtils.isEmpty(applys.stream().filter(a -> SystemTypeEnum.OPERATIONAPPLY.equals(a.getSystemType())
                || SystemTypeEnum.OPERATION.equals(a.getSystemType())).toList())) {
            CisOperationApplyDetail.getByCisOperationApplyIds(applyIds).stream().forEach(detail -> {
                CisBaseApply to = applyMap.get(detail.getApplyId());
                if (to != null) {
                    toList.add(CisBaseApplyCustomAssembler.toTo(to, detail));
                }
            });
        }
    }

    /**
     * 病理
     *
     * @param applys
     * @param applyMap
     * @param applyIds
     * @param toList
     */
    private void extractedPalg(List<CisBaseApply> applys, Map<String, CisBaseApply> applyMap, List<String> applyIds, List<CisBaseApplyCustomTo> toList) {
        if (!CollectionUtils.isEmpty(applys.stream().filter(a -> SystemTypeEnum.PALG.equals(a.getSystemType())).toList())) {
            CisPalgApplyDetail.getByCisPalgApplyIds(applyIds).stream().forEach(detail -> {
                CisBaseApply to = applyMap.get(detail.getApplyId());
                if (to != null) {
                    toList.add(CisBaseApplyCustomAssembler.toTo(to, detail));
                }
            });
        }
    }
}