package com.bjgoodwill.hip.ds.cis.apply.charge.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.econ.enums.SystemItemClassEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.repository.CisApplyChargeRepository;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeEto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import com.google.common.collect.Lists;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import jdk.jfr.Description;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "申请单费用")
@Table(name = "cis_apply_charge", indexes = {@Index(name = "idx_cis_apply_charge_apply_id", columnList = "apply_id"),
        @Index(name = "idx_cis_apply_charge_order_id", columnList = "order_id"),
        @Index(name = "idx_cis_apply_charge_visit_code", columnList = "visit_code"),
        @Index(name = "idx_cis_apply_charge_detail_id", columnList = "detail_id")}, uniqueConstraints = {})
public class CisApplyCharge {

    // 标识
    private String id;
    // 抽象父类标识
    private String applyId;
    // 医嘱id
    private String orderId;
    // 患者接诊流水号
    private String visitCode;
    // 收费项目编码
    private String priceItemCode;
    // 收费项目名称
    private String priceItemName;
    // 固定费用
    private Boolean isFixed;
    // 规格
    private String packageSpec;
    // 单价
    private BigDecimal price;
    // 单位
    private String unit;
    // 单位名称
    private String unitName;
    // 数量
    private Double num;
    // 计费时段
    private String chargeTime;
    // 计费频次
    private String chargeFrequency;
    // 
    private CisStatusEnum statusCode;
    // 创建的人员
    private String createdStaff;
    // 创建的人员
    private String createdStaffName;
    // 创建的时间
    private LocalDateTime createdDate;
    // 最后修改的人员
    private String updatedStaff;
    // 最后修改的人员
    private String updatedStaffName;
    // 最后修改的时间
    private LocalDateTime updatedDate;
    // 执行科室
    private String executeOrgCode;
    // 执行科室名称
    private String executeOrgName;
    // 金额
    private BigDecimal chageAmount;
    // 特限符合标识:1符合,0不符合
    private Boolean limitConformFlag;
    // SPD高值耗材唯一码
    private String barCode;
    // 版本
    private Integer version;

    private boolean deleted;

    private CisChargeTypeEnum chargeType;

    private String detailId;

    private SystemItemClassEnum systemItemClass;

    public CisApplyCharge() {
    }


    public CisApplyCharge(String executeOrgCode) {
        setExecuteOrgCode(executeOrgCode);
    }

    public static Optional<CisApplyCharge> getCisApplyChargeById(String id) {
        return dao().findById(id);
    }

    public static List<CisApplyCharge> getCisApplyCharges(String cisBaseApplyId, CisApplyChargeQto qto) {
        if (cisBaseApplyId != null) {
            qto.setCisBaseApplyId(cisBaseApplyId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisApplyCharge> getCisApplyChargePage(String cisBaseApplyId, CisApplyChargeQto qto) {

        if (cisBaseApplyId != null) {
            qto.setCisBaseApplyId(cisBaseApplyId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisApplyCharge> getByCisBaseApplyId(String cisBaseApplyId) {
        return dao().findByApplyIdAndDeletedFalse(cisBaseApplyId);
    }

    /**
     * @generated
     */
    private static Specification<CisApplyCharge> getSpecification(CisApplyChargeQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getCisBaseApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("applyId"), qto.getCisBaseApplyId()));
            }
            if (StringUtils.isNotBlank(qto.getOrderId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderId"), qto.getOrderId()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getExecuteOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("executeOrgCode"), qto.getExecuteOrgCode()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            return predicate;
        };
    }

    private static CisApplyChargeRepository dao() {
        return SpringUtil.getBean(CisApplyChargeRepository.class);
    }

    public static void deleteallByCisBaseApplyIdInAndStatueCode(List<String> ids, CisStatusEnum status, CisChargeTypeEnum cisChargeTypeEnum) {
        dao().deleteUsageCharge(ids, status, cisChargeTypeEnum);
    }

    public static void updateDetailidByIdIn(List<String> ids, String detailId) {
        dao().updateDetailIdByIdIn(ids, detailId);
    }

    public static void updateApplyIdByIdsIn(List<String> ids, String applyId, String orderId) {
        dao().updateApplyIdByIdsIn(ids, applyId, orderId, HIPLoginUtil.getStaffId(), HIPLoginUtil.getLoginName());
    }

    public static List<CisApplyCharge> findCisApplyChargesByIdIn(List<String> ids) {
        BusinessAssert.notEmpty(ids, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单费用id");
        return Lists.partition(ids, 100).stream().map(p -> dao().findCisApplyChargesByIdInAndDeletedFalse(p))
                .flatMap(List::stream).toList();
//        return dao().findCisApplyChargesByIdInAndDeletedFalse(ids);
    }

    public static List<CisApplyCharge> findCisApplyChargesByVisitCode(String visitCode, CisStatusEnum status, List<String> applyIds) {
        return dao().findCisApplyChargesByVisitCodeAndDeletedFalseAndStatusCodeAndApplyIdIn(visitCode, status, applyIds);
    }

    public static List<CisApplyCharge> findCisApplyChargesByApplyIdIn(List<String> applyIds) {
        BusinessAssert.notEmpty(applyIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单id");

        return Lists.partition(applyIds, 100).stream()
                .map(p ->
                        dao().findCisApplyChargesByApplyIdInAndDeletedFalseAndStatusCode(p, CisStatusEnum.NEW)
                )
                .flatMap(List::stream).toList();
//        return dao().findCisApplyChargesByApplyIdInAndDeletedFalseAndStatusCode(applyIds, CisStatusEnum.NEW);
    }

    @Comment("补费类型")
    @Enumerated(EnumType.STRING)
    @Column(name = "charge_type", nullable = false)
    public CisChargeTypeEnum getChargeType() {
        return chargeType;
    }

    public void setChargeType(CisChargeTypeEnum chargeType) {
        this.chargeType = chargeType;
    }

    @Comment("申请单明细编码")
    @Column(name = "detail_id", nullable = true)
    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
//    @GeneratedValue(generator = "snowflake_generator")
//    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("抽象父类标识")
    @Column(name = "apply_id", nullable = false, length = 50)
    public String getApplyId() {
        return applyId;
    }

    protected void setApplyId(String cisBaseApplyId) {
        this.applyId = cisBaseApplyId;
    }

    @Comment("医嘱id")
    @Column(name = "order_id", nullable = false)
    public String getOrderId() {
        return orderId;
    }

    protected void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Comment("患者接诊流水号")
    @Column(name = "visit_code", nullable = false)
    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    @Comment("收费项目编码")
    @Column(name = "price_item_code", nullable = false)
    public String getPriceItemCode() {
        return priceItemCode;
    }

    protected void setPriceItemCode(String priceItemCode) {
        this.priceItemCode = priceItemCode;
    }

    @Comment("收费项目名称")
    @Column(name = "price_item_name", nullable = true)
    public String getPriceItemName() {
        return priceItemName;
    }

    protected void setPriceItemName(String priceItemName) {
        this.priceItemName = priceItemName;
    }

    @Comment("固定费用")
    @Column(name = "is_fixed", nullable = true)
    public Boolean getIsFixed() {
        return isFixed;
    }

    protected void setIsFixed(Boolean isFixed) {
        this.isFixed = isFixed;
    }

    @Comment("规格")
    @Column(name = "package_spec", nullable = true)
    public String getPackageSpec() {
        return packageSpec;
    }

    protected void setPackageSpec(String packageSpec) {
        this.packageSpec = packageSpec;
    }

    @Comment("单价")
    @Column(name = "price", nullable = false)
    public BigDecimal getPrice() {
        return price;
    }

    protected void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Comment("单位")
    @Column(name = "unit", nullable = true)
    public String getUnit() {
        return unit;
    }

    protected void setUnit(String unit) {
        this.unit = unit;
    }

    @Comment("数量")
    @Column(name = "num", nullable = true)
    public Double getNum() {
        return num;
    }

    protected void setNum(Double num) {
        this.num = num;
    }

    @Comment("计费时段")
    @Column(name = "charge_time", nullable = true)
    public String getChargeTime() {
        return chargeTime;
    }

    protected void setChargeTime(String chargeTime) {
        this.chargeTime = chargeTime;
    }

    @Comment("计费频次")
    @Column(name = "charge_frequency", nullable = true)
    public String getChargeFrequency() {
        return chargeFrequency;
    }

    protected void setChargeFrequency(String chargeFrequency) {
        this.chargeFrequency = chargeFrequency;
    }

    @Comment("申请单费用状态")
    @Column(name = "status_code", nullable = true)
    @Enumerated(EnumType.STRING)
    @Description("每次拆分重新生成申请单的时候需要有不同的状态")
    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    protected void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的人员名称")
    @Column(name = "created_staff_name", nullable = true)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Comment("最后修改的人员名称")
    @Column(name = "updated_staff_name", nullable = true)
    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Comment("执行科室")
    @Column(name = "execute_org_code", nullable = false)
    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    protected void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = executeOrgCode;
    }

    @Comment("执行科室名称")
    @Column(name = "execute_org_name", nullable = true)
    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    @Comment("金额")
    @Column(name = "chage_amount", nullable = true)
    public BigDecimal getChageAmount() {
        return chageAmount;
    }

    protected void setChageAmount(BigDecimal chageAmount) {
        this.chageAmount = chageAmount;
    }

    @Comment("特限符合标识:1符合,0不符合")
    @Column(name = "limit_conform_flag", nullable = true)
    public Boolean getLimitConformFlag() {
        return limitConformFlag;
    }

//    @Transient
//    public CisBaseApply getCisBaseApply() {
//        return CisBaseApply.getCisBaseApplyById(getApplyId()).orElse(null);
//    }

    protected void setLimitConformFlag(Boolean limitConformFlag) {
        this.limitConformFlag = limitConformFlag;
    }

    @Comment("SPD高值耗材唯一码")
    @Column(name = "bar_code", nullable = true)
    public String getBarCode() {
        return barCode;
    }

    protected void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    @Version
    @Comment("版本")
    @Column(name = "version", nullable = false)
    public Integer getVersion() {
        return version;
    }

    protected void setVersion(Integer version) {
        this.version = version;
    }

    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Comment("单位名称")
    @Column(name = "unit_name", nullable = true)
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Comment("系统项目分类")
    @Enumerated(EnumType.STRING)
    @Column(name = "system_item_class", nullable = false)
    public SystemItemClassEnum getSystemItemClass() {
        return systemItemClass;
    }

    public void setSystemItemClass(SystemItemClassEnum systemItemClass) {
        this.systemItemClass = systemItemClass;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisApplyCharge other = (CisApplyCharge) obj;
        return Objects.equals(id, other.id);
    }

    protected CisApplyCharge create(String cisBaseApplyId, CisApplyChargeNto cisApplyChargeNto) {
        BusinessAssert.notNull(cisApplyChargeNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisApplyChargeNto不能为空！");

        setApplyId(cisBaseApplyId);

        setId(cisApplyChargeNto.getId());
        setOrderId(cisApplyChargeNto.getOrderId());
        setVisitCode(cisApplyChargeNto.getVisitCode());
        setPriceItemCode(cisApplyChargeNto.getPriceItemCode());
        setPriceItemName(cisApplyChargeNto.getPriceItemName());
        setIsFixed(cisApplyChargeNto.getIsFixed());
        setPackageSpec(cisApplyChargeNto.getPackageSpec());
        setPrice(cisApplyChargeNto.getPrice());
        setUnit(cisApplyChargeNto.getUnit());
        setUnitName(cisApplyChargeNto.getUnitName());
        setNum(cisApplyChargeNto.getNum());
        setChargeTime(cisApplyChargeNto.getChargeTime());
        setChargeFrequency(cisApplyChargeNto.getChargeFrequency());
        setStatusCode(cisApplyChargeNto.getStatusCode());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        setExecuteOrgCode(cisApplyChargeNto.getExecuteOrgCode());
        setExecuteOrgName(cisApplyChargeNto.getExecuteOrgName());
        setChageAmount(cisApplyChargeNto.getChageAmount());
        setLimitConformFlag(cisApplyChargeNto.getLimitConformFlag());
        setBarCode(cisApplyChargeNto.getBarCode());
        setChargeType(cisApplyChargeNto.getChargeType());
        setUpdatedDate(LocalDateUtil.now());
        setDeleted(false);
        setDetailId(cisApplyChargeNto.getDetailId());
        setSystemItemClass(cisApplyChargeNto.getSystemItemClass());
//        dao().save(this);
        return this;
    }

    public CisApplyCharge create(String cisBaseApplyId, CisApplyChargeNto cisApplyChargeNto, Boolean save) {
        create(cisBaseApplyId, cisApplyChargeNto);
        if (save) {
            dao().save(this);
        }
        return this;
    }

    public void clone(String cisBaseApplyId) {
        dao().save(this);
    }

    public void update(CisApplyChargeEto cisApplyChargeEto) {
        BusinessAssert.isTrue(cisApplyChargeEto.getVersion().equals(getVersion()), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0003, "cisApplyChargeEto");

        setPriceItemCode(cisApplyChargeEto.getPriceItemCode());
        setPriceItemName(cisApplyChargeEto.getPriceItemName());
        setPackageSpec(cisApplyChargeEto.getPackageSpec());
        setNum(cisApplyChargeEto.getNum());
        setUnit(cisApplyChargeEto.getUnit());
        setUnitName(cisApplyChargeEto.getUnitName());
        setPrice(cisApplyChargeEto.getPrice());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        setExecuteOrgCode(cisApplyChargeEto.getExecuteOrgCode());
        setExecuteOrgName(cisApplyChargeEto.getExecuteOrgName());
        setChageAmount(cisApplyChargeEto.getChageAmount());
        setVersion(cisApplyChargeEto.getVersion());
        setSystemItemClass(cisApplyChargeEto.getSystemItemClass());
        setLimitConformFlag(cisApplyChargeEto.getLimitConformFlag());
    }

    public void delete() {
        dao().delete(this);
    }

    public void deleteById() {
        dao().delete(this);
    }

    public void obsolete() {
        setStatusCode(CisStatusEnum.OBSOLETE);
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void updateApplyId(String cisBaseApplyId) {
        setApplyId(cisBaseApplyId);
    }
}
