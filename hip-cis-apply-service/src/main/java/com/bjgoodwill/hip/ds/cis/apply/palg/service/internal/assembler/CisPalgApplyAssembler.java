package com.bjgoodwill.hip.ds.cis.apply.palg.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.service.internal.assembler.CisMedicalHistoryAssembler;
import com.bjgoodwill.hip.ds.cis.apply.palg.entity.CisPalgApply;
import com.bjgoodwill.hip.ds.cis.apply.palg.entity.CisPalgApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.palg.entity.CisPalgApplyExt;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisPalgApplyAssembler {

    public static List<CisPalgApplyTo> toTos(List<CisPalgApply> cisPalgApplys) {
        return toTos(cisPalgApplys, false);
    }

    public static List<CisPalgApplyTo> toTos(List<CisPalgApply> cisPalgApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisPalgApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisPalgApplys不能为空！");

        List<CisPalgApplyTo> tos = new ArrayList<>();
        for (CisPalgApply cisPalgApply : cisPalgApplys)
            tos.add(toTo(cisPalgApply, withAllParts));
        return tos;
    }

    public static CisPalgApplyTo toTo(CisPalgApply cisPalgApply) {
        return toTo(cisPalgApply, false);
    }

    /**
     * @generated
     */
    public static CisPalgApplyTo toTo(CisPalgApply cisPalgApply, boolean withAllParts) {
        if (cisPalgApply == null)
            return null;
        CisPalgApplyTo to = new CisPalgApplyTo();
        to.setId(cisPalgApply.getId());
        to.setPatMiCode(cisPalgApply.getPatMiCode());
        to.setVisitCode(cisPalgApply.getVisitCode());
        to.setServiceItemCode(cisPalgApply.getServiceItemCode());
        to.setServiceItemName(cisPalgApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisPalgApply.getIsCanPriorityFlag());
        to.setStatusCode(cisPalgApply.getStatusCode());
        to.setCreatedStaff(cisPalgApply.getCreatedStaff());
        to.setCreatedStaffName(cisPalgApply.getCreatedStaffName());
        to.setCreatedDate(cisPalgApply.getCreatedDate());
        to.setUpdatedStaff(cisPalgApply.getUpdatedStaff());
        to.setUpdatedDate(cisPalgApply.getUpdatedDate());
        to.setExecutorStaff(cisPalgApply.getExecutorStaff());
        to.setExecutorDate(cisPalgApply.getExecutorDate());
        to.setExecutorHosptialCode(cisPalgApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisPalgApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisPalgApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisPalgApply.getMedrecordExamabstractId());
        to.setVisitType(cisPalgApply.getVisitType());
        to.setDeptNurseCode(cisPalgApply.getDeptNurseCode());
        to.setDeptNurseName(cisPalgApply.getDeptNurseName());
        to.setOrderID(cisPalgApply.getOrderID());
        to.setHospitalCode(cisPalgApply.getHospitalCode());
        to.setPrescriptionID(cisPalgApply.getPrescriptionID());
        to.setIsPrint(cisPalgApply.getIsPrint());
        to.setPrintStaff(cisPalgApply.getPrintStaff());
        to.setPrintDate(cisPalgApply.getPrintDate());
        to.setReMark(cisPalgApply.getReMark());
        to.setIcuExecuteDate(cisPalgApply.getIcuExecuteDate());
        to.setIsChargeManager(cisPalgApply.getIsChargeManager());
        to.setVersion(cisPalgApply.getVersion());
        to.setCreateOrgCode(cisPalgApply.getCreateOrgCode());
        to.setSortNo(cisPalgApply.getSortNo());
        to.setIsBaby(cisPalgApply.getIsBaby());
        to.setLastMenstrualDate(cisPalgApply.getLastMenstrualDate());
        to.setIsMenopause(cisPalgApply.getIsMenopause());
        to.setMedicalRecord(cisPalgApply.getMedicalRecord());
        to.setOrtherQuestions(cisPalgApply.getOrtherQuestions());
        to.setPastResults(cisPalgApply.getPastResults());
        to.setNum(cisPalgApply.getNum());
        to.setIsOlation(cisPalgApply.getIsOlation());
        to.setIsApply(cisPalgApply.getIsApply());
        to.setCheckPurpose(cisPalgApply.getCheckPurpose());
        to.setContagiousDiseaseHistoryFlag(cisPalgApply.getContagiousDiseaseHistoryFlag());
        to.setClinicalHistory(cisPalgApply.getClinicalHistory());
        to.setClinicalDiagnosis(cisPalgApply.getClinicalDiagnosis());
        to.setVisitOrgCode(cisPalgApply.getVisitOrgCode());
        to.setVisitOrgName(cisPalgApply.getVisitOrgName());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisPalgApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisPalgApply.getCisOrderExecPlans()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisPalgApply.getApplyDiagnoses()));
            to.setDetails(CisPalgApplyDetailAssembler.toTos(CisPalgApplyDetail.getByCisPalgApplyId(cisPalgApply.getId())));
            to.setPalgExt(CisPalgApplyExtAssembler.toTo(CisPalgApplyExt.getByCisPalgApplyExtApplyId(cisPalgApply.getId())));
            to.setCisMedicalHistoryTo(CisMedicalHistoryAssembler.toTo(cisPalgApply.getCisMedicalHistory()));
        }
        return to;
    }

    /**
     * @generated
     */
    public static CisPalgApplyNto toNto(CisPalgApply cisPalgApply, boolean withAllParts) {
        if (cisPalgApply == null)
            return null;
        CisPalgApplyNto to = new CisPalgApplyNto();
        to.setId(cisPalgApply.getId());
        to.setPatMiCode(cisPalgApply.getPatMiCode());
        to.setVisitCode(cisPalgApply.getVisitCode());
        to.setServiceItemCode(cisPalgApply.getServiceItemCode());
        to.setServiceItemName(cisPalgApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisPalgApply.getIsCanPriorityFlag());
//        to.setMedrecordExamabstractId(cisPalgApply.getMedrecordExamabstractId());
        to.setVisitType(cisPalgApply.getVisitType());
        to.setDeptNurseCode(cisPalgApply.getDeptNurseCode());
        to.setDeptNurseName(cisPalgApply.getDeptNurseName());
        to.setOrderID(cisPalgApply.getOrderID());
        to.setHospitalCode(cisPalgApply.getHospitalCode());
        to.setPrescriptionID(cisPalgApply.getPrescriptionID());
//        to.setIsPrint(cisPalgApply.getIsPrint());
//        to.setPrintStaff(cisPalgApply.getPrintStaff());
//        to.setPrintDate(cisPalgApply.getPrintDate());
        to.setReMark(cisPalgApply.getReMark());
        to.setIcuExecuteDate(cisPalgApply.getIcuExecuteDate());
        to.setIsChargeManager(cisPalgApply.getIsChargeManager());
        to.setCreateOrgCode(cisPalgApply.getCreateOrgCode());
        to.setSortNo(cisPalgApply.getSortNo());
        to.setIsBaby(cisPalgApply.getIsBaby());
        to.setLastMenstrualDate(cisPalgApply.getLastMenstrualDate());
        to.setIsMenopause(cisPalgApply.getIsMenopause());
        to.setMedicalRecord(cisPalgApply.getMedicalRecord());
        to.setOrtherQuestions(cisPalgApply.getOrtherQuestions());
        to.setPastResults(cisPalgApply.getPastResults());
        to.setOrderType(cisPalgApply.getOrderType());
        to.setVisitOrgCode(cisPalgApply.getVisitOrgCode());
        to.setVisitOrgName(cisPalgApply.getVisitOrgName());
        to.setCreateOrgName(cisPalgApply.getCreateOrgName());
        to.setExecutorOrgCode(cisPalgApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisPalgApply.getExecutorOrgName());
        to.setNum(cisPalgApply.getNum());
        to.setIsOlation(cisPalgApply.getIsOlation());
        to.setIsApply(cisPalgApply.getIsApply());
        to.setClinicalHistory(cisPalgApply.getClinicalHistory());
        to.setClinicalDiagnosis(cisPalgApply.getClinicalDiagnosis());
        return to;
    }

}