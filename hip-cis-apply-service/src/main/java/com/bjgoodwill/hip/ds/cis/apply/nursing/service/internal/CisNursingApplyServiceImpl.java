package com.bjgoodwill.hip.ds.cis.apply.nursing.service.internal;

import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.CisBaseApplyServiceImpl;
import com.bjgoodwill.hip.ds.cis.apply.nursing.entity.CisNursingApply;
import com.bjgoodwill.hip.ds.cis.apply.nursing.service.CisNursingApplyService;
import com.bjgoodwill.hip.ds.cis.apply.nursing.service.internal.assembler.CisNursingApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.nursing.to.CisNursingApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.nursing.to.CisNursingApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.nursing.to.CisNursingApplyTo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisNursingApplyService")
@RequestMapping(value = "/api/apply/apply/cisNursingApply", produces = "application/json; charset=utf-8")
public class CisNursingApplyServiceImpl extends CisBaseApplyServiceImpl implements CisNursingApplyService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisNursingApplyTo getCisNursingApplyById(String id) {
        return CisNursingApplyAssembler.toTo(CisNursingApply.getCisNursingApplyById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisNursingApplyTo createCisNursingApply(CisNursingApplyNto cisNursingApplyNto) {
        CisNursingApply cisNursingApply = new CisNursingApply();
        cisNursingApply = cisNursingApply.create(cisNursingApplyNto, true);
        return CisNursingApplyAssembler.toTo(cisNursingApply);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisNursingApply(String id, CisNursingApplyEto cisNursingApplyEto) {
        Optional<CisNursingApply> cisNursingApplyOptional = CisNursingApply.getCisNursingApplyById(id);
        cisNursingApplyOptional.ifPresent(cisNursingApply -> cisNursingApply.update(cisNursingApplyEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisNursingApply(String id) {
        Optional<CisNursingApply> cisNursingApplyOptional = CisNursingApply.getCisNursingApplyById(id);
        cisNursingApplyOptional.ifPresent(cisNursingApply -> cisNursingApply.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}