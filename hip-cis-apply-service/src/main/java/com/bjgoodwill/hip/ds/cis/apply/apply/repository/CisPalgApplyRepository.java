package com.bjgoodwill.hip.ds.cis.apply.apply.repository;

import com.bjgoodwill.hip.ds.cis.apply.palg.entity.CisPalgApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisPalgApplyRepository")
public interface CisPalgApplyRepository extends JpaRepository<CisPalgApply, String>, JpaSpecificationExecutor<CisPalgApply> {

}