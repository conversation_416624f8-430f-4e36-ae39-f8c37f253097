package com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisChangeDeptApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisChangeDeptApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisChangeDeptApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;

import java.util.ArrayList;
import java.util.List;

public abstract class CisChangeDeptApplyAssembler {

    public static List<CisChangeDeptApplyTo> toTos(List<CisChangeDeptApply> cisChangeDeptApplys) {
        return toTos(cisChangeDeptApplys, false);
    }

    public static List<CisChangeDeptApplyTo> toTos(List<CisChangeDeptApply> cisChangeDeptApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisChangeDeptApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisChangeDeptApplys不能为空！");

        List<CisChangeDeptApplyTo> tos = new ArrayList<>();
        for (CisChangeDeptApply cisChangeDeptApply : cisChangeDeptApplys)
            tos.add(toTo(cisChangeDeptApply, withAllParts));
        return tos;
    }

    public static CisChangeDeptApplyTo toTo(CisChangeDeptApply cisChangeDeptApply) {
        return toTo(cisChangeDeptApply, false);
    }

    /**
     * @generated
     */
    public static CisChangeDeptApplyTo toTo(CisChangeDeptApply cisChangeDeptApply, boolean withAllParts) {
        if (cisChangeDeptApply == null)
            return null;
        CisChangeDeptApplyTo to = new CisChangeDeptApplyTo();
        to.setId(cisChangeDeptApply.getId());
        to.setPatMiCode(cisChangeDeptApply.getPatMiCode());
        to.setVisitCode(cisChangeDeptApply.getVisitCode());
        to.setServiceItemCode(cisChangeDeptApply.getServiceItemCode());
        to.setServiceItemName(cisChangeDeptApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisChangeDeptApply.getIsCanPriorityFlag());
        to.setStatusCode(cisChangeDeptApply.getStatusCode());
        to.setCreatedStaff(cisChangeDeptApply.getCreatedStaff());
        to.setCreatedDate(cisChangeDeptApply.getCreatedDate());
        to.setUpdatedStaff(cisChangeDeptApply.getUpdatedStaff());
        to.setUpdatedDate(cisChangeDeptApply.getUpdatedDate());
        to.setExecutorStaff(cisChangeDeptApply.getExecutorStaff());
        to.setExecutorDate(cisChangeDeptApply.getExecutorDate());
        to.setExecutorHosptialCode(cisChangeDeptApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisChangeDeptApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisChangeDeptApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisChangeDeptApply.getMedrecordExamabstractId());
        to.setVisitType(cisChangeDeptApply.getVisitType());
        to.setDeptNurseCode(cisChangeDeptApply.getDeptNurseCode());
        to.setDeptNurseName(cisChangeDeptApply.getDeptNurseName());
        to.setOrderID(cisChangeDeptApply.getOrderID());
        to.setHospitalCode(cisChangeDeptApply.getHospitalCode());
        to.setPrescriptionID(cisChangeDeptApply.getPrescriptionID());
        to.setIsPrint(cisChangeDeptApply.getIsPrint());
        to.setPrintStaff(cisChangeDeptApply.getPrintStaff());
        to.setPrintDate(cisChangeDeptApply.getPrintDate());
        to.setReMark(cisChangeDeptApply.getReMark());
        to.setIcuExecuteDate(cisChangeDeptApply.getIcuExecuteDate());
        to.setIsChargeManager(cisChangeDeptApply.getIsChargeManager());
        to.setVersion(cisChangeDeptApply.getVersion());
        to.setCreateOrgCode(cisChangeDeptApply.getCreateOrgCode());
        to.setSortNo(cisChangeDeptApply.getSortNo());
        to.setIsBaby(cisChangeDeptApply.getIsBaby());
        to.setOutOrgCode(cisChangeDeptApply.getOutOrgCode());
        to.setOutOrgName(cisChangeDeptApply.getOutOrgName());
        to.setOutDeptNurseCode(cisChangeDeptApply.getOutDeptNurseCode());
        to.setOutDeptNurseName(cisChangeDeptApply.getOutDeptNurseName());
        to.setOutHospitalCode(cisChangeDeptApply.getOutHospitalCode());
        to.setOutHospitalName(cisChangeDeptApply.getOutHospitalName());
        to.setInOrgCode(cisChangeDeptApply.getInOrgCode());
        to.setInOrgName(cisChangeDeptApply.getInOrgName());
        to.setInDeptNurseCode(cisChangeDeptApply.getInDeptNurseCode());
        to.setInDeptNurseName(cisChangeDeptApply.getInDeptNurseName());
        to.setInHospitalCode(cisChangeDeptApply.getInHospitalCode());
        to.setInHospitalName(cisChangeDeptApply.getInHospitalName());
        to.setVisitOrgCode(cisChangeDeptApply.getVisitOrgCode());
        to.setVisitOrgName(cisChangeDeptApply.getVisitOrgName());
        to.setIsOlation(cisChangeDeptApply.getIsOlation());
        to.setIsApply(cisChangeDeptApply.getIsApply());
        to.setNum(cisChangeDeptApply.getNum());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisChangeDeptApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisChangeDeptApply.getCisOrderExecPlans()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisChangeDeptApply.getApplyDiagnoses()));
        }
        return to;
    }

    public static CisChangeDeptApplyNto toNto(CisChangeDeptApply cisChangeDeptApply, boolean withAllParts) {
        if (cisChangeDeptApply == null)
            return null;
        CisChangeDeptApplyNto to = new CisChangeDeptApplyNto();
        to.setId(cisChangeDeptApply.getId());
        to.setPatMiCode(cisChangeDeptApply.getPatMiCode());
        to.setVisitCode(cisChangeDeptApply.getVisitCode());
        to.setServiceItemCode(cisChangeDeptApply.getServiceItemCode());
        to.setServiceItemName(cisChangeDeptApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisChangeDeptApply.getIsCanPriorityFlag());
//        to.setMedrecordExamabstractId(cisChangeDeptApply.getMedrecordExamabstractId());
        to.setVisitType(cisChangeDeptApply.getVisitType());
        to.setDeptNurseCode(cisChangeDeptApply.getDeptNurseCode());
        to.setDeptNurseName(cisChangeDeptApply.getDeptNurseName());
        to.setOrderID(cisChangeDeptApply.getOrderID());
        to.setHospitalCode(cisChangeDeptApply.getHospitalCode());
        to.setPrescriptionID(cisChangeDeptApply.getPrescriptionID());
//        to.setIsPrint(cisChangeDeptApply.getIsPrint());
//        to.setPrintStaff(cisChangeDeptApply.getPrintStaff());
//        to.setPrintDate(cisChangeDeptApply.getPrintDate());
        to.setReMark(cisChangeDeptApply.getReMark());
        to.setIcuExecuteDate(cisChangeDeptApply.getIcuExecuteDate());
        to.setIsChargeManager(cisChangeDeptApply.getIsChargeManager());
        to.setCreateOrgCode(cisChangeDeptApply.getCreateOrgCode());
        to.setSortNo(cisChangeDeptApply.getSortNo());
        to.setIsBaby(cisChangeDeptApply.getIsBaby());
        to.setOutOrgCode(cisChangeDeptApply.getOutOrgCode());
        to.setOutOrgName(cisChangeDeptApply.getOutOrgName());
        to.setOutDeptNurseCode(cisChangeDeptApply.getOutDeptNurseCode());
        to.setOutDeptNurseName(cisChangeDeptApply.getOutDeptNurseName());
        to.setOutHospitalCode(cisChangeDeptApply.getOutHospitalCode());
        to.setOutHospitalName(cisChangeDeptApply.getOutHospitalName());
        to.setInOrgCode(cisChangeDeptApply.getInOrgCode());
        to.setInOrgName(cisChangeDeptApply.getInOrgName());
        to.setInDeptNurseCode(cisChangeDeptApply.getInDeptNurseCode());
        to.setInDeptNurseName(cisChangeDeptApply.getInDeptNurseName());
        to.setInHospitalCode(cisChangeDeptApply.getInHospitalCode());
        to.setInHospitalName(cisChangeDeptApply.getInHospitalName());
        to.setVisitOrgCode(cisChangeDeptApply.getVisitOrgCode());
        to.setOrderType(cisChangeDeptApply.getOrderType());
        to.setVisitOrgName(cisChangeDeptApply.getVisitOrgName());
        to.setCreateOrgName(cisChangeDeptApply.getCreateOrgName());
        to.setExecutorOrgCode(cisChangeDeptApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisChangeDeptApply.getExecutorOrgName());
        to.setNum(cisChangeDeptApply.getNum());
        to.setIsOlation(cisChangeDeptApply.getIsOlation());
        to.setIsApply(cisChangeDeptApply.getIsApply());
        if (withAllParts) {
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisChangeDeptApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisChangeDeptApply.getCisOrderExecPlans()));
        }
        return to;
    }

}