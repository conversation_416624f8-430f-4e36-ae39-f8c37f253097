package com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.diag.entity.ApplyDiagnosis;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisTo;

import java.util.ArrayList;
import java.util.List;

public abstract class ApplyDiagnosisAssembler {

    public static List<ApplyDiagnosisTo> toTos(List<ApplyDiagnosis> applyDiagnosiss) {
        return toTos(applyDiagnosiss, false);
    }

    public static List<ApplyDiagnosisTo> toTos(List<ApplyDiagnosis> applyDiagnosiss, boolean withAllParts) {
        BusinessAssert.notNull(applyDiagnosiss, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数applyDiagnosiss不能为空！");

        List<ApplyDiagnosisTo> tos = new ArrayList<>();
        for (ApplyDiagnosis applyDiagnosis : applyDiagnosiss)
            tos.add(toTo(applyDiagnosis, withAllParts));
        return tos;
    }

    public static ApplyDiagnosisTo toTo(ApplyDiagnosis applyDiagnosis) {
        return toTo(applyDiagnosis, false);
    }

    /**
     * @generated
     */
    public static ApplyDiagnosisTo toTo(ApplyDiagnosis applyDiagnosis, boolean withAllParts) {
        if (applyDiagnosis == null)
            return null;
        ApplyDiagnosisTo to = new ApplyDiagnosisTo();
        to.setCisBaseApplyId(applyDiagnosis.getApplyId());
        to.setId(applyDiagnosis.getId());
        to.setDiagCode(applyDiagnosis.getDiagCode());
        to.setDiagName(applyDiagnosis.getDiagName());
        to.setDeleted(applyDiagnosis.isDeleted());
        to.setCreatedStaff(applyDiagnosis.getCreatedStaff());
        to.setCreatedDate(applyDiagnosis.getCreatedDate());
        to.setUpdatedStaff(applyDiagnosis.getUpdatedStaff());
        to.setUpdatedDate(applyDiagnosis.getUpdatedDate());

        if (withAllParts) {
        }
        return to;
    }

}