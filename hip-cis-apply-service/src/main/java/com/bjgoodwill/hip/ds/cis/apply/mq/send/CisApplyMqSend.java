package com.bjgoodwill.hip.ds.cis.apply.mq.send;

import com.bjgoodwill.hip.business.util.cis.common.CisOrgCommonNto;
import com.bjgoodwill.hip.business.util.cis.common.enums.CheckTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.econ.enums.SetlStasEnum;
import com.bjgoodwill.hip.business.util.mq.conf.cis.CisApplyRabbitConfig;
import com.bjgoodwill.hip.business.util.mq.conf.cis.CisCdrRabbitConfig;
import com.bjgoodwill.hip.business.util.mq.conf.cis.CisOrderRabbitConfig;
import com.bjgoodwill.hip.business.util.mq.enums.MqBusinessTypeEnum;
import com.bjgoodwill.hip.business.util.mq.enums.MqDomainTypeEnum;
import com.bjgoodwill.hip.business.util.mq.to.cis.CisAntimicrobialsSkinExecMqNto;
import com.bjgoodwill.hip.business.util.mq.to.cis.CisIpdOrderExtNto;
import com.bjgoodwill.hip.business.util.mq.to.cis.CisOrderStatueEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanSetlStasEto;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanTo;
import com.bjgoodwill.hip.message.service.HipLocalMessageProducer;
import com.bjgoodwill.hip.message.to.HipLocalMessage;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-10-09 09:09
 * @className: CisApplyMqSend
 * @description:
 **/
@Component
public class CisApplyMqSend {

    @Autowired
    private HipLocalMessageProducer hipLocalMessageProducer;

    public void IpdOrderExtSend(List<CisIpdOrderExtNto> ntoList) {
        if (CollectionUtils.isEmpty(ntoList)) {
            return;
        }

        HipLocalMessage<List<CisIpdOrderExtNto>> message = new HipLocalMessage<>();
        message.setData(ntoList);
        message.setRoutingKey(CisOrderRabbitConfig.CIS_IPD_ORDER_EXT_SAVE_ROUTING_KEY);
        message.setDomainType(MqDomainTypeEnum.IPD_CPOE.getCode());
        message.setBusinessType(MqBusinessTypeEnum.EXECUTE.getCode());
        hipLocalMessageProducer.sendMsg(CisOrderRabbitConfig.CIS_CPOE_SYNC_EXCHANGE, message);
    }

    public void OrderStatueSend(CisOrderExecPlan plan, CisStatusEnum statusEnum, CisOrgCommonNto cisOrgCommonNto) {
        if (plan == null) {
            return;
        }

        HipLocalMessage<List<CisOrderStatueEto>> message = new HipLocalMessage<>();
        CisOrderStatueEto eto = new CisOrderStatueEto(plan.getOrderId(), statusEnum, cisOrgCommonNto.getOrgCode(), cisOrgCommonNto.getOrgName());
        message.setData(Arrays.asList(eto));
        message.setRoutingKey(CisOrderRabbitConfig.CIS_ORDER_SERVICECODE_UPDATE_KEY);
        message.setDomainType(MqDomainTypeEnum.IPD_CPOE.getCode());
        message.setBusinessType(MqBusinessTypeEnum.EXECUTE.getCode());
        hipLocalMessageProducer.sendTransactionMsg(CisOrderRabbitConfig.CIS_CPOE_SYNC_EXCHANGE, message);
    }

    public void OrderStatueSend(CisBaseApply apply) {
        if (apply == null) {
            return;
        }

        HipLocalMessage<List<CisOrderStatueEto>> message = new HipLocalMessage<>();
        CisOrderStatueEto eto = new CisOrderStatueEto(apply.getOrderID(), apply.getStatusCode(), apply.getDeptNurseCode(), apply.getDeptNurseName());
        message.setData(Arrays.asList(eto));
        message.setRoutingKey(CisOrderRabbitConfig.CIS_ORDER_SERVICECODE_UPDATE_KEY);
        message.setDomainType(MqDomainTypeEnum.IPD_CPOE.getCode());
        message.setBusinessType(MqBusinessTypeEnum.EXECUTE.getCode());
        hipLocalMessageProducer.sendTransactionMsg(CisOrderRabbitConfig.CIS_CPOE_SYNC_EXCHANGE, message);
    }

    public void OrderStatueSend(CisOrderStatueEto cisOrderStatueEto) {
        if (cisOrderStatueEto == null) {
            return;
        }

        HipLocalMessage<List<CisOrderStatueEto>> message = new HipLocalMessage<>();
        message.setData(Arrays.asList(cisOrderStatueEto));
        message.setRoutingKey(CisOrderRabbitConfig.CIS_ORDER_SERVICECODE_UPDATE_KEY);
        message.setDomainType(MqDomainTypeEnum.IPD_CPOE.getCode());
        message.setBusinessType(MqBusinessTypeEnum.EXECUTE.getCode());
        hipLocalMessageProducer.sendTransactionMsg(CisOrderRabbitConfig.CIS_CPOE_SYNC_EXCHANGE, message);
    }

    //拆组并组 修改 医嘱的服务编码
    public void OrderServiceCodeSend(String id, String serviceCode) {
        if (StringUtil.isBlank(id)) {
            return;
        }

        HipLocalMessage<CisOrderStatueEto> message = new HipLocalMessage<>();
        CisOrderStatueEto eto = new CisOrderStatueEto(id, serviceCode);
        message.setData(eto);
        message.setRoutingKey(CisApplyRabbitConfig.CIS_IPD_ORDER_SERVICECODE_UPDATE_KEY);
        hipLocalMessageProducer.sendMsg(CisApplyRabbitConfig.CIS_IPD_ORDER_SERVICECODE_UPDATE, message);
    }

    /**
     * 创建抗菌药皮试药执行记录
     *
     * @param nto
     */
    public void CisAntimicrobialsSkinExecCreateSend(CisAntimicrobialsSkinExecMqNto nto) {
        if (nto == null) {
            return;
        }

        HipLocalMessage<CisAntimicrobialsSkinExecMqNto> message = new HipLocalMessage<>();
        message.setData(nto);
        message.setRoutingKey(CisCdrRabbitConfig.CIS_CDR_ANTIMICROBIALS_SKIN_ROUTING_KEY);
        message.setDomainType(MqDomainTypeEnum.CLINICAL_DATA.getCode());
        message.setBusinessType(MqBusinessTypeEnum.ANTIMICROBIALS_SKIN_EXEC_INSERT.getCode());
        hipLocalMessageProducer.sendTransactionMsg(CisCdrRabbitConfig.CIS_CDR_SYNC_EXCHANGE, message);
    }

    /**
     * 拆分记录流程日志
     *
     * @param plans
     */
    public void OrderExecLogSend(List<CisOrderExecPlanTo> plans) {
        if (CollectionUtils.isEmpty(plans)) {
            return;
        }
        HipLocalMessage<List<CisOrderExecPlanTo>> message = new HipLocalMessage<>();
        message.setData(plans);
        message.setRoutingKey(CisOrderRabbitConfig.CIS_IPD_ORDER_SPLIT_LOG_ROUTING_KEY);
        message.setDomainType(MqDomainTypeEnum.IPD_CPOE.getCode());
        message.setBusinessType(MqBusinessTypeEnum.SPLIT.getCode());
        hipLocalMessageProducer.sendTransactionMsg(CisOrderRabbitConfig.CIS_CPOE_SYNC_EXCHANGE, message);
    }


    public void OrderCheckSend(List<String> orderIds, CheckTypeEnum checkTypeEnum) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        if (CheckTypeEnum.NONE.equals(checkTypeEnum) || CheckTypeEnum.APPLY.equals(checkTypeEnum)) {
            return;
        }
        HipLocalMessage<List<String>> message = new HipLocalMessage<>();
        message.setRoutingKey(CisOrderRabbitConfig.CIS_IPD_ORDER_CHECK_TYPE_ROUTING_KEY);
        message.setDomainType(MqDomainTypeEnum.CPOE.getCode());
        message.setBusinessType(MqBusinessTypeEnum.CHECK_TYPE.getCode());
        hipLocalMessageProducer.sendTransactionMsg(CisOrderRabbitConfig.CIS_CPOE_SYNC_EXCHANGE, message);
    }

    public void ApplyDrugLogSend(CisOrderExecPlan plan, CisOrgCommonNto cisOrgCommonNto) {
        if (plan == null) {
            return;
        }
        HipLocalMessage<List<CisOrderStatueEto>> message = new HipLocalMessage<>();
        CisOrderStatueEto eto = new CisOrderStatueEto(plan.getOrderId(), null, cisOrgCommonNto.getOrgCode(), cisOrgCommonNto.getOrgName());
        message.setData(Arrays.asList(eto));
        message.setRoutingKey(CisOrderRabbitConfig.CIS_ORDER_APPLYDRUG_LOG_ROUTING_KEY);
        message.setDomainType(MqDomainTypeEnum.IPD_CPOE.getCode());
        message.setBusinessType(MqBusinessTypeEnum.APPLYDRUG.getCode());
        hipLocalMessageProducer.sendTransactionMsg(CisOrderRabbitConfig.CIS_CPOE_SYNC_EXCHANGE, message);
    }

    public void PharmaciessendLogSend(CisOrderExecPlan plan, CisOrgCommonNto cisOrgCommonNto) {
        if (plan == null) {
            return;
        }
        HipLocalMessage<List<CisOrderStatueEto>> message = new HipLocalMessage<>();
        CisOrderStatueEto eto = new CisOrderStatueEto(plan.getOrderId(), null, cisOrgCommonNto.getOrgCode(), cisOrgCommonNto.getOrgName());
        message.setData(Arrays.asList(eto));
        message.setRoutingKey(CisOrderRabbitConfig.CIS_ORDER_PHARMACIESSEND_LOG_ROUTING_KEY);
        message.setDomainType(MqDomainTypeEnum.IPD_CPOE.getCode());
        message.setBusinessType(MqBusinessTypeEnum.PHARMACIESSEND.getCode());
        hipLocalMessageProducer.sendTransactionMsg(CisOrderRabbitConfig.CIS_CPOE_SYNC_EXCHANGE, message);
    }

    public void OpdPharmaciessendLogSend(CisOrderExecPlan plan, CisOrgCommonNto cisOrgCommonNto) {
        if (plan == null) {
            return;
        }
        HipLocalMessage<List<CisOrderStatueEto>> message = new HipLocalMessage<>();
        CisOrderStatueEto eto = new CisOrderStatueEto(plan.getOrderId(), null, cisOrgCommonNto.getOrgCode(), cisOrgCommonNto.getOrgName());
        message.setData(Arrays.asList(eto));
        message.setRoutingKey(CisOrderRabbitConfig.CIS_OPD_ORDER_PHARMACIESSEND_LOG_ROUTING_KEY);
        message.setDomainType(MqDomainTypeEnum.OPD_CPOE.getCode());
        message.setBusinessType(MqBusinessTypeEnum.PHARMACIESSEND.getCode());
        hipLocalMessageProducer.sendTransactionMsg(CisOrderRabbitConfig.CIS_CPOE_SYNC_EXCHANGE, message);
    }

    public void opdPaymentLogSend(CisOrderExecPlan plan, CisOrderExecPlanSetlStasEto setlStasEto) {
        if (plan == null) {
            return;
        }
        HipLocalMessage<List<CisOrderStatueEto>> message = new HipLocalMessage<>();
        CisStatusEnum status = SetlStasEnum.已结算.equals(setlStasEto.getSetlStas()) ? CisStatusEnum.COMPLETED  : CisStatusEnum.CANCELEXCUTE;
        CisOrderStatueEto eto = new CisOrderStatueEto(plan.getOrderId(), status, setlStasEto.getCurWorkGroupCode(), setlStasEto.getCurWorkGroupName());
        message.setData(Arrays.asList(eto));
        message.setRoutingKey(CisOrderRabbitConfig.CIS_OPD_ORDER_PAYMENT_LOG_ROUTING_KEY);
        message.setDomainType(MqDomainTypeEnum.OPD_CPOE.getCode());
        message.setBusinessType(MqBusinessTypeEnum.ECON_OPD_SETL.getCode());
        hipLocalMessageProducer.sendTransactionMsg(CisOrderRabbitConfig.CIS_CPOE_SYNC_EXCHANGE, message);
    }

    public void ipdPaymentLogSend(CisOrderExecPlan plan, CisOrgCommonNto cisOrgCommonNto) {
        if (plan == null) {
            return;
        }
        HipLocalMessage<List<CisOrderStatueEto>> message = new HipLocalMessage<>();
        CisOrderStatueEto eto = new CisOrderStatueEto(plan.getOrderId(), null, cisOrgCommonNto.getOrgCode(), cisOrgCommonNto.getOrgName());
        message.setData(Arrays.asList(eto));
        message.setRoutingKey(CisOrderRabbitConfig.CIS_IPD_ORDER_PAYMENT_LOG_ROUTING_KEY);
        message.setDomainType(MqDomainTypeEnum.IPD_CPOE.getCode());
        message.setBusinessType(MqBusinessTypeEnum.ECON_OPD_SETL.getCode());
        hipLocalMessageProducer.sendTransactionMsg(CisOrderRabbitConfig.CIS_CPOE_SYNC_EXCHANGE, message);
    }
}