package com.bjgoodwill.hip.ds.cis.apply.spcobs.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.ApplyWithDetial;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.repository.CisSpcobsApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "检验类申请单")
@DiscriminatorValue("06")
public class CisSpcobsApply extends ApplyWithDetial<CisSpcobsApplyDetail> {

    // 检验注意事项
    private String remark;
    // 检验分类
    private String spcobsClass;
    // 检验分类名称
    private String spcobsClassName;
    // 条码号
    private String printBarCode;
    // lis条码
    private String barCode;
    // 报告pdf地址
    private String reportPdfUrl;

    // 标本 字典Speciman
    private String speciman;
    // 标本名称 字典Speciman
    private String specimanName;


    public static Optional<CisSpcobsApply> getCisSpcobsApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisSpcobsApply> getCisSpcobsApplies(CisSpcobsApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisSpcobsApply> getCisSpcobsApplyPage(CisSpcobsApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisSpcobsApply> getSpecification(CisSpcobsApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            return predicate;
        };
    }

    private static CisSpcobsApplyRepository dao() {
        return SpringUtil.getBean(CisSpcobsApplyRepository.class);
    }

    @Comment("检验注意事项")
    @Column(name = "remark", nullable = true)
    public String getRemark() {
        return remark;
    }

    protected void setRemark(String remark) {
        this.remark = remark;
    }

    @Comment("检验分类")
    @Column(name = "spcobs_class", nullable = true)
    public String getSpcobsClass() {
        return spcobsClass;
    }

    protected void setSpcobsClass(String spcobsClass) {
        this.spcobsClass = spcobsClass;
    }

    @Comment("检验分类名称")
    @Column(name = "spcobs_class_name", nullable = true)
    public String getSpcobsClassName() {
        return spcobsClassName;
    }

    public void setSpcobsClassName(String spcobsClassName) {
        this.spcobsClassName = spcobsClassName;
    }

    @Comment("条码号")
    @Column(name = "print_bar_code", nullable = true)
    public String getPrintBarCode() {
        return printBarCode;
    }

    protected void setPrintBarCode(String printBarCode) {
        this.printBarCode = printBarCode;
    }

    @Comment("lis条码")
    @Column(name = "bar_code", nullable = true)
    public String getBarCode() {
        return barCode;
    }

    protected void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    @Comment("报告pdf地址")
    @Column(name = "report_pdf_url", nullable = true)
    public String getReportPdfUrl() {
        return reportPdfUrl;
    }

    protected void setReportPdfUrl(String reportPdfUrl) {
        this.reportPdfUrl = reportPdfUrl;
    }

    @Comment("标本 字典Speciman")
    @Column(name = "speciman", nullable = true)
    public String getSpeciman() {
        return speciman;
    }

    protected void setSpeciman(String speciman) {
        this.speciman = speciman;
    }

    @Comment("标本名称 字典Speciman")
    @Column(name = "speciman_name", nullable = true)
    public String getSpecimanName() {
        return specimanName;
    }

    public void setSpecimanName(String specimanName) {
        this.specimanName = specimanName;
    }
//    @Override
//    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto) {
//        return create((CisSpcobsApplyNto) cisBaseApplyNto);
//    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.SPCOBS;
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisSpcobsApplyEto) cisBaseApplyEto);
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisSpcobsApplyNto) cisBaseApplyNto, save);
    }

    public CisSpcobsApply create(CisSpcobsApplyNto cisSpcobsApplyNto, Boolean save) {
        BusinessAssert.notNull(cisSpcobsApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisSpcobsApplyNto不能为空！");
        super.create(cisSpcobsApplyNto, save);

        setRemark(cisSpcobsApplyNto.getRemark());
        setSpcobsClass(cisSpcobsApplyNto.getSpcobsClass());
        setSpcobsClassName(cisSpcobsApplyNto.getSpcobsClassName());
        setSpeciman(cisSpcobsApplyNto.getSpeciman());
        setSpecimanName(cisSpcobsApplyNto.getSpecimanName());
        if (save) {
            dao().save(this);
        }
        if (cisSpcobsApplyNto.getDetails() != null) {
            String code = StringUtils.join(cisSpcobsApplyNto.getDetails().stream().map(p -> p.getSpcobsCode()).toList().toArray(), ",");
            setServiceItemCode(code);
            for (CisSpcobsApplyDetailNto cisSpcobsApplyDetailNto_ : cisSpcobsApplyNto.getDetails()) {
                CisSpcobsApplyDetail cisSpcobsApplyDetail = new CisSpcobsApplyDetail();
                cisSpcobsApplyDetail.create(getId(), cisSpcobsApplyDetailNto_, getStatusCode(), save);
            }
        }

        return this;
    }

    public void update(CisSpcobsApplyEto cisSpcobsApplyEto) {
        super.update(cisSpcobsApplyEto);
        setRemark(cisSpcobsApplyEto.getRemark());
        setSpcobsClass(cisSpcobsApplyEto.getSpcobsClass());
        setSpcobsClassName(cisSpcobsApplyEto.getSpcobsClassName());
        setSpeciman(cisSpcobsApplyEto.getSpeciman());
        setSpecimanName(cisSpcobsApplyEto.getSpecimanName());

        if (!CollectionUtils.isEmpty(cisSpcobsApplyEto.getDetailEtos())) {
            cisSpcobsApplyEto.getDetailEtos().forEach(eto -> {
                Optional<CisSpcobsApplyDetail> detail = CisSpcobsApplyDetail.getCisSpcobsApplyDetailById(eto.getId());
                detail.ifPresent(d -> d.update(eto));
                //                detail.get().update(eto);
            });
        }

        if (!CollectionUtils.isEmpty(cisSpcobsApplyEto.getDetailNtos()))
            cisSpcobsApplyEto.getDetailNtos().forEach(nto -> {
                CisSpcobsApplyDetail detail = new CisSpcobsApplyDetail();
                detail.create(getId(), nto, getStatusCode(), true);
            });
    }

    public void delete() {
        super.delete();
        for (CisSpcobsApplyDetail cisSpcobsApplyDetail : CisSpcobsApplyDetail.getByCisSpcobsApplyId(getId())) {
            cisSpcobsApplyDetail.delete();
        }
        // CisSpcobsApplyDetail.deleteByCisSpcobsApplyId(getId());dao().delete(this);
    }

    @Override
    @Transient
    public List<CisSpcobsApplyDetail> getDetailList() {
        return CisSpcobsApplyDetail.getByCisSpcobsApplyId(getId());
    }

    @Override
    protected List<String> getSplitCodes(List<CisSpcobsApplyDetail> applyDetails) {
        return applyDetails.stream().map(p -> p.getSpcobsCode()).toList();
    }

}
