package com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler;

import cn.hutool.core.convert.Convert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.*;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyCustomTo;
import com.bjgoodwill.hip.ds.cis.apply.blood.entity.CisBloodApply;
import com.bjgoodwill.hip.ds.cis.apply.blood.entity.CisInputBloodApply;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApply;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisCDrugApply;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisDrugApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisEDrugApply;
import com.bjgoodwill.hip.ds.cis.apply.nursing.entity.CisNursingApply;
import com.bjgoodwill.hip.ds.cis.apply.operation.entity.CisOperationApply;
import com.bjgoodwill.hip.ds.cis.apply.operation.entity.CisOperationApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.palg.entity.CisPalgApply;
import com.bjgoodwill.hip.ds.cis.apply.palg.entity.CisPalgApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.skin.entity.CisSkinApply;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApply;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApplyDetail;

public abstract class CisBaseApplyCustomAssembler {

    /**
     * @generated
     */
    public static <T> CisBaseApplyCustomTo toTo(CisBaseApply cisBaseApply, T detail) {
        if (cisBaseApply == null)
            return null;
        CisBaseApplyCustomTo to = new CisBaseApplyCustomTo();
        if (cisBaseApply instanceof CisDgimgApply) {
            extractedCisDgimgApplyTo(cisBaseApply, (CisDgimgApplyDetail) detail, to);
        }
        if (cisBaseApply instanceof CisSpcobsApply) {
            extractedCisSpcobsApplyTo(cisBaseApply, (CisSpcobsApplyDetail) detail, to);
        }
        if (cisBaseApply instanceof CisCommon) {
            cisBaseApplyTo(cisBaseApply, to);
        }
        if (cisBaseApply instanceof CisEDrugApply) {
            extractedCisEDrugApplyTo(cisBaseApply, (CisDrugApplyDetail) detail, to);
        }
        if (cisBaseApply instanceof CisCDrugApply) {
            extractedCisCDrugApplyTo(cisBaseApply, (CisDrugApplyDetail) detail, to);
        }
        if (cisBaseApply instanceof CisOperationApply) {
            extractedCisOperationApplyTo(cisBaseApply, (CisOperationApplyDetail) detail, to);
        }
        if (cisBaseApply instanceof CisTreatmentApply) {
            cisBaseApplyTo(cisBaseApply, to);
        }
        if (cisBaseApply instanceof CisChangeDeptApply) {
            cisBaseApplyTo(cisBaseApply, to);
        }
        if (cisBaseApply instanceof CisOutHospitalApply) {
            cisBaseApplyTo(cisBaseApply, to);
        }
        if (cisBaseApply instanceof CisPalgApply) {
            extractedCisPalgApplyTo(cisBaseApply, (CisPalgApplyDetail) detail, to);
        }
        if (cisBaseApply instanceof CisConsultationApply) {
            cisBaseApplyTo(cisBaseApply, to);
        }
        if (cisBaseApply instanceof CisBloodApply) {
            cisBaseApplyTo(cisBaseApply, to);
        }
        if (cisBaseApply instanceof CisInputBloodApply) {
            cisBaseApplyTo(cisBaseApply, to);
        }
        if (cisBaseApply instanceof CisManagementApply) {
            cisBaseApplyTo(cisBaseApply, to);
        }
        if (cisBaseApply instanceof CisNursingApply) {
            cisBaseApplyTo(cisBaseApply, to);
        }
        if (cisBaseApply instanceof CisSkinApply) {
            cisBaseApplyTo(cisBaseApply, to);
            to.setUsage(((CisSkinApply) cisBaseApply).getUsage());
            to.setUsageName(((CisSkinApply) cisBaseApply).getUsageName());
            to.setDosageUnit(((CisSkinApply) cisBaseApply).getDosageUnit());
            to.setDosage(((CisSkinApply) cisBaseApply).getDosage());
            to.setReceiveOrg(((CisSkinApply) cisBaseApply).getReceiveOrg());
            to.setReceiveOrgName(((CisSkinApply) cisBaseApply).getReceiveOrgName());
        }
        return to;
    }

    //检查
    private static <T> void extractedCisDgimgApplyTo(CisBaseApply cisBaseApply, CisDgimgApplyDetail detail, CisBaseApplyCustomTo to) {
        // 处理基本属性
        cisBaseApplyTo(cisBaseApply, to);
        if (detail == null) {
            return;
        }
        // 处理明细
        to.setId(detail.getId());
        to.setSortNo(detail.getSortNo());
        to.setDgimgName(detail.getDgimgName());
        to.setHumanSystem(detail.getHumanSystem());
        to.setHumanSystemName(detail.getHumanSystemName());
        to.setHumanOrgans(detail.getHumanOrgans());
        to.setHumanOrgansName(detail.getHumanOrgansName());
        to.setMethod(detail.getMethod());
        to.setMethodName(detail.getMethodName());
        to.setRange(detail.getRange());
        to.setRangeName(detail.getRangeName());
        to.setDirection(detail.getDirection());
        to.setDirectionName(detail.getDirectionName());
        to.setLayer(detail.getLayer());
        to.setLayerName(detail.getLayerName());
        to.setOperation(detail.getOperation());
        to.setOperationName(detail.getOperationName());
        to.setRequirementPurpose(detail.getRequirementPurpose());
        to.setStatusCode(detail.getStatusCode());
        to.setDgimgCode(detail.getDgimgCode());
        to.setUnilateralFlag(detail.getUnilateralFlag());
        to.setExtTypeCode(detail.getExtTypeCode());
        to.setServiceItemCode(detail.getDgimgCode());
        to.setServiceItemName(detail.getDgimgName());
    }

    //检验
    private static <T> void extractedCisSpcobsApplyTo(CisBaseApply cisBaseApply, CisSpcobsApplyDetail detail, CisBaseApplyCustomTo to) {
        // 处理基本属性
        cisBaseApplyTo(cisBaseApply, to);
        CisSpcobsApply cisSpcobsApply = (CisSpcobsApply) cisBaseApply;
        to.setSpeciman(cisSpcobsApply.getSpeciman());
        to.setSpecimanName(cisSpcobsApply.getSpecimanName());
        if (detail == null) {
            return;
        }
        // 处理明细
        to.setId(detail.getId());
        to.setSortNo(detail.getSortNo());
        to.setSpcobsName(detail.getSpcobsName());
        to.setDeviceType(detail.getDeviceType());
        to.setDeviceTypeName(detail.getDeviceTypeName());
        to.setTestTubeId(detail.getTestTubeId());
        to.setMethod(detail.getMethod());
        to.setMethodName(detail.getMethod());
        to.setSpcobsCode(detail.getSpcobsCode());
        to.setExtTypeCode(detail.getExtTypeCode());
        to.setServiceItemCode(detail.getSpcobsCode());
        to.setServiceItemName(detail.getSpcobsName());
    }

    //成药
    private static <T> void extractedCisEDrugApplyTo(CisBaseApply cisBaseApply, CisDrugApplyDetail detail, CisBaseApplyCustomTo to) {
        // 处理基本属性
        cisBaseApplyTo(cisBaseApply, to);
        // 处理成药属性
        CisEDrugApply cisEDrugApply = (CisEDrugApply) cisBaseApply;
        to.setUsage(cisEDrugApply.getUsage());
        to.setUsageName(cisEDrugApply.getUsageName());
        to.setFrequency(cisEDrugApply.getFrequency());
        to.setFrequencyName(cisEDrugApply.getFrequencyName());
        to.setTreatmentCourse(cisEDrugApply.getTreatmentCourse());
        to.setTreatmentCourseUnit(cisEDrugApply.getTreatmentCourseUnit());
        to.setTreatmentCourseUnitName(cisEDrugApply.getTreatmentCourseUnitName());
        to.setReceiveOrg(cisEDrugApply.getReceiveOrg());
        to.setReceiveOrgName(cisEDrugApply.getReceiveOrgName());
        to.setDripSpeed(cisEDrugApply.getDripSpeed());
        to.setDripSpeedUnit(cisEDrugApply.getDripSpeedUnit());
        to.setDripSpeedUnitName(cisEDrugApply.getDripSpeedUnitName());
        // 处理明细
        extractedDurg(detail, to);
    }

    //草药
    private static <T> void extractedCisCDrugApplyTo(CisBaseApply cisBaseApply, CisDrugApplyDetail detail, CisBaseApplyCustomTo to) {
        // 处理基本属性
        cisBaseApplyTo(cisBaseApply, to);
        CisCDrugApply cisCDrugApply = (CisCDrugApply) cisBaseApply;
        to.setUsage(cisCDrugApply.getUsage());
        to.setUsageName(cisCDrugApply.getUsageName());
        to.setFrequency(cisCDrugApply.getFrequency());
        to.setFrequencyName(cisCDrugApply.getFrequencyName());
        to.setTreatmentCourse(cisCDrugApply.getTreatmentCourse());
        to.setTreatmentCourseUnit(cisCDrugApply.getTreatmentCourseUnit());
        to.setTreatmentCourseUnitName(cisCDrugApply.getTreatmentCourseUnitName());
        to.setPrescriptionFlag(cisCDrugApply.getPrescriptionFlag());
        to.setReceiveOrg(cisCDrugApply.getReceiveOrg());
        to.setReceiveOrgName(cisCDrugApply.getReceiveOrgName());
        to.setDoseNum(cisCDrugApply.getDoseNum());
        to.setDecoction(cisCDrugApply.getDecoction());
        to.setCdrugPackNum(cisCDrugApply.getCdrugPackNum());
        to.setCdrugPackMl(cisCDrugApply.getCdrugPackMl());
        to.setNum(Convert.toDouble(cisCDrugApply.getDoseNum()));
        to.setHerbsProCode(cisCDrugApply.getHerbsProCode());
        to.setPrescriptionID(cisCDrugApply.getPrescriptionID());
        // 处理明细
//        extractedDurg(detail, to);
    }

    // 药品明细提取
    private static void extractedDurg(CisDrugApplyDetail detail, CisBaseApplyCustomTo to) {
        if (detail == null) {
            return;
        }
        CisDrugApplyDetail cisDrugApplyDetail = detail;
        to.setId(cisDrugApplyDetail.getId());
        to.setSortNo(cisDrugApplyDetail.getSortNo());
        to.setDrugCode(cisDrugApplyDetail.getDrugCode());
        to.setDrugName(cisDrugApplyDetail.getDrugName());
        to.setServiceItemCode(detail.getDrugCode());
        to.setServiceItemName(detail.getDrugName());
        to.setDosage(cisDrugApplyDetail.getDosage());
        to.setDosageUnit(cisDrugApplyDetail.getDosageUnit());
        to.setDosageUnitName(cisDrugApplyDetail.getDosageUnitName());
        to.setPackageNum(cisDrugApplyDetail.getPackageNum());
        to.setNum(cisDrugApplyDetail.getPackageNum());
        to.setPackageUnit(cisDrugApplyDetail.getPackageUnit());
        to.setPackageUnitName(cisDrugApplyDetail.getPackageUnitName());
        to.setReceiveOrg(cisDrugApplyDetail.getReceiveOrg());
        to.setReceiveOrgName(cisDrugApplyDetail.getReceiveOrgName());
        to.setSbadmWay(cisDrugApplyDetail.getSbadmWay());
        to.setSkinResult(cisDrugApplyDetail.getSkinResult());
        to.setAntimicrobialsPurpose(cisDrugApplyDetail.getAntimicrobialsPurpose());
        to.setDecoctMethodCode(cisDrugApplyDetail.getDecoctMethodCode());
        to.setDecoctMethodName(cisDrugApplyDetail.getDecoctMethodName());
        to.setExtTypeCode(cisDrugApplyDetail.getExtTypeCode());
    }

    //手术
    private static <T> void extractedCisOperationApplyTo(CisBaseApply cisBaseApply, CisOperationApplyDetail detail, CisBaseApplyCustomTo to) {
        // 处理基本属性
        cisBaseApplyTo(cisBaseApply, to);
        if (detail == null) {
            return;
        }
        // 处理明细
        to.setId(detail.getId());
        to.setSortNo(detail.getSortNo());
        to.setOperationCode(detail.getOperationCode());
        to.setOperationName(detail.getOperationName());
        to.setServiceItemCode(detail.getServiceItemCode());
        to.setServiceItemName(detail.getServiceItemName());
        to.setHumanOrgans(detail.getHumanOrgans());
        to.setHumanOrgansName(detail.getHumanOrgansName());
        to.setDecubitus(detail.getDecubitus());
        to.setDecubitusName(detail.getDecubitusName());
        to.setOperationLevel(detail.getOperationLevel());
        to.setServiceItemCode(detail.getOperationCode());
        to.setServiceItemName(detail.getOperationName());
    }

    // 病理
    private static <T> void extractedCisPalgApplyTo(CisBaseApply cisBaseApply, CisPalgApplyDetail detail, CisBaseApplyCustomTo to) {
        // 处理基本属性
        cisBaseApplyTo(cisBaseApply, to);
        if (detail == null) {
            return;
        }
        // 处理明细
        to.setId(detail.getId());
        to.setSortNo(detail.getSortNo());
        to.setHumanOrgans(detail.getHumanOrgans());
        to.setSpeciman(detail.getSpeciman());
//        to.setSpecimanName(detail.getSpecimanName());
        to.setNum(Convert.toDouble(detail.getNum()));
        to.setCreatedStaff(detail.getCreatedStaff());
        to.setCreatedDate(detail.getCreatedDate());
        to.setUpdatedStaff(detail.getUpdatedStaff());
        to.setUpdatedDate(detail.getUpdatedDate());
        to.setOutVivoDate(detail.getOutVivoDate());
        to.setServiceItemCode(detail.getServiceItemCode());
    }

    // cisBaseApply属性提取
    private static void cisBaseApplyTo(CisBaseApply cisBaseApply, CisBaseApplyCustomTo to) {
        if (cisBaseApply == null) {
            return;
        }
        to.setId(cisBaseApply.getId());
        to.setApplyid(cisBaseApply.getId());
        to.setPatMiCode(cisBaseApply.getPatMiCode());
        to.setVisitCode(cisBaseApply.getVisitCode());
        to.setServiceItemCode(cisBaseApply.getServiceItemCode());
        to.setServiceItemName(cisBaseApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisBaseApply.getIsCanPriorityFlag());
        to.setCreatedStaff(cisBaseApply.getCreatedStaff());
        to.setCreatedDate(cisBaseApply.getCreatedDate());
        to.setVisitType(cisBaseApply.getVisitType());
        to.setDeptNurseCode(cisBaseApply.getDeptNurseCode());
        to.setDeptNurseName(cisBaseApply.getDeptNurseName());
        to.setOrderID(cisBaseApply.getOrderID());
        to.setHospitalCode(cisBaseApply.getHospitalCode());
        to.setPrescriptionID(cisBaseApply.getPrescriptionID());
        to.setReMark(cisBaseApply.getReMark());
        to.setIcuExecuteDate(cisBaseApply.getIcuExecuteDate());
        to.setIsChargeManager(cisBaseApply.getIsChargeManager());
        to.setCreateOrgCode(cisBaseApply.getCreateOrgCode());
        to.setSortNo(cisBaseApply.getSortNo());
        to.setIsBaby(cisBaseApply.getIsBaby());
        to.setUpdatedDate(cisBaseApply.getUpdatedDate());
        to.setStatusCode(cisBaseApply.getStatusCode());
        to.setVisitOrgCode(cisBaseApply.getVisitOrgCode());
        to.setVisitOrgName(cisBaseApply.getVisitOrgName());
        to.setExecutorOrgCode(cisBaseApply.getExecutorOrgCode());
        to.setIsOlation(cisBaseApply.getIsOlation());
        to.setIsApply(cisBaseApply.getIsApply());
        to.setIsOlation(cisBaseApply.getIsOlation());
        to.setFrequency(cisBaseApply.getFrequency());
        to.setFrequencyName(cisBaseApply.getFrequencyName());
        to.setSystemType(cisBaseApply.getSystemType());
        to.setOrderType(cisBaseApply.getOrderType());
    }

}