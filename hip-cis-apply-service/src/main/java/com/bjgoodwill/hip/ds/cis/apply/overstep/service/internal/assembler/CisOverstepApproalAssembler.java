package com.bjgoodwill.hip.ds.cis.apply.overstep.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.overstep.entity.CisOverstepApproal;
import com.bjgoodwill.hip.ds.cis.apply.overstep.to.CisOverstepApproalTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisOverstepApproalAssembler {

    public static List<CisOverstepApproalTo> toTos(List<CisOverstepApproal> cisOverstepApproals) {
        return toTos(cisOverstepApproals, false);
    }

    public static List<CisOverstepApproalTo> toTos(List<CisOverstepApproal> cisOverstepApproals, boolean withAllParts) {
        BusinessAssert.notNull(cisOverstepApproals, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisOverstepApproals不能为空！");

        List<CisOverstepApproalTo> tos = new ArrayList<>();
        for (CisOverstepApproal cisOverstepApproal : cisOverstepApproals)
            tos.add(toTo(cisOverstepApproal, withAllParts));
        return tos;
    }

    public static CisOverstepApproalTo toTo(CisOverstepApproal cisOverstepApproal) {
        return toTo(cisOverstepApproal, false);
    }

    /**
     * @generated
     */
    public static CisOverstepApproalTo toTo(CisOverstepApproal cisOverstepApproal, boolean withAllParts) {
        if (cisOverstepApproal == null)
            return null;
        CisOverstepApproalTo to = new CisOverstepApproalTo();
        to.setId(cisOverstepApproal.getId());
        to.setOrderId(cisOverstepApproal.getOrderId());
        to.setApplyId(cisOverstepApproal.getApplyId());
        to.setVisitType(cisOverstepApproal.getVisitType());
        to.setCheckType(cisOverstepApproal.getCheckType());
        to.setCheckSystemType(cisOverstepApproal.getCheckSystemType());
        to.setCreatedStaff(cisOverstepApproal.getCreatedStaff());
        to.setCreatedDate(cisOverstepApproal.getCreatedDate());
        to.setCreatedStaffName(cisOverstepApproal.getCreatedStaffName());
        to.setVersion(cisOverstepApproal.getVersion());
        to.setDeleted(cisOverstepApproal.isDeleted());
        to.setPatMiCode(cisOverstepApproal.getPatMiCode());
        to.setReviewStaff(cisOverstepApproal.getReviewStaff());
        to.setReviewStaffName(cisOverstepApproal.getReviewStaffName());
        to.setReviewDateTime(cisOverstepApproal.getReviewDateTime());
        to.setReviewOpinions(cisOverstepApproal.getReviewOpinions());
        to.setApplyDoc(cisOverstepApproal.getApplyDoc());
        to.setApplyOrgCode(cisOverstepApproal.getApplyOrgCode());
        to.setApplyDocName(cisOverstepApproal.getApplyDocName());
        to.setApplyOrgName(cisOverstepApproal.getApplyOrgName());
        to.setApplyDateTime(cisOverstepApproal.getApplyDateTime());
        to.setApplyReason(cisOverstepApproal.getApplyReason());
        to.setVisitCode(cisOverstepApproal.getVisitCode());
        to.setVisitOrgCode(cisOverstepApproal.getVisitOrgCode());
        to.setCreateOrgCode(cisOverstepApproal.getCreateOrgCode());
        to.setDeptNurseCode(cisOverstepApproal.getDeptNurseCode());
        if (withAllParts) {
        }
        return to;
    }

}