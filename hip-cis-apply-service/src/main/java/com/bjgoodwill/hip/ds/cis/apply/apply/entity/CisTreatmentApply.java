package com.bjgoodwill.hip.ds.cis.apply.apply.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisTreatmentApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "治疗申请单")
@DiscriminatorValue("15")
public class CisTreatmentApply extends CisBaseApply {

    // 频次
    private String frequency;

    public static Optional<CisTreatmentApply> getCisTreatmentApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisTreatmentApply> getCisTreatmentApplies(CisTreatmentApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisTreatmentApply> getCisTreatmentApplyPage(CisTreatmentApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisTreatmentApply> getSpecification(CisTreatmentApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            return predicate;
        };
    }

    private static CisTreatmentApplyRepository dao() {
        return SpringUtil.getBean(CisTreatmentApplyRepository.class);
    }

    @Comment("频次")
    @Column(name = "frequency", nullable = true)
    public String getFrequency() {
        return frequency;
    }

    protected void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.TREATMENT;
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisTreatmentApplyNto) cisBaseApplyNto, save);
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisTreatmentApplyEto) cisBaseApplyEto);
    }

    public CisTreatmentApply create(CisTreatmentApplyNto cisTreatmentApplyNto, Boolean save) {
        BusinessAssert.notNull(cisTreatmentApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisTreatmentApplyNto不能为空！");
        super.create(cisTreatmentApplyNto, save);

        BusinessAssert.hasText(cisTreatmentApplyNto.getServiceItemCode(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "serviceItemCode");
        setServiceItemCode(cisTreatmentApplyNto.getServiceItemCode());

        setFrequency(cisTreatmentApplyNto.getFrequency());
        if (save) {
            dao().save(this);
        }

        return this;
    }

    public void update(CisTreatmentApplyEto cisTreatmentApplyEto) {
        super.update(cisTreatmentApplyEto);
        setFrequency(cisTreatmentApplyEto.getFrequency());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
