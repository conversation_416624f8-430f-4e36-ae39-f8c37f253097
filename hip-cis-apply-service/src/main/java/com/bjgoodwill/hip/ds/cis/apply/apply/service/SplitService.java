package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.ds.base.cis.dict.frequency.service.FrequencyService;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisBaseApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisProofNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisSplitEto;
import com.bjgoodwill.hip.ds.cis.apply.charge.entity.CisApplyCharge;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisBaseDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisDrugApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisEDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlanCharge;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanChargeTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanNto;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanTo;
import com.bjgoodwill.hip.ds.cis.apply.mq.send.CisApplyMqSend;
import com.bjgoodwill.hip.ds.cis.apply.proxy.ApplyCodeServiceProxy;
import com.bjgoodwill.hip.lock.distributed.annotation.HipDistributedLock;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-12 09:12
 * @className: SplitService
 * @description: 申请单 拆分务
 **/
public abstract class SplitService {

    @Autowired
    protected ApplyCodeServiceProxy applyCodeServiceProxy;

    @Autowired
    private FrequencyService frequencyService;

    // 使用自适应线程池替代固定大小的线程池
    @Autowired
    private ThreadPoolTaskExecutor executorService;

    @Autowired
    private CisApplyMqSend cisApplyMqSend;

    //region 创建执行单和执行单的费用

    /**
     * 根据申请信息和收费信息创建Cis订单执行计划。
     *
     * @param splitConversion 申请信息，包含订单的执行频率等关键信息。
     * @param charges         订单的收费信息列表，用于生成订单的执行计划。
     * @return 返回一个订单执行计划列表，每个执行计划对应一次订单的执行。
     */
    private CisBaseApplyTo createCisOrderExecPlan(CisBaseApply apply, CisSplitConversion splitConversion,
                                                  List<CisApplyCharge> charges, Boolean save, List<CisOrderExecPlan> execPlans) throws IllegalArgumentException {
        // 根据申请的执行频率获取执行日期数组
        LocalDateTime[] executionDates = this.getExecutionDates(splitConversion);
        // 使用 toMap 方法进行一对一映射
        Map<String, CisOrderExecPlan> groupedByCisBaseApplyId = execPlans.stream().filter(a -> a.getExecPlanDate() != null).collect(Collectors.toMap(
                CisOrderExecPlan::getCisBaseApplyId,
                plan -> plan,
                (existing, replacement) -> existing));
        // 获取申请单拆分最后预计执行时间，用来判断是否需要创建新的执行计划
        LocalDateTime lastExecPlanDate = Convert.toLocalDateTime(groupedByCisBaseApplyId.get(splitConversion.getApplyId()) != null ? groupedByCisBaseApplyId.get(splitConversion.getApplyId()).getExecPlanDate() : null);
        List<CisOrderExecPlanTo> orderExecPlans = new ArrayList<>();
        CisBaseApplyNto cisBaseApplyNto = splitConversion.getApply();
        // 对执行日期数组进行流式处理，为每个执行日期创建一个订单执行计划
        Stream.of(executionDates).filter(a -> lastExecPlanDate == null || a.isAfter(lastExecPlanDate)).forEach(executionDate -> {
            orderExecPlans.addAll(this.createOrderExecPlanAndCharge(apply, executionDate, charges, save, cisBaseApplyNto));
        });

        CisBaseApplyTo cisBaseApplyTo = CisBaseApplyAssembler.toTo(apply);
        cisBaseApplyTo.setCisOrderExecPlans(orderExecPlans);
        return cisBaseApplyTo;
    }

    /**
     * 整取药检查
     *
     * @param charges
     * @param wholetakeDrugApplyDetails
     * @param notWholetakeApplyIds
     * @param splitConversion
     */
    private void wholetakeDrugcCheck(List<CisApplyCharge> charges, List<CisDrugApplyDetailNto> wholetakeDrugApplyDetails,
                                     List<String> notWholetakeApplyIds, CisSplitConversion splitConversion, List<CisApplyCharge> finalCharges) {
        // 获取所有药物申请详情
        List<CisDrugApplyDetailNto> drugApplyDetails = Optional.ofNullable(splitConversion.getApply())
                .filter(CisEDrugApplyNto.class::isInstance)
                .map(CisEDrugApplyNto.class::cast)
                .map(CisEDrugApplyNto::getDetails)
                .orElse(new ArrayList<>());
        // 分离整取药和非整取药
        wholetakeDrugApplyDetails.addAll(drugApplyDetails.stream()
                .filter(detail -> SbadmWayEnum.WHOLETAKE.equals(detail.getSbadmWay()))
                .collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(wholetakeDrugApplyDetails)) {
            notWholetakeApplyIds.addAll(drugApplyDetails.stream()
                    .filter(detail -> !(SbadmWayEnum.WHOLETAKE.equals(detail.getSbadmWay()))).map(CisDrugApplyDetailNto::getApplyId).distinct()
                    .collect(Collectors.toList()));
            // 过滤 splitCharges，排除与整取药匹配的费用项
            Set<String> wholeTakeDrugCodes = wholetakeDrugApplyDetails.stream()
                    .map(detail -> detail.getDrugCode() + "-" + detail.getReceiveOrg())
                    .collect(Collectors.toSet());
            finalCharges.addAll(charges.stream()
                    .filter(charge -> wholeTakeDrugCodes.stream()
                            .noneMatch(code -> code.equals(charge.getPriceItemCode() + "-" + charge.getExecuteOrgCode())))
                    .collect(Collectors.toList()));
        } else {
            Optional.ofNullable(charges)
                    .filter(c -> !c.isEmpty())
                    .ifPresent(finalCharges::addAll);
        }
    }

    protected List<CisOrderExecPlanTo> createOrderExecPlanAndCharge(CisBaseApply apply, LocalDateTime exexDate,
                                                                    List<CisApplyCharge> charges, Boolean save, CisBaseApplyNto cisBaseApplyNto) {
        Map<String, List<CisApplyCharge>> map;
        if (!CollectionUtils.isEmpty(charges)) {
            map = charges.stream().filter(cisApplyCharge -> !StringUtil.isEmpty(cisApplyCharge.getExecuteOrgCode()))
                    .collect(Collectors.groupingBy(CisApplyCharge::getExecuteOrgCode));
        } else {
            map = new HashMap<>();
        }
        List<CisOrderExecPlanTo> tos = new ArrayList<>();
        map.keySet().stream().forEach(executeOrgCode -> {
            // 创建订单执行计划
            CisOrderExecPlan cisOrderExecPlan = createOrderExecPlan(apply, exexDate, executeOrgCode, save, cisBaseApplyNto);
            List<CisApplyCharge> execCharge = map.get(executeOrgCode);
            // 为订单执行计划添加收费信息
            List<CisOrderExecPlanCharge> execPlanCharges = createOrderExecPlanCharge(apply, cisOrderExecPlan, execCharge, save);

            //region 数据转换
            CisOrderExecPlanTo to = CisOrderExecPlanAssembler.toTo(cisOrderExecPlan);
            List<CisOrderExecPlanChargeTo> execPlanChargeTos = CisOrderExecPlanChargeAssembler.toTos(execPlanCharges);
            to.setCisOrderExecPlanChargeTos(execPlanChargeTos);
            //endregion
            tos.add(to);
        });

        //region 无费申请单，生成一个执行单。
        if (CollectionUtils.isEmpty(map.get(apply.getExecutorOrgCode()))) {
            tos.add(CisOrderExecPlanAssembler.toTo(createOrderExecPlan(apply, exexDate, apply.getExecutorOrgCode(), save, cisBaseApplyNto)));
        }
        //endregion

        return tos;
    }

    /**
     * 创建订单执行计划费用项。
     * <p>
     * 本方法用于根据申请单、订单执行计划和费用信息，创建订单执行计划的费用详细信息。
     * 它通过填充费用详细信息对象的各项属性，然后调用创建方法，来实现订单执行计划费用项的建立。
     *
     * @param applie           申请单对象，提供申请单ID等信息。
     * @param cisOrderExecPlan 订单执行计划对象，提供订单执行计划ID等信息。
     * @param charges          费用信息对象，提供费用的各项详细信息，如价格、数量等。
     */
    private List<CisOrderExecPlanCharge> createOrderExecPlanCharge(CisBaseApply applie, CisOrderExecPlan cisOrderExecPlan,
                                                                   List<CisApplyCharge> charges, Boolean save) {

        // 创建订单执行计划费用项的详细信息对象
        CisOrderExecPlanChargeNto chargeNto = new CisOrderExecPlanChargeNto();
        List<CisOrderExecPlanCharge> lst = new ArrayList<>();
        charges.forEach(charge -> {
            // 设置订单执行计划费用项的详细信息
            // 设置费用详细信息的基本属性
            chargeNto.setCisBaseApplyId(applie.getId());
            chargeNto.setExecuteOrgCode(charge.getExecuteOrgCode());
            chargeNto.setExecuteOrgName(charge.getExecuteOrgName());
            chargeNto.setPrice(charge.getPrice());
            chargeNto.setPriceItemCode(charge.getPriceItemCode());
            chargeNto.setPriceItemName(charge.getPriceItemName());
            chargeNto.setNum(charge.getNum());
            chargeNto.setPackageSpec(charge.getPackageSpec());
            chargeNto.setUnit(charge.getUnit());
            chargeNto.setUnitName(charge.getUnitName());
            chargeNto.setChargeAmount(charge.getChageAmount());
            chargeNto.setIsFixed(charge.getIsFixed());
            chargeNto.setLimitConformFlag(charge.getLimitConformFlag());
            chargeNto.setVisitCode(applie.getVisitCode());
            chargeNto.setChargeType(charge.getChargeType());
            chargeNto.setSystemItemClass(charge.getSystemItemClass());
            chargeNto.setDetailId(charge.getDetailId());
            // 创建订单执行计划费用项对象
            CisOrderExecPlanCharge cisOrderExecPlanCharge = new CisOrderExecPlanCharge();
            // 使用订单执行计划费用项的详细信息创建订单执行计划费用项
            lst.add(cisOrderExecPlanCharge.create(cisOrderExecPlan.getId(), chargeNto, save)); // 假设此方法用于创建费用详细信息
        });
        return lst;
    }


    protected abstract LocalDateTime[] getExecutionDates(CisSplitConversion splitConversion);

    /**
     * 根据申请创建订单执行计划。
     *
     * @param applie 订单申请对象，可以是各种类型的申请，比如药品申请、检查申请等。
     * @return 返回创建的订单执行计划对象。
     * <p>
     * 该方法通过订单申请对象创建一个订单执行计划对象，并设置执行计划的相关信息。
     * 这里使用了面向对象的多态特性，通过判断申请对象的类型来设置特定的属性。
     */
    public CisOrderExecPlan createOrderExecPlan(CisBaseApply applie, LocalDateTime exexDate, String execOrgCode, Boolean save, CisBaseApplyNto cisBaseApplyNto) {
        // 初始化订单执行计划对象
        CisOrderExecPlan cisOrderExecPlan = new CisOrderExecPlan();

        // 初始化订单执行计划的详细信息
        //region 执行记录
        CisOrderExecPlanNto nto = new CisOrderExecPlanNto();
//        nto.setId(HIPIDUtil.getNextIdString());
        // 设置执行计划的相关属性，这些属性大部分是从申请对象中获取的
//        nto.setOrderClass(SystemTypeEnum.getValue(applie.getOrderClass()));
        nto.setOrderClass(applie.getSystemType());
        nto.setVisitCode(applie.getVisitCode());
        nto.setPatMiCode(applie.getPatMiCode());
        nto.setOrgCode(applie.getVisitOrgCode());
        nto.setOrgName(applie.getVisitOrgName());
        nto.setDeptNurseCode(applie.getDeptNurseCode());
        nto.setOrderId(applie.getOrderID());
        nto.setSortNo(applie.getSortNo());
        nto.setServiceItemCode(applie.getServiceItemCode());
        nto.setServiceItemName(applie.getServiceItemName());
        nto.setExecPlanDate(exexDate);
        // 草药的付数在子类中处理了，这里不单独处理
        nto.setNum(applie.getNum());

        nto.setExecOrgCode(execOrgCode);
        nto.setExecOrgName(applie.getExecutorOrgName());
        nto.setOrgName(applie.getVisitOrgName());
        nto.setHeldStaff(applie.getCreatedStaff());
        nto.setHeldStaffName(applie.getCreatedStaffName());
        // 如果申请对象是药品申请类型，则设置领药科室
        // 药品有领药科室
        Optional.ofNullable(cisBaseApplyNto)
                .filter(CisBaseDrugApplyNto.class::isInstance)
                .map(CisBaseDrugApplyNto.class::cast)
                .ifPresent(cisBaseDrugApplyNto -> {
                    nto.setReceiveOrg(cisBaseDrugApplyNto.getReceiveOrg());
                    nto.setReceiveOrgName(cisBaseDrugApplyNto.getReceiveOrgName());
                    nto.setUsage(cisBaseDrugApplyNto.getUsage());
                    nto.setUsageName(cisBaseDrugApplyNto.getUsageName());
                });

        nto.setCreateOrgCode(applie.getCreateOrgCode());
        nto.setCreateOrgName(applie.getCreateOrgName());
        nto.setMainPlanFlag(execOrgCode.equals(applie.getExecutorOrgCode()));
        nto.setCurLoginStaff(applie.getCreatedStaff());
        //endregion

        // 使用申请对象的ID和详细的执行计划信息创建并返回订单执行计划对象
        return cisOrderExecPlan.create(applie.getId(), nto, save); // 假设此方法创建并返回一个新的 CisOrderExecPlan 实例
    }

    //endregion

    /**
     * 根据访问码和申请单进行拆分证明的处理。
     * 使用分布式锁确保同一访问码下的处理操作互斥，防止并发问题。
     * 在一个事务中完成整个处理流程，确保数据的一致性。
     *
     * @param visitCode 访问码，用于唯一标识一批申请单。
     * @param applies   申请单列表，需要进行拆分证明处理的申请单集合。
     * @return 返回处理后的订单执行计划列表。
     */
    @HipDistributedLock(businessType = "proofSplit", keys = {"#{visitCode}"}, leaseTime = 60)
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> proofSplit(String visitCode, List<CisSplitConversion> applies, Boolean save) {
        // 将申请单列表转换为申请单ID列表
        List<String> orderids = applies.stream().map(CisSplitConversion::getOrderId).toList();

        // 断言申请单ID列表不为空，否则抛出业务异常
        BusinessAssert.notEmpty(orderids, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单");
        // 根据访问码查询所有的申请费用项，并按申请单ID进行分组
        // 按申请单分组
        List<String> ids = applies.stream().map(CisSplitConversion::getApplyId).toList();

        Map<String, List<CisApplyCharge>> map =
                CisApplyCharge.findCisApplyChargesByVisitCode(visitCode, CisStatusEnum.NEW, ids).stream()
                        .collect(Collectors.groupingBy(CisApplyCharge::getOrderId));


//        return applies.stream()
//                .map(applie->(processCharges(applie,map.get(applie.getOrderId()),save)).stream())
//                .flatMap(list->list).toList();
        // 获取所有申请单的执行计划，用来判断是否拆分过
        List<CisOrderExecPlan> execPlans = CisOrderExecPlan.getByCisBaseApplyIdOrderByExecPlanDateDesc(visitCode, applies.get(0).getEndDate());
        return applies.stream()
                .map(applie -> processCharges(applie, map.get(applie.getOrderId()), save, execPlans)).toList();
    }

    /**
     * 根据访问码和申请单进行拆分证明的处理。
     * 使用分布式锁确保同一访问码下的处理操作互斥，防止并发问题。
     * 在一个事务中完成整个处理流程，确保数据的一致性。
     *
     * @param visitCode 访问码，用于唯一标识一批申请单。
     * @param applies   申请单列表，需要进行拆分证明处理的申请单集合。
     * @return 返回处理后的订单执行计划列表。
     */
    @HipDistributedLock(businessType = "proofSplitProof", keys = {"#{visitCode}"}, leaseTime = 60)
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> proofSplitProof(String visitCode, List<CisSplitConversion> applies, Boolean save) {
        // 将申请单列表转换为申请单ID列表
        List<String> orderids = applies.stream().map(CisSplitConversion::getOrderId).toList();

        // 断言申请单ID列表不为空，否则抛出业务异常
        BusinessAssert.notEmpty(orderids, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单");
        // 根据访问码查询所有的申请费用项，并按申请单ID进行分组
        // 按申请单分组
        List<String> ids = applies.stream().map(CisSplitConversion::getApplyId).toList();

        Map<String, List<CisApplyCharge>> map =
                CisApplyCharge.findCisApplyChargesByVisitCode(visitCode, CisStatusEnum.NEW, ids).stream()
                        .collect(Collectors.groupingBy(CisApplyCharge::getOrderId));
        // 获取所有申请单的执行计划，用来判断是否拆分过
        List<CisOrderExecPlan> execPlans = CisOrderExecPlan.getByCisBaseApplyIdOrderByExecPlanDateDesc(visitCode, applies.get(0).getEndDate());
        return applies.stream()
                .map(applie -> processChargesProof(applie, map.get(applie.getOrderId()), save, execPlans)).toList();
    }


    /**
     * 处理费用信息，根据组织代码分组费用，并生成订单执行计划。
     *
     * @param applie  费用申请相关信息。
     * @param charges 费用详细列表。
     * @return 返回处理后的订单执行计划列表。
     */
    protected CisBaseApplyTo processCharges(CisSplitConversion applie, List<CisApplyCharge> charges,
                                            Boolean save, List<CisOrderExecPlan> execPlans) {
        //region 申请单操作。创建基础申请单
        CisBaseApply apply = doApply(applie.getApply(), save);
        //endregion

        return createCisOrderExecPlan(apply, applie, charges, save, execPlans);
    }

    protected CisBaseApplyTo processChargesProof(CisSplitConversion applie, List<CisApplyCharge> charges,
                                                 Boolean save, List<CisOrderExecPlan> execPlans) {
        //region 申请单操作。创建基础申请单
        CisBaseApply apply = doApplyProof(applie.getApply(), save);
        //endregion

        return createCisOrderExecPlan(apply, applie, charges, save, execPlans);
    }

    public List<CisBaseApplyTo> processSplits(List<CisSplitConversion> applies, Boolean save) {

        // 确保查询到的cis基础申请单不为空
        BusinessAssert.notEmpty(applies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");

        //存拆分后的执行单
        List<CisBaseApplyTo> cisOrderExecPlanTos = processSplitsAsync(applies, save);

        return cisOrderExecPlanTos;
    }

    public List<CisBaseApplyTo> processSplitsProof(List<CisSplitConversion> applies, Boolean save) {

        // 确保查询到的cis基础申请单不为空
//        BusinessAssert.notEmpty(applies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");
        if (CollectionUtils.isEmpty(applies)) {
            return Collections.emptyList();
        }
        //存拆分后的执行单
        List<CisBaseApplyTo> cisOrderExecPlanTos = processSplitsAsyncProof(applies, save);

        return cisOrderExecPlanTos;
    }

    protected abstract CisBaseApply doApply(CisBaseApplyNto applyNto, Boolean save);

    protected abstract CisBaseApply doApplyProof(CisBaseApplyNto applyNto, Boolean save);

    /**
     * 异步处理证明文件。
     * 使用工作窃取线程池来执行异步任务，每个任务会对一组具有相同访问码的证明进行处理。
     * 这种方式可以提高处理大量证明文件的效率，同时避免单个线程处理所有任务导致的性能瓶颈。
     *
     * @param proofs 证明文件列表，每个证明文件包含一个访问码。
     */
    private List<CisBaseApplyTo> processSplitsAsync(List<CisSplitConversion> proofs, Boolean save) {
        // 检查证明文件列表是否为空，如果为空则直接返回。
        if (CollectionUtils.isEmpty(proofs)) {
            return Collections.emptyList();
        }

        // 创建一个工作窃取线程池，用于执行异步任务。
        // 用于存储所有拆分后的执行计划。
        List<CisBaseApplyTo> allApplies = new ArrayList<>();
        CisFrequenciesService.setFrequencies(frequencyService.getFrequencies());

        // 创建并启动异步任务，每个任务负责处理一组具有相同访问码的证明文件。
        List<CompletableFuture<Void>> tasks = proofs.stream()
                .collect(Collectors.groupingBy(CisSplitConversion::getVisitCode))
                .values()
                .stream()
                .map(v -> {
                    // 使用CompletableFuture.runAsync启动一个异步任务。
                    CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                        // 调用临时拆分服务，对一组证明进行拆分，并将结果添加到allPlans列表中。
                        List<CisBaseApplyTo> applies = proofSplit(v.get(0).getVisitCode(), v, save);
                        // 线程安全地添加拆分后的执行计划。
                        // 线程安全地添加元素
                        synchronized (this) {
                            if (!CollectionUtils.isEmpty(applies)) {
                                allApplies.addAll(applies);
                            }
                        }
                    }, executorService);
                    return task;
                })
                .collect(Collectors.toList());

        // 等待所有异步任务完成。
        CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).join();

        // 关闭线程池。
        // 关闭线程池
//        executorService.shutdown();
        return allApplies;
    }

    /**
     * 异步处理证明文件。
     * 使用工作窃取线程池来执行异步任务，每个任务会对一组具有相同访问码的证明进行处理。
     * 这种方式可以提高处理大量证明文件的效率，同时避免单个线程处理所有任务导致的性能瓶颈。
     *
     * @param proofs 证明文件列表，每个证明文件包含一个访问码。
     */
    private List<CisBaseApplyTo> processSplitsAsyncProof(List<CisSplitConversion> proofs, Boolean save) {
        // 检查证明文件列表是否为空，如果为空则直接返回。
        if (CollectionUtils.isEmpty(proofs)) {
            return Collections.emptyList();
        }
        // 用于存储所有拆分后的执行计划。
        List<CisBaseApplyTo> allApplies = new ArrayList<>();
        CisFrequenciesService.setFrequencies(frequencyService.getFrequencies());
        proofs.forEach(a -> a.getApply().setCurLoginStaff(HIPLoginUtil.getStaffId()));
        // 创建并启动异步任务，每个任务负责处理一组具有相同访问码的证明文件。
        List<CompletableFuture<Void>> tasks = proofs.stream()
                .collect(Collectors.groupingBy(CisSplitConversion::getVisitCode))
                .values()
                .stream()
                .map(v -> {
                    // 使用CompletableFuture.runAsync启动一个异步任务。
                    CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                        // 调用临时拆分服务，对一组证明进行拆分，并将结果添加到allPlans列表中。
                        List<CisBaseApplyTo> applies = proofSplitProof(v.get(0).getVisitCode(), v, save);
                        // 线程安全地添加拆分后的执行计划。
                        // 线程安全地添加元素
                        synchronized (this) {
                            if (!CollectionUtils.isEmpty(applies)) {
                                allApplies.addAll(applies);
                            }
                        }
                    }, executorService);
                    return task;
                })
                .collect(Collectors.toList());

        // 等待所有异步任务完成。
        CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).join();
        // 记录拆分流程日志，发消息放在异步方法内缺失请求头
        if (save) {
            allApplies.stream().filter(a -> VisitTypeEnum.IPD.equals(a.getVisitType()))
                    .forEach(a -> cisApplyMqSend.OrderExecLogSend(a.getCisOrderExecPlans()));
        }
        return allApplies;
    }

    /**
     * 根据ETOs和申请单列表，获取拆分转换列表。
     * 此方法通过匹配ETOs中的订单ID与申请单列表中的订单ID，将匹配到的申请单信息与ETOs的日期范围一起封装成拆分转换对象。
     *
     * @param etos    ETOs列表，包含需要转换的日期范围和订单ID。
     * @param applies 申请单列表，用于查找与ETOs中订单ID对应的申请单信息。
     * @return 返回一个拆分转换对象的列表，每个对象包含一个日期范围和对应的申请单信息。
     */
    protected abstract List<CisSplitConversion> getCisSplitConversion(List<CisSplitEto> etos, List<CisBaseApplyNto> applies);


    //创建拆分的申请单和申请单明细还有费用。
    public List<CisBaseApplyTo> splitCisBaseApplys(List<CisSplitEto> etos, Boolean save) {
        List<String> orderids = etos.stream().map(CisSplitEto::getOrderId).collect(Collectors.toList());
        ApplyCreateService applyCreateService = SpringUtil.getBean(ApplyCreateService.class);
        List<CisBaseApplyNto> applies = applyCreateService.createcisBaseApplysNewByOrderIdProof(etos.get(0).getVisitCode(), orderids);
        applies = getCisBaseApplyNtos(applies);
        return processSplits(getCisSplitConversion(etos, applies), save);
    }

    /**
     * 根据提供的列表拆分创建申请单及其详细信息和费用
     * 此方法主要用于处理拆分申请单的逻辑，包括创建申请单、更新申请单详细信息和关联费用
     *
     * @param etos 包含拆分信息的列表，用于生成申请单
     * @param save 一个布尔值，指示是否将生成的申请单保存到数据库中
     * @return 返回一个包含拆分后申请单信息的列表
     */
    public List<CisBaseApplyTo> splitCisBaseApplysProof(List<CisSplitEto> etos, Boolean save) {
        // 提取订单ID列表，以便后续使用
        List<String> orderids = etos.stream().map(CisSplitEto::getOrderId).collect(Collectors.toList());

        // 获取ApplyCreateService实例，用于创建申请单
        ApplyCreateService applyCreateService = SpringUtil.getBean(ApplyCreateService.class);

        // 调用服务创建新的基础申请单，基于订单ID和访问代码
        List<CisBaseApplyNto> applies = applyCreateService.createcisBaseApplysNewByOrderIdProof(etos.get(0).getVisitCode(), orderids);

        // 进一步处理申请单，确保它们的详细信息被正确设置
        applies = getCisBaseApplyNtos(applies);

        // 最后处理拆分逻辑，并根据'save'参数决定是否保存申请单
        return processSplitsProof(getCisSplitConversion(etos, applies), save);
    }

    protected abstract List<CisBaseApplyNto> getCisBaseApplyNtos(List<CisBaseApplyNto> applies);

    protected abstract List<CisBaseApplyNto> getApplyNtos(List<CisSplitEto> etos);

    //region 拆分不新建申请单的校对拆分。

    /**
     * 根据校对拆分实体列表进行拆分操作，不新建申请单
     *
     * @param etos 校对拆分实体列表
     * @return 拆分后生成的执行计划传输对象列表
     */
    public List<CisBaseApplyTo> splitApplys(List<CisSplitEto> etos) {
        // 获取拆分所需的申请单信息
        List<CisBaseApplyNto> applies = getApplyNtos(etos);
        // 根据获取的信息进行拆分操作，并返回拆分结果
        return splitExecPlans(getCisSplitConversion(etos, applies), false);
    }

    /**
     * 根据拆分转换列表来生成执行计划
     * <p>
     * 此方法通过流处理将每个拆分转换对象映射为一个执行计划对象，并决定是否保存
     * 它首先使用 map 函数对流中的每个元素应用 createOrderExecPlanAndCharge 方法
     * 然后使用 flatMap 函数将结果流展平，最后收集结果到列表中
     *
     * @param splitConversions 拆分转换列表，包含了需要转换的所有对象
     * @param save             指示是否保存生成的执行计划的布尔值
     * @return 返回生成的执行计划列表
     */
    private List<CisBaseApplyTo> splitExecPlans(List<CisSplitConversion> splitConversions, Boolean save) {
        return splitConversions.stream()
                .map(p -> createOrderExecPlanAndCharge(p, save))
                .flatMap(Collection::stream)
                .toList();
    }

    /**
     * 创建订单执行计划并分配费用
     * <p>
     * 该方法根据分拆转换申请的信息创建订单执行计划，并根据执行计划分配相关费用它首先获取所有执行日期，
     * 然后为每个执行日期创建相应的订单执行计划和费用分配此方法支持保存所创建的记录选项
     *
     * @param splitConversion 分拆转换申请对象，包含申请信息及费用详情
     * @param save            控制是否保存创建的订单执行计划和费用分配的标志
     * @return 返回一个包含所有创建的订单执行计划的对象列表
     */
    private List<CisBaseApplyTo> createOrderExecPlanAndCharge(CisSplitConversion splitConversion, Boolean save) {
        // 根据申请的执行频率获取执行日期数组
        LocalDateTime[] executionDates = this.getExecutionDates(splitConversion);

        List<CisBaseApplyTo> applyTos = new ArrayList<>();
        // 获取申请中的费用列表
        List<CisApplyChargeNto> charges = splitConversion.getApply().getCisApplyCharges();

        CisBaseApplyNto cisBaseApplyNto = splitConversion.getApply();

        // 对执行日期数组进行流式处理，为每个执行日期创建一个订单执行计划
        Stream.of(executionDates).forEach(executionDate -> {
            // 对每个执行日期，调用另一方法创建订单执行计划并处理费用分配
            applyTos.add(this.createOrderExecPlanAndCharge(splitConversion.getApply(), executionDate, charges, save, cisBaseApplyNto));
        });

        // 返回创建的订单执行计划列表
        return applyTos;
    }

    /**
     * 根据申请信息和收费信息创建订单执行计划及其收费信息
     *
     * @param nto      申请信息对象
     * @param exexDate 执行日期
     * @param charges  收费信息列表
     * @param save     是否保存数据标识
     * @return 订单执行计划及其收费信息的传输对象列表
     */
    private CisBaseApplyTo createOrderExecPlanAndCharge(CisBaseApplyNto nto, LocalDateTime exexDate, List<CisApplyChargeNto> charges, Boolean save, CisBaseApplyNto cisBaseApplyNto) {

        // 创建申请信息，但不保存
        CisBaseApply apply = doApply(nto, false);

        // 将收费信息按照执行机构代码分组
        Map<String, List<CisApplyChargeNto>> map = charges.stream().collect(Collectors.groupingBy(CisApplyChargeNto::getExecuteOrgCode));

        // 创建订单执行计划及其收费信息的传输对象列表
        List<CisOrderExecPlanTo> tos = new ArrayList<>();

        // 遍历每一条执行机构代码对应的收费信息
        map.keySet().stream().forEach(executeOrgCode -> {
            // 创建订单执行计划，但不保存
            CisOrderExecPlan cisOrderExecPlan = createOrderExecPlan(apply, exexDate, executeOrgCode, save, cisBaseApplyNto);

            // 获取当前执行机构代码对应的收费信息
            List<CisApplyChargeNto> execCharge = map.get(executeOrgCode);

            // 为订单执行计划添加收费信息
            List<CisOrderExecPlanCharge> execPlanCharges = createOrderExecPlanCharge2(apply, cisOrderExecPlan, execCharge, save);

            // 将订单执行计划转换为传输对象
            CisOrderExecPlanTo to = CisOrderExecPlanAssembler.toTo(cisOrderExecPlan);

            // 将订单执行计划的收费信息转换为传输对象，并设置到订单执行计划的传输对象中
            List<CisOrderExecPlanChargeTo> execPlanChargeTos = CisOrderExecPlanChargeAssembler.toTos(execPlanCharges);
            to.setCisOrderExecPlanChargeTos(execPlanChargeTos);

            // 将订单执行计划的传输对象添加到列表中
            tos.add(to);
        });

        // 检查是否存在执行机构代码与申请人的执行机构代码相同的收费信息，如果不存在，则创建一个订单执行计划
        if (CollectionUtils.isEmpty(map.get(apply.getExecutorOrgCode()))) {
            tos.add(CisOrderExecPlanAssembler.toTo(createOrderExecPlan(apply, exexDate, apply.getExecutorOrgCode(), save, cisBaseApplyNto)));
        }

        CisBaseApplyTo applyTo = CisBaseApplyAssembler.toTo(apply);
        applyTo.setCisOrderExecPlans(tos);
        // 返回订单执行计划及其收费信息的传输对象列表
        return applyTo;
    }

    /**
     * 根据申请单费用明细生成订单执行计划费用
     *
     * @param applie           申请单基本信息对象
     * @param cisOrderExecPlan 订单执行计划对象
     * @param chargeNto        申请单费用明细列表
     * @param save             标识是否保存生成的费用信息
     * @return 返回生成的订单执行计划费用明细列表
     */
    private List<CisOrderExecPlanCharge> createOrderExecPlanCharge2(CisBaseApply applie, CisOrderExecPlan cisOrderExecPlan,
                                                                    List<CisApplyChargeNto> chargeNto, Boolean save) {
        return chargeNto.stream().map(charge -> {
            CisOrderExecPlanCharge cisOrderExecPlanCharge = new CisOrderExecPlanCharge();
            CisOrderExecPlanChargeNto planChargeNto = new CisOrderExecPlanChargeNto();
            // 设置费用详细信息的基本属性
            planChargeNto.setCisBaseApplyId(applie.getId());
            planChargeNto.setExecuteOrgCode(charge.getExecuteOrgCode());
            planChargeNto.setPrice(charge.getPrice());
            planChargeNto.setPriceItemCode(charge.getPriceItemCode());
            planChargeNto.setPriceItemName(charge.getPriceItemName());
            planChargeNto.setNum(charge.getNum());
            planChargeNto.setPackageSpec(charge.getPackageSpec());
            planChargeNto.setUnit(charge.getUnit());
            planChargeNto.setChargeAmount(charge.getChageAmount());
            planChargeNto.setIsFixed(charge.getIsFixed());
            planChargeNto.setLimitConformFlag(charge.getLimitConformFlag());
            planChargeNto.setVisitCode(applie.getVisitCode());
            // 根据订单执行计划ID和费用详细信息创建订单执行计划费用
            return cisOrderExecPlanCharge.create(cisOrderExecPlan.getId(), planChargeNto, save);
        }).toList();
    }


    public void saveProofExecPlan(List<CisProofNto> proofNtos) {
        BusinessAssert.notNull(proofNtos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "校对数据！");

        //1. 需要 按人 分个组
        Map<String, List<CisProofNto>> map = proofNtos.stream().collect(Collectors.groupingBy(CisProofNto::getVisitCode));

        List<CompletableFuture<Void>> tasks = new ArrayList<>();
        map.forEach((visitCode, proofNtoList) -> {
            // 使用CompletableFuture.runAsync启动一个异步任务。
            CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                // 调用临时拆分服务，对一组证明进行拆分，并将结果添加到allPlans列表中。
                saveAndJudgeExecPlan(visitCode, proofNtoList);
                // 线程安全地添加拆分后的执行计划。
            }, executorService);
            tasks.add(task);
        });

        CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).join();
    }


    /**
     * 保存并判断执行计划
     * 该方法通过验证订单计划的正确性，处理并拆分执行计划
     *
     * @param visitCode  访问代码，用于标识和查询相关的执行计划
     * @param orderPlans 订单计划列表，包含相关的执行计划和其他信息
     */
    @Transactional
    public void saveAndJudgeExecPlan(String visitCode, List<CisProofNto> orderPlans) {
        // 验证订单计划的有效性和正确性
        judgeOrderPlan(visitCode, orderPlans);

        // 从订单计划中提取执行计划对象
        List<CisOrderExecPlanNto> proofExecPlans = orderPlans.stream().map(p -> p.getExecPlanNtos()).flatMap(List::stream).toList();

        // 从订单计划中提取申请ID列表
        List<String> proofApplyIds = orderPlans.stream().map(CisProofNto::getApplyId).collect(Collectors.toList());
        // 根据访问代码和申请ID列表获取执行计划
        List<CisOrderExecPlan> execPlans = getOrderExecPlans(visitCode, proofApplyIds);

        // 检查订单计划与提取的执行计划是否一致
        checkOrderPlan(execPlans, proofExecPlans);

        // 处理并拆分执行计划
        proofSplitExecPlan(visitCode, proofExecPlans);
    }

    protected abstract void judgeOrderPlan(String visitCode, List<CisProofNto> orderPlans);


    /**
     * 根据就诊单号和申请单ID列表获取订单执行计划列表
     *
     * @param visitCode 就诊单号，用于识别患者的一次就诊
     * @param applyIds  申请单ID列表，每个申请单对应一个订单执行计划
     * @return 返回订单执行计划列表，包含所有匹配的订单执行计划
     */
    protected List<CisOrderExecPlan> getOrderExecPlans(String visitCode, List<String> applyIds) {
        return CisOrderExecPlan.findProofExecCisOrderExecPlans(visitCode, applyIds);
    }


    /**
     * 校验订单执行计划的唯一性
     * 该方法旨在确保每个订单在相同的执行计划日期内不会有重复的执行计划
     *
     * @param execPlans      正式的执行计划列表
     * @param proofExecPlans 证据执行计划列表，用于和正式的执行计划列表进行对比
     */
    private void checkOrderPlan(List<CisOrderExecPlan> execPlans, List<CisOrderExecPlanNto> proofExecPlans) {
        // 如果执行计划非空，则进行进一步校验
        if (!CollectionUtils.isEmpty(execPlans)) {
            // 将执行计划的订单ID和执行计划日期组合成集合，用于快速查找
            Set<String> proofJudge = execPlans.stream().map(p -> p.getOrderId() + p.getExecPlanDate()).collect(Collectors.toSet());
            // 找出重复的订单ID
            Set<String> repeat = proofExecPlans.stream().filter(plan -> proofJudge.contains(plan.getOrderId() + plan.getExecPlanDate()))
                    .map(p -> p.getOrderId()).collect(Collectors.toSet());

            // 断言重复的订单ID列表非空，否则抛出异常
            BusinessAssert.isTrue(!CollectionUtils.isEmpty(proofExecPlans), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0002, repeat);
        }
    }


    /**
     * 执行证明拆分的执行计划
     * 该方法通过分布式锁保护，以确保当多个线程尝试对同一个拜访代码执行证明拆分时，不会造成数据不一致
     * 它将订单执行计划按订单ID分组，并为每份订单创建新的执行计划
     *
     * @param visitCode         拜访代码，用于获取分布式锁，确保数据安全
     * @param orderExecPlanNtos 订单执行计划列表，包含需要拆分的订单信息
     */
    @HipDistributedLock(businessType = "proofSplit", keys = {"#{visitCode}"}, leaseTime = 60)
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    protected void proofSplitExecPlan(String visitCode, List<CisOrderExecPlanNto> orderExecPlanNtos) {
        // 将订单执行计划按订单ID分组，以方便后续处理
        Map<String, List<CisOrderExecPlanNto>> map =
                orderExecPlanNtos.stream().collect(Collectors.groupingBy(CisOrderExecPlanNto::getOrderId));

        // 遍历分组后的订单执行计划，为每个订单创建新的执行计划
        map.forEach((orderId, orderExecPlans) -> {
            // 尝试获取订单执行计划，可能存在获取不到的情况
            Optional<CisOrderExecPlan> cisOrderExecPlan = CisOrderExecPlan.getCisOrderExecPlanById(orderId);
            // 如果订单执行计划存在，则进行创建操作
            if (cisOrderExecPlan.isPresent()) {
                cisOrderExecPlan.get().create(cisOrderExecPlan.get().getCisBaseApplyId(), orderExecPlans, true);
            }
        });
    }
    //endregion
}