package com.bjgoodwill.hip.ds.cis.apply.apply.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisChangeDeptApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "转科申请单")
@DiscriminatorValue("17")
public class CisChangeDeptApply extends CisBaseApply {

    // 转出科室
    private String outOrgCode;
    // 转出科室名称
    private String outOrgName;
    // 转出护理组
    private String outDeptNurseCode;
    // 转出护理组名称
    private String outDeptNurseName;
    // 转出医院编码
    private String outHospitalCode;
    // 转出医院名称
    private String outHospitalName;
    // 转入科室
    private String inOrgCode;
    // 转入科室名称
    private String inOrgName;
    // 转入护理组
    private String inDeptNurseCode;
    // 转入护理组名称
    private String inDeptNurseName;
    // 转入医院编码
    private String inHospitalCode;
    // 转入医院名称
    private String inHospitalName;

    public static Optional<CisChangeDeptApply> getCisChangeDeptApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisChangeDeptApply> getCisChangeDeptApplies(CisChangeDeptApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisChangeDeptApply> getCisChangeDeptApplyPage(CisChangeDeptApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisChangeDeptApply> getSpecification(CisChangeDeptApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            return predicate;
        };
    }

    private static CisChangeDeptApplyRepository dao() {
        return SpringUtil.getBean(CisChangeDeptApplyRepository.class);
    }

    @Comment("转出科室")
    @Column(name = "out_org_code", nullable = true)
    public String getOutOrgCode() {
        return outOrgCode;
    }

    protected void setOutOrgCode(String outOrgCode) {
        this.outOrgCode = outOrgCode;
    }

    @Comment("转出护理组")
    @Column(name = "out_dept_nurse_code", nullable = true)
    public String getOutDeptNurseCode() {
        return outDeptNurseCode;
    }

    protected void setOutDeptNurseCode(String outDeptNurseCode) {
        this.outDeptNurseCode = outDeptNurseCode;
    }

    @Comment("转出医院编码")
    @Column(name = "out_hospital_code", nullable = true)
    public String getOutHospitalCode() {
        return outHospitalCode;
    }

    protected void setOutHospitalCode(String outHospitalCode) {
        this.outHospitalCode = outHospitalCode;
    }

    @Comment("转入科室")
    @Column(name = "in_org_code", nullable = true)
    public String getInOrgCode() {
        return inOrgCode;
    }

    protected void setInOrgCode(String inOrgCode) {
        this.inOrgCode = inOrgCode;
    }

    @Comment("转入护理组")
    @Column(name = "in_dept_nurse_code", nullable = true)
    public String getInDeptNurseCode() {
        return inDeptNurseCode;
    }

    protected void setInDeptNurseCode(String inDeptNurseCode) {
        this.inDeptNurseCode = inDeptNurseCode;
    }

    @Comment("转入医院编码")
    @Column(name = "in_hospital_code", nullable = true)
    public String getInHospitalCode() {
        return inHospitalCode;
    }

    protected void setInHospitalCode(String inHospitalCode) {
        this.inHospitalCode = inHospitalCode;
    }

    @Comment("转出科室名称")
    @Column(name = "out_org_name", nullable = true)
    public String getOutOrgName() {
        return outOrgName;
    }

    public void setOutOrgName(String outOrgName) {
        this.outOrgName = outOrgName;
    }

    @Comment("转出护理组名称")
    @Column(name = "out_dept_nurse_name", nullable = true)
    public String getOutDeptNurseName() {
        return outDeptNurseName;
    }

    public void setOutDeptNurseName(String outDeptNurseName) {
        this.outDeptNurseName = outDeptNurseName;
    }

    @Comment("转出医院名称")
    @Column(name = "out_hospital_name", nullable = true)
    public String getOutHospitalName() {
        return outHospitalName;
    }

    public void setOutHospitalName(String outHospitalName) {
        this.outHospitalName = outHospitalName;
    }

    @Comment("转入科室名称")
    @Column(name = "in_org_name", nullable = true)
    public String getInOrgName() {
        return inOrgName;
    }

    public void setInOrgName(String inOrgName) {
        this.inOrgName = inOrgName;
    }

    @Comment("转入护理组名称")
    @Column(name = "in_dept_nurse_name", nullable = true)
    public String getInDeptNurseName() {
        return inDeptNurseName;
    }

    public void setInDeptNurseName(String inDeptNurseName) {
        this.inDeptNurseName = inDeptNurseName;
    }

    @Comment("转入医院名称")
    @Column(name = "in_hospital_name", nullable = true)
    public String getInHospitalName() {
        return inHospitalName;
    }

    public void setInHospitalName(String inHospitalName) {
        this.inHospitalName = inHospitalName;
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.CHANGEDEPT;
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisChangeDeptApplyNto) cisBaseApplyNto, save);
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisChangeDeptApplyEto) cisBaseApplyEto);
    }

    public CisChangeDeptApply create(CisChangeDeptApplyNto cisChangeDeptApplyNto, Boolean save) {
        BusinessAssert.notNull(cisChangeDeptApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "参数cisChangeDeptApplyNto");
        super.create(cisChangeDeptApplyNto, save);
        BusinessAssert.hasText(cisChangeDeptApplyNto.getServiceItemCode(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "serviceItemCode");
        setServiceItemCode(cisChangeDeptApplyNto.getServiceItemCode());

        setOutOrgCode(cisChangeDeptApplyNto.getOutOrgCode());
        setOutOrgName(cisChangeDeptApplyNto.getOutOrgName());
        setOutDeptNurseCode(cisChangeDeptApplyNto.getOutDeptNurseCode());
        setOutDeptNurseName(cisChangeDeptApplyNto.getOutDeptNurseName());
        setOutHospitalCode(cisChangeDeptApplyNto.getOutHospitalCode());
        setOutHospitalName(cisChangeDeptApplyNto.getOutHospitalName());
        setInOrgCode(cisChangeDeptApplyNto.getInOrgCode());
        setOutOrgName(cisChangeDeptApplyNto.getOutHospitalName());
        setInDeptNurseCode(cisChangeDeptApplyNto.getInDeptNurseCode());
        setDeptNurseName(cisChangeDeptApplyNto.getInDeptNurseName());
        setInHospitalCode(cisChangeDeptApplyNto.getInHospitalCode());
        setInHospitalName(cisChangeDeptApplyNto.getInHospitalName());
        dao().save(this);
        return this;
    }

    public void update(CisChangeDeptApplyEto cisChangeDeptApplyEto) {
        super.update(cisChangeDeptApplyEto);
        setOutOrgCode(cisChangeDeptApplyEto.getOutOrgCode());
        setOutOrgName(cisChangeDeptApplyEto.getOutOrgName());
        setOutDeptNurseCode(cisChangeDeptApplyEto.getOutDeptNurseCode());
        setOutDeptNurseName(cisChangeDeptApplyEto.getOutDeptNurseName());
        setOutHospitalCode(cisChangeDeptApplyEto.getOutHospitalCode());
        setOutHospitalName(cisChangeDeptApplyEto.getOutHospitalName());
        setInOrgCode(cisChangeDeptApplyEto.getInOrgCode());
        setInOrgName(cisChangeDeptApplyEto.getInOrgName());
        setInDeptNurseCode(cisChangeDeptApplyEto.getInDeptNurseCode());
        setInDeptNurseName(cisChangeDeptApplyEto.getInDeptNurseName());
        setInHospitalCode(cisChangeDeptApplyEto.getInHospitalCode());
        setInHospitalName(cisChangeDeptApplyEto.getInHospitalName());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
