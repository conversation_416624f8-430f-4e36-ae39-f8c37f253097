package com.bjgoodwill.hip.ds.cis.apply.apply.service.internal;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.exception.BusinessException;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.*;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisBaseApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisBaseApplyCustomAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.*;
import com.bjgoodwill.hip.ds.cis.apply.charge.entity.CisApplyCharge;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeEto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeQto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeTo;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.ApplyWithDetial;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApply;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.service.internal.assembler.CisDgimgApplyDetailAssembler;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.diag.entity.ApplyDiagnosis;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisQto;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisTo;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisDrugApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.drug.service.internal.assembler.CisDrugApplyDetailAssembler;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisBaseDrugApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlanCharge;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.CisOrderExecPlanService;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.*;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApply;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.service.internal.assembler.CisSpcobsApplyDetailAssembler;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import com.bjgoodwill.hip.idempotent.annotation.HipIdempotent;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public abstract class CisBaseApplyServiceImpl implements CisBaseApplyService {

    //申请单校对
    private ApplyProofService applyProofService = SpringUtil.getBean(ApplyProofService.class);
    private ExecuteService executeService = SpringUtil.getBean(ExecuteService.class);
    @Autowired
    private CisApplyCustomService cisApplyCustomService;
    @Autowired
    private DictElementService dictElementService;


    protected String getDictName(String dictCode, String dictElementCode) {
        Map<String, Map<String, String>> map = getServiceDicts();
        BusinessAssert.notEmpty(map.get(dictCode), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00016, dictCode, "");

        BusinessAssert.hasText(map.get(dictCode).get(dictElementCode),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00016, dictCode, dictElementCode);
        return map.get(dictCode).get(dictElementCode);
    }

    protected Map<String, Map<String, String>> getServiceDicts() {
        return new HashMap<>();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisBaseApplyTo> getCisBaseApplies(CisBaseApplyQto cisBaseApplyQto) {
        return CisBaseApplyAssembler.toTos(CisBaseApply.getCisBaseApplies(cisBaseApplyQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisBaseApplyTo> getCisBaseApplyPage(CisBaseApplyQto cisBaseApplyQto) {
        Page<CisBaseApply> page = CisBaseApply.getCisBaseApplyPage(cisBaseApplyQto);
        Page<CisBaseApplyTo> result = page.map(CisBaseApplyAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisBaseApply(String id) {
        Optional<CisBaseApply> cisBaseApplyOptional = CisBaseApply.getCisBaseApplyById(id);
        cisBaseApplyOptional.ifPresent(cisBaseApply -> cisBaseApply.deleteById());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisApplyCharge(String id) {
        Optional<CisApplyCharge> cisApplyChargeOptional = CisApplyCharge.getCisApplyChargeById(id);
        cisApplyChargeOptional.ifPresent(cisApplyCharge -> cisApplyCharge.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisApplyChargeByIds(List<String> ids) {
        ids.forEach(a -> deleteCisApplyCharge(a));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisApplyChargeTo getCisApplyChargeById(String id) {
        Optional<CisApplyCharge> cisApplyChargeOptional = CisApplyCharge.getCisApplyChargeById(id);
        return cisApplyChargeOptional.map(CisApplyChargeAssembler::toTo).orElse(null);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderExecPlanTo> getCisOrderExecPlans(String cisBaseApplyId, CisOrderExecPlanQto cisOrderExecPlanQto) {
        return CisOrderExecPlanAssembler.toTos(CisOrderExecPlan.getCisOrderExecPlans(cisBaseApplyId, cisOrderExecPlanQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisOrderExecPlanTo> getCisOrderExecPlanPage(String cisBaseApplyId, CisOrderExecPlanQto cisOrderExecPlanQto) {
        Page<CisOrderExecPlan> page = CisOrderExecPlan.getCisOrderExecPlanPage(cisBaseApplyId, cisOrderExecPlanQto);
        Page<CisOrderExecPlanTo> result = page.map(CisOrderExecPlanAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisOrderExecPlanTo getCisOrderExecPlanById(String id) {
        return CisOrderExecPlanAssembler.toTo(CisOrderExecPlan.getCisOrderExecPlanById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOrderExecPlanTo createCisOrderExecPlan(String cisBaseApplyId, CisOrderExecPlanNto cisOrderExecPlanNto) {
        CisOrderExecPlan cisOrderExecPlan = new CisOrderExecPlan();
        cisOrderExecPlan = cisOrderExecPlan.create(cisBaseApplyId, cisOrderExecPlanNto, true);
        return CisOrderExecPlanAssembler.toTo(cisOrderExecPlan);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderExecPlan(String id, CisOrderExecPlanEto cisOrderExecPlanEto) {
        Optional<CisOrderExecPlan> cisOrderExecPlanOptional = CisOrderExecPlan.getCisOrderExecPlanById(id);
        cisOrderExecPlanOptional.ifPresent(cisOrderExecPlan -> cisOrderExecPlan.update(cisOrderExecPlanEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOrderExecPlan(String id) {
        Optional<CisOrderExecPlan> cisOrderExecPlanOptional = CisOrderExecPlan.getCisOrderExecPlanById(id);
        cisOrderExecPlanOptional.ifPresent(cisOrderExecPlan -> cisOrderExecPlan.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisBaseApplyTo getCisBaseApplyById(String id) {
        Optional<CisBaseApply> optional = CisBaseApply.getCisBaseApplyById(id);
        if (optional.isPresent()) {
            return CisBaseApplyAssembler.toTo(optional.get(), true);
        }
        return null;
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }

    /**
     * 根据访问代码获取CIS基础申请信息。
     * 此方法封装了从数据库中检索CIS基础申请信息的过程，并根据访问代码组装相关数据。
     * 它处理了两种特定类型的申请：DGIMG申请和SPCOBS申请，并为每种申请加载其详细的申请信息。
     *
     * @param visitCode 访问代码，用于检索特定的申请信息。
     * @return 返回一个包含CIS基础申请信息的列表。
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> getCisBaseApplyByVisitCode(String visitCode) {
        // 根据访问代码获取CIS基础申请信息，并将其转换为TO对象列表。
        List<CisBaseApplyTo> cisBaseApplyTos = CisBaseApplyAssembler.toTos(CisBaseApply.getCisBaseApplyByVisitCode(visitCode));

        setCharge(visitCode, cisBaseApplyTos);

        // 过滤出DGIMG类型的申请，并为每个DGIMG申请加载其详细信息。
        setDgimgApplyDetail(visitCode, cisBaseApplyTos);

        // 过滤出SPCOBS类型的申请，并为每个SPCOBS申请加载其详细信息。
        setSpcobsApplyDetail(visitCode, cisBaseApplyTos);

        setDrugApplyDetail(visitCode, cisBaseApplyTos);

        // 返回包含所有类型申请信息的列表。
        return cisBaseApplyTos;
    }

    /**
     * 根据访问代码为每个cis基础申请设置费用信息。
     * 此方法通过访问代码查找相关的费用申请信息，并将这些费用信息分配给对应的cis基础申请对象。
     *
     * @param visitCode       访问代码，用于查找相关的费用申请信息。
     * @param cisBaseApplyTos 一个包含cis基础申请对象的列表，这些对象需要被设置费用信息。
     */
    private void setCharge(String visitCode, List<CisBaseApplyTo> cisBaseApplyTos) {
        // 根据访问代码查找相关的费用申请信息。
        List<String> ids = cisBaseApplyTos.stream().map(CisBaseApplyTo::getId).toList();

        List<CisApplyCharge> cisApplyCharges = CisApplyCharge.findCisApplyChargesByVisitCode(visitCode, CisStatusEnum.NEW, ids);

        // 将费用申请信息按照cis基础申请ID进行分组，以便后续为每个cis基础申请设置费用信息。
        Map<String, List<CisApplyCharge>> map = cisApplyCharges.stream().collect(Collectors.groupingBy(CisApplyCharge::getApplyId));

        // 遍历cis基础申请列表，为每个cis基础申请设置相应的费用信息。
        cisBaseApplyTos.stream().forEach(p -> p.setCisApplyCharges(CisApplyChargeAssembler.toTos(map.get(p.getId()))));
    }

    /**
     * 根据访问代码和申请列表，设置DGIMG申请的详细信息。
     * 此方法通过访问代码查找相应的DGIMG申请详细信息，并将这些详细信息匹配到相应的申请对象中。
     *
     * @param visitCode       访问代码，用于查询DGIMG申请详细信息。
     * @param cisBaseApplyTos 申请对象列表，这些对象可能包含DGIMG申请的信息。
     */
    private void setDgimgApplyDetail(String visitCode, List<CisBaseApplyTo> cisBaseApplyTos) {
        // 过滤出所有CisDgimgApplyTo对象，这些对象是DGIMG申请的具体类型。
        var dgimgApplyTos = cisBaseApplyTos.stream().filter(CisDgimgApplyTo.class::isInstance).map(CisDgimgApplyTo.class::cast).toList();

        // 如果存在DGIMG申请对象，则进一步处理。
        if (!CollectionUtils.isEmpty(dgimgApplyTos)) {
            // 根据访问代码查询DGIMG申请详细信息，并按申请ID分组。
            Map<String, List<CisDgimgApplyDetail>> dgimgApplyDetailMap =
                    CisDgimgApplyDetail.findCisDgimgApplyDetailsByVisitCode(visitCode).stream()
                            .collect(Collectors.groupingBy(CisDgimgApplyDetail::getApplyId));

            // 遍历每个申请ID及其对应的详细信息列表。
            // 为每个DGIMG申请填充详细信息。
            dgimgApplyDetailMap.forEach((applyId, details) -> Optional.ofNullable(dgimgApplyTos.stream()
                            .filter(applyTo -> applyTo.getId().equals(applyId))
                            .findFirst()
                            .orElse(null))
                    .ifPresent(applyTo ->
                            // 使用组装器将详细的申请信息转换为合适的对象类型，并设置到申请对象中。
                            applyTo.setCisDgimgApplyDetails(CisDgimgApplyDetailAssembler.toTos(details))));
        }
    }

    /**
     * 根据访问代码和申请列表，设置SPCOBS申请的详细信息。
     * 此方法通过访问代码查找相应的SPCOBS申请详细信息，并将这些详细信息填充到相应的申请对象中。
     *
     * @param visitCode       访问代码，用于查询SPCOBS申请详细信息。
     * @param cisBaseApplyTos 申请对象列表，这些对象可能是多种类型的申请，其中包含SPCOBS申请。
     */
    private void setSpcobsApplyDetail(String visitCode, List<CisBaseApplyTo> cisBaseApplyTos) {
        // 过滤出所有SPCOBS类型的申请对象
        var spcobsApplyTos = cisBaseApplyTos.stream().filter(CisSpcobsApplyTo.class::isInstance).map(CisSpcobsApplyTo.class::cast).toList();

        // 如果有SPCOBS类型的申请对象
        if (!CollectionUtils.isEmpty(spcobsApplyTos)) {
            // 根据访问代码查询SPCOBS申请详细信息，并按申请ID分组
            // 根据访问代码查询SPCOBS申请详细信息，并按申请ID分组。
            Map<String, List<CisSpcobsApplyDetail>> spcobsApplyDetailMap =
                    CisSpcobsApplyDetail.findCisSpcobsApplyDetailsByVisitCode(visitCode).stream()
                            .collect(Collectors.groupingBy(CisSpcobsApplyDetail::getApplyId));

            // 遍历每个申请ID及其详细的SPCOBS申请信息
            // 为每个SPCOBS申请填充详细信息。
            spcobsApplyDetailMap.forEach((applyId, details) -> Optional.ofNullable(spcobsApplyTos.stream()
                            .filter(applyTo -> applyTo.getId().equals(applyId))
                            .findFirst()
                            .orElse(null))
                    .ifPresent(applyTo ->
                            // 使用组装器将详细的SPCOBS申请信息转换为合适的格式，并设置到申请对象中
                            applyTo.setCisSpcobsApplyDetails(CisSpcobsApplyDetailAssembler.toTos(details))));
        }
    }

    private void setDrugApplyDetail(String visitCode, List<CisBaseApplyTo> cisBaseApplyTos) {
        var drugApplyTos = cisBaseApplyTos.stream().filter(CisBaseDrugApplyTo.class::isInstance).map(CisBaseDrugApplyTo.class::cast).toList();
        if (!CollectionUtils.isEmpty(drugApplyTos)) {
            // 根据访问代码查询SPCOBS申请详细信息，并按申请ID分组。
            Map<String, List<CisDrugApplyDetail>> drugApplyDetailMap =
                    CisDrugApplyDetail.findCisDrugApplyDetailsByVisitCode(visitCode).stream()
                            .collect(Collectors.groupingBy(CisDrugApplyDetail::getApplyId));
            // 为每个SPCOBS申请填充详细信息。
            drugApplyDetailMap.forEach((applyId, details) ->
                    Optional.ofNullable(drugApplyTos.stream()
                                    .filter(applyTo -> applyTo.getId().equals(applyId))
                                    .findFirst()
                                    .orElse(null))
                            .ifPresent(applyTo ->
                                    applyTo.setCisDrugApplyDetails(CisDrugApplyDetailAssembler.toTos(details))));
        }
    }

    @Override
    public List<CisApplyChargeTo> getCisApplyCharges(String cisBaseApplyId, CisApplyChargeQto cisApplyChargeQto) {
        return CisApplyChargeAssembler.toTos(CisApplyCharge.getCisApplyCharges(cisBaseApplyId, cisApplyChargeQto));
    }

    @Override
    public List<CisApplyChargeTo> getCisApplyChargesByCisBaseApplyIds(List<String> cisBaseApplyIds) {
        return CisApplyChargeAssembler.toTos(CisApplyCharge.findCisApplyChargesByApplyIdIn(cisBaseApplyIds));
    }

    @Override
    public GridResultSet<CisApplyChargeTo> getCisApplyChargePage(String cisBaseApplyId, CisApplyChargeQto cisApplyChargeQto) {
        Page<CisApplyCharge> page = CisApplyCharge.getCisApplyChargePage(cisBaseApplyId, cisApplyChargeQto);
        Page<CisApplyChargeTo> result = page.map(CisApplyChargeAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional
    @HipIdempotent
    public CisApplyChargeTo createCisApplyCharge(String cisBaseApplyId, CisApplyChargeNto cisApplyChargeNto) {
        CisApplyCharge cisApplyCharge = new CisApplyCharge();
        cisApplyCharge = cisApplyCharge.create(cisBaseApplyId, cisApplyChargeNto, true);
        return CisApplyChargeAssembler.toTo(cisApplyCharge);
    }


    //region 申请单保存

    @Override
    public void updateCisApplyCharge(String id, CisApplyChargeEto cisApplyChargeEto) {
        Optional<CisApplyCharge> cisApplyCharge = CisApplyCharge.getCisApplyChargeById(id);
        cisApplyCharge.ifPresent(c -> c.update(cisApplyChargeEto));
    }

    /**
     * 创建CIS基础申请单。
     * <p>
     * 此方法用于批量创建CIS基础申请单。它首先验证申请单列表不为空，然后根据最小类别对申请单进行分组，
     * 最后对每个分组的申请单进行处理。
     *
     * @param entitys 申请单列表，不能为空。
     * @Transactional 注解确保此方法在一个事务中执行，如果发生异常则回滚事务。
     */
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, readOnly = false)
    public void createCisBaseApplys(List<CisBaseApplyNto> entitys) {
        // 验证申请单列表不为空且不为空列表
        BusinessAssert.notEmpty(entitys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单列表为空");

        try {
            //这里做个医嘱项目的校验。
//            List<String> serviceItemCodes = extractServiceItemCodes(entitys);

            ApplyCreateService applyCreateService = SpringUtil.getBean(ApplyCreateService.class);
            applyCreateService.clone(entitys);

        } catch (Exception e) {
            throw e; // 重新抛出异常以触发事务回滚
        }
    }
    //endregion

    /**
     * 保存申请单列表
     *
     * @param entitys 申请单实体列表，不能为空
     * @throws BusinessException 当申请单列表为空时抛出业务异常
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, readOnly = false)
    public List<CisBaseApplyTo> saveApplys(@RequestBody List<CisBaseApplyNto> entitys) {
        // 验证申请单列表不为空且不为空列表
        BusinessAssert.notEmpty(entitys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单列表为空");

        // 获取申请创建服务实例
        ApplyCreateService applyCreateService = SpringUtil.getBean(ApplyCreateService.class);

        // 调用申请创建服务的保存方法，保存申请单列表
        return CisBaseApplyAssembler.toTos(applyCreateService.save(entitys));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, readOnly = false)
    public List<CisBaseApplyTo> saveAndSubmitApplys(@RequestBody List<CisBaseApplyNto> entitys) {
        // 验证申请单列表不为空且不为空列表
        BusinessAssert.notEmpty(entitys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单列表为空");

        // 获取申请创建服务实例
        ApplyCreateService applyCreateService = SpringUtil.getBean(ApplyCreateService.class);

        // 调用申请创建服务的保存方法，保存申请单列表
        return CisBaseApplyAssembler.toTos(applyCreateService.saveAndSubmit(entitys));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisBaseApplyTo updateCisBaseApplyClone(CisBaseApplyEto entity, String baseApplyId) {

        ApplyUpdateService applyUpdateService = SpringUtil.getBean(ApplyUpdateService.class);
        applyUpdateService.cloneUpdate(entity, baseApplyId);

        Optional<CisBaseApply> applyOptional = CisBaseApply.getCisBaseApplyById(baseApplyId);
        return CisBaseApplyAssembler.toTo(applyOptional.get(), true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisBaseApplyTo updateCisBaseApply(CisBaseApplyEto entity, String baseApplyId) {
        ApplyUpdateService applyUpdateService = SpringUtil.getBean(ApplyUpdateService.class);
        return applyUpdateService.update(entity, baseApplyId);
    }

    //region 校对

    //回退

    /**
     * 备份CIS基础申请信息
     * <p>
     * 此方法用于将特定订单的CIS基础申请信息备份到安全位置
     * 它接收一个订单ID列表，对每个订单ID执行备份操作
     *
     * @param orderIds 订单ID列表，用于指定需要备份的订单
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void backUpCisBaseApply(List<String> orderIds) {
        // 获取ApplyUpdateService的实例，用于执行实际的备份操作
        ApplyUpdateService applyUpdateService = SpringUtil.getBean(ApplyUpdateService.class);
        // 调用ApplyUpdateService的备份方法，传递订单ID列表
        applyUpdateService.backUpCisBaseApply(orderIds);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void backUpCisBaseApplyOpd(List<String> prescriptionIds) {
        ApplyUpdateService applyUpdateService = SpringUtil.getBean(ApplyUpdateService.class);
        // 门诊申请单撤回方法，传递处方号列表
        applyUpdateService.backUpCisBaseApplyOpd(prescriptionIds);
    }

    /**
     * 更新申请单的处方ID
     * 此方法用于批量更新申请单中的处方ID，确保申请单信息与实际处方一致
     *
     * @param prescriptionIdMap 包含申请单ID和新处方ID映射的字典
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updatePrescriptionId(Map<String, String> prescriptionIdMap) {
        // 确保传入的映射不为空，如果为空则抛出异常
        BusinessAssert.notEmpty(prescriptionIdMap, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单修改");

        // 获取所有申请单ID
        List<String> ids = prescriptionIdMap.keySet().stream().toList();

        // 根据申请单ID列表查询对应的申请单对象
        List<CisBaseApply> cisBaseApplyList = CisBaseApply.findCisBaseAppliesByPrescriptionIds(ids);

        // 批量更新处方ID（假设支持批量更新）
        cisBaseApplyList.stream().filter(p -> prescriptionIdMap.containsKey(p.getPrescriptionID()))
                .forEach(p -> p.updatePrescriptionId(prescriptionIdMap.get(p.getPrescriptionID())));
    }

    /**
     * 根据医嘱ID列表批量更新处方ID
     * @param etos 包含医嘱ID和处方ID映射关系的对象列表，不可为空
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updatePrescriptionIdByOrderIds(List<CisBaseApplySplitGroupNewEto> etos) {
        // 确保传入的映射不为空，如果为空则抛出异常
        BusinessAssert.notEmpty(etos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "etos");

        // 获取所有医嘱ID
        List<String> ids = etos.stream().map(CisBaseApplySplitGroupNewEto::getOrderId).toList();

        // 根据申请单ID列表查询对应的申请单对象
        List<CisBaseApply> cisBaseApplyList = CisBaseApply.findCisBaseAppliesByOrderIDIn(ids);

        Map<String, String> prescriptionIdMap = etos.stream()
                                                    .collect(Collectors.toMap(CisBaseApplySplitGroupNewEto::getOrderId,
                                                            CisBaseApplySplitGroupNewEto::getPrescriptionID));

        // 批量更新处方ID
        cisBaseApplyList.stream().filter(p -> prescriptionIdMap.containsKey(p.getOrderID()))
                .forEach(p -> p.updatePrescriptionId(prescriptionIdMap.get(p.getOrderID())));
    }

    /**
     * 校验并确认cis基础申请单。
     * 该方法用于处理基于访问码和订单ID列表的cis基础申请单的校验和确认流程。
     * 它首先验证订单ID列表不为空，然后根据访问码查询相应的cis基础申请单，
     * 确保这些申请单的状态为克隆状态。接着，它对订单ID列表和查询到的申请单的订单ID进行比对，
     * 以确保所有订单ID都能匹配到相应的申请单。如果不匹配，则抛出异常。
     * 最后，它筛选出与订单ID列表匹配的申请单，为后续处理做准备。
     *
     * @param etos 访问码，用于查询cis基础申请单。
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> proofCisBaseApplys(List<CisSplitEto> etos) {

//        BusinessAssert.notEmpty(etos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "校对数据");
//
//        // 验证参数
//        TempprarysplitService tempprarysplitService = SpringUtil.getBean(TempprarysplitService.class);
//
//        Map<String, List<CisSplitEto>> map = etos.stream().collect(Collectors.groupingBy(CisSplitEto::getVisitCode));
//        List<CisBaseApplyTo> applyTos = new ArrayList<>();
//        map.forEach((k,v)->{
////            List<CisBaseApply> applies = getCisBaseApplysByVisitCode(k,CisStatusEnum.NEW);
//            applyTos.addAll(tempprarysplitService.splitCisBaseApplys(v,true));
//        });
//        return applyTos;
        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        return proofCisBaseApplys(etos, true);
    }

    /**
     * 处理校对申请
     *
     * @param etos 校对申请数据列表
     * @return 校对结果列表
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> proofApplys(@RequestBody List<CisSplitEto> etos) {
        // 校对数据不能为空
        BusinessAssert.notEmpty(etos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "校对数据");

        // 注入临时拆分服务
        TempprarysplitService tempprarysplitService = SpringUtil.getBean(TempprarysplitService.class);

        // 将校对申请数据按visitCode分组
        Map<String, List<CisSplitEto>> map = etos.stream().collect(Collectors.groupingBy(CisSplitEto::getVisitCode));
        List<CisBaseApplyTo> applyTos = new ArrayList<>();
        // 对每组数据执行拆分处理
        map.forEach((k, v) -> {
            applyTos.addAll(tempprarysplitService.splitApplys(v));
        });

        // 申请数量大于临时医嘱数量，校对数据存在长期医嘱。
        if (etos.size() > applyTos.size()) {
            // 查询部门护士下所有新的长期医嘱
            List<CisBaseApply> proofItem = CisBaseApply.findCisBaseAppliesByDeptNurseCodeAnAndStatusCode(
                            etos.get(0).getNurseDeptCode(), CisStatusEnum.NEW)
                    .stream().filter(p -> p.getOrderType().equals(OrderTypeEnum.LONG_TERM_ORDER)).toList();
            // 没有临时医嘱，长期医嘱全部校对
            if (CollectionUtils.isEmpty(applyTos)) {
                proofItem.forEach(CisBaseApply::proof2);
            } else {
                // 否则，将找到的长期医嘱转换后添加到拆分结果中
                applyTos.addAll(CisBaseApplyAssembler.toTos(proofItem));
            }
        }

        // 返回处理后的申请列表
        return applyTos;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public void saveProofExecPlans(List<CisProofNto> ntos) {
        TempprarysplitService tempprarysplitService = SpringUtil.getBean(TempprarysplitService.class);
        tempprarysplitService.saveProofExecPlan(ntos);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void saveSplitExecPlans(List<CisSplitNto> ntos) {
        LongTermSplitService longTermSplitService = SpringUtil.getBean(LongTermSplitService.class);
        var items = ntos.stream().filter(CisProofNto.class::isInstance).map(CisProofNto.class::cast).toList();
        longTermSplitService.saveProofExecPlan(items);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> proofCisBaseApplysNoSave(List<CisSplitEto> etos) {

//        BusinessAssert.notEmpty(etos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "校对数据");
//
//        // 验证参数
//        TempprarysplitService tempprarysplitService = SpringUtil.getBean(TempprarysplitService.class);
//
//        Map<String, List<CisSplitEto>> map = etos.stream().collect(Collectors.groupingBy(CisSplitEto::getVisitCode));
//        List<CisOrderExecPlanTo> planTos = new ArrayList<>();
//        map.forEach((k,v)->{
////            List<CisBaseApply> applies = getCisBaseApplysByVisitCode(k,CisStatusEnum.NEW);
//            planTos.addAll(tempprarysplitService.splitCisBaseApplys(v,false));
//        });
//        return planTos;
        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        return proofCisBaseApplys(etos, false);
    }

    /**
     * 校对CIS基础申请单
     *
     * @param etos 待校对的CIS分割ETO列表，不能为空
     * @param save 是否保存校对结果
     * @return 返回校对后的CIS基础申请TO列表
     * <p>
     * 该方法主要用于对CIS基础申请单进行校对处理它首先确保输入列表不为空，
     * 然后根据访问码对ETO进行分组，接着调用临时分割服务对每个分组进行处理，
     * 最后返回处理后的申请TO列表
     */
    private List<CisBaseApplyTo> proofCisBaseApplys(List<CisSplitEto> etos, Boolean save) {

        // 确保输入的ETO列表不为空，否则抛出异常
        BusinessAssert.notEmpty(etos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "校对数据");

        // 验证参数
        TempprarysplitService tempprarysplitService = SpringUtil.getBean(TempprarysplitService.class);

        // 根据访问码对ETO列表进行分组
        Map<String, List<CisSplitEto>> map = etos.stream().collect(Collectors.groupingBy(CisSplitEto::getVisitCode));
        List<CisBaseApplyTo> applyTos = new ArrayList<>();
        // 遍历分组后的ETO列表，并进行校对处理
        map.forEach((k, v) -> {
            //            List<CisBaseApply> applies = getCisBaseApplysByVisitCode(k,CisStatusEnum.NEW);
            // 将校对结果添加到返回列表中
            applyTos.addAll(tempprarysplitService.splitCisBaseApplysProof(v, save));
        });
        return applyTos;
        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
    }


    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> splitCisBaseApplys(List<CisSplitEto> etos) {

        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        return splitCisBaseApplysProof(etos, true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> splitCisBaseApplysNoSave(List<CisSplitEto> etos) {

        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        return splitCisBaseApplysProof(etos, false);
    }

    private List<CisBaseApplyTo> splitCisBaseApplys(List<CisSplitEto> etos, Boolean save) {
        BusinessAssert.notEmpty(etos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "拆分数据");
        Map<String, List<CisSplitEto>> map = etos.stream().collect(Collectors.groupingBy(CisSplitEto::getVisitCode));
        List<CisBaseApplyTo> applyTos = new ArrayList<>();

        LongTermSplitService longTermSplitService = SpringUtil.getBean(LongTermSplitService.class);
        map.forEach((k, v) -> {
//            List<String> orderIds = v.stream().map(v1->v1.getOrderId()).toList();
//            //创建新的申请单和申请单费用
//            List<CisBaseApply> applies = createcisBaseApplysNewByOrderId(k,orderIds);

            applyTos.addAll(longTermSplitService.splitCisBaseApplys(v, save));
        });
        return applyTos;
        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
    }

    //endregion

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> splitCisBaseApplysNoSaveProof(List<CisSplitEto> etos) {

        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        return splitCisBaseApplysProof(etos, false);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> splitCisBaseApplysSaveProof(List<CisSplitEto> etos) {

        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        return splitCisBaseApplysProof(etos, true);
    }

    private List<CisBaseApplyTo> splitCisBaseApplysProof(List<CisSplitEto> etos, Boolean save) {
        BusinessAssert.notEmpty(etos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "拆分数据");
        Map<String, List<CisSplitEto>> map = etos.stream().collect(Collectors.groupingBy(CisSplitEto::getVisitCode));
        List<CisBaseApplyTo> applyTos = new ArrayList<>();

        LongTermSplitService longTermSplitService = SpringUtil.getBean(LongTermSplitService.class);
        map.forEach((k, v) -> {
//            List<String> orderIds = v.stream().map(v1->v1.getOrderId()).toList();
//            //创建新的申请单和申请单费用
//            List<CisBaseApply> applies = createcisBaseApplysNewByOrderId(k,orderIds);

            applyTos.addAll(longTermSplitService.splitCisBaseApplysProof(v, save));
        });
        return applyTos;
        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void splitCisBaseApplysSave(List<CisBaseApplyNto> ntos) {
        saveProofAndSplitCisBaseApplies(ntos);
    }

    /**
     * 提交申请单,从clone状态申请单查询
     *
     * @param visitCode
     * @param orderIds
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void submitCisBaseApplys(String visitCode, List<String> orderIds) {
        ApplySubmitService applySubmitService = SpringUtil.getBean(ApplySubmitService.class);
        applySubmitService.submit(visitCode, orderIds);
    }

    /**
     * 提交门诊申请单并处理相关逻辑
     * <p>
     * 该方法使用了@Override注解，表明它重写了父类或接口的方法
     *
     * @param visitCode 就诊编号，用于关联相关的申请单
     * @param orderIds  订单ID列表，标识需要提交的申请单
     * @Transactional注解用于声明该方法需要事务管理，rollbackFor参数指定了需要回滚的异常类型， readOnly参数表明该操作是否会修改数据库
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> submitOpdCisBaseApplys(String visitCode, List<String> orderIds) {
        // 获取ApplySubmitService服务实例，用于提交申请
        ApplySubmitService applySubmitService = SpringUtil.getBean(ApplySubmitService.class);

        // 提交申请并获取提交结果列表
        List<CisBaseApply> applies = applySubmitService.submit(visitCode, orderIds);

        // 将提交结果转换为CisSplitEto对象列表，便于后续处理
        List<CisSplitEto> etos = applies.stream()
                .map(p -> new CisSplitEto(p.getVisitCode(), p.getOrderID()))
                .flatMap(Stream::ofNullable)
                .collect(Collectors.toList());

        // 对转换后的申请单进行验证或进一步处理
        return proofCisBaseApplys(etos);
    }

    //region 停止校对

    /**
     * 提交申请单
     *
     * @param visitCode
     * @param orderIds
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void submitApplys(@RequestParam String visitCode, @RequestParam List<String> orderIds) {
        ApplySubmitService applySubmitService = SpringUtil.getBean(ApplySubmitService.class);
        applySubmitService.submit2(visitCode, orderIds);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> getProofStopCisBaseApplyByOrderIds(List<CisProofQto> qtos) {
        return applyProofService.getProofStopCisBaseApplyByOrderIds(qtos);
    }
    //endregion

    //申请单的停止校对。
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void proofStop(List<String> orderIds) {
//        executeService.proofStopExecPlans(etos);
        BusinessAssert.notEmpty(orderIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "orderIds");
        List<CisBaseApply> applies = CisBaseApply.findCisBaseAppliesByOrderIDIn(orderIds);
        applies.forEach(a -> a.stop());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void execOrderPlan(List<CisProofEto> etos) {
        executeService.exexPlansExecute(etos);
    }

    //取消执行
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void cancelPlansExecute(List<CisProofEto> etos) {
        executeService.cancelPlansExecute(etos);
    }

    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void noExecPlansExecute(List<CisProofEto> etos) {
        executeService.noExecPlansExecute(etos);
    }

    //region 作废相关
    //预作废操作
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void preObsolete(String orderid) {
        applyProofService.preObsolete(orderid);
    }

    //endregion

    //作废校对
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void proofObsolete(String deptNurseCode, List<String> orderIds) {
        BusinessAssert.notEmpty(orderIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "医嘱号");
        BusinessAssert.hasText(deptNurseCode, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "护理组号");
        List<CisBaseApply> applies = CisBaseApply.findCisBaseAppliesByOrderIDIn(orderIds);

        applyProofService.proofObsolete(applies, orderIds);
    }

    // 非拆分部分的补费
    @Override
    // 使用事务管理，确保数据一致性
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    // 使用分布式系统中的一致性机制，防止重复提交
    @HipIdempotent
    public CisBaseApplyTo addCisApplyCharge(@PathVariable("id") String orderid, @RequestBody CisApplyChargeNto cisApplyChargeNto) {
        // 根据订单ID查询相关的申请单
        List<CisBaseApply> cisBaseApplies = CisBaseApply.findCisBaseAppliesByOrderIDIn(Arrays.asList(orderid));
        // 确保查询到的申请单不为空
        BusinessAssert.notEmpty(cisBaseApplies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0006, "医嘱号为：" + orderid);
        // 寻找状态为克隆的申请单
        CisBaseApply cisBaseApply = cisBaseApplies.stream().filter(p -> CisStatusEnum.CLONE.equals(p.getStatusCode())).findFirst().orElse(null);
        // 确保找到的申请单不为空
        BusinessAssert.notNull(cisBaseApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0006, "医嘱号为：" + orderid);
        // 创建一个新的申请收费对象
        CisApplyCharge cisApplyCharge = new CisApplyCharge();
        // 设置申请收费的状态为克隆
        cisApplyChargeNto.setStatusCode(CisStatusEnum.CLONE);
        // 创建申请收费记录
        cisApplyCharge.create(cisBaseApply.getId(), cisApplyChargeNto, true);
        // 将申请单转换为传输对象并返回
        return CisBaseApplyAssembler.toTo(cisBaseApply, true);
    }


    /**
     * 门诊签发后的医嘱补费，签发且已经计费，则创建新的执行单，未计费，费用按照执行科室添加到执行单中
     *
     * @param id
     * @param cisApplyChargeNtos
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    @HipIdempotent
    public CisBaseApplyTo repairOpdFeeCharge(String id, List<CisApplyChargeNto> cisApplyChargeNtos) {
        CisBaseApply cisBaseApply = CisBaseApply.getCisBaseApplyById(id).orElse(null);
        BusinessAssert.notNull(cisBaseApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单");

        CisBaseApplyTo to = CisBaseApplyAssembler.toTo(cisBaseApply, true);

        if(CisStatusEnum.NEW.equals(to.getStatusCode()) ){
            cisApplyChargeNtos.stream().forEach(p ->
                    new CisApplyCharge().create(id, p, true)
            );
        }
        else {
            boolean tolled = isAnyExecPlanCharged(to);
            List<CisOrderExecPlanChargeNto> orderExecPlanChargeNtos = getOrderExecPlanChargeNtos(id, cisApplyChargeNtos);

            if (CollectionUtils.isEmpty(orderExecPlanChargeNtos)) {
                return to;
            }

            if (tolled) {
                repairFeeInNewTransaction(HIPIDUtil.getNextIdString(), orderExecPlanChargeNtos);
            } else {
                addChargesToApplyAndExecPlan(cisBaseApply, to, cisApplyChargeNtos,orderExecPlanChargeNtos);
            }
        }

        return CisBaseApplyAssembler.toTo(CisBaseApply.getCisBaseApplyById(id).orElse(null), true);
    }

    private boolean isAnyExecPlanCharged(CisBaseApplyTo to) {
        return to.getCisOrderExecPlans().stream().anyMatch(p -> p.getSeltFlag());
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void repairFeeInNewTransaction(String execPlanId, List<CisOrderExecPlanChargeNto> chargeNtos) {
        CisOrderExecPlanService execPlanService = SpringUtil.getBean(CisOrderExecPlanService.class);
        execPlanService.repairFeeCisOrderExecPlans(execPlanId, chargeNtos);
    }

    private void addChargesToApplyAndExecPlan(CisBaseApply cisBaseApply, CisBaseApplyTo to,List<CisApplyChargeNto> cisApplyChargeNtos,
                                              List<CisOrderExecPlanChargeNto> chargeNtos) {
        // 添加申请单费用
        CisApplyCharge cisApplyCharge = new CisApplyCharge();
        cisApplyChargeNtos.forEach(p -> cisApplyCharge.create(cisBaseApply.getId(), p, true));

        // 按机构分组
        Map<String, List<CisOrderExecPlanChargeNto>> grouped = chargeNtos.stream()
                .collect(Collectors.groupingBy(CisOrderExecPlanChargeNto::getExecuteOrgCode));

        grouped.forEach((orgCode, charges) -> {
            CisOrderExecPlanTo existingPlan = findExistingPlan(to, orgCode);
            if (existingPlan == null) {
                createNewExecPlan(cisBaseApply, charges);
            } else {
                addChargesToExistingPlan(existingPlan.getId(), charges);
            }
        });
    }

    private CisOrderExecPlanTo findExistingPlan(CisBaseApplyTo to, String orgCode) {
        return to.getCisOrderExecPlans().stream()
                .filter(p -> p.getExecOrgCode().equals(orgCode))
                .findFirst()
                .orElse(null);
    }

    private void createNewExecPlan(CisBaseApply apply, List<CisOrderExecPlanChargeNto> charges) {
        CisOrderExecPlanNto planNto = buildOrderExecPlanNto(apply, charges);
        CisOrderExecPlan cisOrderExecPlan = new CisOrderExecPlan();
        cisOrderExecPlan.create(apply.getId(), planNto, true);
    }

    /**
     * 构建执行计划对象
     *
     * @param apply 申请单对象
     * @param charges 费用列表
     * @return 执行计划对象
     */
    private CisOrderExecPlanNto buildOrderExecPlanNto(CisBaseApply apply, List<CisOrderExecPlanChargeNto> charges) {

        CisOrderExecPlanNto nto = new CisOrderExecPlanNto();
        nto.setOrderClass(apply.getSystemType());
        nto.setVisitCode(apply.getVisitCode());
        nto.setPatMiCode(apply.getPatMiCode());
        nto.setOrgCode(apply.getVisitOrgCode());
        nto.setOrgName(apply.getVisitOrgName());
        nto.setDeptNurseCode(apply.getDeptNurseCode());
        nto.setOrderId(apply.getOrderID());
        nto.setSortNo(apply.getSortNo());
        nto.setServiceItemCode(apply.getServiceItemCode());
        nto.setServiceItemName(apply.getServiceItemName());

        nto.setNum(apply.getNum());
        CisOrderExecPlanChargeNto firstCharge = charges.get(0);
        if (firstCharge != null) {
            nto.setExecOrgCode(firstCharge.getExecuteOrgCode());
            nto.setExecOrgName(firstCharge.getExecuteOrgName());
        }
        nto.setOrgName(apply.getVisitOrgName());
        nto.setHeldStaff(apply.getCreatedStaff());
        nto.setHeldStaffName(apply.getCreatedStaffName());
        return nto;
    }

    private void addChargesToExistingPlan(String planId, List<CisOrderExecPlanChargeNto> charges) {
        new CisOrderExecPlanCharge().create(planId, charges, true);
    }

    private List<CisOrderExecPlanChargeNto> getOrderExecPlanChargeNtos(String id, List<CisApplyChargeNto> cisApplyChargeNtos) {
        return cisApplyChargeNtos.stream().map(p -> {
            CisOrderExecPlanChargeNto cisOrderExecPlanChargeNto = new CisOrderExecPlanChargeNto();
            cisOrderExecPlanChargeNto.setCisBaseApplyId(id);
            cisOrderExecPlanChargeNto.setExecuteOrgCode(p.getExecuteOrgCode());
            cisOrderExecPlanChargeNto.setPriceItemCode(p.getPriceItemCode());
            cisOrderExecPlanChargeNto.setPriceItemName(p.getPriceItemName());
            cisOrderExecPlanChargeNto.setPackageSpec(p.getPackageSpec());
            cisOrderExecPlanChargeNto.setPrice(p.getPrice());
            cisOrderExecPlanChargeNto.setUnit(p.getUnit());
            cisOrderExecPlanChargeNto.setNum(p.getNum());
            cisOrderExecPlanChargeNto.setIsFixed(p.getIsFixed());
            cisOrderExecPlanChargeNto.setLimitConformFlag(p.getLimitConformFlag());
            cisOrderExecPlanChargeNto.setSystemItemClass(p.getSystemItemClass());
            cisOrderExecPlanChargeNto.setChargeType(p.getChargeType());
            cisOrderExecPlanChargeNto.setExecuteOrgName(p.getExecuteOrgName());
            cisOrderExecPlanChargeNto.setUnitName(p.getUnitName());
            return cisOrderExecPlanChargeNto;
        }).toList();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisBaseApplyTo> findCisApplyByOrderId(@RequestParam String orderId) {
        return CisBaseApplyAssembler.toTos(CisBaseApply.findCisBaseAppliesByOrderIDIn(Arrays.asList(orderId)), true);
    }

    //region 校对医嘱校验后数据保存。搭配之前的接口使用。
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void proofCisBaseApplysSave(List<CisBaseApplyNto> proofCisBaseApplyNtos) {
        saveProofAndSplitCisBaseApplies(proofCisBaseApplyNtos);
    }

    /**
     * 保存证明并拆分CisBaseApply记录
     * 该方法的目的是将给定的实体列表根据minimal_class字段进行分组，
     * 然后保存每个分组中的活动CisBaseApply记录
     *
     * @param entities 一个包含CisBaseApplyNto对象的列表，可能包含null值
     */
    private void saveProofAndSplitCisBaseApplies(List<CisBaseApplyNto> entities) {
        // 将实体列表转换为流，过滤掉可能的null值，并根据minimal_class字段进行分组
        Map<String, List<CisBaseApplyNto>> maps = entities.stream()
                .flatMap(Stream::ofNullable)
                .collect(Collectors.groupingBy(CisBaseApplyNto::getAll_class));

        // 遍历分组后的地图，对每个分组的CisBaseApply记录进行保存
        // 数据 插入保存。
        maps.forEach((k, v) -> {
            saveActiveCisBaseApplies(v);
        });
    }
    //endregion

    /**
     * 保存活动的Cis基础申请信息
     * 此方法用于处理和保存处于活动状态的基础申请列表每个实体将被转换并保存为活动状态的基础申请
     *
     * @param entities Cis基础申请的列表，表示待处理的申请实体
     */
    private void saveActiveCisBaseApplies(List<CisBaseApplyNto> entities) {
        // 根据第一个实体创建一个新的Cis基础申请实例，并设置其状态为活动
        CisBaseApply cisBaseApply = CisBaseApply.newInstanceByNto(entities.get(0));
        cisBaseApply.setStatusCode(CisStatusEnum.ACTIVE);

        // 遍历实体列表，对每个实体执行创建和验证操作
        entities.forEach(entity -> {
            // 创建基础申请实例，参数true表示强制创建，即使实体已存在
            cisBaseApply.createProof(entity, true);
            // 对当前基础申请进行验证
//            cisBaseApply.proof2();
        });
        // 更新申请单状态
        updateCisBaseAppliesStatus(entities.stream().map(CisBaseApplyNto::getId).collect(Collectors.toList()));
    }

    private void updateCisBaseAppliesStatus(List<String> applyIds) {
        if (!CollectionUtils.isEmpty(applyIds)) {
            List<CisBaseApply> cisBaseApplys = CisBaseApply.findCisBaseAppliesByApplyIDIn(applyIds);
            cisBaseApplys.forEach(p -> p.proof2());
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void withdraw(String id) {
        Optional<CisBaseApply> cisBaseApply = CisBaseApply.getCisBaseApplyById(id);
        cisBaseApply.ifPresent(p -> p.withdraw());
    }

//    @Override
//    @Transactional(rollbackFor = Throwable.class, readOnly = false)
//    public void test(String id){
//        PatIndexTo p = patIndexService.getPatIndexById(id);
//
//    }

    /**
     * 根据订单ID列表查询基础申请信息
     * 此方法使用了事务管理，确保在查询过程中遇到异常时能够回滚事务
     * 事务设置为只读，以优化性能，因为它仅用于查询操作
     *
     * @param orderIds 订单ID列表，用于查询与这些订单ID关联的基础申请信息
     * @return 返回一个CisBaseApplyTo对象列表，每个对象代表与提供的订单ID匹配的基础申请信息
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisBaseApplyTo> findCisBaseApplyByOrderIds(List<String> orderIds) {
        // 使用CisBaseApplyAssembler将查询结果转换为CisBaseApplyTo对象列表，并返回
        // 参数true表示需要转换为DTO对象
        return CisBaseApplyAssembler.toTos(CisBaseApply.findCisBaseAppliesByOrderIDIn(orderIds), true);
    }

    /**
     * 根据创建时间获取申请信息
     *
     * @param date
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisBaseApplyTo> findCisBaseApplyByCreateDate(LocalDateTime date) {
        return CisBaseApplyAssembler.toTos(CisBaseApply.findCisBaseApplyByCreatedDateAfter(date), false);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<String> queryOrderIdsByDiagCode(String visitCode, String diagCode) {
        return CisBaseApply.queryOrderIdsByDiagCode(visitCode, diagCode);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<String> queryOrderIdsByDiagCodeWithTreatmentCode(String treatmentCode, String diagCode) {
        return CisBaseApply.queryOrderIdsByDiagCodeWithTreatmentCode(treatmentCode, diagCode);
    }

    /**
     * 根据订单ID列表查找基础申请和预订信息
     * 此方法首先根据提供的订单ID列表获取相关的基础申请信息，然后获取与这些申请相关的预订信息，并将两者关联起来
     * 使用了事务管理，确保在出现异常时能够回滚事务，同时以只读方式执行，提高性能
     *
     * @param orderIds 订单ID列表，用于查找基础申请和预订信息
     * @return 返回一个包含基础申请及其对应预订信息的列表如果输入列表为空或未找到相关信息，则返回空列表
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisBaseApplyTo> findCisBaseApplyAndBookingByOrderIds(List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }

        List<CisBaseApplyTo> applys = CisBaseApplyAssembler.toTos(
                CisBaseApply.findCisBaseAppliesByOrderIDIn(orderIds),
                false
        );

        if (!CollectionUtils.isEmpty(applys)) {
            enrichWithExecutionPlanInfo(applys, orderIds);
        }
        return applys;
    }

    /**
     * 使用执行计划信息丰富申请信息
     */
    private void enrichWithExecutionPlanInfo(List<CisBaseApplyTo> applys, List<String> orderIds) {
        CisOrderExecPlanService execPlanService = SpringUtil.getBean(CisOrderExecPlanService.class);
        List<CisOrderExecPlanTo> execPlans = execPlanService.findCisOrderExecPlanInOrderIds(orderIds);

        Map<String, List<CisOrderExecPlanTo>> execPlanMap = execPlans.stream()
                .collect(Collectors.groupingBy(CisOrderExecPlanTo::getCisBaseApplyId));

        applys.forEach(apply -> {
            execPlanMap.getOrDefault(apply.getId(), Collections.emptyList())
                    .stream()
                    .findFirst()
                    .ifPresent(plan -> apply.setThridStatus(plan.getThridStatus()));
        });
    }


    /**
     * 根据就诊码查找基础申请信息
     * 此方法使用了事务管理，任何异常都会导致事务回滚，并且只读事务保证了数据的一致性
     *
     * @param visitCode  就诊码，用于查询基础申请信息
     * @param orderClass 订单类别枚举，用于限定查询范围
     * @return 返回匹配的CisBaseApplyTo对象列表
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisBaseApplyTo> findCisBaseApplyByVisitCode(String visitCode, SystemTypeEnum orderClass) {
        // 创建查询传输对象并设置查询条件
        CisBaseApplyQto qto = new CisBaseApplyQto();
        qto.setVisitCode(visitCode);
        qto.setOrderClass(orderClass.getCode());

        // 使用流处理过滤掉状态码为OBSOLETE的记录，并将结果转换为CisBaseApplyTo对象列表
        return CisBaseApplyAssembler.toTos(CisBaseApply.getCisBaseApplies(qto).stream()
                        .filter(p -> !CisStatusEnum.OBSOLETE.equals(qto.getStatusCode()))
                        .toList()
                , false);
    }

    /**
     * 定制给医嘱单打印使用
     *
     * @param orderIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisBaseApplyCustomTo> queryCisBaseApplyByOrderIds(List<String> orderIds) {
        List<CisBaseApply> applys = CisBaseApply.findCisBaseAppliesByOrderIDIn(orderIds);
        Map<String, CisBaseApply> applyMap = applys.stream()
                .collect(Collectors.toMap(CisBaseApply::getId, apply -> apply));

        List<String> detailApplyIds = applys.stream()
                .filter(ApplyWithDetial.class::isInstance)
                .map(CisBaseApply::getId)
                .toList();

        List<CisBaseApplyCustomTo> result = cisApplyCustomService.extractedDetail(applys, applyMap, detailApplyIds);

        // 处理普通申请数据
        applys.stream()
                .filter(apply -> !detailApplyIds.contains(apply.getId()))
                .map(apply -> CisBaseApplyCustomAssembler.toTo(apply, null))
                .forEach(result::add);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisOrderExecPlanMtTo> findCisOrderExecPlanMtPages(@ParameterObject @SpringQueryMap CisOrderExecPlanMtQto qto) {
        GridResultSet<CisOrderExecPlanMtTo> resultSet = new GridResultSet<>();
        // 查询指定状态、时间和执行科室的执行计划
        List<CisOrderExecPlanTo> orderExecPlanToList = CisOrderExecPlanAssembler.toTos(CisOrderExecPlan.getCisOrderExecPlanWithRange(qto), true);
        if (CollectionUtils.isEmpty(orderExecPlanToList)) return resultSet;
        Map<String, CisOrderExecPlanTo> orderExecPlanMap = orderExecPlanToList.stream().collect(Collectors.toMap(CisOrderExecPlanTo::getCisBaseApplyId, p -> p));
        List<String> applyIds = orderExecPlanMap.keySet().stream().distinct().collect(Collectors.toList());
        CisBaseApplyQto applyQto = new CisBaseApplyQto();
        applyQto.setIds(applyIds);
        applyQto.setSortBy(qto.getSortBy());
        applyQto.setSortOrder(qto.getSortOrder());
        applyQto.setPageSize(qto.getPageSize());
        applyQto.setPageNum(qto.getPageNum());
        Page<CisBaseApply> result = CisBaseApply.getCisBaseApplyByIdsPage(applyQto);
        //Page<CisBaseApplyTo> result = cisBaseApplyByIdsPage.map(CisBaseApplyAssembler::toTo);
        if (CollectionUtils.isEmpty(result.getContent())) return resultSet;
        // 转换为目标对象
        List<CisOrderExecPlanMtTo> mtToList = result.getContent().stream()
                .map(cisBaseApply -> {
                    CisOrderExecPlanMtTo mtTo = HIPBeanUtil.copy(orderExecPlanMap.get(cisBaseApply.getId()), CisOrderExecPlanMtTo.class);
                    mtTo.setCisBaseApplyId(cisBaseApply.getId());
                    mtTo.setCreateDateTime(cisBaseApply.getCreatedDate());
                    String type = "";
                    if (cisBaseApply instanceof CisDgimgApply dgimgApply) {
                        type = dgimgApply.getDeviceTypeName();
                        if (StringUtils.isBlank(dgimgApply.getDeviceTypeName())) {
                            DictElementTo deviceType = dictElementService.getCustomDictElement("DgimgDeviceType", dgimgApply.getDeviceType());
                            if (deviceType != null) {
                                type = deviceType.getElementName();
                            }
                        }
                    } else if (cisBaseApply instanceof CisSpcobsApply spcobsApply) {
                        type = spcobsApply.getSpecimanName();
                        if (StringUtils.isBlank(spcobsApply.getSpecimanName())) {
                            DictElementTo speciman = dictElementService.getCustomDictElement("Speciman", spcobsApply.getSpeciman());
                            if (speciman != null) {
                                type = speciman.getElementName();
                            }
                        }
                    }
                    mtTo.setType(type);
                    return mtTo;
                }).collect(Collectors.toList());
        resultSet.setResult(mtToList);
        resultSet.setPageNum(result.getNumber());
        resultSet.setPageSize(result.getSize());
        resultSet.setTotal(result.getTotalElements());
        return resultSet;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisBaseApplyTo> findCisBaseApplyByApplyIds(List<String> applyIds) {
        return CisBaseApplyAssembler.toTos(CisBaseApply.findCisBaseAppliesByApplyIDIn(applyIds), false);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<ApplyDiagnosisTo> findCisDiaggnosisByVisitCode(String visitCode){
        return ApplyDiagnosisAssembler.toTos(ApplyDiagnosis.findApplyDiagnosisByVisitCode(visitCode));
    }

}
