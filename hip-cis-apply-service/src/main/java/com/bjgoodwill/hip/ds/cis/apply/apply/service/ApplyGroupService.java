package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisBaseApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.*;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.ApplyWithDetial;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.BaseDetail;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailEto;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailNto;
import com.bjgoodwill.hip.ds.cis.apply.mq.send.CisApplyMqSend;
import com.bjgoodwill.hip.ds.cis.apply.proxy.ApplyCodeServiceProxy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-24 09:50
 * @className: ApplyGroupService
 * @description:
 **/
@Component
public class ApplyGroupService {

    @Autowired
    private CisApplyMqSend cisApplyMqSend;
    @Autowired
    private ApplyCodeServiceProxy applyCodeServiceProxy;

    //新拆组接口
    public List<CisBaseApplyTo> splitApplyGroup(CisBaseApplySplitGroupNto cisBaseApplyGroupNto) {
        // 1 创建新的申请单 转移明细，转移明细的费用
        //拆出前的申请单
        String id = cisBaseApplyGroupNto.getSourceApplyId();
        CisBaseApply sourceApply = CisBaseApply.getCisBaseApplyById(id).orElse(null);
        BusinessAssert.notNull(sourceApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");

        return cisBaseApplyGroupNto.getDetails().stream().map(v -> {
            v.setId(applyCodeServiceProxy.getApplyNextCode());
            List<String> detailIds = v.getDetails().stream().map(p -> ((DetailNto) p).getId()).toList();
            return CisBaseApplyAssembler.toTo(((ApplyWithDetial) CisBaseApply.newInstanceByNto(v)).splitGroup(v, detailIds));
            //region 修改申请单和医嘱的服务项目编码
//            cisApplyMqSend.OrderServiceCodeSend(newApply.getOrderID(), newApply.getServiceItemCode());
//            String serviceItemCode = ((ApplyWithDetial) sourceApply).getServiceItemCode();
//            sourceApply.setServiceItemCode(serviceItemCode);
//            cisApplyMqSend.OrderServiceCodeSend(sourceApply.getOrderID(), serviceItemCode);
            //endregion
        }).toList();
    }


    /**
     * 合并申请组
     * 该方法用于处理申请合并操作，将多个详情申请合并到一个主申请中
     *
     * @param cisBaseApplyMergeGroupEto 包含合并申请所需信息的数据传输对象
     */
    public CisBaseApplyTo mergeApplyGroup(CisBaseApplyMergeGroupEto cisBaseApplyMergeGroupEto) {
        // 获取源申请对象
        CisBaseApply cisBaseApply = CisBaseApply.getCisBaseApplyById(cisBaseApplyMergeGroupEto.getSourceApplyId()).orElse(null);

        // 检查源申请是否存在且为详细申请类型
        if (cisBaseApply != null && cisBaseApply instanceof ApplyWithDetial) {
            ((ApplyWithDetial) cisBaseApply).mergeGroup(cisBaseApply.getOrderID(), cisBaseApply.getId(), cisBaseApplyMergeGroupEto.getDetailEtos());
        }

        List<DetailEto> detailEtos = cisBaseApplyMergeGroupEto.getDetailEtos().values().stream()
                .map(o -> o.getDetailEtos()).flatMap(List::stream).toList();
        // 获取合并后的服务项代码
        String serviceItemCode = cisBaseApply.getServiceItemCode() + "," + ((ApplyWithDetial) cisBaseApply).getServiceCodeByEto(detailEtos);
        serviceItemCode = StringUtils.join(Arrays.stream(serviceItemCode.split(",")).filter(s -> !s.isEmpty())
                .distinct().toArray(), ",");
        // 更新源申请的服务项代码
        cisBaseApply.updateServiceItemCode(serviceItemCode);
        // 发送合并后的申请信息到消息队列
        cisApplyMqSend.OrderServiceCodeSend(cisBaseApplyMergeGroupEto.getSourceApplyId(), serviceItemCode);

        return CisBaseApplyAssembler.toTo(cisBaseApply);
    }

    /**
     * 合并申请单
     *
     * @param applyIds 申请单ID列表，用于标识需要合并的申请单
     * @return 返回合并后的申请单对象，如果无法合并或输入无效则返回null
     */
    public CisBaseApplyTo mergeApplyGroupNew(List<String> applyIds) {
        BusinessAssert.notEmpty(applyIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "applyIds");

        List<CisBaseApply> applies = CisBaseApply.findCisBaseAppliesByApplyIDIn(applyIds);

        BusinessAssert.notEmpty(applies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");
        BusinessAssert.isTrue(applies.get(0) instanceof ApplyWithDetial, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0005, "申请单");

        // 过滤并转换申请单列表为详细申请单列表
        List<ApplyWithDetial> applyList = applies.stream()
                .filter(ApplyWithDetial.class::isInstance)
                .map(ApplyWithDetial.class::cast)
                .toList();

        if (applyList.isEmpty()) {
            return null;
        }

        ApplyWithDetial mainApply = applyList.get(0).getMergeMain(applyList);
        String orderId = mainApply.getOrderID();
        String applyCode = mainApply.getId();

        // 执行合并删除操作
        applyList.stream()
                .filter(p -> !p.equals(mainApply))
                .forEach(e -> e.mergeDelete(orderId, applyCode));

        // 合并服务项代码
        String serviceItemCode = mergeServiceItemCodes(applyList);
        BusinessAssert.hasText(serviceItemCode, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0005, "申请单服务编码");

        mainApply.updateServiceItemCode(serviceItemCode);

        // 发送MQ消息
        cisApplyMqSend.OrderServiceCodeSend(applyCode, serviceItemCode);

        // 获取合并后的申请单
        CisBaseApply mergedApply = ApplyWithDetial.getCisBaseApplyById(applyCode).orElse(null);
        return CisBaseApplyAssembler.toTo(mergedApply, true);
    }

    // 提取为独立方法，便于测试和复用
    private String mergeServiceItemCodes(List<ApplyWithDetial> applyList) {
        return StringUtils.join(
                applyList.stream()
                        .map(p -> p.getServiceItemCode() == null ? new String[0] : p.getServiceItemCode().split(","))
                        .flatMap(Arrays::stream)
                        .distinct()
                        .toArray(),
                ","
        );
    }


    /**
     * 拆申请单
     *
     * @param id 申请单ID，用于标识需要拆分的申请
     * @return 拆分后的申请单列表，如果无法拆分则返回null
     */
    public List<CisBaseApplyTo> splitApplyGroupNew(String id, CisBaseApplySplitGroupNewNtoLst cisBaseApplySplitGroupNewNtoLst) {
        // 获取指定ID的申请单对象
        CisBaseApply cisBaseApply = CisBaseApply.getCisBaseApplyById(id).orElse(null);
        BusinessAssert.notNull(cisBaseApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");
        // 检查是否是带有详细信息的申请单
        if (cisBaseApply instanceof ApplyWithDetial) {
            // 提取详细信息列表，并过滤出BaseDetail类型的细节
            List<BaseDetail> baseDetails = ((ApplyWithDetial) cisBaseApply).getDetailList().stream().filter(BaseDetail.class::isInstance).map(BaseDetail.class::cast)
                    .toList();
            BusinessAssert.isTrue(baseDetails.size() > 1, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "可拆分的申请单");

            Map<String, String> map =
                    cisBaseApplySplitGroupNewNtoLst.getNtos()
                            .stream().collect(Collectors.toMap(CisBaseApplySplitGroupNewNto::getDetailId, CisBaseApplySplitGroupNewNto::getOrderId));
            // 映射每个详细信息到新的申请单对象，并收集到列表中
            return baseDetails.stream().skip(1).map(p -> {
                // 将当前申请单对象转换为Nto对象，并生成新的申请单代码和订单ID
                CisBaseApplyNto nto = CisBaseApplyAssembler.toNto(cisBaseApply, false);
                nto.setId(applyCodeServiceProxy.getApplyNextCode());
                nto.setOrderID(map.get(p.getId()));
                nto.setServiceItemCode(p.getServiceItemCode());
                // 使用转换后的Nto对象创建新的申请单对象
                CisBaseApply newCisBaseApply = ((ApplyWithDetial<?>) cisBaseApply).splitCreate(nto, p);

                // 将新创建的申请单对象转换为To对象并返回
                return CisBaseApplyAssembler.toTo(newCisBaseApply);
            }).toList();
        }

        // 如果不是带有详细信息的申请单，返回null
        return null;
    }


//    private BiConsumer<CisBaseApplyNto, List<String>> mergeGroup(CisBaseApply cisBaseApply) {
//        return (nto, detailids) -> {
//            cisBaseApply.splitGroup(nto, detailids);
//        };
//    }


}