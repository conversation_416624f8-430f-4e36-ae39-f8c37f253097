package com.bjgoodwill.hip.ds.cis.apply.blood.service.internal;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.ds.cis.apply.blood.entity.CisBloodComponent;
import com.bjgoodwill.hip.ds.cis.apply.blood.service.CisBloodComponentService;
import com.bjgoodwill.hip.ds.cis.apply.blood.service.internal.assembler.CisBloodComponentAssembler;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodComponentEto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodComponentNto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodComponentTo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.apply.blood.service.CisBloodComponentService")
@RequestMapping(value = "/api/apply/blood/cisBloodComponent", produces = "application/json; charset=utf-8")
public class CisBloodComponentServiceImpl implements CisBloodComponentService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisBloodComponentTo getCisBloodComponentById(String id) {
        return CisBloodComponentAssembler.toTo(CisBloodComponent.getCisBloodComponentById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisBloodComponentTo createCisBloodComponent(CisBloodComponentNto cisBloodComponentNto) {
        CisBloodComponent cisBloodComponent = new CisBloodComponent();
        cisBloodComponent = cisBloodComponent.create(cisBloodComponentNto.getCisBloodApplyId(), cisBloodComponentNto, CisStatusEnum.NEW, true);
        return CisBloodComponentAssembler.toTo(cisBloodComponent);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisBloodComponent(String id, CisBloodComponentEto cisBloodComponentEto) {
        Optional<CisBloodComponent> cisBloodComponentOptional = CisBloodComponent.getCisBloodComponentById(id);
        cisBloodComponentOptional.ifPresent(cisBloodComponent -> cisBloodComponent.update(cisBloodComponentEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisBloodComponent(String id) {
        Optional<CisBloodComponent> cisBloodComponentOptional = CisBloodComponent.getCisBloodComponentById(id);
        cisBloodComponentOptional.ifPresent(cisBloodComponent -> cisBloodComponent.delete());
    }


    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}