package com.bjgoodwill.hip.ds.cis.apply.blood.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.blood.repository.CisBloodApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodApplyQto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodComponentNto;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.ApplyWithDetial;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "备血申请单")
@DiscriminatorValue("08")
public class CisBloodApply extends ApplyWithDetial<CisBloodComponent> {

    // 预定输注时间
    private LocalDateTime preInfusionDate;
    // 临床诊断
    private String clinicalDiagnosis;
    // 输血指征
    private String transfusionTrigger;
    // 输血需求 1-常规用血，2-紧急手术，3-手术备血，4-异性输血
    private String transfusionDemand;
    // 输血目的
    private String transfusionPurpose;
    // 输血方式 1-异体，2-自体，
    private String transfusionWay;
    // 输血检测项目 0-未送检，1-已送检
    private String transfusionDetection;
    // 采血人
    private String drawBloodUser;
    // 采血人名称
    private String drawBloodUserName;
    // 血型
    private String bloodType;
    // 0-阴性，1-阳性
    private Integer rh_d;
    // 红细胞RBC
    private Double erythrocyte;
    // 白细胞 WBC 单位x10^9/L
    private String leukocyte;
    // 血红蛋白 HB
    private Integer hemoglobin;
    // 血小板 PL
    private Double thrombocyte;
    // 红细胞压积HCT
    private Double hematokrit;
    // 谷丙转氨酶ALT
    private Double glutamicPyruvic;
    // APTT单位秒 参考值 28.0~43.5
    private Double aptt;
    // FIBD单位g/L 参考值 2.00~4.00
    private Double fibd;
    // PT单位秒 参考值11.0~15.0
    private Double pt;
    // HbsAg乙肝表面抗原 0-阴性，1-阳性
    private Boolean hbsAg;
    // HbsAb乙型肝炎表面抗体 0-阴性，1-阳性
    private Boolean hbsAb;
    // HbeAg乙型肝炎E抗原 0-阴性，1-阳性
    private Boolean hbeAg;
    // HbeAb乙型肝炎E抗体 0-阴性，1-阳性
    private Boolean hbeAb;
    // HbcAb乙肝核心抗体 0-阴性，1-阳性
    private Boolean hbcAb;
    // HCVAb丙肝病毒抗体 0-阴性，1-阳性
    private Boolean hcvAb;
    // TP-Ab梅毒 0-阴性，1-阳性
    private Boolean tpAb;
    // HIV(1+2)Ab艾滋病
    private Boolean hivAb;
    // 注明1 
    private Boolean indicate1;
    // 注明2
    private Boolean indicate2;
    // 标本留取时间
    private LocalDateTime specimenRetentionDate;
    // 申请医生签字
    private String applyUser;
    // 申请医生签字名称
    private String applyUserName;
    // 申请时间
    private LocalDateTime applyDate;
    // 申请确认标识：0申请，1通过
    private Boolean appleType;
    // 此患者仅备血未输血 0-否，1-是
    private Boolean preBlood;

    // 是否输血史
    private Boolean transfusionHistory;
    // 是否妊娠史
    private Boolean pregnancyHistory;
    // 是否过敏史
    private Boolean allergicHistoryFlag;
    // 上级医师
    private String seniorPhysician;
    // 上级医师名称
    private String seniorPhysicianName;
    // 上级医师意见
    private String seniorPhysicianOpinion;
    // 预定输血血型
    private String preInfusionBloodType;

    public static Optional<CisBloodApply> getCisBloodApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisBloodApply> getCisBloodApplies(CisBloodApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisBloodApply> getCisBloodApplyPage(CisBloodApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisBloodApply> getSpecification(CisBloodApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            return predicate;
        };
    }

    private static CisBloodApplyRepository dao() {
        return SpringUtil.getBean(CisBloodApplyRepository.class);
    }

    @Comment("预定输注时间")
    @Column(name = "pre_infusion_date", nullable = true)
    public LocalDateTime getPreInfusionDate() {
        return preInfusionDate;
    }

    protected void setPreInfusionDate(LocalDateTime preInfusionDate) {
        this.preInfusionDate = preInfusionDate;
    }

    @Comment("临床诊断")
    @Column(name = "clinical_diagnosis", nullable = true)
    public String getClinicalDiagnosis() {
        return clinicalDiagnosis;
    }

    protected void setClinicalDiagnosis(String clinicalDiagnosis) {
        this.clinicalDiagnosis = clinicalDiagnosis;
    }

    @Comment("输血指征")
    @Column(name = "transfusion_trigger", nullable = true)
    public String getTransfusionTrigger() {
        return transfusionTrigger;
    }

    protected void setTransfusionTrigger(String transfusionTrigger) {
        this.transfusionTrigger = transfusionTrigger;
    }

    @Comment("输血需求 1-正常，2-紧急，3-大量，4-特殊(TransfusionDemand)")
    @Column(name = "transfusion_demand", nullable = true)
    public String getTransfusionDemand() {
        return transfusionDemand;
    }

    protected void setTransfusionDemand(String transfusionDemand) {
        this.transfusionDemand = transfusionDemand;
    }

    @Comment("输血目的")
    @Column(name = "transfusion_purpose", nullable = true)
    public String getTransfusionPurpose() {
        return transfusionPurpose;
    }

    protected void setTransfusionPurpose(String transfusionPurpose) {
        this.transfusionPurpose = transfusionPurpose;
    }

    @Comment("输血方式 1-异体，2-自体，")
    @Column(name = "transfusion_way", nullable = true)
    public String getTransfusionWay() {
        return transfusionWay;
    }

    protected void setTransfusionWay(String transfusionWay) {
        this.transfusionWay = transfusionWay;
    }

    @Comment("输血检测项目 0-未送检，1-已送检")
    @Column(name = "transfusion_detection", nullable = true)
    public String getTransfusionDetection() {
        return transfusionDetection;
    }

    protected void setTransfusionDetection(String transfusionDetection) {
        this.transfusionDetection = transfusionDetection;
    }

    @Comment("采血人")
    @Column(name = "draw_blood_user", nullable = true)
    public String getDrawBloodUser() {
        return drawBloodUser;
    }

    protected void setDrawBloodUser(String drawBloodUser) {
        this.drawBloodUser = drawBloodUser;
    }

    @Comment("血型")
    @Column(name = "blood_type", nullable = true)
    public String getBloodType() {
        return bloodType;
    }

    protected void setBloodType(String bloodType) {
        this.bloodType = bloodType;
    }

    @Comment("0-阴性，1-阳性")
    @Column(name = "rh_d", nullable = true)
    public Integer getRh_d() {
        return rh_d;
    }

    protected void setRh_d(Integer rh_d) {
        this.rh_d = rh_d;
    }

    @Comment("红细胞RBC")
    @Column(name = "erythrocyte", nullable = true)
    public Double getErythrocyte() {
        return erythrocyte;
    }

    protected void setErythrocyte(Double erythrocyte) {
        this.erythrocyte = erythrocyte;
    }

    @Comment("白细胞 WBC 单位x10^9/L")
    @Column(name = "leukocyte", nullable = true)
    public String getLeukocyte() {
        return leukocyte;
    }

    protected void setLeukocyte(String leukocyte) {
        this.leukocyte = leukocyte;
    }

    @Comment("血红蛋白 HB")
    @Column(name = "hemoglobin", nullable = true)
    public Integer getHemoglobin() {
        return hemoglobin;
    }

    protected void setHemoglobin(Integer hemoglobin) {
        this.hemoglobin = hemoglobin;
    }

    @Comment("血小板 PL")
    @Column(name = "thrombocyte", nullable = true)
    public Double getThrombocyte() {
        return thrombocyte;
    }

    protected void setThrombocyte(Double thrombocyte) {
        this.thrombocyte = thrombocyte;
    }

    @Comment("红细胞压积HCT")
    @Column(name = "hematokrit", nullable = true)
    public Double getHematokrit() {
        return hematokrit;
    }

    protected void setHematokrit(Double hematokrit) {
        this.hematokrit = hematokrit;
    }

    @Comment("谷丙转氨酶ALT")
    @Column(name = "glutamic_pyruvic", nullable = true)
    public Double getGlutamicPyruvic() {
        return glutamicPyruvic;
    }

    protected void setGlutamicPyruvic(Double glutamicPyruvic) {
        this.glutamicPyruvic = glutamicPyruvic;
    }

    @Comment("APTT单位秒 参考值 28.0~43.5")
    @Column(name = "aptt", nullable = true)
    public Double getAptt() {
        return aptt;
    }

    protected void setAptt(Double aptt) {
        this.aptt = aptt;
    }

    @Comment("FIBD单位g/L 参考值 2.00~4.00")
    @Column(name = "fibd", nullable = true)
    public Double getFibd() {
        return fibd;
    }

    protected void setFibd(Double fibd) {
        this.fibd = fibd;
    }

    @Comment("PT单位秒 参考值11.0~15.0")
    @Column(name = "pt", nullable = true)
    public Double getPt() {
        return pt;
    }

    protected void setPt(Double pt) {
        this.pt = pt;
    }

    @Comment("HbsAg乙肝表面抗原 0-阴性，1-阳性")
    @Column(name = "hbs_ag", nullable = true)
    public Boolean getHbsAg() {
        return hbsAg;
    }

    protected void setHbsAg(Boolean hbsAg) {
        this.hbsAg = hbsAg;
    }

    @Comment("HbsAb乙型肝炎表面抗体 0-阴性，1-阳性")
    @Column(name = "hbs_ab", nullable = true)
    public Boolean getHbsAb() {
        return hbsAb;
    }

    protected void setHbsAb(Boolean hbsAb) {
        this.hbsAb = hbsAb;
    }

    @Comment("HbeAg乙型肝炎E抗原 0-阴性，1-阳性")
    @Column(name = "hbe_ag", nullable = true)
    public Boolean getHbeAg() {
        return hbeAg;
    }

    protected void setHbeAg(Boolean hbeAg) {
        this.hbeAg = hbeAg;
    }

    @Comment("HbeAb乙型肝炎E抗体 0-阴性，1-阳性")
    @Column(name = "hbe_ab", nullable = true)
    public Boolean getHbeAb() {
        return hbeAb;
    }

    protected void setHbeAb(Boolean hbeAb) {
        this.hbeAb = hbeAb;
    }

    @Comment("HbcAb乙肝核心抗体 0-阴性，1-阳性")
    @Column(name = "hbc_ab", nullable = true)
    public Boolean getHbcAb() {
        return hbcAb;
    }

    protected void setHbcAb(Boolean hbcAb) {
        this.hbcAb = hbcAb;
    }

    @Comment("HCVAb丙肝病毒抗体 0-阴性，1-阳性")
    @Column(name = "hcv_ab", nullable = true)
    public Boolean getHcvAb() {
        return hcvAb;
    }

    protected void setHcvAb(Boolean hcvAb) {
        this.hcvAb = hcvAb;
    }

    @Comment("TP-Ab梅毒 0-阴性，1-阳性")
    @Column(name = "tp_ab", nullable = true)
    public Boolean getTpAb() {
        return tpAb;
    }

    protected void setTpAb(Boolean tpAb) {
        this.tpAb = tpAb;
    }

    @Comment("HIV(1+2)Ab艾滋病")
    @Column(name = "hiv_ab", nullable = true)
    public Boolean getHivAb() {
        return hivAb;
    }

    protected void setHivAb(Boolean hivAb) {
        this.hivAb = hivAb;
    }

    @Comment("注明1-紧急输血标本已取结果")
    @Column(name = "indicate_1", nullable = true)
    public Boolean getIndicate1() {
        return indicate1;
    }

    protected void setIndicate1(Boolean indicate1) {
        this.indicate1 = indicate1;
    }

    @Comment("注明2-因患者(或代理人)拒绝检测,故无数据")
    @Column(name = "indicate_2", nullable = true)
    public Boolean getIndicate2() {
        return indicate2;
    }

    protected void setIndicate2(Boolean indicate2) {
        this.indicate2 = indicate2;
    }

    @Comment("标本留取时间")
    @Column(name = "specimen_retention_date", nullable = true)
    public LocalDateTime getSpecimenRetentionDate() {
        return specimenRetentionDate;
    }

    protected void setSpecimenRetentionDate(LocalDateTime specimenRetentionDate) {
        this.specimenRetentionDate = specimenRetentionDate;
    }

    @Comment("申请医生签字")
    @Column(name = "apply_user", nullable = true)
    public String getApplyUser() {
        return applyUser;
    }

    protected void setApplyUser(String applyUser) {
        this.applyUser = applyUser;
    }

    @Comment("申请时间")
    @Column(name = "apply_date", nullable = true)
    public LocalDateTime getApplyDate() {
        return applyDate;
    }

    protected void setApplyDate(LocalDateTime applyDate) {
        this.applyDate = applyDate;
    }

    @Comment("申请确认标识：0申请，1通过")
    @Column(name = "apple_type", nullable = true)
    public Boolean getAppleType() {
        return appleType;
    }

    protected void setAppleType(Boolean appleType) {
        this.appleType = appleType;
    }

    @Comment("此患者仅备血未输血 0-否，1-是")
    @Column(name = "pre_blood", nullable = true)
    public Boolean getPreBlood() {
        return preBlood;
    }

    protected void setPreBlood(Boolean preBlood) {
        this.preBlood = preBlood;
    }

    @Comment("采血人名称")
    @Column(name = "draw_blood_user_name", nullable = true)
    public String getDrawBloodUserName() {
        return drawBloodUserName;
    }

    public void setDrawBloodUserName(String drawBloodUserName) {
        this.drawBloodUserName = drawBloodUserName;
    }

    @Comment("申请医生签字名称")
    @Column(name = "apply_user_name", nullable = true)
    public String getApplyUserName() {
        return applyUserName;
    }

    public void setApplyUserName(String applyUserName) {
        this.applyUserName = applyUserName;
    }

    @Comment("是否输血史")
    @Column(name = "transfusion_history", nullable = true)
    public Boolean getTransfusionHistory() {
        return transfusionHistory;
    }

    public void setTransfusionHistory(Boolean transfusionHistory) {
        this.transfusionHistory = transfusionHistory;
    }

    @Comment("是否妊娠史")
    @Column(name = "pregnancy_history", nullable = true)
    public Boolean getPregnancyHistory() {
        return pregnancyHistory;
    }

    public void setPregnancyHistory(Boolean pregnancyHistory) {
        this.pregnancyHistory = pregnancyHistory;
    }

    @Comment("是否过敏史")
    @Column(name = "Allergic_History_Flag", nullable = true)
    public Boolean getAllergicHistoryFlag() {
        return allergicHistoryFlag;
    }

    public void setAllergicHistoryFlag(Boolean allergicHistoryFlag) {
        this.allergicHistoryFlag = allergicHistoryFlag;
    }

    @Comment("上级医师")
    @Column(name = "senior_physician", nullable = true)
    public String getSeniorPhysician() {
        return seniorPhysician;
    }

    public void setSeniorPhysician(String seniorPhysician) {
        this.seniorPhysician = seniorPhysician;
    }

    @Comment("上级医师名称")
    @Column(name = "senior_physician_name", nullable = true)
    public String getSeniorPhysicianName() {
        return seniorPhysicianName;
    }

    public void setSeniorPhysicianName(String seniorPhysicianName) {
        this.seniorPhysicianName = seniorPhysicianName;
    }

    @Comment("上级医师意见")
    @Column(name = "senior_physician_opinion", nullable = true)
    public String getSeniorPhysicianOpinion() {
        return seniorPhysicianOpinion;
    }

    public void setSeniorPhysicianOpinion(String seniorPhysicianOpinion) {
        this.seniorPhysicianOpinion = seniorPhysicianOpinion;
    }

    @Comment("预定输血血型")
    @Column(name = "pre_infusion_blood_type", nullable = true)
    public String getPreInfusionBloodType() {
        return preInfusionBloodType;
    }

    public void setPreInfusionBloodType(String preInfusionBloodType) {
        this.preInfusionBloodType = preInfusionBloodType;
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.BLOOD;
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisBloodApplyNto) cisBaseApplyNto, save);
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisBloodApplyEto) cisBaseApplyEto);
    }

    public CisBloodApply create(CisBloodApplyNto cisBloodApplyNto, Boolean save) {
        BusinessAssert.notNull(cisBloodApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisBloodApplyNto不能为空！");
        super.create(cisBloodApplyNto, save);

        setPreInfusionDate(cisBloodApplyNto.getPreInfusionDate());
        setClinicalDiagnosis(cisBloodApplyNto.getClinicalDiagnosis());
        setTransfusionTrigger(cisBloodApplyNto.getTransfusionTrigger());
        setTransfusionDemand(cisBloodApplyNto.getTransfusionDemand());
        setTransfusionPurpose(cisBloodApplyNto.getTransfusionPurpose());
        setTransfusionWay(cisBloodApplyNto.getTransfusionWay());
        setTransfusionDetection(cisBloodApplyNto.getTransfusionDetection());
        setDrawBloodUser(cisBloodApplyNto.getDrawBloodUser());
        setDrawBloodUserName(cisBloodApplyNto.getDrawBloodUserName());
        setBloodType(cisBloodApplyNto.getBloodType());
        setRh_d(cisBloodApplyNto.getRh_d());
        setErythrocyte(cisBloodApplyNto.getErythrocyte());
        setLeukocyte(cisBloodApplyNto.getLeukocyte());
        setHemoglobin(cisBloodApplyNto.getHemoglobin());
        setThrombocyte(cisBloodApplyNto.getThrombocyte());
        setHematokrit(cisBloodApplyNto.getHematokrit());
        setGlutamicPyruvic(cisBloodApplyNto.getGlutamicPyruvic());
        setAptt(cisBloodApplyNto.getAptt());
        setFibd(cisBloodApplyNto.getFibd());
        setPt(cisBloodApplyNto.getPt());
        setHbsAg(cisBloodApplyNto.getHbsAg());
        setHbsAb(cisBloodApplyNto.getHbsAb());
        setHbeAg(cisBloodApplyNto.getHbeAg());
        setHbeAb(cisBloodApplyNto.getHbeAb());
        setHbcAb(cisBloodApplyNto.getHbcAb());
        setHcvAb(cisBloodApplyNto.getHcvAb());
        setTpAb(cisBloodApplyNto.getTpAb());
        setHivAb(cisBloodApplyNto.getHivAb());
        setIndicate1(cisBloodApplyNto.getIndicate1());
        setIndicate2(cisBloodApplyNto.getIndicate2());
        setSpecimenRetentionDate(cisBloodApplyNto.getSpecimenRetentionDate());
        setApplyUser(cisBloodApplyNto.getApplyUser());
        setApplyUserName(cisBloodApplyNto.getApplyUserName());
        setApplyDate(cisBloodApplyNto.getApplyDate());
        setAppleType(cisBloodApplyNto.getAppleType());
        setPreBlood(cisBloodApplyNto.getPreBlood());
        setTransfusionHistory(cisBloodApplyNto.getTransfusionHistory());
        setPregnancyHistory(cisBloodApplyNto.getPregnancyHistory());
        setAllergicHistoryFlag(cisBloodApplyNto.getAllergicHistoryFlag());
        setSeniorPhysician(cisBloodApplyNto.getSeniorPhysician());
        setSeniorPhysicianName(cisBloodApplyNto.getSeniorPhysicianName());
        setSeniorPhysicianOpinion(cisBloodApplyNto.getSeniorPhysicianOpinion());
        setPreInfusionBloodType(cisBloodApplyNto.getPreInfusionBloodType());
        if (save) {
            dao().save(this);
        }
        if (!CollectionUtils.isEmpty(cisBloodApplyNto.getDetails())) {
            for (CisBloodComponentNto _cisBloodComponentNto : cisBloodApplyNto.getDetails()) {
                CisBloodComponent cisBloodComponent = new CisBloodComponent();
                cisBloodComponent.create(getId(), _cisBloodComponentNto, getStatusCode(), save);
            }
        }
        return this;
    }

    public void update(CisBloodApplyEto cisBloodApplyEto) {
        super.update(cisBloodApplyEto);
        setPreInfusionDate(cisBloodApplyEto.getPreInfusionDate());
        setClinicalDiagnosis(cisBloodApplyEto.getClinicalDiagnosis());
        setTransfusionTrigger(cisBloodApplyEto.getTransfusionTrigger());
        setTransfusionDemand(cisBloodApplyEto.getTransfusionDemand());
        setTransfusionPurpose(cisBloodApplyEto.getTransfusionPurpose());
        setTransfusionWay(cisBloodApplyEto.getTransfusionWay());
        setTransfusionDetection(cisBloodApplyEto.getTransfusionDetection());
        setDrawBloodUser(cisBloodApplyEto.getDrawBloodUser());
        setDrawBloodUserName(cisBloodApplyEto.getDrawBloodUserName());
        setBloodType(cisBloodApplyEto.getBloodType());
        setRh_d(cisBloodApplyEto.getRh_d());
        setErythrocyte(cisBloodApplyEto.getErythrocyte());
        setLeukocyte(cisBloodApplyEto.getLeukocyte());
        setHemoglobin(cisBloodApplyEto.getHemoglobin());
        setThrombocyte(cisBloodApplyEto.getThrombocyte());
        setHematokrit(cisBloodApplyEto.getHematokrit());
        setGlutamicPyruvic(cisBloodApplyEto.getGlutamicPyruvic());
        setAptt(cisBloodApplyEto.getAptt());
        setFibd(cisBloodApplyEto.getFibd());
        setPt(cisBloodApplyEto.getPt());
        setHbsAg(cisBloodApplyEto.getHbsAg());
        setHbsAb(cisBloodApplyEto.getHbsAb());
        setHbeAg(cisBloodApplyEto.getHbeAg());
        setHbeAb(cisBloodApplyEto.getHbeAb());
        setHbcAb(cisBloodApplyEto.getHbcAb());
        setHcvAb(cisBloodApplyEto.getHcvAb());
        setTpAb(cisBloodApplyEto.getTpAb());
        setHivAb(cisBloodApplyEto.getHivAb());
        setIndicate1(cisBloodApplyEto.getIndicate1());
        setIndicate2(cisBloodApplyEto.getIndicate2());
        setSpecimenRetentionDate(cisBloodApplyEto.getSpecimenRetentionDate());
        setApplyUser(cisBloodApplyEto.getApplyUser());
        setApplyUserName(cisBloodApplyEto.getApplyUserName());
        setApplyDate(cisBloodApplyEto.getApplyDate());
        setAppleType(cisBloodApplyEto.getAppleType());
        setPreBlood(cisBloodApplyEto.getPreBlood());
        setTransfusionHistory(cisBloodApplyEto.getTransfusionHistory());
        setPregnancyHistory(cisBloodApplyEto.getPregnancyHistory());
        setAllergicHistoryFlag(cisBloodApplyEto.getAllergicHistoryFlag());
        setSeniorPhysician(cisBloodApplyEto.getSeniorPhysician());
        setSeniorPhysicianName(cisBloodApplyEto.getSeniorPhysicianName());
        setSeniorPhysicianOpinion(cisBloodApplyEto.getSeniorPhysicianOpinion());
        setPreInfusionBloodType(cisBloodApplyEto.getPreInfusionBloodType());
        if (!CollectionUtils.isEmpty(cisBloodApplyEto.getDetailEtos())) {
            cisBloodApplyEto.getDetailEtos().forEach(eto -> {
                Optional<CisBloodComponent> detail = CisBloodComponent.getCisBloodComponentById(eto.getId());
                detail.get().update(eto);
            });
        }

        if (!CollectionUtils.isEmpty(cisBloodApplyEto.getDetailNtos())) {
            cisBloodApplyEto.getDetailNtos().forEach(nto -> {
                CisBloodComponent detail = new CisBloodComponent();
                detail.create(getId(), nto, getStatusCode(), true);
            });
        }
    }

    @Override
    @Transient
    public List<CisBloodComponent> getDetailList() {
        return CisBloodComponent.getByCisBloodApplyId(getId());
    }

    @Override
    protected List<String> getSplitCodes(List<CisBloodComponent> applyDetails) {
        return applyDetails.stream().map(p -> p.getBloodComponent()).toList();
    }

    @Override
    public void delete() {
        super.delete();
//        for (CisBloodApply cisBloodApply : CisBloodApply.getByCisBloodApplyId(getId())) {
//            cisBloodApply.delete();
//        }
        // CisBloodApply.deleteByCisBloodApplyId(getId());
        for (CisBloodComponent cisBloodComponent : CisBloodComponent.getByCisBloodApplyId(getId())) {
            cisBloodComponent.delete();
        }
        // CisBloodComponent.deleteByCisBloodApplyId(getId());dao().delete(this);
        dao().delete(this);
    }
}
