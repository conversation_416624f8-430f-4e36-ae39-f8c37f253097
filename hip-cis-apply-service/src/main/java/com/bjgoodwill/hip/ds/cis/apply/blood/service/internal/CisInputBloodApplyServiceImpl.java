package com.bjgoodwill.hip.ds.cis.apply.blood.service.internal;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.CisBaseApplyServiceImpl;
import com.bjgoodwill.hip.ds.cis.apply.blood.entity.CisBloodComponent;
import com.bjgoodwill.hip.ds.cis.apply.blood.entity.CisInputBloodApply;
import com.bjgoodwill.hip.ds.cis.apply.blood.service.CisInputBloodApplyService;
import com.bjgoodwill.hip.ds.cis.apply.blood.service.internal.assembler.CisInputBloodApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodComponentNto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisInputBloodApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisInputBloodApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisInputBloodApplyTo;
import com.bjgoodwill.hip.idempotent.annotation.HipIdempotent;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.apply.blood.service.CisInputBloodApplyService")
@RequestMapping(value = "/api/apply/apply/cisInputBloodApply", produces = "application/json; charset=utf-8")
public class CisInputBloodApplyServiceImpl extends CisBaseApplyServiceImpl implements CisInputBloodApplyService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisInputBloodApplyTo getCisBloodApplyById(String id) {
        return CisInputBloodApplyAssembler.toTo(CisInputBloodApply.getCisBloodApplyById(id).orElse(null), true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisInputBloodApplyTo createCisBloodApply(CisInputBloodApplyNto cisInputBloodApplyNto) {
        CisInputBloodApply cisInputBloodApply = new CisInputBloodApply();
        cisInputBloodApply = cisInputBloodApply.create(cisInputBloodApplyNto, true);
        return CisInputBloodApplyAssembler.toTo(cisInputBloodApply);
    }


    //添加血液成分
    @Override
    @Transactional
    @HipIdempotent
    public CisInputBloodApplyTo addCisBloodApply(String id, CisBloodComponentNto cisBloodComponentNto) {
        CisInputBloodApply cisInputBloodApply = CisInputBloodApply.getCisBloodApplyById(id).orElse(null);
        BusinessAssert.notNull(cisInputBloodApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单");

        CisBloodComponent cisBloodComponent = new CisBloodComponent();
        cisBloodComponentNto.setCisBloodApplyId(id);
        cisBloodComponent.create(id, cisBloodComponentNto, CisStatusEnum.NEW, true);
        return CisInputBloodApplyAssembler.toTo(cisInputBloodApply);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisBloodApply(String id, CisInputBloodApplyEto cisInputBloodApplyEto) {
        Optional<CisInputBloodApply> cisBloodApplyOptional = CisInputBloodApply.getCisBloodApplyById(id);
        cisBloodApplyOptional.ifPresent(cisBloodApply -> cisBloodApply.update(cisInputBloodApplyEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisBloodApply(String id) {
        Optional<CisInputBloodApply> cisBloodApplyOptional = CisInputBloodApply.getCisBloodApplyById(id);
        cisBloodApplyOptional.ifPresent(cisBloodApply -> cisBloodApply.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}