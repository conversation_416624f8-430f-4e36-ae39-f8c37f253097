package com.bjgoodwill.hip.ds.cis.apply.overstep.service.internal;

import com.bjgoodwill.hip.business.util.cis.common.enums.CheckTypeEnum;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.mq.send.CisApplyMqSend;
import com.bjgoodwill.hip.ds.cis.apply.overstep.entity.CisOverstepApproal;
import com.bjgoodwill.hip.ds.cis.apply.overstep.service.CisOverstepApproalService;
import com.bjgoodwill.hip.ds.cis.apply.overstep.service.internal.assembler.CisOverstepApproalAssembler;
import com.bjgoodwill.hip.ds.cis.apply.overstep.to.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * 跨越审批服务实现类
 * 提供与跨越审批相关的操作，如创建、删除和审核审批请求
 */
@RestController("com.bjgoodwill.hip.ds.cis.apply.overstep.service.CisOverstepApproalService")
@RequestMapping(value = "/api/apply/overstep/cisOverstepApproal", produces = "application/json; charset=utf-8")
public class CisOverstepApproalServiceImpl implements CisOverstepApproalService {

    @Autowired
    private CisApplyMqSend cisApplyMqSend;

    /**
     * 创建跨越审批请求
     * 将新的审批请求信息保存到数据库中
     *
     * @param cisOverstepApproalNto 包含要创建的审批请求信息的对象
     * @return 创建的审批请求对象
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOverstepApproalTo createCisOverstepApproal(CisOverstepApproalNto cisOverstepApproalNto) {
        CisOverstepApproal cisOverstepApproal = new CisOverstepApproal();
        cisOverstepApproal = cisOverstepApproal.create(cisOverstepApproalNto);
        return CisOverstepApproalAssembler.toTo(cisOverstepApproal);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOverstepApproalTo createCisOverstepApproalByApplyId(String applyId, CisOverStepNto cisOverStepNto) {
        CisOverstepApproal cisOverstepApproal = new CisOverstepApproal();
        CisBaseApply apply = CisBaseApply.getCisBaseApplyById(applyId).orElse(null);
        CisOverstepApproalNto cisOverstepApproalNto = (CisOverstepApproalNto) cisOverStepNto;
        cisOverstepApproalNto.setApplyId(apply.getId());
        cisOverstepApproalNto.setApplyDoc(HIPLoginUtil.getStaffId());
        cisOverstepApproalNto.setApplyOrgCode(apply.getVisitOrgCode());
        cisOverstepApproalNto.setApplyDocName(HIPLoginUtil.getLoginName());
        cisOverstepApproalNto.setApplyOrgName(apply.getVisitOrgName());
        cisOverstepApproalNto.setPatMiCode(apply.getPatMiCode());
        cisOverstepApproalNto.setVisitCode(apply.getVisitCode());
        cisOverstepApproalNto.setVisitOrgCode(apply.getVisitOrgCode());
        cisOverstepApproalNto.setCreateOrgCode(apply.getCreateOrgCode());
        cisOverstepApproalNto.setDeptNurseCode(apply.getDeptNurseCode());
        cisOverstepApproalNto.setOrderId(apply.getOrderID());
        cisOverstepApproalNto.setVisitType(apply.getVisitType());
        return CisOverstepApproalAssembler.toTo(cisOverstepApproal.create(cisOverstepApproalNto));

    }


    /**
     * 删除特定的跨越审批请求
     * 根据提供的ID找到并删除对应的审批请求
     *
     * @param id 要删除的审批请求的ID
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOverstepApproal(String id) {
        Optional<CisOverstepApproal> cisOverstepApproalOptional = CisOverstepApproal.getCisOverstepApproalById(id);
        cisOverstepApproalOptional.ifPresent(cisOverstepApproal -> cisOverstepApproal.delete());
    }

    /**
     * 审核通过审批申请
     * 该方法主要用于将给定的审批申请标记为通过，并执行相关的业务逻辑
     *
     * @param ids                   待审核通过的审批申请ID列表，不能为空
     * @param cisOverstepApproalEto 包含审核信息的ETO对象，用于更新审批申请的状态
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisOverstepApproalTo> passApproal(List<String> ids, CisOverstepApproalEto cisOverstepApproalEto) {
        // 根据ID列表查询审批申请记录
        List<CisOverstepApproal> cisOverstepApprovals = CisOverstepApproal.getCisOverstepApproalsByIdsIn(ids);

        // 遍历并审核通过每个审批申请
        cisOverstepApprovals.forEach(cisOverstepApproal -> cisOverstepApproal.review(cisOverstepApproalEto));

        List<String> orderIds = cisOverstepApprovals.stream().map(CisOverstepApproal::getOrderId).toList();
        // 发送消息通知审批通过
        cisApplyMqSend.OrderCheckSend(orderIds, CheckTypeEnum.PASS);
        return CisOverstepApproalAssembler.toTos(cisOverstepApprovals);
    }

    /**
     * 审核不通过审批申请
     * <p>
     * 此方法接收一个审批申请ID列表和审批信息对象，将这些申请标记为审核不通过，并返回更新后的审批申请列表
     *
     * @param ids                   审批申请ID列表，用于标识待审核的审批申请
     * @param cisOverstepApproalEto 包含审核信息的对象，用于更新审批申请的状态和信息
     * @return 返回一个更新后的审批申请列表，表示这些申请已被审核且未通过
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisOverstepApproalTo> noPassApproal(List<String> ids, CisOverstepApproalEto cisOverstepApproalEto) {
        // 根据ID列表查询审批申请记录
        List<CisOverstepApproal> cisOverstepApprovals = CisOverstepApproal.getCisOverstepApproalsByIdsIn(ids);
        // 遍历并审核通过每个审批申请
        cisOverstepApprovals.forEach(cisOverstepApproal -> cisOverstepApproal.noPass(cisOverstepApproalEto));

        return CisOverstepApproalAssembler.toTos(cisOverstepApprovals);
    }


    /**
     * 初始化数据绑定
     * 用于配置Web数据绑定，目前未实现具体逻辑
     *
     * @param binder Web数据绑定器
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // 未来可添加数据绑定的自定义配置
    }


    /**
     * 根据就诊码获取 申请通过的 cisOverstepApproal 列表
     * 此方法用于查询与特定就诊码相关的所有 cisOverstepApproal 记录，并根据提供的订单 ID 进行过滤
     * 如果提供了非空的订单 ID 列表，那么只返回与这些订单 ID 匹配的 cisOverstepApproal 项目
     * 否则，返回与该就诊码关联的所有 cisOverstepApproal 项目
     *
     * @param visitCode 用于查询的就诊码
     * @param orderIds  用于过滤结果的订单 ID 列表，可以为空
     * @return 返回过滤后的 cisOverstepApproal 项目列表
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOverstepApproalTo> getCisOverstepApproalByVisitCode(String visitCode, List<String> orderIds) {
        // 创建查询传输对象并设置就诊码
        CisOverstepApproalQto cisOverstepApproalQto = new CisOverstepApproalQto();
        cisOverstepApproalQto.setVisitCode(visitCode);
        cisOverstepApproalQto.setCheckType(CheckTypeEnum.PASS);
        // 查询并转换 cisOverstepApproal 项目
        List<CisOverstepApproalTo> items = CisOverstepApproalAssembler.toTos(CisOverstepApproal.getCisOverstepApproals(cisOverstepApproalQto));

        // 如果提供了非空的订单 ID 列表，根据订单 ID 过滤 cisOverstepApproal 项目
        if (CollectionUtils.isNotEmpty(orderIds)) {
            return items.stream().filter(item -> orderIds.contains(item.getOrderId())).toList();
        }
        // 如果没有提供订单 ID 列表，直接返回查询到的所有项目
        return items;
    }

    /**
     * 获取CisOverstepApproal列表
     * <p>
     * 此方法使用了@Transactional注解，指明该方法在执行时需要一个数据库事务
     * rollbackFor = Throwable.class表示任何 Throwable 类型的异常都会导致事务回滚
     * readOnly = true表示该方法只读取数据，不修改数据库，这有助于提高性能
     *
     * @param cisOverstepQto 用于查询的CisOverstepQto对象，包含了查询条件
     * @return 返回一个CisOverstepApproalTo对象列表，表示查询结果
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOverstepApproalTo> getCisOverstepApproal(CisOverstepQto cisOverstepQto) {
        return CisOverstepApproalAssembler.toTos(CisOverstepApproal.getCisOverstepApproals(cisOverstepQto));
    }


}
