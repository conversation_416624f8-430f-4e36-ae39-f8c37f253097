package com.bjgoodwill.hip.ds.cis.apply.drug.repository;

import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisBaseDrugApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisBaseDrugApplyRepository")
public interface CisBaseDrugApplyRepository extends JpaRepository<CisBaseDrugApply, String>, JpaSpecificationExecutor<CisBaseDrugApply> {

}