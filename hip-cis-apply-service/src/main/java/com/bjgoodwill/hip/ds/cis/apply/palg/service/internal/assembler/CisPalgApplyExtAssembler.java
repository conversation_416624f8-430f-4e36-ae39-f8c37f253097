package com.bjgoodwill.hip.ds.cis.apply.palg.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.apply.palg.entity.CisPalgApplyExt;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyExtTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisPalgApplyExtAssembler {

    public static List<CisPalgApplyExtTo> toTos(List<CisPalgApplyExt> cisPalgApplyExts) {
        return toTos(cisPalgApplyExts, false);
    }

    public static List<CisPalgApplyExtTo> toTos(List<CisPalgApplyExt> cisPalgApplyExts, boolean withAllParts) {
        Assert.notNull(cisPalgApplyExts, "参数cisPalgApplyExts不能为空！");

        List<CisPalgApplyExtTo> tos = new ArrayList<>();
        for (CisPalgApplyExt cisPalgApplyExt : cisPalgApplyExts)
            tos.add(toTo(cisPalgApplyExt, withAllParts));
        return tos;
    }

    public static CisPalgApplyExtTo toTo(CisPalgApplyExt cisPalgApplyExt) {
        return toTo(cisPalgApplyExt, false);
    }

    /**
     * @generated
     */
    public static CisPalgApplyExtTo toTo(CisPalgApplyExt cisPalgApplyExt, boolean withAllParts) {
        if (cisPalgApplyExt == null)
            return null;
        CisPalgApplyExtTo to = new CisPalgApplyExtTo();
        to.setId(cisPalgApplyExt.getId());
        to.setApplyId(cisPalgApplyExt.getApplyId());
        to.setPalgExtType(cisPalgApplyExt.getPalgExtType());
        to.setOperationCode(cisPalgApplyExt.getOperationCode());
        to.setOperationName(cisPalgApplyExt.getOperationName());
        to.setOperationDoctor(cisPalgApplyExt.getOperationDoctor());
        to.setOperationDoctorName(cisPalgApplyExt.getOperationDoctorName());
        to.setOperationDate(cisPalgApplyExt.getOperationDate());
        to.setOperationRoom(cisPalgApplyExt.getOperationRoom());
        to.setOperationRoomName(cisPalgApplyExt.getOperationRoomName());
        to.setIntraoperativelyPalg(cisPalgApplyExt.getIntraoperativelyPalg());
        to.setOperationSeen(cisPalgApplyExt.getOperationSeen());
        to.setDiscoveryDate(cisPalgApplyExt.getDiscoveryDate());
        to.setHumanOrgans(cisPalgApplyExt.getHumanOrgans());
        to.setTumorSize(cisPalgApplyExt.getTumorSize());
        to.setTumorShape(cisPalgApplyExt.getTumorShape());
        to.setTumorMobility(cisPalgApplyExt.getTumorMobility());
        to.setTumorFirmness(cisPalgApplyExt.getTumorFirmness());
        to.setTumorGrowthRate(cisPalgApplyExt.getTumorGrowthRate());
        to.setTumorTransfer(cisPalgApplyExt.getTumorTransfer());
        to.setTumorOther(cisPalgApplyExt.getTumorOther());
        to.setFirstMenstruation(cisPalgApplyExt.getFirstMenstruation());
        to.setCycle(cisPalgApplyExt.getCycle());
        to.setLastMenstrualDate(cisPalgApplyExt.getLastMenstrualDate());
        to.setMenopauseFlag(cisPalgApplyExt.getMenopauseFlag());
        to.setPregnant(cisPalgApplyExt.getPregnant());
        to.setProduce(cisPalgApplyExt.getProduce());
        to.setAbortion(cisPalgApplyExt.getAbortion());
        to.setLastDeliveryDate(cisPalgApplyExt.getLastDeliveryDate());
        to.setTreatmentElapse(cisPalgApplyExt.getTreatmentElapse());
        to.setCreatedDate(cisPalgApplyExt.getCreatedDate());
        to.setCreatedStaff(cisPalgApplyExt.getCreatedStaff());
        to.setCreatedStaffName(cisPalgApplyExt.getCreatedStaffName());
        to.setUpdatedDate(cisPalgApplyExt.getUpdatedDate());
        to.setUpdatedStaff(cisPalgApplyExt.getUpdatedStaff());
        to.setUpdatedStaffName(cisPalgApplyExt.getUpdatedStaffName());

        if (withAllParts) {
        }
        return to;
    }

}