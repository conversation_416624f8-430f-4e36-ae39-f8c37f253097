package com.bjgoodwill.hip.ds.cis.apply.drug.entity;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.EDrugSystemTypeExtEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.ApplyWithDetial;
import com.bjgoodwill.hip.ds.cis.apply.drug.repository.CisBaseDrugApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisBaseDrugApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisBaseDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisBaseDrugApplyQto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisDrugApplyDetailNto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "药品申请单")
public abstract class CisBaseDrugApply extends ApplyWithDetial<CisDrugApplyDetail> {

    // 用法
    private String usage;
    // 用法名称
    private String usageName;

    // 疗程
    private String treatmentCourse;
    // 疗程单位
    private String treatmentCourseUnit;
    // 疗程单位名称
    private String treatmentCourseUnitName;
    // 药房
    private String receiveOrg;
    // 药房名称
    private String receiveOrgName;

    //region 取药方式
//    private SbadmWayEnum sbadmWay;
//
//    @Comment("取药方式")
//    @Column(name = "sbadm_way", nullable = true)
//    public SbadmWayEnum getSbadmWay() {
//        return sbadmWay;
//    }
//
//    public void setSbadmWay(SbadmWayEnum sbadmWay) {
//        this.sbadmWay = sbadmWay;
//    }
    //endregion
    private List<CisDrugApplyDetail> details = new ArrayList<>();

    public static Optional<CisBaseDrugApply> getCisBaseDrugApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisBaseDrugApply> getCisBaseDrugApplies(CisBaseDrugApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisBaseDrugApply> getCisBaseDrugApplyPage(CisBaseDrugApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisBaseDrugApply> getSpecification(CisBaseDrugApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
//            if (qto.getSbadmWay() != null) {
//                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sbadmWay"), qto.getSbadmWay()));
//            }

            return predicate;
        };
    }

    public static CisBaseDrugApply newInstanceByNto(CisBaseDrugApplyNto cisBaseApplyNto) {
        try {
            return (CisBaseDrugApply) Class.forName("com.bjgoodwill.hip.ds.cis.apply.apply.entity."
                    + StringUtils.removeEnd(cisBaseApplyNto.getClass().getSimpleName(), "Nto")).getConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static CisBaseDrugApplyRepository dao() {
        return SpringUtil.getBean(CisBaseDrugApplyRepository.class);
    }

    @Comment("用法")
    @Column(name = "usage", nullable = true)
    public String getUsage() {
        return usage;
    }

    protected void setUsage(String usage) {
        this.usage = usage;
    }

    @Comment("用法名称")
    @Column(name = "usage_name", nullable = true)
    public String getUsageName() {
        return usageName;
    }

    protected void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    @Comment("疗程")
    @Column(name = "treatment_course", nullable = true)
    public String getTreatmentCourse() {
        return treatmentCourse;
    }

    protected void setTreatmentCourse(String treatmentCourse) {
        this.treatmentCourse = treatmentCourse;
    }

    @Comment("疗程单位")
    @Column(name = "treatment_course_unit", nullable = true)
    public String getTreatmentCourseUnit() {
        return treatmentCourseUnit;
    }

    protected void setTreatmentCourseUnit(String treatmentCourseUnit) {
        this.treatmentCourseUnit = treatmentCourseUnit;
    }

    @Comment("疗程单位名称")
    @Column(name = "treatment_course_unit_name", nullable = true)
    public String getTreatmentCourseUnitName() {
        return treatmentCourseUnitName;
    }

    public void setTreatmentCourseUnitName(String treatmentCourseUnitName) {
        this.treatmentCourseUnitName = treatmentCourseUnitName;
    }

    @Comment("药房")
    @Column(name = "receive_org", nullable = true)
    public String getReceiveOrg() {
        return receiveOrg;
    }

    protected void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    @Comment("药房名称")
    @Column(name = "receive_org_name", nullable = true)
    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public CisBaseDrugApply create(CisBaseDrugApplyNto cisBaseDrugApplyNto, Boolean save) {
        BusinessAssert.notNull(cisBaseDrugApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisBaseDrugApplyNto不能为空！");
        super.create(cisBaseDrugApplyNto, save);

        setUsage(cisBaseDrugApplyNto.getUsage());
        setUsageName(cisBaseDrugApplyNto.getUsageName());
        setTreatmentCourse(cisBaseDrugApplyNto.getTreatmentCourse());
        setTreatmentCourseUnit(cisBaseDrugApplyNto.getTreatmentCourseUnit());
        setTreatmentCourseUnitName(cisBaseDrugApplyNto.getTreatmentCourseUnitName());
        setReceiveOrg(cisBaseDrugApplyNto.getReceiveOrg());
        setReceiveOrgName(cisBaseDrugApplyNto.getReceiveOrgName());
//        setSbadmWay(cisBaseDrugApplyNto.getSbadmWay());

//        BusinessAssert.notNull(cisBaseDrugApplyNto.getDetail(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001,"药品明细！");
//        CisDrugApplyDetailNto _cisDrugApplyDetailNto_ = cisBaseDrugApplyNto.getDetail();
//        CisDrugApplyDetail cisDrugApplyDetail = new CisDrugApplyDetail();
//        cisDrugApplyDetail = cisDrugApplyDetail.create(getId(), _cisDrugApplyDetailNto_,getStatusCode());
//        updateChargeDetailID(cisDrugApplyDetail.getId(),cisBaseDrugApplyNto);
        if (!CollectionUtils.isEmpty(cisBaseDrugApplyNto.getDetails())) {
//            String code = StringUtils.join(cisBaseDrugApplyNto.getDetails().stream().map(p -> p.getDrugCode()).toList().toArray(),",");
//            setServiceItemCode(code);

            for (CisDrugApplyDetailNto _cisDrugApplyDetailNto_ : cisBaseDrugApplyNto.getDetails()) {
                CisDrugApplyDetail cisDrugApplyDetail = new CisDrugApplyDetail();
                cisDrugApplyDetail = cisDrugApplyDetail.create(getId(), _cisDrugApplyDetailNto_, getStatusCode(), save);
            }
        }
        return this;
    }

    public void update(CisBaseDrugApplyEto cisBaseDrugApplyEto) {
        super.update(cisBaseDrugApplyEto);
        setUsage(cisBaseDrugApplyEto.getUsage());
        setTreatmentCourse(cisBaseDrugApplyEto.getTreatmentCourse());
        setTreatmentCourseUnit(cisBaseDrugApplyEto.getTreatmentCourseUnit());
        setTreatmentCourseUnitName(cisBaseDrugApplyEto.getTreatmentCourseUnitName());

        if (!CollectionUtils.isEmpty(cisBaseDrugApplyEto.getDetailEtos())) {
            cisBaseDrugApplyEto.getDetailEtos().forEach(eto -> {
                Optional<CisDrugApplyDetail> detail = CisDrugApplyDetail.getCisDrugApplyDetailById(eto.getId());
                detail.get().update(eto);
            });
        }

        if (!CollectionUtils.isEmpty(cisBaseDrugApplyEto.getDetailNtos())) {
            cisBaseDrugApplyEto.getDetailNtos().forEach(nto -> {
                CisDrugApplyDetail detail = new CisDrugApplyDetail();
                detail.create(getId(), nto, getStatusCode(), true);
            });
        }
    }

    public void delete() {
        super.delete();
    }

//    /**
//     * 合并组内费用项。
//     * 根据申请ID、费用项代码列表和详情ID列表，从现有的费用项列表中筛选出匹配的费用项，并更新它们的申请ID。
//     * 此方法主要用于处理费用项合并的场景，确保相同申请下的费用项能够被正确地聚合。
//     *
//     * @param applyId   申请ID，用于更新费用项的申请ID。
//     * @param codes     费用项代码列表，用于筛选费用项。
//     * @param detailIds 详情ID列表，用于进一步筛选费用项。
//     * @return 返回筛选并更新后的费用项列表。
//     */
//    private List<CisApplyCharge> mergeGroupCharge(String applyId, List<String> codes, List<String> detailIds) {
//        // 获取所有的费用项列表
//        List<CisApplyCharge> cisApplyCharges = getCisApplyCharges();
//        // 如果费用项列表为空，则直接返回
//        if (CollectionUtils.isEmpty(cisApplyCharges)) {
//            return cisApplyCharges;
//        }
//
//        // 过滤费用项列表，仅保留代码在codes列表中或详情ID在detailIds列表中的费用项
//        cisApplyCharges = cisApplyCharges.stream()
//                .filter(cisApplyCharge -> codes.contains(cisApplyCharge.getPriceItemCode())
//                        || (!CollectionUtils.isEmpty(detailIds) && detailIds.contains(cisApplyCharge.getDetailId()))
//                ).toList();
//        // 如果过滤后的费用项列表为空，则直接返回
//        if (CollectionUtils.isEmpty(cisApplyCharges)) {
//            return cisApplyCharges;
//        }
//
//        // 更新费用项的申请ID为传入的applyId
//        cisApplyCharges.stream().forEach(p -> p.updateApplyId(applyId));
//
//        // 返回更新后的费用项列表
//        return cisApplyCharges;
//    }

    protected List<String> getSplitCodes(List<CisDrugApplyDetail> applyDetails) {
        return applyDetails.stream()
                .filter(e -> EDrugSystemTypeExtEnum.COMMON.getCode().equals(e.getExtTypeCode()))
                .map(p -> p.getDrugCode()).toList();
    }

    @Transient
    @Override
    public List<CisDrugApplyDetail> getDetailList() {
        var items = CisDrugApplyDetail.getByCisBaseDrugApplyId(getId());
        if (CollectionUtil.isNotEmpty(CisDrugApplyDetail.getByCisBaseDrugApplyId(getId()))) {
            return items;
        }
        return details;
    }

    @Override
    protected void changeDetialApplyCode(String applyCode) {
        if (CollectionUtils.isEmpty(getDetailList())) {
            return;
        }
        List<String> ids = getDetailList().stream().map(p -> p.getId()).toList();
        CisDrugApplyDetail.updateApplyIdByIdsIn(ids, applyCode);
    }

    protected void changeDetialApplyCode(String newOrderId, String newApplyId, List<String> detailids) {
        if (CollectionUtils.isEmpty(detailids)) {
            return;
        }
        CisDrugApplyDetail.updateApplyIdByIdsIn(detailids, newApplyId);
    }
}
