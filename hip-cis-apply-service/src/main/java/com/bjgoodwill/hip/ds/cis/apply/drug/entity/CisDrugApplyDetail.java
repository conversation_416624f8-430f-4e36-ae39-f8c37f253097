package com.bjgoodwill.hip.ds.cis.apply.drug.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.BaseDetail;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.repository.CisDrugApplyDetailRepository;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisDrugApplyDetailEto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisDrugApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisDrugApplyDetailQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import com.google.common.collect.Lists;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "药品明细")
@Table(name = "cis_drug_apply_detail", indexes = {@Index(name = "cis_drug_apply_detail_apply_id", columnList = "apply_id")
        , @Index(name = "cis_drug_apply_detail_visit_code", columnList = "visit_code")}, uniqueConstraints = {})
public class CisDrugApplyDetail extends BaseDetail {

    //    // 标识
//    private String id;
//    // 就诊流水号
//    private String visitCode;
//    // 药品申请单标识
//    private String cisBaseDrugApplyId;
//    // 序号
//    private Double sortNo;
    // 药品编码
    private String drugCode;
    // 药品名称
    private String drugName;
    // 每次剂量
    private Double dosage;
    // 剂量单位 字典DosageUnit
    private String dosageUnit;
    // 剂量单位名称 字典DosageUnit
    private String dosageUnitName;
    // 包装总量
    private Double packageNum;
    // 包装单位 MinUnit/PackageUnit
    private String packageUnit;
    // 包装单位名称 MinUnit/PackageUnit
    private String packageUnitName;
    // 领药科室
    private String receiveOrg;
    // 领药科室名称
    private String receiveOrgName;
    //    private DrugInoutTypeEnum drugInoutType;
    private SbadmWayEnum sbadmWay;
    // 是否皮试
    private Boolean isSkin;
    // 皮试结果
    private String skinResult;
    // 抗菌药使用说明:0-预防，1-治疗
    private Integer antimicrobialsPurpose;
    //特殊煎法：字典DecoctMethod
    private String decoctMethodCode;
    //特殊煎法：字典DecoctMethod
    private String decoctMethodName;

    public static Optional<CisDrugApplyDetail> getCisDrugApplyDetailById(String id) {
        return dao().findById(id);
    }

    //    // 状态
//    private CisStatusEnum statusCode;
//
//    //创建时间
//    private LocalDateTime createdDate;
//
//    private LocalDateTime updatedDate;

//    @Id
//    @GeneratedValue(generator = "snowflake_generator")
//    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
//    @Comment("标识")
//    @Column(name = "id", nullable = false, length = 50)
//    public String getId() {
//    	return id;
//    }
//
//    protected void setId(String id) {
//    	this.id = id;
//    }
//
//    @Comment("药品申请单标识")
//    @Column(name = "cis_base_drug_apply_id", nullable = false, length = 50)
//    public String
//    getCisBaseDrugApplyId() {
//    	return cisBaseDrugApplyId;
//    }
//
//    protected void setCisBaseDrugApplyId(String cisBaseDrugApplyId) {
//    	this.cisBaseDrugApplyId = cisBaseDrugApplyId;
//    }

//    @Comment("序号")
//    @Column(name = "sort_no", nullable = true)
//    public Double getSortNo() {
//    	return sortNo;
//    }
//
//    protected void setSortNo(Double sortNo) {
//    	this.sortNo = sortNo;
//    }

//    @Comment("就诊流水号")
//    @Column(name = "visit_code", nullable = false)
//    public String getVisitCode() {
//    	return visitCode;
//    }
//
//    protected void setVisitCode(String visitCode) {
//    	this.visitCode = visitCode;
//    }


//    @Comment("发药方式")
//    @Column(name = "drug_inout_type", nullable = false)
//    public DrugInoutTypeEnum getDrugInoutType() {
//        return drugInoutType;
//    }
//
//    public void setDrugInoutType(DrugInoutTypeEnum drugInoutType) {
//        this.drugInoutType = drugInoutType;
//    }

    public static List<CisDrugApplyDetail> getCisDrugApplyDetails(String cisBaseDrugApplyId, CisDrugApplyDetailQto qto) {
        if (cisBaseDrugApplyId != null) {
            qto.setCisBaseDrugApplyId(cisBaseDrugApplyId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisDrugApplyDetail> getCisDrugApplyDetailPage(String cisBaseDrugApplyId, CisDrugApplyDetailQto qto) {

        if (cisBaseDrugApplyId != null) {
            qto.setCisBaseDrugApplyId(cisBaseDrugApplyId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisDrugApplyDetail> getByCisBaseDrugApplyId(String cisBaseDrugApplyId) {
        return dao().findByApplyId(cisBaseDrugApplyId);
    }

    public static List<CisDrugApplyDetail> getByCisBaseDrugApplyIds(List<String> applyIds) {
        return Lists.partition(applyIds, 100).stream().map(list -> dao().findCisDrugApplyDetailsByApplyIdIn(list))
                .flatMap(List::stream).toList();
    }

    public static void deleteByCisBaseDrugApplyId(String cisBaseDrugApplyId) {
        dao().deleteByApplyId(cisBaseDrugApplyId);
    }

    /**
     * @generated
     */
    private static Specification<CisDrugApplyDetail> getSpecification(CisDrugApplyDetailQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getCisBaseDrugApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisBaseDrugApplyId"), qto.getCisBaseDrugApplyId()));
            }
            if (qto.getSbadmWay() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("sbadmWay"), qto.getSbadmWay()));
            }
            return predicate;
        };
    }
//    @Enumerated(EnumType.STRING)
//    @Comment("状态")
//    @Column(name = "status_code", nullable = false)
//    public CisStatusEnum getStatusCode() {
//        return statusCode;
//    }
//
//    public void setStatusCode(CisStatusEnum statusCode) {
//        this.statusCode = statusCode;
//    }
//
//    @Comment("创建的时间")
//    @Column(name = "created_date", nullable = false)
//    public LocalDateTime getCreatedDate() {
//        return createdDate;
//    }
//
//    protected void setCreatedDate(LocalDateTime createdDate) {
//        this.createdDate = createdDate;
//    }
//
//    @Comment("修改的时间")
//    @Column(name = "updated_date", nullable = false)
//    public LocalDateTime getUpdatedDate() {
//        return updatedDate;
//    }
//
//    protected void setUpdatedDate(LocalDateTime updatedDate) {
//        this.updatedDate = updatedDate;
//    }

    private static CisDrugApplyDetailRepository dao() {
        return SpringUtil.getBean(CisDrugApplyDetailRepository.class);
    }

    public static List<CisDrugApplyDetail> findCisDrugApplyDetailByCreatedDateAfter(LocalDateTime dateTime) {
        return dao().findCisDrugApplyDetailByCreatedDateAfter(dateTime);
    }

    public static List<CisDrugApplyDetail> findCisDrugApplyDetailsByVisitCode(String visitCode) {
        return dao().findCisDrugApplyDetailsByVisitCode(visitCode);
    }

    public static List<CisDrugApplyDetail> findWholeDrugApplyDetailByApplyIdIn(List<String> applyIdList) {
        BusinessAssert.notEmpty(applyIdList, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单id");
        return Lists.partition(applyIdList, 100).stream()
                .map(p -> dao().findCisDrugApplyDetailsByApplyIdInAndSbadmWay(p, SbadmWayEnum.WHOLETAKE))
                .flatMap(List::stream).toList();
//        return dao().findCisDrugApplyDetailsByApplyIdInAndSbadmWay(applyIdList, SbadmWayEnum.WHOLETAKE);
    }

    /**
     * 根据申请单ID列表查询药物申请详情
     * 此方法首先确保传入的申请单ID列表不为空，然后分批处理这些ID，最后合并查询结果并返回
     * 使用分批查询的原因是，如果ID列表非常长，一次性查询可能会导致数据库查询效率低下或超出数据库的参数限制
     * 分批查询可以显著提高查询效率和系统稳定性
     *
     * @param applyIdList 申请单ID列表，用于查询药物申请详情
     * @return 返回一个包含多个CisDrugApplyDetail对象的列表，这些对象与传入的申请单ID列表对应
     */
    public static List<CisDrugApplyDetail> findCisDrugApplyDetailsByApplyIdIn(List<String> applyIdList) {
        BusinessAssert.notEmpty(applyIdList, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单id");
        return Lists.partition(applyIdList, 100).stream().map(p -> dao().findCisDrugApplyDetailsByApplyIdIn(p))
                .flatMap(List::stream).toList();
//        return dao().findCisDrugApplyDetailsByApplyIdIn(applyIdList);
    }

    public static void updateApplyIdByIdsIn(List<String> ids, String applyId) {
        dao().updateApplyIdByIdsIn(ids, applyId);
    }

    @Comment("药品编码")
    @Column(name = "drug_code", nullable = false)
    public String getDrugCode() {
        return drugCode;
    }

    protected void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    @Comment("药品名称")
    @Column(name = "drug_name", nullable = true)
    public String getDrugName() {
        return drugName;
    }

    protected void setDrugName(String drugName) {
        this.drugName = drugName;
    }

    @Comment("每次剂量")
    @Column(name = "dosage", nullable = false)
    public Double getDosage() {
        return dosage;
    }

    protected void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    @Comment("剂量单位 字典DosageUnit")
    @Column(name = "dosage_unit", nullable = true)
    public String getDosageUnit() {
        return dosageUnit;
    }

    protected void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    @Comment("包装总量")
    @Column(name = "package_num", nullable = true)
    public Double getPackageNum() {
        return packageNum;
    }

    protected void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    @Comment("包装单位 MinUnit/PackageUnit")
    @Column(name = "package_unit", nullable = true)
    public String getPackageUnit() {
        return packageUnit;
    }

    protected void setPackageUnit(String packageUnit) {
        this.packageUnit = packageUnit;
    }

    @Comment("领药科室")
    @Column(name = "receive_org", nullable = false)
    public String getReceiveOrg() {
        return receiveOrg;
    }

    protected void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    @Comment("取药方式")
    @Column(name = "sbadm_way", nullable = true)
    public SbadmWayEnum getSbadmWay() {
        return sbadmWay;
    }

    public void setSbadmWay(SbadmWayEnum sbadmWay) {
        this.sbadmWay = sbadmWay;
    }

    @Comment("是否皮试")
    @Column(name = "is_skin", nullable = true)
    public Boolean getIsSkin() {
        return isSkin;
    }

    protected void setIsSkin(Boolean isSkin) {
        this.isSkin = isSkin;
    }

    @Comment("皮试结果")
    @Column(name = "skin_result", nullable = true)
    public String getSkinResult() {
        return skinResult;
    }

    protected void setSkinResult(String skinResult) {
        this.skinResult = skinResult;
    }

    @Comment("抗菌药使用说明:0-预防，1-治疗")
    @Column(name = "antimicrobials_purpose", nullable = true)
    public Integer getAntimicrobialsPurpose() {
        return antimicrobialsPurpose;
    }

    protected void setAntimicrobialsPurpose(Integer antimicrobialsPurpose) {
        this.antimicrobialsPurpose = antimicrobialsPurpose;
    }

    @Comment("特殊煎法：字典DecoctMethod")
    @Column(name = "decoct_method_code", nullable = true)
    public String getDecoctMethodCode() {
        return decoctMethodCode;
    }

    public void setDecoctMethodCode(String decoctMethodCode) {
        this.decoctMethodCode = decoctMethodCode;
    }

    @Comment("剂量单位 字典DosageUnit")
    @Column(name = "dosage_unit_name", nullable = true)
    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    @Comment("包装单位 MinUnit/PackageUnit")
    @Column(name = "package_unit_name", nullable = true)
    public String getPackageUnitName() {
        return packageUnitName;
    }

    public void setPackageUnitName(String packageUnitName) {
        this.packageUnitName = packageUnitName;
    }

    @Comment("领药科室名称")
    @Column(name = "receive_org_name", nullable = true)
    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    @Comment("特殊煎法：字典DecoctMethod")
    @Column(name = "decoct_method_name", nullable = true)
    public String getDecoctMethodName() {
        return decoctMethodName;
    }

    public void setDecoctMethodName(String decoctMethodName) {
        this.decoctMethodName = decoctMethodName;
    }

    @Transient
    public CisBaseDrugApply getCisBaseDrugApply() {
        return CisBaseDrugApply.getCisBaseDrugApplyById(getApplyId()).orElse(null);
    }

    @Transient
    @Override
    public String getServiceItemCode() {
        return this.getDrugCode();
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisDrugApplyDetail other = (CisDrugApplyDetail) obj;
        return Objects.equals(id, other.id);
    }

    @Override
    public CisDrugApplyDetail create(String applyId, DetailNto baseDetailNto, CisStatusEnum statusEnum, Boolean save) {
        CisDrugApplyDetailNto cisDrugApplyDetailNto = (CisDrugApplyDetailNto) baseDetailNto;
        return this.create(applyId, cisDrugApplyDetailNto, statusEnum, save);
    }

    public CisDrugApplyDetail create(String cisBaseDrugApplyId, CisDrugApplyDetailNto cisDrugApplyDetailNto, CisStatusEnum statusEnum, Boolean save) {
        BusinessAssert.notNull(cisDrugApplyDetailNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisDrugApplyDetailNto不能为空！");
        super.create(cisBaseDrugApplyId, cisDrugApplyDetailNto, statusEnum, save);

        setDrugCode(cisDrugApplyDetailNto.getDrugCode());
        setDrugName(cisDrugApplyDetailNto.getDrugName());
        setDosage(cisDrugApplyDetailNto.getDosage());
        setDosageUnit(cisDrugApplyDetailNto.getDosageUnit());
        setDosageUnitName(cisDrugApplyDetailNto.getDosageUnitName());
        setPackageNum(cisDrugApplyDetailNto.getPackageNum());
        setPackageUnit(cisDrugApplyDetailNto.getPackageUnit());
        setPackageUnitName(cisDrugApplyDetailNto.getPackageUnitName());
        setReceiveOrg(cisDrugApplyDetailNto.getReceiveOrg());
        setReceiveOrgName(cisDrugApplyDetailNto.getReceiveOrgName());
        setSbadmWay(cisDrugApplyDetailNto.getSbadmWay());
        setIsSkin(cisDrugApplyDetailNto.getIsSkin());
        setSkinResult(cisDrugApplyDetailNto.getSkinResult());
        setAntimicrobialsPurpose(cisDrugApplyDetailNto.getAntimicrobialsPurpose());
        setDecoctMethodCode(cisDrugApplyDetailNto.getDecoctMethodCode());
        setDecoctMethodName(cisDrugApplyDetailNto.getDecoctMethodName());
        if (save) {
            dao().save(this);
        }

        return this;
    }

    public void update(CisDrugApplyDetailEto cisDrugApplyDetailEto) {
        super.update(cisDrugApplyDetailEto);
        setSortNo(cisDrugApplyDetailEto.getSortNo());
        setDrugCode(cisDrugApplyDetailEto.getDrugCode());
        setDrugName(cisDrugApplyDetailEto.getDrugName());
        setDosage(cisDrugApplyDetailEto.getDosage());
        setDosageUnit(cisDrugApplyDetailEto.getDosageUnit());
        setDosageUnitName(cisDrugApplyDetailEto.getDosageUnitName());
        setPackageNum(cisDrugApplyDetailEto.getPackageNum());
        setPackageUnit(cisDrugApplyDetailEto.getPackageUnit());
        setPackageUnitName(cisDrugApplyDetailEto.getPackageUnitName());
        setReceiveOrg(cisDrugApplyDetailEto.getReceiveOrg());
        setReceiveOrgName(cisDrugApplyDetailEto.getReceiveOrgName());
        setSbadmWay(cisDrugApplyDetailEto.getSbadmWay());
        setAntimicrobialsPurpose(cisDrugApplyDetailEto.getAntimicrobialsPurpose());
        setDecoctMethodCode(cisDrugApplyDetailEto.getDecoctMethodCode());
        setDecoctMethodName(cisDrugApplyDetailEto.getDecoctMethodName());
        setUpdatedDate(LocalDateTime.now());
    }

    public void delete() {
        dao().delete(this);
    }

    public void updateApplyId(String cisBaseDrugApplyId) {
        setApplyId(cisBaseDrugApplyId);
    }

    @Override
    public List<BaseDetail> queryDetailsByCreateDate(LocalDateTime dateTime) {
        return dao().queryDetailsByCreateDate(dateTime);
    }
}
