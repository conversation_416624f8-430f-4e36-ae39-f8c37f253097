package com.bjgoodwill.hip.ds.cis.apply.material.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.material.repository.CisMaterialApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.material.to.CisMaterialApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.material.to.CisMaterialApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.material.to.CisMaterialApplyQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "材料")
@DiscriminatorValue("11")
public class CisMaterialApply extends CisBaseApply {

    // 高值
    private Boolean highFlag;
    // 高值编码
    private String barCode;

    public static Optional<CisMaterialApply> getCisMaterialApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisMaterialApply> getCisMaterialApplies(CisMaterialApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisMaterialApply> getCisMaterialApplyPage(CisMaterialApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisMaterialApply> getSpecification(CisMaterialApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            return predicate;
        };
    }

    private static CisMaterialApplyRepository dao() {
        return SpringUtil.getBean(CisMaterialApplyRepository.class);
    }

    @Comment("高值")
    @Column(name = "high_flag", nullable = true)
    public Boolean getHighFlag() {
        return highFlag;
    }

    protected void setHighFlag(Boolean highFlag) {
        this.highFlag = highFlag;
    }

    @Comment("高值编码")
    @Column(name = "bar_code", nullable = true)
    public String getBarCode() {
        return barCode;
    }

    protected void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.MATERIAL;
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisMaterialApplyNto) cisBaseApplyNto, save);
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisMaterialApplyEto) cisBaseApplyEto);
    }

    public CisMaterialApply create(CisMaterialApplyNto cisMaterialApplyNto, Boolean save) {
        BusinessAssert.notNull(cisMaterialApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisMaterialApplyNto不能为空！");
        super.create(cisMaterialApplyNto, save);

        BusinessAssert.hasText(cisMaterialApplyNto.getServiceItemCode(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "serviceItemCode");
        setServiceItemCode(cisMaterialApplyNto.getServiceItemCode());

        setHighFlag(cisMaterialApplyNto.getHighFlag());
        setBarCode(cisMaterialApplyNto.getBarCode());
        if (save) {
            dao().save(this);
        }
//        if (cisMaterialApplyNto.getCisApplyCharges() != null) {
//            for (CisApplyChargeNto cisApplyChargeNto_ : cisMaterialApplyNto.getCisApplyCharges()) {
//                CisApplyCharge cisApplyCharge = new CisApplyCharge();
//                cisApplyCharge.create(getId(), cisApplyChargeNto_, save);
//            }
//        }
//        if (cisMaterialApplyNto.getCisOrderExecPlans() != null) {
//            for (CisOrderExecPlanNto cisOrderExecPlanNto_ : cisMaterialApplyNto.getCisOrderExecPlans()) {
//                cisOrderExecPlanNto_.setMainPlanFlag(cisMaterialApplyNto.getExecutorOrgCode().equals(cisOrderExecPlanNto_.getExecOrgCode()));
//                CisOrderExecPlan cisOrderExecPlan = new CisOrderExecPlan();
//                cisOrderExecPlan.create(getId(), cisOrderExecPlanNto_, save);
//            }
//        }
        return this;
    }

    public void update(CisMaterialApplyEto cisMaterialApplyEto) {
        super.update(cisMaterialApplyEto);
        setHighFlag(cisMaterialApplyEto.getHighFlag());
        setBarCode(cisMaterialApplyEto.getBarCode());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
