package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;

import java.time.LocalDateTime;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-16 09:27
 * @className: CisSplitConversion
 * @description:
 **/
public class CisSplitConversion {

    private LocalDateTime beginDate;

    private LocalDateTime endDate;

    private CisBaseApplyNto apply;

    private String visitCode;

    private String applyId;

    private String firstDayTimepoint;
    private String nowWeekDay;
    private String orderId;

    // 构造方法,首次拆分过了 就不要给firstDayTimepoint
    public CisSplitConversion(LocalDateTime beginDate, LocalDateTime endDate, String firstDayTimepoint,
                              CisBaseApplyNto applie) {
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.apply = applie;
        this.visitCode = applie.getVisitCode();
        this.applyId = applie.getId();
        this.firstDayTimepoint = firstDayTimepoint;
        this.orderId = apply.getOrderID();
    }

    public CisSplitConversion(CisBaseApplyNto apply) {

        this.apply = apply;
        this.visitCode = apply.getVisitCode();
        this.applyId = apply.getId();
        this.orderId = apply.getOrderID();
    }

    public String getVisitCode() {
        return visitCode;
    }

    public LocalDateTime getBeginDate() {
        return beginDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public CisBaseApplyNto getApply() {
        return apply;
    }

    public String getApplyId() {
        return applyId;
    }

    public String getFirstDayTimepoint() {
        return firstDayTimepoint;
    }

    public String getNowWeekDay() {
        return nowWeekDay;
    }

    public void setNowWeekDay(String nowWeekDay) {
        this.nowWeekDay = nowWeekDay;
    }

    public String getOrderId() {
        return orderId;
    }

}