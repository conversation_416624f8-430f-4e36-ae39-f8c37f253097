package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisBaseApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.CisOrderExecPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class ApplyUpdateService {

    @Autowired
    private CisOrderExecPlanService cisOrderExecPlanService;

    public CisBaseApplyTo cloneUpdate(CisBaseApplyEto entity, String id) {

        CisBaseApply apply = CisBaseApply.newInstanceByEto(entity);
        Optional<CisBaseApply> cisBaseApplyOptional = CisBaseApply.getCisBaseApplyById(id);
        BusinessAssert.isTrue(cisBaseApplyOptional.isPresent(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "未查询到申请单信息！！");
//        CisBaseApplyAssembler assembler = CisBaseApply.newAssemblerInstanceByEntity(apply);
//
        cisBaseApplyOptional.get().update(entity);
//        CisBaseApplyTo cisBaseApplyTo = assembler.toTo(CisBaseApply.getCisBaseApplyById(id).get());
//
//        return cisBaseApplyTo;
        return null;
    }


    public CisBaseApplyTo update(CisBaseApplyEto entity, String id) {

        CisBaseApply cisBaseApply = CisBaseApply.getCisBaseApplyById(id).orElse(null);
        BusinessAssert.notNull(cisBaseApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "未查询到申请单信息！！");

        if (VisitTypeEnum.IPD.equals(cisBaseApply.getVisitType())) {
            BusinessAssert.isTrue(CisStatusEnum.NEW.equals(cisBaseApply.getStatusCode()), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014,
                    "申请单状态不正确，不能修改！！");
        } else {
            String prescriptionID = cisBaseApply.getPrescriptionID();
            BusinessAssert.hasText(prescriptionID, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单处方号！！");
            ApplyUpdateService applyUpdateService = SpringUtil.getBean(ApplyUpdateService.class);
            //applyUpdateService.backUpCisBaseApply(prescriptionID);
        }
        cisBaseApply.update(entity);
        return CisBaseApplyAssembler.toTo(cisBaseApply);
    }

    /**
     * 回退申请单
     *
     * @param orderIds
     */
    public void backUpCisBaseApply(List<String> orderIds) {
        BusinessAssert.notEmpty(orderIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0002, "回退");

        List<CisBaseApply> applyList = CisBaseApply.findCisBaseAppliesByOrderIDIn(orderIds);

        BusinessAssert.notEmpty(applyList, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0006, "回退");

        List<CisOrderExecPlan> orderExecPlans = CisOrderExecPlan.findCisOrderExecPlansByOrderIdIn(orderIds);

        BusinessAssert.isEmpty(orderExecPlans, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0002, "拆分数据");
        applyList.forEach(CisBaseApply::backeUp);

    }

    /**
     * 回退门诊CisBaseApply记录
     * 当回退处方时，此方法用于处理与处方ID关联的所有CisBaseApply记录
     * 它不仅回退CisBaseApply记录，还删除与这些记录关联的CisOrderExecPlan
     *
     * @param prescriptionIds 处方ID集合，用于查找需要回退的CisBaseApply记录
     */
    public void backUpCisBaseApplyOpd(List<String> prescriptionIds) {
        // 根据处方ID查找所有关联的CisBaseApply记录
        List<CisBaseApply> applyList = CisBaseApply.findCisBaseAppliesByPrescriptionIds(prescriptionIds);
        // 确保找到的CisBaseApply记录不为空，否则抛出异常
        BusinessAssert.notEmpty(applyList, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0006, "回退");

        //需要查询是否有结算
        List<String> orderIds = applyList.stream().map(CisBaseApply::getOrderID).toList();
        BusinessAssert.isEmpty(cisOrderExecPlanService.queryCisOrderExecPlanByOrderIds(orderIds),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0002, "计算执行单数据");

        // 遍历并回退每个CisBaseApply记录
        applyList.forEach(CisBaseApply::backeUp);

        // 根据orderID查找所有关联的CisOrderExecPlan记录
        List<CisOrderExecPlan> orderExecPlans = CisOrderExecPlan.findCisOrderExecPlansByOrderIdIn(orderIds);
        // 遍历并删除每个找到的CisOrderExecPlan记录
        orderExecPlans.forEach(CisOrderExecPlan::delete);
    }
}
