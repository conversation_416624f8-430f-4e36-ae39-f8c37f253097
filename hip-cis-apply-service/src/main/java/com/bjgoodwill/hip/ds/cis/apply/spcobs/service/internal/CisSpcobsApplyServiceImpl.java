package com.bjgoodwill.hip.ds.cis.apply.spcobs.service.internal;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.enums.dict.DictCodeEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.service.internal.ApplyWithDetialServiceImpl;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailTo;
import com.bjgoodwill.hip.ds.cis.apply.proxy.DictElementServiceProxy;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApply;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.service.CisSpcobsApplyService;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.service.internal.assembler.CisSpcobsApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.service.internal.assembler.CisSpcobsApplyDetailAssembler;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.*;
import jodd.util.StringUtil;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisSpcobsApplyService")
@RequestMapping(value = "/api/apply/apply/cisSpcobsApply", produces = "application/json; charset=utf-8")
public class CisSpcobsApplyServiceImpl extends ApplyWithDetialServiceImpl<CisSpcobsApplyDetail> implements CisSpcobsApplyService {

    private DictElementServiceProxy dictElementServiceProxy() {
        return SpringUtil.getBean(DictElementServiceProxy.class);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisSpcobsApplyTo getCisSpcobsApplyById(String id) {
        return CisSpcobsApplyAssembler.toTo(CisSpcobsApply.getCisSpcobsApplyById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisSpcobsApplyTo getCisSpcobsApplyAllById(String id) {
        return CisSpcobsApplyAssembler.toTo(CisSpcobsApply.getCisSpcobsApplyById(id).orElse(null), true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisSpcobsApplyTo createCisSpcobsApply(CisSpcobsApplyNto cisSpcobsApplyNto) {
        CisSpcobsApply cisSpcobsApply = new CisSpcobsApply();
        if (StringUtil.isNotBlank(cisSpcobsApplyNto.getSpeciman())) {
            cisSpcobsApplyNto.setSpecimanName(dictElementServiceProxy().getElementName(DictCodeEnum.标本.getCode(), cisSpcobsApplyNto.getSpeciman()));
        }
        cisSpcobsApply = cisSpcobsApply.create(cisSpcobsApplyNto, true);
        return CisSpcobsApplyAssembler.toTo(cisSpcobsApply);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisSpcobsApply(String id, CisSpcobsApplyEto cisSpcobsApplyEto) {
        Optional<CisSpcobsApply> cisSpcobsApplyOptional = CisSpcobsApply.getCisSpcobsApplyById(id);
        cisSpcobsApplyOptional.ifPresent(cisSpcobsApply -> {
            if (StringUtil.isNotBlank(cisSpcobsApplyEto.getSpeciman())) {
                cisSpcobsApplyEto.setSpecimanName(dictElementServiceProxy().getElementName(DictCodeEnum.标本.getCode(), cisSpcobsApplyEto.getSpeciman()));
            }
            cisSpcobsApply.update(cisSpcobsApplyEto);
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisSpcobsApply(String id) {
        Optional<CisSpcobsApply> cisSpcobsApplyOptional = CisSpcobsApply.getCisSpcobsApplyById(id);
        cisSpcobsApplyOptional.ifPresent(cisSpcobsApply -> cisSpcobsApply.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisSpcobsApplyDetailTo getCisSpcobsApplyDetailById(String id) {
        return CisSpcobsApplyDetailAssembler.toTo(CisSpcobsApplyDetail.getCisSpcobsApplyDetailById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisSpcobsApplyDetailTo createCisSpcobsApplyDetail(String cisSpcobsApplyId, CisSpcobsApplyDetailNto cisSpcobsApplyDetailNto) {
        CisSpcobsApplyDetail cisSpcobsApplyDetail = new CisSpcobsApplyDetail();
        cisSpcobsApplyDetail = cisSpcobsApplyDetail.create(cisSpcobsApplyId, cisSpcobsApplyDetailNto, CisStatusEnum.CLONE, true);
        return CisSpcobsApplyDetailAssembler.toTo(cisSpcobsApplyDetail);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisSpcobsApplyDetail(String id, CisSpcobsApplyDetailEto cisSpcobsApplyDetailEto) {
        Optional<CisSpcobsApplyDetail> cisSpcobsApplyDetailOptional = CisSpcobsApplyDetail.getCisSpcobsApplyDetailById(id);
        cisSpcobsApplyDetailOptional.ifPresent(cisSpcobsApplyDetail -> cisSpcobsApplyDetail.update(cisSpcobsApplyDetailEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisSpcobsApplyDetail(String id) {
        Optional<CisSpcobsApplyDetail> cisSpcobsApplyDetailOptional = CisSpcobsApplyDetail.getCisSpcobsApplyDetailById(id);
        cisSpcobsApplyDetailOptional.ifPresent(cisSpcobsApplyDetail -> cisSpcobsApplyDetail.delete());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisSpcobsApplyDetailTo> getCisSpcobsApplyDetailByApplyId(String applyId) {
        List<CisSpcobsApplyDetail> details = CisSpcobsApplyDetail.getByCisSpcobsApplyId(applyId);
        return CollectionUtils.isEmpty(details) ? Collections.emptyList() : CisSpcobsApplyDetailAssembler.toTos(details);
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<DetailTo> queryDetailToByCreateDate(LocalDateTime dateTime) {
        return new ArrayList<>(CisSpcobsApplyDetailAssembler.toTos(
                new CisSpcobsApplyDetail().queryDetailsByCreateDate(dateTime)
                        .stream().filter(CisSpcobsApplyDetail.class::isInstance)
                        .map(CisSpcobsApplyDetail.class::cast)
                        .toList()));
    }
}