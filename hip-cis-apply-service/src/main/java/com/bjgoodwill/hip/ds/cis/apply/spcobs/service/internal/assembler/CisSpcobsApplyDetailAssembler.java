package com.bjgoodwill.hip.ds.cis.apply.spcobs.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyDetailTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisSpcobsApplyDetailAssembler {

    public static List<CisSpcobsApplyDetailTo> toTos(List<CisSpcobsApplyDetail> cisSpcobsApplyDetails) {
        return toTos(cisSpcobsApplyDetails, false);
    }

    public static List<CisSpcobsApplyDetailTo> toTos(List<CisSpcobsApplyDetail> cisSpcobsApplyDetails, boolean withAllParts) {
        BusinessAssert.notNull(cisSpcobsApplyDetails, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisSpcobsApplyDetails不能为空！");

        List<CisSpcobsApplyDetailTo> tos = new ArrayList<>();
        for (CisSpcobsApplyDetail cisSpcobsApplyDetail : cisSpcobsApplyDetails)
            tos.add(toTo(cisSpcobsApplyDetail, withAllParts));
        return tos;
    }

    public static CisSpcobsApplyDetailTo toTo(CisSpcobsApplyDetail cisSpcobsApplyDetail) {
        return toTo(cisSpcobsApplyDetail, false);
    }

    /**
     * @generated
     */
    public static CisSpcobsApplyDetailTo toTo(CisSpcobsApplyDetail cisSpcobsApplyDetail, boolean withAllParts) {
        if (cisSpcobsApplyDetail == null)
            return null;
        CisSpcobsApplyDetailTo to = new CisSpcobsApplyDetailTo();
        to.setId(cisSpcobsApplyDetail.getId());
        to.setCisSpcobsApplyId(cisSpcobsApplyDetail.getApplyId());
        to.setNo(cisSpcobsApplyDetail.getSortNo());
        to.setSpcobsName(cisSpcobsApplyDetail.getSpcobsName());
        to.setDeviceType(cisSpcobsApplyDetail.getDeviceType());
        to.setDeviceTypeName(cisSpcobsApplyDetail.getDeviceTypeName());
        to.setTestTubeId(cisSpcobsApplyDetail.getTestTubeId());
        to.setMethod(cisSpcobsApplyDetail.getMethod());
        to.setMethodName(cisSpcobsApplyDetail.getMethod());
//        to.setSpeciman(cisSpcobsApplyDetail.getSpeciman());
        to.setSpcobsCode(cisSpcobsApplyDetail.getSpcobsCode());
        to.setExtTypeCode(cisSpcobsApplyDetail.getExtTypeCode());
        if (withAllParts) {
        }
        return to;
    }


    public static List<CisSpcobsApplyDetailNto> toNtos(List<CisSpcobsApplyDetail> cisSpcobsApplyDetails) {
        BusinessAssert.notNull(cisSpcobsApplyDetails, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisSpcobsApplyDetails不能为空！");

        List<CisSpcobsApplyDetailNto> tos = new ArrayList<>();
        for (CisSpcobsApplyDetail cisSpcobsApplyDetail : cisSpcobsApplyDetails)
            tos.add(toNto(cisSpcobsApplyDetail));
        return tos;
    }

    /**
     * @generated
     */
    public static CisSpcobsApplyDetailNto toNto(CisSpcobsApplyDetail cisSpcobsApplyDetail) {
        if (cisSpcobsApplyDetail == null)
            return null;
        CisSpcobsApplyDetailNto to = new CisSpcobsApplyDetailNto();
        to.setId(cisSpcobsApplyDetail.getId());
        to.setSortNo(cisSpcobsApplyDetail.getSortNo());
        to.setSpcobsName(cisSpcobsApplyDetail.getSpcobsName());
        to.setDeviceType(cisSpcobsApplyDetail.getDeviceType());
        to.setDeviceTypeName(cisSpcobsApplyDetail.getDeviceTypeName());
        to.setTestTubeId(cisSpcobsApplyDetail.getTestTubeId());
        to.setMethod(cisSpcobsApplyDetail.getMethod());
        to.setMethodName(cisSpcobsApplyDetail.getMethodName());
//        to.setSpeciman(cisSpcobsApplyDetail.getSpeciman());
        to.setVisitCode(cisSpcobsApplyDetail.getVisitCode());
        to.setSpcobsCode(cisSpcobsApplyDetail.getSpcobsCode());
        return to;
    }
}