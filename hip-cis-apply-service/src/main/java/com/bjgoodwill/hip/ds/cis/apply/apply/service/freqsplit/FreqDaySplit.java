package com.bjgoodwill.hip.ds.cis.apply.apply.service.freqsplit;

import com.bjgoodwill.hip.ds.base.cis.dict.frequency.to.FrequencyTo;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisSplitConversion;
import jodd.util.StringUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-18 10:51
 * @className: FreqDaySplit
 * @description:
 **/
public class FreqDaySplit extends FreqSplit {

    /**
     * 根据起始时间和结束时间，获取第一个时间点。
     * 此方法用于根据给定的首个时间点字符串、起始时间和结束时间，计算出在给定范围内第一个符合时间点规则的时间。
     * 首个时间点字符串格式为“HH-mm-ss”，表示小时、分钟和秒的分割。
     *
     * @param firstDayTimepoint 首个时间点字符串，格式为"HH-mm-ss"。
     * @param beginDate         起始时间。
     * @param endDate           结束时间。
     * @return 返回在给定范围内第一个符合时间点规则的时间列表。
     */
    private List<LocalDateTime> getFirstDayTimepoint(String firstDayTimepoint, LocalDateTime beginDate, LocalDateTime endDate) {
        // 验证输入参数
        if (StringUtil.isNotBlank(firstDayTimepoint)) {
            return Collections.emptyList();
        }

        // 使用更高效的方式分割时间点字符串
        String[] timeParts = firstDayTimepoint.split("-");
        if (timeParts.length != 3) {
            return Collections.emptyList();
        }

        // 直接返回组合后的日期时间列表
        return combinationDateTime(Arrays.asList(timeParts), beginDate);
    }

    /**
     * 根据时间点字符串和日期，组合出完整的日期时间字符串。
     * 此方法用于根据给定的时间点字符串和日期，生成完整的日期时间字符串。
     *
     * @param freqTimepoint 时间点字符串，格式为"HH-mm-ss"。
     * @param dateTime      日期。
     * @return 返回完整的日期时间字符串。
     */
    private List<LocalDateTime> combinationDateTime(List<String> freqTimepoint, LocalDateTime dateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

        return freqTimepoint.stream()
                .filter(Objects::nonNull)
                .map(p -> {
                    try {
                        String[] parts = p.split(":");
                        String timeStr = String.format("%02d:%02d:00",
                                Integer.parseInt(parts[0]),
                                parts.length > 1 ? Integer.parseInt(parts[1]) : 0);
                        return LocalDateTime.parse(
                                dateTime.toLocalDate().toString() + "T" + timeStr,
                                formatter);
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .toList();
    }

    @Override
    protected List<LocalDateTime> splitDate(LocalDateTime lastSplitDate, CisSplitConversion splitConversion, FrequencyTo frequencyTo) {
        List<LocalDateTime> times = new ArrayList<>();
        List<LocalDateTime> firstTimes = new ArrayList<>();
        int freqTime = frequencyTo.getFreqTime();

        //首日点 不为空的话。
        if (StringUtil.isNotBlank(splitConversion.getFirstDayTimepoint())) {
            firstTimes.addAll(getFirstDayTimepoint(splitConversion.getFirstDayTimepoint(), lastSplitDate, splitConversion.getEndDate()));
            //首日时间拆出后 修改拆分开始时间
            LocalDateTime startOfDay = lastSplitDate.withHour(0).withMinute(0).withSecond(0).withNano(0);
            lastSplitDate = startOfDay.plusDays(1);
        }

        long dayNum = (long) ChronoUnit.DAYS.between(lastSplitDate, splitConversion.getEndDate());
        LocalDateTime nextDate = lastSplitDate;
        while (dayNum > 0) {
            times.addAll(combinationDateTime(getTimes(frequencyTo), nextDate));
            nextDate = nextDate.plusDays(freqTime);
            dayNum = dayNum - freqTime;
        }

        LocalDateTime finalLastSplitDate = lastSplitDate;
        firstTimes.addAll(times.stream().filter(time -> time.isBefore(splitConversion.getEndDate()) &&
                time.isAfter(finalLastSplitDate)
        ).toList());

        firstTimes.addAll(getLastDayTimes(frequencyTo, splitConversion.getEndDate()));

        return firstTimes;
    }

    private List<LocalDateTime> getLastDayTimes(FrequencyTo frequencyTo, LocalDateTime endDate) {
        return combinationDateTime(getTimes(frequencyTo), endDate).stream().filter(time -> time.isBefore(endDate)).toList();
    }
}