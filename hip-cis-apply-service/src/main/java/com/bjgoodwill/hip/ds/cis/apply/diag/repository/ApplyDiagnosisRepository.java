package com.bjgoodwill.hip.ds.cis.apply.diag.repository;

import com.bjgoodwill.hip.ds.cis.apply.diag.entity.ApplyDiagnosis;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.apply.diag.repository.ApplyDiagnosisRepository")
public interface ApplyDiagnosisRepository extends JpaRepository<ApplyDiagnosis, String>, JpaSpecificationExecutor<ApplyDiagnosis> {

    List<ApplyDiagnosis> findApplyDiagnosesByApplyId(String applyid);

    Page<ApplyDiagnosis> findByApplyId(String cisBaseApplyId, Pageable pageable);

    boolean existsByApplyId(String cisBaseApplyId);

    @Modifying
    @Query("update ApplyDiagnosis c set c.applyId = ?2 ,c.updatedStaff = ?3  where c.id in ?1")
    void updateApplyIdByIdsIn(List<String> ids, String applyId, String updateStaff);

    @Query("select a from ApplyDiagnosis a where a.applyId in (select b.id from CisBaseApply b where b.visitCode = ?1)")
    List<ApplyDiagnosis> findApplyDiagnosesByVisitCode(String visitCode);
}