package com.bjgoodwill.hip.ds.cis.apply.apply.service.internal;

import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyTangibleService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-08 16:27
 * @className: CisBaseApplyServiceTangibleImpl
 * @description:
 **/
@RestController("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyService")
@RequestMapping(value = "/api/apply/cisBaseApplyService", produces = "application/json; charset=utf-8")
@Tag(name = "申请单领域服务", description = "申请单领域服务")
public class CisBaseApplyServiceTangibleImpl extends CisBaseApplyServiceImpl implements CisBaseApplyTangibleService {


}