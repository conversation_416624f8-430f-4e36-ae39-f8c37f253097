package com.bjgoodwill.hip.ds.cis.apply.proxy;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisCodeRuleEnum;
import com.bjgoodwill.hip.ds.code.generation.api.service.BusinessCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-03-05 15:31
 * @className: ApplyCodeServiceProxy
 * @description: 获取申请单ID
 **/
@Component
public class ApplyCodeServiceProxy {

    @Autowired
    private BusinessCodeService businessCodeService;

    public String getApplyNextCode() {
        return businessCodeService.getMoreRuleNextSequence(CisCodeRuleEnum.ApplyCode.getValue());
    }
}