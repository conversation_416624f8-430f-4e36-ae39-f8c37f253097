package com.bjgoodwill.hip.ds.cis.apply.operation.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.ApplyWithDetial;
import com.bjgoodwill.hip.ds.cis.apply.diag.entity.ApplyDiagnosis;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.apply.operation.repository.CisOperationApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.CisOperationApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.CisOperationApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.CisOperationApplyQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Entity
@Comment(value = "手术申请")
@DiscriminatorValue("07")
public class CisOperationApply extends ApplyWithDetial<CisOperationApplyDetail> {

    // 血型
    private String bloodType;
    // RH血型
    private String bloodTypeRh;
    // 手术分类
    private String operationType;
    // 手术分类名称
    private String operationTypeName;
    // 麻醉类型
    private String anaesthesiaType;
    // 麻醉类型名称
    private String anaesthesiaTypeName;
    // 麻醉方式
    private String anaesthesiaMode;
    // 麻醉方式名称
    private String anaesthesiaModeName;
    //  特殊手术属性
    private String operationSpecialAttr;
    // 拟手术日期
    private LocalDateTime operationDate;
    // 拟预计用时
    private Double operationTime;
    // 切口类型
    private String incisionType;
    // 切口等级 字典WoundGrade
    private String incisionLevel;
    // 切口等级名称 字典WoundGrade
    private String incisionLevelName;
    // 合并手术
    private Boolean mergeFlag;
    // 手术审批类型：1常规手术，2急诊手术；默认1
    private String apprOperTypc;
    // 手术医生
    private String operationDoctor;
    // 手术医生名称
    private String operationDoctorName;
    // 手术医生科室
    private String operationDoctorOrg;
    // 手术医生科室名称
    private String operationDoctorOrgName;
    // 第一助手编码
    private String firstAidesCode;
    // 第一助手名称
    private String firstAidesName;
    // 第二助手编码
    private String secondAidesCode;
    // 第二助手名称
    private String secondAidesName;
    // 第三助手编码
    private String thirdAidesCode;
    // 第三助手名称
    private String thirdAidesName;
    // 第四助手编码
    private String fourthAidesCode;
    // 第四助手名称
    private String fourthAidesName;
    // 基本操作
    private String operation;
    // 入路
    private String approach;
    // 入路名称
    private String approachName;
    // 辅助器械
    private String instrument;
    // 辅助器械名称
    private String instrumentName;
    // 协作科室
    private String coopDept;
    // 非计划再次重组手术
    private Boolean reorganize;
    // 术后计划转ICU
    private Boolean transferIcu;
    // 术中冰冻病理检查
    private Boolean freezePathology;

    public static Optional<CisOperationApply> getCisOperationApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisOperationApply> getCisOperationApplies(CisOperationApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisOperationApply> getCisOperationApplyPage(CisOperationApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisOperationApply> getSpecification(CisOperationApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            return predicate;
        };
    }

    private static CisOperationApplyRepository dao() {
        return SpringUtil.getBean(CisOperationApplyRepository.class);
    }

    @Comment("血型")
    @Column(name = "blood_type", nullable = true)
    public String getBloodType() {
        return bloodType;
    }

    protected void setBloodType(String bloodType) {
        this.bloodType = bloodType;
    }

    @Comment("RH血型")
    @Column(name = "blood_type_rh", nullable = true)
    public String getBloodTypeRh() {
        return bloodTypeRh;
    }

    protected void setBloodTypeRh(String bloodTypeRh) {
        this.bloodTypeRh = bloodTypeRh;
    }

    @Comment("手术分类")
    @Column(name = "operation_type", nullable = true)
    public String getOperationType() {
        return operationType;
    }

    protected void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.OPERATION;
    }

    @Comment("麻醉类型")
    @Column(name = "anaesthesia_type", nullable = true)
    public String getAnaesthesiaType() {
        return anaesthesiaType;
    }

    protected void setAnaesthesiaType(String anaesthesiaType) {
        this.anaesthesiaType = anaesthesiaType;
    }

    @Comment("麻醉方式")
    @Column(name = "anaesthesia_mode", nullable = true)
    public String getAnaesthesiaMode() {
        return anaesthesiaMode;
    }

    protected void setAnaesthesiaMode(String anaesthesiaMode) {
        this.anaesthesiaMode = anaesthesiaMode;
    }

    @Comment(" 特殊手术属性")
    @Column(name = "operation_special_attr", nullable = true)
    public String getOperationSpecialAttr() {
        return operationSpecialAttr;
    }

    protected void setOperationSpecialAttr(String operationSpecialAttr) {
        this.operationSpecialAttr = operationSpecialAttr;
    }

    @Comment("拟手术日期")
    @Column(name = "operation_date", nullable = true)
    public LocalDateTime getOperationDate() {
        return operationDate;
    }

    protected void setOperationDate(LocalDateTime operationDate) {
        this.operationDate = operationDate;
    }

    @Comment("拟预计用时")
    @Column(name = "operation_time", nullable = true)
    public Double getOperationTime() {
        return operationTime;
    }

    protected void setOperationTime(Double operationTime) {
        this.operationTime = operationTime;
    }

    @Comment("切口类型")
    @Column(name = "incision_type", nullable = true)
    public String getIncisionType() {
        return incisionType;
    }

    protected void setIncisionType(String incisionType) {
        this.incisionType = incisionType;
    }

    @Comment("切口等级 字典WoundGrade")
    @Column(name = "incision_level", nullable = true)
    public String getIncisionLevel() {
        return incisionLevel;
    }

    protected void setIncisionLevel(String incisionLevel) {
        this.incisionLevel = incisionLevel;
    }

    @Comment("合并手术")
    @Column(name = "merge_flag", nullable = true)
    public Boolean getMergeFlag() {
        return mergeFlag;
    }

    protected void setMergeFlag(Boolean mergeFlag) {
        this.mergeFlag = mergeFlag;
    }

    @Comment("手术审批类型：1常规手术，2急诊手术；默认1")
    @Column(name = "appr_oper_typc", nullable = true)
    public String getApprOperTypc() {
        return apprOperTypc;
    }

    protected void setApprOperTypc(String apprOperTypc) {
        this.apprOperTypc = apprOperTypc;
    }

    @Comment("手术医生")
    @Column(name = "operation_doctor", nullable = true)
    public String getOperationDoctor() {
        return operationDoctor;
    }

    protected void setOperationDoctor(String operationDoctor) {
        this.operationDoctor = operationDoctor;
    }

    @Comment("手术医生科室")
    @Column(name = "operation_doctor_org", nullable = true)
    public String getOperationDoctorOrg() {
        return operationDoctorOrg;
    }

    protected void setOperationDoctorOrg(String operationDoctorOrg) {
        this.operationDoctorOrg = operationDoctorOrg;
    }

    @Comment("基本操作")
    @Column(name = "operation", nullable = true)
    public String getOperation() {
        return operation;
    }

    protected void setOperation(String operation) {
        this.operation = operation;
    }

    @Comment("入路")
    @Column(name = "approach", nullable = true)
    public String getApproach() {
        return approach;
    }

    protected void setApproach(String approach) {
        this.approach = approach;
    }

    @Comment("辅助器械")
    @Column(name = "instrument", nullable = true)
    public String getInstrument() {
        return instrument;
    }

    protected void setInstrument(String instrument) {
        this.instrument = instrument;
    }

    @Comment("手术分类名称")
    @Column(name = "operation_type_name", nullable = true)
    public String getOperationTypeName() {
        return operationTypeName;
    }

    public void setOperationTypeName(String operationTypeName) {
        this.operationTypeName = operationTypeName;
    }

    @Comment("麻醉类型名称")
    @Column(name = "anaesthesia_type_name", nullable = true)
    public String getAnaesthesiaTypeName() {
        return anaesthesiaTypeName;
    }

    public void setAnaesthesiaTypeName(String anaesthesiaTypeName) {
        this.anaesthesiaTypeName = anaesthesiaTypeName;
    }

    @Comment("麻醉方式名称")
    @Column(name = "anaesthesia_mode_name", nullable = true)
    public String getAnaesthesiaModeName() {
        return anaesthesiaModeName;
    }

    public void setAnaesthesiaModeName(String anaesthesiaModeName) {
        this.anaesthesiaModeName = anaesthesiaModeName;
    }

    @Comment("切口等级名称 字典WoundGrade")
    @Column(name = "incision_level_name", nullable = true)
    public String getIncisionLevelName() {
        return incisionLevelName;
    }

    public void setIncisionLevelName(String incisionLevelName) {
        this.incisionLevelName = incisionLevelName;
    }

    @Comment("手术医生名称")
    @Column(name = "operation_doctor_name", nullable = true)
    public String getOperationDoctorName() {
        return operationDoctorName;
    }

    public void setOperationDoctorName(String operationDoctorName) {
        this.operationDoctorName = operationDoctorName;
    }

    @Comment("手术医生科室名称")
    @Column(name = "operation_doctor_org_name", nullable = true)
    public String getOperationDoctorOrgName() {
        return operationDoctorOrgName;
    }

    public void setOperationDoctorOrgName(String operationDoctorOrgName) {
        this.operationDoctorOrgName = operationDoctorOrgName;
    }

    @Comment("入路名称")
    @Column(name = "approach_name", nullable = true)
    public String getApproachName() {
        return approachName;
    }

    public void setApproachName(String approachName) {
        this.approachName = approachName;
    }

    @Comment("辅助器械名称")
    @Column(name = "instrument_name", nullable = true)
    public String getInstrumentName() {
        return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }

    @Comment("第一助手编码")
    @Column(name = "first_aides_code", nullable = true)
    public String getFirstAidesCode() {
        return firstAidesCode;
    }

    public void setFirstAidesCode(String firstAidesCode) {
        this.firstAidesCode = firstAidesCode;
    }

    @Comment("第一助手名称")
    @Column(name = "first_aides_name", nullable = true)
    public String getFirstAidesName() {
        return firstAidesName;
    }

    public void setFirstAidesName(String firstAidesName) {
        this.firstAidesName = firstAidesName;
    }

    @Comment("第二助手编码")
    @Column(name = "second_aides_code", nullable = true)
    public String getSecondAidesCode() {
        return secondAidesCode;
    }

    public void setSecondAidesCode(String secondAidesCode) {
        this.secondAidesCode = secondAidesCode;
    }

    @Comment("第二助手名称")
    @Column(name = "second_aides_name", nullable = true)
    public String getSecondAidesName() {
        return secondAidesName;
    }

    public void setSecondAidesName(String secondAidesName) {
        this.secondAidesName = secondAidesName;
    }

    @Comment("第三助手编码")
    @Column(name = "third_aides_code", nullable = true)
    public String getThirdAidesCode() {
        return thirdAidesCode;
    }

    public void setThirdAidesCode(String thirdAidesCode) {
        this.thirdAidesCode = thirdAidesCode;
    }

    @Comment("第三助手名称")
    @Column(name = "third_aides_name", nullable = true)
    public String getThirdAidesName() {
        return thirdAidesName;
    }

    public void setThirdAidesName(String thirdAidesName) {
        this.thirdAidesName = thirdAidesName;
    }

    @Comment("第四助手编码")
    @Column(name = "fourth_aides_code", nullable = true)
    public String getFourthAidesCode() {
        return fourthAidesCode;
    }

    public void setFourthAidesCode(String fourthAidesCode) {
        this.fourthAidesCode = fourthAidesCode;
    }

    @Comment("第四助手名称")
    @Column(name = "fourth_aides_name", nullable = true)
    public String getFourthAidesName() {
        return fourthAidesName;
    }

    public void setFourthAidesName(String fourthAidesName) {
        this.fourthAidesName = fourthAidesName;
    }

    @Comment("协作科室")
    @Column(name = "coop_dept", nullable = true)
    public String getCoopDept() {
        return coopDept;
    }

    public void setCoopDept(String coopDept) {
        this.coopDept = coopDept;
    }

    @Comment("非计划再次重组手术")
    @Column(name = "reorganize", nullable = true)
    public Boolean getReorganize() {
        return reorganize;
    }

    public void setReorganize(Boolean reorganize) {
        this.reorganize = reorganize;
    }

    @Comment("术后计划转ICU")
    @Column(name = "transfer_icu", nullable = true)
    public Boolean getTransferIcu() {
        return transferIcu;
    }

    public void setTransferIcu(Boolean transferIcu) {
        this.transferIcu = transferIcu;
    }

    @Comment("术中冰冻病理检查")
    @Column(name = "freeze_pathology", nullable = true)
    public Boolean getFreezePathology() {
        return freezePathology;
    }

    public void setFreezePathology(Boolean freezePathology) {
        this.freezePathology = freezePathology;
    }

    @Override
    @Transient
    public List<CisOperationApplyDetail> getDetailList() {
        return CisOperationApplyDetail.findByApplyId(getId());
    }

    @Override
    protected List<String> getSplitCodes(List<CisOperationApplyDetail> applyDetails) {
        return applyDetails.stream().map(p -> p.getOperationCode()).toList();
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisOperationApplyNto) cisBaseApplyNto, save);
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisOperationApplyEto) cisBaseApplyEto);
    }

    public CisOperationApply create(CisOperationApplyNto cisOperationApplyNto, Boolean save) {
        BusinessAssert.notNull(cisOperationApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisOperationApplyNto不能为空！");
        super.create(cisOperationApplyNto, save);

        setBloodType(cisOperationApplyNto.getBloodType());
        setBloodTypeRh(cisOperationApplyNto.getBloodTypeRh());
        setOperationType(cisOperationApplyNto.getOperationType());
        setOperationTypeName(cisOperationApplyNto.getOperationTypeName());
        setAnaesthesiaType(cisOperationApplyNto.getAnaesthesiaType());
        setAnaesthesiaTypeName(cisOperationApplyNto.getAnaesthesiaTypeName());
        setAnaesthesiaMode(cisOperationApplyNto.getAnaesthesiaMode());
        setAnaesthesiaModeName(cisOperationApplyNto.getAnaesthesiaModeName());
        setOperationSpecialAttr(cisOperationApplyNto.getOperationSpecialAttr());
        setOperationDate(cisOperationApplyNto.getOperationDate());
        setOperationTime(cisOperationApplyNto.getOperationTime());
        setIncisionType(cisOperationApplyNto.getIncisionType());
        setIncisionLevel(cisOperationApplyNto.getIncisionLevel());
        setIncisionLevelName(cisOperationApplyNto.getIncisionLevelName());
        setMergeFlag(cisOperationApplyNto.getMergeFlag());
        setApprOperTypc(cisOperationApplyNto.getApprOperTypc());
        setOperationDoctor(cisOperationApplyNto.getOperationDoctor());
        setOperationDoctorName(cisOperationApplyNto.getOperationDoctorName());
        setOperationDoctorOrg(cisOperationApplyNto.getOperationDoctorOrg());
        setOperationDoctorOrgName(cisOperationApplyNto.getOperationDoctorOrgName());
        setOperation(cisOperationApplyNto.getOperation());
        setApproach(cisOperationApplyNto.getApproach());
        setApproachName(cisOperationApplyNto.getApproachName());
        setInstrument(cisOperationApplyNto.getInstrument());
        setInstrumentName(cisOperationApplyNto.getInstrumentName());
        setFirstAidesCode(cisOperationApplyNto.getFirstAidesCode());
        setFirstAidesName(cisOperationApplyNto.getFirstAidesName());
        setSecondAidesCode(cisOperationApplyNto.getSecondAidesCode());
        setSecondAidesName(cisOperationApplyNto.getSecondAidesName());
        setThirdAidesCode(cisOperationApplyNto.getThirdAidesCode());
        setThirdAidesName(cisOperationApplyNto.getThirdAidesName());
        setFourthAidesCode(cisOperationApplyNto.getFourthAidesCode());
        setFourthAidesName(cisOperationApplyNto.getFourthAidesName());
        setCoopDept(cisOperationApplyNto.getCoopDept());
        setReorganize(cisOperationApplyNto.getReorganize());
        setTransferIcu(cisOperationApplyNto.getTransferIcu());
        setFreezePathology(cisOperationApplyNto.getFreezePathology());
        if (save) {
            dao().save(this);
        }
        if (cisOperationApplyNto.getDetails() != null) {
            cisOperationApplyNto.getDetails().forEach(applyDetailNto -> {
                CisOperationApplyDetail cisOperationApplyDetail = new CisOperationApplyDetail();
                cisOperationApplyDetail.create(getId(), applyDetailNto, getStatusCode(), save);
            });
        }
        if (cisOperationApplyNto.getApplyDiagnosisNtos() != null) {
            cisOperationApplyNto.getApplyDiagnosisNtos().forEach(applyDiagnosisNto -> {
                ApplyDiagnosis addDiagnosis = new ApplyDiagnosis();
                applyDiagnosisNto.setCisBaseApplyId(getId());
                addDiagnosis.create(applyDiagnosisNto);
            });
        }
        return this;
    }

    public void update(CisOperationApplyEto cisOperationApplyEto) {
        super.update(cisOperationApplyEto);
        setBloodType(cisOperationApplyEto.getBloodType());
        setBloodTypeRh(cisOperationApplyEto.getBloodTypeRh());
        setOperationType(cisOperationApplyEto.getOperationType());
        setOperationTypeName(cisOperationApplyEto.getOperationTypeName());
        setAnaesthesiaType(cisOperationApplyEto.getAnaesthesiaType());
        setAnaesthesiaTypeName(cisOperationApplyEto.getAnaesthesiaTypeName());
        setAnaesthesiaMode(cisOperationApplyEto.getAnaesthesiaMode());
        setAnaesthesiaModeName(cisOperationApplyEto.getAnaesthesiaModeName());
        setOperationSpecialAttr(cisOperationApplyEto.getOperationSpecialAttr());
        setOperationDate(cisOperationApplyEto.getOperationDate());
        setOperationTime(cisOperationApplyEto.getOperationTime());
        setIncisionLevelName(cisOperationApplyEto.getIncisionLevelName());
        setIncisionType(cisOperationApplyEto.getIncisionType());
        setIncisionLevel(cisOperationApplyEto.getIncisionLevel());
        setMergeFlag(cisOperationApplyEto.getMergeFlag());
        setApprOperTypc(cisOperationApplyEto.getApprOperTypc());
        setOperationDoctor(cisOperationApplyEto.getOperationDoctor());
        setOperationDoctorName(cisOperationApplyEto.getOperationDoctorName());
        setOperationDoctorOrg(cisOperationApplyEto.getOperationDoctorOrg());
        setOperationDoctorOrgName(cisOperationApplyEto.getOperationDoctorOrgName());
        setOperation(cisOperationApplyEto.getOperation());
        setApproach(cisOperationApplyEto.getApproach());
        setApproachName(cisOperationApplyEto.getApproachName());
        setInstrument(cisOperationApplyEto.getInstrument());
        setInstrumentName(cisOperationApplyEto.getInstrumentName());
        setFirstAidesCode(cisOperationApplyEto.getFirstAidesCode());
        setFirstAidesName(cisOperationApplyEto.getFirstAidesName());
        setSecondAidesCode(cisOperationApplyEto.getSecondAidesCode());
        setSecondAidesName(cisOperationApplyEto.getSecondAidesName());
        setThirdAidesCode(cisOperationApplyEto.getThirdAidesCode());
        setThirdAidesName(cisOperationApplyEto.getThirdAidesName());
        setFourthAidesCode(cisOperationApplyEto.getFourthAidesCode());
        setFourthAidesName(cisOperationApplyEto.getFourthAidesName());
        setCoopDept(cisOperationApplyEto.getCoopDept());
        setReorganize(cisOperationApplyEto.getReorganize());
        setTransferIcu(cisOperationApplyEto.getTransferIcu());
        setFreezePathology(cisOperationApplyEto.getFreezePathology());
        if (!CollectionUtils.isEmpty(cisOperationApplyEto.getDetailEtos())) {
            cisOperationApplyEto.getDetailEtos().forEach(detailEtos -> {
                Optional<CisOperationApplyDetail> detail = CisOperationApplyDetail.getCisOperationApplyDetailById(detailEtos.getId());
                detail.get().update(detailEtos);
            });
        }

        if (!CollectionUtils.isEmpty(cisOperationApplyEto.getDetailNtos())) {
            cisOperationApplyEto.getDetailNtos().forEach(nto -> {
                CisOperationApplyDetail detail = new CisOperationApplyDetail();
                detail.create(getId(), nto, getStatusCode(), true);
            });
        }
        //术前诊断处理
        if (!CollectionUtils.isEmpty(cisOperationApplyEto.getDiagnosisNtos())) {
            String applyId = getId();
            if (applyId == null) {
                return;
            }

            // 获取现有诊断并建立映射
            List<ApplyDiagnosis> existingDiagnoses = ApplyDiagnosis.getByCisBaseApplyId(applyId);
            Map<String, ApplyDiagnosis> diagnosisMap = existingDiagnoses.stream()
                    .collect(Collectors.toMap(ApplyDiagnosis::getId, odt -> odt));

            // 单次遍历处理新增
            for (ApplyDiagnosisNto nto : cisOperationApplyEto.getDiagnosisNtos()) {
                String ntoId = nto.getId();
                ApplyDiagnosis existing = diagnosisMap.remove(ntoId);
                if (existing == null) {
                    // 新增诊断
                    ApplyDiagnosis newDiagnosis = new ApplyDiagnosis();
                    nto.setCisBaseApplyId(applyId);
                    newDiagnosis.create(nto);
                }
            }
            // 批量删除残留诊断（不再需要的数据）
            diagnosisMap.values().forEach(ApplyDiagnosis::delete);
        }
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
