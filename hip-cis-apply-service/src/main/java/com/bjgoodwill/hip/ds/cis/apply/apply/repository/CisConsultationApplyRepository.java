package com.bjgoodwill.hip.ds.cis.apply.apply.repository;

import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisConsultationApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisConsultationApplyRepository")
public interface CisConsultationApplyRepository extends JpaRepository<CisConsultationApply, String>, JpaSpecificationExecutor<CisConsultationApply> {

}