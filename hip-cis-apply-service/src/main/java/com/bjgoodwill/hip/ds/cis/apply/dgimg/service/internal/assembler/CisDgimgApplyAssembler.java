package com.bjgoodwill.hip.ds.cis.apply.dgimg.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApply;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.service.internal.assembler.CisMedicalHistoryAssembler;

import java.util.ArrayList;
import java.util.List;

public abstract class CisDgimgApplyAssembler {

    public static List<CisDgimgApplyTo> toTos(List<CisDgimgApply> cisDgimgApplys) {
        return toTos(cisDgimgApplys, false);
    }

    public static List<CisDgimgApplyTo> toTos(List<CisDgimgApply> cisDgimgApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisDgimgApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisDgimgApplys不能为空！");

        List<CisDgimgApplyTo> tos = new ArrayList<>();
        for (CisDgimgApply cisDgimgApply : cisDgimgApplys)
            tos.add(toTo(cisDgimgApply, withAllParts));
        return tos;
    }

    public static CisDgimgApplyTo toTo(CisDgimgApply cisDgimgApply) {
        return toTo(cisDgimgApply, false);
    }

    /**
     * @generated
     */
    public static CisDgimgApplyTo toTo(CisDgimgApply cisDgimgApply, boolean withAllParts) {
        if (cisDgimgApply == null)
            return null;
        CisDgimgApplyTo to = new CisDgimgApplyTo();
        to.setId(cisDgimgApply.getId());
        to.setPatMiCode(cisDgimgApply.getPatMiCode());
        to.setVisitCode(cisDgimgApply.getVisitCode());
        to.setServiceItemCode(cisDgimgApply.getServiceItemCode());
        to.setServiceItemName(cisDgimgApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisDgimgApply.getIsCanPriorityFlag());
        to.setStatusCode(cisDgimgApply.getStatusCode());
        to.setCreatedStaff(cisDgimgApply.getCreatedStaff());
        to.setCreatedStaffName(cisDgimgApply.getCreatedStaffName());
        to.setCreatedDate(cisDgimgApply.getCreatedDate());
        to.setUpdatedStaff(cisDgimgApply.getUpdatedStaff());
        to.setUpdatedDate(cisDgimgApply.getUpdatedDate());
        to.setExecutorStaff(cisDgimgApply.getExecutorStaff());
        to.setExecutorDate(cisDgimgApply.getExecutorDate());
        to.setExecutorHosptialCode(cisDgimgApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisDgimgApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisDgimgApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisDgimgApply.getMedrecordExamabstractId());
        to.setVisitType(cisDgimgApply.getVisitType());
        to.setDeptNurseCode(cisDgimgApply.getDeptNurseCode());
        to.setDeptNurseName(cisDgimgApply.getDeptNurseName());
        to.setOrderID(cisDgimgApply.getOrderID());
        to.setHospitalCode(cisDgimgApply.getHospitalCode());
        to.setPrescriptionID(cisDgimgApply.getPrescriptionID());
        to.setIsPrint(cisDgimgApply.getIsPrint());
        to.setPrintStaff(cisDgimgApply.getPrintStaff());
        to.setPrintDate(cisDgimgApply.getPrintDate());
        to.setReMark(cisDgimgApply.getReMark());
        to.setIcuExecuteDate(cisDgimgApply.getIcuExecuteDate());
        to.setIsChargeManager(cisDgimgApply.getIsChargeManager());
        to.setVersion(cisDgimgApply.getVersion());
        to.setCreateOrgCode(cisDgimgApply.getCreateOrgCode());
        to.setSortNo(cisDgimgApply.getSortNo());
        to.setIsBaby(cisDgimgApply.getIsBaby());
        to.setPrecautions(cisDgimgApply.getPrecautions());
        to.setMedrecordAndExamabstract(cisDgimgApply.getMedrecordAndExamabstract());
        to.setPhysiqueAndExam(cisDgimgApply.getPhysiqueAndExam());
        to.setDgimgClass(cisDgimgApply.getDgimgClass());
        to.setDgimgClassName(cisDgimgApply.getDgimgClassName());
        to.setDgimgSubClass(cisDgimgApply.getDgimgSubClass());
        to.setDgimgSubClassName(cisDgimgApply.getDgimgSubClassName());
        to.setAuxiliaryInspection(cisDgimgApply.getAuxiliaryInspection());
        to.setCheckPurpose(cisDgimgApply.getCheckPurpose());
        to.setApplyBookId(cisDgimgApply.getApplyBookId());
        to.setReportPdfUrl(cisDgimgApply.getReportPdfUrl());
        to.setPreviousPathologicalExamin(cisDgimgApply.getPreviousPathologicalExamin());
        to.setVisitOrgCode(cisDgimgApply.getVisitOrgCode());
        to.setVisitOrgName(cisDgimgApply.getVisitOrgName());
        to.setNum(cisDgimgApply.getNum());
        to.setClinicalHistory(cisDgimgApply.getClinicalHistory());
        to.setOccupationalDiseasesFlag(cisDgimgApply.getOccupationalDiseasesFlag());
        to.setAllergicHistoryFlag(cisDgimgApply.getAllergicHistoryFlag());
        to.setContagiousDiseaseHistoryFlag(cisDgimgApply.getContagiousDiseaseHistoryFlag());
        to.setIsOlation(cisDgimgApply.getIsOlation());
        to.setDeviceType(cisDgimgApply.getDeviceType());
        to.setDeviceTypeName(cisDgimgApply.getDeviceTypeName());
        to.setIsApply(cisDgimgApply.getIsApply());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisDgimgApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisDgimgApply.getCisOrderExecPlans()));
            to.setCisDgimgApplyDetails(CisDgimgApplyDetailAssembler.toTos(cisDgimgApply.getDetailList()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisDgimgApply.getApplyDiagnoses()));
            to.setCisMedicalHistoryTo(CisMedicalHistoryAssembler.toTo(cisDgimgApply.getCisMedicalHistory()));
        }
        return to;
    }

    public static CisDgimgApplyNto toNto(CisDgimgApply cisDgimgApply, boolean withAllParts) {
        if (cisDgimgApply == null)
            return null;
        CisDgimgApplyNto to = new CisDgimgApplyNto();
        to.setId(cisDgimgApply.getId());
        to.setPatMiCode(cisDgimgApply.getPatMiCode());
        to.setVisitCode(cisDgimgApply.getVisitCode());
        to.setServiceItemCode(cisDgimgApply.getServiceItemCode());
        to.setServiceItemName(cisDgimgApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisDgimgApply.getIsCanPriorityFlag());
        //to.setMedrecordExamabstractId(cisDgimgApply.getMedrecordExamabstractId());
        to.setVisitType(cisDgimgApply.getVisitType());
        to.setDeptNurseCode(cisDgimgApply.getDeptNurseCode());
        to.setDeptNurseName(cisDgimgApply.getDeptNurseName());
        to.setOrderID(cisDgimgApply.getOrderID());
        to.setHospitalCode(cisDgimgApply.getHospitalCode());
        to.setPrescriptionID(cisDgimgApply.getPrescriptionID());
//        to.setIsPrint(cisDgimgApply.getIsPrint());
//        to.setPrintStaff(cisDgimgApply.getPrintStaff());
//        to.setPrintDate(cisDgimgApply.getPrintDate());
        to.setReMark(cisDgimgApply.getReMark());
        to.setIcuExecuteDate(cisDgimgApply.getIcuExecuteDate());
        to.setIsChargeManager(cisDgimgApply.getIsChargeManager());
        to.setCreateOrgCode(cisDgimgApply.getCreateOrgCode());
        to.setSortNo(cisDgimgApply.getSortNo());
        to.setIsBaby(cisDgimgApply.getIsBaby());
        to.setPrecautions(cisDgimgApply.getPrecautions());
        to.setMedrecordAndExamabstract(cisDgimgApply.getMedrecordAndExamabstract());
        to.setPhysiqueAndExam(cisDgimgApply.getPhysiqueAndExam());
        to.setDgimgClass(cisDgimgApply.getDgimgClass());
        to.setDgimgClassName(cisDgimgApply.getDgimgClassName());
        to.setDgimgSubClass(cisDgimgApply.getDgimgSubClass());
        to.setDgimgSubClassName(cisDgimgApply.getDgimgSubClassName());
        to.setAuxiliaryInspection(cisDgimgApply.getAuxiliaryInspection());
        to.setCheckPurpose(cisDgimgApply.getCheckPurpose());
        to.setApplyBookId(cisDgimgApply.getApplyBookId());
        to.setPreviousPathologicalExamin(cisDgimgApply.getPreviousPathologicalExamin());
        to.setVisitOrgCode(cisDgimgApply.getVisitOrgCode());
        to.setOrderType(cisDgimgApply.getOrderType());
        to.setVisitOrgName(cisDgimgApply.getVisitOrgName());
        to.setCreateOrgName(cisDgimgApply.getCreateOrgName());
        to.setExecutorOrgCode(cisDgimgApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisDgimgApply.getExecutorOrgName());
        to.setNum(cisDgimgApply.getNum());
        to.setIsOlation(cisDgimgApply.getIsOlation());
        to.setDeviceType(cisDgimgApply.getDeviceType());
        to.setDeviceTypeName(cisDgimgApply.getDeviceTypeName());
        to.setIsApply(cisDgimgApply.getIsApply());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toNtos(cisDgimgApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisDgimgApply.getCisOrderExecPlans()));
        }
        return to;
    }
}