package com.bjgoodwill.hip.ds.cis.apply.overstep.entity;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CheckTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisOverstepEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.overstep.repository.CisOverstepApproalRepository;
import com.bjgoodwill.hip.ds.cis.apply.overstep.to.CisOverstepApproalEto;
import com.bjgoodwill.hip.ds.cis.apply.overstep.to.CisOverstepApproalNto;
import com.bjgoodwill.hip.ds.cis.apply.overstep.to.CisOverstepApproalQto;
import com.bjgoodwill.hip.ds.cis.apply.overstep.to.CisOverstepQto;
import com.bjgoodwill.hip.jpa.core.SnowflakeIdGenerator;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "越级审批")
@Table(name = "cis_overstep_approal", indexes = {@Index(name = "cis_overstep_approal_pat_mi_code_index", columnList = "pat_mi_code"),
        @Index(name = "cis_overstep_approal_apply_id_index", columnList = "apply_id")
        , @Index(name = "cis_overstep_approal_visit_code_index", columnList = "visit_code")}, uniqueConstraints = {})
public class CisOverstepApproal {

    // 标识
    private String id;
    // 医嘱id
    private String orderId;
    // 申请单ID
    private String applyId;
    // 就诊类型
    private VisitTypeEnum visitType;
    // 申请，通过，不通过；默认申请
    private CheckTypeEnum checkType;
    // 审核类型，输血， 抗菌药，手术,肿瘤药等
    private CisOverstepEnum checkSystemType;
    //开方科室
    private String visitOrgCode;
    //医生所属科室
    private String createOrgCode;
    //护理组
    private String deptNurseCode;
    // 创建的人员
    private String createdStaff;
    // 创建的时间
    private LocalDateTime createdDate;
    // 创建的人员姓名
    private String createdStaffName;
    // 版本
    private Integer version;
    // 逻辑删除标记
    private boolean deleted;
    // 主索引
    private String patMiCode;
    //流水号
    private String visitCode;
    // 审核人
    private String reviewStaff;
    // 审核人姓名
    private String reviewStaffName;
    // 审核时间
    private LocalDateTime reviewDateTime;
    // 审核意见
    private String reviewOpinions;
    // 申请医生
    private String applyDoc;
    // 申请科室
    private String applyOrgCode;
    // 申请医生姓名
    private String applyDocName;
    // 申请科室名称
    private String applyOrgName;
    // 申请时间
    private LocalDateTime applyDateTime;
    // 申请原因
    private String applyReason;

    // 最后修改的时间
    private LocalDateTime updatedDate;

    public static Optional<CisOverstepApproal> getCisOverstepApproalById(String id) {
        return dao().findById(id);
    }

    public static List<CisOverstepApproal> getCisOverstepApproalsByIdsIn(List<String> ids) {
        // 确保传入的ID列表不为空，否则抛出业务异常
        BusinessAssert.notEmpty(ids, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "参数ids");

        return dao().findAllByIdIn(ids);
    }

    public static List<CisOverstepApproal> getCisOverstepApproals(CisOverstepApproalQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }


    public static List<CisOverstepApproal> getCisOverstepApproals(CisOverstepQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisOverstepApproal> getCisOverstepApproalPage(CisOverstepApproalQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisOverstepApproal> getSpecification(CisOverstepApproalQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getOrderId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderId"), qto.getOrderId()));
            }
            if (StringUtils.isNotBlank(qto.getApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("applyId"), qto.getApplyId()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (qto.getCheckType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("checkType"), qto.getCheckType()));
            }
            if (qto.getCheckSystemType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("checkSystemType"), qto.getCheckSystemType()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getReviewStaff())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("reviewStaff"), qto.getReviewStaff()));
            }
            if (StringUtils.isNotBlank(qto.getApplyDoc())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("applyDoc"), qto.getApplyDoc()));
            }
            if (StringUtils.isNotBlank(qto.getApplyOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("applyOrgCode"), qto.getApplyOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getApplyOrgName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("applyOrgName"), qto.getApplyOrgName()));
            }
            if (qto.getApplyDateTime() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("applyDateTime"), LocalDateUtil.beginOfDay(qto.getApplyDateTime()), LocalDateUtil.endOfDay(qto.getApplyDateTime())));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitOrgCode"), qto.getVisitOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }

            return predicate;
        };
    }

    /**
     * 根据查询传输对象生成动态查询条件
     * 该方法用于构建一个针对CisOverstepApproal实体的动态查询条件，基于传入的查询传输对象（QTO）
     *
     * @param qto 查询传输对象，包含了一系列查询条件
     * @return Specification<CisOverstepApproal> 动态查询条件
     */
    private static Specification<CisOverstepApproal> getSpecification(CisOverstepQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();

            // 根据检查类型动态生成查询条件
            if (qto.getCheckType() != null) {
                CriteriaBuilder.In<Object> in = criteriaBuilder.in(root.get("checkType"));
                Arrays.stream(qto.getCheckType()).forEach(in::value);
                predicate = criteriaBuilder.and(criteriaBuilder.and(in));
            }

            // 根据检查系统类型动态生成查询条件
            if (qto.getCheckSystemType() != null) {
                CriteriaBuilder.In<Object> in = criteriaBuilder.in(root.get("checkSystemType"));
                Arrays.stream(qto.getCheckSystemType()).forEach(in::value);
                predicate = criteriaBuilder.and(criteriaBuilder.and(in));
            }

            // 根据创建者姓名动态生成查询条件
            if (StringUtils.isNotEmpty(qto.getCreateName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("createdStaffName"), "%" + qto.getCreateName() + "%"));
            }

            // 仅查询未删除的记录
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            // 根据访问组织代码动态生成查询条件
            if (StringUtils.isNotBlank(qto.getVisitOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitOrgCode"), qto.getVisitOrgCode()));
            }

            // 根据创建组织代码动态生成查询条件
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }

            // 根据护士部门代码动态生成查询条件
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }

            return predicate;
        };
    }

    private static CisOverstepApproalRepository dao() {
        return SpringUtil.getBean(CisOverstepApproalRepository.class);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    @GeneratedValue(generator = "snowflake_generator")
    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("医嘱id")
    @Column(name = "order_id", nullable = false)
    public String getOrderId() {
        return orderId;
    }

    protected void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Comment("申请单ID")
    @Column(name = "apply_id", nullable = false)
    public String getApplyId() {
        return applyId;
    }

    protected void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    @Enumerated(EnumType.STRING)
    @Comment("就诊类型")
    @Column(name = "visit_type", nullable = false)
    public VisitTypeEnum getVisitType() {
        return visitType;
    }

    protected void setVisitType(VisitTypeEnum visitType) {
        this.visitType = visitType;
    }

    @Enumerated(EnumType.STRING)
    @Comment("申请，通过，不通过；默认申请")
    @Column(name = "check_type", nullable = false)
    public CheckTypeEnum getCheckType() {
        return checkType;
    }

    protected void setCheckType(CheckTypeEnum checkType) {
        this.checkType = checkType;
    }

    @Enumerated(EnumType.STRING)
    @Comment("审核类型， 抗菌药，手术")
    @Column(name = "check_system_type", nullable = true)
    public CisOverstepEnum getCheckSystemType() {
        return checkSystemType;
    }

    protected void setCheckSystemType(CisOverstepEnum checkSystemType) {
        this.checkSystemType = checkSystemType;
    }

    @Comment("开方科室")
    @Column(name = "visit_org_code", nullable = true)
    public String getVisitOrgCode() {
        return visitOrgCode;
    }

    public void setVisitOrgCode(String visitOrgCode) {
        this.visitOrgCode = visitOrgCode;
    }

    @Comment("医生所属科室")
    @Column(name = "create_org_code", nullable = true)
    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    @Comment("护理组")
    @Column(name = "dept_nurse_code", nullable = false)
    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Version
    @Comment("版本")
    @Column(name = "version", nullable = false)
    public Integer getVersion() {
        return version;
    }

    protected void setVersion(Integer version) {
        this.version = version;
    }

    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Comment("主索引")
    @Column(name = "pat_mi_code", nullable = false)
    public String getPatMiCode() {
        return patMiCode;
    }

    protected void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    @Comment("流水号")
    @Column(name = "visit_code", nullable = false)
    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    @Comment("审核人")
    @Column(name = "review_staff", nullable = true)
    public String getReviewStaff() {
        return reviewStaff;
    }

    protected void setReviewStaff(String reviewStaff) {
        this.reviewStaff = reviewStaff;
    }

    @Comment("审核人姓名")
    @Column(name = "review_staff_name", nullable = true)
    public String getReviewStaffName() {
        return reviewStaffName;
    }

    protected void setReviewStaffName(String reviewStaffName) {
        this.reviewStaffName = reviewStaffName;
    }

    @Comment("审核时间")
    @Column(name = "review_date_time", nullable = true)
    public LocalDateTime getReviewDateTime() {
        return reviewDateTime;
    }

    protected void setReviewDateTime(LocalDateTime reviewDateTime) {
        this.reviewDateTime = reviewDateTime;
    }

    @Comment("审核意见")
    @Column(name = "review_opinions", nullable = true)
    public String getReviewOpinions() {
        return reviewOpinions;
    }

    protected void setReviewOpinions(String reviewOpinions) {
        this.reviewOpinions = reviewOpinions;
    }

    @Comment("申请医生")
    @Column(name = "apply_doc", nullable = false)
    public String getApplyDoc() {
        return applyDoc;
    }

    protected void setApplyDoc(String applyDoc) {
        this.applyDoc = applyDoc;
    }

    @Comment("申请科室")
    @Column(name = "apply_org_code", nullable = true)
    public String getApplyOrgCode() {
        return applyOrgCode;
    }

    protected void setApplyOrgCode(String applyOrgCode) {
        this.applyOrgCode = applyOrgCode;
    }

    @Comment("申请医生姓名")
    @Column(name = "apply_doc_name", nullable = true)
    public String getApplyDocName() {
        return applyDocName;
    }

    protected void setApplyDocName(String applyDocName) {
        this.applyDocName = applyDocName;
    }

    @Comment("申请科室名称")
    @Column(name = "apply_org_name", nullable = true)
    public String getApplyOrgName() {
        return applyOrgName;
    }

    protected void setApplyOrgName(String applyOrgName) {
        this.applyOrgName = applyOrgName;
    }

    @Comment("申请时间")
    @Column(name = "apply_date_time", nullable = true)
    public LocalDateTime getApplyDateTime() {
        return applyDateTime;
    }

    protected void setApplyDateTime(LocalDateTime applyDateTime) {
        this.applyDateTime = applyDateTime;
    }

    @Comment("申请原因")
    @Column(name = "apply_reason", nullable = true)
    public String getApplyReason() {
        return applyReason;
    }

    protected void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

//    public void update(CisOverstepApproalEto cisOverstepApproalEto) {
//        setCheckType(cisOverstepApproalEto.getCheckType());
//        setVersion(cisOverstepApproalEto.getVersion());
//        setReviewOpinions(cisOverstepApproalEto.getReviewOpinions());

    /// /        setApplyDocName(cisOverstepApproalEto.getApplyDocName());
    /// /        setApplyOrgName(cisOverstepApproalEto.getApplyOrgName());
    /// /        setApplyReason(cisOverstepApproalEto.getApplyReason());
//    }
    @Comment("修改时间")
    @Column(name = "updated_Date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOverstepApproal other = (CisOverstepApproal) obj;
        return Objects.equals(id, other.id);
    }

    public CisOverstepApproal create(CisOverstepApproalNto cisOverstepApproalNto) {
        BusinessAssert.notNull(cisOverstepApproalNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisOverstepApproalNto不能为空！");

        setOrderId(cisOverstepApproalNto.getOrderId());
        setApplyId(cisOverstepApproalNto.getApplyId());
        setVisitType(cisOverstepApproalNto.getVisitType());
        setCheckSystemType(cisOverstepApproalNto.getCheckSystemType());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setDeleted(false);
        setPatMiCode(cisOverstepApproalNto.getPatMiCode());

        setApplyDoc(HIPLoginUtil.getStaffId());
        setApplyOrgName(HIPLoginUtil.getLoginName());

        setApplyOrgCode(cisOverstepApproalNto.getApplyOrgCode());
        setApplyDocName(cisOverstepApproalNto.getApplyDocName());
        setApplyDateTime(LocalDateTimeUtil.now());
        setApplyReason(cisOverstepApproalNto.getApplyReason());
        setVisitCode(cisOverstepApproalNto.getVisitCode());
        setCheckType(CheckTypeEnum.APPLY);
        setUpdatedDate(getCreatedDate());
        setVisitOrgCode(cisOverstepApproalNto.getVisitOrgCode());
        setCreateOrgCode(cisOverstepApproalNto.getCreateOrgCode());
        setDeptNurseCode(cisOverstepApproalNto.getDeptNurseCode());
        dao().save(this);
        return this;
    }

    public void review(CisOverstepApproalEto cisOverstepApproalEto) {
        BusinessAssert.isTrue(getVersion().equals(cisOverstepApproalEto.getVersion()),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0003, "审批数据！");
        setCheckType(CheckTypeEnum.PASS);
        setVersion(cisOverstepApproalEto.getVersion());
        setReviewDateTime(LocalDateTimeUtil.now());
        setReviewOpinions(cisOverstepApproalEto.getReviewOpinions());
        setReviewStaff(HIPLoginUtil.getStaffId());
        setReviewStaffName(HIPLoginUtil.getLoginName());
    }

    public void noPass(CisOverstepApproalEto cisOverstepApproalEto) {
        BusinessAssert.isTrue(getVersion().equals(cisOverstepApproalEto.getVersion()),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0003, "审批数据！");
        setCheckType(CheckTypeEnum.NOT_PASS);
        setVersion(cisOverstepApproalEto.getVersion());
        setReviewDateTime(LocalDateTimeUtil.now());
        setReviewOpinions(cisOverstepApproalEto.getReviewOpinions());
        setReviewStaff(HIPLoginUtil.getStaffId());
        setReviewStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        setDeleted(true);
    }

}
