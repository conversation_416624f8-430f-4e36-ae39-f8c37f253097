package com.bjgoodwill.hip.ds.cis.apply.material.service.internal;

import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.CisBaseApplyServiceImpl;
import com.bjgoodwill.hip.ds.cis.apply.material.entity.CisMaterialApply;
import com.bjgoodwill.hip.ds.cis.apply.material.service.CisMaterialApplyService;
import com.bjgoodwill.hip.ds.cis.apply.material.service.internal.assembler.CisMaterialApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.material.to.CisMaterialApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.material.to.CisMaterialApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.material.to.CisMaterialApplyTo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisMaterialApplyService")
@RequestMapping(value = "/api/apply/apply/cisMaterialApply", produces = "application/json; charset=utf-8")
public class CisMaterialApplyServiceImpl extends CisBaseApplyServiceImpl implements CisMaterialApplyService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisMaterialApplyTo getCisMaterialApplyById(String id) {
        return CisMaterialApplyAssembler.toTo(CisMaterialApply.getCisMaterialApplyById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisMaterialApplyTo createCisMaterialApply(CisMaterialApplyNto cisMaterialApplyNto) {
        CisMaterialApply cisMaterialApply = new CisMaterialApply();
        cisMaterialApply = cisMaterialApply.create(cisMaterialApplyNto, true);
        return CisMaterialApplyAssembler.toTo(cisMaterialApply);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisMaterialApply(String id, CisMaterialApplyEto cisMaterialApplyEto) {
        Optional<CisMaterialApply> cisMaterialApplyOptional = CisMaterialApply.getCisMaterialApplyById(id);
        cisMaterialApplyOptional.ifPresent(cisMaterialApply -> cisMaterialApply.update(cisMaterialApplyEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisMaterialApply(String id) {
        Optional<CisMaterialApply> cisMaterialApplyOptional = CisMaterialApply.getCisMaterialApplyById(id);
        cisMaterialApplyOptional.ifPresent(cisMaterialApply -> cisMaterialApply.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}