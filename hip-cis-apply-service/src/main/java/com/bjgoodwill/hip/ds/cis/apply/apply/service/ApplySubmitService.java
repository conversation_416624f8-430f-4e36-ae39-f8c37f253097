package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.base.cis.dict.usage.to.UsagePriceTo;
import com.bjgoodwill.hip.ds.base.cis.dict.usage.to.UsageTo;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.entity.CisApplyCharge;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisBaseDrugApply;
import com.bjgoodwill.hip.ds.cis.apply.proxy.CisDictServiceProxy;
import com.bjgoodwill.hip.ds.cis.apply.proxy.CisRuleProxy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-23 11:29
 * @className: ApplySubmitService
 * @description: 申请单的提交
 **/
@Component
public class ApplySubmitService {


    /**
     * 根据访问码和订单ID列表提交Cis基础申请单
     * 该方法首先根据访问码筛选出状态为克隆的新申请单，然后设置提交申请单，
     * 最后执行提交操作，并返回处理后的申请单列表
     *
     * @param visitCode 访问码，用于查询Cis基础申请单
     * @param orderIds  订单ID列表，用于筛选和提交申请单
     * @return 返回提交后的Cis基础申请单列表
     */
    public List<CisBaseApply> submit2(String visitCode, List<String> orderIds) {

        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        List<CisBaseApply> applies = CisBaseApply.findCisBaseAppliesByVisitCodeAndStatusCodeIn(visitCode, Arrays.asList(CisStatusEnum.NEW, CisStatusEnum.BACK))
                .stream().filter(apply -> orderIds.contains(apply.getOrderID())).toList();
        //region 添加提交校验
        CisRuleProxy cisRuleProxy = SpringUtil.getBean(CisRuleProxy.class);
        cisRuleProxy.vertyfyRule(applies);
        //endregion

        // 设置提交申请单，这里可能涉及到对申请单的一些预处理或者状态更新
        setSubmitApply(applies, orderIds);

        // 对每个申请单执行提交操作
        applies.stream().forEach(CisBaseApply::submit);
        return applies;
    }

    /**
     * 根据访问码和订单ID列表提交Cis基础申请单
     * 该方法主要负责筛选出特定访问码下状态为“克隆”的申请单，并为其中符合条件的申请单设置提交状态
     *
     * @param visitCode 访问码，用于识别一组相关的申请单
     * @param orderIds  订单ID列表，用于确定需要提交的申请单
     * @return 返回经过筛选和提交状态设置后的申请单列表
     */
    public List<CisBaseApply> submit(String visitCode, List<String> orderIds) {

        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        List<CisBaseApply> applies = CisBaseApply.findCisBaseAppliesByVisitCodeAndStatusCodeIn(visitCode, Arrays.asList(CisStatusEnum.NEW, CisStatusEnum.BACK))
                .stream().filter(apply -> orderIds.contains(apply.getOrderID())).toList();

        // 为符合条件的申请单设置提交状态
//        setSubmitApply(applies, orderIds);
        // 对每个申请单执行提交操作
        applies.stream().forEach(CisBaseApply::submit);
        // 返回经过筛选和提交状态设置后的申请单列表
        return applies;
    }

    /**
     * 设置并提交申请单
     *
     * @param applies  申请单列表，包含多个申请单对象
     * @param orderIds 订单ID列表，用于筛选特定的申请单
     */
    private void setSubmitApply(List<CisBaseApply> applies, List<String> orderIds) {

        BusinessAssert.notEmpty(orderIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "医嘱ID");

        // 将订单ID转换为Set以提高查找效率
        Set<String> orderIdSet = new HashSet<>(orderIds);

        // 筛选符合条件的申请单，即订单ID包含在给定的订单ID列表中
        applies = applies.stream()
                .filter(apply -> orderIdSet.contains(apply.getOrderID()))
                .collect(Collectors.toList());

        // 确保查询到的cis基础申请单不为空
        BusinessAssert.notEmpty(applies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");

        // 将申请单按照OrderClass进行分组
        Map<SystemTypeEnum, List<CisBaseApply>> map = applies.stream()
                .collect(Collectors.groupingBy(CisBaseApply::getSystemType));

        // 遍历分组后的申请单，并调用相应的提交逻辑
        map.forEach((k, v) -> {
            Consumer<List<CisBaseApply>> consumer = applySubmitMap().get(k);
            if (consumer != null) {
                consumer.accept(v);
            }
        });

    }

    private Map<SystemTypeEnum, Consumer<List<CisBaseApply>>> applySubmitMap() {
        Map<SystemTypeEnum, Consumer<List<CisBaseApply>>> map = new HashMap<>();
        map.put(SystemTypeEnum.CDRUG, this::processCDrugOrders);
        map.put(SystemTypeEnum.EDRUG, this::processEDrugOrders);
        return map;
    }


    private void processCDrugOrders(List<CisBaseApply> applies) {
        //TODO 整煎药非得
        //煎药费 从应用层传递 暂时 不从领域自动获取。
    }

    //region 用法加费
    private void processEDrugOrders(List<CisBaseApply> applies) {
        List<CisBaseDrugApply> edrugs = applies.stream()
                .filter(CisBaseDrugApply.class::isInstance)
                .map(CisBaseDrugApply.class::cast)
                .toList();

        if (!CollectionUtils.isEmpty(edrugs)) {
            //先删除 所有用法带得费用。
            List<String> ids = applies.stream().map(CisBaseApply::getId).toList();
            CisApplyCharge.deleteallByCisBaseApplyIdInAndStatueCode(ids, CisStatusEnum.CLONE, CisChargeTypeEnum.USAGE);

            Map<String, List<CisApplyChargeNto>> map = buildUsagePriceMap();
            edrugs.forEach(p -> processDrugOrder(p, map));
        }
    }

    private Map<String, List<CisApplyChargeNto>> buildUsagePriceMap() {

        CisDictServiceProxy cisDictServiceProxy = SpringUtil.getBean(CisDictServiceProxy.class);

        Map<String, List<CisApplyChargeNto>> map = new ConcurrentHashMap<>();
        cisDictServiceProxy.getUsagesAndUsagePrice().stream()
                .filter(p -> !CollectionUtils.isEmpty(p.getUsagePriceTos()))
                .forEach(p -> map.computeIfAbsent(p.getCode(), k -> new ArrayList<>())
                        .addAll(filterAndConvertCisApplyChargeTos(p)));
        return map;
    }

    private List<CisApplyChargeNto> filterAndConvertCisApplyChargeTos(UsageTo p) {
        return p.getUsagePriceTos().stream()
                .map(this::convertToUsagePriceTo)
                .toList();
    }

    private CisApplyChargeNto convertToUsagePriceTo(UsagePriceTo usagePriceTo) {
        CisApplyChargeNto result = new CisApplyChargeNto();
        result.setNum(Double.valueOf(usagePriceTo.getNum()));
        result.setPrice(usagePriceTo.getPrice());
        result.setPriceItemCode(usagePriceTo.getServicePriceCode());
        result.setIsFixed(usagePriceTo.getIsFixed());
        result.setExecuteOrgCode(usagePriceTo.getDefaultExecOrgCode());
        result.setChargeType(CisChargeTypeEnum.USAGE);
        result.setStatusCode(CisStatusEnum.CLONE);
        result.setUnit(usagePriceTo.getUnit());
        return result;
    }

    private void processDrugOrder(CisBaseDrugApply drugOrder, Map<String, List<CisApplyChargeNto>> map) {
        var cisApplyChargeNtos = map.getOrDefault(drugOrder.getUsage(), Collections.emptyList())
                .stream()
                .toList();


        cisApplyChargeNtos.forEach(cisApplyChargeNto -> {
            cisApplyChargeNto.setExecuteOrgCode(StringUtils.isNotBlank(cisApplyChargeNto.getExecuteOrgCode()) ?
                    cisApplyChargeNto.getExecuteOrgCode() : drugOrder.getExecutorOrgCode());
            CisApplyCharge cisApplyCharge = new CisApplyCharge();

            cisApplyChargeNto.setOrderId(drugOrder.getOrderID());
            cisApplyChargeNto.setVisitCode(drugOrder.getVisitCode());
            cisApplyChargeNto.setCisBaseApplyId(drugOrder.getId());

            cisApplyCharge.create(drugOrder.getId(), cisApplyChargeNto, true);
            drugOrder.getCisApplyCharges().add(cisApplyCharge);
        });
    }
    //endregion
}