package com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.repository;

import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.entity.CisOrderExecLimit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.repository.CisOrderExecLimitRepository")
public interface CisOrderExecLimitRepository extends JpaRepository<CisOrderExecLimit, String>, JpaSpecificationExecutor<CisOrderExecLimit> {

    @Query(value = "select a from CisOrderExecLimit a,CisOrderExecPlan b where b.id in ?1 and a.noExecFlag = true")
    List<CisOrderExecLimit> findNoExecByExecPlanIds(List<String> execPlanIds);

    @Query(value = "select a from CisOrderExecLimit a,CisOrderExecPlan b where b.id in ?1 and a.cancelExecFlag = true")
    List<CisOrderExecLimit> findCancelExecByExecPlanIds(List<String> execPlanIds);


}