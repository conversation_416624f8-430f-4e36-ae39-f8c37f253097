package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisProofNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisSplitEto;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-15 17:12
 * @className: LongTermSplitService
 * @description: 长期医嘱拆分
 **/
@Component
public class LongTermSplitService extends SplitService {


    @Override
    protected LocalDateTime[] getExecutionDates(CisSplitConversion splitConversion) {
        CisFrequenciesService cisFrequenciesService = SpringUtil.getBean(CisFrequenciesService.class);
        return cisFrequenciesService.getTimes(splitConversion).toArray(new LocalDateTime[0]);
    }

    @Override
    protected CisBaseApply doApply(CisBaseApplyNto applyNto, Boolean save) {
        CisBaseApply apply = CisBaseApply.newInstanceByNto(applyNto);
        apply.setStatusCode(CisStatusEnum.NEW);
        applyNto.setId(applyCodeServiceProxy.getApplyNextCode());
        apply.create(applyNto, save);
        return apply;
    }

    @Override
    protected CisBaseApply doApplyProof(CisBaseApplyNto applyNto, Boolean save) {
        CisBaseApply apply = CisBaseApply.newInstanceByNto(applyNto);
        apply.setStatusCode(CisStatusEnum.NEW);
        apply.createProof(applyNto, save);
        return apply;
    }


    @Override
    protected List<CisBaseApplyNto> getCisBaseApplyNtos(List<CisBaseApplyNto> applies) {
        if (CollectionUtils.isEmpty(applies)) {
            return applies;
        }
        return applies.stream().filter(p -> OrderTypeEnum.LONG_TERM_ORDER.equals(p.getOrderType())).toList();
    }

    @Override
    protected List<CisBaseApplyNto> getApplyNtos(List<CisSplitEto> etos) {
        List<String> orderids = etos.stream().map(CisSplitEto::getOrderId).collect(Collectors.toList());
        ApplyCreateService applyCreateService = SpringUtil.getBean(ApplyCreateService.class);
        List<CisBaseApplyNto> applies = applyCreateService.getSplitApplys(etos.get(0).getNurseDeptCode(), orderids);
        return getCisBaseApplyNtos(applies);
    }

    @Override
    protected void judgeOrderPlan(String visitCode, List<CisProofNto> orderPlans) {
        // 根据访问代码和活动状态获取申请ID列表
        List<String> applyIds = CisBaseApply.findCisBaseAppliesByVisitCodeAndStatusCode(visitCode, CisStatusEnum.PASS)
                .stream().map(CisBaseApply::getId).toList();

        // 断言申请ID列表非空，否则抛出异常
        BusinessAssert.notNull(applyIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0006, visitCode + "拆分");

        // 断言订单计划中存在至少一个不在申请ID列表中的计划，否则抛出异常
        BusinessAssert.isTrue(orderPlans.stream().anyMatch(p -> !applyIds.contains(p.getApplyId())),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0003, visitCode + "拆分");


    }

    /**
     * 根据ETOs和申请单列表，获取拆分转换列表。
     * 此方法通过匹配ETOs中的订单ID与申请单列表中的订单ID，将匹配到的申请单信息与ETOs的日期范围一起封装成拆分转换对象。
     *
     * @param etos    ETOs列表，包含需要转换的日期范围和订单ID。
     * @param applies 申请单列表，用于查找与ETOs中订单ID对应的申请单信息。
     * @return 返回一个拆分转换对象的列表，每个对象包含一个日期范围和对应的申请单信息。
     */
    public List<CisSplitConversion> getCisSplitConversion(List<CisSplitEto> etos, List<CisBaseApplyNto> applies) {
        // 使用ConcurrentHashMap来存储申请单列表，以订单ID作为键，申请单对象作为值，用于后续快速查找。
        Map<String, CisBaseApplyNto> map = new ConcurrentHashMap<>(applies.stream().collect(Collectors.toMap(CisBaseApplyNto::getOrderID, p -> p)));

        // 初始化拆分转换对象的列表，用于存储匹配后的结果。
        List<CisSplitConversion> cisSplitConversions = new ArrayList<>();

        // 遍历ETOs列表，对每个ETO对象，尝试从map中查找对应的申请单信息。
        etos.stream().forEach(eto -> {
            BusinessAssert.notNull(eto.getBeginDate(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "拆分开始时间");
            BusinessAssert.notNull(eto.getEndDate(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "拆分结束时间");

            // 使用Optional来处理可能的空值，避免NullPointerException。
            Optional.ofNullable(map.get(eto.getOrderId())).ifPresent(p -> {
                // 如果找到匹配的申请单信息，则创建一个新的拆分转换对象，并添加到结果列表中。
                cisSplitConversions.add(new CisSplitConversion(eto.getBeginDate(), eto.getEndDate(), eto.getFirstDayTimepoint(), p));
            });
        });

        // 返回拆分转换对象的列表。
        return cisSplitConversions;
    }


}