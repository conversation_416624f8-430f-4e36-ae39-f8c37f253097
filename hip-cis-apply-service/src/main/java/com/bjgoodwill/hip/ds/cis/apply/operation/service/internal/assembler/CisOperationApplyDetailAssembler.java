package com.bjgoodwill.hip.ds.cis.apply.operation.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.operation.entity.CisOperationApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.CisOperationApplyDetailTo;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-09-29 16:38
 * @className: CisOperationApplyDetailAssembler
 * @description:
 **/
public abstract class CisOperationApplyDetailAssembler {
    public static CisOperationApplyDetailTo toTo(CisOperationApplyDetail cisOperationApply) {
        CisOperationApplyDetailTo to = new CisOperationApplyDetailTo();
        to.setApplyId(cisOperationApply.getApplyId());
        to.setId(cisOperationApply.getId());
        to.setOperationCode(cisOperationApply.getOperationCode());
        to.setOperationName(cisOperationApply.getOperationName());
        to.setSortNo(cisOperationApply.getSortNo());
        to.setServiceItemCode(cisOperationApply.getServiceItemCode());
        to.setServiceItemName(cisOperationApply.getServiceItemName());
        to.setHumanOrgans(cisOperationApply.getHumanOrgans());
        to.setHumanOrgansName(cisOperationApply.getHumanOrgansName());
        to.setDecubitus(cisOperationApply.getDecubitus());
        to.setDecubitusName(cisOperationApply.getDecubitusName());
        to.setOperationLevel(cisOperationApply.getOperationLevel());
        return to;
    }

    public static List<CisOperationApplyDetailTo> toTos(List<CisOperationApplyDetail> cisOperationApplys) {
        BusinessAssert.notNull(cisOperationApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisOperationApplys不能为空！");

        List<CisOperationApplyDetailTo> tos = new ArrayList<>();
        for (CisOperationApplyDetail cisOperationApplyDetail : cisOperationApplys)
            tos.add(toTo(cisOperationApplyDetail));
        return tos;

    }
}