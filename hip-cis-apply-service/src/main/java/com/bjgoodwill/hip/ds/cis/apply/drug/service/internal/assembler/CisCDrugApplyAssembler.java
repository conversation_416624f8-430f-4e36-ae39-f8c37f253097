package com.bjgoodwill.hip.ds.cis.apply.drug.service.internal.assembler;

import cn.hutool.core.convert.Convert;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisCDrugApply;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;

import java.util.ArrayList;
import java.util.List;

public abstract class CisCDrugApplyAssembler {

    public static List<CisCDrugApplyTo> toTos(List<CisCDrugApply> cisCDrugApplys) {
        return toTos(cisCDrugApplys, false);
    }

    public static List<CisCDrugApplyTo> toTos(List<CisCDrugApply> cisCDrugApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisCDrugApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisCDrugApplys不能为空！");

        List<CisCDrugApplyTo> tos = new ArrayList<>();
        for (CisCDrugApply cisCDrugApply : cisCDrugApplys)
            tos.add(toTo(cisCDrugApply, withAllParts));
        return tos;
    }

    public static CisCDrugApplyTo toTo(CisCDrugApply cisCDrugApply) {
        return toTo(cisCDrugApply, false);
    }

    /**
     * @generated
     */
    public static CisCDrugApplyTo toTo(CisCDrugApply cisCDrugApply, boolean withAllParts) {
        if (cisCDrugApply == null)
            return null;
        CisCDrugApplyTo to = new CisCDrugApplyTo();
        to.setId(cisCDrugApply.getId());
        to.setPatMiCode(cisCDrugApply.getPatMiCode());
        to.setVisitCode(cisCDrugApply.getVisitCode());
        to.setServiceItemCode(cisCDrugApply.getServiceItemCode());
        to.setServiceItemName(cisCDrugApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisCDrugApply.getIsCanPriorityFlag());
        to.setStatusCode(cisCDrugApply.getStatusCode());
        to.setCreatedStaff(cisCDrugApply.getCreatedStaff());
        to.setCreatedDate(cisCDrugApply.getCreatedDate());
        to.setUpdatedStaff(cisCDrugApply.getUpdatedStaff());
        to.setUpdatedDate(cisCDrugApply.getUpdatedDate());
        to.setExecutorStaff(cisCDrugApply.getExecutorStaff());
        to.setExecutorDate(cisCDrugApply.getExecutorDate());
        to.setExecutorHosptialCode(cisCDrugApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisCDrugApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisCDrugApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisCDrugApply.getMedrecordExamabstractId());
        to.setVisitType(cisCDrugApply.getVisitType());
        to.setDeptNurseCode(cisCDrugApply.getDeptNurseCode());
        to.setDeptNurseName(cisCDrugApply.getDeptNurseName());
        to.setOrderID(cisCDrugApply.getOrderID());
        to.setHospitalCode(cisCDrugApply.getHospitalCode());
        to.setPrescriptionID(cisCDrugApply.getPrescriptionID());
        to.setIsPrint(cisCDrugApply.getIsPrint());
        to.setPrintStaff(cisCDrugApply.getPrintStaff());
        to.setPrintDate(cisCDrugApply.getPrintDate());
        to.setReMark(cisCDrugApply.getReMark());
        to.setIcuExecuteDate(cisCDrugApply.getIcuExecuteDate());
        to.setIsChargeManager(cisCDrugApply.getIsChargeManager());
        to.setVersion(cisCDrugApply.getVersion());
        to.setCreateOrgCode(cisCDrugApply.getCreateOrgCode());
        to.setSortNo(cisCDrugApply.getSortNo());
        to.setIsBaby(cisCDrugApply.getIsBaby());
        to.setUsage(cisCDrugApply.getUsage());
        to.setUsageName(cisCDrugApply.getUsageName());
        to.setFrequency(cisCDrugApply.getFrequency());
        to.setFrequencyName(cisCDrugApply.getFrequencyName());
        to.setTreatmentCourse(cisCDrugApply.getTreatmentCourse());
        to.setTreatmentCourseUnit(cisCDrugApply.getTreatmentCourseUnit());
        to.setTreatmentCourseUnitName(cisCDrugApply.getTreatmentCourseUnitName());
        to.setPrescriptionFlag(cisCDrugApply.getPrescriptionFlag());
        to.setReceiveOrg(cisCDrugApply.getReceiveOrg());
        to.setReceiveOrgName(cisCDrugApply.getReceiveOrgName());
        to.setDoseNum(cisCDrugApply.getDoseNum());
        to.setDecoction(cisCDrugApply.getDecoction());
        to.setCdrugPackNum(cisCDrugApply.getCdrugPackNum());
        to.setCdrugPackMl(cisCDrugApply.getCdrugPackMl());
        to.setVisitOrgCode(cisCDrugApply.getVisitOrgCode());
        to.setVisitOrgName(cisCDrugApply.getVisitOrgName());
        to.setIsOlation(cisCDrugApply.getIsOlation());
        to.setHerbsProCode(cisCDrugApply.getHerbsProCode());
        to.setNum(cisCDrugApply.getNum());
        to.setIsApply(cisCDrugApply.getIsApply());
        to.setCreateOrgName(cisCDrugApply.getCreateOrgName());
//        to.setSbadmWay(cisCDrugApply.getSbadmWay());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisCDrugApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisCDrugApply.getCisOrderExecPlans()));
            to.setCisDrugApplyDetails(CisDrugApplyDetailAssembler.toTos(cisCDrugApply.getDetailList()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisCDrugApply.getApplyDiagnoses()));

        }
        return to;
    }

    public static CisCDrugApplyNto toNto(CisCDrugApply cisCDrugApply, boolean withAllParts) {
        if (cisCDrugApply == null)
            return null;
        CisCDrugApplyNto to = new CisCDrugApplyNto();
        to.setId(cisCDrugApply.getId());
        to.setPatMiCode(cisCDrugApply.getPatMiCode());
        to.setVisitCode(cisCDrugApply.getVisitCode());
        to.setServiceItemCode(cisCDrugApply.getServiceItemCode());
        to.setServiceItemName(cisCDrugApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisCDrugApply.getIsCanPriorityFlag());

//        to.setMedrecordExamabstractId(cisCDrugApply.getMedrecordExamabstractId());
        to.setVisitType(cisCDrugApply.getVisitType());
        to.setDeptNurseCode(cisCDrugApply.getDeptNurseCode());
        to.setDeptNurseName(cisCDrugApply.getDeptNurseName());
        to.setOrderID(cisCDrugApply.getOrderID());
        to.setHospitalCode(cisCDrugApply.getHospitalCode());
        to.setPrescriptionID(cisCDrugApply.getPrescriptionID());
//        to.setIsPrint(cisCDrugApply.getIsPrint());
//        to.setPrintStaff(cisCDrugApply.getPrintStaff());
//        to.setPrintDate(cisCDrugApply.getPrintDate());
        to.setReMark(cisCDrugApply.getReMark());
        to.setIcuExecuteDate(cisCDrugApply.getIcuExecuteDate());
        to.setIsChargeManager(cisCDrugApply.getIsChargeManager());
        to.setCreateOrgCode(cisCDrugApply.getCreateOrgCode());
        to.setSortNo(cisCDrugApply.getSortNo());
        to.setIsBaby(cisCDrugApply.getIsBaby());
        to.setUsage(cisCDrugApply.getUsage());
        to.setUsageName(cisCDrugApply.getUsageName());
        to.setFrequency(cisCDrugApply.getFrequency());
        to.setFrequencyName(cisCDrugApply.getFrequencyName());
        to.setTreatmentCourse(cisCDrugApply.getTreatmentCourse());
        to.setTreatmentCourseUnit(cisCDrugApply.getTreatmentCourseUnit());
        to.setTreatmentCourseUnitName(cisCDrugApply.getTreatmentCourseUnitName());
        to.setPrescriptionFlag(cisCDrugApply.getPrescriptionFlag());
        to.setReceiveOrg(cisCDrugApply.getReceiveOrg());
        to.setReceiveOrgName(cisCDrugApply.getReceiveOrgName());
        to.setDoseNum(cisCDrugApply.getDoseNum());
        to.setDecoction(cisCDrugApply.getDecoction());
        to.setCdrugPackNum(cisCDrugApply.getCdrugPackNum());
        to.setCdrugPackMl(cisCDrugApply.getCdrugPackMl());
        to.setVisitOrgCode(cisCDrugApply.getVisitOrgCode());
        to.setOrderType(cisCDrugApply.getOrderType());
        to.setVisitOrgName(cisCDrugApply.getVisitOrgName());
        to.setCreateOrgName(cisCDrugApply.getCreateOrgName());
        to.setExecutorOrgCode(cisCDrugApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisCDrugApply.getExecutorOrgName());
        // 草药数量取付数
        to.setNum(Convert.toDouble(cisCDrugApply.getDoseNum()));
        to.setHerbsProCode(cisCDrugApply.getHerbsProCode());
        to.setIsOlation(cisCDrugApply.getIsOlation());
        to.setIsApply(cisCDrugApply.getIsApply());
//        to.setSbadmWay(cisCDrugApply.getSbadmWay());
        if (withAllParts) {
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisCDrugApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisCDrugApply.getCisOrderExecPlans()));
        }
        return to;
    }

}