package com.bjgoodwill.hip.ds.cis.apply.dgimg.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.ApplyWithDetial;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.repository.CisDgimgApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyQto;
import com.bjgoodwill.hip.ds.cis.apply.diag.entity.ApplyDiagnosis;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.entity.CisMedicalHistory;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryEto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import jakarta.persistence.criteria.Predicate;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Entity
@Comment(value = "检查申请单")
@DiscriminatorValue("05")
public class CisDgimgApply extends ApplyWithDetial<CisDgimgApplyDetail> {

    // 检查注意事项
    private String precautions;
    // 病历及查体摘要
    private String medrecordAndExamabstract;
    //    // 临床诊断
//    private String clinicalDiagnosis;
    // 体格及其他检查
    private String physiqueAndExam;
    // 分类
    private String dgimgClass;
    // 分类名称
    private String dgimgClassName;
    // 子分类
    private String dgimgSubClass;
    // 子分类名称
    private String dgimgSubClassName;
    // 相关辅检
    private String auxiliaryInspection;

    // 申请单预约标识
    private String applyBookId;
    // 报告pdf地址
    private String reportPdfUrl;
    // 既往病理检查结果
    private String previousPathologicalExamin;
    // 检查设备类型
    private String deviceType;
    // 检查设备类型名称
    private String deviceTypeName;
    // 是否过敏史
    private Boolean allergicHistoryFlag;

    // 是否职业病史
    private Boolean occupationalDiseasesFlag;

    //临床病史
    private String clinicalHistory;


    public static Optional<CisDgimgApply> getCisDgimgApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisDgimgApply> getCisDgimgApplies(CisDgimgApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisDgimgApply> getCisDgimgApplyPage(CisDgimgApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisDgimgApply> getByApplyBookId(String applyBookId) {
        return dao().findByApplyBookId(applyBookId);
    }

    public static void deleteByApplyBookId(String applyBookId) {
        dao().deleteByApplyBookId(applyBookId);
    }

    /**
     * @generated
     */
    private static Specification<CisDgimgApply> getSpecification(CisDgimgApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            addPredicate(predicate, criteriaBuilder, root, "visitCode", qto.getVisitCode());
            addPredicate(predicate, criteriaBuilder, root, "serviceItemCode", qto.getServiceItemCode());
            addPredicate(predicate, criteriaBuilder, root, "statusCode", qto.getStatusCode());
            addPredicate(predicate, criteriaBuilder, root, "visitType", qto.getVisitType());
            addPredicate(predicate, criteriaBuilder, root, "deptNurseCode", qto.getDeptNurseCode());
            addPredicate(predicate, criteriaBuilder, root, "orderID", qto.getOrderID());
            addPredicate(predicate, criteriaBuilder, root, "hospitalCode", qto.getHospitalCode());
            addPredicate(predicate, criteriaBuilder, root, "prescriptionID", qto.getPrescriptionID());
            addPredicate(predicate, criteriaBuilder, root, "createOrgCode", qto.getCreateOrgCode());
            return predicate;
        };
    }

    private static CisDgimgApplyRepository dao() {
        return SpringUtil.getBean(CisDgimgApplyRepository.class);
    }

    @Comment("检查注意事项")
    @Column(name = "precautions", nullable = true)
    public String getPrecautions() {
        return precautions;
    }

    protected void setPrecautions(String precautions) {
        this.precautions = precautions;
    }

    @Comment("病历及查体摘要")
    @Column(name = "medrecord_and_examabstract", nullable = true)
    public String getMedrecordAndExamabstract() {
        return medrecordAndExamabstract;
    }

    protected void setMedrecordAndExamabstract(String medrecordAndExamabstract) {
        this.medrecordAndExamabstract = medrecordAndExamabstract;
    }

    @Comment("体格及其他检查")
    @Column(name = "physique_and_exam", nullable = true)
    public String getPhysiqueAndExam() {
        return physiqueAndExam;
    }

    protected void setPhysiqueAndExam(String physiqueAndExam) {
        this.physiqueAndExam = physiqueAndExam;
    }

    @Comment("分类")
    @Column(name = "dgimg_class", nullable = true)
    public String getDgimgClass() {
        return dgimgClass;
    }

    protected void setDgimgClass(String dgimgClass) {
        this.dgimgClass = dgimgClass;
    }

    @Comment("子分类")
    @Column(name = "dgimg_sub_class", nullable = true)
    public String getDgimgSubClass() {
        return dgimgSubClass;
    }

    protected void setDgimgSubClass(String dgimgSubClass) {
        this.dgimgSubClass = dgimgSubClass;
    }

    @Comment("相关辅检")
    @Column(name = "auxiliary_inspection", nullable = true)
    public String getAuxiliaryInspection() {
        return auxiliaryInspection;
    }

    protected void setAuxiliaryInspection(String auxiliaryInspection) {
        this.auxiliaryInspection = auxiliaryInspection;
    }

    @Comment("申请单预约标识")
    @Column(name = "apply_book_id", nullable = true, length = 50)
    public String getApplyBookId() {
        return applyBookId;
    }

    protected void setApplyBookId(String applyBookId) {
        this.applyBookId = applyBookId;
    }

    @Comment("报告pdf地址")
    @Column(name = "report_pdf_url", nullable = true)
    public String getReportPdfUrl() {
        return reportPdfUrl;
    }

    protected void setReportPdfUrl(String reportPdfUrl) {
        this.reportPdfUrl = reportPdfUrl;
    }

    @Comment("既往病理检查结果")
    @Column(name = "previous_pathological_examin", nullable = true)
    public String getPreviousPathologicalExamin() {
        return previousPathologicalExamin;
    }

    protected void setPreviousPathologicalExamin(String previousPathologicalExamin) {
        this.previousPathologicalExamin = previousPathologicalExamin;
    }

    @Comment("检查设备类型")
    @Column(name = "device_type", nullable = true)
    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    @Comment("是否过敏史")
    @Column(name = "Allergic_History_Flag", nullable = true)
    public Boolean getAllergicHistoryFlag() {
        return allergicHistoryFlag;
    }

    public void setAllergicHistoryFlag(Boolean allergicHistoryFlag) {
        this.allergicHistoryFlag = allergicHistoryFlag;
    }

    @Comment("是否职业病史")
    @Column(name = "Occupational_Diseases_Flag", nullable = true)
    public Boolean getOccupationalDiseasesFlag() {
        return occupationalDiseasesFlag;
    }

    public void setOccupationalDiseasesFlag(Boolean occupationalDiseasesFlag) {
        this.occupationalDiseasesFlag = occupationalDiseasesFlag;
    }

    @Comment("临床病史")
    @Column(name = "Clinical_History", nullable = true)
    public String getClinicalHistory() {
        return clinicalHistory;
    }

    public void setClinicalHistory(String clinicalHistory) {
        this.clinicalHistory = clinicalHistory;
    }

    @Comment("分类名称")
    @Column(name = "dgimg_class_name", nullable = true)
    public String getDgimgClassName() {
        return dgimgClassName;
    }

    public void setDgimgClassName(String dgimgClassName) {
        this.dgimgClassName = dgimgClassName;
    }

    @Comment("子分类名称")
    @Column(name = "dgimg_sub_class_name", nullable = true)
    public String getDgimgSubClassName() {
        return dgimgSubClassName;
    }

    public void setDgimgSubClassName(String dgimgSubClassName) {
        this.dgimgSubClassName = dgimgSubClassName;
    }

    @Comment("检查设备类型名称")
    @Column(name = "device_type_name", nullable = true)
    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.DGIMG;
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisDgimgApplyEto) cisBaseApplyEto);
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return this.create((CisDgimgApplyNto) cisBaseApplyNto, save);
    }

    public CisDgimgApply create(CisDgimgApplyNto cisDgimgApplyNto, Boolean save) {
        BusinessAssert.notNull(cisDgimgApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisDgimgApplyNto不能为空！");
        super.create(cisDgimgApplyNto, save);

        setPrecautions(cisDgimgApplyNto.getPrecautions());
        setMedrecordAndExamabstract(cisDgimgApplyNto.getMedrecordAndExamabstract());
//        setClinicalDiagnosis(cisDgimgApplyNto.getClinicalDiagnosis());
        setPhysiqueAndExam(cisDgimgApplyNto.getPhysiqueAndExam());
        setDgimgClass(cisDgimgApplyNto.getDgimgClass());
        setDgimgClassName(cisDgimgApplyNto.getDgimgClassName());
        setDgimgSubClass(cisDgimgApplyNto.getDgimgSubClass());
        setDgimgSubClassName(cisDgimgApplyNto.getDgimgSubClassName());
        setAuxiliaryInspection(cisDgimgApplyNto.getAuxiliaryInspection());
        setCheckPurpose(cisDgimgApplyNto.getCheckPurpose());
        setApplyBookId(cisDgimgApplyNto.getApplyBookId());
        setPreviousPathologicalExamin(cisDgimgApplyNto.getPreviousPathologicalExamin());
        setDeviceType(cisDgimgApplyNto.getDeviceType());
        setDeviceTypeName(cisDgimgApplyNto.getDeviceTypeName());
        setClinicalHistory(cisDgimgApplyNto.getClinicalHistory());
        setOccupationalDiseasesFlag(cisDgimgApplyNto.getOccupationalDiseasesFlag());
        setAllergicHistoryFlag(cisDgimgApplyNto.getAllergicHistoryFlag());
        setContagiousDiseaseHistoryFlag(cisDgimgApplyNto.getContagiousDiseaseHistoryFlag());

        if (save) {
            dao().save(this);
        }

        if (!CollectionUtils.isEmpty(cisDgimgApplyNto.getDetails())) {
            for (CisDgimgApplyDetailNto _cisDgimgApplyDetail : cisDgimgApplyNto.getDetails()) {
                CisDgimgApplyDetail cisDgimgApplyDetail = new CisDgimgApplyDetail();
                _cisDgimgApplyDetail.setCisDgimgApplyId(getId());
                cisDgimgApplyDetail = cisDgimgApplyDetail.create(getId(), _cisDgimgApplyDetail, getStatusCode(), save);
                updateChargeDetailID(cisDgimgApplyDetail.getId(), cisDgimgApplyNto);
            }
        }
        if (cisDgimgApplyNto.getApplyDiagnosisNtos() != null) {
            cisDgimgApplyNto.getApplyDiagnosisNtos().forEach(applyDiagnosisNto -> {
                ApplyDiagnosis addDiagnosis = new ApplyDiagnosis();
                applyDiagnosisNto.setCisBaseApplyId(getId());
                addDiagnosis.create(applyDiagnosisNto);
            });
        }
        if (cisDgimgApplyNto.getCisMedicalHistoryNto() != null) {
            CisMedicalHistory cisMedicalHistory = getCisMedicalHistory();
            if (cisMedicalHistory != null) {
                CisMedicalHistoryEto eto = HIPBeanUtil.copy(cisDgimgApplyNto.getCisMedicalHistoryNto(), CisMedicalHistoryEto.class);
                cisMedicalHistory.update(eto);
            } else {
                cisMedicalHistory = new CisMedicalHistory();
                cisMedicalHistory.create(cisDgimgApplyNto.getCisMedicalHistoryNto());
            }
        }
        return this;
    }

    public void update(CisDgimgApplyEto cisDgimgApplyEto) {
        super.update(cisDgimgApplyEto);
        setPrecautions(cisDgimgApplyEto.getPrecautions());
        setMedrecordAndExamabstract(cisDgimgApplyEto.getMedrecordAndExamabstract());
        setDgimgClass(cisDgimgApplyEto.getDgimgClass());
        setDgimgClassName(cisDgimgApplyEto.getDgimgClassName());
        setDgimgSubClass(cisDgimgApplyEto.getDgimgSubClass());
        setDgimgSubClassName(cisDgimgApplyEto.getDgimgSubClassName());
        setAuxiliaryInspection(cisDgimgApplyEto.getAuxiliaryInspection());
        setCheckPurpose(cisDgimgApplyEto.getCheckPurpose());

        setClinicalHistory(cisDgimgApplyEto.getClinicalHistory());
        setOccupationalDiseasesFlag(cisDgimgApplyEto.getOccupationalDiseasesFlag());
        setAllergicHistoryFlag(cisDgimgApplyEto.getAllergicHistoryFlag());
        setContagiousDiseaseHistoryFlag(cisDgimgApplyEto.getContagiousDiseaseHistoryFlag());
        setPreviousPathologicalExamin(cisDgimgApplyEto.getPreviousPathologicalExamin());

        if (!CollectionUtils.isEmpty(cisDgimgApplyEto.getDetailEtos())) {
            cisDgimgApplyEto.getDetailEtos().forEach(eto -> {
                Optional<CisDgimgApplyDetail> detailOptional = CisDgimgApplyDetail.getCisDgimgApplyDetailById(eto.getId());
                detailOptional.get().update(eto);
            });
        }

        if (!CollectionUtils.isEmpty(cisDgimgApplyEto.getDetailNtos())) {
            cisDgimgApplyEto.getDetailNtos().forEach(nto -> {
                CisDgimgApplyDetail detail = new CisDgimgApplyDetail();
                detail.create(getId(), nto, getStatusCode());
            });
        }
        //术前诊断处理
        if (!CollectionUtils.isEmpty(cisDgimgApplyEto.getApplyDiagnosisNtos())) {
            String applyId = getId();
            if (applyId == null) {
                return;
            }

            // 获取现有诊断并建立映射
            List<ApplyDiagnosis> existingDiagnoses = ApplyDiagnosis.getByCisBaseApplyId(applyId);
            Map<String, ApplyDiagnosis> diagnosisMap = existingDiagnoses.stream()
                    .collect(Collectors.toMap(ApplyDiagnosis::getId, odt -> odt));

            // 单次遍历处理新增
            for (ApplyDiagnosisNto nto : cisDgimgApplyEto.getApplyDiagnosisNtos()) {
                String ntoId = nto.getId();
                ApplyDiagnosis existing = diagnosisMap.remove(ntoId);
                if (existing == null) {
                    // 新增诊断
                    ApplyDiagnosis newDiagnosis = new ApplyDiagnosis();
                    nto.setCisBaseApplyId(applyId);
                    newDiagnosis.create(nto);
                }
            }
            // 批量删除残留诊断（不再需要的数据）
            diagnosisMap.values().forEach(ApplyDiagnosis::delete);
        }
        //患者病史
        if (cisDgimgApplyEto.getCisMedicalHistoryNto() != null) {
            CisMedicalHistory cisMedicalHistory = getCisMedicalHistory();
            if (cisMedicalHistory != null) {
                CisMedicalHistoryEto eto = HIPBeanUtil.copy(cisDgimgApplyEto.getCisMedicalHistoryNto(), CisMedicalHistoryEto.class);
                cisMedicalHistory.update(eto);
            } else {
                cisMedicalHistory = new CisMedicalHistory();
                cisMedicalHistory.create(cisDgimgApplyEto.getCisMedicalHistoryNto());
            }
        }
    }

    public void delete() {
        super.delete();
        for (CisDgimgApplyDetail cisDgimgApplyDetail : CisDgimgApplyDetail.getByCisDgimgApplyId(getId())) {
            cisDgimgApplyDetail.delete();
        }
        // CisDgimgApplyDetail.deleteByCisDgimgApplyId(getId());dao().delete(this);
    }

    @Override
    @Transient
    public List<CisDgimgApplyDetail> getDetailList() {
        return CisDgimgApplyDetail.getByCisDgimgApplyId(getId());
    }

    @Override
    protected List<String> getSplitCodes(List<CisDgimgApplyDetail> applyDetails) {
        return applyDetails.stream().map(p -> p.getDgimgCode()).toList();
    }

//    @Override
//    protected List<CisDgimgApplyDetail> findCisApplyDetailsByVisitCode(String vistCode) {
//        return CisDgimgApplyDetail.findCisDgimgApplyDetailsByVisitCode(vistCode);
//    }
//
//    @Override
//    protected List<CisBaseApplyTo> getApplyBaseTos(List<CisBaseApplyTo> cisBaseApplyTos) {
//        // 使用Stream API进行过滤和映射操作
//        // 这里之所以不存在线程安全问题，是因为Stream的操作在执行时被设计为不可变的，
//        // 除非外部有并发修改列表，这在大多数情况下是被避免的。
//        return cisBaseApplyTos.stream()
//                .filter(CisDgimgApplyTo.class::isInstance)
//                .map(CisDgimgApplyTo.class::cast)
//                .collect(Collectors.toList());
//    }
//
//    @Override
//    protected void setApplyBaseToDetail(CisBaseApplyTo applyBaseTo, List<CisDgimgApplyDetail> applyDetails) {
//        ((CisDgimgApplyTo)applyBaseTo).setCisDgimgApplyDetails(CisDgimgApplyDetailAssembler.toTos(applyDetails));
//    }
}
