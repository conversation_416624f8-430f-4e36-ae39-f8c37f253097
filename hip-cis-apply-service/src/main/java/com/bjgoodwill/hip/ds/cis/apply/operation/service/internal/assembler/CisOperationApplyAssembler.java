package com.bjgoodwill.hip.ds.cis.apply.operation.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.operation.entity.CisOperationApply;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.CisOperationApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.CisOperationApplyTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisOperationApplyAssembler {

    public static List<CisOperationApplyTo> toTos(List<CisOperationApply> cisOperationApplys) {
        return toTos(cisOperationApplys, false);
    }

    public static List<CisOperationApplyTo> toTos(List<CisOperationApply> cisOperationApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisOperationApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisOperationApplys不能为空！");

        List<CisOperationApplyTo> tos = new ArrayList<>();
        for (CisOperationApply cisOperationApply : cisOperationApplys)
            tos.add(toTo(cisOperationApply, withAllParts));
        return tos;
    }

    public static CisOperationApplyTo toTo(CisOperationApply cisOperationApply) {
        return toTo(cisOperationApply, false);
    }

    /**
     * @generated
     */
    public static CisOperationApplyTo toTo(CisOperationApply cisOperationApply, boolean withAllParts) {
        if (cisOperationApply == null)
            return null;
        CisOperationApplyTo to = new CisOperationApplyTo();
        to.setId(cisOperationApply.getId());
        to.setPatMiCode(cisOperationApply.getPatMiCode());
        to.setVisitCode(cisOperationApply.getVisitCode());
        to.setServiceItemCode(cisOperationApply.getServiceItemCode());
        to.setServiceItemName(cisOperationApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisOperationApply.getIsCanPriorityFlag());
        to.setStatusCode(cisOperationApply.getStatusCode());
        to.setCreatedStaff(cisOperationApply.getCreatedStaff());
        to.setCreatedDate(cisOperationApply.getCreatedDate());
        to.setUpdatedStaff(cisOperationApply.getUpdatedStaff());
        to.setUpdatedDate(cisOperationApply.getUpdatedDate());
        to.setExecutorStaff(cisOperationApply.getExecutorStaff());
        to.setExecutorDate(cisOperationApply.getExecutorDate());
        to.setExecutorHosptialCode(cisOperationApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisOperationApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisOperationApply.getExecutorOrgName());
        to.setVisitType(cisOperationApply.getVisitType());
        to.setDeptNurseCode(cisOperationApply.getDeptNurseCode());
        to.setDeptNurseName(cisOperationApply.getDeptNurseName());
        to.setOrderID(cisOperationApply.getOrderID());
        to.setHospitalCode(cisOperationApply.getHospitalCode());
        to.setPrescriptionID(cisOperationApply.getPrescriptionID());
        to.setIsPrint(cisOperationApply.getIsPrint());
        to.setPrintStaff(cisOperationApply.getPrintStaff());
        to.setPrintDate(cisOperationApply.getPrintDate());
        to.setReMark(cisOperationApply.getReMark());
        to.setIcuExecuteDate(cisOperationApply.getIcuExecuteDate());
        to.setIsChargeManager(cisOperationApply.getIsChargeManager());
        to.setVersion(cisOperationApply.getVersion());
        to.setCreateOrgCode(cisOperationApply.getCreateOrgCode());
        to.setSortNo(cisOperationApply.getSortNo());
        to.setIsBaby(cisOperationApply.getIsBaby());
        to.setBloodType(cisOperationApply.getBloodType());
        to.setBloodTypeRh(cisOperationApply.getBloodTypeRh());
        to.setOperationType(cisOperationApply.getOperationType());
        to.setOperationTypeName(cisOperationApply.getOperationTypeName());
        to.setIsOlation(cisOperationApply.getIsOlation());
        to.setAnaesthesiaType(cisOperationApply.getAnaesthesiaType());
        to.setAnaesthesiaTypeName(cisOperationApply.getAnaesthesiaTypeName());
        to.setAnaesthesiaMode(cisOperationApply.getAnaesthesiaMode());
        to.setAnaesthesiaModeName(cisOperationApply.getAnaesthesiaModeName());
        to.setOperationSpecialAttr(cisOperationApply.getOperationSpecialAttr());
        to.setOperationDate(cisOperationApply.getOperationDate());
        to.setOperationTime(cisOperationApply.getOperationTime());
        to.setIncisionType(cisOperationApply.getIncisionType());
        to.setIncisionLevel(cisOperationApply.getIncisionLevel());
        to.setIncisionLevelName(cisOperationApply.getIncisionLevelName());
        to.setMergeFlag(cisOperationApply.getMergeFlag());
        to.setApprOperTypc(cisOperationApply.getApprOperTypc());
        to.setOperationDoctor(cisOperationApply.getOperationDoctor());
        to.setOperationDoctorName(cisOperationApply.getOperationDoctorName());
        to.setOperationDoctorOrg(cisOperationApply.getOperationDoctorOrg());
        to.setOperationDoctorOrgName(cisOperationApply.getOperationDoctorOrgName());
        to.setOperation(cisOperationApply.getOperation());
        to.setApproach(cisOperationApply.getApproach());
        to.setApproachName(cisOperationApply.getApproachName());
        to.setInstrument(cisOperationApply.getInstrument());
        to.setInstrumentName(cisOperationApply.getInstrumentName());
        to.setVisitOrgCode(cisOperationApply.getVisitOrgCode());
        to.setVisitOrgName(cisOperationApply.getVisitOrgName());
        to.setNum(cisOperationApply.getNum());
        to.setIsOlation(cisOperationApply.getIsOlation());
        to.setIsApply(cisOperationApply.getIsApply());
        to.setFirstAidesCode(cisOperationApply.getFirstAidesCode());
        to.setFirstAidesName(cisOperationApply.getFirstAidesName());
        to.setSecondAidesCode(cisOperationApply.getSecondAidesCode());
        to.setSecondAidesName(cisOperationApply.getSecondAidesName());
        to.setThirdAidesCode(cisOperationApply.getThirdAidesCode());
        to.setThirdAidesName(cisOperationApply.getThirdAidesName());
        to.setFourthAidesCode(cisOperationApply.getFourthAidesCode());
        to.setFourthAidesName(cisOperationApply.getFourthAidesName());
        to.setCoopDept(cisOperationApply.getCoopDept());
        to.setReorganize(cisOperationApply.getReorganize());
        to.setTransferIcu(cisOperationApply.getTransferIcu());
        to.setFreezePathology(cisOperationApply.getFreezePathology());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisOperationApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisOperationApply.getCisOrderExecPlans()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisOperationApply.getApplyDiagnoses()));
            to.setCisOperationApplyDetailToList(CisOperationApplyDetailAssembler.toTos(cisOperationApply.getDetailList()));
        }
        return to;
    }

    public static CisOperationApplyNto toNto(CisOperationApply cisOperationApply, boolean withAllParts) {
        if (cisOperationApply == null)
            return null;
        CisOperationApplyNto to = new CisOperationApplyNto();
        to.setId(cisOperationApply.getId());
        to.setPatMiCode(cisOperationApply.getPatMiCode());
        to.setVisitCode(cisOperationApply.getVisitCode());
        to.setServiceItemCode(cisOperationApply.getServiceItemCode());
        to.setServiceItemName(cisOperationApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisOperationApply.getIsCanPriorityFlag());
        to.setVisitType(cisOperationApply.getVisitType());
        to.setDeptNurseCode(cisOperationApply.getDeptNurseCode());
        to.setDeptNurseName(cisOperationApply.getDeptNurseName());
        to.setOrderID(cisOperationApply.getOrderID());
        to.setHospitalCode(cisOperationApply.getHospitalCode());
        to.setPrescriptionID(cisOperationApply.getPrescriptionID());
        to.setReMark(cisOperationApply.getReMark());
        to.setIcuExecuteDate(cisOperationApply.getIcuExecuteDate());
        to.setIsChargeManager(cisOperationApply.getIsChargeManager());
        to.setCreateOrgCode(cisOperationApply.getCreateOrgCode());
        to.setSortNo(cisOperationApply.getSortNo());
        to.setIsBaby(cisOperationApply.getIsBaby());
        to.setBloodType(cisOperationApply.getBloodType());
        to.setBloodTypeRh(cisOperationApply.getBloodTypeRh());
        to.setOperationType(cisOperationApply.getOperationType());
        to.setOperationTypeName(cisOperationApply.getOperationTypeName());
        to.setAnaesthesiaType(cisOperationApply.getAnaesthesiaType());
        to.setAnaesthesiaTypeName(cisOperationApply.getAnaesthesiaTypeName());
        to.setAnaesthesiaMode(cisOperationApply.getAnaesthesiaMode());
        to.setAnaesthesiaModeName(cisOperationApply.getAnaesthesiaModeName());
        to.setOperationSpecialAttr(cisOperationApply.getOperationSpecialAttr());
        to.setOperationDate(cisOperationApply.getOperationDate());
        to.setOperationTime(cisOperationApply.getOperationTime());
        to.setIncisionType(cisOperationApply.getIncisionType());
        to.setIncisionLevel(cisOperationApply.getIncisionLevel());
        to.setIncisionLevelName(cisOperationApply.getIncisionLevelName());
        to.setMergeFlag(cisOperationApply.getMergeFlag());
        to.setApprOperTypc(cisOperationApply.getApprOperTypc());
        to.setOperationDoctor(cisOperationApply.getOperationDoctor());
        to.setOperationDoctorName(cisOperationApply.getOperationDoctorName());
        to.setOperationDoctorOrg(cisOperationApply.getOperationDoctorOrg());
        to.setOperationDoctorOrgName(cisOperationApply.getOperationDoctorOrgName());
        to.setOperation(cisOperationApply.getOperation());
        to.setApproach(cisOperationApply.getApproach());
        to.setApproachName(cisOperationApply.getApproachName());
        to.setInstrument(cisOperationApply.getInstrument());
        to.setInstrumentName(cisOperationApply.getInstrumentName());
        to.setVisitOrgCode(cisOperationApply.getVisitOrgCode());
        to.setOrderType(cisOperationApply.getOrderType());
        to.setVisitOrgName(cisOperationApply.getVisitOrgName());
        to.setCreateOrgName(cisOperationApply.getCreateOrgName());
        to.setExecutorOrgCode(cisOperationApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisOperationApply.getExecutorOrgName());
        to.setNum(cisOperationApply.getNum());
        to.setIsOlation(cisOperationApply.getIsOlation());
        to.setIsApply(cisOperationApply.getIsApply());
        to.setFirstAidesCode(cisOperationApply.getFirstAidesCode());
        to.setFirstAidesName(cisOperationApply.getFirstAidesName());
        to.setSecondAidesCode(cisOperationApply.getSecondAidesCode());
        to.setSecondAidesName(cisOperationApply.getSecondAidesName());
        to.setThirdAidesCode(cisOperationApply.getThirdAidesCode());
        to.setThirdAidesName(cisOperationApply.getThirdAidesName());
        to.setFourthAidesCode(cisOperationApply.getFourthAidesCode());
        to.setFourthAidesName(cisOperationApply.getFourthAidesName());
        to.setCoopDept(cisOperationApply.getCoopDept());
        to.setReorganize(cisOperationApply.getReorganize());
        to.setTransferIcu(cisOperationApply.getTransferIcu());
        to.setFreezePathology(cisOperationApply.getFreezePathology());
        if (withAllParts) {
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisOperationApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisOperationApply.getCisOrderExecPlans()));
        }
        return to;
    }
}