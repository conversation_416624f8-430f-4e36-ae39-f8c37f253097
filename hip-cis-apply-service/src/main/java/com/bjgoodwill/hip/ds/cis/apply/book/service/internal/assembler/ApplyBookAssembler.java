package com.bjgoodwill.hip.ds.cis.apply.book.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.book.entity.ApplyBook;
import com.bjgoodwill.hip.ds.cis.apply.book.to.ApplyBookTo;

import java.util.ArrayList;
import java.util.List;

public abstract class ApplyBookAssembler {

    public static List<ApplyBookTo> toTos(List<ApplyBook> applyBooks) {
        return toTos(applyBooks, false);
    }

    public static List<ApplyBookTo> toTos(List<ApplyBook> applyBooks, boolean withAllParts) {
        BusinessAssert.notNull(applyBooks, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数applyBooks不能为空！");

        List<ApplyBookTo> tos = new ArrayList<>();
        for (ApplyBook applyBook : applyBooks)
            tos.add(toTo(applyBook, withAllParts));
        return tos;
    }

    public static ApplyBookTo toTo(ApplyBook applyBook) {
        return toTo(applyBook, false);
    }

    /**
     * @generated
     */
    public static ApplyBookTo toTo(ApplyBook applyBook, boolean withAllParts) {
        if (applyBook == null)
            return null;
        ApplyBookTo to = new ApplyBookTo();
        to.setId(applyBook.getId());
        to.setAppointsStatus(applyBook.getAppointsStatus());
        to.setAppointsStartDate(applyBook.getAppointsStartDate());
        to.setAppointsEndDate(applyBook.getAppointsEndDate());
        to.setApplyId(applyBook.getApplyId());
        if (withAllParts) {
        }
        return to;
    }

}