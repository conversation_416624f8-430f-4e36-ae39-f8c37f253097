package com.bjgoodwill.hip.ds.cis.apply.apply.service.freqsplit;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.base.cis.dict.frequency.to.FrequencyTo;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisSplitConversion;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-18 10:49
 * @className: freqSplit
 * @description:
 **/
public abstract class FreqSplit {

    /**
     * 根据分拆转换和频率要求，计算出分拆后的日期时间列表。
     *
     * @param splitConversion 分拆转换对象，包含分拆的起始和结束日期时间以及申请ID。
     * @param frequencyTo     频率转换对象，包含频率时间和单位。
     * @return 返回一个LocalDateTime类型的列表，表示分拆后的日期时间点。
     */
    public List<LocalDateTime> compute(CisSplitConversion splitConversion, FrequencyTo frequencyTo) {
        // 获取频率时间间隔
        // 检查间隔时间是否有效
        int freqTime = frequencyTo.getFreqTime();

        // 设置初始的最后一个分拆日期时间为起始日期时间减去10秒，为了处理边界情况
        LocalDateTime lastSplitDate = splitConversion.getBeginDate().plusSeconds(-10);

        // 逻辑上对结束日期时间加上10秒，但此处并不赋值给lastSplitDate，只是为了表达逻辑
        splitConversion.getEndDate().plusSeconds(10);

        // 如果频率时间大于1，则需要计算出两个分拆日期时间点之间的间隔
        // 间隔时间大于1。
        if (freqTime > 1) {
            List<LocalDateTime> dates = new ArrayList<>();
            dates.add(lastSplitDate);

            // 尝试获取最后一个执行计划的日期时间，如果不存在，则使用当前时间加上10秒
            CisOrderExecPlan cisOrderExecPlans = CisOrderExecPlan.getLastCisOrderExecPlan(splitConversion.getApplyId()).orElse(null);

            dates.add(cisOrderExecPlans == null ? LocalDateTime.now().plusSeconds(10) : cisOrderExecPlans.getExecPlanDate());
            // 从两个日期时间点中选择较晚的一个作为新的lastSplitDate
            lastSplitDate = dates.stream().max(LocalDateTime::compareTo).orElse(lastSplitDate);

        }

        // 调用splitDate方法，根据lastSplitDate和频率要求，计算并返回分拆后的日期时间列表
        return splitDate(lastSplitDate, splitConversion, frequencyTo);
    }

    protected abstract List<LocalDateTime> splitDate(LocalDateTime lastSplitDate, CisSplitConversion splitConversion, FrequencyTo frequencyTo);

    protected List<String> getTimes(FrequencyTo frequencyTo) {

        BusinessAssert.hasText(frequencyTo.getCode(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "频次编码！");

        String[] freqTimepoint = frequencyTo.getFreqTimepoint().split("-");
        BusinessAssert.notEmpty(freqTimepoint, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "频次点！");
//        Assert.notEmpty(freqTimepoint, frequencyTo.getCode() + "频次点为空！");

//        Assert.isTrue(freqTimepoint.length == frequencyTo.getFreqTime(), frequencyTo.getCode() + "频次数量和格式对不上");
//        BusinessAssert.isTrue(freqTimepoint.length == frequencyTo.getFreqTime(),
//                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0005, "频次数量和格式");
        return Arrays.stream(freqTimepoint).toList();
    }


    protected LocalDateTime getDateTime(LocalDateTime date, String hour) {
        // 如果匹配，格式化日期时间为字符串，但当前实现中存在错误，导致方法返回null
        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
        return LocalDateTime.parse(date.toLocalDate().toString() + "T" + hour, formatter);
    }
}
