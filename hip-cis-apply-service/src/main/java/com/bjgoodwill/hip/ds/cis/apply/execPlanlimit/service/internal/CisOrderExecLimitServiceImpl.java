package com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.service.internal;

import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.entity.CisOrderExecLimit;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.service.CisOrderExecLimitService;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.service.internal.assembler.CisOrderExecLimitAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to.CisOrderExecLimitEto;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to.CisOrderExecLimitNto;
import com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.to.CisOrderExecLimitTo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.apply.execPlanlimit.service.CisOrderExecLimitService")
@RequestMapping(value = "/api/apply/execPlanlimit/cisOrderExecLimit", produces = "application/json; charset=utf-8")
public class CisOrderExecLimitServiceImpl implements CisOrderExecLimitService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisOrderExecLimitTo getCisOrderExecLimitById(String id) {
        return CisOrderExecLimitAssembler.toTo(CisOrderExecLimit.getCisOrderExecLimitById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOrderExecLimitTo createCisOrderExecLimit(CisOrderExecLimitNto cisOrderExecLimitNto) {
        CisOrderExecLimit cisOrderExecLimit = new CisOrderExecLimit();
        cisOrderExecLimit = cisOrderExecLimit.create(cisOrderExecLimitNto);
        return CisOrderExecLimitAssembler.toTo(cisOrderExecLimit);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderExecLimit(String id, CisOrderExecLimitEto cisOrderExecLimitEto) {
        Optional<CisOrderExecLimit> cisOrderExecLimitOptional = CisOrderExecLimit.getCisOrderExecLimitById(id);
        cisOrderExecLimitOptional.ifPresent(cisOrderExecLimit -> cisOrderExecLimit.update(cisOrderExecLimitEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOrderExecLimit(String id) {
        Optional<CisOrderExecLimit> cisOrderExecLimitOptional = CisOrderExecLimit.getCisOrderExecLimitById(id);
        cisOrderExecLimitOptional.ifPresent(cisOrderExecLimit -> cisOrderExecLimit.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}