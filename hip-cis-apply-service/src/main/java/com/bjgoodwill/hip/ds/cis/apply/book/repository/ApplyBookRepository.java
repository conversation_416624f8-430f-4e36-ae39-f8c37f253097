package com.bjgoodwill.hip.ds.cis.apply.book.repository;

import com.bjgoodwill.hip.ds.cis.apply.book.entity.ApplyBook;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.apply.book.repository.ApplyBookRepository")
public interface ApplyBookRepository extends JpaRepository<ApplyBook, String>, JpaSpecificationExecutor<ApplyBook> {
    List<ApplyBook> findApplyBooksByApplyIdIn(List<String> applyids);
}