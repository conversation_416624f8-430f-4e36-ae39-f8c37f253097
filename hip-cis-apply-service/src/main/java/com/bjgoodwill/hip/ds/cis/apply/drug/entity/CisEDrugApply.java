package com.bjgoodwill.hip.ds.cis.apply.drug.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.EDrugSystemTypeExtEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.ApplyWithDetial;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.BaseDetail;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailEto;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.repository.CisEDrugApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "成药申请单")
@DiscriminatorValue("01")
public class CisEDrugApply extends CisBaseDrugApply {

    // 滴速
    private String dripSpeed;
    // 滴速单位
    private String dripSpeedUnit;
    // 滴速单位名称
    private String dripSpeedUnitName;

    public static Optional<CisEDrugApply> getCisEDrugApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisEDrugApply> getCisEDrugApplies(CisEDrugApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisEDrugApply> getCisEDrugApplyPage(CisEDrugApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * 优化后的Specification获取方法
     */
    private static Specification<CisEDrugApply> getSpecification(CisEDrugApplyQto qto) {
        // 检查qto是否为null
        if (qto == null) {
            throw new IllegalArgumentException("Query parameters object 'qto' cannot be null.");
        }

        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            addPredicate(predicate, criteriaBuilder, root, "visitCode", qto.getVisitCode());
            addPredicate(predicate, criteriaBuilder, root, "serviceItemCode", qto.getServiceItemCode());
            addPredicate(predicate, criteriaBuilder, root, "statusCode", qto.getStatusCode());
            addPredicate(predicate, criteriaBuilder, root, "visitType", qto.getVisitType());
            addPredicate(predicate, criteriaBuilder, root, "deptNurseCode", qto.getDeptNurseCode());
            addPredicate(predicate, criteriaBuilder, root, "orderID", qto.getOrderID());
            addPredicate(predicate, criteriaBuilder, root, "hospitalCode", qto.getHospitalCode());
            addPredicate(predicate, criteriaBuilder, root, "prescriptionID", qto.getPrescriptionID());
            addPredicate(predicate, criteriaBuilder, root, "createOrgCode", qto.getCreateOrgCode());
            return predicate;
        };
    }

    private static CisEDrugApplyRepository dao() {
        return SpringUtil.getBean(CisEDrugApplyRepository.class);
    }

    public static List<CisEDrugApply> findCisEDrugAppliesByCreatedDateAfter(LocalDateTime dateTime) {
        return dao().findCisEDrugAppliesByCreatedDateAfter(dateTime);
    }

    @Comment("滴速")
    @Column(name = "drip_speed", nullable = true)
    public String getDripSpeed() {
        return dripSpeed;
    }

    protected void setDripSpeed(String dripSpeed) {
        this.dripSpeed = dripSpeed;
    }

    @Comment("滴速单位")
    @Column(name = "drip_speed_unit", nullable = true)
    public String getDripSpeedUnit() {
        return dripSpeedUnit;
    }

    protected void setDripSpeedUnit(String dripSpeedUnit) {
        this.dripSpeedUnit = dripSpeedUnit;
    }

    public String getDripSpeedUnitName() {
        return dripSpeedUnitName;
    }

    public void setDripSpeedUnitName(String dripSpeedUnitName) {
        this.dripSpeedUnitName = dripSpeedUnitName;
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.EDRUG;
    }

    @Override
    public CisBaseDrugApply create(CisBaseDrugApplyNto cisBaseDrugApplyNto, Boolean save) {
        return create((CisEDrugApplyNto) cisBaseDrugApplyNto, save);
    }

    @Override
    public void update(CisBaseDrugApplyEto cisBaseDrugApplyEto) {
        update((CisEDrugApplyEto) cisBaseDrugApplyEto);
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisEDrugApplyNto) cisBaseApplyNto, save);
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisEDrugApplyEto) cisBaseApplyEto);
    }

    public CisEDrugApply create(CisEDrugApplyNto cisEDrugApplyNto, Boolean save) {
        BusinessAssert.notNull(cisEDrugApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisEDrugApplyNto不能为空！");
        super.create(cisEDrugApplyNto, save);

        setDripSpeed(cisEDrugApplyNto.getDripSpeed());
        setDripSpeedUnit(cisEDrugApplyNto.getDripSpeedUnit());
        setDripSpeedUnitName(cisEDrugApplyNto.getDripSpeedUnitName());
        dao().save(this);

        return this;
    }

    public void update(CisEDrugApplyEto cisEDrugApplyEto) {
        super.update(cisEDrugApplyEto);
        setDripSpeed(cisEDrugApplyEto.getDripSpeed());
        setDripSpeedUnit(cisEDrugApplyEto.getDripSpeedUnit());
        setDripSpeedUnitName(cisEDrugApplyEto.getDripSpeedUnitName());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

    protected String getServiceCode(List<DetailNto> details) {
        List<CisDrugApplyDetailNto> detailNtos = details.stream().filter(CisDrugApplyDetailNto.class::isInstance)
                .filter(o -> EDrugSystemTypeExtEnum.COMMON.getCode().equals(o.getExtTypeCode()))
                .map(p -> (CisDrugApplyDetailNto) p).toList();
        return StringUtils.join(detailNtos.stream().filter(DetailNto.class::isInstance)
                .map(p -> (p).getDrugCode()).toList().toArray(), ",");
    }

    public String getServiceCodeByEto(List<DetailEto> details) {
        List<CisDrugApplyDetailEto> detailNtos = details.stream().filter(CisDrugApplyDetailEto.class::isInstance)
                .filter(o -> EDrugSystemTypeExtEnum.COMMON.getCode().equals(o.getExtTypeCode()))
                .map(p -> (CisDrugApplyDetailEto) p).toList();
        return StringUtils.join(detailNtos.stream()
                .map(p -> (p).getDrugCode()).toList().toArray(), ",");
    }

    @Override
    public String getMainId(List<BaseDetail> details, List<ApplyWithDetial> applyList) {
        //如果有抗菌药 直接把抗菌药扣出来 抗菌药的医嘱作为合并的主医嘱。
        return details.stream()
                .filter(CisDrugApplyDetail.class::isInstance)
                .map(CisDrugApplyDetail.class::cast)
                .filter(detail -> detail.getAntimicrobialsPurpose() != null)
                .findFirst()
                .map(CisDrugApplyDetail::getApplyId)
                .orElse(applyList.get(0).getId());
    }

}
