package com.bjgoodwill.hip.ds.cis.apply.dgimg.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisUnilateralEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.BaseDetail;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailNto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.repository.CisDgimgApplyDetailRepository;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyDetailEto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyDetailQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import com.google.common.collect.Lists;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "检查申请单明细")
@Table(name = "cis_dgimg_apply_detail", indexes = {@Index(name = "cis_dgimg_apply_detail_visit_code", columnList = "visit_code"),
        @Index(name = "cis_dgimg_apply_detail_apply_id", columnList = "apply_id")}, uniqueConstraints = {})
public class CisDgimgApplyDetail extends BaseDetail {

    //    // 标识
//    private String id;
//    // 患者流水号
//    private String visitCode;
//    // 检查申请单标识
//    private String cisDgimgApplyId;
    // 序号
//    private Double no;
    // 检查名称
    private String dgimgName;

    private String dgimgCode;

    // 人体系统
    private String humanSystem;
    // 人体系统名称
    private String humanSystemName;
    //部位编码
    private String humanOrgans;
    //部位名称
    private String humanOrgansName;
    // 方法
    private String method;
    // 方法
    private String methodName;
    // 范围
    private String range;
    // 范围
    private String rangeName;
    // 方位
    private String direction;
    // 方位名称
    private String directionName;
    // 层数
    private String layer;
    // 层数
    private String layerName;
    // 操作
    private String operation;
    // 操作名称
    private String operationName;
    // 要求与目的
    private String requirementPurpose;
    //单侧标记，左，右，双侧
    private CisUnilateralEnum unilateralFlag;
    // 创建的人员
    private String createdStaff;
    // 创建的人员
    private String createdStaffName;
    // 最后修改的人员
    private String updatedStaff;
    // 最后修改的人员
    private String updatedStaffName;


//    // 状态
//    private CisStatusEnum statusCode;
//
//    private LocalDateTime createdDate;
//
//    private LocalDateTime updatedDate;

//    @Id
//    @Comment("标识")
//    @Column(name = "id", nullable = false, length = 50)
//    @GeneratedValue(generator = "snowflake_generator")
//    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
//    public String getId() {
//        return id;
//    }
//
//    protected void setId(String id) {
//        this.id = id;
//    }
//
//    @Comment("就诊流水号")
//    @Column(name = "visit_code", nullable = false)
//    public String getVisitCode() {
//        return visitCode;
//    }
//
//    protected void setVisitCode(String visitCode) {
//        this.visitCode = visitCode;
//    }
//
//    @Comment("检查申请单标识")
//    @Column(name = "cis_dgimg_apply_id", nullable = false, length = 50)
//    public String getCisDgimgApplyId() {
//        return cisDgimgApplyId;
//    }
//
//    protected void setCisDgimgApplyId(String cisDgimgApplyId) {
//        this.cisDgimgApplyId = cisDgimgApplyId;
//    }

//    @Comment("序号")
//    @Column(name = "no", nullable = false)
//    public Double getNo() {
//        return no;
//    }
//
//    protected void setNo(Double no) {
//        this.no = no;
//    }

    public static Optional<CisDgimgApplyDetail> getCisDgimgApplyDetailById(String id) {
        return dao().findById(id);
    }

    public static List<CisDgimgApplyDetail> getCisDgimgApplyDetails(CisDgimgApplyDetailQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisDgimgApplyDetail> getCisDgimgApplyDetailPage(CisDgimgApplyDetailQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisDgimgApplyDetail> getByCisDgimgApplyId(String cisDgimgApplyId) {
        return dao().findByApplyId(cisDgimgApplyId);
    }

    public static List<CisDgimgApplyDetail> getByCisDgimgApplyIds(List<String> applyIds) {
        return Lists.partition(applyIds, 100).stream().map(list -> dao().findByApplyIdIn(list))
                .flatMap(List::stream).toList();
    }

    public static void deleteByCisDgimgApplyId(String cisDgimgApplyId) {
        dao().deleteByApplyId(cisDgimgApplyId);
    }

    /**
     * @generated
     */
    private static Specification<CisDgimgApplyDetail> getSpecification(CisDgimgApplyDetailQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getCisDgimgApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisDgimgApplyId"), qto.getCisDgimgApplyId()));
            }
            return predicate;
        };
    }

    private static CisDgimgApplyDetailRepository dao() {
        return SpringUtil.getBean(CisDgimgApplyDetailRepository.class);
    }

    public static List<CisDgimgApplyDetail> findCisDgimgApplyDetailsByVisitCode(String visitCode) {
        BusinessAssert.hasText(visitCode, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "流水号");
        return dao().findCisDgimgApplyDetailsByVisitCode(visitCode);
    }

    @Override
    public List<BaseDetail> queryDetailsByCreateDate(LocalDateTime dateTime) {
        return dao().queryDetailsByCreateDate(dateTime);
    }

    @Comment("检查名称")
    @Column(name = "dgimg_name", nullable = false)
    public String getDgimgName() {
        return dgimgName;
    }

    protected void setDgimgName(String dgimgName) {
        this.dgimgName = dgimgName;
    }

    @Comment("检查项目编码")
    @Column(name = "dgimg_code", nullable = false)
    public String getDgimgCode() {
        return dgimgCode;
    }

    public void setDgimgCode(String dgimgCode) {
        this.dgimgCode = dgimgCode;
    }

//    @Comment("状态")
//    @Column(name = "status_code", nullable = true)
//    @Enumerated(EnumType.STRING)
//    public CisStatusEnum getStatusCode() {
//        return statusCode;
//    }
//
//    protected void setStatusCode(CisStatusEnum statusCode) {
//        this.statusCode = statusCode;
//    }
//
//    @Comment("创建时间")
//    @Column(name = "created_date", nullable = false)
//    public LocalDateTime getCreatedDate() {
//        return createdDate;
//    }
//    public void setCreatedDate(LocalDateTime createdDate) {
//        this.createdDate = createdDate;
//    }
//
//    @Comment("更新时间")
//    @Column(name = "updated_date", nullable = false)
//    public LocalDateTime getUpdatedDate() {
//        return updatedDate;
//    }
//
//    public void setUpdatedDate(LocalDateTime updatedDate) {
//        this.updatedDate = updatedDate;
//    }

    @Comment("人体系统")
    @Column(name = "human_system", nullable = true)
    public String getHumanSystem() {
        return humanSystem;
    }

    protected void setHumanSystem(String humanSystem) {
        this.humanSystem = humanSystem;
    }

    @Comment("部位编码")
    @Column(name = "human_organs", nullable = false)
    public String getHumanOrgans() {
        return humanOrgans;
    }

    protected void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    @Comment("方法")
    @Column(name = "method", nullable = true)
    public String getMethod() {
        return method;
    }

    protected void setMethod(String method) {
        this.method = method;
    }

    @Comment("范围")
    @Column(name = "range", nullable = true)
    public String getRange() {
        return range;
    }

    protected void setRange(String range) {
        this.range = range;
    }

    @Comment("方位")
    @Column(name = "direction", nullable = true)
    public String getDirection() {
        return direction;
    }

    protected void setDirection(String direction) {
        this.direction = direction;
    }

    @Comment("层数")
    @Column(name = "layer", nullable = true)
    public String getLayer() {
        return layer;
    }

    protected void setLayer(String layer) {
        this.layer = layer;
    }

    @Comment("操作")
    @Column(name = "operation", nullable = true)
    public String getOperation() {
        return operation;
    }

    protected void setOperation(String operation) {
        this.operation = operation;
    }

    @Comment("要求与目的")
    @Column(name = "requirement_purpose", nullable = true)
    public String getRequirementPurpose() {
        return requirementPurpose;
    }

    protected void setRequirementPurpose(String requirementPurpose) {
        this.requirementPurpose = requirementPurpose;
    }

    @Enumerated(EnumType.STRING)
    @Comment("单侧标记：左，右，双侧")
    @Column(name = "unilateral_flag", nullable = true)
    public CisUnilateralEnum getUnilateralFlag() {
        return unilateralFlag;
    }

    public void setUnilateralFlag(CisUnilateralEnum unilateralFlag) {
        this.unilateralFlag = unilateralFlag;
    }

    @Comment("人体系统名称")
    @Column(name = "human_system_name", nullable = true)
    public String getHumanSystemName() {
        return humanSystemName;
    }

    public void setHumanSystemName(String humanSystemName) {
        this.humanSystemName = humanSystemName;
    }

    @Comment("部位名称")
    @Column(name = "human_organs_name", nullable = true)
    public String getHumanOrgansName() {
        return humanOrgansName;
    }

    public void setHumanOrgansName(String humanOrgansName) {
        this.humanOrgansName = humanOrgansName;
    }

    @Comment("方法名称")
    @Column(name = "method_name", nullable = true)
    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    @Comment("范围名称")
    @Column(name = "range_name", nullable = true)
    public String getRangeName() {
        return rangeName;
    }

    public void setRangeName(String rangeName) {
        this.rangeName = rangeName;
    }

    @Comment("方位")
    @Column(name = "direction_name", nullable = true)
    public String getDirectionName() {
        return directionName;
    }

    public void setDirectionName(String directionName) {
        this.directionName = directionName;
    }

    @Comment("层数名称")
    @Column(name = "layer_name", nullable = true)
    public String getLayerName() {
        return layerName;
    }

    public void setLayerName(String layerName) {
        this.layerName = layerName;
    }

    @Comment("操作名称")
    @Column(name = "operation_name", nullable = true)
    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = true, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的人员名称")
    @Column(name = "created_staff_name", nullable = true)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Comment("最后修改的人员名称")
    @Column(name = "updated_staff_name", nullable = true)
    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Transient
    @Override
    public String getServiceItemCode() {
        return this.dgimgCode;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisDgimgApplyDetail other = (CisDgimgApplyDetail) obj;
        return Objects.equals(id, other.id);
    }

    public BaseDetail create(String applyId, DetailNto baseDetailNto, CisStatusEnum statusEnum) {
        return create(applyId, (CisDgimgApplyDetailNto) baseDetailNto, statusEnum);
    }

    public CisDgimgApplyDetail create(String applyId, CisDgimgApplyDetailNto cisDgimgApplyDetailNto, CisStatusEnum statusEnum, Boolean save) {
        BusinessAssert.notNull(cisDgimgApplyDetailNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisDgimgApplyDetailNto不能为空！");
        super.create(applyId, cisDgimgApplyDetailNto, statusEnum, save);

        setId(cisDgimgApplyDetailNto.getId());
        setApplyId(cisDgimgApplyDetailNto.getCisDgimgApplyId());
        setDgimgName(cisDgimgApplyDetailNto.getDgimgName());
        setHumanOrgans(cisDgimgApplyDetailNto.getHumanOrgans());
        setHumanOrgansName(cisDgimgApplyDetailNto.getHumanOrgansName());
        setHumanSystem(cisDgimgApplyDetailNto.getHumanSystem());
        setHumanSystemName(cisDgimgApplyDetailNto.getHumanSystemName());
        setMethod(cisDgimgApplyDetailNto.getMethod());
        setMethodName(cisDgimgApplyDetailNto.getMethodName());
        setDirection(cisDgimgApplyDetailNto.getDirection());
        setDirectionName(cisDgimgApplyDetailNto.getDirectionName());
        setLayer(cisDgimgApplyDetailNto.getLayer());
        setLayerName(cisDgimgApplyDetailNto.getLayerName());
        setRange(cisDgimgApplyDetailNto.getRange());
        setRangeName(cisDgimgApplyDetailNto.getRangeName());
        setRequirementPurpose(cisDgimgApplyDetailNto.getRequirementPurpose());
        setSortNo(cisDgimgApplyDetailNto.getSortNo());
        setUnilateralFlag(cisDgimgApplyDetailNto.getUnilateralFlag());

        setDgimgCode(cisDgimgApplyDetailNto.getDgimgCode());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        if (save) {
            dao().save(this);
        }
        return this;
    }

    public void update(CisDgimgApplyDetailEto cisDgimgApplyDetaileto) {
        super.update(cisDgimgApplyDetaileto);
        setSortNo(cisDgimgApplyDetaileto.getNo());
        setDgimgName(cisDgimgApplyDetaileto.getDgimgName());
        setHumanOrgans(cisDgimgApplyDetaileto.getHumanOrgans());
        setHumanOrgansName(cisDgimgApplyDetaileto.getHumanOrgansName());
        setHumanSystem(cisDgimgApplyDetaileto.getHumanSystem());
        setHumanSystemName(cisDgimgApplyDetaileto.getHumanSystemName());
        setMethod(cisDgimgApplyDetaileto.getMethod());
        setMethodName(cisDgimgApplyDetaileto.getMethodName());
        setRange(cisDgimgApplyDetaileto.getRange());
        setRangeName(cisDgimgApplyDetaileto.getRangeName());
        setDirection(cisDgimgApplyDetaileto.getDirection());
        setDirectionName(cisDgimgApplyDetaileto.getDirectionName());
        setLayer(cisDgimgApplyDetaileto.getLayer());
        setLayerName(cisDgimgApplyDetaileto.getLayerName());
        setOperation(cisDgimgApplyDetaileto.getOperation());
        setOperationName(cisDgimgApplyDetaileto.getOperationName());
        setRequirementPurpose(cisDgimgApplyDetaileto.getRequirementPurpose());
        setUnilateralFlag(cisDgimgApplyDetaileto.getUnilateralFlag());
        setDgimgCode(cisDgimgApplyDetaileto.getDgimgCode());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
    }

    public void delete() {
        dao().delete(this);
    }

}
