package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.business.util.mq.to.cis.CisAntimicrobialsSkinExecMqNto;
import com.bjgoodwill.hip.business.util.mq.to.cis.CisIpdOrderExtNto;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisProofEto;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;
import com.bjgoodwill.hip.ds.cis.apply.mq.send.CisApplyMqSend;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-12 09:12
 * @className: ExecuteService
 * @description: 执行单执行 服务
 **/
@Component
public class ExecuteService {

    @Autowired
    private CisApplyMqSend cisApplyMqSend;

    public void proofStopExecPlans(List<CisProofEto> etos) {
        processCisProofEtoWithExecPlans(etos, CisOrderExecPlan::obsolete, null);
    }

    public void exexPlansExecute(List<CisProofEto> etos) {
        List<CisOrderExecPlan> cisOrderExecPlans = new ArrayList<>();
        Map<String, CisBaseApply> cisBaseApplyMap = processCisProofEtoWithExecPlans(etos, CisOrderExecPlan::exec, cisOrderExecPlans);
        //校验申请单状态
        verifyStatus(cisOrderExecPlans, CisStatusEnum.COMPLETED);
        cisOrderExecPlans.stream().filter(CisOrderExecPlan::getMainPlanFlag).forEach(p -> {
            // 更新医嘱状态
            updateCisOrderStatus(cisBaseApplyMap, CisStatusEnum.COMPLETED);
            // 回写医嘱扩展信息
            writeIpdOrderExt(p);
        });
        // 调用把皮试药和抗菌药写入cdr
        exexPlansWriteCdr(etos);
    }

    /**
     * 执行发送消息回写医嘱扩展信息
     *
     * @param p 订单执行计划对象，包含执行计划的详细信息
     */
    public void writeIpdOrderExt(CisOrderExecPlan cisOrderExecPlan) {
        Map<String, Object> map = getFieldsValues(cisOrderExecPlan, Arrays.asList("execStaff", "execStaffName", "stStaffA", "stStaffAName", "stStaffB", "stStaffBName"));
        List<CisIpdOrderExtNto> ntoList = map.entrySet().stream()
                .filter(entry -> !ObjectUtils.isEmpty(entry.getValue()))
                .map(entry -> {
                    CisIpdOrderExtNto nto = new CisIpdOrderExtNto();
                    nto.setOrderId(cisOrderExecPlan.getOrderId());
                    nto.setKey(entry.getKey());
                    nto.setValue(String.valueOf(entry.getValue()));
                    nto.setVisitCode(cisOrderExecPlan.getVisitCode());
                    return nto;
                }).collect(Collectors.toList());
        cisApplyMqSend.IpdOrderExtSend(ntoList);
    }

    /**
     * 动态获取实体类中指定字段的值
     *
     * @param entity     实体对象
     * @param fieldNames 需要获取的字段名列表
     * @return 包含字段名和字段值的Map
     */
    private Map<String, Object> getFieldsValues(Object entity, List<String> fieldNames) {
        Map<String, Object> fieldValues = new LinkedHashMap<>();
        Class<?> clazz = entity.getClass();
        fieldNames.forEach(fieldName -> {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                fieldValues.put(fieldName, field.get(entity));
            } catch (Exception e) {
            }
        });
        return fieldValues;
    }

    public void cancelPlansExecute(List<CisProofEto> etos) {
        List<CisOrderExecPlan> cisOrderExecPlans = new ArrayList<>();
        Map<String, CisBaseApply> cisBaseApplyMap = processCisProofEtoWithExecPlans(etos, CisOrderExecPlan::cancel, cisOrderExecPlans);
        verifyStatus(cisOrderExecPlans, CisStatusEnum.CANCELEXCUTE);
        cisOrderExecPlans.stream().filter(CisOrderExecPlan::getMainPlanFlag).forEach(p -> {
            // 更新医嘱状态
            updateCisOrderStatus(cisBaseApplyMap, CisStatusEnum.PASS);
        });
    }

    public void noExecPlansExecute(List<CisProofEto> etos) {
        List<CisOrderExecPlan> cisOrderExecPlans = new ArrayList<>();
        Map<String, CisBaseApply> cisBaseApplyMap = processCisProofEtoWithExecPlans(etos, CisOrderExecPlan::noExec, cisOrderExecPlans);
        cisOrderExecPlans.stream().filter(CisOrderExecPlan::getMainPlanFlag).forEach(p -> {
            // 更新医嘱状态
            updateCisOrderStatus(cisBaseApplyMap, CisStatusEnum.PASS);
        });
    }

    /**
     * 处理CisProofEto列表，根据执行计划ID筛选并执行给定的操作。
     * 此方法首先通过应用ID收集CisBaseApply对象，然后针对每个CisProofEto对象，
     * 如果其执行计划ID存在于对应的CisBaseApply的执行计划列表中，则调用提供的Consumer函数处理该执行计划。
     *
     * @param cisProofEtos CisProofEto对象的列表，包含待处理的证明信息。
     * @param func         一个消费者函数，用于处理匹配的CisOrderExecPlan对象。
     */
    private Map<String, CisBaseApply> processCisProofEtoWithExecPlans(List<CisProofEto> cisProofEtos, Consumer<CisOrderExecPlan> func, List<CisOrderExecPlan> cisOrderExecPlans) {
        // 使用HashMap存储CisBaseApply对象，以应用ID为键，提高后续查询效率。
        // 创建一个HashMap以优化查询性能
        Map<String, CisBaseApply> cisBaseApplyMap = new HashMap<>();
        // 检查输入列表是否为空，如果是，则直接返回。
        // 检查输入列表是否为空
        if (cisProofEtos == null || cisProofEtos.isEmpty()) {
            return cisBaseApplyMap;
        }

        // 通过应用ID收集CisBaseApply对象。
        // 填充cisBaseApplyMap以避免重复查询
        cisProofEtos.stream()
                .distinct()
                .forEach(apply -> {
                    Optional<CisBaseApply> cisBaseApplyOptional = CisBaseApply.getCisBaseApplyById(apply.getApplyId());
                    cisBaseApplyOptional.ifPresent(cisBaseApply -> {
                        cisBaseApplyMap.put(cisBaseApply.getId(), cisBaseApply);
                        //临时医嘱执行要写执行人和执行时间，并更新状态。
                        if (OrderTypeEnum.TEMPORARY_ORDER.equals(apply.getOrderType())) {
                            cisBaseApply.exec();
                        }
                    });
                });

        // 遍历每个CisProofEto对象，筛选并处理匹配的CisOrderExecPlan对象。
        // 处理每个CisProofEto对象
        cisProofEtos.forEach(o -> {
            Optional<CisBaseApply> optionalCisBaseApply = Optional.ofNullable(cisBaseApplyMap.get(o.getApplyId()));
            optionalCisBaseApply.ifPresent(c -> {
                // 将执行计划ID列表转换为HashSet，以提高查找效率。
                // 将getExecPlanIds()的结果转换为HashSet以提高性能
                Set<String> execPlanIdsSet = new HashSet<>(o.getExecPlanIds());

                cisOrderExecPlans.addAll(c.getCisOrderExecPlans().stream()
                        .filter(p -> execPlanIdsSet.contains(p.getId())).collect(Collectors.toList()));
                // 筛选并处理匹配的执行计划。
                // 使用Stream操作处理过滤后的CisOrderExecPlan列表
                c.getCisOrderExecPlans().stream()
                        .filter(p -> execPlanIdsSet.contains(p.getId()))
                        .forEach(func);
            });
        });
        return cisBaseApplyMap;
    }

    /*
     * 将抗菌药皮试药写入CDR
     */
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void exexPlansWriteCdr(List<CisProofEto> etos) {
        // 查询皮试和抗菌药的执行计划
        List<CisAntimicrobialsSkinExecMqNto> ntos = etos.stream()
                .filter(q -> !CollectionUtils.isEmpty(q.getExecPlanIds()))
                .flatMap(a -> Stream.concat(
                        CisOrderExecPlan.findExecCisOrderExecPlansAntimicrobialsByIds(a.getExecPlanIds()).stream(),
                        CisOrderExecPlan.findExecCisOrderExecPlansSkinByIds(a.getExecPlanIds()).stream()
                )).map(a -> {
                    CisAntimicrobialsSkinExecMqNto nto = new CisAntimicrobialsSkinExecMqNto();
                    nto.setId(HIPIDUtil.getNextIdString());
                    nto.setVisitCode(a.getVisitCode());
                    nto.setPatMiCode(a.getPatMiCode());
                    nto.setVisitType(VisitTypeEnum.IPD);
                    nto.setSystemType(a.getOrderClass());
                    nto.setServiceItemCode(a.getServiceItemCode());
                    nto.setServiceItemName(a.getServiceItemName());
                    nto.setOrderId(a.getOrderId());
                    nto.setApplyId(a.getCisBaseApplyId());
                    nto.setExecPlanId(a.getId());
                    nto.setExecPlanDate(LocalDateUtil.now());
                    return nto;
                }).collect(Collectors.toList());

        ntos.forEach(nto -> cisApplyMqSend.CisAntimicrobialsSkinExecCreateSend(nto));
    }

    private void updateCisOrderStatus(Map<String, CisBaseApply> cisBaseApplyMap, CisStatusEnum statusEnum) {
        // 更新医嘱状态
        cisBaseApplyMap.entrySet().stream()
                .filter(entry -> OrderTypeEnum.TEMPORARY_ORDER.equals(entry.getValue().getOrderType()))
                .peek(entry -> {
                    entry.getValue().setStatusCode(statusEnum);
                    cisApplyMqSend.OrderStatueSend(entry.getValue());
                }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    public void verifyStatus(List<CisOrderExecPlan> items, CisStatusEnum statusEnum) {
        switch (statusEnum) {
            case COMPLETED -> {
                // 校验申请单状态
                if (!CollectionUtils.isEmpty(items)) {
                    List<String> applyIds = items.stream().map(CisOrderExecPlan::getCisBaseApplyId).distinct().toList();
                    List<CisBaseApply> applies = CisBaseApply.findCisBaseAppliesByApplyIDIn(applyIds);
                    List<CisBaseApply> passApplies = applies.stream().filter(a -> !(CisStatusEnum.PASS.equals(a.getStatusCode())
                            || CisStatusEnum.ACTIVE.equals(a.getStatusCode())
                            || CisStatusEnum.COMPLETED.equals(a.getStatusCode()))).toList();
                    BusinessAssert.isEmpty(passApplies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0008, "申请单");
                    // 执行状态更新
                    applies.stream().filter(a -> OrderTypeEnum.TEMPORARY_ORDER.equals(a.getOrderType())).forEach(a -> a.exec());
                }
            }
            case CANCELEXCUTE -> {
                if (!CollectionUtils.isEmpty(items)) {
                    List<String> applyIds = items.stream().map(CisOrderExecPlan::getCisBaseApplyId).distinct().toList();
                    List<CisBaseApply> applies = CisBaseApply.findCisBaseAppliesByApplyIDIn(applyIds).stream().filter(a -> OrderTypeEnum.TEMPORARY_ORDER.equals(a.getOrderType())).toList();
                    applies.forEach(a -> a.proof2());
                    List<String> allIds = applies.stream().map(CisBaseApply::getOrderID).toList();
                    if (!CollectionUtils.isEmpty(allIds)) {
                        List<String> expandIds = items.stream().map(CisOrderExecPlan::getId).distinct().toList();
                        List<CisOrderExecPlan> execPlans = CisOrderExecPlan.findCisOrderExecPlansByOrderIdIn(allIds).stream().filter(a -> !expandIds.contains(a.getId())).toList();
                        execPlans.forEach(a -> a.cancel());
                    }
                }
            }
        }
    }

}