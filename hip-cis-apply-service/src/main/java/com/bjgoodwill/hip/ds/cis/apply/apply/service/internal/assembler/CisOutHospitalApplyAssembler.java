package com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisOutHospitalApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisOutHospitalApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisOutHospitalApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;

import java.util.ArrayList;
import java.util.List;

public abstract class CisOutHospitalApplyAssembler {

    public static List<CisOutHospitalApplyTo> toTos(List<CisOutHospitalApply> cisOutHospitalApplys) {
        return toTos(cisOutHospitalApplys, false);
    }

    public static List<CisOutHospitalApplyTo> toTos(List<CisOutHospitalApply> cisOutHospitalApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisOutHospitalApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisOutHospitalApplys不能为空！");

        List<CisOutHospitalApplyTo> tos = new ArrayList<>();
        for (CisOutHospitalApply cisOutHospitalApply : cisOutHospitalApplys)
            tos.add(toTo(cisOutHospitalApply, withAllParts));
        return tos;
    }

    public static CisOutHospitalApplyTo toTo(CisOutHospitalApply cisOutHospitalApply) {
        return toTo(cisOutHospitalApply, false);
    }

    /**
     * @generated
     */
    public static CisOutHospitalApplyTo toTo(CisOutHospitalApply cisOutHospitalApply, boolean withAllParts) {
        if (cisOutHospitalApply == null)
            return null;
        CisOutHospitalApplyTo to = new CisOutHospitalApplyTo();
        to.setId(cisOutHospitalApply.getId());
        to.setPatMiCode(cisOutHospitalApply.getPatMiCode());
        to.setVisitCode(cisOutHospitalApply.getVisitCode());
        to.setServiceItemCode(cisOutHospitalApply.getServiceItemCode());
        to.setServiceItemName(cisOutHospitalApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisOutHospitalApply.getIsCanPriorityFlag());
        to.setStatusCode(cisOutHospitalApply.getStatusCode());
        to.setCreatedStaff(cisOutHospitalApply.getCreatedStaff());
        to.setCreatedDate(cisOutHospitalApply.getCreatedDate());
        to.setUpdatedStaff(cisOutHospitalApply.getUpdatedStaff());
        to.setUpdatedDate(cisOutHospitalApply.getUpdatedDate());
        to.setExecutorStaff(cisOutHospitalApply.getExecutorStaff());
        to.setExecutorDate(cisOutHospitalApply.getExecutorDate());
        to.setExecutorHosptialCode(cisOutHospitalApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisOutHospitalApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisOutHospitalApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisOutHospitalApply.getMedrecordExamabstractId());
        to.setVisitType(cisOutHospitalApply.getVisitType());
        to.setDeptNurseCode(cisOutHospitalApply.getDeptNurseCode());
        to.setDeptNurseName(cisOutHospitalApply.getDeptNurseName());
        to.setOrderID(cisOutHospitalApply.getOrderID());
        to.setHospitalCode(cisOutHospitalApply.getHospitalCode());
        to.setPrescriptionID(cisOutHospitalApply.getPrescriptionID());
        to.setIsPrint(cisOutHospitalApply.getIsPrint());
        to.setPrintStaff(cisOutHospitalApply.getPrintStaff());
        to.setPrintDate(cisOutHospitalApply.getPrintDate());
        to.setReMark(cisOutHospitalApply.getReMark());
        to.setIcuExecuteDate(cisOutHospitalApply.getIcuExecuteDate());
        to.setIsChargeManager(cisOutHospitalApply.getIsChargeManager());
        to.setVersion(cisOutHospitalApply.getVersion());
        to.setCreateOrgCode(cisOutHospitalApply.getCreateOrgCode());
        to.setSortNo(cisOutHospitalApply.getSortNo());
        to.setIsBaby(cisOutHospitalApply.getIsBaby());
        to.setOutDate(cisOutHospitalApply.getOutDate());
        to.setDischargeDisposition(cisOutHospitalApply.getDischargeDisposition());
        to.setVisitOrgCode(cisOutHospitalApply.getVisitOrgCode());
        to.setVisitOrgName(cisOutHospitalApply.getVisitOrgName());
        to.setIsOlation(cisOutHospitalApply.getIsOlation());
        to.setIsApply(cisOutHospitalApply.getIsApply());
        to.setNum(cisOutHospitalApply.getNum());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisOutHospitalApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisOutHospitalApply.getCisOrderExecPlans()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisOutHospitalApply.getApplyDiagnoses()));

        }
        return to;
    }


    public static CisOutHospitalApplyNto toNto(CisOutHospitalApply cisOutHospitalApply, boolean withAllParts) {
        if (cisOutHospitalApply == null)
            return null;
        CisOutHospitalApplyNto to = new CisOutHospitalApplyNto();
        to.setId(cisOutHospitalApply.getId());
        to.setPatMiCode(cisOutHospitalApply.getPatMiCode());
        to.setVisitCode(cisOutHospitalApply.getVisitCode());
        to.setServiceItemCode(cisOutHospitalApply.getServiceItemCode());
        to.setServiceItemName(cisOutHospitalApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisOutHospitalApply.getIsCanPriorityFlag());
//        to.setMedrecordExamabstractId(cisOutHospitalApply.getMedrecordExamabstractId());
        to.setVisitType(cisOutHospitalApply.getVisitType());
        to.setDeptNurseCode(cisOutHospitalApply.getDeptNurseCode());
        to.setDeptNurseName(cisOutHospitalApply.getDeptNurseName());
        to.setOrderID(cisOutHospitalApply.getOrderID());
        to.setHospitalCode(cisOutHospitalApply.getHospitalCode());
        to.setPrescriptionID(cisOutHospitalApply.getPrescriptionID());
//        to.setIsPrint(cisOutHospitalApply.getIsPrint());
//        to.setPrintStaff(cisOutHospitalApply.getPrintStaff());
//        to.setPrintDate(cisOutHospitalApply.getPrintDate());
        to.setReMark(cisOutHospitalApply.getReMark());
        to.setIcuExecuteDate(cisOutHospitalApply.getIcuExecuteDate());
        to.setIsChargeManager(cisOutHospitalApply.getIsChargeManager());
        to.setCreateOrgCode(cisOutHospitalApply.getCreateOrgCode());
        to.setSortNo(cisOutHospitalApply.getSortNo());
        to.setIsBaby(cisOutHospitalApply.getIsBaby());
        to.setOutDate(cisOutHospitalApply.getOutDate());
        to.setDischargeDisposition(cisOutHospitalApply.getDischargeDisposition());
        to.setVisitOrgCode(cisOutHospitalApply.getVisitOrgCode());
        to.setOrderType(cisOutHospitalApply.getOrderType());
        to.setVisitOrgName(cisOutHospitalApply.getVisitOrgName());
        to.setCreateOrgName(cisOutHospitalApply.getCreateOrgName());
        to.setExecutorOrgCode(cisOutHospitalApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisOutHospitalApply.getExecutorOrgName());
        to.setNum(cisOutHospitalApply.getNum());
        to.setIsOlation(cisOutHospitalApply.getIsOlation());
        to.setIsApply(cisOutHospitalApply.getIsApply());
        if (withAllParts) {
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisOutHospitalApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisOutHospitalApply.getCisOrderExecPlans()));
        }
        return to;
    }

}