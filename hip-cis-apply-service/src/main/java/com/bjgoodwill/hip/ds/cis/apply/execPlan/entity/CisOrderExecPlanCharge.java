package com.bjgoodwill.hip.ds.cis.apply.execPlan.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.econ.enums.SystemItemClassEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.repository.CisOrderExecPlanChargeRepository;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanChargeEto;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanChargeQto;
import com.bjgoodwill.hip.jpa.core.SnowflakeIdGenerator;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import com.google.common.collect.Lists;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "医嘱执行档费用从表")
@Table(name = "cis_order_exec_plan_charge", indexes = {
        @Index(name = "cis_order_exec_plan_charge_cisOrderExecPlanId", columnList = "cisOrderExecPlanId"),
        @Index(name = "cis_order_exec_plan_charge_visit_Code", columnList = "visit_code"),
        @Index(name = "cis_order_exec_plan_charge_apply_id", columnList = "apply_id")}, uniqueConstraints = {})
public class CisOrderExecPlanCharge {

    // 标识
    private String id;
    // 医嘱执行档标识
    private String cisOrderExecPlanId;
    // 抽象父类标识
    private String cisBaseApplyId;
    // 患者接诊流水号
    private String visitCode;
    // 收费项目编码
    private String priceItemCode;
    // 收费项目名称
    private String priceItemName;
    // 包装规格
    private String packageSpec;
    // 单价
    private BigDecimal price;
    // 单位 字典Measures
    private String unit;
    // 单位 字典Measures
    private String unitName;
    // 数量
    private Double num;
    // 应收
    private BigDecimal chargeAmount;
    // 是否为固定项
    private Boolean isFixed;
    // 特限符合标识 1符合；0 不符合
    private Boolean limitConformFlag;
    // 执行/取药科室
    private String executeOrgCode;
    // 执行/取药科室名称
    private String executeOrgName;
//    //计费状态
//    private SetlStasEnum setlStas;
    //补费类型
    private CisChargeTypeEnum chargeType;

    private SystemItemClassEnum systemItemClass;

    public static Optional<CisOrderExecPlanCharge> getCisOrderExecPlanChargeById(String id) {
        return dao().findById(id);
    }

    public static List<CisOrderExecPlanCharge> getCisOrderExecPlanCharges(CisOrderExecPlanChargeQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisOrderExecPlanCharge> getCisOrderExecPlanChargePage(CisOrderExecPlanChargeQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisOrderExecPlanCharge> getByCisOrderExecPlanId(String cisOrderExecPlanId) {
        return dao().findByCisOrderExecPlanId(cisOrderExecPlanId);
    }

    public static List<CisOrderExecPlanCharge> findByCisOrderExecPlanIdsIn(List<String> cisOrderExecPlanIds) {
        BusinessAssert.notEmpty(cisOrderExecPlanIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "cisOrderExecPlanIds");
        return Lists.partition(cisOrderExecPlanIds, 100).stream().map(p -> dao().findByCisOrderExecPlanIdByCisOrderExecPlanIds(p))
                .flatMap(List::stream).toList();
    }

    public static void deleteByCisOrderExecPlanId(String cisOrderExecPlanId) {
        dao().deleteByCisOrderExecPlanId(cisOrderExecPlanId);
    }

    public static List<CisOrderExecPlanCharge> getByCisBaseApplyId(String cisBaseApplyId) {
        return dao().findByCisBaseApplyId(cisBaseApplyId);
    }

    public static List<CisOrderExecPlanCharge> findByCisBaseApplyIdIn(List<String> cisBaseApplyIds) {
        return Lists.partition(cisBaseApplyIds, 100).stream().map(p -> dao().findByCisBaseApplyIdIn(p))
                .flatMap(List::stream).toList();
    }

    public static void deleteByCisBaseApplyId(String cisBaseApplyId) {
        dao().deleteByCisBaseApplyId(cisBaseApplyId);
    }

    /**
     * @generated
     */
    private static Specification<CisOrderExecPlanCharge> getSpecification(CisOrderExecPlanChargeQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getCisOrderExecPlanId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisOrderExecPlanId"), qto.getCisOrderExecPlanId()));
            }
            if (StringUtils.isNotBlank(qto.getCisBaseApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisBaseApplyId"), qto.getCisBaseApplyId()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            return predicate;
        };
    }

    private static CisOrderExecPlanChargeRepository dao() {
        return SpringUtil.getBean(CisOrderExecPlanChargeRepository.class);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    @GeneratedValue(generator = "snowflake_generator")
    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("医嘱执行档标识")
    @Column(name = "cis_order_exec_plan_id", nullable = false, length = 50)
    public String getCisOrderExecPlanId() {
        return cisOrderExecPlanId;
    }

    protected void setCisOrderExecPlanId(String cisOrderExecPlanId) {
        this.cisOrderExecPlanId = cisOrderExecPlanId;
    }

    @Comment("抽象父类标识")
    @Column(name = "apply_id", nullable = true, length = 50)
    public String getCisBaseApplyId() {
        return cisBaseApplyId;
    }

    protected void setCisBaseApplyId(String cisBaseApplyId) {
        this.cisBaseApplyId = cisBaseApplyId;
    }

    @Comment("患者接诊流水号")
    @Column(name = "visit_code", nullable = false)
    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    @Comment("收费项目编码")
    @Column(name = "price_item_code", nullable = false)
    public String getPriceItemCode() {
        return priceItemCode;
    }

    protected void setPriceItemCode(String priceItemCode) {
        this.priceItemCode = priceItemCode;
    }

    @Comment("收费项目名称")
    @Column(name = "price_item_name", nullable = true)
    public String getPriceItemName() {
        return priceItemName;
    }

    protected void setPriceItemName(String priceItemName) {
        this.priceItemName = priceItemName;
    }

    @Comment("包装规格")
    @Column(name = "package_spec", nullable = true)
    public String getPackageSpec() {
        return packageSpec;
    }

    protected void setPackageSpec(String packageSpec) {
        this.packageSpec = packageSpec;
    }

    @Comment("单价")
    @Column(name = "price", nullable = false)
    public BigDecimal getPrice() {
        return price;
    }

    protected void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Comment("单位 字典Measures")
    @Column(name = "unit", nullable = true)
    public String getUnit() {
        return unit;
    }

    protected void setUnit(String unit) {
        this.unit = unit;
    }

    @Comment("数量")
    @Column(name = "num", nullable = true)
    public Double getNum() {
        return num;
    }

    protected void setNum(Double num) {
        this.num = num;
    }

    @Comment("应收")
    @Column(name = "charge_amount", nullable = true)
    public BigDecimal getChargeAmount() {
        return chargeAmount;
    }

    protected void setChargeAmount(BigDecimal chargeAmount) {
        this.chargeAmount = chargeAmount;
    }

    @Comment("是否为固定项")
    @Column(name = "is_fixed", nullable = true)
    public Boolean getIsFixed() {
        return isFixed;
    }

    protected void setIsFixed(Boolean isFixed) {
        this.isFixed = isFixed;
    }

    @Comment("特限符合标识 1符合；0 不符合")
    @Column(name = "limit_conform_flag", nullable = true)
    public Boolean getLimitConformFlag() {
        return limitConformFlag;
    }

    protected void setLimitConformFlag(Boolean limitConformFlag) {
        this.limitConformFlag = limitConformFlag;
    }

    @Comment("执行/取药科室")
    @Column(name = "execute_org_code", nullable = false)
    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    protected void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = executeOrgCode;
    }

//    @Comment("收费状态")
//    @Column(name = "setl_stas", nullable = true)
//    public SetlStasEnum getSetlStas() {
//        return setlStas;
//    }

//    public void setSetlStas(SetlStasEnum setlStas) {
//        this.setlStas = setlStas;
//    }

    @Comment("单位名称 字典Measures")
    @Column(name = "unit_name", nullable = true)
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Comment("执行/取药科室名称")
    @Column(name = "execute_org_name", nullable = true)
    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    @Comment("补费类型")
    @Enumerated(EnumType.STRING)
    @Column(name = "charge_type", nullable = false)
    public CisChargeTypeEnum getChargeType() {
        return chargeType;
    }

    public void setChargeType(CisChargeTypeEnum chargeType) {
        this.chargeType = chargeType;
    }

    @Comment("系统项目分类")
    @Enumerated(EnumType.STRING)
    @Column(name = "system_item_class", nullable = false)
    public SystemItemClassEnum getSystemItemClass() {
        return systemItemClass;
    }

    public void setSystemItemClass(SystemItemClassEnum systemItemClass) {
        this.systemItemClass = systemItemClass;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOrderExecPlanCharge other = (CisOrderExecPlanCharge) obj;
        return Objects.equals(id, other.id);
    }

    /**
     * 创建医嘱执行档费用记录
     *
     * @param cisOrderExecPlanId        医嘱执行档ID
     * @param cisOrderExecPlanChargeNto 费用数据传输对象
     * @return 创建的实体对象
     */
    private CisOrderExecPlanCharge create(String cisOrderExecPlanId, CisOrderExecPlanChargeNto cisOrderExecPlanChargeNto) {
        BusinessAssert.notNull(cisOrderExecPlanChargeNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisOrderExecPlanChargeNto不能为空！");

        // 设置基础信息
        setCisOrderExecPlanId(cisOrderExecPlanId);
        setCisBaseApplyId(cisOrderExecPlanChargeNto.getCisBaseApplyId());
        setVisitCode(cisOrderExecPlanChargeNto.getVisitCode());

        // 设置收费项目信息
        setPriceItemCode(cisOrderExecPlanChargeNto.getPriceItemCode());
        setPriceItemName(cisOrderExecPlanChargeNto.getPriceItemName());
        setPackageSpec(cisOrderExecPlanChargeNto.getPackageSpec());
        setPrice(cisOrderExecPlanChargeNto.getPrice());

        // 设置单位和数量
        setUnit(cisOrderExecPlanChargeNto.getUnit());
        setUnitName(cisOrderExecPlanChargeNto.getUnitName());
        setNum(cisOrderExecPlanChargeNto.getNum());
        setChargeAmount(cisOrderExecPlanChargeNto.getChargeAmount());

        // 设置业务标志
        setIsFixed(cisOrderExecPlanChargeNto.getIsFixed());
        setLimitConformFlag(cisOrderExecPlanChargeNto.getLimitConformFlag());

        // 设置执行科室信息
        setExecuteOrgCode(cisOrderExecPlanChargeNto.getExecuteOrgCode());
        setExecuteOrgName(cisOrderExecPlanChargeNto.getExecuteOrgName());

        // 设置收费类型和系统分类
        setChargeType(cisOrderExecPlanChargeNto.getChargeType());
        setSystemItemClass(cisOrderExecPlanChargeNto.getSystemItemClass());

        return this;
    }

    /**
     * 创建并保存医嘱执行档费用记录
     *
     * @param cisOrderExecPlanId        医嘱执行档ID
     * @param cisOrderExecPlanChargeNto 费用数据传输对象
     * @param save                      是否立即保存
     * @return 创建的实体对象
     */
    public CisOrderExecPlanCharge create(String cisOrderExecPlanId, CisOrderExecPlanChargeNto cisOrderExecPlanChargeNto, Boolean save) {
        create(cisOrderExecPlanId, cisOrderExecPlanChargeNto);
        if (Boolean.TRUE.equals(save)) {
            dao().save(this);
        }
        return this;
    }

    public CisOrderExecPlanCharge create(String cisOrderExecPlanId, List<CisOrderExecPlanChargeNto> cisOrderExecPlanChargeNtos, Boolean save) {
        cisOrderExecPlanChargeNtos.forEach(cisOrderExecPlanChargeNto -> create(cisOrderExecPlanId, cisOrderExecPlanChargeNto));
//        create(cisOrderExecPlanId, cisOrderExecPlanChargeNto);
        if (Boolean.TRUE.equals(save)) {
            dao().save(this);
        }
        return this;
    }

    /**
     * 更新医嘱执行档费用记录
     *
     * @param cisOrderExecPlanChargeEto 费用编辑传输对象
     * @return 更新后的实体对象
     */
    public CisOrderExecPlanCharge update(CisOrderExecPlanChargeEto cisOrderExecPlanChargeEto) {
        setNum(cisOrderExecPlanChargeEto.getNum());
        setChargeAmount(cisOrderExecPlanChargeEto.getChargeAmount());
        return this;
    }

    /**
     * 删除当前医嘱执行档费用记录
     */
    public void delete() {
        dao().delete(this);
    }

}
