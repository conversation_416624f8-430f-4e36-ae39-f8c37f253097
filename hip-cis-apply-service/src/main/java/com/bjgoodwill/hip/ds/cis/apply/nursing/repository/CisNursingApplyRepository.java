package com.bjgoodwill.hip.ds.cis.apply.nursing.repository;

import com.bjgoodwill.hip.ds.cis.apply.nursing.entity.CisNursingApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisNursingApplyRepository")
public interface CisNursingApplyRepository extends JpaRepository<CisNursingApply, String>, JpaSpecificationExecutor<CisNursingApply> {

}