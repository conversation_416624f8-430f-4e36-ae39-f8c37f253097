package com.bjgoodwill.hip.ds.cis.apply.medicalhistory.service.internal;

import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.entity.CisMedicalHistory;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.service.CisMedicalHistoryService;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.service.internal.assembler.CisMedicalHistoryAssembler;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryEto;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryNto;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryTo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 住院患者病史实现类
 * @date 2025/5/8 9:57
 */
@RestController("com.bjgoodwill.hip.ds.cis.apply.medicalhistory.service.CisMedicalHistoryService")
@RequestMapping(value = "/api/apply/medicalhistory/cisMedicalHistoryService", produces = "application/json; charset=utf-8")
public class CisMedicalHistoryServiceImpl implements CisMedicalHistoryService {
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisMedicalHistoryTo getCisMedicalHistoryById(String id) {
        return CisMedicalHistoryAssembler.toTo(CisMedicalHistory.getCisMedicalHistoryById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void createCisMedicalHistory(CisMedicalHistoryNto cisMedicalHistoryNto) {
        CisMedicalHistory old = CisMedicalHistory.getByVisitCode(cisMedicalHistoryNto.getVisitCode());
        //已存在，update
        if (old != null) {
            CisMedicalHistoryEto eto = HIPBeanUtil.copy(cisMedicalHistoryNto, CisMedicalHistoryEto.class);
            this.updateCisMedicalHistory(cisMedicalHistoryNto.getId(), eto);
            return;
        }
        //不存在，create
        CisMedicalHistory cisMedicalHistory = new CisMedicalHistory();
        cisMedicalHistory.create(cisMedicalHistoryNto);
    }


    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisMedicalHistoryTo getCisMedicalHistoryByVisitCode(String visitCode) {
        return CisMedicalHistoryAssembler.toTo(CisMedicalHistory.getByVisitCode(visitCode));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisMedicalHistory(String id, CisMedicalHistoryEto cisMedicalHistoryEto) {
        CisMedicalHistory cisMedicalHistory = HIPBeanUtil.copy(cisMedicalHistoryEto, CisMedicalHistory.class);
        cisMedicalHistory.setId(id);
        cisMedicalHistory.update(cisMedicalHistoryEto);
    }
}