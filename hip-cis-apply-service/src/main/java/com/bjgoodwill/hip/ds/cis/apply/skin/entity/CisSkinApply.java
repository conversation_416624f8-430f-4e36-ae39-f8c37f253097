package com.bjgoodwill.hip.ds.cis.apply.skin.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.skin.repository.CisSkinApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.skin.to.CisSkinApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.skin.to.CisSkinApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.skin.to.CisSkinApplyQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "skin")
@DiscriminatorValue("26")
public class CisSkinApply extends CisBaseApply {


    private String frequency;

    private Double dosage;


    private String dosageUnit;

    private String usage;

    private String usageName;

    private String receiveOrg;

    private String receiveOrgName;

    public static Optional<CisSkinApply> getCisSkinApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisSkinApply> getCisSkinApplies(CisSkinApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisSkinApply> getCisSkinApplyPage(CisSkinApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisSkinApply> getSpecification(CisSkinApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getReceiveOrg())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("receiveOrg"), qto.getReceiveOrg()));
            }
            return predicate;
        };
    }

    private static CisSkinApplyRepository dao() {
        return SpringUtil.getBean(CisSkinApplyRepository.class);
    }

    @Comment("频次")
    @Column(name = "frequency", nullable = true)
    public String getFrequency() {
        return frequency;
    }

    protected void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    @Comment("每次剂量")
    @Column(name = "dosage", nullable = true)
    public Double getDosage() {
        return dosage;
    }

    protected void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    @Comment("剂量单位")
    @Column(name = "dosage_unit", nullable = true)
    public String getDosageUnit() {
        return dosageUnit;
    }

    protected void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    @Comment("用法")
    @Column(name = "usage", nullable = true)
    public String getUsage() {
        return usage;
    }

    protected void setUsage(String usage) {
        this.usage = usage;
    }

    @Comment("用法名称")
    @Column(name = "usage_name", nullable = true)
    public String getUsageName() {
        return usageName;
    }

    protected void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    @Comment("药房")
    @Column(name = "receive_org", nullable = true)
    public String getReceiveOrg() {
        return receiveOrg;
    }

    protected void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    @Comment("药房名称")
    @Column(name = "receive_org_name", nullable = true)
    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.SKIN;
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisSkinApplyNto) cisBaseApplyNto, save);
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisSkinApplyEto) cisBaseApplyEto);
    }

    public CisSkinApply create(CisSkinApplyNto cisSkinApplyNto, Boolean save) {
        Assert.notNull(cisSkinApplyNto, "参数cisSkinApplyNto不能为空！");
        super.create(cisSkinApplyNto, save);

        BusinessAssert.hasText(cisSkinApplyNto.getServiceItemCode(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "serviceItemCode");
        setServiceItemCode(cisSkinApplyNto.getServiceItemCode());

        setFrequency(cisSkinApplyNto.getFrequency());
        setDosage(cisSkinApplyNto.getDosage());
        setDosageUnit(cisSkinApplyNto.getDosageUnit());
        setUsage(cisSkinApplyNto.getUsage());
        setUsageName(cisSkinApplyNto.getUsageName());
        setReceiveOrg(cisSkinApplyNto.getReceiveOrg());
        setReceiveOrgName(cisSkinApplyNto.getReceiveOrgName());
        dao().save(this);

        return this;
    }

    public void update(CisSkinApplyEto cisSkinApplyEto) {
        super.update(cisSkinApplyEto);
        setDosage(cisSkinApplyEto.getDosage());
        setDosageUnit(cisSkinApplyEto.getDosageUnit());
        setReceiveOrg(cisSkinApplyEto.getReceiveOrg());
        setReceiveOrgName(cisSkinApplyEto.getReceiveOrgName());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
