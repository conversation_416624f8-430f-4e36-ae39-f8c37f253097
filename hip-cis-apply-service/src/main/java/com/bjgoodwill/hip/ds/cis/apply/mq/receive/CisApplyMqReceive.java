package com.bjgoodwill.hip.ds.cis.apply.mq.receive;

import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.CisOrderExecPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-09-19 09:23
 * @className: CisApplyMqReceive
 * @description: 申请单 数据接收
 **/
@Component
public class CisApplyMqReceive {

    @Autowired
    private CisOrderExecPlanService cisOrderExecPlanService;

//    //计费状态回写
//    @RabbitListener(bindings = @QueueBinding(
//            value = @Queue(value = CisApplyRabbitConfig.CIS_APPLY_SYNC_INOUT_QUEUE,
//                    arguments = {@Argument(name = "x-dead-letter-exchange", value = HipRabbitAutoConfiguration.DEAD_LETTER_EXCHANGE),
//                            @Argument(name = "x-dead-letter-routing-key", value = HipRabbitAutoConfiguration.DEAD_LETTER_ROUTING_KEY),
//                            @Argument(name = "x-single-active-consumer", value = "true", type = "java.lang.Boolean")}),
//            exchange = @Exchange(value = CisApplyRabbitConfig.CIS_APPLY_SYNC_EXCHANGE, type = ExchangeTypes.TOPIC),
//            key = {CisApplyRabbitConfig.CIS_APPLY_CHARGE_SYNC_ROUTING_KEY}
//    ), ackMode = "MANUAL")
//    public void receiveCharge(Channel channel, Message message) throws Exception {
//        try {
//            List<CisOrderExecPlanReceiveChargeEto> orderPlanList = MsgProcessUtil.getListBody(message, CisOrderExecPlanReceiveChargeEto.class);
//
//            if (!CollectionUtils.isEmpty(orderPlanList)) {
//                orderPlanList.forEach(item -> {
//                    CisOrderExecPlan cisOrderExecPlan = new CisOrderExecPlan();
//                    cisOrderExecPlan.setCharge(item);
//                });
//            }
//            MsgProcessUtil.success(channel, message);
//        } catch (Exception e) {
//            // TODO 需要定义领域类型、业务类型
//            MsgProcessUtil.failReTry(channel, message, "", "", e.getMessage());
//        }
//    }
//
//
//    //发药状态回写
//    @RabbitListener(bindings = @QueueBinding(
//            value = @Queue(value = CisApplyRabbitConfig.CIS_APPLY_SYNC_INOUT_QUEUE,
//                    arguments = {@Argument(name = "x-dead-letter-exchange", value = HipRabbitAutoConfiguration.DEAD_LETTER_EXCHANGE),
//                            @Argument(name = "x-dead-letter-routing-key", value = HipRabbitAutoConfiguration.DEAD_LETTER_ROUTING_KEY),
//                            @Argument(name = "x-single-active-consumer", value = "true", type = "java.lang.Boolean")}),
//            exchange = @Exchange(value = CisApplyRabbitConfig.CIS_APPLY_SYNC_EXCHANGE, type = ExchangeTypes.TOPIC),
//            key = {CisApplyRabbitConfig.CIS_APPLY_DRUG_SYNC_ROUTING_KEY}
//    ), ackMode = "MANUAL")
//    public void receiveDrug(Channel channel, Message message) throws Exception {
//        try {
//            List<String> orderPlanIdList = MsgProcessUtil.getListBody(message, String.class);
//
//            if (!CollectionUtils.isEmpty(orderPlanIdList)) {
//                for (String orderPlanId : orderPlanIdList) {
//                    CisOrderExecPlan cisOrderExecPlan = CisOrderExecPlan.findByOrderPlanId(orderPlanId).orElse(null);
//                    cisOrderExecPlan.setOrderPlanDrugInoutType(DrugIpdDataStatusEnum.已发);
//                }
//            }
//            MsgProcessUtil.success(channel, message);
//        } catch (Exception e) {
//            // TODO 需要定义领域类型、业务类型
//            MsgProcessUtil.failReTry(channel, message, "", "", e.getMessage());
//        }
//    }


//    @RabbitListener(bindings = @QueueBinding(
//            value = @Queue(value = CisApplyRabbitConfig.CIS_APPLY_SYNC_INOUT_QUEUE,
//                    arguments = {@Argument(name = "x-dead-letter-exchange", value = HipRabbitAutoConfiguration.DEAD_LETTER_EXCHANGE),
//                            @Argument(name = "x-dead-letter-routing-key", value = HipRabbitAutoConfiguration.DEAD_LETTER_ROUTING_KEY),
//                            @Argument(name = "x-single-active-consumer", value = "true", type = "java.lang.Boolean")}),
//            exchange = @Exchange(value = CisApplyRabbitConfig.CIS_APPLY_SYNC_EXCHANGE, type = ExchangeTypes.TOPIC),
//            key = {CisApplyRabbitConfig.CIS_APPLY_PLAN_SYNC_ROUTING_KEY}
//    ), ackMode = "MANUAL")
//    public void receicePlanExec(Channel channel, Message message) throws Exception {
//        try {
//            CisOrderExecPlanReceiveTo orderPlan = MsgProcessUtil.getBody(message, CisOrderExecPlanReceiveTo.class);
//            LoginInfo loginInfo = MsgProcessUtil.getLoginUser(message);
//            if (StringUtils.hasText(orderPlan.getExecPlanId())) {
//                // 直接给ID的单独写
//                cisOrderExecPlanService.executeCisOrderExecPlan(orderPlan.getExecPlanId());
//            } else {
//                CisBaseApplyQto qto = new CisBaseApplyQto();
//                qto.setStatusCode(CisStatusEnum.PASS);
//
//                qto.setVisitCode(orderPlan.getVisitCode());
//                qto.setOrderClass(orderPlan.getSystemType().getCode());
//                CisBaseApply apply = CisBaseApply.getCisBaseApplies(qto).stream().findFirst().orElse(null);
//                if (apply == null) {
//                    MsgProcessUtil.success(channel, message);
//                    return;
//                }
//                String execStaff = StringUtil.isNotBlank(loginInfo.getStaffId()) ? loginInfo.getStaffId() : loginInfo.getUserId();
//                String execStaffName = StringUtil.isNotBlank(loginInfo.getName()) ? loginInfo.getName() : loginInfo.getUserName();
//                apply.execMQ(execStaff);
//                CisOrderExecPlan.getByCisBaseApplyId(apply.getId()).stream().forEach(p -> p.execMQ(execStaff, execStaffName));
//                //调用 医嘱的消息接口
//                CisOrderStatueEto  eto = new CisOrderStatueEto(apply.getOrderID(),CisStatusEnum.COMPLETED);
//                cisOrderExecPlanService.sendMessaging(eto);
//                MsgProcessUtil.success(channel, message);
//            }
//        } catch (Exception e) {
//                MsgProcessUtil.failReTry(channel, message, MqDomainTypeEnum.IN_HOSPITAL.getCode(), MqBusinessTypeEnum.EXECUTE_DISCHARGE.getCode(), e.getMessage());
//        }
//    }
//
//    @RabbitListener(bindings = @QueueBinding(
//            value = @Queue(value = CisApplyRabbitConfig.CIS_APPLY_PLAN_SYNC_CANCEL_QUEUE,
//                    arguments = {@Argument(name = "x-dead-letter-exchange", value = HipRabbitAutoConfiguration.DEAD_LETTER_EXCHANGE),
//                            @Argument(name = "x-dead-letter-routing-key", value = HipRabbitAutoConfiguration.DEAD_LETTER_ROUTING_KEY),
//                            @Argument(name = "x-single-active-consumer", value = "true", type = "java.lang.Boolean")}),
//            exchange = @Exchange(value = CisApplyRabbitConfig.CIS_APPLY_SYNC_EXCHANGE, type = ExchangeTypes.TOPIC),
//            key = {CisApplyRabbitConfig.CIS_APPLY_PLAN_SYNC_CANCEL_ROUTING_KEY}
//    ), ackMode = "MANUAL")
//    public void receicePlanCancelExec(Channel channel, Message message) throws Exception {
//        try {
//            CisOrderExecPlanReceiveTo orderPlan = MsgProcessUtil.getBody(message, CisOrderExecPlanReceiveTo.class);
//            LoginInfo loginInfo = MsgProcessUtil.getLoginUser(message);
//            if (StringUtils.hasText(orderPlan.getExecPlanId())) {
//                // 直接给ID的单独写
//                cisOrderExecPlanService.cancelCisOrderExecPlan(orderPlan.getExecPlanId());
//            } else {
//                CisBaseApplyQto qto = new CisBaseApplyQto();
//                qto.setStatusCode(CisStatusEnum.COMPLETED);
//                qto.setVisitCode(orderPlan.getVisitCode());
//                qto.setOrderClass(orderPlan.getSystemType().getCode());
//                CisBaseApply apply = CisBaseApply.getCisBaseApplies(qto).stream().findFirst().orElse(null);
//                if (apply == null) {
//                    MsgProcessUtil.success(channel, message);
//                    return;
//                }
//                apply.cancelMQ();
//                CisOrderExecPlan.getByCisBaseApplyId(apply.getId()).stream().forEach(p -> p.cancelMQ());
//                CisOrderStatueEto  eto = new CisOrderStatueEto(apply.getOrderID(),CisStatusEnum.PASS);
//                cisOrderExecPlanService.sendMessaging(eto);
//                MsgProcessUtil.success(channel, message);
//            }
//        } catch (Exception e) {
//            MsgProcessUtil.failReTry(channel, message, MqDomainTypeEnum.IN_HOSPITAL.getCode(), MqBusinessTypeEnum.CANCELDISCHARGE.getCode(), e.getMessage());
//        }
//    }
}