package com.bjgoodwill.hip.ds.cis.apply.apply.repository;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisBaseApplyRepository")
public interface CisBaseApplyRepository extends JpaRepository<CisBaseApply, String>, JpaSpecificationExecutor<CisBaseApply> {

//    List<CisBaseApply> findByMedrecordExamabstractId(String medrecordExamabstractId);
//
//    Page<CisBaseApply> findByMedrecordExamabstractId(String medrecordExamabstractId, Pageable pageable);
//
//    boolean existsByMedrecordExamabstractId(String medrecordExamabstractId);
//
//    void deleteByMedrecordExamabstractId(String medrecordExamabstractId);

    List<CisBaseApply> findByVisitCodeAndStatusCode(String visitCode, CisStatusEnum statusCode);

    List<CisBaseApply> findCisBaseAppliesByDeptNurseCodeAndStatusCode(String deptNurseCode, CisStatusEnum statusCode);

    List<CisBaseApply> findCisBaseAppliesByOrderIDIn(List<String> orderids);

    List<CisBaseApply> findCisBaseAppliesByIdIn(List<String> applyIds);

    List<CisBaseApply> findCisBaseAppliesByVisitCodeAndStatusCode(String visitCode, CisStatusEnum statusCode);

    @Query(value = "SELECT a FROM CisBaseApply a WHERE a.systemType IN ('DGIMG','SPCOBS') and a.createdDate >= ?1 ")
    List<CisBaseApply> findCisBaseApplyByCreatedDateAfter(LocalDateTime dateTime);

    List<CisBaseApply> findCisBaseAppliesByVisitCodeAndStatusCodeIn(String visitCode, List<CisStatusEnum> statusCodes);

    @Query(value = "SELECT a.orderID FROM CisBaseApply a,ApplyDiagnosis d WHERE a.id = d.applyId and a.visitCode = ?1 and d.diagCode = ?2")
    List<String> queryOrderIdsByDiagCode(String visitCode, String diagCode);

    @Query(value = "SELECT a.orderID FROM CisBaseApply a,ApplyDiagnosis d WHERE a.id = d.applyId  and d.diagCode = ?2 and a.treatmentCode = ?1")
    List<String> queryOrderIdsByDiagCodeWithTreatmentCode(String treatmentCode, String diagCode);

    @Query(value = "SELECT a.id,a.orderType FROM CisBaseApply a WHERE a.id in ?1")
    List<Object[]> findCisBaseApplyOrderType(List<String> applyIds);

    List<CisBaseApply> findCisBaseAppliesByPrescriptionIDIn(List<String> prescriptionIds);

}