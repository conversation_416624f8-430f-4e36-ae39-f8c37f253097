package com.bjgoodwill.hip.ds.cis.apply.drug.service.internal;

import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisDrugApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisEDrugApply;
import com.bjgoodwill.hip.ds.cis.apply.drug.service.CisEDrugApplyService;
import com.bjgoodwill.hip.ds.cis.apply.drug.service.internal.assembler.CisDrugApplyDetailAssembler;
import com.bjgoodwill.hip.ds.cis.apply.drug.service.internal.assembler.CisEDrugApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisEDrugApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisEDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisEDrugApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanEDrugQto;
import com.esotericsoftware.minlog.Log;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@RestController("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisEDrugApplyService")
@RequestMapping(value = "/api/apply/apply/cisEDrugApply", produces = "application/json; charset=utf-8")
public class CisEDrugApplyServiceImpl extends CisBaseDrugApplyServiceImpl implements CisEDrugApplyService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisEDrugApplyTo getCisEDrugApplyById(String id) {
        return CisEDrugApplyAssembler.toTo(CisEDrugApply.getCisEDrugApplyById(id).orElse(null), true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisEDrugApplyTo createCisEDrugApply(CisEDrugApplyNto cisEDrugApplyNto) {
        CisEDrugApply cisEDrugApply = new CisEDrugApply();
        cisEDrugApply = cisEDrugApply.create(cisEDrugApplyNto, true);
        return CisEDrugApplyAssembler.toTo(cisEDrugApply);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisEDrugApply(String id, CisEDrugApplyEto cisEDrugApplyEto) {
        Optional<CisEDrugApply> cisEDrugApplyOptional = CisEDrugApply.getCisEDrugApplyById(id);
        cisEDrugApplyOptional.ifPresent(cisEDrugApply -> cisEDrugApply.update(cisEDrugApplyEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisEDrugApply(String id) {
        Optional<CisEDrugApply> cisEDrugApplyOptional = CisEDrugApply.getCisEDrugApplyById(id);
        cisEDrugApplyOptional.ifPresent(cisEDrugApply -> cisEDrugApply.delete());
    }

    /**
     * 根据创建时间获取Cis E药品申请信息。
     *
     * @param date 创建时间，用于查询Cis E药品申请。
     * @return 返回匹配的Cis E药品申请列表，如果不存在匹配项，则返回空列表。
     */
    @Override
    public List<CisEDrugApplyTo> getCisEDrugApplyByCreateDate(LocalDateTime date) {
        // 验证输入参数date是否为空，如果为空则抛出异常
        BusinessAssert.notNull(date, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "时间");

        // 根据创建时间查询Cis E药品申请列表
        List<CisEDrugApply> items = CisEDrugApply.findCisEDrugAppliesByCreatedDateAfter(date);

        // 如果查询结果为空，直接返回空列表
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }

        // 将查询到的Cis E药品申请转换为CisEDrugApplyTo对象列表
        List<CisEDrugApplyTo> tos = CisEDrugApplyAssembler.toTos(items);

        // 查询与Cis E药品申请相关的详细信息
        List<CisDrugApplyDetail> details = CisDrugApplyDetail.findCisDrugApplyDetailByCreatedDateAfter(date);

        // 如果详细信息不为空，进行进一步处理
        if (!CollectionUtils.isEmpty(details)) {
            // 根据CisBaseDrugApplyId对详细信息进行分组
            Map<String, List<CisDrugApplyDetail>> maps = details.stream().collect(Collectors.groupingBy(CisDrugApplyDetail::getApplyId));

            // 遍历转换后的Cis E药品申请列表，为每个项设置相关的详细信息
            tos.forEach(o -> o.setCisDrugApplyDetails(CisDrugApplyDetailAssembler.toTos(maps.get(ObjectUtils.defaultIfNull(o.getId(), "")))));
        }

        // 返回处理后的Cis E药品申请列表
        return tos;
    }

    /**
     * 根据电子药物计划查询药物申请信息
     *
     * @param cisOrderExecPlanDrugQto 电子药物计划查询对象
     * @return 药物申请信息列表
     */
    @Override
    public List<CisEDrugApplyTo> getEDrugSend(CisOrderExecPlanEDrugQto cisOrderExecPlanDrugQto) {
        List<CisOrderExecPlan> cisOrderExecPlans = CisOrderExecPlan.findExecCisOrderEDrugPlans(cisOrderExecPlanDrugQto);
        if (CollectionUtils.isEmpty(cisOrderExecPlans)) {
            return Collections.emptyList();
        }

        // 获取所有订单ID，去除重复
        Set<String> orderIds = cisOrderExecPlans.stream()
                .map(CisOrderExecPlan::getOrderId)
                .collect(Collectors.toSet());

        // 根据订单ID查询药物申请详情，仅包括正常和出院两种给药方式
        List<CisDrugApplyDetail> details = CisDrugApplyDetail.findCisDrugApplyDetailsByApplyIdIn(new ArrayList<>(orderIds))
                .stream()
                .filter(detail -> SbadmWayEnum.NORMAL.equals(detail.getSbadmWay()) || SbadmWayEnum.DISCHARGE.equals(detail.getSbadmWay()))
                .collect(Collectors.toList());

        // 更新订单ID列表为已申请药物的订单ID
        orderIds = details.stream()
                .map(CisDrugApplyDetail::getApplyId)
                .collect(Collectors.toSet());

        // 将药物申请详情按申请ID分组
        Map<String, List<CisDrugApplyDetail>> mapDetail = details.stream()
                .collect(Collectors.groupingBy(CisDrugApplyDetail::getApplyId));

        // 将电子药物计划按订单ID分组
        Map<String, List<CisOrderExecPlan>> mapOrderExecPlan = cisOrderExecPlans.stream()
                .collect(Collectors.groupingBy(CisOrderExecPlan::getOrderId));

        // 转换并组装最终的药物申请信息列表
        List<CisEDrugApply> cisEDrugApplies = CisBaseApply.findCisBaseAppliesByOrderIDIn(new ArrayList<>(orderIds))
                .stream()
                .filter(CisEDrugApply.class::isInstance)
                .map(CisEDrugApply.class::cast)
                .collect(Collectors.toList());

        List<CisEDrugApplyTo> tos = CisEDrugApplyAssembler.toTos(cisEDrugApplies);

        tos.forEach(to -> {
            List<CisDrugApplyDetail> drugDetails = mapDetail.getOrDefault(to.getId(), Collections.emptyList());
            to.setCisDrugApplyDetails(CisDrugApplyDetailAssembler.toTos(drugDetails));
            List<CisOrderExecPlan> execPlans = mapOrderExecPlan.getOrDefault(to.getId(), Collections.emptyList());
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(execPlans));
        });

        return tos;
    }


    /**
     * 根据订单ID列表获取完整的药物申请信息
     *
     * @param orderIds 订单ID列表
     * @return 包含详细信息的药物申请列表
     */
    @Override
    public List<CisEDrugApplyTo> getWholeDrugApplys(List<String> orderIds) {
        // 检查订单ID列表是否为空
        if (CollectionUtils.isEmpty(orderIds)) {
            Log.debug("没有整取医嘱数据！");
            return Collections.emptyList();
        }

        // 根据订单ID列表查询对应的药物申请单
        List<CisBaseApply> items = CisBaseApply.findCisBaseAppliesByOrderIDIn(orderIds).stream()
                .filter(CisEDrugApply.class::isInstance).toList();
        // 提取所有申请单的ID
        List<String> applyIds = items.stream().map(CisBaseApply::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(applyIds)) {
            Log.debug("没有药品申请单数据！");
            return Collections.emptyList();
        }

        // 根据申请单ID列表，查询并聚合所有药物申请详情
//        Map<String, List<CisDrugApplyDetail>> detailsMap = CisDrugApplyDetail.findWholeDrugApplyDetailByApplyIdIn(applyIds)
//                .stream().collect(Collectors.groupingBy(CisDrugApplyDetail::getApplyId));

        List<CisDrugApplyDetail> eDrugApply = CisDrugApplyDetail.findWholeDrugApplyDetailByApplyIdIn(applyIds);
        if (CollectionUtils.isEmpty(eDrugApply)) {
            return Collections.emptyList();
        }
        // 获取整取药
        List<String> drugApplyIds = eDrugApply.stream().map(CisDrugApplyDetail::getApplyId).distinct().collect(Collectors.toList());

        // 将查询到的申请单和详情进行转换和组装，返回最终结果
        return items.stream().filter(a -> drugApplyIds.contains(a.getId())).map(p -> {
            CisEDrugApplyTo to = CisEDrugApplyAssembler.toTo((CisEDrugApply) p);
            return to;
        }).toList();

    }

    /**
     * 分拆整取药物申请
     * 该方法根据提供的id获取基础申请信息，并筛选出其中的整取药物申请详细信息
     * 如果申请是整取类型，将这些详细信息组装成一个新的药物申请对象并返回
     *
     * @param id 申请的唯一标识
     * @return 返回分拆后的整取药物申请对象，如果分拆失败或无符合条件的申请则返回null
     */
    @Override
    public CisEDrugApplyTo splitWholeTakeDrugApply(String id) {
        // 根据id获取基础申请信息
        CisBaseApply cisBaseApply = CisBaseApply.getCisBaseApplyById(id).orElse(null);

        // 断言申请信息不为空，否则抛出异常
        BusinessAssert.notNull(cisBaseApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0006, "id");

        // 检查申请是否为CisEDrugApply类型
        if (cisBaseApply instanceof CisEDrugApply cisEDrugApply) {
            // 筛选出整取方式的详细信息
            List<CisDrugApplyDetail> details = cisEDrugApply.getDetailList().stream()
                    .filter(p -> SbadmWayEnum.WHOLETAKE.equals(p.getSbadmWay())).toList();
            // 断言筛选后的详细信息不为空，否则抛出异常
            BusinessAssert.notEmpty(details, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "整取");

            // 将药物申请转换为非持久化对象，并创建新的药物申请对象
            CisEDrugApplyNto nto = CisEDrugApplyAssembler.toNto(cisEDrugApply, false);
            CisEDrugApply cisEDrugApplyNew = new CisEDrugApply().create(nto, true);
            // 将新的药物申请对象转换为目标传输对象
            CisEDrugApplyTo to = CisEDrugApplyAssembler.toTo(cisEDrugApplyNew, false);
            // 将筛选后的详细信息转换为传输对象，并设置到目标传输对象中
            to.setCisDrugApplyDetails(CisDrugApplyDetailAssembler.toTos(details));
            // 返回分拆后的整取药物申请对象
            return to;
        }
        // 如果申请不是CisEDrugApply类型，返回null
        return null;
    }


    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }


}