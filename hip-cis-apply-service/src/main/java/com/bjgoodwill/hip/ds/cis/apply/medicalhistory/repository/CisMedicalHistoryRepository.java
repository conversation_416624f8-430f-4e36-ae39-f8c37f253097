package com.bjgoodwill.hip.ds.cis.apply.medicalhistory.repository;

import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.entity.CisMedicalHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.cpoe.medicalhistory.repository.CisMedicalHistoryRepository")
public interface CisMedicalHistoryRepository extends JpaRepository<CisMedicalHistory, String>, JpaSpecificationExecutor<CisMedicalHistory> {

    CisMedicalHistory findCisMedicalHistoryByVisitCode(String visitCode);
}