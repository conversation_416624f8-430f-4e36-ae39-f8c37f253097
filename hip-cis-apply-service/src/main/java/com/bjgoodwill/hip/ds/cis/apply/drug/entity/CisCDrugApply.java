package com.bjgoodwill.hip.ds.cis.apply.drug.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.repository.CisCDrugApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "中草药申请单")
@DiscriminatorValue("03")
public class CisCDrugApply extends CisBaseDrugApply {

    // 付数
    private String doseNum;
    // 草药煎法;0自煎,1代煎
    private Integer decoction;
    // 草药一付包数
    private Integer cdrugPackNum;
    // 草药每包毫升数
    private Integer cdrugPackMl;
    // 协定处方
    private Boolean prescriptionFlag;
    //草药属性（DrugHerbsProEnum
    private String herbsProCode;

    public static Optional<CisCDrugApply> getCisCDrugApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisCDrugApply> getCisCDrugApplies(CisCDrugApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisCDrugApply> getCisCDrugApplyPage(CisCDrugApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisCDrugApply> getSpecification(CisCDrugApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getDoseNum())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("doseNum"), qto.getDoseNum()));
            }
//            if (qto.getCreatedDate() != null) {
//                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createdDate"), qto.getCreatedDate()));
//            }
            return predicate;
        };
    }

    private static CisCDrugApplyRepository dao() {
        return SpringUtil.getBean(CisCDrugApplyRepository.class);
    }

    @Comment("付数")
    @Column(name = "dose_num", nullable = true)
    public String getDoseNum() {
        return doseNum;
    }

    protected void setDoseNum(String doseNum) {
        this.doseNum = doseNum;
    }

    @Comment("草药煎法;0自煎,1代煎")
    @Column(name = "decoction", nullable = true)
    public Integer getDecoction() {
        return decoction;
    }

    protected void setDecoction(Integer decoction) {
        this.decoction = decoction;
    }

    @Comment("草药一付包数")
    @Column(name = "cdrug_pack_num", nullable = true)
    public Integer getCdrugPackNum() {
        return cdrugPackNum;
    }

    protected void setCdrugPackNum(Integer cdrugPackNum) {
        this.cdrugPackNum = cdrugPackNum;
    }

    @Comment("草药每包毫升数")
    @Column(name = "cdrug_pack_ml", nullable = true)
    public Integer getCdrugPackMl() {
        return cdrugPackMl;
    }

    protected void setCdrugPackMl(Integer cdrugPackMl) {
        this.cdrugPackMl = cdrugPackMl;
    }

    @Comment("协定处方")
    @Column(name = "prescription_flag", nullable = true)
    public Boolean getPrescriptionFlag() {
        return prescriptionFlag;
    }

    public void setPrescriptionFlag(Boolean prescriptionFlag) {
        this.prescriptionFlag = prescriptionFlag;
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.CDRUG;
    }

    @Comment("草药属性（DrugHerbsProEnum）")
    @Column(name = "herbs_pro_code", nullable = true)
    public String getHerbsProCode() {
        return herbsProCode;
    }

    public void setHerbsProCode(String herbsProCode) {
        this.herbsProCode = herbsProCode;
    }

    @Override
    public CisBaseDrugApply create(CisBaseDrugApplyNto cisBaseDrugApplyNto, Boolean save) {
        return create((CisCDrugApplyNto) cisBaseDrugApplyNto, save);
    }

    @Override
    public void update(CisBaseDrugApplyEto cisBaseDrugApplyEto) {
        update((CisCDrugApplyEto) cisBaseDrugApplyEto);
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisCDrugApplyNto) cisBaseApplyNto, save);
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisCDrugApplyEto) cisBaseApplyEto);
    }

    public CisCDrugApply create(CisCDrugApplyNto cisCDrugApplyNto, Boolean save) {
        BusinessAssert.notNull(cisCDrugApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisCDrugApplyNto不能为空！");
        super.create(cisCDrugApplyNto, save);

        setDoseNum(cisCDrugApplyNto.getDoseNum());
        setDecoction(cisCDrugApplyNto.getDecoction());
        setCdrugPackNum(cisCDrugApplyNto.getCdrugPackNum());
        setCdrugPackMl(cisCDrugApplyNto.getCdrugPackMl());
        setPrescriptionFlag(cisCDrugApplyNto.getPrescriptionFlag());
        setHerbsProCode(cisCDrugApplyNto.getHerbsProCode());
        if (save) {
            dao().save(this);
        }

        return this;
    }

    public void update(CisCDrugApplyEto cisCDrugApplyEto) {
        super.update(cisCDrugApplyEto);
        setDoseNum(cisCDrugApplyEto.getDoseNum());
        setDecoction(cisCDrugApplyEto.getDecoction());
        setCdrugPackNum(cisCDrugApplyEto.getCdrugPackNum());
        setCdrugPackMl(cisCDrugApplyEto.getCdrugPackMl());
        setPrescriptionFlag(cisCDrugApplyEto.getPrescriptionFlag());
        setHerbsProCode(cisCDrugApplyEto.getHerbsProCode());
        setUpdatedDate(LocalDateTime.now());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
