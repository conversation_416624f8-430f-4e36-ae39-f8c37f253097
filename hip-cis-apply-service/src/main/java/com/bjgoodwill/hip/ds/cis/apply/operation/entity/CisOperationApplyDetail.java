package com.bjgoodwill.hip.ds.cis.apply.operation.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.BaseDetail;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.DetailNto;
import com.bjgoodwill.hip.ds.cis.apply.operation.repository.CisOperationApplyDetailRepository;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.CisOperationApplyDetailEto;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.CisOperationApplyDetailNto;
import com.google.common.collect.Lists;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-09-29 15:31
 * @className: CisOperationApplyDetail
 * @description:
 **/
@Entity
@Comment(value = "手术术式")
@Table(name = "cis_operation_apply_detail", indexes = {@Index(columnList = "apply_id", name = "cis_operation_apply_detai_id_idx")}, uniqueConstraints = {})
public class CisOperationApplyDetail extends BaseDetail {

    //region 属性
    //ICD9-CM3编码
    private String operationCode;
    //ICD9-CM3名称
    private String operationName;
    //项目编码
    private String serviceItemCode;
    //项目名称
    private String serviceItemName;
    // 手术级别
    private String operationLevel;
    // 部位
    private String humanOrgans;
    // 部位名称
    private String humanOrgansName;
    // 体位
    private String decubitus;
    // 体位名称
    private String decubitusName;
    //逻辑删除
    private boolean deleted;
    // 创建的人员
    private String createdStaff;
    // 创建的人员
    private String createdStaffName;
    // 最后修改的人员
    private String updatedStaff;
    // 最后修改的人员
    private String updatedStaffName;

    public static Optional<CisOperationApplyDetail> getCisOperationApplyDetailById(String id) {
        return dao().findById(id);
    }

    private static CisOperationApplyDetailRepository dao() {
        return SpringUtil.getBean(CisOperationApplyDetailRepository.class);
    }

    public static List<CisOperationApplyDetail> findByApplyId(String applyId) {
        return dao().findCisOperationApplyDetailsByApplyIdAndDeletedFalse(applyId);
    }

    public static List<CisOperationApplyDetail> getByCisOperationApplyId(String applyId) {
        return dao().findByApplyId(applyId);
    }

    public static List<CisOperationApplyDetail> getByCisOperationApplyIds(List<String> applyIds) {
        return Lists.partition(applyIds, 100).stream().map(list -> dao().findByApplyIdIn(list))
                .flatMap(List::stream).toList();
    }

    @Column(name = "operation_code", nullable = false)
    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    @Column(name = "operation_name", nullable = true)
    public String getOperationName() {
        return operationName;
    }
    //endregion

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Comment("项目编码")
    @Column(name = "service_item_code", nullable = true)
    @Override
    public String getServiceItemCode() {
        return serviceItemCode;
    }

    @Override
    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    @Comment("项目名称")
    @Column(name = "service_item_name", nullable = true)
    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    @Comment("创建人编码")
    @Column(name = "created_staff", nullable = true)
    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建人名称")
    @Column(name = "created_staff_name", nullable = true)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("修改人编码")
    @Column(name = "updated_staff", nullable = true)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Comment("修改人名称")
    @Column(name = "updated_staff_name", nullable = true)
    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Comment("手术级别")
    @Column(name = "operation_level", nullable = true)
    public String getOperationLevel() {
        return operationLevel;
    }

    protected void setOperationLevel(String operationLevel) {
        this.operationLevel = operationLevel;
    }

    @Comment("部位")
    @Column(name = "human_organs", nullable = true)
    public String getHumanOrgans() {
        return humanOrgans;
    }

    protected void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    @Comment("部位名称")
    @Column(name = "human_organs_name", nullable = true)
    public String getHumanOrgansName() {
        return humanOrgansName;
    }

    public void setHumanOrgansName(String humanOrgansName) {
        this.humanOrgansName = humanOrgansName;
    }

    @Comment("体位")
    @Column(name = "decubitus", nullable = true)
    public String getDecubitus() {
        return decubitus;
    }

    protected void setDecubitus(String decubitus) {
        this.decubitus = decubitus;
    }

    @Comment("体位名称")
    @Column(name = "decubitus_name", nullable = true)
    public String getDecubitusName() {
        return decubitusName;
    }

    public void setDecubitusName(String decubitusName) {
        this.decubitusName = decubitusName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOperationApplyDetail other = (CisOperationApplyDetail) obj;
        return Objects.equals(id, other.id);
    }

    @Override
    public CisOperationApplyDetail create(String applyId, DetailNto baseDetailNto, CisStatusEnum statusEnum, Boolean save) {
        CisOperationApplyDetailNto cisOperationApplyDetailNto = (CisOperationApplyDetailNto) baseDetailNto;
        return this.create(applyId, cisOperationApplyDetailNto, statusEnum, save);
    }

    @Override
    public List<BaseDetail> queryDetailsByCreateDate(LocalDateTime dateTime) {
        return dao().queryDetailsByCreateDate(dateTime);
    }

    public CisOperationApplyDetail create(String applyId, CisOperationApplyDetailNto cisOperationApplyDetailNto, CisStatusEnum statusEnum, Boolean save) {
        BusinessAssert.notNull(cisOperationApplyDetailNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisOperationApplyDetailNto不能为空！");
        super.create(applyId, cisOperationApplyDetailNto, statusEnum, save);

        setOperationCode(cisOperationApplyDetailNto.getOperationCode());
        setOperationName(cisOperationApplyDetailNto.getOperationName());
        setServiceItemCode(cisOperationApplyDetailNto.getServiceItemCode());
        setServiceItemName(cisOperationApplyDetailNto.getServiceItemName());
        setOperationLevel(cisOperationApplyDetailNto.getOperationLevel());
        setHumanOrgans(cisOperationApplyDetailNto.getHumanOrgans());
        setHumanOrgansName(cisOperationApplyDetailNto.getHumanOrgansName());
        setDecubitus(cisOperationApplyDetailNto.getDecubitus());
        setDecubitusName(cisOperationApplyDetailNto.getDecubitusName());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        if (save) {
            dao().save(this);
        }

        return this;
    }

    public void update(CisOperationApplyDetailEto cisOperationApplyDetailEto) {
        setSortNo(cisOperationApplyDetailEto.getSortNo());
        setOperationCode(cisOperationApplyDetailEto.getOperationCode());
        setOperationName(cisOperationApplyDetailEto.getOperationName());
        setHumanOrgans(cisOperationApplyDetailEto.getHumanOrgans());
        setHumanOrgansName(cisOperationApplyDetailEto.getHumanOrgansName());
        setDecubitus(cisOperationApplyDetailEto.getDecubitus());
        setDecubitusName(cisOperationApplyDetailEto.getDecubitusName());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateTime.now());
    }

    public void delete() {
        setDeleted(true);
    }


}