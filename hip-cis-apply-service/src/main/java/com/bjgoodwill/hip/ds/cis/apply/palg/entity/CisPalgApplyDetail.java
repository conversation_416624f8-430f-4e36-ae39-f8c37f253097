package com.bjgoodwill.hip.ds.cis.apply.palg.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.BaseDetail;
import com.bjgoodwill.hip.ds.cis.apply.palg.repository.CisPalgApplyDetailRepository;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyDetailEto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyDetailQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import com.google.common.collect.Lists;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "病理明细")
@Table(name = "cis_palg_apply_detail", indexes = {@Index(name = "cis_palg_apply_detail_visit_code", columnList = "visit_code"),
        @Index(name = "cis_palg_apply_detail_apply_id", columnList = "apply_id")}, uniqueConstraints = {})
public class CisPalgApplyDetail extends BaseDetail {

    //    // 标识
//    private String id;
//    // 病理申请单标识
//    private String cisPalgApplyId;
    // 序号
    private Double sortNo;
    // 部位
    private String humanOrgans;
    // 标本
    private String speciman;
    // 数量
    private Integer num;
    // 离体时间
    private LocalDateTime outVivoDate;
    // 逻辑删除标记
    private boolean deleted;
    // 创建的人员
    private String createdStaff;
    //    // 创建的时间
//    private LocalDateTime createdDate;
    // 最后修改的人员
    private String updatedStaff;
    //    // 最后修改的时间
//    private LocalDateTime updatedDate;
    private String serviceItemCode;

    public static Optional<CisPalgApplyDetail> getCisPalgApplyDetailById(String id) {
        return dao().findById(id);
    }

    public static List<CisPalgApplyDetail> getCisPalgApplyDetails(CisPalgApplyDetailQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisPalgApplyDetail> getCisPalgApplyDetailPage(CisPalgApplyDetailQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisPalgApplyDetail> getByCisPalgApplyId(String cisPalgApplyId) {
        return dao().findByApplyId(cisPalgApplyId);
    }

    public static List<CisPalgApplyDetail> getByCisPalgApplyIds(List<String> applyIds) {
        return Lists.partition(applyIds, 100).stream().map(list -> dao().findByApplyIdIn(list))
                .flatMap(List::stream).toList();
    }

    public static void deleteByCisPalgApplyId(String cisPalgApplyId) {
        dao().deleteByApplyId(cisPalgApplyId);
    }

    /**
     * @generated
     */
    private static Specification<CisPalgApplyDetail> getSpecification(CisPalgApplyDetailQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getCisPalgApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisPalgApplyId"), qto.getCisPalgApplyId()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            return predicate;
        };
    }

    private static CisPalgApplyDetailRepository dao() {
        return SpringUtil.getBean(CisPalgApplyDetailRepository.class);
    }
//
//    @Comment("最后修改的时间")
//    @Column(name = "updated_date", nullable = true)
//    public LocalDateTime getUpdatedDate() {
//    	return updatedDate;
//    }
//
//    protected void setUpdatedDate(LocalDateTime updatedDate) {
//    	this.updatedDate = updatedDate;
//    }

    // 状态
//    private CisStatusEnum statusCode;
//
//    @Enumerated(EnumType.STRING)
//    @Comment("状态")
//    @Column(name = "status_code", nullable = false)
//    public CisStatusEnum getStatusCode() {
//        return statusCode;
//    }
//
//    public void setStatusCode(CisStatusEnum statusCode) {
//        this.statusCode = statusCode;
//    }

    //    @Id
//    @Comment("标识")
//    @Column(name = "id", nullable = false, length = 50)
//    @GeneratedValue(generator = "snowflake_generator")
//    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
//    public String getId() {
//    	return id;
//    }
//
//    protected void setId(String id) {
//    	this.id = id;
//    }
//
//    @Comment("病理申请单标识")
//    @Column(name = "cis_palg_apply_id", nullable = true, length = 50)
//    public String getCisPalgApplyId() {
//    	return cisPalgApplyId;
//    }
//
//    protected void setCisPalgApplyId(String cisPalgApplyId) {
//    	this.cisPalgApplyId = cisPalgApplyId;
//    }
//
    @Comment("序号")
    @Column(name = "sort_no", nullable = true)
    public Double getSortNo() {
        return sortNo;
    }

    protected void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    @Override
    public List<BaseDetail> queryDetailsByCreateDate(LocalDateTime dateTime) {
        return dao().queryDetailsByCreateDate(dateTime);
    }

    @Comment("部位")
    @Column(name = "human_organs", nullable = false)
    public String getHumanOrgans() {
        return humanOrgans;
    }

    protected void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    @Comment("标本")
    @Column(name = "speciman", nullable = false)
    public String getSpeciman() {
        return speciman;
    }

    protected void setSpeciman(String speciman) {
        this.speciman = speciman;
    }

    @Comment("数量")
    @Column(name = "num", nullable = false)
    public Integer getNum() {
        return num;
    }

    protected void setNum(Integer num) {
        this.num = num;
    }

    @Comment("离体时间")
    @Column(name = "out_vivo_date", nullable = true)
    public LocalDateTime getOutVivoDate() {
        return outVivoDate;
    }

    public void setOutVivoDate(LocalDateTime outVivoDate) {
        this.outVivoDate = outVivoDate;
    }

    @Override
    @Column(name = "service_code", nullable = false)
    public String getServiceItemCode() {
        return serviceItemCode;
    }

    @Override
    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = false)
    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    //    @Comment("创建的时间")
//    @Column(name = "created_date", nullable = false)
//    public LocalDateTime getCreatedDate() {
//    	return createdDate;
//    }
//
//    protected void setCreatedDate(LocalDateTime createdDate) {
//    	this.createdDate = createdDate;
//    }
//
    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisPalgApplyDetail other = (CisPalgApplyDetail) obj;
        return Objects.equals(id, other.id);
    }

    public CisPalgApplyDetail create(String cisPalgApplyId, CisPalgApplyDetailNto cisPalgApplyDetailNto, CisStatusEnum cisStatusEnum, Boolean save) {
        BusinessAssert.notNull(cisPalgApplyDetailNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisPalgApplyDetailNto不能为空！");
        super.create(cisPalgApplyId, cisPalgApplyDetailNto, cisStatusEnum, save);
        setId(cisPalgApplyDetailNto.getId());
//        setApplyId(cisPalgApplyId);
        setHumanOrgans(cisPalgApplyDetailNto.getHumanOrgans());
        setSpeciman(cisPalgApplyDetailNto.getSpeciman());
        setNum(cisPalgApplyDetailNto.getNum());
        setSortNo(cisPalgApplyDetailNto.getSortNo());
        setOutVivoDate(cisPalgApplyDetailNto.getOutVivoDate());
        setServiceItemCode(cisPalgApplyDetailNto.getServiceItemCode());
        setDeleted(false);
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        if (save) {
            dao().save(this);
        }
        return this;
    }

    public void update(CisPalgApplyDetailEto cisPalgApplyDetailEto) {
        setSortNo(cisPalgApplyDetailEto.getSortNo());
        setHumanOrgans(cisPalgApplyDetailEto.getHumanOrgans());
        setSpeciman(cisPalgApplyDetailEto.getSpeciman());
        setNum(cisPalgApplyDetailEto.getNum());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedDate(LocalDateUtil.now());
        setServiceItemCode(cisPalgApplyDetailEto.getServiceItemCode());
    }

    public void delete() {
        dao().delete(this);
    }
}
