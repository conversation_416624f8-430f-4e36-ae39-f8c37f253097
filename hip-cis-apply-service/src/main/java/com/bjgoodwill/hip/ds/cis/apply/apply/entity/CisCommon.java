package com.bjgoodwill.hip.ds.cis.apply.apply.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisCommonRepository;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "通用申请单")
@DiscriminatorValue("00")
public class CisCommon extends CisBaseApply {

    // 频次
    private String frequency;
    // 每次剂量
    private Double dosage;
    // 剂量单位
    private String dosageUnit;
    // 剂量单位名称
    private String dosageUnitName;
    // 包装总量
    private Double packageNum;
    // 包装单位 MinUnit/PackageUnit
    private String packageUnit;
    // 包装单位名称 MinUnit/PackageUnit
    private String packageUnitName;
    // 每次持续时间
    private Double continueTime;
    // 每次持续单位 字典TimeUnit
    private String continueUnit;
    // 每次持续单位名称
    private String continueUnitName;


    public static Optional<CisCommon> getCisCommonById(String id) {
        return dao().findById(id);
    }

    public static List<CisCommon> getCisCommons(CisCommonQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisCommon> getCisCommonPage(CisCommonQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisCommon> getSpecification(CisCommonQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            return predicate;
        };
    }

    private static CisCommonRepository dao() {
        return SpringUtil.getBean(CisCommonRepository.class);
    }

    @Comment("频次")
    @Column(name = "frequency", nullable = true)
    public String getFrequency() {
        return frequency;
    }

    protected void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    @Comment("每次剂量")
    @Column(name = "dosage", nullable = true)
    public Double getDosage() {
        return dosage;
    }

    protected void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    @Comment("剂量单位")
    @Column(name = "dosage_unit", nullable = true)
    public String getDosageUnit() {
        return dosageUnit;
    }

    protected void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    @Comment("剂量单位名称")
    @Column(name = "dosage_unit_name", nullable = true)
    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    @Comment("包装总量")
    @Column(name = "package_num", nullable = true)
    public Double getPackageNum() {
        return packageNum;
    }

    protected void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    @Comment("包装单位 MinUnit/PackageUnit")
    @Column(name = "package_unit", nullable = true)
    public String getPackageUnit() {
        return packageUnit;
    }

    protected void setPackageUnit(String packageUnit) {
        this.packageUnit = packageUnit;
    }

    @Comment("包装单位名称 MinUnit/PackageUnit")
    @Column(name = "package_unit_name", nullable = true)
    public String getPackageUnitName() {
        return packageUnitName;
    }

    public void setPackageUnitName(String packageUnitName) {
        this.packageUnitName = packageUnitName;
    }

    @Comment("每次持续时间")
    @Column(name = "continue_time", nullable = true)
    public Double getContinueTime() {
        return continueTime;
    }

    protected void setContinueTime(Double continueTime) {
        this.continueTime = continueTime;
    }

    @Comment("每次持续单位 字典TimeUnit")
    @Column(name = "continue_unit", nullable = true)
    public String getContinueUnit() {
        return continueUnit;
    }

    protected void setContinueUnit(String continueUnit) {
        this.continueUnit = continueUnit;
    }

    @Comment("每次持续单位名称 字典TimeUnit")
    @Column(name = "continue_unit_name", nullable = true)
    public String getContinueUnitName() {
        return continueUnitName;
    }

    public void setContinueUnitName(String continueUnitName) {
        this.continueUnitName = continueUnitName;
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisCommonNto) cisBaseApplyNto, save);
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisCommonEto) cisBaseApplyEto);
    }

    public CisCommon create(CisCommonNto cisCommonNto, Boolean save) {
        BusinessAssert.notNull(cisCommonNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisCommonNto不能为空！");
        super.create(cisCommonNto, save);

        BusinessAssert.hasText(cisCommonNto.getServiceItemCode(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "serviceItemCode");
        setServiceItemCode(cisCommonNto.getServiceItemCode());

        setFrequency(cisCommonNto.getFrequency());
        setDosage(cisCommonNto.getDosage());
        setDosageUnit(cisCommonNto.getDosageUnit());
        setDosageUnitName(cisCommonNto.getDosageUnitName());
        setPackageNum(cisCommonNto.getPackageNum());
        setPackageUnit(cisCommonNto.getPackageUnit());
        setPackageUnitName(cisCommonNto.getPackageUnitName());
        setContinueTime(cisCommonNto.getContinueTime());
        setContinueUnit(cisCommonNto.getContinueUnit());
        setContinueUnitName(cisCommonNto.getContinueUnitName());
        setSystemType(cisCommonNto.getSystemType());
        if (save) {
            dao().save(this);
        }
        return this;
    }

    public void update(CisCommonEto cisCommonEto) {
        super.update(cisCommonEto);
        setDosage(cisCommonEto.getDosage());
        setContinueTime(cisCommonEto.getContinueTime());
        setContinueUnit(cisCommonEto.getContinueUnit());
        setContinueUnitName(cisCommonEto.getContinueUnitName());
        setSystemType(cisCommonEto.getSystemType());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
