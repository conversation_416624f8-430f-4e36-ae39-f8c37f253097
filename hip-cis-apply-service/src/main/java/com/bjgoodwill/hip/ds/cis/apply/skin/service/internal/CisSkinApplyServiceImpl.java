package com.bjgoodwill.hip.ds.cis.apply.skin.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.CisBaseApplyServiceImpl;
import com.bjgoodwill.hip.ds.cis.apply.skin.entity.CisSkinApply;
import com.bjgoodwill.hip.ds.cis.apply.skin.service.CisSkinApplyService;
import com.bjgoodwill.hip.ds.cis.apply.skin.service.internal.assembler.CisSkinApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.skin.to.CisSkinApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.skin.to.CisSkinApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.skin.to.CisSkinApplyQto;
import com.bjgoodwill.hip.ds.cis.apply.skin.to.CisSkinApplyTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisSkinApplyService")
@RequestMapping(value = "/api/apply/apply/cisSkinApply", produces = "application/json; charset=utf-8")
public class CisSkinApplyServiceImpl extends CisBaseApplyServiceImpl implements CisSkinApplyService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisSkinApplyTo> getCisSkinApplies(CisSkinApplyQto cisSkinApplyQto) {
        return CisSkinApplyAssembler.toTos(CisSkinApply.getCisSkinApplies(cisSkinApplyQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisSkinApplyTo> getCisSkinApplyPage(CisSkinApplyQto cisSkinApplyQto) {
        Page<CisSkinApply> page = CisSkinApply.getCisSkinApplyPage(cisSkinApplyQto);
        Page<CisSkinApplyTo> result = page.map(CisSkinApplyAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisSkinApplyTo getCisSkinApplyById(String id) {
        return CisSkinApplyAssembler.toTo(CisSkinApply.getCisSkinApplyById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisSkinApplyTo createCisSkinApply(CisSkinApplyNto cisSkinApplyNto) {
        CisSkinApply cisSkinApply = new CisSkinApply();
        cisSkinApply = cisSkinApply.create(cisSkinApplyNto, true);
        return CisSkinApplyAssembler.toTo(cisSkinApply);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisSkinApply(String id, CisSkinApplyEto cisSkinApplyEto) {
        Optional<CisSkinApply> cisSkinApplyOptional = CisSkinApply.getCisSkinApplyById(id);
        cisSkinApplyOptional.ifPresent(cisSkinApply -> cisSkinApply.update(cisSkinApplyEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisSkinApply(String id) {
        Optional<CisSkinApply> cisSkinApplyOptional = CisSkinApply.getCisSkinApplyById(id);
        cisSkinApplyOptional.ifPresent(cisSkinApply -> cisSkinApply.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}