package com.bjgoodwill.hip.ds.cis.apply.medicalhistory.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.repository.CisMedicalHistoryRepository;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryEto;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryNto;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "患者病史")
@Table(name = "cis_medical_history", indexes = {@Index(name = "visit_code_index", columnList = "visit_code")}, uniqueConstraints = {})
public class CisMedicalHistory {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("患者流水号")
    @Column(name = "visit_code", nullable = false)
    private String visitCode;


    @Comment("主诉")
    @Column(name = "chief_complaint", nullable = true)
    private String chiefComplaint;


    @Comment("现病史")
    @Column(name = "present_illness_history", nullable = true)
    private String presentIllnessHistory;


    @Comment("既往史")
    @Column(name = "past_medical_history", nullable = true)
    private String pastMedicalHistory;


    @Comment("过敏史")
    @Column(name = "allergic_history", nullable = true)
    private String allergicHistory;


    @Comment("个人史")
    @Column(name = "social_history", nullable = true)
    private String socialHistory;


    @Comment("家族史")
    @Column(name = "family_history", nullable = true)
    private String familyHistory;


    @Comment("治疗方案")
    @Column(name = "treatment_plan", nullable = true)
    private String treatmentPlan;


    @Comment("创建时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建人员编码")
    @Column(name = "created_staff", nullable = false)
    private String createdStaff;


    @Comment("创建人员名称")
    @Column(name = "created_staff_name", nullable = false)
    private String createdStaffName;


    @Comment("修改时间")
    @Column(name = "updated_date", nullable = false)
    private LocalDateTime updatedDate;


    @Comment("修改人员编码")
    @Column(name = "updated_staff", nullable = false)
    private String updatedStaff;


    @Comment("修改人员名称")
    @Column(name = "updated_staff_name", nullable = false)
    private String updatedStaffName;

    public static Optional<CisMedicalHistory> getCisMedicalHistoryById(String id) {
        return dao().findById(id);
    }

    public static List<CisMedicalHistory> getCisMedicalHistories(CisMedicalHistoryQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisMedicalHistory> getCisMedicalHistoryPage(CisMedicalHistoryQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static CisMedicalHistory getByVisitCode(String visitCode) {
        return dao().findCisMedicalHistoryByVisitCode(visitCode);
    }

    /**
     * @generated
     */
    private static Specification<CisMedicalHistory> getSpecification(CisMedicalHistoryQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("id"), qto.getId()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getChiefComplaint())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("chiefComplaint"), qto.getChiefComplaint()));
            }
            if (StringUtils.isNotBlank(qto.getPresentIllnessHistory())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("presentIllnessHistory"), qto.getPresentIllnessHistory()));
            }
            if (StringUtils.isNotBlank(qto.getPastMedicalHistory())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("pastMedicalHistory"), qto.getPastMedicalHistory()));
            }
            if (StringUtils.isNotBlank(qto.getAllergicHistory())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("allergicHistory"), qto.getAllergicHistory()));
            }
            if (StringUtils.isNotBlank(qto.getSocialHistory())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("socialHistory"), qto.getSocialHistory()));
            }
            if (StringUtils.isNotBlank(qto.getFamilyHistory())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("familyHistory"), qto.getFamilyHistory()));
            }
            if (StringUtils.isNotBlank(qto.getTreatmentPlan())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("treatmentPlan"), qto.getTreatmentPlan()));
            }
            return predicate;
        };
    }

    private static CisMedicalHistoryRepository dao() {
        return SpringUtil.getBean(CisMedicalHistoryRepository.class);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getChiefComplaint() {
        return chiefComplaint;
    }

    protected void setChiefComplaint(String chiefComplaint) {
        this.chiefComplaint = chiefComplaint;
    }

    public String getPresentIllnessHistory() {
        return presentIllnessHistory;
    }

    protected void setPresentIllnessHistory(String presentIllnessHistory) {
        this.presentIllnessHistory = presentIllnessHistory;
    }

    public String getPastMedicalHistory() {
        return pastMedicalHistory;
    }

    protected void setPastMedicalHistory(String pastMedicalHistory) {
        this.pastMedicalHistory = pastMedicalHistory;
    }

    public String getAllergicHistory() {
        return allergicHistory;
    }

    protected void setAllergicHistory(String allergicHistory) {
        this.allergicHistory = allergicHistory;
    }

    public String getSocialHistory() {
        return socialHistory;
    }

    protected void setSocialHistory(String socialHistory) {
        this.socialHistory = socialHistory;
    }

    public String getFamilyHistory() {
        return familyHistory;
    }

    protected void setFamilyHistory(String familyHistory) {
        this.familyHistory = familyHistory;
    }

    public String getTreatmentPlan() {
        return treatmentPlan;
    }

    protected void setTreatmentPlan(String treatmentPlan) {
        this.treatmentPlan = treatmentPlan;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisMedicalHistory other = (CisMedicalHistory) obj;
        return Objects.equals(id, other.id);
    }

    public CisMedicalHistory create(CisMedicalHistoryNto cisMedicalHistoryNto) {
        Assert.notNull(cisMedicalHistoryNto, "参数cisMedicalHistoryNto不能为空！");
        setId(cisMedicalHistoryNto.getId());
        setVisitCode(cisMedicalHistoryNto.getVisitCode());
        setChiefComplaint(cisMedicalHistoryNto.getChiefComplaint());
        setPresentIllnessHistory(cisMedicalHistoryNto.getPresentIllnessHistory());
        setPastMedicalHistory(cisMedicalHistoryNto.getPastMedicalHistory());
        setAllergicHistory(cisMedicalHistoryNto.getAllergicHistory());
        setSocialHistory(cisMedicalHistoryNto.getSocialHistory());
        setFamilyHistory(cisMedicalHistoryNto.getFamilyHistory());
        setTreatmentPlan(cisMedicalHistoryNto.getTreatmentPlan());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        dao().save(this);
        return this;
    }

    public void update(CisMedicalHistoryEto cisMedicalHistoryEto) {
        setChiefComplaint(cisMedicalHistoryEto.getChiefComplaint());
        setPresentIllnessHistory(cisMedicalHistoryEto.getPresentIllnessHistory());
        setPastMedicalHistory(cisMedicalHistoryEto.getPastMedicalHistory());
        setAllergicHistory(cisMedicalHistoryEto.getAllergicHistory());
        setSocialHistory(cisMedicalHistoryEto.getSocialHistory());
        setFamilyHistory(cisMedicalHistoryEto.getFamilyHistory());
        setTreatmentPlan(cisMedicalHistoryEto.getTreatmentPlan());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
    }

    public void delete() {
        dao().delete(this);
    }

}
