package com.bjgoodwill.hip.ds.cis.apply.blood.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.blood.entity.CisInputBloodApply;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisInputBloodApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisInputBloodApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;

import java.util.ArrayList;
import java.util.List;

public abstract class CisInputBloodApplyAssembler {

    public static List<CisInputBloodApplyTo> toTos(List<CisInputBloodApply> cisInputBloodApplies) {
        return toTos(cisInputBloodApplies, false);
    }

    public static List<CisInputBloodApplyTo> toTos(List<CisInputBloodApply> cisInputBloodApplies, boolean withAllParts) {
        BusinessAssert.notNull(cisInputBloodApplies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisBloodApplys不能为空！");

        List<CisInputBloodApplyTo> tos = new ArrayList<>();
        for (CisInputBloodApply cisInputBloodApply : cisInputBloodApplies)
            tos.add(toTo(cisInputBloodApply, withAllParts));
        return tos;
    }

    public static CisInputBloodApplyTo toTo(CisInputBloodApply cisInputBloodApply) {
        return toTo(cisInputBloodApply, false);
    }

    /**
     * @generated
     */
    public static CisInputBloodApplyTo toTo(CisInputBloodApply cisInputBloodApply, boolean withAllParts) {
        if (cisInputBloodApply == null)
            return null;
        CisInputBloodApplyTo to = new CisInputBloodApplyTo();
        to.setId(cisInputBloodApply.getId());
        to.setPatMiCode(cisInputBloodApply.getPatMiCode());
        to.setVisitCode(cisInputBloodApply.getVisitCode());
        to.setServiceItemCode(cisInputBloodApply.getServiceItemCode());
        to.setServiceItemName(cisInputBloodApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisInputBloodApply.getIsCanPriorityFlag());
        to.setStatusCode(cisInputBloodApply.getStatusCode());
        to.setCreatedStaff(cisInputBloodApply.getCreatedStaff());
        to.setCreatedDate(cisInputBloodApply.getCreatedDate());
        to.setUpdatedStaff(cisInputBloodApply.getUpdatedStaff());
        to.setUpdatedDate(cisInputBloodApply.getUpdatedDate());
        to.setExecutorStaff(cisInputBloodApply.getExecutorStaff());
        to.setExecutorDate(cisInputBloodApply.getExecutorDate());
        to.setExecutorHosptialCode(cisInputBloodApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisInputBloodApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisInputBloodApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisBloodApply.getMedrecordExamabstractId());
        to.setVisitType(cisInputBloodApply.getVisitType());
        to.setDeptNurseCode(cisInputBloodApply.getDeptNurseCode());
        to.setDeptNurseName(cisInputBloodApply.getDeptNurseName());
        to.setOrderID(cisInputBloodApply.getOrderID());
        to.setHospitalCode(cisInputBloodApply.getHospitalCode());
        to.setPrescriptionID(cisInputBloodApply.getPrescriptionID());
        to.setIsPrint(cisInputBloodApply.getIsPrint());
        to.setPrintStaff(cisInputBloodApply.getPrintStaff());
        to.setPrintDate(cisInputBloodApply.getPrintDate());
        to.setReMark(cisInputBloodApply.getReMark());
        to.setIcuExecuteDate(cisInputBloodApply.getIcuExecuteDate());
        to.setIsChargeManager(cisInputBloodApply.getIsChargeManager());
        to.setVersion(cisInputBloodApply.getVersion());
        to.setCreateOrgCode(cisInputBloodApply.getCreateOrgCode());
        to.setSortNo(cisInputBloodApply.getSortNo());
        to.setIsBaby(cisInputBloodApply.getIsBaby());
        to.setCisBloodApplyId(cisInputBloodApply.getCisBloodApplyId());
        to.setVisitOrgCode(cisInputBloodApply.getVisitOrgCode());
        to.setVisitOrgName(cisInputBloodApply.getVisitOrgName());
        to.setIsOlation(cisInputBloodApply.getIsOlation());
        to.setNum(cisInputBloodApply.getNum());
        to.setBloodType(cisInputBloodApply.getBloodType());
        to.setRh_d(cisInputBloodApply.getRh_d());
        to.setIsApply(cisInputBloodApply.getIsApply());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisInputBloodApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisInputBloodApply.getCisOrderExecPlans()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisInputBloodApply.getApplyDiagnoses()));
            to.setDetails(CisBloodComponentAssembler.toTos(cisInputBloodApply.getDetailList()));
        }
        return to;
    }


    public static CisInputBloodApplyNto toNto(CisInputBloodApply cisInputBloodApply, boolean withAllParts) {
        if (cisInputBloodApply == null)
            return null;
        CisInputBloodApplyNto to = new CisInputBloodApplyNto();
        to.setId(cisInputBloodApply.getId());
        to.setPatMiCode(cisInputBloodApply.getPatMiCode());
        to.setVisitCode(cisInputBloodApply.getVisitCode());
        to.setServiceItemCode(cisInputBloodApply.getServiceItemCode());
        to.setServiceItemName(cisInputBloodApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisInputBloodApply.getIsCanPriorityFlag());
//        to.setMedrecordExamabstractId(cisBloodApply.getMedrecordExamabstractId());
        to.setVisitType(cisInputBloodApply.getVisitType());
        to.setDeptNurseCode(cisInputBloodApply.getDeptNurseCode());
        to.setDeptNurseName(cisInputBloodApply.getDeptNurseName());
        to.setOrderID(cisInputBloodApply.getOrderID());
        to.setHospitalCode(cisInputBloodApply.getHospitalCode());
        to.setPrescriptionID(cisInputBloodApply.getPrescriptionID());
//        to.setIsPrint(cisBloodApply.getIsPrint());
//        to.setPrintStaff(cisBloodApply.getPrintStaff());
//        to.setPrintDate(cisBloodApply.getPrintDate());
        to.setReMark(cisInputBloodApply.getReMark());
        to.setIcuExecuteDate(cisInputBloodApply.getIcuExecuteDate());
        to.setIsChargeManager(cisInputBloodApply.getIsChargeManager());
        to.setCreateOrgCode(cisInputBloodApply.getCreateOrgCode());
        to.setSortNo(cisInputBloodApply.getSortNo());
        to.setIsBaby(cisInputBloodApply.getIsBaby());
        to.setCisBloodApplyId(cisInputBloodApply.getCisBloodApplyId());
        to.setVisitOrgCode(cisInputBloodApply.getVisitOrgCode());
        to.setOrderType(cisInputBloodApply.getOrderType());
        to.setVisitOrgName(cisInputBloodApply.getVisitOrgName());
        to.setCreateOrgName(cisInputBloodApply.getCreateOrgName());
        to.setExecutorOrgCode(cisInputBloodApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisInputBloodApply.getExecutorOrgName());
        to.setNum(cisInputBloodApply.getNum());
        to.setIsOlation(cisInputBloodApply.getIsOlation());
        to.setIsApply(cisInputBloodApply.getIsApply());
        if (withAllParts) {
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisBloodApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisBloodApply.getCisOrderExecPlans()));
        }
        return to;
    }

}