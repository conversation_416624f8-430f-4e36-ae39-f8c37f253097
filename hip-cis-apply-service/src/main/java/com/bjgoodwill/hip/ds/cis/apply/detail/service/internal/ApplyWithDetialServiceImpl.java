package com.bjgoodwill.hip.ds.cis.apply.detail.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.ApplyGroupService;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.CisBaseApplyServiceImpl;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.*;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.ApplyWithDetial;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.BaseDetail;
import com.bjgoodwill.hip.ds.cis.apply.detail.service.ApplyWithDetialService;
import com.bjgoodwill.hip.ds.cis.apply.detail.to.ApplyWithDetailEto;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisDrugApplyDetail;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-30 10:17
 * @className: ApplyWithDetialServiceImpl
 * @description:
 **/
public abstract class ApplyWithDetialServiceImpl<T extends BaseDetail> extends CisBaseApplyServiceImpl implements ApplyWithDetialService {


    /**
     * 根据申请单ID和明细ID列表删除药品基础申请单的明细。
     * 此方法确保在事务环境下执行，任何异常都将导致事务回滚。
     *
     * @param id                   申请单的唯一标识符。
     * @param cisBaseDrugDetailIds 明细ID的列表，表示需要被删除的明细项。
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteApplyWithDetialByDetails(String id, List<String> cisBaseDrugDetailIds) {
        // 断言明细ID列表不为空，否则抛出业务异常
        BusinessAssert.notEmpty(cisBaseDrugDetailIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单明细ID");
        // 根据ID尝试获取申请单对象
        Optional<CisBaseApply> cisBaseDrugApply = ApplyWithDetial.getCisBaseApplyById(id);
        // 如果申请单存在，则进行后续处理
        cisBaseDrugApply.ifPresent(o -> {
            ApplyWithDetial o1 = (ApplyWithDetial) o;
            // 获取申请单的所有明细项
            List<CisDrugApplyDetail> details = o1.getDetailList();
            // 断言明细项列表不为空，否则抛出异常，表示没有可删除的明细
//            Assert.notEmpty(details, "没有删除的明细！");
            BusinessAssert.notEmpty(details, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "没有删除的明细！");

            // 删除过滤后的明细项
            o1.deleteDetails(details, cisBaseDrugDetailIds);
        });
    }


//    @Override
//    @Transactional
//    public void splitGroup(CisBaseApplyGroupNto cisBaseApplyGroupNto) {
//        ApplyGroupService applyGroupService = SpringUtil.getBean(ApplyGroupService.class);
//        applyGroupService.splitApplyGroup(cisBaseApplyGroupNto);
//    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> splitGroup(CisBaseApplySplitGroupNto cisBaseApplySplitGroupNto) {
        ApplyGroupService applyGroupService = SpringUtil.getBean(ApplyGroupService.class);
        return applyGroupService.splitApplyGroup(cisBaseApplySplitGroupNto);
    }

    //mergeGroup
//    @Override
//    @Transactional
//    public void mergeGroup(CisBaseApplyGroupNto cisBaseApplyGroupNto) {
//        ApplyGroupService applyGroupService = SpringUtil.getBean(ApplyGroupService.class);
//        applyGroupService.mergeApplyGroup(cisBaseApplyGroupNto);
//    }

    @Override
    @Transactional
    public CisBaseApplyTo mergeGroup(CisBaseApplyMergeGroupEto cisBaseApplyMergeGroupEto) {
        ApplyGroupService applyGroupService = SpringUtil.getBean(ApplyGroupService.class);
        return applyGroupService.mergeApplyGroup(cisBaseApplyMergeGroupEto);
    }

    //region 新并组 拆组
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisBaseApplyTo mergeGroupNew(List<String> applyIds) {
        ApplyGroupService applyGroupService = SpringUtil.getBean(ApplyGroupService.class);
        return applyGroupService.mergeApplyGroupNew(applyIds);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public List<CisBaseApplyTo> splitGroupNew(String id, CisBaseApplySplitGroupNewNtoLst cisBaseApplySplitGroupNewNtoLst) {
        ApplyGroupService applyGroupService = SpringUtil.getBean(ApplyGroupService.class);
        return applyGroupService.splitApplyGroupNew(id, cisBaseApplySplitGroupNewNtoLst);
    }
    //endregion


    /**
     * 更新CisBaseApply实体及其相关明细
     * <p>
     * 此方法首先尝试根据提供的ID获取CisBaseApply实体，确保其存在
     * 然后，它利用Spring框架的依赖注入功能获取必要的服务实例
     * 接着，删除与给定明细ID关联的申请明细
     * 最后，调用更新服务根据提供的实体数据更新CisBaseApply实体
     *
     * @param id     申请实体的唯一标识符
     * @param entity 包含更新数据的CisBaseApplyEto实体
     * @return 返回更新后的CisBaseApplyTo实体
     * <p>
     * 此方法标记为事务性，确保在出现异常时事务能够回滚
     * 事务设置为对所有Throwable类型进行回滚，且事务不是只读的
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisBaseApplyTo updateCisBaseApply(String id, CisBaseApplyEto entity) {
        // 尝试根据ID获取CisBaseApply实体，如果不存在，则返回null
        CisBaseApply cisBaseApply = CisBaseApply.getCisBaseApplyById(id).orElse(null);
        // 断言CisBaseApply实体不为null，否则抛出业务异常，指示尝试修改不存在的实体
        BusinessAssert.notNull(cisBaseApply, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0006, id);


        CisBaseApplyTo cisBaseApplyTo = super.updateCisBaseApply(entity, id);

        // 获取ApplyWithDetialServiceTangibleImpl实例，用于处理申请明细的删除
        if (entity instanceof ApplyWithDetailEto) {
            List<String> deleteDetails = ((ApplyWithDetailEto) entity).getDeleteDetailIds();
            if (CollectionUtil.isNotEmpty(deleteDetails)) {
                ApplyWithDetialServiceTangibleImpl applyWithDetialServiceTangibleImpl = SpringUtil.getBean(ApplyWithDetialServiceTangibleImpl.class);
                // 根据提供的申请ID和明细ID列表删除申请明细
                applyWithDetialServiceTangibleImpl.deleteApplyWithDetialByDetails(id, deleteDetails);
            }
        }

        return cisBaseApplyTo;
    }

}