package com.bjgoodwill.hip.ds.cis.apply.apply.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisManagementApplyRepository;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "处置")
@DiscriminatorValue("04")
public class CisManagementApply extends CisBaseApply {

    public static Optional<CisManagementApply> getCisManagementApplyById(String id) {
        return dao().findById(id);
    }

    public static List<CisManagementApply> getCismenagementApply(CisManagementApplyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisManagementApply> getCisManagementApplyPage(CisManagementApplyQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisManagementApply> getSpecification(CisManagementApplyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (qto.getVisitType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderID"), qto.getOrderID()));
            }
            if (StringUtils.isNotBlank(qto.getHospitalCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("hospitalCode"), qto.getHospitalCode()));
            }
            if (StringUtils.isNotBlank(qto.getPrescriptionID())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("prescriptionID"), qto.getPrescriptionID()));
            }
            if (StringUtils.isNotBlank(qto.getCreateOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("createOrgCode"), qto.getCreateOrgCode()));
            }
            return predicate;
        };
    }

    private static CisManagementApplyRepository dao() {
        return SpringUtil.getBean(CisManagementApplyRepository.class);
    }

    @Override
    public SystemTypeEnum getSystemType() {
        return SystemTypeEnum.MANAGEMENT;
    }

    @Override
    public CisBaseApply create(CisBaseApplyNto cisBaseApplyNto, Boolean save) {
        return create((CisManagementApplyNto) cisBaseApplyNto, save);
    }

    @Override
    public void update(CisBaseApplyEto cisBaseApplyEto) {
        update((CisManagementApplyEto) cisBaseApplyEto);
    }

    public CisManagementApply create(CisManagementApplyNto cisManagementApplyNto, Boolean save) {
        BusinessAssert.notNull(cisManagementApplyNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisManagementApplyNto不能为空！");
        super.create(cisManagementApplyNto, save);

        BusinessAssert.hasText(cisManagementApplyNto.getServiceItemCode(), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "serviceItemCode");
        setServiceItemCode(cisManagementApplyNto.getServiceItemCode());

        if (save) {
            dao().save(this);
        }

//        if (cisManagementApplyNto.getCisApplyCharges() != null) {
//            for (CisApplyChargeNto cisApplyChargeNto_ : cisManagementApplyNto.getCisApplyCharges()) {
//                CisApplyCharge cisApplyCharge = new CisApplyCharge();
//                cisApplyCharge.create(getId(), cisApplyChargeNto_, save);
//            }
//        }
//        if (cisManagementApplyNto.getCisOrderExecPlans() != null) {
//            for (CisOrderExecPlanNto cisOrderExecPlanNto_ : cisManagementApplyNto.getCisOrderExecPlans()) {
//                cisOrderExecPlanNto_.setMainPlanFlag(cisManagementApplyNto.getExecutorOrgCode().equals(cisOrderExecPlanNto_.getExecOrgCode()));
//                CisOrderExecPlan cisOrderExecPlan = new CisOrderExecPlan();
//                cisOrderExecPlan.create(getId(), cisOrderExecPlanNto_, save);
//            }
//        }
        return this;
    }

    public void update(CisManagementApplyEto cisManagementApplyEto) {
        super.update(cisManagementApplyEto);
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
