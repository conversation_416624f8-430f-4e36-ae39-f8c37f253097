package com.bjgoodwill.hip.ds.cis.apply.drug.repository;

import com.bjgoodwill.hip.business.util.cis.common.enums.SbadmWayEnum;
import com.bjgoodwill.hip.ds.cis.apply.detail.entity.BaseDetail;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisDrugApplyDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.apply.drug.repository.CisDrugApplyDetailRepository")
public interface CisDrugApplyDetailRepository extends JpaRepository<CisDrugApplyDetail, String>, JpaSpecificationExecutor<CisDrugApplyDetail> {

    List<CisDrugApplyDetail> findByApplyId(String cisBaseDrugApplyId);

    Page<CisDrugApplyDetail> findByApplyId(String cisBaseDrugApplyId, Pageable pageable);

    boolean existsByApplyId(String cisBaseDrugApplyId);

    void deleteByApplyId(String cisBaseDrugApplyId);

    List<CisDrugApplyDetail> findCisDrugApplyDetailByCreatedDateAfter(LocalDateTime dateTime);

    List<CisDrugApplyDetail> findCisDrugApplyDetailsByVisitCode(String visitCode);


    List<CisDrugApplyDetail> findCisDrugApplyDetailsByApplyIdInAndSbadmWay(List<String> applyIds, SbadmWayEnum sbadmWay);

    List<CisDrugApplyDetail> findCisDrugApplyDetailsByApplyIdIn(List<String> applyIds);

    @Modifying
    @Query("update CisDrugApplyDetail c set c.applyId = ?2  where c.id in ?1")
    void updateApplyIdByIdsIn(List<String> ids, String applyId);

    @Query("select c from CisDrugApplyDetail c where c.createdDate >= ?1 or ?1 is null ")
    List<BaseDetail> queryDetailsByCreateDate(LocalDateTime dateTime);
}