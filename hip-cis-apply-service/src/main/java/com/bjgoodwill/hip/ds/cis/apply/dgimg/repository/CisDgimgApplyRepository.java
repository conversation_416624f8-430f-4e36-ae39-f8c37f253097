package com.bjgoodwill.hip.ds.cis.apply.dgimg.repository;

import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApply;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisDgimgApplyRepository")
public interface CisDgimgApplyRepository extends JpaRepository<CisDgimgApply, String>, JpaSpecificationExecutor<CisDgimgApply> {

    List<CisDgimgApply> findByApplyBookId(String applyBookId);

    Page<CisDgimgApply> findByApplyBookId(String applyBookId, Pageable pageable);

    boolean existsByApplyBookId(String applyBookId);

    void deleteByApplyBookId(String applyBookId);

}