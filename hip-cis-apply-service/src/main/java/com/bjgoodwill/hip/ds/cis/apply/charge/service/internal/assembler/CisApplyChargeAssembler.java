package com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.entity.CisApplyCharge;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeTo;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public abstract class CisApplyChargeAssembler {

    public static List<CisApplyChargeTo> toTos(List<CisApplyCharge> cisApplyCharges) {
        return toTos(cisApplyCharges, false);
    }

    public static List<CisApplyChargeTo> toTos(List<CisApplyCharge> cisApplyCharges, boolean withAllParts) {
        BusinessAssert.notNull(cisApplyCharges, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisApplyCharges不能为空！");

        List<CisApplyChargeTo> tos = new ArrayList<>();
        for (CisApplyCharge cisApplyCharge : cisApplyCharges)
            tos.add(toTo(cisApplyCharge, withAllParts));
        return tos;
    }

    public static CisApplyChargeTo toTo(CisApplyCharge cisApplyCharge) {
        return toTo(cisApplyCharge, false);
    }

    /**
     * @generated
     */
    public static CisApplyChargeTo toTo(CisApplyCharge cisApplyCharge, boolean withAllParts) {
        if (cisApplyCharge == null)
            return null;
        CisApplyChargeTo to = new CisApplyChargeTo();
        to.setId(cisApplyCharge.getId());
        to.setCisBaseApplyId(cisApplyCharge.getApplyId());
        to.setOrderId(cisApplyCharge.getOrderId());
        to.setVisitCode(cisApplyCharge.getVisitCode());
        to.setPriceItemCode(cisApplyCharge.getPriceItemCode());
        to.setPriceItemName(cisApplyCharge.getPriceItemName());
        to.setIsFixed(cisApplyCharge.getIsFixed());
        to.setPackageSpec(cisApplyCharge.getPackageSpec());
        to.setPrice(cisApplyCharge.getPrice());
        to.setUnit(cisApplyCharge.getUnit());
        to.setUnitName(cisApplyCharge.getUnitName());
        to.setNum(cisApplyCharge.getNum());
        to.setChargeTime(cisApplyCharge.getChargeTime());
        to.setChargeFrequency(cisApplyCharge.getChargeFrequency());
        to.setStatusCode(cisApplyCharge.getStatusCode());
        to.setCreatedStaff(cisApplyCharge.getCreatedStaff());
        to.setCreatedDate(cisApplyCharge.getCreatedDate());
        to.setUpdatedStaff(cisApplyCharge.getUpdatedStaff());
        to.setUpdatedDate(cisApplyCharge.getUpdatedDate());
        to.setExecuteOrgCode(cisApplyCharge.getExecuteOrgCode());
        to.setExecuteOrgName(cisApplyCharge.getExecuteOrgName());
        to.setChageAmount(cisApplyCharge.getChageAmount());
        to.setLimitConformFlag(cisApplyCharge.getLimitConformFlag());
        to.setBarCode(cisApplyCharge.getBarCode());
        to.setVersion(cisApplyCharge.getVersion());
        to.setDetailId(cisApplyCharge.getDetailId());
        to.setSystemItemClass(cisApplyCharge.getSystemItemClass());
        to.setChargeType(cisApplyCharge.getChargeType());
        if (withAllParts) {
        }
        return to;
    }


    //region 转换成Nto
    public static List<CisApplyChargeNto> toNtos(List<CisApplyCharge> cisApplyCharges) {
        return toNtos(cisApplyCharges, false);
    }

    public static List<CisApplyChargeNto> toNtos(List<CisApplyCharge> cisApplyCharges, boolean withAllParts) {
//        BusinessAssert.notNull(cisApplyCharges, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisApplyCharges不能为空！");

        List<CisApplyChargeNto> tos = new ArrayList<>();
        if (CollectionUtils.isEmpty(cisApplyCharges)) {
            return tos;
        }
        for (CisApplyCharge cisApplyCharge : cisApplyCharges)
            tos.add(toNto(cisApplyCharge, withAllParts));
        return tos;
    }

    /**
     * @generated
     */
    public static CisApplyChargeNto toNto(CisApplyCharge cisApplyCharge, boolean withAllParts) {
        if (cisApplyCharge == null)
            return null;
        CisApplyChargeNto to = new CisApplyChargeNto();
        to.setId(HIPIDUtil.getNextIdString());
        to.setOrderId(cisApplyCharge.getOrderId());
        to.setVisitCode(cisApplyCharge.getVisitCode());
        to.setPriceItemCode(cisApplyCharge.getPriceItemCode());
        to.setPriceItemName(cisApplyCharge.getPriceItemName());
        to.setIsFixed(cisApplyCharge.getIsFixed());
        to.setPackageSpec(cisApplyCharge.getPackageSpec());
        to.setPrice(cisApplyCharge.getPrice());
        to.setUnit(cisApplyCharge.getUnit());
        to.setUnitName(cisApplyCharge.getUnitName());
        to.setNum(cisApplyCharge.getNum());
        to.setChargeTime(cisApplyCharge.getChargeTime());
        to.setChargeFrequency(cisApplyCharge.getChargeFrequency());
        to.setStatusCode(cisApplyCharge.getStatusCode());
        to.setExecuteOrgCode(cisApplyCharge.getExecuteOrgCode());
        to.setExecuteOrgName(cisApplyCharge.getExecuteOrgName());
        to.setChageAmount(cisApplyCharge.getChageAmount());
        to.setLimitConformFlag(cisApplyCharge.getLimitConformFlag());
        to.setBarCode(cisApplyCharge.getBarCode());
        to.setCisBaseApplyId(cisApplyCharge.getApplyId());
        to.setChargeType(cisApplyCharge.getChargeType());
        to.setDetailId(cisApplyCharge.getDetailId());
        to.setSystemItemClass(cisApplyCharge.getSystemItemClass());
        to.setChargeType(cisApplyCharge.getChargeType());
        if (withAllParts) {
        }
        return to;
    }
    //endregion
}