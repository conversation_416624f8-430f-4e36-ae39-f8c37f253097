package com.bjgoodwill.hip.ds.cis.apply.apply.repository;

import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisTreatmentApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.apply.apply.repository.CisTreatmentApplyRepository")
public interface CisTreatmentApplyRepository extends JpaRepository<CisTreatmentApply, String>, JpaSpecificationExecutor<CisTreatmentApply> {

}