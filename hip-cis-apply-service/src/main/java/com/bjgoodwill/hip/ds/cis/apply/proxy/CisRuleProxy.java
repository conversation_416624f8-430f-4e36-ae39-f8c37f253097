package com.bjgoodwill.hip.ds.cis.apply.proxy;

import com.bjgoodwill.hip.business.util.cis.util.DoctAuthCommonQto;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.service.DoctDrugAuthorityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-12-23 14:46
 * @className: CisRuleProxy
 * @description:
 **/
@Component
public class CisRuleProxy {

    @Autowired
    private DoctDrugAuthorityService doctDrugAuthorityService;

    /**
     * 验证规则方法
     * 该方法用于验证医生的药物授权规则是否满足要求
     *
     * @param applies 申请列表，包含多个CisBaseApply对象，代表不同的申请记录
     */
    public void vertyfyRule(List<CisBaseApply> applies) {

        // 检查申请列表是否为空，如果为空则直接返回，不再进行后续处理
        if (CollectionUtils.isEmpty(applies)) {
            return;
        }

        // 将申请列表转换为DoctAuthCommonQto对象列表，便于后续统一处理
        // 这里使用了Java 8的Stream API进行列表转换，提高了代码的可读性和效率
        List<DoctAuthCommonQto> qtos = applies.stream()
                .map(apply -> new DoctAuthCommonQto(apply.getSystemType(), apply.getServiceItemCode(), apply.getVisitOrgCode()))
                .toList();

        // 调用医生药物授权服务的验证方法，传入转换后的QTO列表
        // 这一步是核心逻辑，用于实际进行医生药物授权的验证
        doctDrugAuthorityService.verifyDoctDrugAuthority(qtos);
    }
}