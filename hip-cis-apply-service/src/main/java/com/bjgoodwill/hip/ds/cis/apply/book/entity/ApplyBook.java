package com.bjgoodwill.hip.ds.cis.apply.book.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.book.repository.ApplyBookRepository;
import com.bjgoodwill.hip.ds.cis.apply.book.to.ApplyBookEto;
import com.bjgoodwill.hip.ds.cis.apply.book.to.ApplyBookNto;
import com.bjgoodwill.hip.ds.cis.apply.book.to.ApplyBookQto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApply;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "申请单预约")
@Table(name = "cis_apply_book", indexes = {@Index(name = "idx_cis_apply_book_applyId"
        , columnList = "apply_id")}, uniqueConstraints = {})
public class ApplyBook {

    // 标识
    private String id;
    // 预约状态，开立为NEW，预约成功为ACTIVE
    private CisStatusEnum appointsStatus;
    // 预约开始时间
    private LocalDateTime appointsStartDate;
    // 预约结束时间
    private String appointsEndDate;

    private String applyId;

    public static Optional<ApplyBook> getApplyBookById(String id) {
        return dao().findById(id);
    }

    public static List<ApplyBook> findApplyBooksByApplyIdIn(List<String> applyIds) {
        return dao().findApplyBooksByApplyIdIn(applyIds);
    }

    public static List<ApplyBook> getApplyBooks(ApplyBookQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<ApplyBook> getApplyBookPage(ApplyBookQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<ApplyBook> getSpecification(ApplyBookQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (qto.getAppointsStatus() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("appointsStatus"), qto.getAppointsStatus()));
            }
            if (qto.getAppointsStartDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("appointsStartDate"), LocalDateUtil.beginOfDay(qto.getAppointsStartDate()), LocalDateUtil.endOfDay(qto.getAppointsStartDate())));
            }
            if (StringUtils.isNotBlank(qto.getAppointsEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("appointsEndDate"), qto.getAppointsEndDate()));
            }
            return predicate;
        };
    }

    private static ApplyBookRepository dao() {
        return SpringUtil.getBean(ApplyBookRepository.class);
    }

    @Id
//    @GeneratedValue(generator = "snowflake_generator")
//    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Enumerated(EnumType.STRING)
    @Comment("预约状态，开立为NEW，预约成功为ACTIVE")
    @Column(name = "appoints_status", nullable = true)
    public CisStatusEnum getAppointsStatus() {
        return appointsStatus;
    }

    protected void setAppointsStatus(CisStatusEnum appointsStatus) {
        this.appointsStatus = appointsStatus;
    }

    @Comment("预约开始时间")
    @Column(name = "appoints_start_date", nullable = false)
    public LocalDateTime getAppointsStartDate() {
        return appointsStartDate;
    }

    protected void setAppointsStartDate(LocalDateTime appointsStartDate) {
        this.appointsStartDate = appointsStartDate;
    }

    @Comment("预约结束时间")
    @Column(name = "appoints_end_date", nullable = true)
    public String getAppointsEndDate() {
        return appointsEndDate;
    }

    protected void setAppointsEndDate(String appointsEndDate) {
        this.appointsEndDate = appointsEndDate;
    }


    @Comment("抽象父类标识")
    @Column(name = "apply_id", nullable = false, length = 50)
    public String getApplyId() {
        return applyId;
    }

    protected void setApplyId(String cisBaseApplyId) {
        this.applyId = cisBaseApplyId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ApplyBook other = (ApplyBook) obj;
        return Objects.equals(id, other.id);
    }

    public ApplyBook create(ApplyBookNto applyBookNto) {
        BusinessAssert.notNull(applyBookNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数applyBookNto不能为空！");
        setId(applyBookNto.getId());
        setApplyId(applyBookNto.getApplyId());
        setAppointsStartDate(applyBookNto.getAppointsStartDate());
        setAppointsEndDate(applyBookNto.getAppointsEndDate());
        dao().save(this);
        return this;
    }

    public void update(ApplyBookEto applyBookEto) {
        setAppointsEndDate(applyBookEto.getAppointsEndDate());
    }

    public void delete() {
        for (CisDgimgApply cisDgimgApply : CisDgimgApply.getByApplyBookId(getId())) {
            cisDgimgApply.delete();
        }
        // CisDgimgApply.deleteByApplyBookId(getId());dao().delete(this);
    }

}
