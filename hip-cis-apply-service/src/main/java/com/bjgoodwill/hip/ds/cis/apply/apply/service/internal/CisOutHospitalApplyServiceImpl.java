package com.bjgoodwill.hip.ds.cis.apply.apply.service.internal;

import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisOutHospitalApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisOutHospitalApplyService;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisOutHospitalApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisOutHospitalApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisOutHospitalApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisOutHospitalApplyTo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisOutHospitalApplyService")
@RequestMapping(value = "/api/apply/apply/cisOutHospitalApply", produces = "application/json; charset=utf-8")
public class CisOutHospitalApplyServiceImpl extends CisBaseApplyServiceImpl implements CisOutHospitalApplyService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisOutHospitalApplyTo getCisOutHospitalApplyById(String id) {
        return CisOutHospitalApplyAssembler.toTo(CisOutHospitalApply.getCisOutHospitalApplyById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOutHospitalApplyTo createCisOutHospitalApply(CisOutHospitalApplyNto cisOutHospitalApplyNto) {
        CisOutHospitalApply cisOutHospitalApply = new CisOutHospitalApply();
        cisOutHospitalApply = cisOutHospitalApply.create(cisOutHospitalApplyNto, true);
        return CisOutHospitalApplyAssembler.toTo(cisOutHospitalApply);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOutHospitalApply(String id, CisOutHospitalApplyEto cisOutHospitalApplyEto) {
        Optional<CisOutHospitalApply> cisOutHospitalApplyOptional = CisOutHospitalApply.getCisOutHospitalApplyById(id);
        cisOutHospitalApplyOptional.ifPresent(cisOutHospitalApply -> cisOutHospitalApply.update(cisOutHospitalApplyEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOutHospitalApply(String id) {
        Optional<CisOutHospitalApply> cisOutHospitalApplyOptional = CisOutHospitalApply.getCisOutHospitalApplyById(id);
        cisOutHospitalApplyOptional.ifPresent(cisOutHospitalApply -> cisOutHospitalApply.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}