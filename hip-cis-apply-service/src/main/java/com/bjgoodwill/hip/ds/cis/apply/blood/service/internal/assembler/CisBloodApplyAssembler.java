package com.bjgoodwill.hip.ds.cis.apply.blood.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.blood.entity.CisBloodApply;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;

import java.util.ArrayList;
import java.util.List;

public abstract class CisBloodApplyAssembler {

    public static List<CisBloodApplyTo> toTos(List<CisBloodApply> cisBloodApplies) {
        return toTos(cisBloodApplies, false);
    }

    public static List<CisBloodApplyTo> toTos(List<CisBloodApply> cisBloodApplies, boolean withAllParts) {
        BusinessAssert.notNull(cisBloodApplies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisPreparationBloodApplys不能为空！");

        List<CisBloodApplyTo> tos = new ArrayList<>();
        for (CisBloodApply cisBloodApply : cisBloodApplies)
            tos.add(toTo(cisBloodApply, withAllParts));
        return tos;
    }

    public static CisBloodApplyTo toTo(CisBloodApply cisBloodApply) {
        return toTo(cisBloodApply, false);
    }

    /**
     * @generated
     */
    public static CisBloodApplyTo toTo(CisBloodApply cisBloodApply, boolean withAllParts) {
        if (cisBloodApply == null)
            return null;
        CisBloodApplyTo to = new CisBloodApplyTo();
        to.setId(cisBloodApply.getId());
        to.setPatMiCode(cisBloodApply.getPatMiCode());
        to.setVisitCode(cisBloodApply.getVisitCode());
        to.setServiceItemCode(cisBloodApply.getServiceItemCode());
        to.setServiceItemName(cisBloodApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisBloodApply.getIsCanPriorityFlag());
        to.setStatusCode(cisBloodApply.getStatusCode());
        to.setCreatedStaff(cisBloodApply.getCreatedStaff());
        to.setCreatedDate(cisBloodApply.getCreatedDate());
        to.setUpdatedStaff(cisBloodApply.getUpdatedStaff());
        to.setUpdatedDate(cisBloodApply.getUpdatedDate());
        to.setExecutorStaff(cisBloodApply.getExecutorStaff());
        to.setExecutorDate(cisBloodApply.getExecutorDate());
        to.setExecutorHosptialCode(cisBloodApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisBloodApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisBloodApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisPreparationBloodApply.getMedrecordExamabstractId());
        to.setVisitType(cisBloodApply.getVisitType());
        to.setDeptNurseCode(cisBloodApply.getDeptNurseCode());
        to.setDeptNurseName(cisBloodApply.getDeptNurseName());
        to.setOrderID(cisBloodApply.getOrderID());
        to.setHospitalCode(cisBloodApply.getHospitalCode());
        to.setPrescriptionID(cisBloodApply.getPrescriptionID());
        to.setIsPrint(cisBloodApply.getIsPrint());
        to.setPrintStaff(cisBloodApply.getPrintStaff());
        to.setPrintDate(cisBloodApply.getPrintDate());
        to.setReMark(cisBloodApply.getReMark());
        to.setIcuExecuteDate(cisBloodApply.getIcuExecuteDate());
        to.setIsChargeManager(cisBloodApply.getIsChargeManager());
        to.setVersion(cisBloodApply.getVersion());
        to.setCreateOrgCode(cisBloodApply.getCreateOrgCode());
        to.setSortNo(cisBloodApply.getSortNo());
        to.setIsBaby(cisBloodApply.getIsBaby());
        to.setPreInfusionDate(cisBloodApply.getPreInfusionDate());
        to.setClinicalDiagnosis(cisBloodApply.getClinicalDiagnosis());
        to.setTransfusionTrigger(cisBloodApply.getTransfusionTrigger());
        to.setTransfusionDemand(cisBloodApply.getTransfusionDemand());
        to.setTransfusionPurpose(cisBloodApply.getTransfusionPurpose());
        to.setTransfusionWay(cisBloodApply.getTransfusionWay());
        to.setTransfusionDetection(cisBloodApply.getTransfusionDetection());
        to.setDrawBloodUser(cisBloodApply.getDrawBloodUser());
        to.setDrawBloodUserName(cisBloodApply.getDrawBloodUserName());
        to.setBloodType(cisBloodApply.getBloodType());
        to.etRh_d(cisBloodApply.getRh_d());
        to.setErythrocyte(cisBloodApply.getErythrocyte());
        to.setLeukocyte(cisBloodApply.getLeukocyte());
        to.setHemoglobin(cisBloodApply.getHemoglobin());
        to.setThrombocyte(cisBloodApply.getThrombocyte());
        to.setHematokrit(cisBloodApply.getHematokrit());
        to.setGlutamicPyruvic(cisBloodApply.getGlutamicPyruvic());
        to.setAptt(cisBloodApply.getAptt());
        to.setFibd(cisBloodApply.getFibd());
        to.setPt(cisBloodApply.getPt());
        to.setHbsAg(cisBloodApply.getHbsAg());
        to.setHbsAb(cisBloodApply.getHbsAb());
        to.setHbeAg(cisBloodApply.getHbeAg());
        to.setHbeAb(cisBloodApply.getHbeAb());
        to.setHbcAb(cisBloodApply.getHbcAb());
        to.setHcvAb(cisBloodApply.getHcvAb());
        to.setTpAb(cisBloodApply.getTpAb());
        to.setHivAb(cisBloodApply.getHivAb());
        to.setIndicate1(cisBloodApply.getIndicate1());
        to.setIndicate2(cisBloodApply.getIndicate2());
        to.setSpecimenRetentionDate(cisBloodApply.getSpecimenRetentionDate());
        to.setApplyUser(cisBloodApply.getApplyUser());
        to.setApplyUserName(cisBloodApply.getApplyUserName());
        to.setApplyDate(cisBloodApply.getApplyDate());
        to.setAppleType(cisBloodApply.getAppleType());
        to.setPreBlood(cisBloodApply.getPreBlood());
        to.setVisitOrgCode(cisBloodApply.getVisitOrgCode());
        to.setVisitOrgName(cisBloodApply.getVisitOrgName());
        to.setNum(cisBloodApply.getNum());
        to.setIsOlation(cisBloodApply.getIsOlation());
        to.setTransfusionHistory(cisBloodApply.getTransfusionHistory());
        to.setPregnancyHistory(cisBloodApply.getPregnancyHistory());
        to.setAllergicHistoryFlag(cisBloodApply.getAllergicHistoryFlag());
        to.setSeniorPhysician(cisBloodApply.getSeniorPhysician());
        to.setSeniorPhysicianName(cisBloodApply.getSeniorPhysicianName());
        to.setSeniorPhysicianOpinion(cisBloodApply.getSeniorPhysicianOpinion());
        to.setPreInfusionBloodType(cisBloodApply.getPreInfusionBloodType());
        to.setIsApply(cisBloodApply.getIsApply());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisBloodApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisBloodApply.getCisOrderExecPlans()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisBloodApply.getApplyDiagnoses()));
            to.setDetails(CisBloodComponentAssembler.toTos(cisBloodApply.getDetailList()));
        }
        return to;
    }


    public static CisBloodApplyNto toNto(CisBloodApply cisBloodApply, boolean withAllParts) {
        if (cisBloodApply == null)
            return null;
        CisBloodApplyNto to = new CisBloodApplyNto();
        to.setId(cisBloodApply.getId());
        to.setPatMiCode(cisBloodApply.getPatMiCode());
        to.setVisitCode(cisBloodApply.getVisitCode());
        to.setServiceItemCode(cisBloodApply.getServiceItemCode());
        to.setServiceItemName(cisBloodApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisBloodApply.getIsCanPriorityFlag());
//        to.setMedrecordExamabstractId(cisPreparationBloodApply.getMedrecordExamabstractId());
        to.setVisitType(cisBloodApply.getVisitType());
        to.setDeptNurseCode(cisBloodApply.getDeptNurseCode());
        to.setDeptNurseName(cisBloodApply.getDeptNurseName());
        to.setOrderID(cisBloodApply.getOrderID());
        to.setHospitalCode(cisBloodApply.getHospitalCode());
        to.setPrescriptionID(cisBloodApply.getPrescriptionID());
//        to.setIsPrint(cisPreparationBloodApply.getIsPrint());
//        to.setPrintStaff(cisPreparationBloodApply.getPrintStaff());
//        to.setPrintDate(cisPreparationBloodApply.getPrintDate());
        to.setReMark(cisBloodApply.getReMark());
        to.setIcuExecuteDate(cisBloodApply.getIcuExecuteDate());
        to.setIsChargeManager(cisBloodApply.getIsChargeManager());
        to.setCreateOrgCode(cisBloodApply.getCreateOrgCode());
        to.setSortNo(cisBloodApply.getSortNo());
        to.setIsBaby(cisBloodApply.getIsBaby());
        to.setPreInfusionDate(cisBloodApply.getPreInfusionDate());
        to.setClinicalDiagnosis(cisBloodApply.getClinicalDiagnosis());
        to.setTransfusionTrigger(cisBloodApply.getTransfusionTrigger());
        to.setTransfusionDemand(cisBloodApply.getTransfusionDemand());
        to.setTransfusionPurpose(cisBloodApply.getTransfusionPurpose());
        to.setTransfusionWay(cisBloodApply.getTransfusionWay());
        to.setTransfusionDetection(cisBloodApply.getTransfusionDetection());
        to.setDrawBloodUser(cisBloodApply.getDrawBloodUser());
        to.setBloodType(cisBloodApply.getBloodType());
        to.setRh_d(cisBloodApply.getRh_d());
        to.setErythrocyte(cisBloodApply.getErythrocyte());
        to.setLeukocyte(cisBloodApply.getLeukocyte());
        to.setHemoglobin(cisBloodApply.getHemoglobin());
        to.setThrombocyte(cisBloodApply.getThrombocyte());
        to.setHematokrit(cisBloodApply.getHematokrit());
        to.setGlutamicPyruvic(cisBloodApply.getGlutamicPyruvic());
        to.setAptt(cisBloodApply.getAptt());
        to.setFibd(cisBloodApply.getFibd());
        to.setPt(cisBloodApply.getPt());
        to.setHbsAg(cisBloodApply.getHbsAg());
        to.setHbsAb(cisBloodApply.getHbsAb());
        to.setHbeAg(cisBloodApply.getHbeAg());
        to.setHbeAb(cisBloodApply.getHbeAb());
        to.setHbcAb(cisBloodApply.getHbcAb());
        to.setHcvAb(cisBloodApply.getHcvAb());
        to.setTpAb(cisBloodApply.getTpAb());
        to.setHivAb(cisBloodApply.getHivAb());
        to.setIndicate1(cisBloodApply.getIndicate1());
        to.setIndicate2(cisBloodApply.getIndicate2());
        to.setSpecimenRetentionDate(cisBloodApply.getSpecimenRetentionDate());
        to.setApplyUser(cisBloodApply.getApplyUser());
        to.setApplyDate(cisBloodApply.getApplyDate());
        to.setAppleType(cisBloodApply.getAppleType());
        to.setPreBlood(cisBloodApply.getPreBlood());
        to.setVisitOrgCode(cisBloodApply.getVisitOrgCode());
        to.setOrderType(cisBloodApply.getOrderType());
        to.setVisitOrgName(cisBloodApply.getVisitOrgName());
        to.setCreateOrgName(cisBloodApply.getCreateOrgName());
        to.setExecutorOrgCode(cisBloodApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisBloodApply.getExecutorOrgName());
        to.setNum(cisBloodApply.getNum());
        to.setIsOlation(cisBloodApply.getIsOlation());
        to.setTransfusionHistory(cisBloodApply.getTransfusionHistory());
        to.setPregnancyHistory(cisBloodApply.getPregnancyHistory());
        to.setAllergicHistoryFlag(cisBloodApply.getAllergicHistoryFlag());
        to.setSeniorPhysician(cisBloodApply.getSeniorPhysician());
        to.setSeniorPhysicianName(cisBloodApply.getSeniorPhysicianName());
        to.setSeniorPhysicianOpinion(cisBloodApply.getSeniorPhysicianOpinion());
        to.setPreInfusionBloodType(cisBloodApply.getPreInfusionBloodType());
        to.setIsApply(cisBloodApply.getIsApply());
        if (withAllParts) {
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisPreparationBloodApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisPreparationBloodApply.getCisOrderExecPlans()));
        }
        return to;
    }

}