package com.bjgoodwill.hip.ds.cis.apply.apply.service.internal;

import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisChangeDeptApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisChangeDeptApplyService;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisChangeDeptApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisChangeDeptApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisChangeDeptApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisChangeDeptApplyTo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisChangeDeptApplyService")
@RequestMapping(value = "/api/apply/apply/cisChangeDeptApply", produces = "application/json; charset=utf-8")
public class CisChangeDeptApplyServiceImpl extends CisBaseApplyServiceImpl implements CisChangeDeptApplyService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisChangeDeptApplyTo getCisChangeDeptApplyById(String id) {
        return CisChangeDeptApplyAssembler.toTo(CisChangeDeptApply.getCisChangeDeptApplyById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisChangeDeptApplyTo createCisChangeDeptApply(CisChangeDeptApplyNto cisChangeDeptApplyNto) {
        CisChangeDeptApply cisChangeDeptApply = new CisChangeDeptApply();
        cisChangeDeptApply = cisChangeDeptApply.create(cisChangeDeptApplyNto, true);
        return CisChangeDeptApplyAssembler.toTo(cisChangeDeptApply);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisChangeDeptApply(String id, CisChangeDeptApplyEto cisChangeDeptApplyEto) {
        Optional<CisChangeDeptApply> cisChangeDeptApplyOptional = CisChangeDeptApply.getCisChangeDeptApplyById(id);
        cisChangeDeptApplyOptional.ifPresent(cisChangeDeptApply -> cisChangeDeptApply.update(cisChangeDeptApplyEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisChangeDeptApply(String id) {
        Optional<CisChangeDeptApply> cisChangeDeptApplyOptional = CisChangeDeptApply.getCisChangeDeptApplyById(id);
        cisChangeDeptApplyOptional.ifPresent(cisChangeDeptApply -> cisChangeDeptApply.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}