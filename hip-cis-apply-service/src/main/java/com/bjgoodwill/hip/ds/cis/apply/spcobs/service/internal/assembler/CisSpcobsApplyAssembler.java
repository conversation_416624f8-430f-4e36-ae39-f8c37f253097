package com.bjgoodwill.hip.ds.cis.apply.spcobs.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApply;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisSpcobsApplyAssembler {

    public static List<CisSpcobsApplyTo> toTos(List<CisSpcobsApply> cisSpcobsApplys) {
        return toTos(cisSpcobsApplys, false);
    }

    public static List<CisSpcobsApplyTo> toTos(List<CisSpcobsApply> cisSpcobsApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisSpcobsApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisSpcobsApplys不能为空！");

        List<CisSpcobsApplyTo> tos = new ArrayList<>();
        for (CisSpcobsApply cisSpcobsApply : cisSpcobsApplys)
            tos.add(toTo(cisSpcobsApply, withAllParts));
        return tos;
    }

    public static CisSpcobsApplyTo toTo(CisSpcobsApply cisSpcobsApply) {
        return toTo(cisSpcobsApply, false);
    }

    /**
     * @generated
     */
    public static CisSpcobsApplyTo toTo(CisSpcobsApply cisSpcobsApply, boolean withAllParts) {
        if (cisSpcobsApply == null)
            return null;
        CisSpcobsApplyTo to = new CisSpcobsApplyTo();
        to.setId(cisSpcobsApply.getId());
        to.setPatMiCode(cisSpcobsApply.getPatMiCode());
        to.setVisitCode(cisSpcobsApply.getVisitCode());
        to.setServiceItemCode(cisSpcobsApply.getServiceItemCode());
        to.setServiceItemName(cisSpcobsApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisSpcobsApply.getIsCanPriorityFlag());
        to.setStatusCode(cisSpcobsApply.getStatusCode());
        to.setCreatedStaff(cisSpcobsApply.getCreatedStaff());
        to.setCreatedStaffName(cisSpcobsApply.getCreatedStaffName());
        to.setCreatedDate(cisSpcobsApply.getCreatedDate());
        to.setUpdatedStaff(cisSpcobsApply.getUpdatedStaff());
        to.setUpdatedDate(cisSpcobsApply.getUpdatedDate());
        to.setExecutorStaff(cisSpcobsApply.getExecutorStaff());
        to.setExecutorDate(cisSpcobsApply.getExecutorDate());
        to.setExecutorHosptialCode(cisSpcobsApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisSpcobsApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisSpcobsApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisSpcobsApply.getMedrecordExamabstractId());
        to.setVisitType(cisSpcobsApply.getVisitType());
        to.setDeptNurseCode(cisSpcobsApply.getDeptNurseCode());
        to.setDeptNurseName(cisSpcobsApply.getDeptNurseName());
        to.setOrderID(cisSpcobsApply.getOrderID());
        to.setHospitalCode(cisSpcobsApply.getHospitalCode());
        to.setPrescriptionID(cisSpcobsApply.getPrescriptionID());
        to.setIsPrint(cisSpcobsApply.getIsPrint());
        to.setPrintStaff(cisSpcobsApply.getPrintStaff());
        to.setPrintDate(cisSpcobsApply.getPrintDate());
        to.setReMark(cisSpcobsApply.getReMark());
        to.setIcuExecuteDate(cisSpcobsApply.getIcuExecuteDate());
        to.setIsChargeManager(cisSpcobsApply.getIsChargeManager());
        to.setVersion(cisSpcobsApply.getVersion());
        to.setCreateOrgCode(cisSpcobsApply.getCreateOrgCode());
        to.setSortNo(cisSpcobsApply.getSortNo());
        to.setIsBaby(cisSpcobsApply.getIsBaby());
        to.setRemark(cisSpcobsApply.getRemark());
        to.setSpcobsClass(cisSpcobsApply.getSpcobsClass());
        to.setSpcobsClassName(cisSpcobsApply.getSpcobsClassName());
        to.setPrintBarCode(cisSpcobsApply.getPrintBarCode());
        to.setBarCode(cisSpcobsApply.getBarCode());
        to.setReportPdfUrl(cisSpcobsApply.getReportPdfUrl());
        to.setVisitOrgCode(cisSpcobsApply.getVisitOrgCode());
        to.setVisitOrgName(cisSpcobsApply.getVisitOrgName());
        to.setSpeciman(cisSpcobsApply.getSpeciman());
        to.setSpecimanName(cisSpcobsApply.getSpecimanName());
        to.setNum(cisSpcobsApply.getNum());
        to.setIsOlation(cisSpcobsApply.getIsOlation());
        to.setIsApply(cisSpcobsApply.getIsApply());
        if (withAllParts) {
            to.setCisSpcobsApplyDetails(CisSpcobsApplyDetailAssembler.toTos(cisSpcobsApply.getDetailList()));
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisSpcobsApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisSpcobsApply.getCisOrderExecPlans()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisSpcobsApply.getApplyDiagnoses()));
        }
        return to;
    }


    public static CisSpcobsApplyNto toNto(CisSpcobsApply cisSpcobsApply, boolean withAllParts) {
        if (cisSpcobsApply == null)
            return null;
        CisSpcobsApplyNto to = new CisSpcobsApplyNto();
        to.setId(cisSpcobsApply.getId());
        to.setPatMiCode(cisSpcobsApply.getPatMiCode());
        to.setVisitCode(cisSpcobsApply.getVisitCode());
        to.setServiceItemCode(cisSpcobsApply.getServiceItemCode());
        to.setServiceItemName(cisSpcobsApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisSpcobsApply.getIsCanPriorityFlag());
//        to.setMedrecordExamabstractId(cisSpcobsApply.getMedrecordExamabstractId());
        to.setVisitType(cisSpcobsApply.getVisitType());
        to.setDeptNurseCode(cisSpcobsApply.getDeptNurseCode());
        to.setDeptNurseName(cisSpcobsApply.getDeptNurseName());
        to.setOrderID(cisSpcobsApply.getOrderID());
        to.setHospitalCode(cisSpcobsApply.getHospitalCode());
        to.setPrescriptionID(cisSpcobsApply.getPrescriptionID());
//        to.setIsPrint(cisSpcobsApply.getIsPrint());
//        to.setPrintStaff(cisSpcobsApply.getPrintStaff());
//        to.setPrintDate(cisSpcobsApply.getPrintDate());
        to.setReMark(cisSpcobsApply.getReMark());
        to.setIcuExecuteDate(cisSpcobsApply.getIcuExecuteDate());
        to.setIsChargeManager(cisSpcobsApply.getIsChargeManager());
        to.setCreateOrgCode(cisSpcobsApply.getCreateOrgCode());
        to.setSortNo(cisSpcobsApply.getSortNo());
        to.setIsBaby(cisSpcobsApply.getIsBaby());
        to.setRemark(cisSpcobsApply.getRemark());
        to.setSpcobsClass(cisSpcobsApply.getSpcobsClass());
        to.setSpcobsClassName(cisSpcobsApply.getSpcobsClassName());
        to.setVisitOrgCode(cisSpcobsApply.getVisitOrgCode());
        to.setOrderType(cisSpcobsApply.getOrderType());
        to.setVisitOrgName(cisSpcobsApply.getVisitOrgName());
        to.setCreateOrgName(cisSpcobsApply.getCreateOrgName());
        to.setSpeciman(cisSpcobsApply.getSpeciman());
        to.setSpecimanName(cisSpcobsApply.getSpecimanName());
        to.setExecutorOrgCode(cisSpcobsApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisSpcobsApply.getExecutorOrgName());
        to.setNum(cisSpcobsApply.getNum());
        to.setIsOlation(cisSpcobsApply.getIsOlation());
        to.setIsApply(cisSpcobsApply.getIsApply());
        if (withAllParts) {
//            to.setCisSpcobsApplyDetails(CisSpcobsApplyDetailAssembler.toTos(cisSpcobsApply.getCisSpcobsApplyDetails()));
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisSpcobsApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisSpcobsApply.getCisOrderExecPlans()));
        }
        return to;
    }
}