package com.bjgoodwill.hip.ds.cis.apply.nursing.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.nursing.entity.CisNursingApply;
import com.bjgoodwill.hip.ds.cis.apply.nursing.to.CisNursingApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.nursing.to.CisNursingApplyTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisNursingApplyAssembler {

    public static List<CisNursingApplyTo> toTos(List<CisNursingApply> cisNursingApplys) {
        return toTos(cisNursingApplys, false);
    }

    public static List<CisNursingApplyTo> toTos(List<CisNursingApply> cisNursingApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisNursingApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisNursingApplys不能为空！");

        List<CisNursingApplyTo> tos = new ArrayList<>();
        for (CisNursingApply cisNursingApply : cisNursingApplys)
            tos.add(toTo(cisNursingApply, withAllParts));
        return tos;
    }

    public static CisNursingApplyTo toTo(CisNursingApply cisNursingApply) {
        return toTo(cisNursingApply, false);
    }

    /**
     * @generated
     */
    public static CisNursingApplyTo toTo(CisNursingApply cisNursingApply, boolean withAllParts) {
        if (cisNursingApply == null)
            return null;
        CisNursingApplyTo to = new CisNursingApplyTo();
        to.setId(cisNursingApply.getId());
        to.setPatMiCode(cisNursingApply.getPatMiCode());
        to.setVisitCode(cisNursingApply.getVisitCode());
        to.setServiceItemCode(cisNursingApply.getServiceItemCode());
        to.setServiceItemName(cisNursingApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisNursingApply.getIsCanPriorityFlag());
        to.setStatusCode(cisNursingApply.getStatusCode());
        to.setCreatedStaff(cisNursingApply.getCreatedStaff());
        to.setCreatedDate(cisNursingApply.getCreatedDate());
        to.setUpdatedStaff(cisNursingApply.getUpdatedStaff());
        to.setUpdatedDate(cisNursingApply.getUpdatedDate());
        to.setExecutorStaff(cisNursingApply.getExecutorStaff());
        to.setExecutorDate(cisNursingApply.getExecutorDate());
        to.setExecutorHosptialCode(cisNursingApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisNursingApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisNursingApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisNursingApply.getMedrecordExamabstractId());
        to.setVisitType(cisNursingApply.getVisitType());
        to.setDeptNurseCode(cisNursingApply.getDeptNurseCode());
        to.setDeptNurseName(cisNursingApply.getDeptNurseName());
        to.setOrderID(cisNursingApply.getOrderID());
        to.setHospitalCode(cisNursingApply.getHospitalCode());
        to.setPrescriptionID(cisNursingApply.getPrescriptionID());
        to.setIsPrint(cisNursingApply.getIsPrint());
        to.setPrintStaff(cisNursingApply.getPrintStaff());
        to.setPrintDate(cisNursingApply.getPrintDate());
        to.setReMark(cisNursingApply.getReMark());
        to.setIcuExecuteDate(cisNursingApply.getIcuExecuteDate());
        to.setIsChargeManager(cisNursingApply.getIsChargeManager());
        to.setVersion(cisNursingApply.getVersion());
        to.setCreateOrgCode(cisNursingApply.getCreateOrgCode());
        to.setSortNo(cisNursingApply.getSortNo());
        to.setIsBaby(cisNursingApply.getIsBaby());
//        to.setClinicalDiagnosis(cisNursingApply.getClinicalDiagnosis());
        to.setAutoFlag(cisNursingApply.getAutoFlag());
        to.setMutuallyExclusiveFlag(cisNursingApply.getMutuallyExclusiveFlag());
        to.setIsOlation(cisNursingApply.getIsOlation());
        to.setNum(cisNursingApply.getNum());
        to.setIsApply(cisNursingApply.getIsApply());
        to.setVisitOrgCode(cisNursingApply.getVisitOrgCode());
        to.setVisitOrgName(cisNursingApply.getVisitOrgName());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisNursingApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisNursingApply.getCisOrderExecPlans()));
        }
        return to;
    }

    public static CisNursingApplyNto toNto(CisNursingApply cisNursingApply, boolean withAllParts) {
        if (cisNursingApply == null)
            return null;
        CisNursingApplyNto to = new CisNursingApplyNto();
        to.setId(cisNursingApply.getId());
        to.setPatMiCode(cisNursingApply.getPatMiCode());
        to.setVisitCode(cisNursingApply.getVisitCode());
        to.setServiceItemCode(cisNursingApply.getServiceItemCode());
        to.setServiceItemName(cisNursingApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisNursingApply.getIsCanPriorityFlag());
        to.setExecutorOrgCode(cisNursingApply.getExecutorOrgCode());
//        to.setMedrecordExamabstractId(cisNursingApply.getMedrecordExamabstractId());
        to.setVisitType(cisNursingApply.getVisitType());
        to.setDeptNurseCode(cisNursingApply.getDeptNurseCode());
        to.setDeptNurseName(cisNursingApply.getDeptNurseName());
        to.setOrderID(cisNursingApply.getOrderID());
        to.setHospitalCode(cisNursingApply.getHospitalCode());
        to.setPrescriptionID(cisNursingApply.getPrescriptionID());
        to.setReMark(cisNursingApply.getReMark());
        to.setIcuExecuteDate(cisNursingApply.getIcuExecuteDate());
        to.setIsChargeManager(cisNursingApply.getIsChargeManager());
        to.setCreateOrgCode(cisNursingApply.getCreateOrgCode());
        to.setSortNo(cisNursingApply.getSortNo());
        to.setIsBaby(cisNursingApply.getIsBaby());
//        to.setClinicalDiagnosis(cisNursingApply.getClinicalDiagnosis());
        to.setAutoFlag(cisNursingApply.getAutoFlag());
        to.setMutuallyExclusiveFlag(cisNursingApply.getMutuallyExclusiveFlag());
        to.setExecutorOrgCode(cisNursingApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisNursingApply.getExecutorOrgName());
        to.setNum(cisNursingApply.getNum());
        to.setIsOlation(cisNursingApply.getIsOlation());
        to.setOrderType(cisNursingApply.getOrderType());
        to.setFrequency(cisNursingApply.getFrequency());
        to.setFrequencyName(cisNursingApply.getFrequencyName());
        to.setVisitOrgCode(cisNursingApply.getVisitOrgCode());
        to.setVisitOrgName(cisNursingApply.getVisitOrgName());
        to.setCreateOrgName(cisNursingApply.getCreateOrgName());
        to.setIsApply(cisNursingApply.getIsApply());
        if (withAllParts) {
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisNursingApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisNursingApply.getCisOrderExecPlans()));
        }
        return to;
    }

}