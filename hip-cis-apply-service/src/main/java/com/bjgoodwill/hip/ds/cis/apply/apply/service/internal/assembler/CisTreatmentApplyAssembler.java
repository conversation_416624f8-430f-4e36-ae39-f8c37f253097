package com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisTreatmentApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisTreatmentApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisTreatmentApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.diag.service.internal.assembler.ApplyDiagnosisAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;

import java.util.ArrayList;
import java.util.List;

public abstract class CisTreatmentApplyAssembler {

    public static List<CisTreatmentApplyTo> toTos(List<CisTreatmentApply> cisTreatmentApplys) {
        return toTos(cisTreatmentApplys, false);
    }

    public static List<CisTreatmentApplyTo> toTos(List<CisTreatmentApply> cisTreatmentApplys, boolean withAllParts) {
        BusinessAssert.notNull(cisTreatmentApplys, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisTreatmentApplys不能为空！");

        List<CisTreatmentApplyTo> tos = new ArrayList<>();
        for (CisTreatmentApply cisTreatmentApply : cisTreatmentApplys)
            tos.add(toTo(cisTreatmentApply, withAllParts));
        return tos;
    }

    public static CisTreatmentApplyTo toTo(CisTreatmentApply cisTreatmentApply) {
        return toTo(cisTreatmentApply, false);
    }

    /**
     * @generated
     */
    public static CisTreatmentApplyTo toTo(CisTreatmentApply cisTreatmentApply, boolean withAllParts) {
        if (cisTreatmentApply == null)
            return null;
        CisTreatmentApplyTo to = new CisTreatmentApplyTo();
        to.setId(cisTreatmentApply.getId());
        to.setPatMiCode(cisTreatmentApply.getPatMiCode());
        to.setVisitCode(cisTreatmentApply.getVisitCode());
        to.setServiceItemCode(cisTreatmentApply.getServiceItemCode());
        to.setServiceItemName(cisTreatmentApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisTreatmentApply.getIsCanPriorityFlag());
        to.setStatusCode(cisTreatmentApply.getStatusCode());
        to.setCreatedStaff(cisTreatmentApply.getCreatedStaff());
        to.setCreatedDate(cisTreatmentApply.getCreatedDate());
        to.setUpdatedStaff(cisTreatmentApply.getUpdatedStaff());
        to.setUpdatedDate(cisTreatmentApply.getUpdatedDate());
        to.setExecutorStaff(cisTreatmentApply.getExecutorStaff());
        to.setExecutorDate(cisTreatmentApply.getExecutorDate());
        to.setExecutorHosptialCode(cisTreatmentApply.getExecutorHosptialCode());
        to.setExecutorOrgCode(cisTreatmentApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisTreatmentApply.getExecutorOrgName());
//        to.setMedrecordExamabstractId(cisTreatmentApply.getMedrecordExamabstractId());
        to.setVisitType(cisTreatmentApply.getVisitType());
        to.setDeptNurseCode(cisTreatmentApply.getDeptNurseCode());
        to.setDeptNurseName(cisTreatmentApply.getDeptNurseName());
        to.setOrderID(cisTreatmentApply.getOrderID());
        to.setHospitalCode(cisTreatmentApply.getHospitalCode());
        to.setPrescriptionID(cisTreatmentApply.getPrescriptionID());
        to.setIsPrint(cisTreatmentApply.getIsPrint());
        to.setPrintStaff(cisTreatmentApply.getPrintStaff());
        to.setPrintDate(cisTreatmentApply.getPrintDate());
        to.setReMark(cisTreatmentApply.getReMark());
        to.setIcuExecuteDate(cisTreatmentApply.getIcuExecuteDate());
        to.setIsChargeManager(cisTreatmentApply.getIsChargeManager());
        to.setVersion(cisTreatmentApply.getVersion());
        to.setCreateOrgCode(cisTreatmentApply.getCreateOrgCode());
        to.setSortNo(cisTreatmentApply.getSortNo());
        to.setIsBaby(cisTreatmentApply.getIsBaby());
        to.setFrequency(cisTreatmentApply.getFrequency());
        to.setVisitOrgCode(cisTreatmentApply.getVisitOrgCode());
        to.setVisitOrgName(cisTreatmentApply.getVisitOrgName());
        to.setNum(cisTreatmentApply.getNum());
        to.setIsOlation(cisTreatmentApply.getIsOlation());
        to.setIsApply(cisTreatmentApply.getIsApply());
        if (withAllParts) {
            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisTreatmentApply.getCisApplyCharges()));
            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisTreatmentApply.getCisOrderExecPlans()));
            to.setApplyDiagnoses(ApplyDiagnosisAssembler.toTos(cisTreatmentApply.getApplyDiagnoses()));

        }
        return to;
    }

    public static CisTreatmentApplyNto toNto(CisTreatmentApply cisTreatmentApply, boolean withAllParts) {
        if (cisTreatmentApply == null)
            return null;
        CisTreatmentApplyNto to = new CisTreatmentApplyNto();
        to.setId(cisTreatmentApply.getId());
        to.setPatMiCode(cisTreatmentApply.getPatMiCode());
        to.setVisitCode(cisTreatmentApply.getVisitCode());
        to.setServiceItemCode(cisTreatmentApply.getServiceItemCode());
        to.setServiceItemName(cisTreatmentApply.getServiceItemName());
        to.setIsCanPriorityFlag(cisTreatmentApply.getIsCanPriorityFlag());
//        to.setMedrecordExamabstractId(cisTreatmentApply.getMedrecordExamabstractId());
        to.setVisitType(cisTreatmentApply.getVisitType());
        to.setDeptNurseCode(cisTreatmentApply.getDeptNurseCode());
        to.setDeptNurseName(cisTreatmentApply.getDeptNurseName());
        to.setOrderID(cisTreatmentApply.getOrderID());
        to.setHospitalCode(cisTreatmentApply.getHospitalCode());
        to.setPrescriptionID(cisTreatmentApply.getPrescriptionID());
//        to.setIsPrint(cisTreatmentApply.getIsPrint());
//        to.setPrintStaff(cisTreatmentApply.getPrintStaff());
//        to.setPrintDate(cisTreatmentApply.getPrintDate());
        to.setReMark(cisTreatmentApply.getReMark());
        to.setIcuExecuteDate(cisTreatmentApply.getIcuExecuteDate());
        to.setIsChargeManager(cisTreatmentApply.getIsChargeManager());
        to.setCreateOrgCode(cisTreatmentApply.getCreateOrgCode());
        to.setSortNo(cisTreatmentApply.getSortNo());
        to.setIsBaby(cisTreatmentApply.getIsBaby());
        to.setFrequency(cisTreatmentApply.getFrequency());
        to.setVisitOrgCode(cisTreatmentApply.getVisitOrgCode());
        to.setOrderType(cisTreatmentApply.getOrderType());
        to.setVisitOrgName(cisTreatmentApply.getVisitOrgName());
        to.setCreateOrgName(cisTreatmentApply.getCreateOrgName());
        to.setExecutorOrgCode(cisTreatmentApply.getExecutorOrgCode());
        to.setExecutorOrgName(cisTreatmentApply.getExecutorOrgName());
        to.setNum(cisTreatmentApply.getNum());
        to.setIsOlation(cisTreatmentApply.getIsOlation());
        to.setIsApply(cisTreatmentApply.getIsApply());
        if (withAllParts) {
//            to.setCisApplyCharges(CisApplyChargeAssembler.toTos(cisTreatmentApply.getCisApplyCharges()));
//            to.setCisOrderExecPlans(CisOrderExecPlanAssembler.toTos(cisTreatmentApply.getCisOrderExecPlans()));
        }
        return to;
    }

}