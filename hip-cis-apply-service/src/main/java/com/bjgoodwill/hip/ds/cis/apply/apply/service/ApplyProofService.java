package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisBaseApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyQto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisProofQto;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.entity.CisOrderExecPlan;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.internal.assembler.CisOrderExecPlanAssembler;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanTo;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-12 09:11
 * @className: ProofService
 * @description: 申请单校对 服务
 **/
@Component
public class ApplyProofService {

    /**
     * 根据订单ID列表获取校对停止状态的CIS基础申请信息。
     * 此方法用于查询一组订单对应的CIS基础申请信息，并结合这些订单的停止校对时间，
     * 筛选出执行计划中停止时间在预计执行时间之前的计划。
     *
     * @param qtos 查询条件列表，包含订单ID和停止时间。
     * @return 包含校对停止状态信息的CIS基础申请列表。
     */
    //停止校对申请单和费用查询
    public List<CisBaseApplyTo> getProofStopCisBaseApplyByOrderIds(List<CisProofQto> qtos) {
        // 断言查询条件列表不为空，否则抛出业务异常
        BusinessAssert.notEmpty(qtos, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "查询条件");
        // 从查询条件列表中提取所有订单ID
        List<String> orderIds = qtos.stream().map(CisProofQto::getOrderid).toList();

        // 根据订单ID列表查询所有的CIS基础申请信息
        List<CisBaseApplyTo> cisBaseApplies = CisBaseApplyAssembler.toTos(CisBaseApply.findCisBaseAppliesByOrderIDIn(orderIds));
        BusinessAssert.notEmpty(cisBaseApplies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");

        // 根据订单ID列表查询所有的执行计划信息
        List<CisOrderExecPlan> cisOrderExecPlans = CisOrderExecPlan.findCisOrderExecPlansByOrderIdIn(orderIds);
        // 筛选出停止时间在预计执行时间之前的执行计划，并按CIS基础申请ID分组
        //找出停止时间在预计时间之前的执行计划
        Map<String, List<CisOrderExecPlanTo>> cisOrderExecPlansMap = cisOrderExecPlans.stream().filter(p ->
                        qtos.stream().anyMatch(q -> q.getOrderid().equals(p.getOrderId())
                                && q.getStopDate().isBefore(p.getExecPlanDate())
                        )).map(CisOrderExecPlanAssembler::toTo)
                .collect(Collectors.groupingBy(CisOrderExecPlanTo::getCisBaseApplyId));

        // 将筛选后的执行计划信息设置到对应的CIS基础申请对象中
        cisBaseApplies.forEach(p -> p.setCisOrderExecPlans(cisOrderExecPlansMap.get(p.getId())));
        // 返回处理后的CIS基础申请列表,直接处理，没有执行和没有计费的申请单。返回有执行和计费的，给页面处理。
        return proofStop(cisBaseApplies);
    }


    /**
     * 校验并处理停止申请列表。
     * 该方法旨在从停止申请列表中筛选出不能停止的申请，并对可以停止的申请对应的执行计划做标记为过时的操作。
     *
     * @param stopApplys 停止申请列表，包含多个申请项。
     * @return 不能停止的申请列表。
     */
    private List<CisBaseApplyTo> proofStop(List<CisBaseApplyTo> stopApplys) {
        // 筛选出不能停止的申请项，并保存到canNotStop列表中。
        List<CisBaseApplyTo> canNotStop = stopApplys.stream()
                .filter(p -> isStop(p.getCisOrderExecPlans()))
                .collect(Collectors.toList());

        // 使用removeIf直接在原列表上移除不能停止的申请项。
        stopApplys.removeIf(p -> isStop(p.getCisOrderExecPlans()));

        // 遍历可以停止的申请项，对每个申请项中的执行计划做标记为过时的操作。
        // 注意：这里需要确保线程安全和异常处理，特别是数据库操作。
        stopApplys.forEach(p -> markExecPlansAsObsolete(p.getCisOrderExecPlans()));

        return canNotStop;
    }

    /**
     * 对给定的执行计划列表做标记为过时的操作。
     *
     * @param orderExecPlans 执行计划列表。
     */
    private void markExecPlansAsObsolete(List<CisOrderExecPlanTo> orderExecPlans) {
        // 避免空指针异常，对输入进行空检查
        if (orderExecPlans != null) {
            // 对执行计划进行批处理或事务处理以提高性能，这里假设进行单个处理
            orderExecPlans.forEach(o -> {
                // 使用Optional进行链式调用，处理数据库操作异常等
                Optional<CisOrderExecPlan> cisOrderExecPlanOptional = CisOrderExecPlan.getCisOrderExecPlanById(o.getId());
                cisOrderExecPlanOptional.ifPresent(CisOrderExecPlan::obsolete);
            });
        }
    }

    private Boolean isStop(List<CisOrderExecPlanTo> ordersPlans) {
        return ordersPlans.stream().anyMatch(p -> CisStatusEnum.COMPLETED.equals(p.getStatusCode())
                || p.getIsCharge());
    }


//    public List<CisBaseApplyTo> getObsoleteCisBaseApplyByOrderIds(List<CisProofQto> qtos) {
//        return getProofStopCisBaseApplyByOrderIds(qtos);
//    }

    /**
     * 标记一个订单为过时。
     * 此方法首先根据订单ID查询相应的申请单，然后检查该申请单是否包含收费的执行计划。
     * 如果申请单存在且包含收费的执行计划，则将该申请单标记为过时。
     *
     * @param orderId 订单ID，用于查询对应的申请单。
     */
    public void preObsolete(String orderId) {
        // 创建查询条件对象，并设置订单ID。
        CisBaseApplyQto qto = new CisBaseApplyQto();
        qto.setOrderID(orderId);

        // 根据订单ID查询申请单，并获取第一条记录。
        CisBaseApply cisBaseApplie = CisBaseApply.getCisBaseApplies(qto).stream().findFirst().orElse(null);

        // 断言申请单不为空，如果为空则抛出业务异常。
        BusinessAssert.notNull(cisBaseApplie, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");

        // 将申请单转换为对应的传输对象。
        CisBaseApplyTo cisBaseApplyTo = CisBaseApplyAssembler.toTo(cisBaseApplie, true);

        // 断言申请单中存在收费的执行计划，如果不存在则抛出业务异常。
        BusinessAssert.isTrue(
                cisBaseApplyTo.getCisOrderExecPlans().stream().anyMatch(p -> p.getIsCharge()),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00012, "申请单");

        // 标记申请单为过时。
        cisBaseApplie.preObsolete();
    }


    /**
     * 标记已过期的申请单和相应的执行计划。
     * 此方法首先筛选出与指定订单ID相关的申请单，然后标记这些申请单及其执行计划为过期。
     *
     * @param applies  申请单列表，这些申请单将被检查是否与指定的订单ID相关。
     * @param orderIds 指定的订单ID列表，用于筛选需要标记为过期的申请单。
     */
    public void proofObsolete(List<CisBaseApply> applies, List<String> orderIds) {
        // 筛选出与指定订单ID相关的申请单
        applies = applies.stream().filter(p -> orderIds.contains(p.getOrderID())).toList();

        // 断言筛选后的申请单列表不为空，如果为空则抛出业务异常
        BusinessAssert.notEmpty(applies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");

        // 标记所有筛选出的申请单为过期
        applies.forEach(CisBaseApply::obsolete);

        // 遍历所有筛选出的申请单，进一步标记它们的执行计划为过期
        applies.stream().flatMap(p -> p.getCisOrderExecPlans().stream()).toList().forEach(CisOrderExecPlan::obsolete);
    }


    /**
     * 根据订单ID列表和基础申请列表，验证并处理订单执行计划。
     * 此方法首先通过将订单ID列表转换为HashSet来优化查找性能，然后筛选出与订单ID匹配的基础申请。
     * 对匹配的基础申请进行处理，转换为CisSplitConversion对象列表，并进一步调用外部服务进行处理。
     *
     * @param orderIds 订单ID列表，用于筛选基础申请。
     * @param applies  基础申请列表，包含所有待处理的申请。
     * @return 返回处理后的订单执行计划列表。
     */
    public List<CisBaseApplyTo> proofCisBaseApplys(List<String> orderIds, List<CisBaseApplyNto> applies, Boolean save) {
        // 将订单ID列表转换为HashSet，以优化后续的查找性能
        // 优化1: 提高性能，将orderIds转换为HashSet以改善查找性能
        Set<String> orderIdsSet = new HashSet<>(orderIds);

        // 通过流操作对基础申请列表进行筛选和映射，只保留订单ID在orderIdsSet中的申请，
        // 并将每个申请转换为CisSplitConversion对象
        // 优化2: 使用流操作结合方法引用提高代码可读性，并收集结果到List中
        List<CisSplitConversion> cisSplitConversions = applies.stream()
                .filter(orderIdsSet::contains) // 使用方法引用改善可读性
                .map(o -> new CisSplitConversion(o)) // 假设构造函数接受null值是合理的，建议在文档中注明
                .collect(Collectors.toList());

        // 调用临时拆分服务处理转换后的CisSplitConversion列表，并返回处理结果
        // 未显示processProofs方法的实现，但建议在此处添加异常处理逻辑
        try {
            TempprarysplitService tempprarysplitService = SpringUtil.getBean(TempprarysplitService.class);
            return tempprarysplitService.processSplits(cisSplitConversions, save);
        } catch (Exception e) {
            // 异常处理：打印异常堆栈跟踪，并返回空列表
            // 根据实际情况处理异常，例如记录日志、返回空列表或特定错误列表等
            // 此处仅为示例，具体实现应根据实际需求决定
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
}