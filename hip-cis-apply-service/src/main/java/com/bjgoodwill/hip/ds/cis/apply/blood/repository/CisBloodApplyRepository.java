package com.bjgoodwill.hip.ds.cis.apply.blood.repository;

import com.bjgoodwill.hip.ds.cis.apply.blood.entity.CisBloodApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.apply.blood.repository.CisBloodApplyRepository")
public interface CisBloodApplyRepository extends JpaRepository<CisBloodApply, String>, JpaSpecificationExecutor<CisBloodApply> {

}