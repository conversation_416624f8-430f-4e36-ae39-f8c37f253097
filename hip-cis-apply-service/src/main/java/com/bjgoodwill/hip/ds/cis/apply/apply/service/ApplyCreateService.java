package com.bjgoodwill.hip.ds.cis.apply.apply.service;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.validation.ConOrderLimit;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.internal.assembler.CisBaseApplyAssembler;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.charge.entity.CisApplyCharge;
import com.bjgoodwill.hip.ds.cis.apply.charge.service.internal.assembler.CisApplyChargeAssembler;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.entity.CisDgimgApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.service.internal.assembler.CisDgimgApplyDetailAssembler;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.entity.CisDrugApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.drug.service.internal.assembler.CisDrugApplyDetailAssembler;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisBaseDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.proxy.ApplyCodeServiceProxy;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.entity.CisSpcobsApplyDetail;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.service.internal.assembler.CisSpcobsApplyDetailAssembler;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyNto;
import com.bjgoodwill.hip.ds.cis.rule.cisItemFlow.enums.CisRuleBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DetailTypeEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.enmus.DocNurseTypeEnum;
import com.bjgoodwill.hip.ds.cis.rule.confOrderLimit.to.CheckCisConfOrderLimitQto;
import com.bjgoodwill.hip.ds.pat.index.service.PatIndexService;
import com.bjgoodwill.hip.ds.pat.index.to.PatIndexTo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @program: HIP5.0-CIS
 * @author: xdguo
 * @create: 2024-07-23 16:01
 * @className: ApplyCreateService
 * @description:
 **/
@Component
public class ApplyCreateService {


    @Autowired
    protected ApplyCodeServiceProxy applyCodeServiceProxy;
    @Autowired
    private PatIndexService patIndexService;

    private ApplyCreateService getInstance() {
        return SpringUtil.getBean(ApplyCreateService.class);
    }

    public List<CisBaseApplyNto> getProofApplys(String visitCode, List<String> orderIds) {
        // 验证参数
        validateParameters(orderIds, visitCode);

        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        List<CisBaseApply> applies = getCisBaseApplysByNurseDeptCode(visitCode, CisStatusEnum.ACTIVE);

        applies = cisBaseApplysByOrderId(orderIds, applies);
        // 确保查询到的cis基础申请单不为空
        BusinessAssert.notEmpty(applies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");

        //过滤出临时医嘱
        applies = applies.stream().filter(p -> OrderTypeEnum.TEMPORARY_ORDER.equals(p.getOrderType())).toList();

        if (CollectionUtils.isEmpty(applies)) {
            return Collections.emptyList();
        }

        //region 创建申请单和申请单明细
        List<CisBaseApplyNto> cisBaseApplyTos = CisBaseApplyAssembler.toNtos(applies, true);
        setChargeNto(cisBaseApplyTos);
        //endregion

        return cisBaseApplyTos;
    }

    public List<CisBaseApplyNto> getSplitApplys(String nurseDeptCode, List<String> orderIds) {
        // 验证参数
        validateParameters(orderIds, nurseDeptCode);

        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        List<CisBaseApply> applies = getCisBaseApplysByNurseDeptCode(nurseDeptCode, CisStatusEnum.PASS);

        applies = cisBaseApplysByOrderId(orderIds, applies);
        // 确保查询到的cis基础申请单不为空
        BusinessAssert.notEmpty(applies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");

        //过滤出临时医嘱
        applies = applies.stream().filter(p -> OrderTypeEnum.LONG_TERM_ORDER.equals(p.getOrderType())).toList();

        if (CollectionUtils.isEmpty(applies)) {
            return Collections.emptyList();
        }

        //region 创建申请单和申请单明细
        List<CisBaseApplyNto> cisBaseApplyTos = CisBaseApplyAssembler.toNtos(applies, true);
        setChargeNto(cisBaseApplyTos);
        //endregion

        return cisBaseApplyTos;
    }

    public List<CisBaseApplyNto> createcisBaseApplysNewByOrderId(String visitCode, List<String> orderIds) {
        // 验证参数
        validateParameters(orderIds, visitCode);

        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        List<CisBaseApply> applies = getCisBaseApplysByVisitCode(visitCode, CisStatusEnum.CLONE);

        applies = cisBaseApplysByOrderId(orderIds, applies);
        // 确保查询到的cis基础申请单不为空
        BusinessAssert.notEmpty(applies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");

        //region 临时的申请单 只能拆分一次
        verifyTemporayProof(applies);
        //endregion

        //region 创建申请单和申请单明细
        List<CisBaseApplyNto> cisBaseApplyTos = CisBaseApplyAssembler.toNtos(applies, true);
        setDgimgApplyDetailNto(visitCode, cisBaseApplyTos);
        setSpcobsApplyDetailNto(visitCode, cisBaseApplyTos);
        setDrugApplyDetailNto(visitCode, cisBaseApplyTos);
        setChargeNto(visitCode, cisBaseApplyTos, CisStatusEnum.CLONE);

        //endregion

        return cisBaseApplyTos;
    }

    public List<CisBaseApplyNto> createcisBaseApplysNewByOrderIdProof(String visitCode, List<String> orderIds) {
        // 验证参数
        validateParameters(orderIds, visitCode);

        // 根据访问码查询cis基础申请单，筛选出状态为克隆的申请单
        List<CisBaseApply> applies = getCisBaseApplysByVisitCodeAndStatusCodes(visitCode, Arrays.asList(CisStatusEnum.ACTIVE, CisStatusEnum.PASS, CisStatusEnum.COMPLETED));

        applies = cisBaseApplysByOrderId(orderIds, applies);
        // 确保查询到的cis基础申请单不为空
        BusinessAssert.notEmpty(applies, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0009, "申请单");

        //region 临时的申请单 只能拆分一次
        verifyTemporayProof(applies);
        //endregion

        //region 创建申请单和申请单明细
        List<CisBaseApplyNto> cisBaseApplyTos = CisBaseApplyAssembler.toNtos(applies, true);
        setDgimgApplyDetailNtoProof(visitCode, cisBaseApplyTos);
        setSpcobsApplyDetailNto(visitCode, cisBaseApplyTos);
        setDrugApplyDetailNto(visitCode, cisBaseApplyTos);
        setChargeNto(visitCode, cisBaseApplyTos, CisStatusEnum.ACTIVE);

        //endregion

        return cisBaseApplyTos;
    }

    private void verifyTemporayProof(List<CisBaseApply> applies) {
        List<String> ids = applies.stream().filter(p -> p.getOrderType().equals(OrderTypeEnum.TEMPORARY_ORDER))
                .map(CisBaseApply::getOrderID).toList();
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        List<CisBaseApply> cisBaseApplies = CisBaseApply.findCisBaseAppliesByOrderIDIn(ids).stream()
                .filter(p -> CisStatusEnum.PASS.equals(p.getStatusCode())).toList();
        BusinessAssert.isTrue(CollectionUtils.isEmpty(cisBaseApplies), CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00010,
                cisBaseApplies.stream().map(CisBaseApply::getId).toList(),
                "重复");
    }


//    private Map<String, List<CisBaseApplyNto>> groupByMinimalClass(List<CisBaseApplyNto> entitys) {
//        return entitys.stream()
//                .collect(Collectors.groupingBy(CisBaseApplyNto::getMinimal_class));
//    }


    //region 查询备份数据，拼插入数据
    private void setDgimgApplyDetailNto(String visitCode, List<CisBaseApplyNto> cisBaseApplyTos) {
        var dgimgApplyTos = cisBaseApplyTos.stream().filter(CisDgimgApplyNto.class::isInstance)
                .map(CisDgimgApplyNto.class::cast).toList();

        if (!CollectionUtils.isEmpty(dgimgApplyTos)) {
            // 根据访问代码查询DGIMG申请详细信息，并按申请ID分组。
            Map<String, List<CisDgimgApplyDetail>> dgimgApplyDetailMap =
                    CisDgimgApplyDetail.findCisDgimgApplyDetailsByVisitCode(visitCode).stream()
                            .filter(p -> CisStatusEnum.CLONE.equals(p.getStatusCode()))
                            .collect(Collectors.groupingBy(CisDgimgApplyDetail::getApplyId));
            // 为每个DGIMG申请填充详细信息。

            dgimgApplyDetailMap.forEach((applyId, details) ->
                    Optional.ofNullable(dgimgApplyTos.stream()
                                    .filter(applyTo -> applyTo.getId().equals(applyId))
                                    .findFirst()
                                    .orElse(null))
                            .ifPresent(applyTo ->
                                    applyTo.setDetails(CisDgimgApplyDetailAssembler.toNtos(details))));

        }
    }

    private void setDgimgApplyDetailNtoProof(String visitCode, List<CisBaseApplyNto> cisBaseApplyTos) {
        var dgimgApplyTos = cisBaseApplyTos.stream().filter(CisDgimgApplyNto.class::isInstance)
                .map(CisDgimgApplyNto.class::cast).toList();

        if (!CollectionUtils.isEmpty(dgimgApplyTos)) {
            // 根据访问代码查询DGIMG申请详细信息，并按申请ID分组。
            Map<String, List<CisDgimgApplyDetail>> dgimgApplyDetailMap =
                    CisDgimgApplyDetail.findCisDgimgApplyDetailsByVisitCode(visitCode).stream()
                            .filter(p -> CisStatusEnum.ACTIVE.equals(p.getStatusCode()))
                            .collect(Collectors.groupingBy(CisDgimgApplyDetail::getApplyId));
            // 为每个DGIMG申请填充详细信息。

            dgimgApplyDetailMap.forEach((applyId, details) ->
                    Optional.ofNullable(dgimgApplyTos.stream()
                                    .filter(applyTo -> applyTo.getId().equals(applyId))
                                    .findFirst()
                                    .orElse(null))
                            .ifPresent(applyTo ->
                                    applyTo.setDetails(CisDgimgApplyDetailAssembler.toNtos(details))));

        }
    }

    private void setChargeNto(String visitCode, List<CisBaseApplyNto> cisBaseApplyTos, CisStatusEnum status) {
        List<String> ids = cisBaseApplyTos.stream().map(CisBaseApplyNto::getId).toList();
        List<CisApplyCharge> cisApplyCharges = CisApplyCharge.findCisApplyChargesByVisitCode(visitCode, status, ids);

        Map<String, List<CisApplyCharge>> map = cisApplyCharges.stream().collect(Collectors.groupingBy(CisApplyCharge::getOrderId));
        cisBaseApplyTos.stream().forEach(p -> p.setCisApplyCharges(CisApplyChargeAssembler.toNtos(map.get(p.getOrderID()))));
    }

    private void setChargeNto(List<CisBaseApplyNto> cisBaseApplyTos) {
        List<String> ids = cisBaseApplyTos.stream().map(CisBaseApplyNto::getId).toList();
        List<CisApplyCharge> cisApplyCharges = CisApplyCharge.findCisApplyChargesByApplyIdIn(ids);

        Map<String, List<CisApplyCharge>> map = cisApplyCharges.stream().collect(Collectors.groupingBy(CisApplyCharge::getOrderId));
        cisBaseApplyTos.stream().forEach(p -> p.setCisApplyCharges(CisApplyChargeAssembler.toNtos(map.get(p.getOrderID()))));
    }

    private void setSpcobsApplyDetailNto(String visitCode, List<CisBaseApplyNto> cisBaseApplyTos) {
        var spcobsApplyTos = cisBaseApplyTos.stream().filter(CisSpcobsApplyNto.class::isInstance).map(CisSpcobsApplyNto.class::cast).toList();
        if (!CollectionUtils.isEmpty(spcobsApplyTos)) {
            // 根据访问代码查询SPCOBS申请详细信息，并按申请ID分组。
            Map<String, List<CisSpcobsApplyDetail>> spcobsApplyDetailMap =
                    CisSpcobsApplyDetail.findCisSpcobsApplyDetailsByVisitCode(visitCode).stream()
                            .collect(Collectors.groupingBy(CisSpcobsApplyDetail::getApplyId));
            // 为每个SPCOBS申请填充详细信息。
            spcobsApplyDetailMap.forEach((applyId, details) -> Optional.ofNullable(spcobsApplyTos.stream()
                            .filter(applyTo -> applyTo.getId().equals(applyId))
                            .findFirst()
                            .orElse(null))
                    .ifPresent(applyTo ->
                            applyTo.setDetails(CisSpcobsApplyDetailAssembler.toNtos(details))));
        }
    }

    private void setDrugApplyDetailNto(String visitCode, List<CisBaseApplyNto> cisBaseApplyTos) {
        var drugApplyTos = cisBaseApplyTos.stream().filter(CisBaseDrugApplyNto.class::isInstance).map(CisBaseDrugApplyNto.class::cast).toList();
        if (!CollectionUtils.isEmpty(drugApplyTos)) {
            // 根据访问代码查询SPCOBS申请详细信息，并按申请ID分组。
            Map<String, List<CisDrugApplyDetail>> drugApplyDetailMap =
                    CisDrugApplyDetail.findCisDrugApplyDetailsByVisitCode(visitCode).stream()
                            .collect(Collectors.groupingBy(CisDrugApplyDetail::getApplyId));
            // 为每个SPCOBS申请填充详细信息。
            drugApplyDetailMap.forEach((applyId, details) ->
                    Optional.ofNullable(drugApplyTos.stream()
                                    .filter(applyTo -> applyTo.getId().equals(applyId))
                                    .findFirst()
                                    .orElse(null))
                            .ifPresent(applyTo ->
                                    applyTo.setDetails(CisDrugApplyDetailAssembler.toNtos(details))));
        }
    }
    //endregion

    private void validateParameters(List<String> orderIds, String visitCode) {
        BusinessAssert.notEmpty(orderIds, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "医嘱号");
        BusinessAssert.hasText(visitCode, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "流水号");
    }

    private List<CisBaseApply> getCisBaseApplysByVisitCode(String visitCode, CisStatusEnum statusEnum) {
        return CisBaseApply.findCisBaseAppliesByVisitCodeAndStatusCode(visitCode, statusEnum)
                .stream()
                .toList();
    }

    private List<CisBaseApply> getCisBaseApplysByVisitCodeAndStatusCodes(String visitCode, List<CisStatusEnum> statusEnums) {
        return CisBaseApply.findCisBaseAppliesByVisitCodeAndStatusCodeIn(visitCode, statusEnums)
                .stream()
                .toList();
    }

    private List<CisBaseApply> getCisBaseApplysByNurseDeptCode(String nurseDeptCode, CisStatusEnum statusEnum) {
        return CisBaseApply.findCisBaseAppliesByDeptNurseCodeAnAndStatusCode(nurseDeptCode, statusEnum)
                .stream()
                .toList();
    }

    private List<CisBaseApply> cisBaseApplysByOrderId(List<String> orderIds, List<CisBaseApply> applies) {
        //region 数据校验
        // 使用集合差异比较订单ID列表和申请单的订单ID列表，以校验是否所有订单ID都有对应的申请单
        HashSet<String> set1 = new HashSet<>(orderIds);
        HashSet<String> set2 = new HashSet<>(applies.stream().map(p -> p.getOrderID()).toList());
        set1.remove(set2);
        // 如果差异集合不为空，表示有订单ID没有对应的申请单，抛出异常
        BusinessAssert.isTrue(set1.size() > 0, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0008, set1);
        //endregion

        // 筛选出与订单ID列表匹配的cis基础申请单
        return applies.stream().filter(p -> orderIds.contains(p.getOrderID())).toList();
    }

    public List<CisBaseApply> save(List<CisBaseApplyNto> entitys) {
        return create(entitys, CisStatusEnum.NEW);
    }

    public List<CisBaseApply> saveAndSubmit(List<CisBaseApplyNto> entitys) {
        return create(entitys, CisStatusEnum.ACTIVE);
    }

    public void clone(List<CisBaseApplyNto> entitys) {
        create(entitys, CisStatusEnum.CLONE);
    }

    public List<CisBaseApply> create(List<CisBaseApplyNto> entitys, CisStatusEnum statusEnum) {
//        getInstance().validate(changeConfOrderLimitQtos(entitys));

        Map<String, List<CisBaseApplyNto>> maps = entitys.stream()
                .collect(Collectors.groupingBy(CisBaseApplyNto::getAll_class));
        return processApplyGroups(maps, statusEnum);
    }

    //医护限制维护校验入口
    @ConOrderLimit
    public void validate(List<CheckCisConfOrderLimitQto> entitys) {
    }


    private List<CheckCisConfOrderLimitQto> changeConfOrderLimitQtos(List<CisBaseApplyNto> entitys) {
        if (CollectionUtils.isEmpty(entitys)) {
            return null;
        }

        PatIndexTo patIndexTo = patIndexService.getPatIndexById(entitys.get(0).getPatMiCode());
        BusinessAssert.notNull(patIndexTo, CisRuleBusinessErrorEnum.BUS_CIS_RULE_0001, "患者信息");
        List<CheckCisConfOrderLimitQto> qtos = new ArrayList<>();

        CheckCisConfOrderLimitQto qtodrug = new CheckCisConfOrderLimitQto();
        setPatIndex(patIndexTo, qtodrug);
        qtodrug.setDetailType(DetailTypeEnum.DRUG);
        List<String> drugcodes = new ArrayList<>();
        entitys.stream().filter(p -> p instanceof CisBaseDrugApplyNto)
                .forEach(p ->
                        drugcodes.addAll(
                                ((CisBaseDrugApplyNto) p).getDetails().stream().map(d -> d.getDrugCode()).toList()
                        )
                );
        qtodrug.setCodes(drugcodes);
        qtos.add(qtodrug);


        CheckCisConfOrderLimitQto qtoSys = new CheckCisConfOrderLimitQto();
        setPatIndex(patIndexTo, qtoSys);
        qtoSys.setDetailType(DetailTypeEnum.SystemType);
        List<String> syscodes = new ArrayList<>();
        syscodes.addAll(entitys.stream().map(p -> CisBaseApply.newInstanceByNto(p).getOrderClass()).toList());
        qtoSys.setCodes(syscodes);


        CheckCisConfOrderLimitQto qtoItem = new CheckCisConfOrderLimitQto();
        setPatIndex(patIndexTo, qtoItem);
        qtoItem.setDetailType(DetailTypeEnum.ITEMCODE);
        List<String> itemcodes = entitys.stream().filter(p -> !(p instanceof CisBaseDrugApplyNto)).map(o -> o.getServiceItemCode()).toList();
        qtodrug.setCodes(itemcodes);

        qtos.add(qtoItem);
        return qtos;
    }

    private void setPatIndex(PatIndexTo patIndexTo, CheckCisConfOrderLimitQto qto) {
        qto.setBirthday(patIndexTo.getBirthDate());
        qto.setSex(patIndexTo.getSex());
        qto.setDocNurseType(DocNurseTypeEnum.DOC);

    }


    private List<CisBaseApply> processApplyGroups(Map<String, List<CisBaseApplyNto>> maps, CisStatusEnum statusEnum) {
        // 输入验证
//        if (maps == null) {
//            throw new IllegalArgumentException("Input maps cannot be null.");
//        }
        BusinessAssert.notEmpty(maps, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "申请单");

        // 使用ConcurrentHashMap来避免潜在的线程安全问题
        ConcurrentHashMap<String, List<CisBaseApplyNto>> safeMaps = new ConcurrentHashMap<>(maps);

        // 使用forEach来遍历分组后的申请单Map
        return safeMaps.values().stream().map(p -> processApplyGroup(p, statusEnum))
                .flatMap(List::stream).toList();
    }

    /**
     * 处理申请分组列表。
     * 该方法通过遍历申请列表，尝试为每个申请创建相应的对象。如果在创建过程中遇到异常，
     * 则捕获异常并处理，确保不影响其他申请的处理。如果整个处理过程中发生异常，
     * 则将异常抛出，由上层调用者处理。
     *
     * @param applyList 申请列表，包含待处理的申请项。
     */
    private List<CisBaseApply> processApplyGroup(List<CisBaseApplyNto> applyList, CisStatusEnum statusEnum) {
        // 检查申请列表是否为空，非空则进行处理
        if (!applyList.isEmpty()) {
            List<CisBaseApply> lst = new ArrayList<>();
            applyList.forEach(e -> {
                // 从列表中获取第一个申请项，用于创建申请对象
                CisBaseApply apply = CisBaseApply.newInstanceByNto(applyList.get(0));
                apply.setStatusCode(statusEnum);
                e.setId(applyCodeServiceProxy.getApplyNextCode());
                // 尝试为当前申请项创建对象
                lst.add(apply.create(e, true));
            });
            return lst;
        }
        return Collections.emptyList();
    }

}