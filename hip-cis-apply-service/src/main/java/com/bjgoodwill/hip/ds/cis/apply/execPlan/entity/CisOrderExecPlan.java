package com.bjgoodwill.hip.ds.cis.apply.execPlan.entity;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.drug.enums.DrugIpdDataStatusEnum;
import com.bjgoodwill.hip.business.util.econ.enums.SetlStasEnum;
import com.bjgoodwill.hip.business.util.mq.to.cis.CisOrderExecPlanReceiveChargeEto;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.entity.CisBaseApply;
import com.bjgoodwill.hip.ds.cis.apply.apply.enums.CisApplyBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.repository.CisOrderExecPlanRepository;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.*;
import com.bjgoodwill.hip.jpa.core.SnowflakeIdGenerator;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Entity
@Comment(value = "医嘱执行档")
@Table(name = "cis_order_exec_plan", indexes = {
        @Index(name = "cis_order_exec_plan_idx_visitCode", columnList = "visit_code"),
        @Index(name = "cis_order_exec_plan_apply_id", columnList = "apply_id"),
        @Index(name = "cis_order_exec_plan_order_id", columnList = "order_id"),
        @Index(name = "cis_order_exec_plan_created_date", columnList = "created_date"),
        @Index(name = "cis_order_exec_plan_dept_nurse_code", columnList = "dept_nurse_code")
})
public class CisOrderExecPlan {

    // 基础信息字段
    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    @GeneratedValue(generator = "snowflake_generator")
    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    private String id;

    @Comment("主索引")
    @Column(name = "pat_mi_code", nullable = false)
    private String patMiCode;

    @Comment("就诊流水号")
    @Column(name = "visit_code", nullable = false)
    private String visitCode;

    // 科室相关字段
    @Comment("开方科室编码")
    @Column(name = "org_code", nullable = false)
    private String orgCode;

    @Comment("开方科室名称")
    @Column(name = "org_name", nullable = false)
    private String orgName;

    @Comment("护理组号")
    @Column(name = "dept_nurse_code", nullable = true)
    private String deptNurseCode;

    // 医嘱相关字段
    @Comment("医嘱号")
    @Column(name = "order_id", nullable = false)
    private String orderId;

    @Comment("抽象父类标识")
    @Column(name = "apply_id", nullable = false, length = 50)
    private String cisBaseApplyId;

    @Comment("医嘱序号")
    @Column(name = "sort_no", nullable = true)
    private Double sortNo;

    @Comment("医嘱项目编码")
    @Column(name = "service_item_code", nullable = true)
    private String serviceItemCode;

    @Comment("医嘱项目名称")
    @Column(name = "service_item_name", nullable = true)
    private String serviceItemName;

    @Enumerated(EnumType.STRING)
    @Comment("医嘱类型")
    @Column(name = "order_class", nullable = true)
    private SystemTypeEnum orderClass;

    // 皮试相关字段
    @Comment("是否皮试")
    @Column(name = "is_skin", nullable = true)
    private Boolean isSkin;

    @Comment("皮试结果")
    @Column(name = "skin_result", nullable = true)
    private String skinResult;

    // 执行相关字段
    @Comment("领药科室")
    @Column(name = "receive_org", nullable = true)
    private String receiveOrg;

    @Comment("领药科室名称")
    @Column(name = "receive_org_name", nullable = true)
    private String receiveOrgName;

    @Comment("医嘱预计执行时间")
    @Column(name = "exec_plan_date", nullable = true)
    private LocalDateTime execPlanDate;

    @Comment("执行时间")
    @Column(name = "exec_date", nullable = true)
    private LocalDateTime execDate;

    @Comment("执行人")
    @Column(name = "exec_staff", nullable = true)
    private String execStaff;

    @Comment("执行人名称")
    @Column(name = "exec_staff_name", nullable = true)
    private String execStaffName;

    // 计费相关字段
    @Comment("是否计费")
    @Column(name = "is_charge", nullable = true)
    private Boolean isCharge;

    @Comment("计费时间")
    @Column(name = "charge_date", nullable = true)
    private LocalDateTime chargeDate;

    @Comment("计费人")
    @Column(name = "charge_staff", nullable = true)
    private String chargeStaff;

    @Comment("计费人名称")
    @Column(name = "charge_staff_name", nullable = true)
    private String chargeStaffName;

    @Comment("计费科室")
    @Column(name = "charge_org", nullable = true)
    private String chargeOrg;

    @Comment("计费科室名称")
    @Column(name = "charge_org_name", nullable = true)
    private String chargeOrgName;

    // 打印相关字段
    @Comment("执行单打印标记")
    @Column(name = "is_print", nullable = true)
    private Boolean isPrint;

    @Comment("执行单打印时间")
    @Column(name = "print_date", nullable = true)
    private LocalDateTime printDate;

    @Comment("执行单打印人")
    @Column(name = "print_staff", nullable = true)
    private String printStaff;

    @Comment("执行单打印人名称")
    @Column(name = "print_staff_name", nullable = true)
    private String printStaffName;

    // 其他字段
    @Comment("皮试操作时间(区别于结果录入时间)")
    @Column(name = "skin_test_date", nullable = true)
    private String skinTestDate;

    @Comment("皮试人1")
    @Column(name = "st_staff_a", nullable = true)
    private String stStaffA;

    @Comment("皮试人1名称")
    @Column(name = "st_staff_a_name", nullable = true)
    private String stStaffAName;

    @Comment("皮试人2")
    @Column(name = "st_staff_b", nullable = true)
    private String stStaffB;

    @Comment("皮试人2名称")
    @Column(name = "st_staff_b_name", nullable = true)
    private String stStaffBName;

    @Comment("开立医生")
    @Column(name = "held_staff", nullable = true)
    private String heldStaff;

    @Comment("开立医生名称")
    @Column(name = "held_staff_name", nullable = true)
    private String heldStaffName;

    @Comment("开方医生所在科室")
    @Column(name = "create_org_code", nullable = false)
    private String createOrgCode;

    @Comment("开发医生所在科室名称")
    @Column(name = "create_org_name", nullable = true)
    private String createOrgName;

    @Enumerated(EnumType.STRING)
    @Comment("状态码")
    @Column(name = "status_code", nullable = false)
    private CisStatusEnum statusCode;

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;

    @Comment("最后修改的人员名称")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;

    @Version
    @Comment("版本")
    @Column(name = "version", nullable = false)
    private Integer version;

    @Comment("是否发送")
    @Column(name = "isSend", nullable = true)
    private Boolean isSend;

    @Comment("是否结算")
    @Column(name = "SETL_FLAG", nullable = true)
    private Boolean seltFlag;

    @Comment("数量")
    @Column(name = "num", nullable = false)
    private Double num;

    @Schema(description = "用法")
    @Column(name = "usage", nullable = true)
    private String usage;

    @Schema(description = "用法名称")
    @Column(name = "usage_name", nullable = true)
    private String usageName;

    @Schema(description = "第三方状态")
    @Column(name = "THRID_STATUS", nullable = true)
    private String thridStatus;

    @Schema(description = "发药状态")
    @Column(name = "DRUG_INOUT_TYPE", nullable = true)
    private DrugIpdDataStatusEnum drugInoutType;

    @Schema(description = "执行科室编码")
    @Column(name = "EXEC_ORG_CODE", nullable = false)
    private String execOrgCode;

    @Schema(description = "执行科室名称")
    @Column(name = "exec_org_name", nullable = true)
    private String execOrgName;

    @Schema(description = "冗余，执行单主单")
    @Column(name = "main_plan_flag", nullable = true)
    private Boolean mainPlanFlag;

    @Comment("药品批号")
    @Column(name = "batch_no", nullable = true)
    private String batchNo;

    @Comment("结算状态SetlStasEnum")
    @Enumerated(EnumType.STRING)
    @Column(name = "setl_stas", nullable = true)
    private SetlStasEnum setlStas;

    // ========== 静态查询方法 ==========

    /**
     * 根据ID获取执行单
     */
    public static Optional<CisOrderExecPlan> getCisOrderExecPlanById(String id) {
        return dao().findById(id).stream().findFirst();
    }

    /**
     * 根据申请单ID获取执行单列表
     */
    public static List<CisOrderExecPlan> getByCisBaseApplyId(String cisBaseApplyId) {
        return dao().findByCisBaseApplyId(cisBaseApplyId);
    }

    /**
     * 查询范围内的执行单
     */
    public static List<CisOrderExecPlan> findCisOrderExecPlans(CisOrderExecPlanTollQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static List<CisOrderExecPlan> getCisOrderExecPlans(String cisBaseApplyId, CisOrderExecPlanQto qto) {
        if (cisBaseApplyId != null) {
            qto.setCisBaseApplyId(cisBaseApplyId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Optional<CisOrderExecPlan> getLastCisOrderExecPlan(String cisBaseApplyId) {
        return dao().findTopByCisBaseApplyIdOrderByExecPlanDateDesc(cisBaseApplyId);
    }

    /**
     * 分页查询执行单
     */
    public static Page<CisOrderExecPlan> getCisOrderExecPlanPage(String cisBaseApplyId, CisOrderExecPlanQto qto) {

        if (cisBaseApplyId != null) {
            qto.setCisBaseApplyId(cisBaseApplyId);
        }
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }


    public static List<CisOrderExecPlan> getByCisBaseApplyIdOrderByExecPlanDateDesc(String visitCode, LocalDateTime endDate) {
        // 临时医嘱不返回数据
        if (endDate == null) {
            return Collections.emptyList();
        }
        // sql写法特殊jpa无法直接映射实体，这里手动赋值
        List<Object[]> results = dao().findByCisBaseApplyIdOrderByExecPlanDateDesc(visitCode, endDate.toLocalDate().atStartOfDay());
        return results.stream()
                .map(row -> {
                    CisOrderExecPlan execPlan = new CisOrderExecPlan();
                    execPlan.setCisBaseApplyId(String.valueOf(row[0]));
                    execPlan.setExecPlanDate(Convert.toLocalDateTime(row[1]));
                    return execPlan;
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据就诊码和申请ID列表查找执行中的医嘱执行计划
     *
     * @param visitCode 就诊码
     * @param applyIds  申请ID列表
     * @return 医嘱执行计划列表
     */
    public static List<CisOrderExecPlan> findProofExecCisOrderExecPlans(String visitCode, List<String> applyIds) {
        return dao().findCisOrderExecPlansByVisitCodeAndCisBaseApplyIdInAndStatusCode(visitCode, applyIds, CisStatusEnum.NEW);
    }

    /**
     * 根据ID列表查找医嘱执行计划
     * 由于列表可能很长，采用分块查询以提高效率
     *
     * @param ids 医嘱执行计划ID列表
     * @return 医嘱执行计划列表
     */
    public static List<CisOrderExecPlan> findExecCisOrderExecPlans(List<String> ids) {
        return Lists.partition(ids, 100).stream().map(p -> dao().findCisOrderExecPlansByIdIn(p))
                .flatMap(List::stream).toList();
    }

    /**
     * 根据医嘱申请ID删除医嘱执行计划
     *
     * @param cisBaseApplyId 医嘱申请ID
     */
    public static void deleteByCisBaseApplyId(String cisBaseApplyId) {
        dao().deleteByCisBaseApplyId(cisBaseApplyId);
    }

    /**
     * 根据订单计划ID查找医嘱执行计划
     *
     * @param orderPlanId 订单计划ID
     * @return 医嘱执行计划，如果不存在则返回空
     */
    public static Optional<CisOrderExecPlan> findByOrderPlanId(String orderPlanId) {
        return dao().findById(orderPlanId);
    }

    /**
     * 根据查询传输对象生成医嘱执行计划的规格说明
     * 用于动态构建查询条件
     *
     * @param qto 查询传输对象
     * @return 规格说明
     */
    private static Specification<CisOrderExecPlan> getSpecification(CisOrderExecPlanQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            // 动态构建查询条件
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgCode"), qto.getOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderId"), qto.getOrderId()));
            }
            if (StringUtils.isNotBlank(qto.getCisBaseApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisBaseApplyId"), qto.getCisBaseApplyId()));
            }
            if (qto.getOrderClass() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderClass"), qto.getOrderClass()));
            }
            if (qto.getExecPlanDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("execPlanDate"), LocalDateUtil.beginOfDay(qto.getExecPlanDate()), LocalDateUtil.endOfDay(qto.getExecPlanDate())));
            }
            if (StringUtils.isNotBlank(qto.getExecDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("execDate"), qto.getExecDate()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }

            if (qto.getIsCharge() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isCharge"), qto.getIsCharge()));
            }
            if (qto.getSeltFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("seltFlag"), qto.getSeltFlag()));
            }
            return predicate;
        };
    }

    private static Specification<CisOrderExecPlan> getSpecification(CisOrderExecPlanTollQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();

            // 参数校验
            if (qto == null) {
                return predicate;
            }

            // 患者主索引查询条件（必填字段）
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate,
                        criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }

            // 开始时间查询条件（必填字段）
            if (qto.getStartDate() != null) {
                predicate = criteriaBuilder.and(predicate,
                        criteriaBuilder.greaterThanOrEqualTo(root.get("chargeDate"), qto.getStartDate()));
            }

            // 结束时间查询条件（必填字段）
            if (qto.getEndDate() != null) {
                predicate = criteriaBuilder.and(predicate,
                        criteriaBuilder.lessThanOrEqualTo(root.get("chargeDate"), qto.getEndDate()));
            }

            // 患者结算状态查询条件（可选字段）
            if (qto.getExecOrgCode() != null) {
                predicate = criteriaBuilder.and(predicate,
                        criteriaBuilder.equal(root.get("execOrgCode"), qto.getExecOrgCode()));
            }

            // 结算状态
            if (qto.getSeltFlag() != null) {
                predicate = criteriaBuilder.and(predicate,
                        criteriaBuilder.equal(root.get("seltFlag"), qto.getSeltFlag()));
            }

//            // 就诊类型查询条件（可选字段）
//            // 注意：CisOrderExecPlan表中没有直接的visitType字段
//            // 如果需要根据就诊类型过滤，需要通过关联查询实现
//            if (qto.getVisitType() != null) {
//                // 这里可以根据业务需求添加相应的查询逻辑
//                // 例如：通过visitCode关联患者表或就诊表来获取就诊类型
//                // 暂时注释掉，具体实现需要根据数据库表结构调整
//                 predicate = criteriaBuilder.and(predicate,
//                     criteriaBuilder.equal(root.get("visitType"), qto.getVisitType()));
//            }

            return predicate;
        };
    }

    /**
     * 获取医嘱执行计划仓库实例
     *
     * @return 医嘱执行计划仓库实例
     */
    private static CisOrderExecPlanRepository dao() {
        return SpringUtil.getBean(CisOrderExecPlanRepository.class);
    }

    /**
     * 根据科室护士代码和状态码查找医嘱执行计划
     *
     * @param deptNurseCode 科室护士代码
     * @param statusCode    状态码数组
     * @return 医嘱执行计划列表
     */
    public static List<CisOrderExecPlan> findCisOrderExecPlansByDeptNurseCodeAndStatusCode(String deptNurseCode, CisStatusEnum[] statusCode) {
        BusinessAssert.hasText(deptNurseCode, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0007, "deptNurseCode");
        return dao().findCisOrderExecPlansByDeptNurseCodeAndStatusCodeIn(deptNurseCode, statusCode);
    }

    /**
     * 根据就诊码查找医嘱执行计划
     *
     * @param visitCode 就诊码
     * @return 医嘱执行计划列表
     */
    public static List<CisOrderExecPlan> findByVisitCode(String visitCode) {
        return dao().findByVisitCode(visitCode);
    }

    /**
     * 根据医嘱ID列表查找医嘱执行计划
     * 由于列表可能很长，采用分块查询以提高效率
     *
     * @param orderids 医嘱ID列表
     * @return 医嘱执行计划列表
     */
    public static List<CisOrderExecPlan> findCisOrderExecPlansByOrderIdIn(List<String> orderids) {
        BusinessAssert.notEmpty(orderids, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0001, "医嘱id");

        return Lists.partition(orderids, 100).stream().map(p -> dao().findCisOrderExecPlansByOrderIdIn(p))
                .flatMap(List::stream).toList();
//        return dao().findCisOrderExecPlansByOrderIdIn(orderids);
    }

    /**
     * 根据科室护士代码、状态码和结束日期查找执行中的医嘱执行计划
     *
     * @param deptNurseCode 科室护士代码
     * @param statusCode    状态码
     * @param endDate       结束日期
     * @return 医嘱执行计划列表
     */
    public static List<CisOrderExecPlan> findExecCisOrderExecPlansDeptNurseCode(String deptNurseCode, CisStatusEnum statusCode, LocalDateTime endDate) {
        return dao().findExecCisOrderExecPlansDeptNurseCode(deptNurseCode, statusCode, endDate);
    }

    /**
     * 根据科室护士代码、状态码、开始日期和结束日期查找已执行的医嘱执行计划
     *
     * @param deptNurseCode 科室护士代码
     * @param statusCode    状态码
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @return 医嘱执行计划列表
     */
    public static List<CisOrderExecPlan> findExecCisOrderExecPlansExecutedDeptNurseCode(String deptNurseCode, CisStatusEnum statusCode, LocalDateTime startDate, LocalDateTime endDate) {
        return dao().findExecCisOrderExecPlansExecutedDeptNurseCode(deptNurseCode, statusCode, startDate, endDate);
    }

    /**
     * 根据执行机构代码、状态码和结束日期查找执行中的医嘱执行计划
     *
     * @param execOrgCode 执行机构代码
     * @param statusCode  状态码
     * @param endDate     结束日期
     * @return 医嘱执行计划列表
     */
    public static List<CisOrderExecPlan> findExecCisOrderExecPlans(String execOrgCode, CisStatusEnum statusCode, LocalDateTime endDate) {
        return dao().findExecCisOrderExecPlans(execOrgCode, statusCode, endDate);
    }

    /**
     * 根据执行机构代码、状态码、开始日期和结束日期查找已执行的医嘱执行计划
     *
     * @param execOrgCode 执行机构代码
     * @param statusCode  状态码
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return 医嘱执行计划列表
     */
    public static List<CisOrderExecPlan> findExecCisOrderExecPlansExecuted(String execOrgCode, CisStatusEnum statusCode, LocalDateTime startDate, LocalDateTime endDate) {
        return dao().findExecCisOrderExecPlansExecuted(execOrgCode, statusCode, startDate, endDate);
    }

    /**
     * 查找执行中的药物医嘱执行计划
     *
     * @param qto 查询传输对象
     * @return 医嘱执行计划列表
     */
    public static List<CisOrderExecPlan> findExecCisOrderEDrugPlans(CisOrderExecPlanEDrugQto qto) {
        return findExecCisOrderDrugPlans(qto);
    }

    /**
     * 查找执行中的药物医嘱执行计划
     *
     * @param qto 查询传输对象
     * @return 医嘱执行计划列表
     */
    public static List<CisOrderExecPlan> findExecCisOrderCDrugPlans(CisOrderExecPlanCDrugQto qto) {
        return findExecCisOrderDrugPlans(qto);
    }

    /**
     * 根据查询传输对象查找执行中的药物医嘱执行计划
     *
     * @param qto 查询传输对象
     * @return 医嘱执行计划列表
     */
    private static List<CisOrderExecPlan> findExecCisOrderDrugPlans(CisOrderExecPlanDrugQto qto) {
        return dao().findExecCisOrderDrugPlans(qto.getDeptNurseCode(), qto.getStatusCode(), qto.getEndDate(), qto.getOrderType()
                , qto.getReceiveOrg(), qto.getOrderClass());
    }


    //未执行的执行单查询
    public static List<CisOrderExecPlan> findNoExecCisOrderExecPlans(String visitCode) {
        return dao().findCisOrderExecPlansByVisitCodeAndStatusCode(visitCode, CisStatusEnum.NEW);
    }

    public static List<CisOrderExecPlan> findExecCisOrderExecPlanByVisitCodeAndStatusCode(String visitCode, CisStatusEnum statusCode) {
        return dao().findCisOrderExecPlansByVisitCodeAndStatusCode(visitCode, statusCode);
    }

    public static List<CisOrderExecPlan> findNoChargeCisOrderExecPlans(String visitCode, Boolean isCharge) {
        return dao().findCisOrderExecPlansByVisitCodeAndIsCharge(visitCode, isCharge);
    }

    public static List<CisOrderExecPlan> findCisOrderExecPlanNoExes(CisStatusEnum statusCode, LocalDateTime beginDate, LocalDateTime endDate) {
        return dao().findCisOrderExecPlanNoExes(statusCode, beginDate, endDate);
    }

    public static List<CisOrderExecPlan> findCisOrderExecPlanWithRange(CisStatusEnum[] statusCode, LocalDateTime beginDate, LocalDateTime endDate, String deptNurseCode) {
        return dao().findCisOrderExecPlanWithRange(statusCode, beginDate, endDate, deptNurseCode);
    }

    public static List<CisOrderExecPlan> getCisOrderExecPlanWithRange(CisOrderExecPlanMtQto qto) {
        return dao().findAll(getSpecificationWithRange(qto));
    }

    public static Page<CisOrderExecPlan> getCisOrderExecPlanWithRangePages(CisOrderExecPlanMtQto qto) {
        return dao().findAll(getSpecificationWithRange(qto), JpaUtils.getPage(qto));
    }

    private static Specification<CisOrderExecPlan> getSpecificationWithRange(CisOrderExecPlanMtQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (qto.getBeginDate() != null && qto.getBeginDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.between(root.get("execDate"), LocalDateUtil.beginOfDay(qto.getBeginDate()), LocalDateUtil.endOfDay(qto.getEndDate())));
            }
            if (qto.getStatusCodes() != null) {
                CriteriaBuilder.In<Object> in = criteriaBuilder.in(root.get("statusCode"));
                Arrays.stream(qto.getStatusCodes()).forEach(in::value);
                predicate = criteriaBuilder.and(criteriaBuilder.and(in));
            }
            if (StringUtils.isNotBlank(qto.getExecDeptCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("execOrgCode"), qto.getExecDeptCode()));
            }
            if (StringUtils.isNotBlank(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("visitCode"), qto.getVisitCode()));
            }
            return predicate;
        };
    }

    public static List<CisOrderExecPlan> getCisOrderExecPlanForPrescription(List<String> visitCodes, String deptNurseCode, String receiveOrg, LocalDateTime execPlanDate) {
        CisOrderExecPlanQto qto = new CisOrderExecPlanQto();
        qto.setDeptNurseCode(deptNurseCode);
        qto.setReceiveOrg(receiveOrg);
        qto.setExecPlanDate(execPlanDate);
        return dao().findAll(getSpecificationCustom(visitCodes, qto), JpaUtils.getSort(qto));
    }

    private static Specification<CisOrderExecPlan> getSpecificationCustom(List<String> visitCodes, CisOrderExecPlanQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getPatMiCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patMiCode"), qto.getPatMiCode()));
            }
            if (!CollectionUtils.isEmpty(visitCodes)) {
                predicate = criteriaBuilder.and(predicate, root.get("visitCode").in(visitCodes));
            }
            if (StringUtils.isNotBlank(qto.getOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgCode"), qto.getOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrderId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderId"), qto.getOrderId()));
            }
            if (StringUtils.isNotBlank(qto.getCisBaseApplyId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("cisBaseApplyId"), qto.getCisBaseApplyId()));
            }
            if (qto.getOrderClass() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderClass"), qto.getOrderClass()));
            }
            if (qto.getExecPlanDate() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("execPlanDate"), qto.getExecPlanDate()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            if (StringUtils.isNotBlank(qto.getReceiveOrg())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("receiveOrg"), qto.getReceiveOrg()));
            }
            if (qto.getSeltFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("seltFlag"), qto.getSeltFlag()));
            }
            // 查询未发送的
            Predicate isSendFalse = criteriaBuilder.equal(root.get("isSend"), false);
            Predicate isSendNull = criteriaBuilder.isNull(root.get("isSend"));
            Predicate combinedOrCondition = criteriaBuilder.or(isSendFalse, isSendNull);
            predicate = criteriaBuilder.and(predicate, combinedOrCondition);
            return predicate;
        };
    }


    public static List<CisOrderExecPlan> getOtherExecedCisOrderExecPlan(CisOrderExecPlanNurseQto qto) {
        return dao().findAll(getSpecificationOtherExe(qto), JpaUtils.getSort(qto));
    }

    private static Specification<CisOrderExecPlan> getSpecificationOtherExe(CisOrderExecPlanNurseQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (!CollectionUtils.isEmpty(qto.getVisitCode())) {
                predicate = criteriaBuilder.and(predicate, root.get("visitCode").in(qto.getVisitCode()));
            }
            if (StringUtils.isNotBlank(qto.getExecOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.notEqual(root.get("execOrgCode"), qto.getExecOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.notEqual(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            return predicate;
        };
    }

    public static List<CisOrderExecPlan> findConsultationApply(LocalDateTime dateTime) {
        return dao().findConsultationApply(dateTime);
    }

    /**
     * 根据id查询抗菌药的执行单
     *
     * @param ids
     * @return
     */
    public static List<CisOrderExecPlan> findExecCisOrderExecPlansAntimicrobialsByIds(List<String> ids) {
        return dao().findExecCisOrderExecPlansAntimicrobialsByIds(ids);
    }

    /**
     * 根据id查询皮试的执行单
     *
     * @param ids
     * @return
     */
    public static List<CisOrderExecPlan> findExecCisOrderExecPlansSkinByIds(List<String> ids) {
        return dao().findExecCisOrderExecPlansSkinByIds(ids);
    }

    public static List<CisOrderExecPlan> findCisOrderExecPlanPatientFee(List<String> visitCodes, String deptNurseCode, LocalDateTime beginDate, LocalDateTime endDate) {
        CisOrderExecPlanQto qto = new CisOrderExecPlanQto();
        qto.setDeptNurseCode(deptNurseCode);
        return dao().findAll(getPatientFeeSpecification(qto, beginDate, endDate, visitCodes), JpaUtils.getSort(qto));
    }

    private static Specification<CisOrderExecPlan> getPatientFeeSpecification(CisOrderExecPlanQto qto, LocalDateTime beginDate, LocalDateTime endDate, List<String> visitCodes) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (!CollectionUtils.isEmpty(visitCodes)) {
                predicate = criteriaBuilder.and(predicate, root.get("visitCode").in(visitCodes));
            }
            if (StringUtils.isNotBlank(qto.getDeptNurseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptNurseCode"), qto.getDeptNurseCode()));
            }
            if (qto.getOrderClass() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orderClass"), qto.getOrderClass()));
            }
            if (beginDate != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("execPlanDate"), beginDate));
            }
            if (endDate != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("execPlanDate"), endDate));
            }
            if (qto.getStatusCode() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statusCode"), qto.getStatusCode()));
            }
            // 查询未计费的
            Predicate isChargeFalse = criteriaBuilder.equal(root.get("isCharge"), false);
            Predicate isChargeNull = criteriaBuilder.isNull(root.get("isCharge"));
            Predicate combinedOrCondition = criteriaBuilder.or(isChargeFalse, isChargeNull);
            predicate = criteriaBuilder.and(predicate, combinedOrCondition);
            return predicate;
        };
    }

    public static List<CisOrderExecPlan> findCisOrderExecPlanByCreatedDate(LocalDateTime dateTime) {
        return dao().findCisOrderExecPlanByCreatedDate(dateTime);
    }

    //    @Comment("第三方状态")
//    @Column(name = "THRID_STATUS", nullable = true)
    public String getThridStatus() {
        return thridStatus;
    }

    public void setThridStatus(String thridStatus) {
        this.thridStatus = thridStatus;
    }

    //    @Comment("是否结算")
//    @Column(name = "SETL_FLAG", nullable = true)
    public Boolean getSeltFlag() {
        return seltFlag;
    }

    public void setSeltFlag(Boolean seltFlag) {
        this.seltFlag = seltFlag;
    }

    //    @Comment("是否发送")
//    @Column(name = "isSend", nullable = true)
    public Boolean getIsSend() {
        return isSend;
    }

    protected void setIsSend(Boolean send) {
        isSend = send;
    }

    //    @Comment("数量")
//    @Column(name = "num", nullable = false)
    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    //    @Id
//    @Comment("标识")
//    @Column(name = "id", nullable = false, length = 50)
//    @GeneratedValue(generator = "snowflake_generator")
//    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    //    @Comment("主索引")
//    @Column(name = "pat_mi_code", nullable = false)
    public String getPatMiCode() {
        return patMiCode;
    }

    protected void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    //    @Comment("就诊流水号")
//    @Column(name = "visit_code", nullable = false)
    public String getVisitCode() {
        return visitCode;
    }

    protected void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    //    @Comment("开方科室编码")
//    @Column(name = "org_code", nullable = false)
    public String getOrgCode() {
        return orgCode;
    }

    protected void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    //    @Comment("开方科室名称")
//    @Column(name = "org_name", nullable = false)
    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    //    @Comment("护理组号")
//    @Column(name = "dept_nurse_code", nullable = false)
    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    protected void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    //    @Comment("医嘱号")
//    @Column(name = "order_id", nullable = false)
    public String getOrderId() {
        return orderId;
    }

    protected void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    //    @Comment("抽象父类标识")
//    @Column(name = "apply_id", nullable = false, length = 50)
    public String getCisBaseApplyId() {
        return cisBaseApplyId;
    }

    protected void setCisBaseApplyId(String cisBaseApplyId) {
        this.cisBaseApplyId = cisBaseApplyId;
    }

    //    @Comment("医嘱序号")
//    @Column(name = "sort_no", nullable = true)
    public Double getSortNo() {
        return sortNo;
    }

    protected void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    //    @Comment("医嘱项目编码")
//    @Column(name = "service_item_code", nullable = true)
    public String getServiceItemCode() {
        return serviceItemCode;
    }

    protected void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    //    @Comment("医嘱项目名称")
//    @Column(name = "service_item_name", nullable = true)
    public String getServiceItemName() {
        return serviceItemName;
    }

    protected void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    //    @Enumerated(EnumType.STRING)
//    @Comment("")
//    @Column(name = "order_class", nullable = true)
    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    protected void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    //    @Comment("是否皮试")
//    @Column(name = "is_skin", nullable = true)
    public Boolean getIsSkin() {
        return isSkin;
    }

    protected void setIsSkin(Boolean isSkin) {
        this.isSkin = isSkin;
    }

    //    @Comment("皮试结果")
//    @Column(name = "skin_result", nullable = true)
    public String getSkinResult() {
        return skinResult;
    }

    protected void setSkinResult(String skinResult) {
        this.skinResult = skinResult;
    }

    //    @Comment("领药科室")
//    @Column(name = "receive_org", nullable = true)
    public String getReceiveOrg() {
        return receiveOrg;
    }

    protected void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    //    @Comment("医嘱预计执行时间")
//    @Column(name = "exec_plan_date", nullable = true)
    public LocalDateTime getExecPlanDate() {
        return execPlanDate;
    }

    protected void setExecPlanDate(LocalDateTime execPlanDate) {
        this.execPlanDate = execPlanDate;
    }

    //    @Comment("执行时间")
//    @Column(name = "exec_date", nullable = true)
    public LocalDateTime getExecDate() {
        return execDate;
    }

    protected void setExecDate(LocalDateTime execDate) {
        this.execDate = execDate;
    }

    //    @Comment("执行人")
//    @Column(name = "exec_staff", nullable = true)
    public String getExecStaff() {
        return execStaff;
    }

    protected void setExecStaff(String execStaff) {
        this.execStaff = execStaff;
    }

    //    @Comment("是否计费")
//    @Column(name = "is_charge", nullable = true)
    public Boolean getIsCharge() {
        return isCharge;
    }

    protected void setIsCharge(Boolean isCharge) {
        this.isCharge = isCharge;
    }

    //    @Comment("计费时间")
//    @Column(name = "charge_date", nullable = true)
    public LocalDateTime getChargeDate() {
        return chargeDate;
    }

    protected void setChargeDate(LocalDateTime chargeDate) {
        this.chargeDate = chargeDate;
    }

    //    @Comment("计费人")
//    @Column(name = "charge_staff", nullable = true)
    public String getChargeStaff() {
        return chargeStaff;
    }

    protected void setChargeStaff(String chargeStaff) {
        this.chargeStaff = chargeStaff;
    }

    //    @Comment("计费科室")
//    @Column(name = "charge_org", nullable = true)
    public String getChargeOrg() {
        return chargeOrg;
    }

    protected void setChargeOrg(String chargeOrg) {
        this.chargeOrg = chargeOrg;
    }

    //    @Comment("执行单打印标记")
//    @Column(name = "is_print", nullable = true)
    public Boolean getIsPrint() {
        return isPrint;
    }

    protected void setIsPrint(Boolean isPrint) {
        this.isPrint = isPrint;
    }

    //    @Comment("执行单打印时间")
//    @Column(name = "print_date", nullable = true)
    public LocalDateTime getPrintDate() {
        return printDate;
    }

    protected void setPrintDate(LocalDateTime printDate) {
        this.printDate = printDate;
    }

    //    @Comment("执行单打印人")
//    @Column(name = "print_staff", nullable = true)
    public String getPrintStaff() {
        return printStaff;
    }

    protected void setPrintStaff(String printStaff) {
        this.printStaff = printStaff;
    }

    //    @Comment("皮试操作时间(区别于结果录入时间)")
//    @Column(name = "skin_test_date", nullable = true)
    public String getSkinTestDate() {
        return skinTestDate;
    }

    protected void setSkinTestDate(String skinTestDate) {
        this.skinTestDate = skinTestDate;
    }

    //    @Comment("皮试人1")
//    @Column(name = "st_staff_a", nullable = true)
    public String getStStaffA() {
        return stStaffA;
    }

    protected void setStStaffA(String stStaffA) {
        this.stStaffA = stStaffA;
    }

    //    @Comment("皮试人2")
//    @Column(name = "st_staff_b", nullable = true)
    public String getStStaffB() {
        return stStaffB;
    }

    protected void setStStaffB(String stStaffB) {
        this.stStaffB = stStaffB;
    }

    //    @Comment("开立医生")
//    @Column(name = "held_staff", nullable = true)
    public String getHeldStaff() {
        return heldStaff;
    }

    protected void setHeldStaff(String heldStaff) {
        this.heldStaff = heldStaff;
    }

    //    @Comment("开方医生所在科室")
//    @Column(name = "create_org_code", nullable = false)
    public String getCreateOrgCode() {
        return createOrgCode;
    }

    protected void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    //    @Enumerated(EnumType.STRING)
//    @Comment("")
//    @Column(name = "status_code", nullable = false)
    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    protected void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    //    @Comment("创建的人员")
//    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    //    @Comment("创建的时间")
//    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    //    @Comment("最后修改的人员")
//    @Column(name = "updated_staff", nullable = true, length = 64)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    //    @Comment("最后修改的人员")
//    @Column(name = "updated_staff_name", nullable = true, length = 64)
    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    //    @Comment("最后修改的时间")
//    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    //    @Version
//    @Comment("版本")
//    @Column(name = "version", nullable = false)
    public Integer getVersion() {
        return version;
    }

    protected void setVersion(Integer version) {
        this.version = version;
    }

    //    @Comment("用法")
//    @Column(name = "usage", nullable = true)
    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    //    @Comment("药品状态")
//    @Column(name = "DRUG_INOUT_TYPE", nullable = true)
    public DrugIpdDataStatusEnum getDrugInoutType() {
        return drugInoutType;
    }

    protected void setDrugInoutType(DrugIpdDataStatusEnum drugInoutType) {
        this.drugInoutType = drugInoutType;
    }

    //    @Comment("执行科室")
//    @Column(name = "EXEC_ORG_CODE", nullable = false)
    public String getExecOrgCode() {
        return execOrgCode;
    }

    public void setExecOrgCode(String execOrgCode) {
        this.execOrgCode = execOrgCode;
    }

    //    @Comment("主单标记")
//    @Column(name = "main_plan_flag", nullable = true)
    public Boolean getMainPlanFlag() {
        return mainPlanFlag;
    }

    public void setMainPlanFlag(Boolean mainPlanFlag) {
        this.mainPlanFlag = mainPlanFlag;
    }

    //    @Comment("领药科室名称")
//    @Column(name = "receive_org_name", nullable = true)
    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    //    @Comment("执行人名称")
//    @Column(name = "exec_staff_name", nullable = true)
    public String getExecStaffName() {
        return execStaffName;
    }

    public void setExecStaffName(String execStaffName) {
        this.execStaffName = execStaffName;
    }

    //    @Comment("计费人名称")
//    @Column(name = "charge_staff_name", nullable = true)
    public String getChargeStaffName() {
        return chargeStaffName;
    }

    public void setChargeStaffName(String chargeStaffName) {
        this.chargeStaffName = chargeStaffName;
    }

    //    @Comment("计费科室名称")
//    @Column(name = "charge_org_name", nullable = true)
    public String getChargeOrgName() {
        return chargeOrgName;
    }

    public void setChargeOrgName(String chargeOrgName) {
        this.chargeOrgName = chargeOrgName;
    }

    //    @Comment("执行单打印人名称")
//    @Column(name = "print_staff_name", nullable = true)
    public String getPrintStaffName() {
        return printStaffName;
    }

    public void setPrintStaffName(String printStaffName) {
        this.printStaffName = printStaffName;
    }

    //    @Comment("皮试人1名称")
//    @Column(name = "st_staff_a_name", nullable = true)
    public String getStStaffAName() {
        return stStaffAName;
    }

    public void setStStaffAName(String stStaffAName) {
        this.stStaffAName = stStaffAName;
    }

    //    @Comment("皮试人2名称")
//    @Column(name = "st_staff_b_name", nullable = true)
    public String getStStaffBName() {
        return stStaffBName;
    }

    public void setStStaffBName(String stStaffBName) {
        this.stStaffBName = stStaffBName;
    }

    //    @Comment("开立医生名称")
//    @Column(name = "held_staff_name", nullable = true)
    public String getHeldStaffName() {
        return heldStaffName;
    }

    public void setHeldStaffName(String heldStaffName) {
        this.heldStaffName = heldStaffName;
    }

    //    @Comment("开发医生所在科室名称")
//    @Column(name = "create_org_name", nullable = true)
    public String getCreateOrgName() {
        return createOrgName;
    }

    public void setCreateOrgName(String createOrgName) {
        this.createOrgName = createOrgName;
    }

    //    @Comment("用法名称")
//    @Column(name = "usage_name", nullable = true)
    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    //    @Comment("执行科室名称")
//    @Column(name = "exec_org_name", nullable = true)
    public String getExecOrgName() {
        return execOrgName;
    }

    public void setExecOrgName(String execOrgName) {
        this.execOrgName = execOrgName;
    }

    //    @Comment("药品批号")
//    @Column(name = "batch_no", nullable = true)
    public String getBatchNo() {
        return batchNo;
    }

    protected void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public SetlStasEnum getSetlStas() {
        return setlStas;
    }

    public void setSetlStas(SetlStasEnum setlStas) {
        this.setlStas = setlStas;
    }

    public List<CisOrderExecPlanCharge> findCisOrderExecPlanChargesByOrderPlanId() {
        return CisOrderExecPlanCharge.getByCisOrderExecPlanId(getId());
    }

    @Transient
    public CisBaseApply getCisBaseApply() {
        return CisBaseApply.getCisBaseApplyById(getCisBaseApplyId()).orElse(null);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOrderExecPlan other = (CisOrderExecPlan) obj;
        return Objects.equals(id, other.id);
    }

    public Boolean judge(String orderId, LocalDateTime execPlanDate) {
        return orderId.equals(this.getOrderId()) && execPlanDate.isEqual(this.getExecPlanDate());
    }

    private CisOrderExecPlan create(String cisBaseApplyId, CisOrderExecPlanNto cisOrderExecPlanNto) {
        BusinessAssert.notNull(cisOrderExecPlanNto, CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00014, "参数cisOrderExecPlanNto不能为空！");

        setCisBaseApplyId(cisBaseApplyId);

//        setId(cisOrderExecPlanNto.getId());
        setPatMiCode(cisOrderExecPlanNto.getPatMiCode());
        setVisitCode(cisOrderExecPlanNto.getVisitCode());
        setOrgCode(cisOrderExecPlanNto.getOrgCode());
        setOrgName(cisOrderExecPlanNto.getOrgName());
        setDeptNurseCode(cisOrderExecPlanNto.getDeptNurseCode());
        setOrderId(cisOrderExecPlanNto.getOrderId());
        setSortNo(cisOrderExecPlanNto.getSortNo());
        setServiceItemCode(cisOrderExecPlanNto.getServiceItemCode());
        setServiceItemName(cisOrderExecPlanNto.getServiceItemName());
        setOrderClass(cisOrderExecPlanNto.getOrderClass());
        setReceiveOrg(cisOrderExecPlanNto.getReceiveOrg());
        setReceiveOrgName(cisOrderExecPlanNto.getReceiveOrgName());
        setExecPlanDate(cisOrderExecPlanNto.getExecPlanDate());
        setHeldStaff(cisOrderExecPlanNto.getHeldStaff());
        setHeldStaffName(cisOrderExecPlanNto.getHeldStaffName());
        setCreateOrgCode(cisOrderExecPlanNto.getCreateOrgCode());
        setCreateOrgName(cisOrderExecPlanNto.getCreateOrgName());
        setCreatedStaff(StringUtils.isBlank(HIPLoginUtil.getStaffId()) ? cisOrderExecPlanNto.getCurLoginStaff() : HIPLoginUtil.getStaffId());
        setCreatedDate(LocalDateUtil.now());
        setUpdatedDate(LocalDateUtil.now());
        setStatusCode(CisStatusEnum.NEW);
        setNum(cisOrderExecPlanNto.getNum());
        setUsage(cisOrderExecPlanNto.getUsage());
        setUsageName(cisOrderExecPlanNto.getUsageName());
        setExecOrgCode(cisOrderExecPlanNto.getExecOrgCode());
        setExecOrgName(cisOrderExecPlanNto.getExecOrgName());
        setMainPlanFlag(cisOrderExecPlanNto.getMainPlanFlag());
        return this;
    }

    public CisOrderExecPlan create(String cisBaseApplyId, CisOrderExecPlanNto cisOrderExecPlanNto, Boolean save) {
        create(cisBaseApplyId, cisOrderExecPlanNto);
        if (Boolean.TRUE.equals(save)) {
            dao().save(this);
        }
        if (!CollectionUtils.isEmpty(cisOrderExecPlanNto.getCisOrderExecPlanChargeTos())) {
            cisOrderExecPlanNto.getCisOrderExecPlanChargeTos().forEach(cisOrderExecPlanChargeNto ->
                    new CisOrderExecPlanCharge().create(getId(), cisOrderExecPlanChargeNto, save)
            );
        }

        return this;
    }

    public CisOrderExecPlan create(String cisBaseApplyId, List<CisOrderExecPlanNto> cisOrderExecPlanNtos, Boolean save) {
        cisOrderExecPlanNtos.forEach(cisOrderExecPlanNto -> create(cisBaseApplyId, cisOrderExecPlanNto));
//        create(cisBaseApplyId,cisOrderExecPlanNto);

        if (save) {
            dao().save(this);
        }
        return this;
    }

    public void update(CisOrderExecPlanEto cisOrderExecPlanEto) {
        setSkinResult(cisOrderExecPlanEto.getSkinResult());
        setStatusCode(cisOrderExecPlanEto.getStatusCode());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        setNum(cisOrderExecPlanEto.getNum());
    }

    public void delete() {
        for (CisOrderExecPlanCharge cisOrderExecPlanCharge : CisOrderExecPlanCharge.getByCisOrderExecPlanId(getId())) {
            cisOrderExecPlanCharge.delete();
        }
//         CisOrderExecPlanCharge.deleteByCisOrderExecPlanId(getId());
        dao().delete(this);
    }

    public void exec() {
        BusinessAssert.isTrue(!CisStatusEnum.COMPLETED.equals(getStatusCode()),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_00013, getServiceItemName());
        setStatusCode(CisStatusEnum.COMPLETED);
        setExecStaff(HIPLoginUtil.getStaffId());
        setExecStaffName(HIPLoginUtil.getLoginName());
        setExecDate(LocalDateUtil.now());
        updateCommonFields();
    }

    public void execMQ(String execStaff, String execStaffName) {
        setStatusCode(CisStatusEnum.COMPLETED);
        setExecStaff(execStaff);
        setExecStaffName(execStaffName);
        setExecDate(LocalDateUtil.now());
        dao().save(this);
    }

    public void cancelMQ() {
        setStatusCode(CisStatusEnum.CANCELEXCUTE);
        dao().save(this);
    }

    public void cancel() {
//        Assert.isTrue(!getStatusCode().equals(CisStatusEnum.EXCUTE),"单！");
        BusinessAssert.isTrue(!getStatusCode().equals(CisStatusEnum.CANCELEXCUTE),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0003, "执行单");
        setStatusCode(CisStatusEnum.CANCELEXCUTE);
        setExecStaff(null);
        setExecStaffName(null);
        setExecDate(null);
        updateCommonFields();

    }

    public void noExec() {
        BusinessAssert.isTrue(!getStatusCode().equals(CisStatusEnum.REJECT),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0003, "执行单");
        setStatusCode(CisStatusEnum.REJECT);
        updateCommonFields();

    }

    public void anewExec() {
        setStatusCode(CisStatusEnum.NEW);
        updateCommonFields();
    }

    public void obsolete() {
        setStatusCode(CisStatusEnum.OBSOLETE);
        updateCommonFields();

    }

    public void update(CisOrderExecPlanChangeBackEto cisOrderExecPlanEto) {
        if (cisOrderExecPlanEto.getIsCharge() != null && cisOrderExecPlanEto.getIsCharge()) {
            setChargeDate(cisOrderExecPlanEto.getChargeDate());
            setChargeOrg(cisOrderExecPlanEto.getChargeOrg());
            setChargeStaff(cisOrderExecPlanEto.getChargeStaff());
            setIsCharge(cisOrderExecPlanEto.getIsCharge());
        }
        if (cisOrderExecPlanEto.getIsSend() != null) {
            setIsSend(cisOrderExecPlanEto.getIsSend());
        }
        setUpdatedStaff(cisOrderExecPlanEto.getChargeStaff());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
    }

    //修改第三方的状态。
    public void setThirdStatus(String thirdStatus) {
        setThridStatus(thirdStatus);
    }

    public void setCharge(CisOrderExecPlanReceiveChargeEto cisOrderExecPlanReceiveChargeEto) {
        CisOrderExecPlan cisOrderExecPlan = findByOrderPlanId(cisOrderExecPlanReceiveChargeEto.getId()).orElse(null);
        if (cisOrderExecPlan != null) {
            cisOrderExecPlan.setChargeDate(cisOrderExecPlanReceiveChargeEto.getChargeDate());
            cisOrderExecPlan.setChargeOrg(cisOrderExecPlanReceiveChargeEto.getChargeOrg());
            cisOrderExecPlan.setChargeStaff(cisOrderExecPlanReceiveChargeEto.getChargeStaff());
            cisOrderExecPlan.setIsCharge(true);

            List<CisOrderExecPlanCharge> chargeList = findCisOrderExecPlanChargesByOrderPlanId();
            if (!CollectionUtils.isEmpty(chargeList)) {
//                chargeList.forEach(cisOrderExecPlanCharge -> cisOrderExecPlanCharge.setSetlStas(SetlStasEnum.已结算));
            }
            dao().save(cisOrderExecPlan);
        }
    }

    public void doubleSign(String stStaffA, String stStaffB) {
        setStStaffA(stStaffA);
        setStStaffB(stStaffB);
        exec();
    }

    /**
     * 更新公共字段
     */
    private void updateCommonFields() {
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        dao().save(this);
    }

    public void setOrderPlanDrugInoutType(DrugIpdDataStatusEnum drugInoutType) {
        setDrugInoutType(drugInoutType);
        updateCommonFields();
    }

    public void updateIsSend(Boolean isSend) {
        setIsSend(isSend);
        updateCommonFields();
    }

    public void skinResultsInput(CisOrderExecPlanEto cisOrderExecPlanEto) {
        setIsSkin(true);
        setBatchNo(cisOrderExecPlanEto.getBatchNo());
        setSkinTestDate(cisOrderExecPlanEto.getSkinTestDate());
        setSkinResult(cisOrderExecPlanEto.getSkinResult());
        setStStaffA(cisOrderExecPlanEto.getStStaffA());
        setStStaffAName(cisOrderExecPlanEto.getStStaffAName());
        setStStaffB(cisOrderExecPlanEto.getStStaffB());
        setStStaffBName(cisOrderExecPlanEto.getStStaffBName());
        updateCommonFields();
    }

    public void updateIsPrint() {
        setIsPrint(Boolean.TRUE);
        setPrintStaff(HIPLoginUtil.getStaffId());
        setPrintStaffName(HIPLoginUtil.getLoginName());
        setPrintDate(LocalDateUtil.now());
    }

    public void updateSetlStas(CisOrderExecPlanSetlStasEto eto) {
        BusinessAssert.isTrue(this.getVersion().equals(eto.getVersion()),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0003, "执行单");
        if (SetlStasEnum.已结算.equals(eto.getSetlStas())) {
            setSeltFlag(Boolean.TRUE);
        } else {
            setSeltFlag(Boolean.FALSE);
        }
        setSetlStas(eto.getSetlStas());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
    }

    public void updateOpdChange(CisOrderExecPlanOpdChangeEto cisOrderExecPlanEto) {
        setChargeDate(cisOrderExecPlanEto.getChargeDate());
        setChargeOrg(cisOrderExecPlanEto.getChargeOrg());
        setChargeStaff(cisOrderExecPlanEto.getChargeStaff());
        setIsCharge(cisOrderExecPlanEto.getIsCharge());
        setUpdatedStaff(cisOrderExecPlanEto.getChargeStaff());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
    }

    public void updateVersion(Integer version){
        BusinessAssert.isTrue(this.getVersion().equals(version),
                CisApplyBusinessErrorEnum.BUS_CIS_APPLY_0003, "执行单");
//        setVersion(version);
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        dao().save(this);
    }
}
