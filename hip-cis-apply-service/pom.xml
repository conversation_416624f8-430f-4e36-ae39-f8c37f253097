<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bjgoodwill.hip</groupId>
        <artifactId>hip-parent</artifactId>
        <version>5.0-SNAPSHOT</version>
    </parent>

    <artifactId>hip-cis-apply-service</artifactId>
    <version>5.0-SNAPSHOT</version>
    <name>hip-cis-apply-service</name>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <hip_cis-apply_api.version>5.0-SNAPSHOT</hip_cis-apply_api.version>
        <hip-business-util.version>5.0-SNAPSHOT</hip-business-util.version>
        <!--        <hip-base-cis-medicineitem.version>5.0-SNAPSHOT</hip-base-cis-medicineitem.version>-->
        <hip-base-cis-dict.version>5.0-SNAPSHOT</hip-base-cis-dict.version>
        <hip-lock.version>5.0-SNAPSHOT</hip-lock.version>
        <hip-cis-cdr.version>5.0-SNAPSHOT</hip-cis-cdr.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-message</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-apply-api</artifactId>
            <version>${hip_cis-apply_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-business-util</artifactId>
            <version>${hip-business-util.version}</version>
            <scope>compile</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.bjgoodwill.hip</groupId>-->
        <!--            <artifactId>hip-base-cis-medicineitem-api</artifactId>-->
        <!--            <version>${hip-base-cis-medicineitem.version}</version>-->
        <!--            <scope>compile</scope>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-cis-dict-api</artifactId>
            <version>${hip-base-cis-dict.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-lock</artifactId>
            <version>${hip-lock.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-rule-api</artifactId>
            <version>5.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-pat-index-api</artifactId>
            <version>5.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-goods-api</artifactId>
            <version>5.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>21.0</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-pat-in-hospital-api</artifactId>
            <version>5.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-cdr-api</artifactId>
            <version>${hip-cis-cdr.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-term-api</artifactId>
            <version>5.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
