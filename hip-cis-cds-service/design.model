{"replaceJavaCode": false, "packageName": "com.bjgoodwill.hip.ds.cis.cds", "shortName": "cds", "entities": [{"inheritanceRole": "SUPERCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": null, "discriminatorColumn": "type", "discriminatorValue": null, "id": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "name": "CisDiagnoseCommon", "table": "cis_diagnose_common", "comment": "常用诊断", "subModule": "diagnose", "properties": [{"id": "cc631091-f4e0-43da-9655-7d7ccb18be29", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "45282232-f819-47c9-b97a-72fb0c87d87b", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "type": "Basic", "name": "diagnoseCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "diagnose_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "诊断编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "e3f1617a-0a7c-4159-9173-a39c3590abb8", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "type": "Basic", "name": "diagnoseName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "diagnose_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "诊断名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "a755142e-56d6-485f-baf7-89c962a096b5", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "type": "Basic", "name": "integral", "javaType": "<PERSON>", "enumName": null, "idGenerationType": null, "column": "integral", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "权重", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "fd0eef0e-644f-4bb9-8046-ac034f79216f", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "type": "Basic", "name": "isFix", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_fix", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "人工维护的", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "c3d27922-ab4e-43b0-a7df-a475dc758b03", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "type": "CreatedStaff", "name": "createdStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff", "length": 64, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "744cfaca-c4a2-4c90-9de9-f1b414a72bc8", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "1cc0faa7-817f-4f19-ac6c-a94d478c3a0b", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "type": "UpdatedStaff", "name": "updatedStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "updated_staff", "length": 64, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "b3f48679-6e22-407a-86a6-de7a63a05bd3", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "type": "UpdatedDate", "name": "updatedDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "updated_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "b61f1dc1-5f39-4da1-9cd8-f6f6b8545e54", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "type": "Version", "name": "version", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "version", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "版本", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": false}, {"id": "9dd6a9be-9a93-4df4-8861-05d799b5eaa7", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "type": "Enabled", "name": "enabled", "javaType": "boolean_", "enumName": null, "idGenerationType": null, "column": "enabled", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "已启用", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": true}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "discriminatorColumn": null, "discriminatorValue": "1", "id": "68cda466-c716-456d-af87-cd703582d177", "name": "CisDiagnoseOrgCommon", "table": null, "comment": "科室常用诊断", "subModule": null, "properties": [{"id": "701bde58-f779-42b6-b984-f2b8b0ec7b31", "entityId": "68cda466-c716-456d-af87-cd703582d177", "type": "Basic", "name": "orgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "org_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "科室编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "discriminatorColumn": null, "discriminatorValue": "2", "id": "ed719606-fc9e-46a7-907b-1d4c0fba13d1", "name": "CisDiagnoseDocCommon", "table": null, "comment": "个人常用诊断", "subModule": null, "properties": [{"id": "aa0d00a0-812f-4aff-9bef-3805a629c97b", "entityId": "ed719606-fc9e-46a7-907b-1d4c0fba13d1", "type": "Basic", "name": "docCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "doc_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医生编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "d828fdf6-e4f7-4371-931d-81729d182afe", "name": "CisDiagnoseRecord", "table": "cis_diagnose_record", "comment": "常用诊断自动同步记录", "subModule": "diagnose", "properties": [{"id": "1532913d-446f-424c-b091-ade9a0314177", "entityId": "d828fdf6-e4f7-4371-931d-81729d182afe", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "29bc2a8c-6ca5-4f27-8bfa-aea510bf910f", "entityId": "d828fdf6-e4f7-4371-931d-81729d182afe", "type": "Basic", "name": "recordDateTime", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "record_date_time", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "本次同步时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "e56aa4c6-cdaf-4d51-aed3-b76b2975f320", "entityId": "d828fdf6-e4f7-4371-931d-81729d182afe", "type": "CreatedStaff", "name": "createdStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff", "length": 64, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "9ab022d2-0c5a-4873-8d5d-c2fa9a0121ce", "entityId": "d828fdf6-e4f7-4371-931d-81729d182afe", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "SUPERCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": null, "discriminatorColumn": "type", "discriminatorValue": null, "id": "805175d6-502e-40b3-98cf-e8267bf74e37", "name": "CisOrderCommon", "table": "cis_order_common", "comment": "常用医嘱", "subModule": "order", "properties": [{"id": "fb4173ee-a584-4008-8a6c-1bcfb4d99921", "entityId": "805175d6-502e-40b3-98cf-e8267bf74e37", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "SNOWFLAKE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "b9158c70-e17b-41dc-8003-3b4062c6424e", "entityId": "805175d6-502e-40b3-98cf-e8267bf74e37", "type": "Basic", "name": "integral", "javaType": "<PERSON>", "enumName": null, "idGenerationType": null, "column": "integral", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "权重", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "9689cd00-b475-4bde-85b9-0b8c09abc878", "entityId": "805175d6-502e-40b3-98cf-e8267bf74e37", "type": "Basic", "name": "serviceItemCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "service_item_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "医嘱编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "451b2e69-5d85-4ca1-8bb4-18d601907306", "entityId": "805175d6-502e-40b3-98cf-e8267bf74e37", "type": "Basic", "name": "serviceItemName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "service_item_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医嘱名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "39004cff-2156-40af-8f6f-ebd3e369c40f", "entityId": "805175d6-502e-40b3-98cf-e8267bf74e37", "type": "Basic", "name": "executiveOrg", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "executive_org", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "执行科室", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "b2a0d407-efee-4c7c-aee7-d0df9195b3be", "entityId": "805175d6-502e-40b3-98cf-e8267bf74e37", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "SUB_SUPERCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "805175d6-502e-40b3-98cf-e8267bf74e37", "discriminatorColumn": null, "discriminatorValue": null, "id": "cbebf120-c3a3-4283-8150-09053e825834", "name": "CisDgimgCommon", "table": null, "comment": "检查常用医嘱", "subModule": null, "properties": [{"id": "bf99e1af-1ddd-400d-9fa5-ab95c6e27d45", "entityId": "cbebf120-c3a3-4283-8150-09053e825834", "type": "Basic", "name": "position", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "position", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "部位", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "2bc2210c-f4df-4b0a-bf52-f2f757cbaca3", "entityId": "cbebf120-c3a3-4283-8150-09053e825834", "type": "Basic", "name": "assistiveDevices", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "assistive_devices", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "辅助器械", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "5722a44e-78d1-4a5c-b770-c508e4a08761", "entityId": "cbebf120-c3a3-4283-8150-09053e825834", "type": "Basic", "name": "range", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "range", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "范围", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "80a6b0e4-b9cc-4c53-b69d-11401c7a5a31", "entityId": "cbebf120-c3a3-4283-8150-09053e825834", "type": "Basic", "name": "basicOperation", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "basic_operation", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "基本操作", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "4b451be2-28dd-45d3-9234-0d01629245ea", "entityId": "cbebf120-c3a3-4283-8150-09053e825834", "type": "Basic", "name": "azimuth", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "azimuth", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "方位", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "e35e2507-8eec-404a-89ab-ed56d6688f11", "entityId": "cbebf120-c3a3-4283-8150-09053e825834", "type": "Basic", "name": "layers", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "layers", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "层数", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "SUB_SUPERCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "805175d6-502e-40b3-98cf-e8267bf74e37", "discriminatorColumn": null, "discriminatorValue": null, "id": "568e7df3-c9b9-4428-b3f9-aba84210f824", "name": "CisSpcobsCommon", "table": null, "comment": "检验常用医嘱", "subModule": null, "properties": [{"id": "04bff372-9bf8-4988-8cb1-9476a73b51ae", "entityId": "568e7df3-c9b9-4428-b3f9-aba84210f824", "type": "Basic", "name": "specimen", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "specimen", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "标本", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "f4fc325d-c8c4-4de6-bcdd-e38da5d8b189", "entityId": "568e7df3-c9b9-4428-b3f9-aba84210f824", "type": "Basic", "name": "experimentalMethods", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "experimental_methods", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "实验方法", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "cfdc795d-5a27-49fc-9c36-88418ff4c672", "entityId": "568e7df3-c9b9-4428-b3f9-aba84210f824", "type": "Basic", "name": "approach", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "approach", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "入路", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "cbebf120-c3a3-4283-8150-09053e825834", "discriminatorColumn": null, "discriminatorValue": "1", "id": "d4d04554-59c3-4bc1-8711-c2945b36b3a4", "name": "CisDocDgimgCommon", "table": null, "comment": "常用医生检查医嘱", "subModule": null, "properties": [{"id": "f70356b9-0a9e-4ff8-a745-8cd9149214f2", "entityId": "d4d04554-59c3-4bc1-8711-c2945b36b3a4", "type": "Basic", "name": "docCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "doc_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医生", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "cbebf120-c3a3-4283-8150-09053e825834", "discriminatorColumn": null, "discriminatorValue": "2", "id": "7c0f9d16-56d6-4d40-ab7f-f1f233a561c1", "name": "CisOrgDgimgCommon", "table": null, "comment": "常用科室检查医嘱", "subModule": null, "properties": [{"id": "748f548e-ef38-403a-99ae-a9d98ddc7474", "entityId": "7c0f9d16-56d6-4d40-ab7f-f1f233a561c1", "type": "Basic", "name": "orgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "org_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "科室编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "568e7df3-c9b9-4428-b3f9-aba84210f824", "discriminatorColumn": null, "discriminatorValue": "3", "id": "df23c186-edd4-4976-ac63-a32fe0285e95", "name": "CisDocSpcobsCommon", "table": null, "comment": "医生常用检验医嘱", "subModule": null, "properties": [{"id": "1b835627-0867-4e29-9ab9-1ecf91236969", "entityId": "df23c186-edd4-4976-ac63-a32fe0285e95", "type": "Basic", "name": "docCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "doc_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医生编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "568e7df3-c9b9-4428-b3f9-aba84210f824", "discriminatorColumn": null, "discriminatorValue": "4", "id": "9d962a78-5d51-4cf9-9152-9d7620550612", "name": "CisOrgSpcobsCommon", "table": null, "comment": "常用科室检验医嘱", "subModule": null, "properties": [{"id": "c95b927d-26b5-45a8-a254-2c8bd3c95f34", "entityId": "9d962a78-5d51-4cf9-9152-9d7620550612", "type": "Basic", "name": "orgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "org_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "科室编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "8ea4023c-778f-4c68-a95c-5fc5fc2989cb", "name": "Test1", "table": "test_1", "comment": null, "subModule": "test", "properties": [{"id": "bdd948bb-77c1-4874-8bcc-b30764d97d02", "entityId": "8ea4023c-778f-4c68-a95c-5fc5fc2989cb", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "e654712e-9df1-40c9-99e5-eff4f9f39094", "entityId": "8ea4023c-778f-4c68-a95c-5fc5fc2989cb", "type": "Basic", "name": "test2_id", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "test_2___id", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "a4361240-48ea-4bd7-ba65-bec79dd91643", "entityId": "8ea4023c-778f-4c68-a95c-5fc5fc2989cb", "type": "Version", "name": "version", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "version", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "版本", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": false}, {"id": "f8c8f208-b589-45d6-a1eb-eda7bccf33db", "entityId": "8ea4023c-778f-4c68-a95c-5fc5fc2989cb", "type": "Enabled", "name": "enabled", "javaType": "boolean_", "enumName": null, "idGenerationType": null, "column": "enabled", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "已启用", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": true}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "b53499b7-5341-44f3-8fe3-fad3585bd88e", "name": "Test2", "table": "test_2", "comment": null, "subModule": "test", "properties": [{"id": "19e3b75e-36d5-4bf5-be68-e31bbf9154bb", "entityId": "b53499b7-5341-44f3-8fe3-fad3585bd88e", "type": "Id", "name": "test1Id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "test_1_id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": "COMPOSITION", "associatedEntityId": "8ea4023c-778f-4c68-a95c-5fc5fc2989cb", "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "300268a8-1298-4c34-a271-64d1b76ee6a4", "entityId": "b53499b7-5341-44f3-8fe3-fad3585bd88e", "type": "Enabled", "name": "enabled", "javaType": "boolean_", "enumName": null, "idGenerationType": null, "column": "enabled", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "已启用", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": true}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "0b838f04-b2d1-4b35-801a-eac1bb6b569c", "name": "CisOrderRecord", "table": "cis_order_record", "comment": "常用医嘱记录日志", "subModule": "order", "properties": [{"id": "c78b7736-46e9-4027-b1d5-929e9187a3eb", "entityId": "0b838f04-b2d1-4b35-801a-eac1bb6b569c", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "SNOWFLAKE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "543c7cfa-e1a1-480b-b4fd-1d139de5c55d", "entityId": "0b838f04-b2d1-4b35-801a-eac1bb6b569c", "type": "CreatedStaff", "name": "createdStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff", "length": 64, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "6b33c8e5-4a56-45f8-8250-4a6c1989a778", "entityId": "0b838f04-b2d1-4b35-801a-eac1bb6b569c", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "53709b9e-ee6f-49a8-a620-e7dd2c425620", "entityId": "0b838f04-b2d1-4b35-801a-eac1bb6b569c", "type": "Basic", "name": "recordDateTime", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "record_date_time", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "记录时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "SUPERCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": null, "discriminatorColumn": "type", "discriminatorValue": null, "id": "48f6d332-1f39-4b0e-bebc-f309e1e79aeb", "name": "CisDurgLimit", "table": "cis_durg_limit", "comment": "药品限制管理", "subModule": "durgLimit", "properties": [{"id": "3da361fd-2ef8-4eca-80be-18ab6237731a", "entityId": "48f6d332-1f39-4b0e-bebc-f309e1e79aeb", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "SNOWFLAKE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "c7043665-ca35-4e90-89e4-6dd706d17e16", "entityId": "48f6d332-1f39-4b0e-bebc-f309e1e79aeb", "type": "Basic", "name": "drugCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "drug_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "药品编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": true}, {"id": "8c878ee5-8169-4dd2-b8ff-8b77b83650a3", "entityId": "48f6d332-1f39-4b0e-bebc-f309e1e79aeb", "type": "Basic", "name": "statusCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "status_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "状态", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": true, "hadInQto": true}, {"id": "e1296043-6502-4933-b4f5-dce7d4455ac4", "entityId": "48f6d332-1f39-4b0e-bebc-f309e1e79aeb", "type": "CreatedStaff", "name": "createdStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff", "length": 64, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "24e93c0b-5a76-4a0b-8f39-7e8b6c1cefa0", "entityId": "48f6d332-1f39-4b0e-bebc-f309e1e79aeb", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "48f6d332-1f39-4b0e-bebc-f309e1e79aeb", "discriminatorColumn": null, "discriminatorValue": "1", "id": "9ee6c984-575a-4061-8b59-fb9300335997", "name": "CisOrgDrugLimit", "table": null, "comment": "科室限制药品管理", "subModule": null, "properties": [{"id": "3a5b5641-1774-47dc-ba85-7d5d8773c097", "entityId": "9ee6c984-575a-4061-8b59-fb9300335997", "type": "Basic", "name": "orgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "org_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "科室编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "a3c70eaa-3c4d-4f84-b7cb-249ead75287d", "entityId": "9ee6c984-575a-4061-8b59-fb9300335997", "type": "Basic", "name": "orgName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "org_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "科室名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "SUBCLASS", "inheritanceStrategy": "SINGLE_TABLE", "superEntityId": "48f6d332-1f39-4b0e-bebc-f309e1e79aeb", "discriminatorColumn": null, "discriminatorValue": "2", "id": "2a9e5c3e-848a-431c-912e-7eb656bb72eb", "name": "CisDocDrugLimit", "table": null, "comment": "医生限制药品管理", "subModule": null, "properties": [{"id": "cb2e359a-3961-4587-911a-3c6e825fad77", "entityId": "2a9e5c3e-848a-431c-912e-7eb656bb72eb", "type": "Basic", "name": "docCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "doc_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "医生编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "cf9f8233-51ab-4c69-a56a-de8f012860f4", "entityId": "2a9e5c3e-848a-431c-912e-7eb656bb72eb", "type": "Basic", "name": "doc<PERSON>ame", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "doc_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "医生名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "6344ce08-7fd1-4231-8277-acf504276a77", "name": "CisDrugUsageFreqCommon", "table": "cis_drug_usage_freq_common", "comment": "科室药品开立常用用法和频次信息统计", "subModule": "drugUsageFreq", "properties": [{"id": "7e8b6cf5-a9cb-4ae5-90ed-55200414a760", "entityId": "6344ce08-7fd1-4231-8277-acf504276a77", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "SNOWFLAKE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "61875150-aa9a-4411-b6dd-bfc099fd91b5", "entityId": "6344ce08-7fd1-4231-8277-acf504276a77", "type": "Basic", "name": "deptCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "dept_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "开立科室编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "a3e48e2a-4239-4a76-9124-56695c32bc1e", "entityId": "6344ce08-7fd1-4231-8277-acf504276a77", "type": "Basic", "name": "drugCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "drug_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "药品编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "22e7aa53-5726-47c4-843e-7ddaab96<PERSON>ea", "entityId": "6344ce08-7fd1-4231-8277-acf504276a77", "type": "Basic", "name": "usageCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "usage_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "用法编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "26485038-7a76-4262-b64b-6d744a6a3f85", "entityId": "6344ce08-7fd1-4231-8277-acf504276a77", "type": "Basic", "name": "frequencyCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "frequency_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "频次编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "611a92b2-d606-43b5-a49f-10454bdee3c2", "entityId": "6344ce08-7fd1-4231-8277-acf504276a77", "type": "Basic", "name": "num", "javaType": "Integer", "enumName": null, "idGenerationType": null, "column": "num", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "次数", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "b9061ae6-df5c-4b81-a33b-828edd99b54a", "entityId": "6344ce08-7fd1-4231-8277-acf504276a77", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": false, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "502c756b-ee56-4596-9117-99a9ed173712", "entityId": "6344ce08-7fd1-4231-8277-acf504276a77", "type": "UpdatedDate", "name": "updatedDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "updated_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}]}, {"inheritanceRole": "NORMAL", "inheritanceStrategy": null, "superEntityId": null, "discriminatorColumn": null, "discriminatorValue": null, "id": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "name": "CisOrderTempCharge", "table": "cis_order_temp_charge", "comment": "组套明细费用表", "subModule": "orderTmep.entity", "properties": [{"id": "7c273f7c-51ef-40e0-bf77-3cd4533af7d6", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Id", "name": "id", "javaType": "String", "enumName": null, "idGenerationType": "NONE", "column": "id", "length": 50, "scale": null, "precision": null, "nullable": false, "unique": true, "comment": "标识", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "bad20a33-25e0-4939-b6df-e7ba4bd55750", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "tempDetailId", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "temp_detail_id", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "f14b8e55-32aa-495e-99ec-8531ebc31584", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "priceItemCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "price_item_code", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "收费项目编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "10672fdd-ffb0-4575-9193-bbf2818d4bd5", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "priceItemName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "price_item_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "收费项目名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": true}, {"id": "b6f4b1af-8ef6-4dc6-8ec4-ffa947fc3c07", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "systemItemClass", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "system_item_class", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "类型", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "85b9012c-de5a-4db8-bf92-3410413adf90", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "isFixed", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "is_fixed", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "固定费用", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "835959f8-a780-4d24-a495-1fbeb6b05df9", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "packageSpec", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "package_spec", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "规格", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "fa2dc33b-3136-40cd-bd17-3f50f21a3e9a", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "price", "javaType": "BigDecimal", "enumName": null, "idGenerationType": null, "column": "price", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "单价", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "d10745e3-6e4d-4a39-b546-aa25234d635e", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "unit", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "unit", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "单位", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "a6d90ff3-7877-4f0c-b2b4-5a0800f8debd", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "unitName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "unit_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "单位名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "04daaff1-a8ce-41d1-a7dd-2545ef65254f", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "num", "javaType": "Double", "enumName": null, "idGenerationType": null, "column": "num", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "数量", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "32a3dce4-1f97-46b8-93e0-07c77a8bec93", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "chageAmount", "javaType": "BigDecimal", "enumName": null, "idGenerationType": null, "column": "chage_amount", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "金额", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "bb14acba-625a-4e42-bad9-839d28c101db", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "limitConformFlag", "javaType": "Boolean", "enumName": null, "idGenerationType": null, "column": "limit_conform_flag", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "特限符合标识:1符合,0不符合", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "5d45ece2-6136-4240-960f-1e4426130b23", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "barCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "bar_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "SPD高值耗材唯一码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": false, "hadInQto": false}, {"id": "a38e0bcd-4cd8-4f63-9a31-f38b0b3b5b7c", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "executeOrgCode", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "execute_org_code", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行科室编码", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "41e137ad-83ca-4401-8372-8dbc70a1a5bc", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "Basic", "name": "executeOrgName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "execute_org_name", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "执行科室名称", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": true, "hadInEto": true, "hadInQto": false}, {"id": "aba25eaf-1839-4f8a-a648-cf1d30174930", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "CreatedStaff", "name": "createdStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff", "length": 64, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "1d609094-a81f-47cb-8486-bf1a024432db", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "CreatedStaffName", "name": "createdStaffName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "created_staff_name", "length": 64, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "创建的人员姓名", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "a9de7dac-1ec7-4a0a-91d4-283793226df8", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "CreatedDate", "name": "createdDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "created_date", "length": null, "scale": null, "precision": null, "nullable": false, "unique": false, "comment": "创建的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "878c32e1-1e6d-439d-835b-bfc8db8fd68a", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "UpdatedStaff", "name": "updatedStaff", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "updated_staff", "length": 64, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的人员", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "14385552-c211-4211-a05a-9e54e5667f16", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "UpdatedStaffName", "name": "updatedStaffName", "javaType": "String", "enumName": null, "idGenerationType": null, "column": "updated_staff_name", "length": 64, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的人员姓名", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}, {"id": "e30ccbfd-00ec-4a39-8c5a-0d4d4f3ccc03", "entityId": "63e52821-0c0f-45d3-b7da-2c5ce551226b", "type": "UpdatedDate", "name": "updatedDate", "javaType": "LocalDateTime", "enumName": null, "idGenerationType": null, "column": "updated_date", "length": null, "scale": null, "precision": null, "nullable": true, "unique": false, "comment": "最后修改的时间", "associationType": null, "associatedEntityId": null, "hadInTo": true, "hadInNto": false, "hadInEto": false, "hadInQto": false}]}], "services": [{"id": "cc209d20-0839-4951-91ec-33f984330130", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "superServiceId": null, "comment": "常用诊断领域服务", "pxyEntities": [{"id": "5c387531-a12f-42ec-a01f-c1a38526811c", "serviceId": "cc209d20-0839-4951-91ec-33f984330130", "entityId": "920c6fb3-bf25-4fd7-bb92-ee7245b49b48", "findAll": false, "findOne": false, "create": false, "update": false, "delete": false, "enableAndDisable": true}]}, {"id": "112ba24f-32bb-4e45-9a33-adb7c1ad6a43", "entityId": "68cda466-c716-456d-af87-cd703582d177", "superServiceId": "cc209d20-0839-4951-91ec-33f984330130", "comment": "科室常用诊断领域服务", "pxyEntities": [{"id": "c45fda95-639b-4fa2-9cff-72c3f3b37ee8", "serviceId": "112ba24f-32bb-4e45-9a33-adb7c1ad6a43", "entityId": "68cda466-c716-456d-af87-cd703582d177", "findAll": true, "findOne": true, "create": true, "update": true, "delete": false, "enableAndDisable": false}]}, {"id": "38f34c53-da6d-4ae7-bede-ee8e379e4393", "entityId": "ed719606-fc9e-46a7-907b-1d4c0fba13d1", "superServiceId": "cc209d20-0839-4951-91ec-33f984330130", "comment": "个人常用诊断领域服务", "pxyEntities": [{"id": "5bc3f7a5-482c-4173-9509-d17a14177c1d", "serviceId": "38f34c53-da6d-4ae7-bede-ee8e379e4393", "entityId": "ed719606-fc9e-46a7-907b-1d4c0fba13d1", "findAll": true, "findOne": true, "create": true, "update": true, "delete": false, "enableAndDisable": false}]}, {"id": "a59ffa3b-8a8c-4475-b7b6-ef91a4d51800", "entityId": "d828fdf6-e4f7-4371-931d-81729d182afe", "superServiceId": null, "comment": "常用诊断自动同步记录领域服务", "pxyEntities": [{"id": "6c178d53-b41f-479b-aa2a-b6a38cc14e40", "serviceId": "a59ffa3b-8a8c-4475-b7b6-ef91a4d51800", "entityId": "d828fdf6-e4f7-4371-931d-81729d182afe", "findAll": false, "findOne": true, "create": true, "update": true, "delete": false, "enableAndDisable": false}]}, {"id": "09698768-d010-4dbf-ae7e-f96f7208bd12", "entityId": "d4d04554-59c3-4bc1-8711-c2945b36b3a4", "superServiceId": null, "comment": "常用医生检查医嘱领域服务", "pxyEntities": [{"id": "57928176-eb34-496a-a3d9-04685fe79817", "serviceId": "09698768-d010-4dbf-ae7e-f96f7208bd12", "entityId": "d4d04554-59c3-4bc1-8711-c2945b36b3a4", "findAll": true, "findOne": false, "create": true, "update": true, "delete": false, "enableAndDisable": false}]}, {"id": "bc15077f-25e4-4428-a5f0-c6216afce5c3", "entityId": "7c0f9d16-56d6-4d40-ab7f-f1f233a561c1", "superServiceId": null, "comment": "常用科室检查医嘱领域服务", "pxyEntities": [{"id": "22ab83cb-6758-4c1d-bd98-af8ed414546d", "serviceId": "bc15077f-25e4-4428-a5f0-c6216afce5c3", "entityId": "7c0f9d16-56d6-4d40-ab7f-f1f233a561c1", "findAll": true, "findOne": false, "create": true, "update": true, "delete": false, "enableAndDisable": false}]}, {"id": "be7f647c-c3f9-43f6-a67c-2fb44d7bae2f", "entityId": "df23c186-edd4-4976-ac63-a32fe0285e95", "superServiceId": null, "comment": "医生常用检验医嘱领域服务", "pxyEntities": [{"id": "8983d5c1-25cf-43d1-8e32-c4d486d4735c", "serviceId": "be7f647c-c3f9-43f6-a67c-2fb44d7bae2f", "entityId": "df23c186-edd4-4976-ac63-a32fe0285e95", "findAll": true, "findOne": false, "create": true, "update": true, "delete": false, "enableAndDisable": false}]}, {"id": "d1d190db-b20d-4c11-a62e-d314c2f344c5", "entityId": "9d962a78-5d51-4cf9-9152-9d7620550612", "superServiceId": null, "comment": "常用科室检验医嘱领域服务", "pxyEntities": [{"id": "ec2fe7b8-1959-4cca-94ab-6b060723a2f2", "serviceId": "d1d190db-b20d-4c11-a62e-d314c2f344c5", "entityId": "9d962a78-5d51-4cf9-9152-9d7620550612", "findAll": true, "findOne": false, "create": true, "update": true, "delete": false, "enableAndDisable": false}]}, {"id": "090db6dd-b814-4d00-9da6-e13d9f418d21", "entityId": "0b838f04-b2d1-4b35-801a-eac1bb6b569c", "superServiceId": null, "comment": "常用医嘱记录日志领域服务", "pxyEntities": [{"id": "f8475953-dca9-46ea-8687-f96b651e51da", "serviceId": "090db6dd-b814-4d00-9da6-e13d9f418d21", "entityId": "0b838f04-b2d1-4b35-801a-eac1bb6b569c", "findAll": true, "findOne": false, "create": true, "update": true, "delete": false, "enableAndDisable": false}]}, {"id": "abdbff93-5418-4066-832a-6f1d4d87f30d", "entityId": "9ee6c984-575a-4061-8b59-fb9300335997", "superServiceId": null, "comment": "科室限制药品管理领域服务", "pxyEntities": [{"id": "7f0af197-39b8-4848-9997-029903c73e50", "serviceId": "abdbff93-5418-4066-832a-6f1d4d87f30d", "entityId": "9ee6c984-575a-4061-8b59-fb9300335997", "findAll": true, "findOne": false, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "eff5c07e-8ece-4f3c-bda8-082c8a392884", "entityId": "2a9e5c3e-848a-431c-912e-7eb656bb72eb", "superServiceId": null, "comment": "医生限制药品管理领域服务", "pxyEntities": [{"id": "8cb61683-621a-48aa-9a96-657004857a00", "serviceId": "eff5c07e-8ece-4f3c-bda8-082c8a392884", "entityId": "2a9e5c3e-848a-431c-912e-7eb656bb72eb", "findAll": true, "findOne": false, "create": true, "update": true, "delete": true, "enableAndDisable": false}]}, {"id": "d1306762-23ce-448b-81b7-52fa6ee2362c", "entityId": "6344ce08-7fd1-4231-8277-acf504276a77", "superServiceId": null, "comment": "科室药品开立常用用法和频次信息统计领域服务", "pxyEntities": []}]}