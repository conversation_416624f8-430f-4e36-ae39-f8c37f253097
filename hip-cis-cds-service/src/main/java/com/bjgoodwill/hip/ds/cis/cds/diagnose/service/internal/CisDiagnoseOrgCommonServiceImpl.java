package com.bjgoodwill.hip.ds.cis.cds.diagnose.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.entity.CisDiagnoseOrgCommon;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.service.CisDiagnoseOrgCommonService;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.service.internal.assembler.CisDiagnoseOrgCommonAssembler;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseOrgCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseOrgCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseOrgCommonTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController("com.bjgoodwill.hip.ds.cis.cds.diagnose.service.CisDiagnoseOrgCommonService")
@RequestMapping(value = "/api/cds/diagnose/cisDiagnoseOrgCommon", produces = "application/json; charset=utf-8")
public class CisDiagnoseOrgCommonServiceImpl extends CisDiagnoseCommonServiceImpl implements CisDiagnoseOrgCommonService {

    // private final Double param = 0.1;

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisDiagnoseOrgCommonTo> getCisDiagnoseOrgCommons(CisDiagnoseOrgCommonQto cisDiagnoseOrgCommonQto) {
        return CisDiagnoseOrgCommonAssembler.toTos(CisDiagnoseOrgCommon.getCisDiagnoseOrgCommons(cisDiagnoseOrgCommonQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisDiagnoseOrgCommonTo> getCisDiagnoseOrgCommonPage(CisDiagnoseOrgCommonQto cisDiagnoseOrgCommonQto) {
        Page<CisDiagnoseOrgCommon> page = CisDiagnoseOrgCommon.getCisDiagnoseOrgCommonPage(cisDiagnoseOrgCommonQto);
        Page<CisDiagnoseOrgCommonTo> result = page.map(CisDiagnoseOrgCommonAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisDiagnoseOrgCommonTo getCisDiagnoseOrgCommonById(String id) {
        return CisDiagnoseOrgCommonAssembler.toTo(CisDiagnoseOrgCommon.getCisDiagnoseOrgCommonById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisDiagnoseOrgCommonTo createCisDiagnoseOrgCommon(CisDiagnoseOrgCommonNto cisDiagnoseOrgCommonNto) {
        CisDiagnoseOrgCommon cisDiagnoseOrgCommon = new CisDiagnoseOrgCommon();
        cisDiagnoseOrgCommon = cisDiagnoseOrgCommon.create(cisDiagnoseOrgCommonNto);
        return CisDiagnoseOrgCommonAssembler.toTo(cisDiagnoseOrgCommon);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void cancelCisDiagnoseOrgCommon(String id) {
        var item = CisDiagnoseOrgCommon.getCisDiagnoseCommonById(id);
        item.ifPresent(cisDiagnoseOrgCommon -> cisDiagnoseOrgCommon.disable());
    }

//    @Override
//    public void updateCisDiagnoseOrgCommon(String id, CisDiagnoseOrgCommonEto cisDiagnoseOrgCommonEto) {
//
//    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false)
    public void autostatisticsOrgDiagnose(List<CisDiagnoseOrgCommonNto> ntos) {
        if (ntos == null || ntos.isEmpty()) {
            return;
        }

        CisDiagnoseOrgCommonQto qto = new CisDiagnoseOrgCommonQto();
        qto.setIsFix(false);
        qto.setEnabled(true);

        List<CisDiagnoseOrgCommon> entities = CisDiagnoseOrgCommon.getCisDiagnoseOrgCommons(qto);
        Map<String, CisDiagnoseOrgCommon> entityMap = entities.stream()
                .collect(Collectors.toMap(e -> e.getDiagnoseCode() + "-" + e.getOrgCode(), e -> e));

        CisDiagnoseOrgCommonNto nto = new CisDiagnoseOrgCommonNto();

        ntos.forEach(diagnoseOrgNto -> {
            String key = diagnoseOrgNto.getDiagnoseCode() + "-" + diagnoseOrgNto.getOrgCode();
            Optional<CisDiagnoseOrgCommon> opt = Optional.ofNullable(entityMap.get(key));

            if (opt.isPresent()) {
                opt.get().updateByIntegral(opt.get().getIntegral() + diagnoseOrgNto.getIntegral(), diagnoseOrgNto.getDiagnoseName());
            } else {
                nto.setOrgCode(diagnoseOrgNto.getOrgCode());
                nto.setDiagnoseCode(diagnoseOrgNto.getDiagnoseCode());
                nto.setIntegral(diagnoseOrgNto.getIntegral());
                nto.setDiagnoseName(diagnoseOrgNto.getDiagnoseName());
                nto.setIsFix(false);
                createCisDiagnoseOrgCommon(nto);
            }
        });
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}