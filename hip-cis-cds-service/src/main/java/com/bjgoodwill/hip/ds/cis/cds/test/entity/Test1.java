package com.bjgoodwill.hip.ds.cis.cds.test.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.test.repository.Test1Repository;
import com.bjgoodwill.hip.ds.cis.cds.test.to.Test1Eto;
import com.bjgoodwill.hip.ds.cis.cds.test.to.Test1Nto;
import com.bjgoodwill.hip.ds.cis.cds.test.to.Test1Qto;
import com.bjgoodwill.hip.jpa.core.SnowflakeIdGenerator;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "null")
@Table(name = "test_1", indexes = {}, uniqueConstraints = {})
public class Test1 {

    // 标识
    private String id;
    //
    private String test2_id;
    // 版本
    private Integer version;
    // 已启用
    private boolean enabled;

    public static Optional<Test1> getTest1ById(String id) {
        return dao().findById(id);
    }

    public static List<Test1> getTest1s(Test1Qto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<Test1> getTest1Page(Test1Qto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<Test1> getSpecification(Test1Qto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getTest2_id())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("test2_id"), qto.getTest2_id()));
            }
            if (qto.getEnabled() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("enabled"), qto.getEnabled()));
            }
            return predicate;
        };
    }

    private static Test1Repository dao() {
        return SpringUtil.getBean(Test1Repository.class);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    @GeneratedValue(generator = "snowflake_generator")
    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("")
    @Column(name = "test2_id", nullable = true)
    public String getTest2_id() {
        return test2_id;
    }

//    @OneToOne
//    @JoinColumn(name = "test2_id",referencedColumnName = "id")    @OneToOne
//    @JoinColumn(name = "test2_id",referencedColumnName = "id")
//    private Test2 test2;


//    public Test2 getTest2() {
//        return test2;
//    }
//
//    public void setTest2(Test2 test2) {
//        this.test2 = test2;
//    }

    protected void setTest2_id(String test2_id) {
        this.test2_id = test2_id;
    }

    @Version
    @Comment("版本")
    @Column(name = "version", nullable = false)
    public Integer getVersion() {
        return version;
    }

    protected void setVersion(Integer version) {
        this.version = version;
    }

    @Comment("已启用")
    @Column(name = "enabled", nullable = false)
    public boolean isEnabled() {
        return enabled;
    }

    protected void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        Test1 other = (Test1) obj;
        return Objects.equals(id, other.id);
    }

    public Test1 create(Test1Nto test1Nto) {
        BusinessAssert.notNull(test1Nto, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数test1Nto");

        setId(test1Nto.getId());
        setEnabled(true);
        dao().save(this);
        return this;
    }

    public void update(Test1Eto test1Eto) {
        setVersion(test1Eto.getVersion());
        setEnabled(test1Eto.isEnabled());
    }

    public void enable() {
        setEnabled(true);
    }

    public void disable() {
        setEnabled(false);
    }

    public void delete() {
        dao().delete(this);
    }

}
