package com.bjgoodwill.hip.ds.cis.cds.entry.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.entry.entity.CisPharmacy;
import com.bjgoodwill.hip.ds.cis.cds.entry.service.CisPharmacyService;
import com.bjgoodwill.hip.ds.cis.cds.entry.service.internal.assembler.CisPharmacyAssembler;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisPharmacyEto;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisPharmacyNto;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisPharmacyQto;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisPharmacyTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cds.entry.service.CisPharmacyService")
@RequestMapping(value = "/api/cds/entry/cisPharmacy", produces = "application/json; charset=utf-8")
public class CisPharmacyServiceImpl implements CisPharmacyService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisPharmacyTo> getCisPharmacies(CisPharmacyQto cisPharmacyQto) {
        return CisPharmacyAssembler.toTos(CisPharmacy.getCisPharmacies(cisPharmacyQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisPharmacyTo> getCisPharmacyPage(CisPharmacyQto cisPharmacyQto) {
        Page<CisPharmacy> page = CisPharmacy.getCisPharmacyPage(cisPharmacyQto);
        Page<CisPharmacyTo> result = page.map(CisPharmacyAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisPharmacyTo getCisPharmacyById(String id) {
        return CisPharmacyAssembler.toTo(CisPharmacy.getCisPharmacyById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisPharmacyTo createCisPharmacy(CisPharmacyNto cisPharmacyNto) {
        CisPharmacy cisPharmacy = new CisPharmacy();
        cisPharmacy = cisPharmacy.create(cisPharmacyNto);
        return CisPharmacyAssembler.toTo(cisPharmacy);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisPharmacy(String id, CisPharmacyEto cisPharmacyEto) {
        Optional<CisPharmacy> cisPharmacyOptional = CisPharmacy.getCisPharmacyById(id);
        cisPharmacyOptional.ifPresent(cisPharmacy -> cisPharmacy.update(cisPharmacyEto));
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}