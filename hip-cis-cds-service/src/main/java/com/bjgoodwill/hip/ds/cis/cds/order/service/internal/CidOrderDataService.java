package com.bjgoodwill.hip.ds.cis.cds.order.service.internal;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.enums.dict.DictCodeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.order.entity.CisOrderCommon;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.proxy.CdsDictElementServiceProxy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("com.bjgoodwill.hip.ds.cis.cds.order.service.internal.CidOrderDataService")
public class CidOrderDataService {

    private Map<String, Map<String, String>> dictApplyDicts;

    private CdsDictElementServiceProxy dictElementServiceProxy() {
        return SpringUtil.getBean(CdsDictElementServiceProxy.class);
    }

    private Map<String, Map<String, String>> getServiceDicts() {
        if (dictApplyDicts == null) {
            List<String> dictCodes = List.of(new String[]{DictCodeEnum.入路.getCode(), DictCodeEnum.辅助器械.getCode(),
                    DictCodeEnum.部位.getCode(), DictCodeEnum.标本.getCode(), DictCodeEnum.方位.getCode(), DictCodeEnum.基本操作.getCode(),
                    DictCodeEnum.层数.getCode(), DictCodeEnum.范围.getCode()});
            dictApplyDicts = dictElementServiceProxy().getMuliDicts(dictCodes);
        }
        return dictApplyDicts;
    }

    private String getDictName(String dictCode, String dictElementCode) {
        Map<String, Map<String, String>> map = getServiceDicts();
        BusinessAssert.notEmpty(map.get(dictCode), CisCdsBusinessErrorEnum.BUS_CIS_CDS_00010, dictCode, "");

        BusinessAssert.hasText(map.get(dictCode).get(dictElementCode),
                CisCdsBusinessErrorEnum.BUS_CIS_CDS_00010, dictCode, dictElementCode);
        return map.get(dictCode).get(dictElementCode);
    }

    @Async
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void dispose(List<CisOrderCommonNto> list) {
        // 按照serviceItemCode数据处理
        List<CisOrderCommonNto> listDgimg = list.stream().filter(a -> !SystemTypeEnum.SPCOBS.equals(a.getSystemType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(listDgimg)) {
            saveDgimg(listDgimg);
        }
        // 检验
        List<CisOrderCommonNto> listSpcobs = list.stream().filter(a -> SystemTypeEnum.SPCOBS.equals(a.getSystemType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(listSpcobs)) {
            saveSpcobs(listSpcobs);
        }
    }

    /**
     * 按照serviceItemCode数据处理
     *
     * @param list
     */
    private void saveDgimg(List<CisOrderCommonNto> list) {
        Map<String, Long> summedValues = list.stream()
                .collect(Collectors.groupingBy(nto -> nto.getDocCode() + "_" + nto.getOrgCode() + "_" + nto.getServiceItemCode(),
                        Collectors.summingLong(CisOrderCommonNto::getIntegral)));
        // 结果进行处理，减少数据库操作次数
        summedValues.forEach((key, value) -> {
            CisOrderCommonNto nto = list.stream().filter(v -> key.equals(v.getDocCode() + "_" + v.getOrgCode() + "_" + v.getServiceItemCode())).findFirst().get();
            CisOrderCommon entity = CisOrderCommon.findCisDrugUsageFreqCommon(nto.getDocCode(), nto.getOrgCode(), nto.getServiceItemCode());
            if (entity != null) {
                entity.updateByIntegral(entity.getIntegral() + value);
            } else {
                setDictName(nto);
                nto.setIntegral(value);
                entity = new CisOrderCommon();
                nto.setSaveFlag(false);
                entity.create(nto);
            }
        });
    }

    /**
     * 处理检验数据
     *
     * @param list
     */
    private void saveSpcobs(List<CisOrderCommonNto> list) {
        Map<String, Long> summedValues = list.stream()
                .collect(Collectors.groupingBy(nto -> nto.getDocCode() + "_" + nto.getOrgCode() + "_" + nto.getServiceItemCode() + "_" + nto.getSpeciman(),
                        Collectors.summingLong(CisOrderCommonNto::getIntegral)));
        // 结果进行处理，减少数据库操作次数
        summedValues.forEach((key, value) -> {
            CisOrderCommonNto nto = list.stream().filter(v -> key.equals(v.getDocCode() + "_" + v.getOrgCode() + "_" + v.getServiceItemCode() + "_" + v.getSpeciman())).findFirst().get();
            CisOrderCommon entity = CisOrderCommon.findCisDrugUsageFreqCommonCustomize(nto.getDocCode(), nto.getOrgCode(), nto.getServiceItemCode(), nto.getSpeciman());
            if (entity != null) {
                entity.updateByIntegral(entity.getIntegral() + value);
            } else {
                setDictName(nto);
                nto.setIntegral(value);
                entity = new CisOrderCommon();
                nto.setSaveFlag(false);
                entity.create(nto);
            }
        });
    }

    /**
     * 设置字典名称
     *
     * @param nto
     */

    private void setDictName(CisOrderCommonNto nto) {
        if (StringUtils.isNotBlank(nto.getApproach())) {
            nto.setApproachName(getDictName(DictCodeEnum.入路.getCode(), nto.getApproach()));
        }
        if (StringUtils.isNotBlank(nto.getAssistiveDevices())) {
            nto.setAssistiveDevicesName(getDictName(DictCodeEnum.辅助器械.getCode(), nto.getAssistiveDevices()));
        }
        if (StringUtils.isNotBlank(nto.getPosition())) {
            nto.setPositionName(getDictName(DictCodeEnum.部位.getCode(), nto.getPosition()));
        }
        if (StringUtils.isNotBlank(nto.getSpeciman())) {
            nto.setSpecimanName(getDictName(DictCodeEnum.标本.getCode(), nto.getSpeciman()));
        }
        if (StringUtils.isNotBlank(nto.getAzimuth())) {
            nto.setAzimuthName(getDictName(DictCodeEnum.方位.getCode(), nto.getAzimuth()));
        }
        if (StringUtils.isNotBlank(nto.getBasicOperation())) {
            nto.setBasicOperationName(getDictName(DictCodeEnum.基本操作.getCode(), nto.getBasicOperation()));
        }
        if (StringUtils.isNotBlank(nto.getLayers())) {
            nto.setLayersName(getDictName(DictCodeEnum.层数.getCode(), nto.getLayers()));
        }
        if (StringUtils.isNotBlank(nto.getRange())) {
            nto.setRangeName(getDictName(DictCodeEnum.范围.getCode(), nto.getRange()));
        }
    }
}
