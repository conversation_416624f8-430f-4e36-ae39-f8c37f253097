package com.bjgoodwill.hip.ds.cis.cds.diagnose.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.repository.CisDiagnoseDocCommonRepository;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.*;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "个人常用诊断")
@DiscriminatorValue("2")
public class CisDiagnoseDocCommon extends CisDiagnoseCommon {

    // 医生编码
    private String docCode;

    public static Optional<CisDiagnoseDocCommon> getCisDiagnoseDocCommonById(String id) {
        return dao().findById(id);
    }

    public static List<CisDiagnoseDocCommon> getCisDiagnoseDocCommons(CisDiagnoseDocCommonQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisDiagnoseDocCommon> getCisDiagnoseDocCommonPage(CisDiagnoseDocCommonQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisDiagnoseDocCommon> getSpecification(CisDiagnoseDocCommonQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (qto.getIsFix() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isFix"), qto.getIsFix()));
            }
            if (qto.getEnabled() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("enabled"), qto.getEnabled()));
            }
            if (StringUtils.isNotBlank(qto.getDocCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("docCode"), qto.getDocCode()));
            }
            if (qto.getDiagnosisClass() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("diagnosisClass"), qto.getDiagnosisClass()));
            }
            return predicate;
        };
    }

    private static CisDiagnoseDocCommonRepository dao() {
        return SpringUtil.getBean(CisDiagnoseDocCommonRepository.class);
    }

    @Comment("医生编码")
    @Column(name = "doc_code", nullable = true)
    public String getDocCode() {
        return docCode;
    }

    protected void setDocCode(String docCode) {
        this.docCode = docCode;
    }

    @Override
    public CisDiagnoseCommon create(CisDiagnoseCommonNto cisDiagnoseCommonNto) {
        return create((CisDiagnoseDocCommonNto) cisDiagnoseCommonNto);
    }

    @Override
    public void update(CisDiagnoseCommonEto cisDiagnoseCommonEto) {
        update((CisDiagnoseDocCommonEto) cisDiagnoseCommonEto);
    }

    public CisDiagnoseDocCommon create(CisDiagnoseDocCommonNto cisDiagnoseDocCommonNto) {
        BusinessAssert.notNull(cisDiagnoseDocCommonNto, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisDiagnoseDocCommonNto");
        super.create(cisDiagnoseDocCommonNto);

        setDocCode(cisDiagnoseDocCommonNto.getDocCode());
        dao().save(this);
        return this;
    }

    public void update(CisDiagnoseDocCommonEto cisDiagnoseDocCommonEto) {
        super.update(cisDiagnoseDocCommonEto);
    }


}
