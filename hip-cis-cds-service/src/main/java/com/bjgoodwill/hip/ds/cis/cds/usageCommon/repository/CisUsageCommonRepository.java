package com.bjgoodwill.hip.ds.cis.cds.usageCommon.repository;

import com.bjgoodwill.hip.ds.cis.cds.usageCommon.entity.CisUsageCommon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.cds.UsageCommon.repository.CisUsageCommonRepository")
public interface CisUsageCommonRepository extends JpaRepository<CisUsageCommon, String>, JpaSpecificationExecutor<CisUsageCommon> {
    CisUsageCommon findByOrgCodeAndUsageCodeAndEnabled(String orgCode, String usageCode, boolean enabled);

}