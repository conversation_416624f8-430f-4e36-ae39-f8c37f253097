package com.bjgoodwill.hip.ds.cis.cds.entry.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cds.entry.entity.CisPharmacy;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisPharmacyTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisPharmacyAssembler {

    public static List<CisPharmacyTo> toTos(List<CisPharmacy> cisPharmacys) {
        return toTos(cisPharmacys, false);
    }

    public static List<CisPharmacyTo> toTos(List<CisPharmacy> cisPharmacys, boolean withAllParts) {
        Assert.notNull(cisPharmacys, "参数cisPharmacys不能为空！");

        List<CisPharmacyTo> tos = new ArrayList<>();
        for (CisPharmacy cisPharmacy : cisPharmacys)
            tos.add(toTo(cisPharmacy, withAllParts));
        return tos;
    }

    public static CisPharmacyTo toTo(CisPharmacy cisPharmacy) {
        return toTo(cisPharmacy, false);
    }

    /**
     * @generated
     */
    public static CisPharmacyTo toTo(CisPharmacy cisPharmacy, boolean withAllParts) {
        if (cisPharmacy == null)
            return null;
        CisPharmacyTo to = new CisPharmacyTo();
        to.setId(cisPharmacy.getId());
        to.setContent(cisPharmacy.getContent());
        to.setDoctorCode(cisPharmacy.getDoctorCode());
        to.setCreatedDate(cisPharmacy.getCreatedDate());
        to.setCreatedStaff(cisPharmacy.getCreatedStaff());
        to.setCreatedStaffName(cisPharmacy.getCreatedStaffName());
        to.setUpdatedDate(cisPharmacy.getUpdatedDate());
        to.setUpdatedStaff(cisPharmacy.getUpdatedStaff());
        to.setUpdatedStaffName(cisPharmacy.getUpdatedStaffName());
        to.setVersion(cisPharmacy.getVersion());

        if (withAllParts) {
        }
        return to;
    }

}