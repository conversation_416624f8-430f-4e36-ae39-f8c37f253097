package com.bjgoodwill.hip.ds.cis.cds.entry.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.ds.cis.cds.entry.repository.CisPharmacyRepository;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "医生药房")
@DiscriminatorValue("2")
public class CisPharmacy extends CisEntryTemp {

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    public static Optional<CisPharmacy> getCisPharmacyById(String id) {
        return dao().findById(id);
    }

    public static List<CisPharmacy> getCisPharmacies(CisPharmacyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisPharmacy> getCisPharmacyPage(CisPharmacyQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisPharmacy> getSpecification(CisPharmacyQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getContent())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("content"), qto.getContent()));
            }
            if (StringUtils.isNotBlank(qto.getDoctorCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("doctorCode"), qto.getDoctorCode()));
            }
            return predicate;
        };
    }

    private static CisPharmacyRepository dao() {
        return SpringUtil.getBean(CisPharmacyRepository.class);
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }


    @Override
    public CisEntryTemp create(CisEntryTempNto cisEntryTempNto) {
        return create((CisPharmacyNto) cisEntryTempNto);
    }

    @Override
    public void update(CisEntryTempEto cisEntryTempEto) {
        update((CisPharmacyEto) cisEntryTempEto);
    }

    public CisPharmacy create(CisPharmacyNto cisPharmacyNto) {
        Assert.notNull(cisPharmacyNto, "参数cisPharmacyNto不能为空！");
        super.create(cisPharmacyNto);

        dao().save(this);
        return this;
    }

    public void update(CisPharmacyEto cisPharmacyEto) {
        super.update(cisPharmacyEto);
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setVersion(cisPharmacyEto.getVersion());
    }

    public void delete() {
        super.delete();
        dao().delete(this);
    }

}
