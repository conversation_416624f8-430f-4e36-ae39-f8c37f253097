package com.bjgoodwill.hip.ds.cis.cds.usageCommon.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cds.usageCommon.entity.CisUsageCommon;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisUsageCommonAssembler {

    public static List<CisUsageCommonTo> toTos(List<CisUsageCommon> cisUsageCommons) {
        return toTos(cisUsageCommons, false);
    }

    public static List<CisUsageCommonTo> toTos(List<CisUsageCommon> cisUsageCommons, boolean withAllParts) {
        List<CisUsageCommonTo> tos = new ArrayList<>();
        for (CisUsageCommon cisUsageCommon : cisUsageCommons)
            tos.add(toTo(cisUsageCommon, withAllParts));
        return tos;
    }

    public static CisUsageCommonTo toTo(CisUsageCommon cisUsageCommon) {
        return toTo(cisUsageCommon, false);
    }

    /**
     * @generated
     */
    public static CisUsageCommonTo toTo(CisUsageCommon cisUsageCommon, boolean withAllParts) {
        if (cisUsageCommon == null)
            return null;
        CisUsageCommonTo to = new CisUsageCommonTo();
        to.setId(cisUsageCommon.getId());
        to.setUsageType(cisUsageCommon.getUsageType());
        to.setUsageCode(cisUsageCommon.getUsageCode());
        to.setUsageName(cisUsageCommon.getUsageName());
        to.setOrgCode(cisUsageCommon.getOrgCode());
        to.setUsageNo(cisUsageCommon.getUsageNo());
        to.setEnabled(cisUsageCommon.isEnabled());
        to.setCreatedDate(cisUsageCommon.getCreatedDate());
        to.setCreatedStaff(cisUsageCommon.getCreatedStaff());
        to.setCreatedStaffName(cisUsageCommon.getCreatedStaffName());
        to.setUpdatedDate(cisUsageCommon.getUpdatedDate());
        to.setUpdatedStaff(cisUsageCommon.getUpdatedStaff());
        to.setUpdatedStaffName(cisUsageCommon.getUpdatedStaffName());
        to.setIntegral(cisUsageCommon.getIntegral());

        if (withAllParts) {
        }
        return to;
    }

}