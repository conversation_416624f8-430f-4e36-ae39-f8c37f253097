package com.bjgoodwill.hip.ds.cis.cds.entry.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.entry.repository.CisEntryTempRepository;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisEntryTempEto;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisEntryTempNto;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisEntryTempQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "医生个性存储（词条，药房）")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", discriminatorType = DiscriminatorType.STRING, length = 20)
@Table(name = "cis_entry_temp", indexes = {@Index(name = "idx_cis_entry_temp_docCode", columnList = "doctor_code"),
        @Index(name = "idx_cis_entry_temp_system_type", columnList = "system_type")}, uniqueConstraints = {})
public abstract class CisEntryTemp {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("内容")
    @Column(name = "content", nullable = false)
    private String content;


    @Comment("医生编码")
    @Column(name = "doctor_code", nullable = false)
    private String doctorCode;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;

    @Version
    @Comment("版本")
    @Column(name = "version", nullable = true)
    private Integer version;

    public static Optional<CisEntryTemp> getCisEntryTempById(String id) {
        return dao().findById(id);
    }

    public static List<CisEntryTemp> getCisEntryTemps(CisEntryTempQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisEntryTemp> getCisEntryTempPage(CisEntryTempQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static CisEntryTemp newInstanceByNto(CisEntryTempNto cisEntryTempNto) {
        try {
            return (CisEntryTemp) Class.forName("com.bjgoodwill.hip.ds.cis.cds.entry.entity."
                    + StringUtils.removeEnd(cisEntryTempNto.getClass().getSimpleName(), "Nto")).getConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @generated
     */
    private static Specification<CisEntryTemp> getSpecification(CisEntryTempQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getContent())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("content"), qto.getContent()));
            }
            if (StringUtils.isNotBlank(qto.getDoctorCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("doctorCode"), qto.getDoctorCode()));
            }
            return predicate;
        };
    }

    private static CisEntryTempRepository dao() {
        return SpringUtil.getBean(CisEntryTempRepository.class);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    protected void setContent(String content) {
        this.content = content;
    }

    public String getDoctorCode() {
        return doctorCode;
    }

    protected void setDoctorCode(String doctorCode) {
        this.doctorCode = doctorCode;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public Integer getVersion() {
        return version;
    }

    protected void setVersion(Integer version) {
        this.version = version;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisEntryTemp other = (CisEntryTemp) obj;
        return Objects.equals(id, other.id);
    }

    public CisEntryTemp create(CisEntryTempNto cisEntryTempNto) {
        Assert.notNull(cisEntryTempNto, "参数cisEntryTempNto不能为空！");

        setId(cisEntryTempNto.getId());
        setContent(cisEntryTempNto.getContent());
        setDoctorCode(cisEntryTempNto.getDoctorCode());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        return this;
    }

    public void update(CisEntryTempEto cisEntryTempEto) {
        setContent(cisEntryTempEto.getContent());
        setUpdatedDate(LocalDateUtil.now());
    }

    public void delete() {
        BusinessAssert.isTrue(getCreatedStaff().equals(HIPLoginUtil.getStaffId()),
                CisCdsBusinessErrorEnum.BUS_CIS_CDS_0006,
                HIPLoginUtil.getStaffId());
    }

}
