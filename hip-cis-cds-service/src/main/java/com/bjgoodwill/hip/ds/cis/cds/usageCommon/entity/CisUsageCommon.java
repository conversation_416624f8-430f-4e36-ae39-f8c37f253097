package com.bjgoodwill.hip.ds.cis.cds.usageCommon.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.repository.CisUsageCommonRepository;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonEto;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "科室常用用法")
@Table(name = "cis_usage_common", indexes = {@Index(name = "IDX_cis_usage_common_org_code_enabled", columnList = "orgCode,enabled")}, uniqueConstraints = {})
public class CisUsageCommon {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("用法类型")
    @Column(name = "usage_type", nullable = true)
    private String usageType;


    @Comment("用法编码")
    @Column(name = "usage_code", nullable = true)
    private String usageCode;


    @Comment("用法名称")
    @Column(name = "usage_name", nullable = true)
    private String usageName;


    @Comment("科室编码")
    @Column(name = "org_code", nullable = true)
    private String orgCode;


    @Comment("序号")
    @Column(name = "usage_no", nullable = true)
    private String usageNo;

    @Comment("权重")
    @Column(name = "integral", nullable = true)
    private Long integral;

    @Comment("已启用")
    @Column(name = "enabled", nullable = false)
    private boolean enabled;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;

    public static Optional<CisUsageCommon> getCisUsageCommonById(String id) {
        return dao().findById(id);
    }

    public static List<CisUsageCommon> getCisUsageCommons(CisUsageCommonQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisUsageCommon> getCisUsageCommonPage(CisUsageCommonQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisUsageCommon> getSpecification(CisUsageCommonQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getText())) {
                String text = "%" + qto.getText() + "%";
                Predicate usageCodePredicate = criteriaBuilder.like(root.get("usageCode"), text);
                Predicate usageNamePredicate = criteriaBuilder.like(root.get("usageName"), text);
                predicate = criteriaBuilder.or(usageCodePredicate, usageNamePredicate);
            }
            if (StringUtils.isNotBlank(qto.getUsageType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("usageType"), qto.getUsageType()));
            }
            if (StringUtils.isNotBlank(qto.getUsageCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("usageCode"), qto.getUsageCode()));
            }
            if (StringUtils.isNotBlank(qto.getUsageName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("usageName"), qto.getUsageName()));
            }
            if (StringUtils.isNotBlank(qto.getOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgCode"), qto.getOrgCode()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("enabled"), true));
            return predicate;
        };
    }

    private static CisUsageCommonRepository dao() {
        return SpringUtil.getBean(CisUsageCommonRepository.class);
    }

    public static CisUsageCommon findCisUsageCommonByOrgCode(String orgCode, String usageCode) {
        return dao().findByOrgCodeAndUsageCodeAndEnabled(orgCode, usageCode, true);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getUsageType() {
        return usageType;
    }

    protected void setUsageType(String usageType) {
        this.usageType = usageType;
    }

    public String getUsageCode() {
        return usageCode;
    }

    protected void setUsageCode(String usageCode) {
        this.usageCode = usageCode;
    }

    public String getUsageName() {
        return usageName;
    }

    protected void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    protected void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getUsageNo() {
        return usageNo;
    }

    protected void setUsageNo(String usageNo) {
        this.usageNo = usageNo;
    }

    public boolean isEnabled() {
        return enabled;
    }

    protected void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public Long getIntegral() {
        return integral;
    }

    public void setIntegral(Long integral) {
        this.integral = integral;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisUsageCommon other = (CisUsageCommon) obj;
        return Objects.equals(id, other.id);
    }

    public CisUsageCommon create(CisUsageCommonNto cisUsageCommonNto) {
        BusinessAssert.notNull(cisUsageCommonNto, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisUsageCommonNto");

        setId(cisUsageCommonNto.getId());
        setUsageType(cisUsageCommonNto.getUsageType());
        setUsageCode(cisUsageCommonNto.getUsageCode());
        setUsageName(cisUsageCommonNto.getUsageName());
        setOrgCode(cisUsageCommonNto.getOrgCode());
        setUsageNo(cisUsageCommonNto.getUsageNo());
        setEnabled(true);
        setIntegral(cisUsageCommonNto.getIntegral());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisUsageCommonEto cisUsageCommonEto) {
        setUsageType(cisUsageCommonEto.getUsageType());
        setUsageCode(cisUsageCommonEto.getUsageCode());
        setUsageName(cisUsageCommonEto.getUsageName());
        setOrgCode(cisUsageCommonEto.getOrgCode());
        setUsageNo(cisUsageCommonEto.getUsageNo());
        setEnabled(cisUsageCommonEto.isEnabled());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void enable() {
        setEnabled(true);
    }

    public void disable() {
        setEnabled(false);
    }

    public void delete() {
        dao().delete(this);
    }

    public CisUsageCommon updateByIntegral(Long integral) {
        BusinessAssert.notNull(integral, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数integral");
        setIntegral(integral);
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        return this;
    }
}
