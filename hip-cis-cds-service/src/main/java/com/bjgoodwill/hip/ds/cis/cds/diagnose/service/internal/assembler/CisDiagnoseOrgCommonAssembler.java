package com.bjgoodwill.hip.ds.cis.cds.diagnose.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.entity.CisDiagnoseOrgCommon;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseOrgCommonTo;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;

import java.util.ArrayList;
import java.util.List;

public abstract class CisDiagnoseOrgCommonAssembler {

    public static List<CisDiagnoseOrgCommonTo> toTos(List<CisDiagnoseOrgCommon> cisDiagnoseOrgCommons) {
        return toTos(cisDiagnoseOrgCommons, false);
    }

    public static List<CisDiagnoseOrgCommonTo> toTos(List<CisDiagnoseOrgCommon> cisDiagnoseOrgCommons, boolean withAllParts) {
        BusinessAssert.notNull(cisDiagnoseOrgCommons, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisDiagnoseOrgCommons");

        List<CisDiagnoseOrgCommonTo> tos = new ArrayList<>();
        for (CisDiagnoseOrgCommon cisDiagnoseOrgCommon : cisDiagnoseOrgCommons)
            tos.add(toTo(cisDiagnoseOrgCommon, withAllParts));
        return tos;
    }

    public static CisDiagnoseOrgCommonTo toTo(CisDiagnoseOrgCommon cisDiagnoseOrgCommon) {
        return toTo(cisDiagnoseOrgCommon, false);
    }

    /**
     * @generated
     */
    public static CisDiagnoseOrgCommonTo toTo(CisDiagnoseOrgCommon cisDiagnoseOrgCommon, boolean withAllParts) {
        if (cisDiagnoseOrgCommon == null)
            return null;
        CisDiagnoseOrgCommonTo to = new CisDiagnoseOrgCommonTo();
        to.setId(cisDiagnoseOrgCommon.getId());
        to.setDiagnoseCode(cisDiagnoseOrgCommon.getDiagnoseCode());
        to.setDiagnoseName(cisDiagnoseOrgCommon.getDiagnoseName());
        to.setIntegral(cisDiagnoseOrgCommon.getIntegral());
        to.setIsFix(cisDiagnoseOrgCommon.getIsFix());
        to.setCreatedStaff(cisDiagnoseOrgCommon.getCreatedStaff());
        to.setCreatedDate(cisDiagnoseOrgCommon.getCreatedDate());
        to.setUpdatedStaff(cisDiagnoseOrgCommon.getUpdatedStaff());
        to.setUpdatedDate(cisDiagnoseOrgCommon.getUpdatedDate());
        to.setVersion(cisDiagnoseOrgCommon.getVersion());
        to.setEnabled(cisDiagnoseOrgCommon.isEnabled());
        to.setOrgCode(cisDiagnoseOrgCommon.getOrgCode());

        to.setPrefix(cisDiagnoseOrgCommon.getPrefix());
        to.setSuffix(cisDiagnoseOrgCommon.getSuffix());
        to.setDiagnosisClass(cisDiagnoseOrgCommon.getDiagnosisClass());
        if (withAllParts) {
        }
        return to;
    }

}