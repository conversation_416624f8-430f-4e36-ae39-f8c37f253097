package com.bjgoodwill.hip.ds.cis.cds.order.repository;

import com.bjgoodwill.hip.ds.cis.cds.order.entity.CisOrderCommon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.cds.order.repository.CisOrderCommonRepository")
public interface CisOrderCommonRepository extends JpaRepository<CisOrderCommon, String>, JpaSpecificationExecutor<CisOrderCommon> {
    CisOrderCommon findByDocCodeAndOrgCodeAndServiceItemCodeAndSaveFlag(String docCode, String orgCode, String serviceItemCode, boolean saveFlag);

    CisOrderCommon findByDocCodeAndOrgCodeAndServiceItemCodeAndSpecimanAndSaveFlag(String docCode, String orgCode, String serviceItemCode, String speciman, boolean saveFlag);

    List<CisOrderCommon> findByDocCodeAndOrgCodeAndSaveFlag(String docCode, String orgCode, boolean saveFlag);
}