package com.bjgoodwill.hip.ds.cis.cds.order.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.order.repository.CisOrderRecordRepository;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderRecordNto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderRecordQto;
import com.bjgoodwill.hip.jpa.core.SnowflakeIdGenerator;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "常用医嘱记录日志")
@Table(name = "cis_order_record", indexes = {}, uniqueConstraints = {})
public class CisOrderRecord {

    // 标识
    private String id;
    // 创建的人员
    private String createdStaff;
    // 创建的时间
    private LocalDateTime createdDate;
    // 记录时间
    private LocalDateTime recordDateTime;

    public static Optional<CisOrderRecord> getCisOrderRecordById(String id) {
        return dao().findById(id);
    }

    public static List<CisOrderRecord> getCisOrderRecords(CisOrderRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisOrderRecord> getCisOrderRecordPage(CisOrderRecordQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisOrderRecord> getSpecification(CisOrderRecordQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            return predicate;
        };
    }

    private static CisOrderRecordRepository dao() {
        return SpringUtil.getBean(CisOrderRecordRepository.class);
    }

    @Id
    @GeneratedValue(generator = "snowflake_generator")
    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("记录时间")
    @Column(name = "record_date_time", nullable = true)
    public LocalDateTime getRecordDateTime() {
        return recordDateTime;
    }

    protected void setRecordDateTime(LocalDateTime recordDateTime) {
        this.recordDateTime = recordDateTime;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOrderRecord other = (CisOrderRecord) obj;
        return Objects.equals(id, other.id);
    }

    public CisOrderRecord create(CisOrderRecordNto cisOrderRecordNto) {
        BusinessAssert.notNull(cisOrderRecordNto, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisOrderRecordNto");

        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedDate(LocalDateUtil.now());
        setRecordDateTime(cisOrderRecordNto.getRecordDateTime());
        dao().save(this);
        return this;
    }

    public void delete() {
        dao().delete(this);
    }

}
