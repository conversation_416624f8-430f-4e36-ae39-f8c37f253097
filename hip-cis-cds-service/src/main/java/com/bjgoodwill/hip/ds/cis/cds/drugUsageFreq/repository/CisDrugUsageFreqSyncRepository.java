package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.repository;

import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.entity.CisDrugUsageFreqSync;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.repository.CisDrugUsageFreqSyncRepository")
public interface CisDrugUsageFreqSyncRepository extends JpaRepository<CisDrugUsageFreqSync, String>, JpaSpecificationExecutor<CisDrugUsageFreqSync> {
    List<CisDrugUsageFreqSync> findCisDrugUsageFreqSyncByCreatedDateAfter(LocalDateTime dateTime);
}