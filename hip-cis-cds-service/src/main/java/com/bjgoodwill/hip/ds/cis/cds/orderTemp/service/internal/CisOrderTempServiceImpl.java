package com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.internal;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.exception.BusinessException;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.enmus.TempRangeEnum;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.entity.CisOrderTemp;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.entity.CisOrderTempDetail;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.CisOrderTempChargeService;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.CisOrderTempService;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.internal.assembler.CisOrderTempAssembler;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.internal.assembler.CisOrderTempDetailAssembler;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.*;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController("com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.CisOrderTempService")
@RequestMapping(value = "/api/orderTemp/orderTemp/cisOrderTemp", produces = "application/json; charset=utf-8")
public class CisOrderTempServiceImpl implements CisOrderTempService {

    @Resource
    private CisOrderTempChargeService cisOrderTempChargeService;

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderTempTo> getCisOrderTemps(CisOrderTempQto cisOrderTempQto) {
        return CisOrderTempAssembler.toTos(CisOrderTemp.getCisOrderTemps(cisOrderTempQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisOrderTempTo> getCisOrderTempPage(CisOrderTempQto cisOrderTempQto) {
        Page<CisOrderTemp> page = CisOrderTemp.getCisOrderTempPage(cisOrderTempQto);
        Page<CisOrderTempTo> result = page.map(CisOrderTempAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisOrderTempTo getCisOrderTempById(String id) {
        return CisOrderTempAssembler.toTo(CisOrderTemp.getCisOrderTempById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOrderTempTo createCisOrderTemp(CisOrderTempNto cisOrderTempNto) {
        int sortNo = Optional.ofNullable(CisOrderTemp.findMaxSortNoByParentId(cisOrderTempNto.getParentId())).orElse(0);
        cisOrderTempNto.setSortNo(sortNo + 1);
        CisOrderTemp cisOrderTemp = new CisOrderTemp();
        return CisOrderTempAssembler.toTo(cisOrderTemp.create(cisOrderTempNto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderTemp(String id, CisOrderTempEto cisOrderTempEto) {
        Optional<CisOrderTemp> cisOrderTempOptional = CisOrderTemp.getCisOrderTempById(id);
        cisOrderTempOptional.ifPresent(cisOrderTemp -> {
            cisOrderTemp.update(cisOrderTempEto);
            if (CollectionUtils.isNotEmpty(cisOrderTempEto.getDeleteDetailIds())) {
                deleteBatchCisOrderTempDetail(cisOrderTemp.getId(), cisOrderTempEto.getDeleteDetailIds());
            }
            if (CollectionUtils.isNotEmpty(cisOrderTempEto.getDetailEtos())) {
                updateCisOrderTempDetails(cisOrderTempEto.getDetailEtos());
            }
            if (CollectionUtils.isNotEmpty(cisOrderTempEto.getDetailNtos())) {
                addCisOrderTempDetail(cisOrderTemp.getId(), cisOrderTempEto.getDetailNtos());
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOrderTemp(String id) {
        Optional<CisOrderTemp> cisOrderTempOptional = CisOrderTemp.getCisOrderTempById(id);
        cisOrderTempOptional.ifPresent(cisOrderTemp -> cisOrderTemp.delete());
    }

    /**
     * 更新CisOrderTempDetail的信息。
     * <p>
     * 此方法用于根据提供的ID和明细ID更新对应的组套临时详情信息。它首先尝试根据ID获取组套信息，
     * 然后在组套的详情中找到对应的明细，最后更新该明细的信息。
     *
     * @param id                    组套的唯一标识符。
     * @param detailId              明细的唯一标识符。
     * @param cisOrderTempDetailEto 包含更新后明细信息的实体。
     */
    //region 修改明细
    //修改明细
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderTempDetail(String id, String detailId, CisOrderTempDetailEto cisOrderTempDetailEto) {
        getCisOrderTempDetail(id, detailId).update(cisOrderTempDetailEto);
    }

    //endregion


    //region 删除组套明细
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOrderTempDetail(String id, String detailId) {
        getCisOrderTempDetail(id, detailId).delete();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteBatchCisOrderTempDetail(String id, List<String> detailIds) {
        for (String detailId : detailIds) {
            getCisOrderTempDetail(id, detailId).delete();
        }
    }

    //endregion

    /**
     * 添加组套临时详情信息。
     *
     * @param id                     组套的唯一标识符。
     * @param cisOrderTempDetailNtos 组套的详细信息列表，需要添加到组套中。
     * @return 返回更新后的组套临时对象。
     * <p>
     * 此方法首先验证传入的组套详细信息列表不为空，然后根据ID尝试获取组套信息。
     * 如果组套信息不存在，则抛出异常。如果组套信息存在，则将详细信息添加到组套中，
     * 并返回更新后的组套临时对象。
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOrderTempTo addCisOrderTempDetail(String id, List<CisOrderTempDetailNto> cisOrderTempDetailNtos) {
        // 验证传入的组套详细信息列表不为空
        BusinessAssert.notEmpty(cisOrderTempDetailNtos, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "组套明细");
        // 根据ID尝试获取组套信息，如果不存在则抛出异常
        CisOrderTemp cisOrderTemp = CisOrderTemp.getCisOrderTempById(id)
                .orElseThrow(() -> new BusinessException(CisCdsBusinessErrorEnum.BUS_CIS_CDS_0005, "组套"));
        BusinessAssert.isFalse(cisOrderTemp.getGroupFlag(), CisCdsBusinessErrorEnum.BUS_CIS_CDS_0007, "组套明细");

        // 获取最大排序号
        //BigDecimal sortNo = Optional.ofNullable(CisOrderTempDetail.findMaxSortNoByCisOrderTempId(id))
        //        .map(BigDecimal::valueOf)
        //        .orElse(BigDecimal.ZERO);
        // 缓存前一个元素的 OrderClass
        SystemTypeEnum previousOrderClass = null;
        for (int i = 0; i < cisOrderTempDetailNtos.size(); i++) {
            CisOrderTempDetailNto tempDetailNto = cisOrderTempDetailNtos.get(i);
            // 全院组套下只能维护检验、检查类型医嘱
            if (TempRangeEnum.HOSPITAL.equals(cisOrderTemp.getTempRange())
                    && !SystemTypeEnum.SPCOBS.equals(tempDetailNto.getOrderClass())
                    && !SystemTypeEnum.DGIMG.equals(tempDetailNto.getOrderClass())) {
                throw new BusinessException(CisCdsBusinessErrorEnum.BUS_CIS_CDS_0008,
                        TempRangeEnum.HOSPITAL.getName(), SystemTypeEnum.SPCOBS.getName(), SystemTypeEnum.DGIMG.getName());
            }
            if (i > 0) {
                // 草药类型医嘱不能与其他类型保存到同一组套
                if (SystemTypeEnum.CDRUG.equals(tempDetailNto.getOrderClass())
                        && !tempDetailNto.getOrderClass().equals(previousOrderClass)) {
                    throw new BusinessException(CisCdsBusinessErrorEnum.BUS_CIS_CDS_0009, SystemTypeEnum.CDRUG.getName());
                }
            }
            // 更新排序号
            //sortNo = sortNo.add(BigDecimal.ONE);
            //tempDetailNto.setSortNo(sortNo.doubleValue());
            // 更新缓存
            previousOrderClass = tempDetailNto.getOrderClass();
            //组套明细费用列表，待插入
            if (CollectionUtils.isNotEmpty(tempDetailNto.getOrderTempChargeNtoList())) {
                cisOrderTempChargeService.batchCreateCisOrderTempCharge(tempDetailNto.getOrderTempChargeNtoList());
            }
        }
        // 添加详细信息到组套中，并将更新后的组套转换为相应的传输对象
        return CisOrderTempAssembler.toTo(cisOrderTemp.addDetails(cisOrderTempDetailNtos));
    }

    private CisOrderTempDetail getCisOrderTempDetail(String id, String detailId) {
        // 根据ID尝试获取组套信息，如果不存在则抛出异常
        CisOrderTemp cisOrderTemp = CisOrderTemp.getCisOrderTempById(id).orElse(null);
        BusinessAssert.notNull(cisOrderTemp, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0005, "组套");
        // 在获取的组套详情中，根据明细ID找到对应的明细，如果不存在则抛出异常
        CisOrderTempDetail cisOrderTempDetail = cisOrderTemp.getCisOrderTempDetails().stream()
                .filter(p -> p.getId().equals(detailId)).findFirst().orElse(null);
        BusinessAssert.notNull(cisOrderTempDetail, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0005, "组套明细");

        return cisOrderTempDetail;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderTempDetails(List<CisOrderTempDetailEto> cisOrderTempDetailEtos) {
        BusinessAssert.notNull(cisOrderTempDetailEtos, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "cisOrderTempDetailEtos");
        cisOrderTempDetailEtos.forEach(eto -> {
            getCisOrderTempDetail(eto.getCisOrderTempId(), eto.getId()).update(eto);
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderTempTo> getCisOrderTempsTree(CisOrderTempQto cisOrderTempQto) {
        List<CisOrderTempTo> allList = CisOrderTempAssembler.toTos(CisOrderTemp.getCisOrderTemps(cisOrderTempQto));
        List<CisOrderTempTo> reLList = new ArrayList<>();
        // 获取一级节点，父id与id相同为一级节点
        List<CisOrderTempTo> parentList = allList.stream().filter(a -> a.getId().equals(a.getParentId())).toList();
        parentList.forEach(a -> {
            reLList.add(a);
            addChild(a, allList);
        });
        return reLList;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderTempTo> getCisOrderTempsTreeAll(CisOrderTempQto cisOrderTempQto) {
        List<CisOrderTempTo> allList = CisOrderTempAssembler.toTos(CisOrderTemp.getCisOrderTempsAll(cisOrderTempQto));
        List<CisOrderTempTo> reLList = new ArrayList<>();
        // 获取一级节点，父id与id相同为一级节点
        List<CisOrderTempTo> parentList = allList.stream().filter(a -> a.getId().equals(a.getParentId())).toList();
        parentList.forEach(a -> {
            reLList.add(a);
            addChild(a, allList);
        });
        return reLList;
    }

    /**
     * 递归获取子节点
     *
     * @param cisOrderTempTo
     * @param allList
     */
    private void addChild(CisOrderTempTo cisOrderTempTo, List<CisOrderTempTo> allList) {
        List<CisOrderTempTo> tempList = allList.stream().filter(a ->
                !cisOrderTempTo.getId().equals(a.getId())
                        && cisOrderTempTo.getId().equals(a.getParentId())).collect(Collectors.toList());
        cisOrderTempTo.setChildrenCisOrderTempTos(tempList);
        tempList.forEach(a -> {
            addChild(a, allList);
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderTempDetailTo> getCisOrderTempDetails(CisOrderTempDetailQto cisOrderTempDetailQto) {
        return CisOrderTempDetailAssembler.toTos(CisOrderTempDetail.getCisOrderTempDetails(cisOrderTempDetailQto), true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderTempDetailTo> getCisOrderTempDetailsByIds(List<String> ids) {
        return CisOrderTempDetailAssembler.toTos(CisOrderTempDetail.getByIds(ids), true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderTempDetailTo> getCisOrderTempDetaisByTempId(String tempId) {
        return CisOrderTempDetailAssembler.toTos(CisOrderTempDetail.getByCisOrderTempId(tempId), true);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public Double getCisOrderTempDetailMaxSrotNo(String orderTempId) {
        BusinessAssert.notNull(orderTempId, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "组套id");
        BigDecimal sortNo = Optional.ofNullable(CisOrderTempDetail.findMaxSortNoByCisOrderTempId(orderTempId))
                .map(BigDecimal::valueOf)
                .orElse(BigDecimal.ZERO);
        return sortNo.doubleValue();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderTempSort(List<String> idList) {
        int[] sortNo = {1};
        idList.forEach(id ->
                CisOrderTemp.getCisOrderTempById(id)
                        .ifPresent(cisOrderTemp -> cisOrderTemp.sortNoUpdate(sortNo[0]++)));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderTempPath(CisOrderTempEto.OrderTempChangePathEto changePathEto) {
        CisOrderTemp.getCisOrderTempById(changePathEto.getId()).ifPresent(cisOrderTemp -> {
            CisOrderTempEto cisOrderTempEto = new CisOrderTempEto();
            cisOrderTempEto.setParentId(changePathEto.getNewParentId());
            cisOrderTempEto.setTempRange(changePathEto.getTempRange());
            int sortNo = Optional.ofNullable(CisOrderTemp.findMaxSortNoByParentId(changePathEto.getNewParentId())).orElse(0);
            cisOrderTempEto.setSortNo(sortNo + 1);
            cisOrderTemp.changePath(cisOrderTempEto);
        });
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}