package com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cds.orderTemp.entity.CisOrderTempCharge;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisOrderTempChargeAssembler {

    public static List<CisOrderTempChargeTo> toTos(List<CisOrderTempCharge> cisOrderTempCharges) {
        return toTos(cisOrderTempCharges, false);
    }

    public static List<CisOrderTempChargeTo> toTos(List<CisOrderTempCharge> cisOrderTempCharges, boolean withAllParts) {
        Assert.notNull(cisOrderTempCharges, "参数cisOrderTempCharges不能为空！");

        List<CisOrderTempChargeTo> tos = new ArrayList<>();
        for (CisOrderTempCharge cisOrderTempCharge : cisOrderTempCharges)
            tos.add(toTo(cisOrderTempCharge, withAllParts));
        return tos;
    }

    public static CisOrderTempChargeTo toTo(CisOrderTempCharge cisOrderTempCharge) {
        return toTo(cisOrderTempCharge, false);
    }

    /**
     * @generated
     */
    public static CisOrderTempChargeTo toTo(CisOrderTempCharge cisOrderTempCharge, boolean withAllParts) {
        if (cisOrderTempCharge == null)
            return null;
        CisOrderTempChargeTo to = new CisOrderTempChargeTo();
        to.setId(cisOrderTempCharge.getId());
        to.setTempDetailId(cisOrderTempCharge.getTempDetailId());
        to.setPriceItemCode(cisOrderTempCharge.getPriceItemCode());
        to.setPriceItemName(cisOrderTempCharge.getPriceItemName());
        to.setSystemItemClass(cisOrderTempCharge.getSystemItemClass());
        to.setIsFixed(cisOrderTempCharge.getIsFixed());
        to.setPackageSpec(cisOrderTempCharge.getPackageSpec());
        to.setPrice(cisOrderTempCharge.getPrice());
        to.setUnit(cisOrderTempCharge.getUnit());
        to.setUnitName(cisOrderTempCharge.getUnitName());
        to.setNum(cisOrderTempCharge.getNum());
        to.setChageAmount(cisOrderTempCharge.getChageAmount());
        to.setLimitConformFlag(cisOrderTempCharge.getLimitConformFlag());
        to.setBarCode(cisOrderTempCharge.getBarCode());
        to.setExecuteOrgCode(cisOrderTempCharge.getExecuteOrgCode());
        to.setExecuteOrgName(cisOrderTempCharge.getExecuteOrgName());
        to.setCreatedStaff(cisOrderTempCharge.getCreatedStaff());
        to.setCreatedStaffName(cisOrderTempCharge.getCreatedStaffName());
        to.setCreatedDate(cisOrderTempCharge.getCreatedDate());
        to.setUpdatedStaff(cisOrderTempCharge.getUpdatedStaff());
        to.setUpdatedStaffName(cisOrderTempCharge.getUpdatedStaffName());
        to.setUpdatedDate(cisOrderTempCharge.getUpdatedDate());

        if (withAllParts) {
        }
        return to;
    }

}