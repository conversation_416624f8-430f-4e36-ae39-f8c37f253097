package com.bjgoodwill.hip.ds.cis.cds.diagnose.service.internal;

import com.bjgoodwill.hip.ds.cis.cds.diagnose.entity.CisDiagnoseCommon;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.service.CisDiagnoseCommonService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.util.Optional;

public abstract class CisDiagnoseCommonServiceImpl implements CisDiagnoseCommonService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void enableCisDiagnoseCommon(String id) {
        Optional<CisDiagnoseCommon> cisDiagnoseCommonOptional = CisDiagnoseCommon.getCisDiagnoseCommonById(id);
        cisDiagnoseCommonOptional.ifPresent(cisDiagnoseCommon -> cisDiagnoseCommon.enable());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void disableCisDiagnoseCommon(String id) {
        Optional<CisDiagnoseCommon> cisDiagnoseCommonOptional = CisDiagnoseCommon.getCisDiagnoseCommonById(id);
        cisDiagnoseCommonOptional.ifPresent(cisDiagnoseCommon -> cisDiagnoseCommon.disable());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}