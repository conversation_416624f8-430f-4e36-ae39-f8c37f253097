package com.bjgoodwill.hip.ds.cis.cds.proxy;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictDto;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: 字典相关接口
 * @author: yanht
 * @create: 2025-1-15
 * @className: CdsDictElementServiceProxy
 * @description:
 **/
@Component
public class CdsDictElementServiceProxy {

    @Autowired
    private DictElementService dictElementService;

    public List<DictElementTo> getCustomDictElement(String dictCode) {
        return dictElementService.getCustomDictElement(dictCode);
    }

    public DictElementTo getCustomDictElement(String dictCode, String parentCode) {
        return dictElementService.getCustomDictElement(dictCode, parentCode);
    }

    public String getElementName(String dictCode, String parentCode) {
        // 根据字典代码和单位代码获取字典元素对象
        DictElementTo dictElementTo = getCustomDictElement(dictCode, parentCode);
        // 校验字典元素对象是否为空，确保业务数据的完整性
        BusinessAssert.notNull(dictElementTo, CisCdsBusinessErrorEnum.BUS_CIS_CDS_00010, dictCode, parentCode);
        // 返回字典元素的名称
        return dictElementTo.getElementName();
    }

    public List<DictDto> getCustomDictElementByDictCodes(List<String> dictCodes) {
        return dictElementService.getCustomDictElementByDictCodes(dictCodes);
    }

    /**
     * 根据字典代码列表获取多个字典数据
     * 该方法接收一个字典代码列表，返回一个映射，其中每个字典代码对应一个字典项映射
     * 字典项映射的键是字典元素代码，值是字典元素名称
     *
     * @param dictCodes 字典代码列表，用于查询字典数据
     * @return 返回一个映射，键是字典代码，值是另一个映射（键是字典元素代码，值是字典元素名称）
     */
    public Map<String, Map<String, String>> getMuliDicts(List<String> dictCodes) {

        // 根据字典代码列表获取自定义字典元素列表
        List<DictDto> items = getCustomDictElementByDictCodes(dictCodes);

        /**
         * 将 items 列表转换为一个 Map，其中键是每个 DictDto 对象的 dictCode，
         * 值是另一个 Map，包含该对象的 dictElementToList 中的 elementCode 和 elementName。
         *
         * @param items 包含 DictDto 对象的列表
         * @return 包含每个 DictDto 对象信息的 Map
         */
        return items.stream()
                .collect(Collectors.toMap(
                        DictDto::getDictCode, // 使用 dictCode 作为键
                        item -> item.getDictElementToList().stream()
                                .collect(Collectors.toMap(
                                        DictElementTo::getElementCode, // 使用 elementCode 作为键
                                        DictElementTo::getElementName  // 使用 elementName 作为值
                                ))
                ));
    }

}