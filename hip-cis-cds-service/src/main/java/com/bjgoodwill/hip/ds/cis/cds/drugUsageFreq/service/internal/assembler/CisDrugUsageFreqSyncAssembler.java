package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.entity.CisDrugUsageFreqSync;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqSyncTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisDrugUsageFreqSyncAssembler {

    public static List<CisDrugUsageFreqSyncTo> toTos(List<CisDrugUsageFreqSync> cisDrugUsageFreqSyncs) {
        return toTos(cisDrugUsageFreqSyncs, false);
    }

    public static List<CisDrugUsageFreqSyncTo> toTos(List<CisDrugUsageFreqSync> cisDrugUsageFreqSyncs, boolean withAllParts) {
        List<CisDrugUsageFreqSyncTo> tos = new ArrayList<>();
        for (CisDrugUsageFreqSync cisDrugUsageFreqSync : cisDrugUsageFreqSyncs)
            tos.add(toTo(cisDrugUsageFreqSync, withAllParts));
        return tos;
    }

    public static CisDrugUsageFreqSyncTo toTo(CisDrugUsageFreqSync cisDrugUsageFreqSync) {
        return toTo(cisDrugUsageFreqSync, false);
    }

    /**
     * @generated
     */
    public static CisDrugUsageFreqSyncTo toTo(CisDrugUsageFreqSync cisDrugUsageFreqSync, boolean withAllParts) {
        if (cisDrugUsageFreqSync == null)
            return null;
        CisDrugUsageFreqSyncTo to = new CisDrugUsageFreqSyncTo();
        to.setId(cisDrugUsageFreqSync.getId());
        to.setDeptCode(cisDrugUsageFreqSync.getDeptCode());
        to.setDrugCode(cisDrugUsageFreqSync.getDrugCode());
        to.setUsageCode(cisDrugUsageFreqSync.getUsageCode());
        to.setFrequencyCode(cisDrugUsageFreqSync.getFrequencyCode());
        to.setCreatedStaff(cisDrugUsageFreqSync.getCreatedStaff());
        to.setCreatedStaffName(cisDrugUsageFreqSync.getCreatedStaffName());
        to.setCreatedDate(cisDrugUsageFreqSync.getCreatedDate());
        to.setUpdatedStaff(cisDrugUsageFreqSync.getUpdatedStaff());
        to.setUpdatedStaffName(cisDrugUsageFreqSync.getUpdatedStaffName());
        to.setUpdatedDate(cisDrugUsageFreqSync.getUpdatedDate());

        if (withAllParts) {
        }
        return to;
    }

}