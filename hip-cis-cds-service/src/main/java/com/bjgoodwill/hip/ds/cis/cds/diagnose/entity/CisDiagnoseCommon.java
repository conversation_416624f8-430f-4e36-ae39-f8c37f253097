package com.bjgoodwill.hip.ds.cis.cds.diagnose.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.repository.CisDiagnoseCommonRepository;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseCommonEto;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.jpa.core.SnowflakeIdGenerator;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "常用诊断")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", discriminatorType = DiscriminatorType.STRING, length = 20)
@Table(name = "cis_diagnose_common", indexes = {}, uniqueConstraints = {})
public abstract class CisDiagnoseCommon {

    // 标识
    private String id;
    //中，西医 诊断
    private DiagnosisEnum diagnosisClass;
    // 诊断编码
    private String diagnoseCode;
    // 诊断名称
    private String diagnoseName;
    // 权重
    private Long integral;
    // 人工维护的
    private Boolean isFix;
    // 创建的人员
    private String createdStaff;
    // 创建的时间
    private LocalDateTime createdDate;
    // 最后修改的人员
    private String updatedStaff;
    // 最后修改的时间
    private LocalDateTime updatedDate;
    // 版本
    private Integer version;
    // 已启用
    private boolean enabled;

    private String prefix;

    private String suffix;

    public static Optional<CisDiagnoseCommon> getCisDiagnoseCommonById(String id) {
        return dao().findById(id);
    }

//    public static List<CisDiagnoseCommon> getCisDiagnoseCommons(CisDiagnoseCommonQto qto) {
//        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
//    }
//
//    public static Page<CisDiagnoseCommon> getCisDiagnoseCommonPage(CisDiagnoseCommonQto qto) {
//
//        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
//    }

    public static CisDiagnoseCommon newInstanceByNto(CisDiagnoseCommonNto cisDiagnoseCommonNto) {
        try {
            return (CisDiagnoseCommon) Class.forName("com.bjgoodwill.hip.ds.cis.cds.diagnose.entity."
                    + StringUtils.removeEnd(cisDiagnoseCommonNto.getClass().getSimpleName(), "Nto")).getConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @generated
     */
    private static Specification<CisDiagnoseCommon> getSpecification(CisDiagnoseCommonQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (qto.getIsFix() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isFix"), qto.getIsFix()));
            }
            if (qto.getEnabled() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("enabled"), qto.getEnabled()));
            }
            return predicate;
        };
    }

    private static CisDiagnoseCommonRepository dao() {
        return SpringUtil.getBean(CisDiagnoseCommonRepository.class);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    @GeneratedValue(generator = "snowflake_generator")
    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("诊断编码")
    @Column(name = "diagnose_code", nullable = false)
    public String getDiagnoseCode() {
        return diagnoseCode;
    }

    protected void setDiagnoseCode(String diagnoseCode) {
        this.diagnoseCode = diagnoseCode;
    }

    @Comment("诊断名称")
    @Column(name = "diagnose_name", nullable = true)
    public String getDiagnoseName() {
        return diagnoseName;
    }

    public void setDiagnoseName(String diagnoseName) {
        this.diagnoseName = diagnoseName;
    }

    @Comment("诊断前缀")
    @Column(name = "prefix", nullable = false)
    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    @Comment("诊断后缀")
    @Column(name = "suffix", nullable = false)
    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    @Comment("诊断类型")
    @Column(name = "diagnosis_class", nullable = false)
    public DiagnosisEnum getDiagnosisClass() {
        return diagnosisClass;
    }

    public void setDiagnosisClass(DiagnosisEnum diagnosisClass) {
        this.diagnosisClass = diagnosisClass;
    }


    @Comment("权重")
    @Column(name = "integral", nullable = true)
    public Long getIntegral() {
        return integral;
    }

    public void setIntegral(Long integral) {
        this.integral = integral;
    }

    @Comment("人工维护的")
    @Column(name = "is_fix", nullable = true)
    public Boolean getIsFix() {
        return isFix;
    }

    protected void setIsFix(Boolean isFix) {
        this.isFix = isFix;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Version
    @Comment("版本")
    @Column(name = "version", nullable = false)
    public Integer getVersion() {
        return version;
    }

    protected void setVersion(Integer version) {
        this.version = version;
    }

    @Comment("已启用")
    @Column(name = "enabled", nullable = false)
    public boolean isEnabled() {
        return enabled;
    }

    protected void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisDiagnoseCommon other = (CisDiagnoseCommon) obj;
        return Objects.equals(id, other.id);
    }

    public CisDiagnoseCommon create(CisDiagnoseCommonNto cisDiagnoseCommonNto) {
        BusinessAssert.notNull(cisDiagnoseCommonNto, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisDiagnoseCommonNto");

//        setId(cisDiagnoseCommonNto.getId());
        setDiagnoseCode(cisDiagnoseCommonNto.getDiagnoseCode());
        setDiagnoseName(cisDiagnoseCommonNto.getDiagnoseName());
        setIntegral(cisDiagnoseCommonNto.getIntegral());
        setIsFix(cisDiagnoseCommonNto.getIsFix());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedDate(LocalDateUtil.now());
        setPrefix(cisDiagnoseCommonNto.getPrefix());
        setSuffix(cisDiagnoseCommonNto.getSuffix());
        setDiagnosisClass(cisDiagnoseCommonNto.getDiagnosisClass());
        setEnabled(true);
        return this;
    }

    public void update(CisDiagnoseCommonEto cisDiagnoseCommonEto) {
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedDate(LocalDateUtil.now());
//        setVersion(cisDiagnoseCommonEto.getVersion());
        setEnabled(cisDiagnoseCommonEto.isEnabled());
    }

    public void enable() {
        setEnabled(true);
    }

    public void disable() {
        setEnabled(false);
    }


    public void updateByIntegral(Long integral, String diagnoseName) {
        BusinessAssert.notNull(integral, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数integral");
        setIntegral(integral);
        setDiagnoseName(diagnoseName);
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
    }

}
