package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.repository.CisDrugUsageFreqCommonRepository;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqCommonEto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "科室药品开立常用用法和频次信息统计")
@Table(name = "cis_drug_usage_freq_common", indexes = {@Index(name = "idx_dept_drug_usage_freq_code", columnList = "deptCode,drugCode,usageCode,frequencyCode"),
        @Index(name = "idx_dept_drug_code", columnList = "deptCode,drugCode")}, uniqueConstraints = {})
public class CisDrugUsageFreqCommon {

    // 标识
    private String id;
    // 开立科室编码
    private String deptCode;
    // 药品编码
    private String drugCode;
    // 用法编码
    private String usageCode;
    @Comment("用法名称")
    @Column(name = "usage_name", nullable = true)
    private String usageName;
    // 频次编码
    private String frequencyCode;
    @Comment("频次名称")
    @Column(name = "frequency_name", nullable = true)
    private String frequencyName;
    // 剂量
    private String dosage;
    // 剂量单位
    private String dosageUnit;
    @Comment("剂量单位名称")
    @Column(name = "dosage_unit_name", nullable = true)
    private String dosageUnitName;
    // 次数
    private Integer num;
    // 创建的时间
    private LocalDateTime createdDate;
    // 创建的人员
    private String createdStaff;
    // 创建的人员姓名
    private String createdStaffName;
    // 最后修改时间
    private LocalDateTime updatedDate;
    // 最后修改用户编码
    private String updatedStaff;
    // 最后修改人当时的姓名
    private String updatedStaffName;

    public static CisDrugUsageFreqCommon find(String deptCode, String drugCode, String usageCode, String frequencyCode) {
        BusinessAssert.hasText(deptCode, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数deptCode");
        BusinessAssert.hasText(drugCode, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数drugCode");
        BusinessAssert.hasText(usageCode, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数usageCode");
        BusinessAssert.hasText(frequencyCode, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数frequencyCode");
        return dao().findByDeptCodeAndDrugCodeAndUsageCodeAndFrequencyCode(deptCode, drugCode, usageCode, frequencyCode);
    }

    public static List<CisDrugUsageFreqCommon> findByDeptCodeAndDrugCode(String deptCode, String drugCode) {
        BusinessAssert.hasText(deptCode, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数deptCode");
        BusinessAssert.hasText(drugCode, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数drugCode");
        return dao().findByDeptCodeAndDrugCode(deptCode, drugCode);
    }

    private static CisDrugUsageFreqCommonRepository dao() {
        return SpringUtil.getBean(CisDrugUsageFreqCommonRepository.class);
    }

    public static Optional<CisDrugUsageFreqCommon> getCisDrugUsageFreqCommonById(String id) {
        return dao().findById(id);
    }

    public static List<CisDrugUsageFreqCommon> getCisDrugUsageFreqCommons(CisDrugUsageFreqCommonQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisDrugUsageFreqCommon> getCisDrugUsageFreqCommonPage(CisDrugUsageFreqCommonQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisDrugUsageFreqCommon> getSpecification(CisDrugUsageFreqCommonQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getDeptCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deptCode"), qto.getDeptCode()));
            }
            if (StringUtils.isNotBlank(qto.getDrugCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("drugCode"), qto.getDrugCode()));
            }
            if (StringUtils.isNotBlank(qto.getUsageCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("usageCode"), qto.getUsageCode()));
            }
            if (StringUtils.isNotBlank(qto.getFrequencyCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("frequencyCode"), qto.getFrequencyCode()));
            }
            if (qto.getNum() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("num"), qto.getNum()));
            }
            return predicate;
        };
    }

    public static CisDrugUsageFreqCommon findByDeptCodeAndDrugCodeAndUsageCodeAndFrequencyCodeAndDosage(String deptCode, String drugCode, String usageCode, String frequencyCode, String dosage) {
        return dao().findByDeptCodeAndDrugCodeAndUsageCodeAndFrequencyCodeAndDosage(deptCode, drugCode, usageCode, frequencyCode, dosage);
    }

    @Id
//    @GeneratedValue(generator = "snowflake_generator")
//    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("开立科室编码")
    @Column(name = "dept_code", nullable = false)
    public String getDeptCode() {
        return deptCode;
    }

    protected void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    @Comment("药品编码")
    @Column(name = "drug_code", nullable = false)
    public String getDrugCode() {
        return drugCode;
    }

    protected void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    @Comment("用法编码")
    @Column(name = "usage_code", nullable = false)
    public String getUsageCode() {
        return usageCode;
    }

    protected void setUsageCode(String usageCode) {
        this.usageCode = usageCode;
    }

    @Comment("频次编码")
    @Column(name = "frequency_code", nullable = false)
    public String getFrequencyCode() {
        return frequencyCode;
    }

    protected void setFrequencyCode(String frequencyCode) {
        this.frequencyCode = frequencyCode;
    }

    @Comment("剂量")
    @Column(name = "dosage", nullable = true)
    public String getDosage() {
        return dosage;
    }

    public void setDosage(String dosage) {
        this.dosage = dosage;
    }

    @Comment("剂量单位")
    @Column(name = "dosage_unit", nullable = true)
    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    @Comment("次数")
    @Column(name = "num", nullable = false)
    public Integer getNum() {
        return num;
    }

    protected void setNum(Integer num) {
        this.num = num;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Comment("创建人员")
    @Column(name = "created_staff", nullable = true)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建人员姓名")
    @Column(name = "created_staff_name", nullable = true)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("最后修改人员")
    @Column(name = "updated_staff", nullable = true)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Comment("最后修改人员姓名")
    @Column(name = "updated_staff_name", nullable = true)
    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisDrugUsageFreqCommon other = (CisDrugUsageFreqCommon) obj;
        return Objects.equals(id, other.id);
    }

    public CisDrugUsageFreqCommon create(CisDrugUsageFreqCommonNto cisDrugUsageFreqCommonNto) {
        BusinessAssert.notNull(cisDrugUsageFreqCommonNto, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisDrugUsageFreqCommonNto");

        setId(StringUtils.isNotBlank(cisDrugUsageFreqCommonNto.getId()) ? cisDrugUsageFreqCommonNto.getId() : HIPIDUtil.getNextIdString());
        setDeptCode(cisDrugUsageFreqCommonNto.getDeptCode());
        setDrugCode(cisDrugUsageFreqCommonNto.getDrugCode());
        setUsageCode(cisDrugUsageFreqCommonNto.getUsageCode());
        setUsageName(cisDrugUsageFreqCommonNto.getUsageName());
        setFrequencyCode(cisDrugUsageFreqCommonNto.getFrequencyCode());
        setFrequencyName(cisDrugUsageFreqCommonNto.getFrequencyName());
        setDosage(cisDrugUsageFreqCommonNto.getDosage());
        setDosageUnit(cisDrugUsageFreqCommonNto.getDosageUnit());
        setDosageUnitName(cisDrugUsageFreqCommonNto.getDosageUnitName());
        setNum(cisDrugUsageFreqCommonNto.getNum());
        setCreatedDate(LocalDateUtil.now());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
        dao().save(this);
        return this;
    }

    public CisDrugUsageFreqCommon updateByNum(Integer num) {
        BusinessAssert.notNull(num, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数num");
        setNum(num);
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        dao().save(this);
        return this;
    }

    public void update(CisDrugUsageFreqCommonEto cisDrugUsageFreqCommonEto) {
        setDeptCode(cisDrugUsageFreqCommonEto.getDeptCode());
        setDrugCode(cisDrugUsageFreqCommonEto.getDrugCode());
        setUsageCode(cisDrugUsageFreqCommonEto.getUsageCode());
        setUsageName(cisDrugUsageFreqCommonEto.getUsageName());
        setFrequencyCode(cisDrugUsageFreqCommonEto.getFrequencyCode());
        setFrequencyName(cisDrugUsageFreqCommonEto.getFrequencyName());
        setDosage(cisDrugUsageFreqCommonEto.getDosage());
        setDosageUnit(cisDrugUsageFreqCommonEto.getDosageUnit());
        setDosageUnitName(cisDrugUsageFreqCommonEto.getDosageUnitName());
        setNum(cisDrugUsageFreqCommonEto.getNum());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public void delete() {
        dao().delete(this);
    }
}
