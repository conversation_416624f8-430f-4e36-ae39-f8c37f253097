package com.bjgoodwill.hip.ds.cis.cds.diagnose.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.repository.CisDiagnoseBaseDataRepository;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseBaseDataNto;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseBaseDataQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "同步患者诊断基础数据")
@Table(name = "cis_diagnose_base_data", indexes = {}, uniqueConstraints = {})
public class CisDiagnoseBaseData {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("诊断编码")
    @Column(name = "diagnose_code", nullable = false)
    private String diagnoseCode;


    @Comment("诊断名称")
    @Column(name = "diagnose_name", nullable = true)
    private String diagnoseName;


    @Comment("科室编码")
    @Column(name = "org_code", nullable = false)
    private String orgCode;


    @Comment("医生编码")
    @Column(name = "doc_code", nullable = false)
    private String docCode;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = true)
    private LocalDateTime createdDate;

    public static Optional<CisDiagnoseBaseData> getCisDiagnoseBaseDataById(String id) {
        return dao().findById(id);
    }

    public static List<CisDiagnoseBaseData> getCisDiagnoseBaseDatas(CisDiagnoseBaseDataQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisDiagnoseBaseData> getCisDiagnoseBaseDataPage(CisDiagnoseBaseDataQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisDiagnoseBaseData> getSpecification(CisDiagnoseBaseDataQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getDiagnoseCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("diagnoseCode"), qto.getDiagnoseCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgCode"), qto.getOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getDocCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("docCode"), qto.getDocCode()));
            }
            return predicate;
        };
    }

    private static CisDiagnoseBaseDataRepository dao() {
        return SpringUtil.getBean(CisDiagnoseBaseDataRepository.class);
    }

    public static List<CisDiagnoseBaseData> findCisDiagnoseBaseDataByCreatedDateAfter(LocalDateTime dateTime) {
        return dao().findCisDiagnoseBaseDataByCreatedDateAfter(dateTime);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getDiagnoseCode() {
        return diagnoseCode;
    }

    protected void setDiagnoseCode(String diagnoseCode) {
        this.diagnoseCode = diagnoseCode;
    }

    public String getDiagnoseName() {
        return diagnoseName;
    }

    protected void setDiagnoseName(String diagnoseName) {
        this.diagnoseName = diagnoseName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    protected void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getDocCode() {
        return docCode;
    }

    protected void setDocCode(String docCode) {
        this.docCode = docCode;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisDiagnoseBaseData other = (CisDiagnoseBaseData) obj;
        return Objects.equals(id, other.id);
    }

    public CisDiagnoseBaseData create(CisDiagnoseBaseDataNto cisDiagnoseBaseDataNto) {
        Assert.notNull(cisDiagnoseBaseDataNto, "参数cisDiagnoseBaseDataNto不能为空！");

        setId(cisDiagnoseBaseDataNto.getId());
        setDiagnoseCode(cisDiagnoseBaseDataNto.getDiagnoseCode());
        setDiagnoseName(cisDiagnoseBaseDataNto.getDiagnoseName());
        setOrgCode(cisDiagnoseBaseDataNto.getOrgCode());
        setDocCode(cisDiagnoseBaseDataNto.getDocCode());
        setCreatedDate(cisDiagnoseBaseDataNto.getCreatedDate());
        dao().save(this);
        return this;
    }

    public void delete() {
        dao().delete(this);
    }
}
