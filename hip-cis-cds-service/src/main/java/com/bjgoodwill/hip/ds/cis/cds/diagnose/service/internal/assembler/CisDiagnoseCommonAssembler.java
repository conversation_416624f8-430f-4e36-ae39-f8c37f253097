package com.bjgoodwill.hip.ds.cis.cds.diagnose.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.entity.CisDiagnoseCommon;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.entity.CisDiagnoseDocCommon;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.entity.CisDiagnoseOrgCommon;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseCommonTo;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;

import java.util.ArrayList;
import java.util.List;

public abstract class CisDiagnoseCommonAssembler {

    public static List<CisDiagnoseCommonTo> toTos(List<CisDiagnoseCommon> cisDiagnoseCommons) {
        return toTos(cisDiagnoseCommons, false);
    }

    public static List<CisDiagnoseCommonTo> toTos(List<CisDiagnoseCommon> cisDiagnoseCommons, boolean withAllParts) {
        BusinessAssert.notNull(cisDiagnoseCommons, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisDiagnoseCommons");

        List<CisDiagnoseCommonTo> tos = new ArrayList<>();
        for (CisDiagnoseCommon cisDiagnoseCommon : cisDiagnoseCommons)
            tos.add(toTo(cisDiagnoseCommon, withAllParts));
        return tos;
    }

    public static CisDiagnoseCommonTo toTo(CisDiagnoseCommon cisDiagnoseCommon) {
        return toTo(cisDiagnoseCommon, false);
    }

    /**
     * @generated
     */
    public static CisDiagnoseCommonTo toTo(CisDiagnoseCommon cisDiagnoseCommon, boolean withAllParts) {
        if (cisDiagnoseCommon == null)
            return null;
        if (cisDiagnoseCommon instanceof CisDiagnoseOrgCommon) {
            return CisDiagnoseOrgCommonAssembler.toTo((CisDiagnoseOrgCommon) cisDiagnoseCommon, withAllParts);
        }
        if (cisDiagnoseCommon instanceof CisDiagnoseDocCommon) {
            return CisDiagnoseDocCommonAssembler.toTo((CisDiagnoseDocCommon) cisDiagnoseCommon, withAllParts);
        }
        return null;
    }

}