package com.bjgoodwill.hip.ds.cis.cds.order.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.order.entity.CisOrderRecord;
import com.bjgoodwill.hip.ds.cis.cds.order.service.CisOrderRecordService;
import com.bjgoodwill.hip.ds.cis.cds.order.service.internal.assembler.CisOrderRecordAssembler;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderRecordNto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderRecordQto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderRecordTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController("com.bjgoodwill.hip.ds.cis.cds.order.service.CisOrderRecordService")
@RequestMapping(value = "/api/cds/order/cisOrderRecord", produces = "application/json; charset=utf-8")
public class CisOrderRecordServiceImpl implements CisOrderRecordService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderRecordTo> getCisOrderRecords(CisOrderRecordQto cisOrderRecordQto) {
        return CisOrderRecordAssembler.toTos(CisOrderRecord.getCisOrderRecords(cisOrderRecordQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisOrderRecordTo> getCisOrderRecordPage(CisOrderRecordQto cisOrderRecordQto) {
        Page<CisOrderRecord> page = CisOrderRecord.getCisOrderRecordPage(cisOrderRecordQto);
        Page<CisOrderRecordTo> result = page.map(CisOrderRecordAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOrderRecordTo createCisOrderRecord(CisOrderRecordNto cisOrderRecordNto) {
        CisOrderRecord cisOrderRecord = new CisOrderRecord();
        cisOrderRecord = cisOrderRecord.create(cisOrderRecordNto);
        return CisOrderRecordAssembler.toTo(cisOrderRecord);
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}