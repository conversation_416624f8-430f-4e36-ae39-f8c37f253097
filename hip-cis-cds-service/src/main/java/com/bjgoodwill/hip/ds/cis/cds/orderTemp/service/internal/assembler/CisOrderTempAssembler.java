package com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.entity.CisOrderTemp;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisOrderTempAssembler {

    public static List<CisOrderTempTo> toTos(List<CisOrderTemp> cisOrderTemps) {
        return toTos(cisOrderTemps, false);
    }

    public static List<CisOrderTempTo> toTos(List<CisOrderTemp> cisOrderTemps, boolean withAllParts) {
        BusinessAssert.notNull(cisOrderTemps, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisOrderTemps");

        List<CisOrderTempTo> tos = new ArrayList<>();
        for (CisOrderTemp cisOrderTemp : cisOrderTemps)
            tos.add(toTo(cisOrderTemp, withAllParts));
        return tos;
    }

    public static CisOrderTempTo toTo(CisOrderTemp cisOrderTemp) {
        return toTo(cisOrderTemp, false);
    }

    /**
     * @generated
     */
    public static CisOrderTempTo toTo(CisOrderTemp cisOrderTemp, boolean withAllParts) {
        if (cisOrderTemp == null)
            return null;
        CisOrderTempTo to = new CisOrderTempTo();
        to.setId(cisOrderTemp.getId());
        to.setOrderTempName(cisOrderTemp.getOrderTempName());
        to.setTempType(cisOrderTemp.getTempType());
        to.setTempRange(cisOrderTemp.getTempRange());
        to.setInputPy(cisOrderTemp.getInputPy());
        to.setMnemonicCode(cisOrderTemp.getMnemonicCode());
        to.setTempRemark(cisOrderTemp.getTempRemark());
        to.setParentId(cisOrderTemp.getParentId());
        to.setGroupFlag(cisOrderTemp.getGroupFlag());
        to.setHospitalCode(cisOrderTemp.getHospitalCode());
        to.setHospitalName(cisOrderTemp.getHospitalName());
        to.setOrgCode(cisOrderTemp.getOrgCode());
        to.setOrgName(cisOrderTemp.getOrgName());
        to.setDoctorCode(cisOrderTemp.getDoctorCode());
        to.setDoctorName(cisOrderTemp.getDoctorName());
        to.setHospitalArea(cisOrderTemp.getHospitalArea());
        to.setHospitalAreaName(cisOrderTemp.getHospitalAreaName());
        to.setVersion(cisOrderTemp.getVersion());
        to.setDeleted(cisOrderTemp.isDeleted());
        to.setCreatedStaff(cisOrderTemp.getCreatedStaff());
        to.setCreatedStaffName(cisOrderTemp.getCreatedStaffName());
        to.setCreatedDate(cisOrderTemp.getCreatedDate());
        to.setUpdatedStaff(cisOrderTemp.getUpdatedStaff());
        to.setUpdatedStaffName(cisOrderTemp.getUpdatedStaffName());
        to.setUpdatedDate(cisOrderTemp.getUpdatedDate());
        to.setSystemType(cisOrderTemp.getSystemType());
        to.setSortNo(cisOrderTemp.getSortNo());
        to.setDeptCode(cisOrderTemp.getDeptCode());
        to.setDeptName(cisOrderTemp.getDeptName());
        to.setWorkGroupType(cisOrderTemp.getWorkGroupType());

        if (withAllParts) {
        }
        return to;
    }

}