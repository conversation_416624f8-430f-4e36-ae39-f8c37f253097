package com.bjgoodwill.hip.ds.cis.cds.diagnose.repository;

import com.bjgoodwill.hip.ds.cis.cds.diagnose.entity.CisDiagnoseDocCommon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.cds.diagnose.repository.CisDiagnoseDocCommonRepository")
public interface CisDiagnoseDocCommonRepository extends JpaRepository<CisDiagnoseDocCommon, String>, JpaSpecificationExecutor<CisDiagnoseDocCommon> {

}