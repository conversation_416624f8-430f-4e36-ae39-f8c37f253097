package com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.entity.CisOrderTempCharge;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.CisOrderTempChargeService;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.internal.assembler.CisOrderTempChargeAssembler;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeEto;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeNto;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeQto;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeTo;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cds.orderTemp.service.CisOrderTempChargeService")
@RequestMapping(value = "/api/orderTemp/orderTemp/cisOrderTempCharge", produces = "application/json; charset=utf-8")
public class CisOrderTempChargeServiceImpl implements CisOrderTempChargeService {
    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderTempChargeTo> getCisOrderTempCharges(CisOrderTempChargeQto cisOrderTempChargeQto) {
        return CisOrderTempChargeAssembler.toTos(CisOrderTempCharge.getCisOrderTempCharges(cisOrderTempChargeQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisOrderTempChargeTo> getCisOrderTempChargePage(CisOrderTempChargeQto cisOrderTempChargeQto) {
        Page<CisOrderTempCharge> page = CisOrderTempCharge.getCisOrderTempChargePage(cisOrderTempChargeQto);
        Page<CisOrderTempChargeTo> result = page.map(CisOrderTempChargeAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisOrderTempChargeTo getCisOrderTempChargeById(String id) {
        return CisOrderTempChargeAssembler.toTo(CisOrderTempCharge.getCisOrderTempChargeById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderTempChargeTo> listByTempDetailIds(List<String> tempDetailIds) {
        return CisOrderTempChargeAssembler.toTos(CisOrderTempCharge.listByTempDetailId(tempDetailIds));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void createCisOrderTempCharge(CisOrderTempChargeNto cisOrderTempChargeNto) {
        CisOrderTempCharge cisOrderTempCharge = new CisOrderTempCharge();
        cisOrderTempCharge = cisOrderTempCharge.create(cisOrderTempChargeNto);
        CisOrderTempChargeAssembler.toTo(cisOrderTempCharge);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void batchCreateCisOrderTempCharge(List<CisOrderTempChargeNto> cisOrderTempChargeNtos) {
        cisOrderTempChargeNtos.forEach(cisOrderTempChargeNto -> {
            CisOrderTempCharge cisOrderTempCharge = new CisOrderTempCharge();
            cisOrderTempCharge.create(cisOrderTempChargeNto);
        });

    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderTempCharge(String id, CisOrderTempChargeEto cisOrderTempChargeEto) {
        Optional<CisOrderTempCharge> cisOrderTempChargeOptional = CisOrderTempCharge.getCisOrderTempChargeById(id);
        cisOrderTempChargeOptional.ifPresent(cisOrderTempCharge -> cisOrderTempCharge.update(cisOrderTempChargeEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOrderTempCharge(String id) {
        Optional<CisOrderTempCharge> cisOrderTempChargeOptional = CisOrderTempCharge.getCisOrderTempChargeById(id);
        cisOrderTempChargeOptional.ifPresent(cisOrderTempCharge -> cisOrderTempCharge.delete());
    }
}