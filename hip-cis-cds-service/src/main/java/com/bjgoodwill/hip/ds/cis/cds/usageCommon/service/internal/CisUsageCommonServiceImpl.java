package com.bjgoodwill.hip.ds.cis.cds.usageCommon.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.entity.CisDiagnoseRecord;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseRecordNto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.service.CisDrugUsageFreqSyncService;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqSyncTo;
import com.bjgoodwill.hip.ds.cis.cds.enmus.RecordTypeEnum;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.entity.CisUsageCommon;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.service.CisUsageCommonService;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.service.internal.assembler.CisUsageCommonAssembler;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonEto;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.usageCommon.to.CisUsageCommonTo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RestController("com.bjgoodwill.hip.ds.cis.cds.usageCommon.service.CisUsageCommonService")
@RequestMapping(value = "/api/cds/usageCommon/cisUsageCommon", produces = "application/json; charset=utf-8")
public class CisUsageCommonServiceImpl implements CisUsageCommonService {

    @Autowired
    CisDrugUsageFreqSyncService cisDrugUsageFreqSyncService;

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisUsageCommonTo> getCisUsageCommons(CisUsageCommonQto cisUsageCommonQto) {
        return CisUsageCommonAssembler.toTos(CisUsageCommon.getCisUsageCommons(cisUsageCommonQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisUsageCommonTo> getCisUsageCommonPage(CisUsageCommonQto cisUsageCommonQto) {
        Page<CisUsageCommon> page = CisUsageCommon.getCisUsageCommonPage(cisUsageCommonQto);
        Page<CisUsageCommonTo> result = page.map(CisUsageCommonAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisUsageCommonTo getCisUsageCommonById(String id) {
        return CisUsageCommonAssembler.toTo(CisUsageCommon.getCisUsageCommonById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisUsageCommonTo createCisUsageCommon(CisUsageCommonNto cisUsageCommonNto) {
        CisUsageCommon cisUsageCommon = new CisUsageCommon();
        cisUsageCommon = cisUsageCommon.create(cisUsageCommonNto);
        return CisUsageCommonAssembler.toTo(cisUsageCommon);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisUsageCommon(String id, CisUsageCommonEto cisUsageCommonEto) {
        Optional<CisUsageCommon> cisUsageCommonOptional = CisUsageCommon.getCisUsageCommonById(id);
        cisUsageCommonOptional.ifPresent(cisUsageCommon -> cisUsageCommon.update(cisUsageCommonEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisUsageCommon(String id) {
        Optional<CisUsageCommon> cisUsageCommonOptional = CisUsageCommon.getCisUsageCommonById(id);
        cisUsageCommonOptional.ifPresent(cisUsageCommon -> cisUsageCommon.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void saveCisUsageCommons() {
        CisDiagnoseRecord record = CisDiagnoseRecord.getLastRecord(RecordTypeEnum.USAGECOMMON);

        LocalDateTime recordDateTime = (record != null) ? record.getRecordDateTime() : LocalDateTime.now().minusYears(1);
        //通过时间段查询用药申请单数据
        List<CisDrugUsageFreqSyncTo> drugApplyList = cisDrugUsageFreqSyncService.getCisDrugByCreateDate(recordDateTime);

        //依据科室分组
        Map<String, List<CisDrugUsageFreqSyncTo>> mapDept = drugApplyList.stream().collect(Collectors.groupingBy(CisDrugUsageFreqSyncTo::getDeptCode));
        for (Map.Entry<String, List<CisDrugUsageFreqSyncTo>> entry : mapDept.entrySet()) {
            List<CisUsageCommonNto> list = entry.getValue().parallelStream()
                    .flatMap(detail -> {
                        CisUsageCommonNto nto = new CisUsageCommonNto();
                        nto.setOrgCode(detail.getDeptCode());
                        nto.setUsageCode(detail.getUsageCode());
                        nto.setIntegral(1L);
                        return Stream.of(nto);
                    }).toList();
            dispose(list);
        }
        saveCisDiagnoseRecord();
    }

    public void dispose(List<CisUsageCommonNto> list) {
        Map<String, Long> summedValues = list.stream()
                .collect(Collectors.groupingBy(nto -> nto.getOrgCode() + "_" + nto.getUsageCode(),
                        Collectors.summingLong(CisUsageCommonNto::getIntegral)));
        summedValues.forEach((key, value) -> {
            CisUsageCommonNto nto = list.stream().filter(v -> key.equals(v.getOrgCode() + "_" + v.getUsageCode())).findFirst().get();
            CisUsageCommon entity = CisUsageCommon.findCisUsageCommonByOrgCode(nto.getOrgCode(), nto.getUsageCode());
            if (entity != null) {
                entity.updateByIntegral(entity.getIntegral() + value);
            } else {
                nto.setIntegral(value);
                entity = new CisUsageCommon();
                entity.create(nto);
            }
        });
    }

    public void saveCisDiagnoseRecord() {
        CisDiagnoseRecordNto nto = new CisDiagnoseRecordNto();
        nto.setRecordDateTime(LocalDateTime.now());
        nto.setRecordType(RecordTypeEnum.USAGECOMMON);
        CisDiagnoseRecord cisDiagnoseRecord = new CisDiagnoseRecord();
        cisDiagnoseRecord.create(nto);
    }
}