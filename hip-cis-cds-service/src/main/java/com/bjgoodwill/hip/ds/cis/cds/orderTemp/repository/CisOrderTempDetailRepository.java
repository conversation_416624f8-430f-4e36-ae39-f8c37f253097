package com.bjgoodwill.hip.ds.cis.cds.orderTemp.repository;

import com.bjgoodwill.hip.ds.cis.cds.orderTemp.entity.CisOrderTempDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.cds.orderTemp.repository.CisOrderTempDetailRepository")
public interface CisOrderTempDetailRepository extends JpaRepository<CisOrderTempDetail, String>, JpaSpecificationExecutor<CisOrderTempDetail> {

    List<CisOrderTempDetail> findByCisOrderTempId(String cisOrderTempId);

    Page<CisOrderTempDetail> findByCisOrderTempId(String cisOrderTempId, Pageable pageable);

    boolean existsByCisOrderTempId(String cisOrderTempId);

    void deleteByCisOrderTempId(String cisOrderTempId);

    @Query(value = "SELECT max(a.sortNo) FROM CisOrderTempDetail a WHERE a.cisOrderTempId = ?1")
    Double findMaxSortNoByCisOrderTempId(String cisOrderTempId);

    List<CisOrderTempDetail> findByIdIn(List<String> ids);
}