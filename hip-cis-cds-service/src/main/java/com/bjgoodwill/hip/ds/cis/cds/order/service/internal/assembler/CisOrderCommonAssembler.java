package com.bjgoodwill.hip.ds.cis.cds.order.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cds.order.entity.CisOrderCommon;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonTo;

import java.util.ArrayList;
import java.util.List;

public abstract class CisOrderCommonAssembler {

    public static List<CisOrderCommonTo> toTos(List<CisOrderCommon> cisOrderCommons) {
        return toTos(cisOrderCommons, false);
    }

    public static List<CisOrderCommonTo> toTos(List<CisOrderCommon> cisOrderCommons, boolean withAllParts) {
        List<CisOrderCommonTo> tos = new ArrayList<>();
        for (CisOrderCommon cisOrderCommon : cisOrderCommons)
            tos.add(toTo(cisOrderCommon, withAllParts));
        return tos;
    }

    public static CisOrderCommonTo toTo(CisOrderCommon cisOrderCommon) {
        return toTo(cisOrderCommon, false);
    }

    /**
     * @generated
     */
    public static CisOrderCommonTo toTo(CisOrderCommon cisOrderCommon, boolean withAllParts) {
        if (cisOrderCommon == null)
            return null;
        CisOrderCommonTo to = new CisOrderCommonTo();
        to.setId(cisOrderCommon.getId());
        to.setIntegral(cisOrderCommon.getIntegral());
        to.setServiceItemCode(cisOrderCommon.getServiceItemCode());
        to.setServiceItemName(cisOrderCommon.getServiceItemName());
        to.setDocCode(cisOrderCommon.getDocCode());
        to.setDocName(cisOrderCommon.getDocName());
        to.setOrgCode(cisOrderCommon.getOrgCode());
        to.setDocName(cisOrderCommon.getDocName());
        to.setExecutiveOrg(cisOrderCommon.getExecutiveOrg());
        to.setExecutiveOrgName(cisOrderCommon.getExecutiveOrgName());
        to.setSystemType(cisOrderCommon.getSystemType());
        to.setPosition(cisOrderCommon.getPosition());
        to.setPositionName(cisOrderCommon.getPositionName());
        to.setAssistiveDevices(cisOrderCommon.getAssistiveDevices());
        to.setAssistiveDevicesName(cisOrderCommon.getAssistiveDevicesName());
        to.setRange(cisOrderCommon.getRange());
        to.setRangeName(cisOrderCommon.getRangeName());
        to.setBasicOperation(cisOrderCommon.getBasicOperation());
        to.setBasicOperationName(cisOrderCommon.getBasicOperationName());
        to.setAzimuth(cisOrderCommon.getAzimuth());
        to.setAzimuthName(cisOrderCommon.getAzimuthName());
        to.setLayers(cisOrderCommon.getLayers());
        to.setLayersName(cisOrderCommon.getLayersName());
        to.setSpeciman(cisOrderCommon.getSpeciman());
        to.setSpecimanName(cisOrderCommon.getSpecimanName());
        to.setExperimentalMethods(cisOrderCommon.getExperimentalMethods());
        to.setExperimentalMethodsName(cisOrderCommon.getExperimentalMethodsName());
        to.setApproach(cisOrderCommon.getApproach());
        to.setApproachName(cisOrderCommon.getApproachName());
        to.setCreatedDate(cisOrderCommon.getCreatedDate());

        if (withAllParts) {
        }
        return to;
    }

}