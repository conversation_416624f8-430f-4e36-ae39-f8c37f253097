package com.bjgoodwill.hip.ds.cis.cds.diagnose.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.repository.CisDiagnoseOrgCommonRepository;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.*;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;

@Entity
@Comment(value = "科室常用诊断")
@DiscriminatorValue("1")
public class CisDiagnoseOrgCommon extends CisDiagnoseCommon {

    // 科室编码
    private String orgCode;

    public CisDiagnoseOrgCommon() {
    }

    public static Optional<CisDiagnoseOrgCommon> getCisDiagnoseOrgCommonById(String id) {
        return dao().findById(id);
    }

    public static List<CisDiagnoseOrgCommon> getCisDiagnoseOrgCommons(CisDiagnoseOrgCommonQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisDiagnoseOrgCommon> getCisDiagnoseOrgCommonPage(CisDiagnoseOrgCommonQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisDiagnoseOrgCommon> getSpecification(CisDiagnoseOrgCommonQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (qto.getIsFix() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isFix"), qto.getIsFix()));
            }
            if (qto.getEnabled() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("enabled"), qto.getEnabled()));
            }
            if (StringUtils.isNotBlank(qto.getOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgCode"), qto.getOrgCode()));
            }
            if (qto.getDiagnosisClass() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("diagnosisClass"), qto.getDiagnosisClass()));
            }
            return predicate;
        };
    }

    private static CisDiagnoseOrgCommonRepository dao() {
        return SpringUtil.getBean(CisDiagnoseOrgCommonRepository.class);
    }

    @Comment("科室编码")
    @Column(name = "org_code", nullable = true)
    public String getOrgCode() {
        return orgCode;
    }

    protected void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    @Override
    public CisDiagnoseCommon create(CisDiagnoseCommonNto cisDiagnoseCommonNto) {
        return create((CisDiagnoseOrgCommonNto) cisDiagnoseCommonNto);
    }

    @Override
    public void update(CisDiagnoseCommonEto cisDiagnoseCommonEto) {
        update((CisDiagnoseOrgCommonEto) cisDiagnoseCommonEto);
    }

    public CisDiagnoseOrgCommon create(CisDiagnoseOrgCommonNto cisDiagnoseOrgCommonNto) {
        BusinessAssert.notNull(cisDiagnoseOrgCommonNto, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisDiagnoseOrgCommonNto");
        super.create(cisDiagnoseOrgCommonNto);

        setOrgCode(cisDiagnoseOrgCommonNto.getOrgCode());
        dao().save(this);
        return this;
    }

    public void update(CisDiagnoseOrgCommonEto cisDiagnoseOrgCommonEto) {
        super.update(cisDiagnoseOrgCommonEto);
    }


}
