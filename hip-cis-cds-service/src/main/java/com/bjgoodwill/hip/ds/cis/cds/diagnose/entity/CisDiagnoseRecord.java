package com.bjgoodwill.hip.ds.cis.cds.diagnose.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.repository.CisDiagnoseRecordRepository;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseRecordNto;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseRecordQto;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.enmus.RecordTypeEnum;
import com.bjgoodwill.hip.jpa.core.SnowflakeIdGenerator;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "常用诊断自动同步记录")
@Table(name = "cis_diagnose_record", indexes = {}, uniqueConstraints = {})
public class CisDiagnoseRecord {

    // 标识
    private String id;
    // 本次同步时间
    private LocalDateTime recordDateTime;
    // 同步类型
    private RecordTypeEnum recordType;
    // 创建的人员
    private String createdStaff;
    // 创建的时间
    private LocalDateTime createdDate;

    public static Optional<CisDiagnoseRecord> getCisDiagnoseRecordById(String id) {
        return dao().findById(id);
    }

    public static List<CisDiagnoseRecord> getCisDiagnoseRecords(CisDiagnoseRecordQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisDiagnoseRecord> getCisDiagnoseRecordPage(CisDiagnoseRecordQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisDiagnoseRecord> getSpecification(CisDiagnoseRecordQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            return predicate;
        };
    }

    private static CisDiagnoseRecordRepository dao() {
        return SpringUtil.getBean(CisDiagnoseRecordRepository.class);
    }

    public static CisDiagnoseRecord getLastRecord(RecordTypeEnum recordTypeEnum) {
        return dao().findFirstByRecordTypeOrderByRecordDateTimeDesc(recordTypeEnum);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    @GeneratedValue(generator = "snowflake_generator")
    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("本次同步时间")
    @Column(name = "record_date_time", nullable = true)
    public LocalDateTime getRecordDateTime() {
        return recordDateTime;
    }

    protected void setRecordDateTime(LocalDateTime recordDateTime) {
        this.recordDateTime = recordDateTime;
    }

    @Enumerated(EnumType.STRING)
    @Comment("同步类型 ")
    @Column(name = "record_type", nullable = true)
    public RecordTypeEnum getRecordType() {
        return recordType;
    }

    public void setRecordType(RecordTypeEnum recordType) {
        this.recordType = recordType;
    }

    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisDiagnoseRecord other = (CisDiagnoseRecord) obj;
        return Objects.equals(id, other.id);
    }

    public CisDiagnoseRecord create(CisDiagnoseRecordNto cisDiagnoseRecordNto) {
        BusinessAssert.notNull(cisDiagnoseRecordNto, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisDiagnoseRecordNto");

//        setId(cisDiagnoseRecordNto.getId());
        setRecordDateTime(cisDiagnoseRecordNto.getRecordDateTime());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedDate(LocalDateUtil.now());
        dao().save(this);
        return this;
    }

    public void delete() {
        dao().delete(this);
    }
}
