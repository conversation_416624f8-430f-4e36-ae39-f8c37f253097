package com.bjgoodwill.hip.ds.cis.cds.orderTemp.repository;

import com.bjgoodwill.hip.ds.cis.cds.orderTemp.entity.CisOrderTemp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository("com.bjgoodwill.hip.ds.cis.cds.orderTemp.repository.CisOrderTempRepository")
public interface CisOrderTempRepository extends JpaRepository<CisOrderTemp, String>, JpaSpecificationExecutor<CisOrderTemp> {
    @Query(value = "SELECT max(a.sortNo) FROM CisOrderTemp a WHERE a.parentId = ?1")
    Integer findMaxSortNoByParentId(String parentId);
}