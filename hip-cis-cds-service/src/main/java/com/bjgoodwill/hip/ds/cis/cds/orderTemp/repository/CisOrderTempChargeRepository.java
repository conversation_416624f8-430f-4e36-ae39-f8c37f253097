package com.bjgoodwill.hip.ds.cis.cds.orderTemp.repository;

import com.bjgoodwill.hip.ds.cis.cds.orderTemp.entity.CisOrderTempCharge;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository("com.bjgoodwill.hip.ds.cis.cds.orderTmep.repository.CisOrderTempChargeRepository")
public interface CisOrderTempChargeRepository extends JpaRepository<CisOrderTempCharge, String>, JpaSpecificationExecutor<CisOrderTempCharge> {
    List<CisOrderTempCharge> findCisOrderTempChargeByTempDetailIdIn(List<String> tempDetailIds);
}