package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.service.internal;

import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.entity.CisDrugUsageFreqSync;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.service.CisDrugUsageFreqSyncService;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.service.internal.assembler.CisDrugUsageFreqSyncAssembler;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqSyncEto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqSyncNto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqSyncQto;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqSyncTo;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@RestController("com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.service.CisDrugUsageFreqSyncService")
@RequestMapping(value = "/api/cds/drugUsageFreq/cisDrugUsageFreqSync", produces = "application/json; charset=utf-8")
public class CisDrugUsageFreqSyncServiceImpl implements CisDrugUsageFreqSyncService {

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisDrugUsageFreqSyncTo> getCisDrugUsageFreqSyncs(CisDrugUsageFreqSyncQto cisDrugUsageFreqSyncQto) {
        return CisDrugUsageFreqSyncAssembler.toTos(CisDrugUsageFreqSync.getCisDrugUsageFreqSyncs(cisDrugUsageFreqSyncQto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisDrugUsageFreqSyncTo> getCisDrugUsageFreqSyncPage(CisDrugUsageFreqSyncQto cisDrugUsageFreqSyncQto) {
        Page<CisDrugUsageFreqSync> page = CisDrugUsageFreqSync.getCisDrugUsageFreqSyncPage(cisDrugUsageFreqSyncQto);
        Page<CisDrugUsageFreqSyncTo> result = page.map(CisDrugUsageFreqSyncAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisDrugUsageFreqSyncTo getCisDrugUsageFreqSyncById(String id) {
        return CisDrugUsageFreqSyncAssembler.toTo(CisDrugUsageFreqSync.getCisDrugUsageFreqSyncById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisDrugUsageFreqSyncTo createCisDrugUsageFreqSync(CisDrugUsageFreqSyncNto cisDrugUsageFreqSyncNto) {
        CisDrugUsageFreqSync cisDrugUsageFreqSync = new CisDrugUsageFreqSync();
        cisDrugUsageFreqSync = cisDrugUsageFreqSync.create(cisDrugUsageFreqSyncNto);
        return CisDrugUsageFreqSyncAssembler.toTo(cisDrugUsageFreqSync);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisDrugUsageFreqSync(String id, CisDrugUsageFreqSyncEto cisDrugUsageFreqSyncEto) {
        Optional<CisDrugUsageFreqSync> cisDrugUsageFreqSyncOptional = CisDrugUsageFreqSync.getCisDrugUsageFreqSyncById(id);
        cisDrugUsageFreqSyncOptional.ifPresent(cisDrugUsageFreqSync -> cisDrugUsageFreqSync.update(cisDrugUsageFreqSyncEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisDrugUsageFreqSync(String id) {
        Optional<CisDrugUsageFreqSync> cisDrugUsageFreqSyncOptional = CisDrugUsageFreqSync.getCisDrugUsageFreqSyncById(id);
        cisDrugUsageFreqSyncOptional.ifPresent(cisDrugUsageFreqSync -> cisDrugUsageFreqSync.delete());
    }

    @Override
    public List<CisDrugUsageFreqSyncTo> getCisDrugByCreateDate(LocalDateTime date) {
        // 验证输入参数date是否为空，如果为空则抛出异常
        BusinessAssert.notNull(date, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "时间");
        // 根据创建时间查询
        return CisDrugUsageFreqSyncAssembler.toTos(CisDrugUsageFreqSync.findCisDrugUsageFreqSyncByCreatedDateAfter(date));

    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void createBatch(List<CisDrugUsageFreqSyncNto> cisDrugUsageFreqSyncNtos) {
        CisDrugUsageFreqSync cisDrugUsageFreqSync = new CisDrugUsageFreqSync();
        cisDrugUsageFreqSync.createBatch(cisDrugUsageFreqSyncNtos);
    }
}