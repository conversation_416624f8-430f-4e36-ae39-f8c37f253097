package com.bjgoodwill.hip.ds.cis.cds.order.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.order.repository.CisOrderCommonRepository;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonEto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "常用医嘱")
@Table(name = "cis_order_common", indexes = {@Index(name = "IDX_cis_order_common_service_item_code", columnList = "serviceItemCode")
        , @Index(name = "IDX_cis_order_common_docCode", columnList = "docCode")
        , @Index(name = "IDX_cis_order_common_orgCode", columnList = "orgCode")}
        , uniqueConstraints = {})
public class CisOrderCommon {

    // 标识
    private String id;
    // 权重
    private Long integral;
    // 医嘱编码
    private String serviceItemCode;
    // 医嘱名称
    private String serviceItemName;
    // 医生编码
    private String docCode;
    @Comment("医生名称")
    @Column(name = "doc_name", nullable = true)
    private String docName;
    // 科室编码
    private String orgCode;
    @Comment("科室名称")
    @Column(name = "org_name", nullable = true)
    private String orgName;
    // 执行科室
    private String executiveOrg;
    @Comment("执行科室名称")
    @Column(name = "executive_org_name", nullable = true)
    private String executiveOrgName;
    // 医嘱类型
    private SystemTypeEnum systemType;
    // 部位
    private String position;
    @Comment("部位名称")
    @Column(name = "position_name", nullable = true)
    private String positionName;
    // 辅助器械
    private String assistiveDevices;
    @Comment("辅助器械名称")
    @Column(name = "assistive_devices_name", nullable = true)
    private String assistiveDevicesName;
    // 范围
    private String range;
    @Comment("范围名称")
    @Column(name = "range_name", nullable = true)
    private String rangeName;
    // 基本操作
    private String basicOperation;
    @Comment("基本操作名称")
    @Column(name = "basic_operation_name", nullable = true)
    private String basicOperationName;
    // 方位
    private String azimuth;
    @Comment("方位名称")
    @Column(name = "azimuth_name", nullable = true)
    private String azimuthName;
    // 层数
    private String layers;
    @Comment("层数名称")
    @Column(name = "layers_name", nullable = true)
    private String layersName;
    // 标本
    private String speciman;
    @Comment("标本名称")
    @Column(name = "speciman_name", nullable = true)
    private String specimanName;
    // 实验方法
    private String experimentalMethods;
    @Comment("实验方法名称")
    @Column(name = "experimental_methods_name", nullable = true)
    private String experimentalMethodsName;
    // 入路
    private String approach;
    @Comment("入路名称")
    @Column(name = "approach_name", nullable = true)
    private String approachName;
    //收藏
    private Boolean saveFlag;
    // 创建的时间
    private LocalDateTime createdDate;
    // 创建的人员
    private String createdStaff;
    // 创建的人员姓名
    private String createdStaffName;
    // 最后修改时间
    private LocalDateTime updatedDate;
    // 最后修改用户编码
    private String updatedStaff;
    // 最后修改人当时的姓名
    private String updatedStaffName;

    public static Optional<CisOrderCommon> getCisOrderCommonById(String id) {
        return dao().findById(id);
    }

    public static List<CisOrderCommon> getCisOrderCommons(CisOrderCommonQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisOrderCommon> getCisOrderCommonPage(CisOrderCommonQto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static CisOrderCommon newInstanceByNto(CisOrderCommonNto cisOrderCommonNto) {
        try {
            return (CisOrderCommon) Class.forName("com.bjgoodwill.hip.ds.cis.cds.order.entity."
                    + StringUtils.removeEnd(cisOrderCommonNto.getClass().getSimpleName(), "Nto")).getConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static Specification<CisOrderCommon> getSpecification(CisOrderCommonQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getText())) {
                String searchText = "%" + qto.getText() + "%";
                Predicate code = criteriaBuilder.like(root.get("serviceItemCode"), searchText);
                Predicate name = criteriaBuilder.like(root.get("serviceItemName"), searchText);
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(code, name));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemCode"), qto.getServiceItemCode()));
            }
            if (StringUtils.isNotBlank(qto.getServiceItemName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceItemName"), qto.getServiceItemName()));
            }
            if (StringUtils.isNotBlank(qto.getDocCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("docCode"), qto.getDocCode()));
            }
            if (StringUtils.isNotBlank(qto.getOrgCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("orgCode"), qto.getOrgCode()));
            }
            if (StringUtils.isNotBlank(qto.getExecutiveOrg())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("executiveOrg"), qto.getExecutiveOrg()));
            }
            if (qto.getSystemType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("systemType"), qto.getSystemType()));
            }
            if (StringUtils.isNotBlank(qto.getPosition())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("position"), qto.getPosition()));
            }
            if (StringUtils.isNotBlank(qto.getAssistiveDevices())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("assistiveDevices"), qto.getAssistiveDevices()));
            }
            if (StringUtils.isNotBlank(qto.getRange())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("range"), qto.getRange()));
            }
            if (StringUtils.isNotBlank(qto.getBasicOperation())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("basicOperation"), qto.getBasicOperation()));
            }
            if (StringUtils.isNotBlank(qto.getAzimuth())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("azimuth"), qto.getAzimuth()));
            }
            if (StringUtils.isNotBlank(qto.getLayers())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("layers"), qto.getLayers()));
            }
            if (StringUtils.isNotBlank(qto.getSpeciman())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("speciman"), qto.getSpeciman()));
            }
            if (StringUtils.isNotBlank(qto.getExperimentalMethods())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("experimentalMethods"), qto.getExperimentalMethods()));
            }
            if (StringUtils.isNotBlank(qto.getApproach())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("approach"), qto.getApproach()));
            }
            if (qto.getSaveFlag() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("saveFlag"), qto.getSaveFlag()));
            }
            return predicate;
        };
    }

    private static CisOrderCommonRepository dao() {
        return SpringUtil.getBean(CisOrderCommonRepository.class);
    }

    public static CisOrderCommon findCisDrugUsageFreqCommon(String docCode, String orgCode, String serviceItemCode) {
        return dao().findByDocCodeAndOrgCodeAndServiceItemCodeAndSaveFlag(docCode, orgCode, serviceItemCode, false);
    }

    public static CisOrderCommon findCisDrugUsageFreqCommonCustomize(String docCode, String orgCode, String serviceItemCode, String speciman) {
        return dao().findByDocCodeAndOrgCodeAndServiceItemCodeAndSpecimanAndSaveFlag(docCode, orgCode, serviceItemCode, speciman, false);
    }

    public static List<CisOrderCommon> findByDocCodeAndOrgCode(String docCode, String orgCode) {
        return dao().findByDocCodeAndOrgCodeAndSaveFlag(docCode, orgCode, false);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("权重")
    @Column(name = "integral", nullable = true)
    public Long getIntegral() {
        return integral;
    }

    protected void setIntegral(Long integral) {
        this.integral = integral;
    }

    @Comment("医嘱编码")
    @Column(name = "service_item_code", nullable = false)
    public String getServiceItemCode() {
        return serviceItemCode;
    }

    protected void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    @Comment("医嘱名称")
    @Column(name = "service_item_name", nullable = true)
    public String getServiceItemName() {
        return serviceItemName;
    }

    protected void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    @Comment("医生编码")
    @Column(name = "doc_code", nullable = true)
    public String getDocCode() {
        return docCode;
    }

    protected void setDocCode(String docCode) {
        this.docCode = docCode;
    }

    @Comment("科室编码")
    @Column(name = "org_code", nullable = true)
    public String getOrgCode() {
        return orgCode;
    }

    protected void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    @Comment("执行科室")
    @Column(name = "executive_org", nullable = true)
    public String getExecutiveOrg() {
        return executiveOrg;
    }

    protected void setExecutiveOrg(String executiveOrg) {
        this.executiveOrg = executiveOrg;
    }

    @Enumerated(EnumType.STRING)
    @Comment("医嘱类型")
    @Column(name = "system_type", nullable = true)
    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    protected void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }

    @Comment("部位")
    @Column(name = "position", nullable = true)
    public String getPosition() {
        return position;
    }

    protected void setPosition(String position) {
        this.position = position;
    }

    @Comment("辅助器械")
    @Column(name = "assistive_devices", nullable = true)
    public String getAssistiveDevices() {
        return assistiveDevices;
    }

    protected void setAssistiveDevices(String assistiveDevices) {
        this.assistiveDevices = assistiveDevices;
    }

    @Comment("范围")
    @Column(name = "range", nullable = true)
    public String getRange() {
        return range;
    }

    protected void setRange(String range) {
        this.range = range;
    }

    @Comment("基本操作")
    @Column(name = "basic_operation", nullable = true)
    public String getBasicOperation() {
        return basicOperation;
    }

    protected void setBasicOperation(String basicOperation) {
        this.basicOperation = basicOperation;
    }

    @Comment("方位")
    @Column(name = "azimuth", nullable = true)
    public String getAzimuth() {
        return azimuth;
    }

    protected void setAzimuth(String azimuth) {
        this.azimuth = azimuth;
    }

    @Comment("层数")
    @Column(name = "layers", nullable = true)
    public String getLayers() {
        return layers;
    }

    protected void setLayers(String layers) {
        this.layers = layers;
    }

    @Comment("标本")
    @Column(name = "speciman", nullable = true)
    public String getSpeciman() {
        return speciman;
    }

    public void setSpeciman(String speciman) {
        this.speciman = speciman;
    }

    @Comment("实验方法")
    @Column(name = "experimental_methods", nullable = true)
    public String getExperimentalMethods() {
        return experimentalMethods;
    }

    protected void setExperimentalMethods(String experimentalMethods) {
        this.experimentalMethods = experimentalMethods;
    }

    @Comment("入路")
    @Column(name = "approach", nullable = true)
    public String getApproach() {
        return approach;
    }

    protected void setApproach(String approach) {
        this.approach = approach;
    }

    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    @Comment("收藏")
    @Column(name = "save_flag", nullable = false)
    public Boolean getSaveFlag() {
        return saveFlag;
    }

    public void setSaveFlag(Boolean saveFlag) {
        this.saveFlag = saveFlag;
    }

    @Comment("创建人编码")
    @Column(name = "created_staff", nullable = true)
    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    @Comment("创建人名称")
    @Column(name = "created_staff_name", nullable = true)
    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    @Comment("最后修改时间")
    @Column(name = "updated_date", nullable = true)
    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Comment("最后修改用户编码")
    @Column(name = "updated_staff", nullable = true)
    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    @Comment("最后修改用户名称")
    @Column(name = "updated_staff_name", nullable = true)
    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getExecutiveOrgName() {
        return executiveOrgName;
    }

    public void setExecutiveOrgName(String executiveOrgName) {
        this.executiveOrgName = executiveOrgName;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getAssistiveDevicesName() {
        return assistiveDevicesName;
    }

    public void setAssistiveDevicesName(String assistiveDevicesName) {
        this.assistiveDevicesName = assistiveDevicesName;
    }

    public String getRangeName() {
        return rangeName;
    }

    public void setRangeName(String rangeName) {
        this.rangeName = rangeName;
    }

    public String getBasicOperationName() {
        return basicOperationName;
    }

    public void setBasicOperationName(String basicOperationName) {
        this.basicOperationName = basicOperationName;
    }

    public String getAzimuthName() {
        return azimuthName;
    }

    public void setAzimuthName(String azimuthName) {
        this.azimuthName = azimuthName;
    }

    public String getLayersName() {
        return layersName;
    }

    public void setLayersName(String layersName) {
        this.layersName = layersName;
    }

    public String getSpecimanName() {
        return specimanName;
    }

    public void setSpecimanName(String specimanName) {
        this.specimanName = specimanName;
    }

    public String getExperimentalMethodsName() {
        return experimentalMethodsName;
    }

    public void setExperimentalMethodsName(String experimentalMethodsName) {
        this.experimentalMethodsName = experimentalMethodsName;
    }

    public String getApproachName() {
        return approachName;
    }

    public void setApproachName(String approachName) {
        this.approachName = approachName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOrderCommon other = (CisOrderCommon) obj;
        return Objects.equals(id, other.id);
    }

    public CisOrderCommon create(CisOrderCommonNto cisOrderCommonNto) {
        BusinessAssert.notNull(cisOrderCommonNto, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisOrderCommonNto");
        setId(cisOrderCommonNto.getId());
        setIntegral(cisOrderCommonNto.getIntegral());
        setServiceItemCode(cisOrderCommonNto.getServiceItemCode());
        setServiceItemName(cisOrderCommonNto.getServiceItemName());
        setDocCode(cisOrderCommonNto.getDocCode());
        setOrgName(cisOrderCommonNto.getOrgName());
        setOrgCode(cisOrderCommonNto.getOrgCode());
        setDocName(cisOrderCommonNto.getDocName());
        setExecutiveOrg(cisOrderCommonNto.getExecutiveOrg());
        setExecutiveOrgName(cisOrderCommonNto.getExecutiveOrgName());
        setPosition(cisOrderCommonNto.getPosition());
        setPositionName(cisOrderCommonNto.getPositionName());
        setAssistiveDevices(cisOrderCommonNto.getAssistiveDevices());
        setAssistiveDevicesName(cisOrderCommonNto.getAssistiveDevicesName());
        setRange(cisOrderCommonNto.getRange());
        setRangeName(cisOrderCommonNto.getRangeName());
        setBasicOperation(cisOrderCommonNto.getBasicOperation());
        setBasicOperationName(cisOrderCommonNto.getBasicOperationName());
        setAzimuth(cisOrderCommonNto.getAzimuth());
        setAzimuthName(cisOrderCommonNto.getAzimuthName());
        setLayers(cisOrderCommonNto.getLayers());
        setLayersName(cisOrderCommonNto.getLayersName());
        setSystemType(cisOrderCommonNto.getSystemType());
        setSpeciman(cisOrderCommonNto.getSpeciman());
        setSpecimanName(cisOrderCommonNto.getSpecimanName());
        setExperimentalMethods(cisOrderCommonNto.getExperimentalMethods());
        setExperimentalMethodsName(cisOrderCommonNto.getExperimentalMethodsName());
        setApproach(cisOrderCommonNto.getApproach());
        setApproachName(cisOrderCommonNto.getApproachName());
        setCreatedDate(LocalDateUtil.now());
        setSaveFlag(cisOrderCommonNto.getSaveFlag() == null ? true : cisOrderCommonNto.getSaveFlag());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        dao().save(this);
        return this;
    }

    public void update(CisOrderCommonEto cisOrderCommonEto) {
        setIntegral(cisOrderCommonEto.getIntegral());
        setServiceItemCode(cisOrderCommonEto.getServiceItemCode());
        setServiceItemName(cisOrderCommonEto.getServiceItemName());
        setDocCode(cisOrderCommonEto.getDocCode());
        setOrgName(cisOrderCommonEto.getOrgName());
        setOrgCode(cisOrderCommonEto.getOrgCode());
        setDocName(cisOrderCommonEto.getDocName());
        setExecutiveOrg(cisOrderCommonEto.getExecutiveOrg());
        setExecutiveOrgName(cisOrderCommonEto.getExecutiveOrgName());
        setPosition(cisOrderCommonEto.getPosition());
        setPositionName(cisOrderCommonEto.getPositionName());
        setAssistiveDevices(cisOrderCommonEto.getAssistiveDevices());
        setAssistiveDevicesName(cisOrderCommonEto.getAssistiveDevicesName());
        setRange(cisOrderCommonEto.getRange());
        setRangeName(cisOrderCommonEto.getRangeName());
        setBasicOperation(cisOrderCommonEto.getBasicOperation());
        setBasicOperationName(cisOrderCommonEto.getBasicOperationName());
        setAzimuth(cisOrderCommonEto.getAzimuth());
        setAzimuthName(cisOrderCommonEto.getAzimuthName());
        setLayers(cisOrderCommonEto.getLayers());
        setLayersName(cisOrderCommonEto.getLayersName());
        setSystemType(cisOrderCommonEto.getSystemType());
        setSpeciman(cisOrderCommonEto.getSpeciman());
        setSpecimanName(cisOrderCommonEto.getSpecimanName());
        setExperimentalMethods(cisOrderCommonEto.getExperimentalMethods());
        setExperimentalMethodsName(cisOrderCommonEto.getExperimentalMethodsName());
        setApproach(cisOrderCommonEto.getApproach());
        setApproachName(cisOrderCommonEto.getApproachName());
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
    }

    public CisOrderCommon updateByIntegral(Long integral) {
        BusinessAssert.notNull(integral, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数integral");
        setIntegral(integral);
        setUpdatedDate(LocalDateUtil.now());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        return this;
    }

    public void delete() {
        dao().delete(this);
    }
}
