package com.bjgoodwill.hip.ds.cis.cds.diagnose.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cds.diagnose.entity.CisDiagnoseBaseData;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.CisDiagnoseBaseDataTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisDiagnoseBaseDataAssembler {

    public static List<CisDiagnoseBaseDataTo> toTos(List<CisDiagnoseBaseData> cisDiagnoseBaseDatas) {
        return toTos(cisDiagnoseBaseDatas, false);
    }

    public static List<CisDiagnoseBaseDataTo> toTos(List<CisDiagnoseBaseData> cisDiagnoseBaseDatas, boolean withAllParts) {
        Assert.notNull(cisDiagnoseBaseDatas, "参数cisDiagnoseBaseDatas不能为空！");

        List<CisDiagnoseBaseDataTo> tos = new ArrayList<>();
        for (CisDiagnoseBaseData cisDiagnoseBaseData : cisDiagnoseBaseDatas)
            tos.add(toTo(cisDiagnoseBaseData, withAllParts));
        return tos;
    }

    public static CisDiagnoseBaseDataTo toTo(CisDiagnoseBaseData cisDiagnoseBaseData) {
        return toTo(cisDiagnoseBaseData, false);
    }

    /**
     * @generated
     */
    public static CisDiagnoseBaseDataTo toTo(CisDiagnoseBaseData cisDiagnoseBaseData, boolean withAllParts) {
        if (cisDiagnoseBaseData == null)
            return null;
        CisDiagnoseBaseDataTo to = new CisDiagnoseBaseDataTo();
        to.setId(cisDiagnoseBaseData.getId());
        to.setDiagnoseCode(cisDiagnoseBaseData.getDiagnoseCode());
        to.setDiagnoseName(cisDiagnoseBaseData.getDiagnoseName());
        to.setOrgCode(cisDiagnoseBaseData.getOrgCode());
        to.setDocCode(cisDiagnoseBaseData.getDocCode());
        to.setCreatedDate(cisDiagnoseBaseData.getCreatedDate());

        if (withAllParts) {
        }
        return to;
    }

}