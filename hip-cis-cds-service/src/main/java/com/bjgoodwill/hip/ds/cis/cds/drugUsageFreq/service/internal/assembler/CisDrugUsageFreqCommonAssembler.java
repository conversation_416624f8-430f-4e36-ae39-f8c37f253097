package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.entity.CisDrugUsageFreqCommon;
import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.to.CisDrugUsageFreqCommonTo;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;

import java.util.ArrayList;
import java.util.List;

public abstract class CisDrugUsageFreqCommonAssembler {

    public static List<CisDrugUsageFreqCommonTo> toTos(List<CisDrugUsageFreqCommon> cisDrugUsageFreqCommons) {
        return toTos(cisDrugUsageFreqCommons, false);
    }

    public static List<CisDrugUsageFreqCommonTo> toTos(List<CisDrugUsageFreqCommon> cisDrugUsageFreqCommons, boolean withAllParts) {
        BusinessAssert.notEmpty(cisDrugUsageFreqCommons, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数cisDrugUsageFreqCommons");

        List<CisDrugUsageFreqCommonTo> tos = new ArrayList<>();
        for (CisDrugUsageFreqCommon cisDrugUsageFreqCommon : cisDrugUsageFreqCommons)
            tos.add(toTo(cisDrugUsageFreqCommon, withAllParts));
        return tos;
    }

    public static CisDrugUsageFreqCommonTo toTo(CisDrugUsageFreqCommon cisDrugUsageFreqCommon) {
        return toTo(cisDrugUsageFreqCommon, false);
    }

    /**
     * @generated
     */
    public static CisDrugUsageFreqCommonTo toTo(CisDrugUsageFreqCommon cisDrugUsageFreqCommon, boolean withAllParts) {
        if (cisDrugUsageFreqCommon == null)
            return null;
        CisDrugUsageFreqCommonTo to = new CisDrugUsageFreqCommonTo();
        to.setId(cisDrugUsageFreqCommon.getId());
        to.setDeptCode(cisDrugUsageFreqCommon.getDeptCode());
        to.setDrugCode(cisDrugUsageFreqCommon.getDrugCode());
        to.setUsageCode(cisDrugUsageFreqCommon.getUsageCode());
        to.setUsageName(cisDrugUsageFreqCommon.getUsageName());
        to.setFrequencyCode(cisDrugUsageFreqCommon.getFrequencyCode());
        to.setFrequencyName(cisDrugUsageFreqCommon.getFrequencyName());
        to.setNum(cisDrugUsageFreqCommon.getNum());
        to.setUpdatedDate(cisDrugUsageFreqCommon.getUpdatedDate());
        to.setDosage(cisDrugUsageFreqCommon.getDosage());
        to.setDosageUnit(cisDrugUsageFreqCommon.getDosageUnit());
        to.setDosageUnitName(cisDrugUsageFreqCommon.getDosageUnitName());

        if (withAllParts) {
        }
        return to;
    }

}