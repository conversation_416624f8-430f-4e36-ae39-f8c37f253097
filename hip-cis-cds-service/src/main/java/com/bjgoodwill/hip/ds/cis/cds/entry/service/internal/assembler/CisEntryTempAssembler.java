package com.bjgoodwill.hip.ds.cis.cds.entry.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cds.entry.entity.CisEntryTemp;
import com.bjgoodwill.hip.ds.cis.cds.entry.entity.CisPharmacy;
import com.bjgoodwill.hip.ds.cis.cds.entry.entity.CisRemark;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisEntryTempTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisEntryTempAssembler {

    public static List<CisEntryTempTo> toTos(List<CisEntryTemp> cisEntryTemps) {
        return toTos(cisEntryTemps, false);
    }

    public static List<CisEntryTempTo> toTos(List<CisEntryTemp> cisEntryTemps, boolean withAllParts) {
        Assert.notNull(cisEntryTemps, "参数cisEntryTemps不能为空！");

        List<CisEntryTempTo> tos = new ArrayList<>();
        for (CisEntryTemp cisEntryTemp : cisEntryTemps)
            tos.add(toTo(cisEntryTemp, withAllParts));
        return tos;
    }

    public static CisEntryTempTo toTo(CisEntryTemp cisEntryTemp) {
        return toTo(cisEntryTemp, false);
    }

    /**
     * @generated
     */
    public static CisEntryTempTo toTo(CisEntryTemp cisEntryTemp, boolean withAllParts) {
        if (cisEntryTemp == null)
            return null;
        if (cisEntryTemp instanceof CisRemark) {
            return CisRemarkAssembler.toTo((CisRemark) cisEntryTemp, withAllParts);
        }
        if (cisEntryTemp instanceof CisPharmacy) {
            return CisPharmacyAssembler.toTo((CisPharmacy) cisEntryTemp, withAllParts);
        }
        return null;
    }

}