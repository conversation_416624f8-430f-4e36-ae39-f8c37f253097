package com.bjgoodwill.hip.ds.cis.cds.test.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.test.entity.Test2;
import com.bjgoodwill.hip.ds.cis.cds.test.to.Test2To;

import java.util.ArrayList;
import java.util.List;

public abstract class Test2Assembler {

    public static List<Test2To> toTos(List<Test2> test2s) {
        return toTos(test2s, false);
    }

    public static List<Test2To> toTos(List<Test2> test2s, boolean withAllParts) {
        BusinessAssert.notNull(test2s, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数test2s");

        List<Test2To> tos = new ArrayList<>();
        for (Test2 test2 : test2s)
            tos.add(toTo(test2, withAllParts));
        return tos;
    }

    public static Test2To toTo(Test2 test2) {
        return toTo(test2, false);
    }

    /**
     * @generated
     */
    public static Test2To toTo(Test2 test2, boolean withAllParts) {
        if (test2 == null)
            return null;
        Test2To to = new Test2To();
        to.setId(test2.getId());
        to.setEnabled(test2.isEnabled());

        if (withAllParts) {
        }
        return to;
    }

}