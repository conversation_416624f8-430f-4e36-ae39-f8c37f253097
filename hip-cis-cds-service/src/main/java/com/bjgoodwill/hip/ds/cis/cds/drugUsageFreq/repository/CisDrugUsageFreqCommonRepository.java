package com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.repository;

import com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.entity.CisDrugUsageFreqCommon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.cds.drugUsageFreq.repository.CisDrugUsageFreqCommonRepository")
public interface CisDrugUsageFreqCommonRepository extends JpaRepository<CisDrugUsageFreqCommon, String>, JpaSpecificationExecutor<CisDrugUsageFreqCommon> {
    CisDrugUsageFreqCommon findByDeptCodeAndDrugCodeAndUsageCodeAndFrequencyCode(String deptCode, String drugCode, String usageCode, String frequencyCode);

    List<CisDrugUsageFreqCommon> findByDeptCodeAndDrugCode(String deptCode, String drugCode);

    CisDrugUsageFreqCommon findByDeptCodeAndDrugCodeAndUsageCodeAndFrequencyCodeAndDosage(String deptCode, String drugCode, String usageCode, String frequencyCode, String dosage);

}