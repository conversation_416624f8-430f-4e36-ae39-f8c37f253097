package com.bjgoodwill.hip.ds.cis.cds.orderTemp.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.econ.enums.SystemItemClassEnum;
import com.bjgoodwill.hip.common.util.HIPLoginUtil;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.repository.CisOrderTempChargeRepository;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeEto;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeNto;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempChargeQto;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "组套明细费用表")
@Table(name = "cis_order_temp_charge", indexes = {@Index(name = "idx_cis_order_temp_charge_temp_detail_id", columnList = "temp_detail_id")}, uniqueConstraints = {})
public class CisOrderTempCharge {

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    private String id;


    @Comment("组套明细id")
    @Column(name = "temp_detail_id", nullable = false)
    private String tempDetailId;


    @Comment("收费项目编码")
    @Column(name = "price_item_code", nullable = false)
    private String priceItemCode;


    @Comment("收费项目名称")
    @Column(name = "price_item_name", nullable = true)
    private String priceItemName;

    @Comment("系统项目分类")
    @Enumerated(EnumType.STRING)
    @Column(name = "system_item_class", nullable = false)
    private SystemItemClassEnum systemItemClass;


    @Comment("固定费用")
    @Column(name = "is_fixed", nullable = false)
    private Boolean isFixed;


    @Comment("规格")
    @Column(name = "package_spec", nullable = true)
    private String packageSpec;


    @Comment("单价")
    @Column(name = "price", nullable = true)
    private BigDecimal price;


    @Comment("单位")
    @Column(name = "unit", nullable = true)
    private String unit;


    @Comment("单位名称")
    @Column(name = "unit_name", nullable = true)
    private String unitName;


    @Comment("数量")
    @Column(name = "num", nullable = true)
    private Double num;


    @Comment("金额")
    @Column(name = "chage_amount", nullable = true)
    private BigDecimal chageAmount;


    @Comment("特限符合标识:1符合,0不符合")
    @Column(name = "limit_conform_flag", nullable = false)
    private Boolean limitConformFlag;


    @Comment("SPD高值耗材唯一码")
    @Column(name = "bar_code", nullable = true)
    private String barCode;


    @Comment("执行科室编码")
    @Column(name = "execute_org_code", nullable = true)
    private String executeOrgCode;


    @Comment("执行科室名称")
    @Column(name = "execute_org_name", nullable = true)
    private String executeOrgName;


    @Comment("创建的人员")
    @Column(name = "created_staff", nullable = false, length = 64)
    private String createdStaff;


    @Comment("创建的人员姓名")
    @Column(name = "created_staff_name", nullable = true, length = 64)
    private String createdStaffName;


    @Comment("创建的时间")
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;


    @Comment("最后修改的人员")
    @Column(name = "updated_staff", nullable = true, length = 64)
    private String updatedStaff;


    @Comment("最后修改的人员姓名")
    @Column(name = "updated_staff_name", nullable = true, length = 64)
    private String updatedStaffName;


    @Comment("最后修改的时间")
    @Column(name = "updated_date", nullable = true)
    private LocalDateTime updatedDate;


    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public String getTempDetailId() {
        return tempDetailId;
    }

    protected void setTempDetailId(String tempDetailId) {
        this.tempDetailId = tempDetailId;
    }

    public String getPriceItemCode() {
        return priceItemCode;
    }

    protected void setPriceItemCode(String priceItemCode) {
        this.priceItemCode = priceItemCode;
    }

    public String getPriceItemName() {
        return priceItemName;
    }

    protected void setPriceItemName(String priceItemName) {
        this.priceItemName = priceItemName;
    }

    public SystemItemClassEnum getSystemItemClass() {
        return systemItemClass;
    }

    public void setSystemItemClass(SystemItemClassEnum systemItemClass) {
        this.systemItemClass = systemItemClass;
    }

    public Boolean getIsFixed() {
        return isFixed;
    }

    protected void setIsFixed(Boolean isFixed) {
        this.isFixed = isFixed;
    }

    public String getPackageSpec() {
        return packageSpec;
    }

    protected void setPackageSpec(String packageSpec) {
        this.packageSpec = packageSpec;
    }

    public BigDecimal getPrice() {
        return price;
    }

    protected void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getUnit() {
        return unit;
    }

    protected void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitName() {
        return unitName;
    }

    protected void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Double getNum() {
        return num;
    }

    protected void setNum(Double num) {
        this.num = num;
    }

    public BigDecimal getChageAmount() {
        return chageAmount;
    }

    protected void setChageAmount(BigDecimal chageAmount) {
        this.chageAmount = chageAmount;
    }

    public Boolean getLimitConformFlag() {
        return limitConformFlag;
    }

    protected void setLimitConformFlag(Boolean limitConformFlag) {
        this.limitConformFlag = limitConformFlag;
    }

    public String getBarCode() {
        return barCode;
    }

    protected void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    protected void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = executeOrgCode;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    protected void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    protected void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    protected void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    protected void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    protected void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    protected void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    protected void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CisOrderTempCharge other = (CisOrderTempCharge) obj;
        return Objects.equals(id, other.id);
    }

    public CisOrderTempCharge create(CisOrderTempChargeNto cisOrderTempChargeNto) {
        Assert.notNull(cisOrderTempChargeNto, "参数cisOrderTempChargeNto不能为空！");

        setId(cisOrderTempChargeNto.getId());
        setTempDetailId(cisOrderTempChargeNto.getTempDetailId());
        setPriceItemCode(cisOrderTempChargeNto.getPriceItemCode());
        setPriceItemName(cisOrderTempChargeNto.getPriceItemName());
        setIsFixed(cisOrderTempChargeNto.getIsFixed());
        setPackageSpec(cisOrderTempChargeNto.getPackageSpec());
        setPrice(cisOrderTempChargeNto.getPrice());
        setUnit(cisOrderTempChargeNto.getUnit());
        setUnitName(cisOrderTempChargeNto.getUnitName());
        setNum(cisOrderTempChargeNto.getNum());
        setChageAmount(cisOrderTempChargeNto.getChageAmount());
        setLimitConformFlag(cisOrderTempChargeNto.getLimitConformFlag());
        setBarCode(cisOrderTempChargeNto.getBarCode());
        setExecuteOrgCode(cisOrderTempChargeNto.getExecuteOrgCode());
        setExecuteOrgName(cisOrderTempChargeNto.getExecuteOrgName());
        setCreatedStaff(HIPLoginUtil.getStaffId());
        setCreatedStaffName(HIPLoginUtil.getLoginName());
        setCreatedDate(LocalDateUtil.now());
        setSystemItemClass(cisOrderTempChargeNto.getSystemItemClass());
        dao().save(this);
        return this;
    }

    public void update(CisOrderTempChargeEto cisOrderTempChargeEto) {
        setNum(cisOrderTempChargeEto.getNum());
        setChageAmount(cisOrderTempChargeEto.getChageAmount());
        setLimitConformFlag(cisOrderTempChargeEto.getLimitConformFlag());
        setExecuteOrgCode(cisOrderTempChargeEto.getExecuteOrgCode());
        setExecuteOrgName(cisOrderTempChargeEto.getExecuteOrgName());
        setUpdatedStaff(HIPLoginUtil.getStaffId());
        setUpdatedStaffName(HIPLoginUtil.getLoginName());
        setUpdatedDate(LocalDateUtil.now());
    }

    public void delete() {
        dao().delete(this);
    }

    public static Optional<CisOrderTempCharge> getCisOrderTempChargeById(String id) {
        return dao().findById(id);
    }

    public static List<CisOrderTempCharge> getCisOrderTempCharges(CisOrderTempChargeQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisOrderTempCharge> getCisOrderTempChargePage(CisOrderTempChargeQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    public static List<CisOrderTempCharge> listByTempDetailId(List<String> tempDetailIds) {
        return dao().findCisOrderTempChargeByTempDetailIdIn(tempDetailIds);
    }

    /**
     * @generated
     */
    private static Specification<CisOrderTempCharge> getSpecification(CisOrderTempChargeQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getTempDetailId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("tempDetailId"), qto.getTempDetailId()));
            }
            if (StringUtils.isNotBlank(qto.getPriceItemCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("priceItemCode"), qto.getPriceItemCode()));
            }
            if (StringUtils.isNotBlank(qto.getPriceItemName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("priceItemName"), qto.getPriceItemName()));
            }
            return predicate;
        };
    }

    private static CisOrderTempChargeRepository dao() {
        return SpringUtil.getBean(CisOrderTempChargeRepository.class);
    }

}
