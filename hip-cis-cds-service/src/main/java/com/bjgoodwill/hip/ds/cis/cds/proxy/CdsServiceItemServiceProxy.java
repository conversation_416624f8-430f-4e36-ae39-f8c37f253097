package com.bjgoodwill.hip.ds.cis.cds.proxy;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonQto;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: hip-cis
 * @author: xdguo
 * @create: 2025-02-25 14:34
 * @className: CdsServiceItemServiceProxy
 * @description:
 **/
@Component
public class CdsServiceItemServiceProxy {

    @Autowired
    private ServiceClinicItemTangibleService serviceClinicItemService;

    public Set<String> queryCanUseServiceCodes(CisOrderCommonQto cisOrderCommonQto) {
        List<SystemTypeEnum> systemTypeEnums = cisOrderCommonQto.getSystemType() == null ?
                Arrays.stream(SystemTypeEnum.values()).toList() :
                Arrays.stream(new SystemTypeEnum[]{cisOrderCommonQto.getSystemType()}).toList();

        return serviceClinicItemService.queryServiceClinicItemListByinPutText(
                        systemTypeEnums,
                        500,
                        "",
                        cisOrderCommonQto.getOrgCode(),
                        cisOrderCommonQto.getHospitalModel(),
                        cisOrderCommonQto.getHospitalAreaCode())
                .stream().map(p -> p.getServiceItemCode())
                .collect(Collectors.toSet());
    }
}