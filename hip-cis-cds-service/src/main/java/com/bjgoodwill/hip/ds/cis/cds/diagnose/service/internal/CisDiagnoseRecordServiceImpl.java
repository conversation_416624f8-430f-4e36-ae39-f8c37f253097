package com.bjgoodwill.hip.ds.cis.cds.diagnose.service.internal;

import com.bjgoodwill.hip.ds.cis.cds.diagnose.entity.CisDiagnoseRecord;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.service.CisDiagnoseBaseDataService;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.service.CisDiagnoseDocCommonService;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.service.CisDiagnoseOrgCommonService;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.service.CisDiagnoseRecordService;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.service.internal.assembler.CisDiagnoseRecordAssembler;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.*;
import com.bjgoodwill.hip.ds.cis.cds.enmus.RecordTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

@RestController("com.bjgoodwill.hip.ds.cis.cds.diagnose.service.CisDiagnoseRecordService")
@RequestMapping(value = "/api/cds/diagnose/cisDiagnoseRecord", produces = "application/json; charset=utf-8")
public class CisDiagnoseRecordServiceImpl implements CisDiagnoseRecordService {

    // private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"); // 优化1: 将日期时间格式定义为常量
    //region 生成常用诊断数据
//    @Autowired
//    private CisIpdDiagnoseService cisIpdDiagnoseService;
    @Autowired
    private CisDiagnoseOrgCommonService cisDiagnoseOrgCommonService;
    @Autowired
    private CisDiagnoseDocCommonService cisDiagnoseDocCommonService;
    @Autowired
    private CisDiagnoseBaseDataService cisDiagnoseBaseDataService;

    //    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisDiagnoseRecordTo getCisDiagnoseRecordById(String id) {
        return CisDiagnoseRecordAssembler.toTo(CisDiagnoseRecord.getCisDiagnoseRecordById(id).orElse(null));
    }

    //    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisDiagnoseRecordTo createCisDiagnoseRecord(CisDiagnoseRecordNto cisDiagnoseRecordNto) {
        CisDiagnoseRecord cisDiagnoseRecord = new CisDiagnoseRecord();
        cisDiagnoseRecord = cisDiagnoseRecord.create(cisDiagnoseRecordNto);
        return CisDiagnoseRecordAssembler.toTo(cisDiagnoseRecord);
    }

    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisDiagnoseRecord() {
        CisDiagnoseRecord record = CisDiagnoseRecord.getLastRecord(RecordTypeEnum.DIAGNOSIS);
        LocalDateTime recordDateTime = (record != null) ? record.getRecordDateTime() : LocalDateTime.now().minusYears(1);
        List<CisDiagnoseBaseDataTo> ipdLst = cisDiagnoseBaseDataService.getCisDiagnoseBaseDataByCreateDate(recordDateTime);
        if (CollectionUtils.isEmpty(ipdLst)) {
            return;
        }
        List<CisDiagnoseOrgCommonNto> orgNtoLst = ipdLst.stream().filter(diagnoseTo -> diagnoseTo.getOrgCode() != null && diagnoseTo.getDiagnoseCode() != null)
                .map(p -> {
                    CisDiagnoseOrgCommonNto orgNto = new CisDiagnoseOrgCommonNto();
                    orgNto.setOrgCode(p.getOrgCode());
                    orgNto.setDiagnoseCode(p.getDiagnoseCode());
                    orgNto.setDiagnoseName(p.getDiagnoseName());
                    orgNto.setIntegral(1L);
                    orgNto.setIsFix(false);
                    return orgNto;
                }).toList();
        List<CisDiagnoseDocCommonNto> docNtoLst = ipdLst.stream().filter(diagnoseTo -> diagnoseTo.getDocCode() != null && diagnoseTo.getDiagnoseCode() != null)
                .map(p -> {
                    CisDiagnoseDocCommonNto docNto = new CisDiagnoseDocCommonNto();
                    docNto.setDocCode(p.getDocCode());
                    docNto.setDiagnoseCode(p.getDiagnoseCode());
                    docNto.setDiagnoseName(p.getDiagnoseName());
                    docNto.setIntegral(1L);
                    docNto.setIsFix(false);
                    return docNto;
                }).toList();
        cisDiagnoseOrgCommonService.autostatisticsOrgDiagnose(orgNtoLst);
        cisDiagnoseDocCommonService.autostatisticsDocDiagnose(docNtoLst);

        CisDiagnoseRecordNto nto = new CisDiagnoseRecordNto();
        nto.setRecordDateTime(LocalDateTime.now());
        nto.setRecordType(RecordTypeEnum.DIAGNOSIS);
        createCisDiagnoseRecord(nto);
    }

    // private <T extends CisDiagnoseDocCommonNto> List<CisDiagnoseOrgCommonNto> convertToOrgCommonNto(List<T> source) {
    //     return source.stream()
    //             .map(p -> {
    //                 CisDiagnoseOrgCommonNto orgNto = new CisDiagnoseOrgCommonNto();
    //                 // 填充orgNto的属性，假设T类型有相应的get方法
    //                 orgNto.setOrgCode(getGroupSign(p));
    //                 orgNto.setDiagnoseCode(getDiagnoseCode(p));
    //                 orgNto.setDiagnoseName(getDiagnoseName(p));
    //                 orgNto.setIntegral(getCount(p));
    //                 orgNto.setIsFix(false);
    //                 return orgNto;
    //             })
    //             .toList();
    // }

    // private <T extends CisDiagnoseDocCommonNto> List<CisDiagnoseDocCommonNto> convertToDocCommonNto(List<T> source) {
    //     return source.stream()
    //             .map(p -> {
    //                 CisDiagnoseDocCommonNto docNto = new CisDiagnoseDocCommonNto();
    //                 // 填充docNto的属性，假设T类型有相应的get方法
    //                 docNto.setDocCode(getGroupSign(p));
    //                 docNto.setDiagnoseCode(getDiagnoseCode(p));
    //                 docNto.setDiagnoseName(getDiagnoseName(p));
    //                 docNto.setIntegral(getCount(p));
    //                 docNto.setIsFix(false);
    //                 return docNto;
    //             })
    //             .toList();
    // }

    // 假设这些方法会根据传入的对象类型正确地返回相应的属性值
    private <T> String getGroupSign(T object) {
        return object.toString(); // 示例，具体实现应根据实际情况
    }

    private <T> String getDiagnoseCode(T object) {
        return object.toString(); // 示例
    }

    private <T> String getDiagnoseName(T object) {
        return object.toString(); // 示例
    }

    private <T> Long getCount(T object) {
        return 0L; // 示例
    }
    //endregion


    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }
}