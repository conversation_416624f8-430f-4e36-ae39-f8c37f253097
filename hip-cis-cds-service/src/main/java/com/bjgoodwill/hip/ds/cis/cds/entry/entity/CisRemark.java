package com.bjgoodwill.hip.ds.cis.cds.entry.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.ds.cis.cds.enmus.EntryTempSubTypeEnum;
import com.bjgoodwill.hip.ds.cis.cds.entry.repository.CisRemarkRepository;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.*;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;


@Entity
@Comment(value = "医生词条")
@DiscriminatorValue("1")
public class CisRemark extends CisEntryTemp {

    @Enumerated(EnumType.STRING)
    @Comment("医嘱项目")
    @Column(name = "system_type", nullable = true)
    private SystemTypeEnum systemType;


    @Comment("工作组号")
    @Column(name = "group_number", nullable = true)
    private String groupNumber;


    @Comment("逻辑删除标记")
    @Column(name = "deleted", nullable = true)
    private boolean deleted;

    @Enumerated(EnumType.STRING)
    @Comment("子类型")
    @Column(name = "sub_type", nullable = true)
    private EntryTempSubTypeEnum subType;

    public static Optional<CisRemark> getCisRemarkById(String id) {
        return dao().findById(id);
    }

    public static List<CisRemark> getCisRemarks(CisRemarkQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<CisRemark> getCisRemarkPage(CisRemarkQto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<CisRemark> getSpecification(CisRemarkQto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(qto.getText())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("content"), "%" + qto.getText() + "%"));
            }
            if (StringUtils.isNotBlank(qto.getContent())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("content"), qto.getContent()));
            }
            if (StringUtils.isNotBlank(qto.getDoctorCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("doctorCode"), qto.getDoctorCode()));
            }
            if (qto.getSystemType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("systemType"), qto.getSystemType()));
            }
            if (StringUtils.isNotBlank(qto.getGroupNumber())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("groupNumber"), qto.getGroupNumber()));
            }
            if (qto.getSubType() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("subType"), qto.getSubType()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), false));

            return predicate;
        };
    }

    private static CisRemarkRepository dao() {
        return SpringUtil.getBean(CisRemarkRepository.class);
    }

    public SystemTypeEnum getSystemType() {
        return systemType;
    }

    protected void setSystemType(SystemTypeEnum systemType) {
        this.systemType = systemType;
    }

    public String getGroupNumber() {
        return groupNumber;
    }

    protected void setGroupNumber(String groupNumber) {
        this.groupNumber = groupNumber;
    }

    public boolean isDeleted() {
        return deleted;
    }

    protected void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public EntryTempSubTypeEnum getSubType() {
        return subType;
    }

    protected void setSubType(EntryTempSubTypeEnum subType) {
        this.subType = subType;
    }

    @Override
    public CisEntryTemp create(CisEntryTempNto cisEntryTempNto) {
        return create((CisRemarkNto) cisEntryTempNto);
    }

    @Override
    public void update(CisEntryTempEto cisEntryTempEto) {
        update((CisRemarkEto) cisEntryTempEto);
    }

    public CisRemark create(CisRemarkNto cisRemarkNto) {
        Assert.notNull(cisRemarkNto, "参数cisRemarkNto不能为空！");
        super.create(cisRemarkNto);

        setSystemType(cisRemarkNto.getSystemType());
        setGroupNumber(cisRemarkNto.getGroupNumber());
        setSubType(cisRemarkNto.getSubType());
        setDeleted(false);
        dao().save(this);
        return this;
    }

    public void update(CisRemarkEto cisRemarkEto) {
        super.update(cisRemarkEto);
        setSystemType(cisRemarkEto.getSystemType());
        setSubType(cisRemarkEto.getSubType());
    }

    public void delete() {

        super.delete();
        setDeleted(true);
    }

}
