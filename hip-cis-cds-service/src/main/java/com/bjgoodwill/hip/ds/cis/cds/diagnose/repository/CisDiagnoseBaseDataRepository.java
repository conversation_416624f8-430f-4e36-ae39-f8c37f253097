package com.bjgoodwill.hip.ds.cis.cds.diagnose.repository;

import com.bjgoodwill.hip.ds.cis.cds.diagnose.entity.CisDiagnoseBaseData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository("com.bjgoodwill.hip.ds.cis.cds.diagnose.repository.CisDiagnoseBaseDataRepository")
public interface CisDiagnoseBaseDataRepository extends JpaRepository<CisDiagnoseBaseData, String>, JpaSpecificationExecutor<CisDiagnoseBaseData> {
    List<CisDiagnoseBaseData> findCisDiagnoseBaseDataByCreatedDateAfter(LocalDateTime dateTime);
}