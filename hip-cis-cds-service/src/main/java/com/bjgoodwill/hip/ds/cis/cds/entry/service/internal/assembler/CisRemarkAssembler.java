package com.bjgoodwill.hip.ds.cis.cds.entry.service.internal.assembler;

import com.bjgoodwill.hip.ds.cis.cds.entry.entity.CisRemark;
import com.bjgoodwill.hip.ds.cis.cds.entry.to.CisRemarkTo;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public abstract class CisRemarkAssembler {

    public static List<CisRemarkTo> toTos(List<CisRemark> cisRemarks) {
        return toTos(cisRemarks, false);
    }

    public static List<CisRemarkTo> toTos(List<CisRemark> cisRemarks, boolean withAllParts) {
        Assert.notNull(cisRemarks, "参数cisRemarks不能为空！");

        List<CisRemarkTo> tos = new ArrayList<>();
        for (CisRemark cisRemark : cisRemarks)
            tos.add(toTo(cisRemark, withAllParts));
        return tos;
    }

    public static CisRemarkTo toTo(CisRemark cisRemark) {
        return toTo(cisRemark, false);
    }

    /**
     * @generated
     */
    public static CisRemarkTo toTo(CisRemark cisRemark, boolean withAllParts) {
        if (cisRemark == null)
            return null;
        CisRemarkTo to = new CisRemarkTo();
        to.setId(cisRemark.getId());
        to.setContent(cisRemark.getContent());
        to.setDoctorCode(cisRemark.getDoctorCode());
        to.setCreatedDate(cisRemark.getCreatedDate());
        to.setCreatedStaff(cisRemark.getCreatedStaff());
        to.setCreatedStaffName(cisRemark.getCreatedStaffName());
        to.setUpdatedDate(cisRemark.getUpdatedDate());
        to.setSystemType(cisRemark.getSystemType());
        to.setGroupNumber(cisRemark.getGroupNumber());
        to.setSubType(cisRemark.getSubType());
        if (withAllParts) {
        }
        return to;
    }

}