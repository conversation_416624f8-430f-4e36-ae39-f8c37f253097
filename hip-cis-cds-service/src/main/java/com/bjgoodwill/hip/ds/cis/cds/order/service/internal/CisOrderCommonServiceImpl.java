package com.bjgoodwill.hip.ds.cis.cds.order.service.internal;

import cn.hutool.core.collection.CollectionUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.EDrugSystemTypeExtEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.order.entity.CisOrderCommon;
import com.bjgoodwill.hip.ds.cis.cds.order.service.CisOrderCommonService;
import com.bjgoodwill.hip.ds.cis.cds.order.service.internal.assembler.CisOrderCommonAssembler;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonEto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonTo;
import com.bjgoodwill.hip.ds.cis.cds.proxy.CdsServiceItemServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@RestController("com.bjgoodwill.hip.ds.cis.cds.order.service.CisOrderCommonService")
@RequestMapping(value = "/api/cds/order/cisOrderCommon", produces = "application/json; charset=utf-8")
public class CisOrderCommonServiceImpl implements CisOrderCommonService {

    @Autowired
    CidOrderDataService cidOrderDataService;

    @Autowired
    CdsServiceItemServiceProxy cdsServiceItemServiceProxy;


    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public List<CisOrderCommonTo> getCisOrderCommons(CisOrderCommonQto cisOrderCommonQto) {
        List<CisOrderCommon> list = CisOrderCommon.getCisOrderCommons(cisOrderCommonQto);
        Set<String> seriveItemCodes = cdsServiceItemServiceProxy.queryCanUseServiceCodes(cisOrderCommonQto);
        list = list.stream().filter(p -> {
            boolean isDrugType = SystemTypeEnum.EDRUG.equals(p.getSystemType()) || SystemTypeEnum.CDRUG.equals(p.getSystemType()) || SystemTypeEnum.MATERIAL.equals(p.getSystemType());
            return isDrugType || seriveItemCodes.contains(p.getServiceItemCode());
        }).toList();
        return CisOrderCommonAssembler.toTos(list);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public GridResultSet<CisOrderCommonTo> getCisOrderCommonPage(CisOrderCommonQto cisOrderCommonQto) {
        Page<CisOrderCommon> page = CisOrderCommon.getCisOrderCommonPage(cisOrderCommonQto);
        Page<CisOrderCommonTo> result = page.map(CisOrderCommonAssembler::toTo);
        return new GridResultSet<>(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisOrderCommonTo getCisOrderCommonById(String id) {
        return CisOrderCommonAssembler.toTo(CisOrderCommon.getCisOrderCommonById(id).orElse(null));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public CisOrderCommonTo createCisOrderCommon(CisOrderCommonNto cisOrderCommonNto) {
        CisOrderCommon cisOrderCommon = CisOrderCommon.newInstanceByNto(cisOrderCommonNto);
        cisOrderCommon = cisOrderCommon.create(cisOrderCommonNto);
        return CisOrderCommonAssembler.toTo(cisOrderCommon);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void updateCisOrderCommon(String id, CisOrderCommonEto cisOrderCommonEto) {
        Optional<CisOrderCommon> cisOrderCommonOptional = CisOrderCommon.getCisOrderCommonById(id);
        cisOrderCommonOptional.ifPresent(cisOrderCommon -> cisOrderCommon.update(cisOrderCommonEto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void deleteCisOrderCommon(String id) {
        Optional<CisOrderCommon> cisOrderCommonOptional = CisOrderCommon.getCisOrderCommonById(id);
        cisOrderCommonOptional.ifPresent(cisOrderCommon -> cisOrderCommon.delete());
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = false)
    public void saveOrderCommons(List<CisOrderCommonNto> cisOrderCommonNtos) {
        BusinessAssert.notEmpty(cisOrderCommonNtos, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "cisOrderCommonNtos");
        // 过滤掉自带药
        cisOrderCommonNtos = cisOrderCommonNtos.stream().filter(p -> !String.valueOf(EDrugSystemTypeExtEnum.OWNDRUG).equals(p.getExtCode())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(cisOrderCommonNtos)) {
            return;
        }
        //依据医生、科室分组
        Map<String, List<CisOrderCommonNto>> mapDept = cisOrderCommonNtos.stream()
                .collect(Collectors.groupingBy(cisOrder -> cisOrder.getDocCode() + "_" + cisOrder.getOrgCode()));
        for (Map.Entry<String, List<CisOrderCommonNto>> entry : mapDept.entrySet()) {
            cidOrderDataService.dispose(entry.getValue());
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, readOnly = true)
    public CisOrderCommonTo getCisOrderCommonTop(String docCode, String orgCode, String rule) {
        if (StringUtils.isEmpty(rule)) {
            rule = "1|M|0.9";
        }
        List<CisOrderCommon> list = CisOrderCommon.findByDocCodeAndOrgCode(docCode, orgCode);

        if (CollectionUtil.isEmpty(list)) {
            return null;
        } else if (list.size() == 1) {
            return CisOrderCommonAssembler.toTo(list.get(0));
        } else {
            CisOrderCommon one = null;
            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    one = list.get(i);
                }
                one = this.getWeightTop(one, list.get(i + 1), rule);
                if (i == list.size() - 2) {
                    break;
                }
            }
            return CisOrderCommonAssembler.toTo(one);
        }
    }

    /**
     * 获取权重大的用法频次
     *
     * @param one
     * @param two
     * @return
     */
    private CisOrderCommon getWeightTop(CisOrderCommon one, CisOrderCommon two, String rule) {

        String[] split = rule.split("\\|");
        BusinessAssert.notNull(split, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "规则配置");
        Integer time = Integer.valueOf(split[0]);
        String unit = split[1];
        Double weight = Double.valueOf(split[2]);

        Long proportion = null;

        Duration duration = Duration.between(one.getUpdatedDate(), two.getUpdatedDate());
        if ("Y".equals(unit)) {
            proportion = (duration.abs().toDays() / 365 / time);
        } else if ("M".equals(unit)) {
            proportion = (duration.abs().toDays() / 30 / time);
        } else if ("D".equals(unit)) {
            proportion = (duration.abs().toDays() / time);
        } else {
            BusinessAssert.isTrue(false, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "规则配置错误");
        }

        if (duration.toDays() == 0) {
            //同时更新
            return one.getIntegral() > two.getIntegral() ? one : two;
        } else if (duration.toDays() > 0) {
            //tow 最新的时间
            return one.getIntegral() * Math.pow(weight, proportion) > two.getIntegral() ? one : two;
        } else {
            //one 最新的时间
            return one.getIntegral() > two.getIntegral() * Math.pow(weight, proportion) ? one : two;
        }
    }
}