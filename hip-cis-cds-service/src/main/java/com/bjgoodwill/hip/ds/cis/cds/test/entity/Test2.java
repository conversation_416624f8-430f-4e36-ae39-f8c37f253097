package com.bjgoodwill.hip.ds.cis.cds.test.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.test.repository.Test2Repository;
import com.bjgoodwill.hip.ds.cis.cds.test.to.Test2Eto;
import com.bjgoodwill.hip.ds.cis.cds.test.to.Test2Nto;
import com.bjgoodwill.hip.ds.cis.cds.test.to.Test2Qto;
import com.bjgoodwill.hip.jpa.core.SnowflakeIdGenerator;
import com.bjgoodwill.hip.jpa.util.JpaUtils;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Comment(value = "null")
@Table(name = "test_2", indexes = {}, uniqueConstraints = {})
public class Test2 {

    // 标识
    private String id;
    // 已启用
    private boolean enabled;

    public static Optional<Test2> getTest2ById(String id) {
        return dao().findById(id);
    }

    public static List<Test2> getTest2s(Test2Qto qto) {
        return dao().findAll(getSpecification(qto), JpaUtils.getSort(qto));
    }

    public static Page<Test2> getTest2Page(Test2Qto qto) {

        return dao().findAll(getSpecification(qto), JpaUtils.getPage(qto));
    }

    /**
     * @generated
     */
    private static Specification<Test2> getSpecification(Test2Qto qto) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (qto.getEnabled() != null) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("enabled"), qto.getEnabled()));
            }
            return predicate;
        };
    }

//    @OneToOne(mappedBy = "test2")
//    private Test1 test1;

    private static Test2Repository dao() {
        return SpringUtil.getBean(Test2Repository.class);
    }

    @Id
    @Comment("标识")
    @Column(name = "id", nullable = false, length = 50)
    @GeneratedValue(generator = "snowflake_generator")
    @GenericGenerator(name = "snowflake_generator", type = SnowflakeIdGenerator.class)
    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    @Comment("已启用")
    @Column(name = "enabled", nullable = false)
    public boolean isEnabled() {
        return enabled;
    }

    protected void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        Test2 other = (Test2) obj;
        return Objects.equals(id, other.id);
    }

    public Test2 create(Test2Nto test2Nto) {
        BusinessAssert.notNull(test2Nto, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数test2Nto");

        setId(test2Nto.getId());
        setEnabled(true);
        dao().save(this);
        return this;
    }

    public void update(Test2Eto test2Eto) {
        setEnabled(test2Eto.isEnabled());
    }

    public void enable() {
        setEnabled(true);
    }

    public void disable() {
        setEnabled(false);
    }

    public void delete() {
        dao().delete(this);
    }

}
