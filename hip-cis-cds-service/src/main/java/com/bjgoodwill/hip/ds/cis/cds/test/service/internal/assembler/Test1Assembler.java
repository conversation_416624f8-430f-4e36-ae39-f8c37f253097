package com.bjgoodwill.hip.ds.cis.cds.test.service.internal.assembler;

import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cds.enmus.CisCdsBusinessErrorEnum;
import com.bjgoodwill.hip.ds.cis.cds.test.entity.Test1;
import com.bjgoodwill.hip.ds.cis.cds.test.to.Test1To;

import java.util.ArrayList;
import java.util.List;

public abstract class Test1Assembler {

    public static List<Test1To> toTos(List<Test1> test1s) {
        return toTos(test1s, false);
    }

    public static List<Test1To> toTos(List<Test1> test1s, boolean withAllParts) {
        BusinessAssert.notNull(test1s, CisCdsBusinessErrorEnum.BUS_CIS_CDS_0001, "参数test1s");

        List<Test1To> tos = new ArrayList<>();
        for (Test1 test1 : test1s)
            tos.add(toTo(test1, withAllParts));
        return tos;
    }

    public static Test1To toTo(Test1 test1) {
        return toTo(test1, false);
    }

    /**
     * @generated
     */
    public static Test1To toTo(Test1 test1, boolean withAllParts) {
        if (test1 == null)
            return null;
        Test1To to = new Test1To();
        to.setId(test1.getId());
        to.setVersion(test1.getVersion());
        to.setEnabled(test1.isEnabled());

        if (withAllParts) {
        }
        return to;
    }

}