<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bjgoodwill.hip</groupId>
        <artifactId>hip-parent</artifactId>
        <version>5.0-SNAPSHOT</version>
    </parent>

    <artifactId>hip-cis-cds-service</artifactId>
    <version>5.0-SNAPSHOT</version>
    <name>hip-cis-cds-service</name>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <hip_cis-cds_api.version>5.0-SNAPSHOT</hip_cis-cds_api.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-cds-api</artifactId>
            <version>${hip_cis-cds_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-business-util</artifactId>
            <version>5.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-term-api</artifactId>
            <version>5.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-cis-medicineitem-api</artifactId>
            <version>5.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
